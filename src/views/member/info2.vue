<template>
    <div v-loading="loading">
        <top-bar :steps="tabShow ? steps : steps1" :showTransfer="top_barShow? true:false"  :showSave="top_barShow? false:true" :noShow="false" @save="saveIp" @transfer="transfer"></top-bar>
        <el-collapse @scroll.native="handleScroll" ref="content" class="contentbox" v-model="activeNames1">
            <el-collapse-item title="IP Base" class="step-jump" name="1">
            <div class=" contentbox">                       
                    <el-form :inline="true" label-width="120px" :model="formDummy" :rules="rulesFormDummy" label-position="right" class="demo-form-inline">
                        <div class="boxline">
                            <div>
                                <el-form-item label="IP Base No">
                                    <el-input v-model="formDummy.ipBaseNo" placeholder="" :readonly="disableshow"></el-input>
                                </el-form-item>
                                <el-form-item label="type">
                                    <el-select v-model="formDummy.ipType" placeholder="" @change="ipTypeChange" :disabled="disableshow">
                                        <el-option label="L" value="L"></el-option>
                                        <el-option label="N" value="N"></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="sex" v-if="formDummy.ipType =='N'">
                                    <el-select v-model="formDummy.sex" placeholder="" :disabled="disableshow">
                                        <!--<el-option label="默認選項" value=""></el-option>-->
                                        <el-option label="男" value="M"></el-option>
                                        <el-option label="女" value="F"></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="Company" v-else>
                                    <el-select v-model="formDummy.sex" placeholder="" :disabled="disableshow">
                                        <!--<el-option label="默認選項" value=""></el-option>-->
                                        <el-option label="S" value="S"></el-option>
                                        <el-option label="B" value="B"></el-option>
                                        <el-option label="P" value="P"></el-option>
                                        <el-option label="I" value="I"></el-option>
                                        <el-option label="C" value="C"></el-option>
                                    </el-select>
                                </el-form-item>
                            </div>
                            <el-form-item label="Place of Birth">
                                <el-input v-model="formDummy.birthPlace" placeholder="" :readonly="disableshow"></el-input>
                            </el-form-item>
                            <el-form-item label="State of Birth">
                                <el-input v-model="formDummy.birthState" placeholder="" :readonly="disableshow"></el-input>
                            </el-form-item>
                            <div>
                                <el-form-item label="CountryOfBirth">
                                    <el-input v-model="formDummy.birthCountryCode" style="width: 80px;"  :placeholder="disableshow ? '' : '雙擊查詢'" @dblclick.native="disableshow ? '' : getCountry('birth')" readonly :disabled="disableshow" ></el-input>
                                    <el-input v-model="formDummy.birthCountryName" style="width: 260px;"  :placeholder="disableshow ? '' : '雙擊查詢'" @dblclick.native="disableshow ? '' : getCountry('birth')" readonly :disabled="disableshow" ></el-input>
                                </el-form-item>
                                <el-form-item label="Nationality">
                                    <!-- <el-input v-model="formDummy.countryCode" :placeholder="disableshow ? '' : '雙擊查詢'" @dblclick.native="disableshow ? '' : getCountry('nationality')" readonly :disabled="disableshow" style="width: 80px;"></el-input>
                                    <el-input v-model="formDummy.nationality" :placeholder="disableshow ? '' : '雙擊查詢'" @dblclick.native="disableshow ? '' : getCountry('nationality')" readonly :disabled="disableshow" style="width: 260px;"></el-input> -->
                                    <el-input id="countryCode" v-model="formDummy.countryCode" @dblclick.native="showCountry()" readonly style="width: 80px"></el-input>
                                    <el-input id="nationality" v-model="formDummy.nationality" @dblclick.native="showCountry()" readonly style="width: 260px"></el-input>
                                </el-form-item>
                            </div>
                            <div>
                                <el-form-item label="Status">
                                    <el-select v-model="formDummy.status" placeholder=""  disabled>
                                        <el-option label="1" value="1"></el-option>
                                        <el-option label="2" value="2"></el-option>
                                        <el-option label="3" value="3"></el-option>
                                        <el-option label="4" value="4"></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item style="margin-left: 40px">
                                    <el-checkbox v-model="checkedDummy" disabled>Dummy IPI</el-checkbox>
                                </el-form-item>
                            <!-- </div>
                            <div> -->
                                <el-form-item label="DateOfBirth">
                                    <el-input v-model="formDummy.birthDate" :readonly="disableshow" style="width: 100px !important;"></el-input>
                                </el-form-item>
                                <el-form-item label="DateOfDeath">
                                    <el-input v-model="formDummy.deathDate" :readonly="disableshow" style="width: 100px !important;"></el-input>
                                </el-form-item>
                            </div>
                        </div>
                    </el-form>
                </div>
                </el-collapse-item>
        </el-collapse>

          <el-tabs v-model="activeNames"  class="frombox">
            
            <el-tab-pane label="IP Right" name="2" class="step-jump">
                <div class="frombox clear" style="margin-bottom: 20px;">
                    <div><el-button type="primary" @click="adddata1()" v-if="!tabShow && isAuth('member:ipi:change')">新 增</el-button>
                    <el-button type="warning" @click="showDetail()" >合約明細</el-button></div>
                    <div class="boxline clear">
                        <el-form :inline="true" :model="formAgreement" class="demo-form-inline f-l">
                            <el-form-item label="Right">
                                <!--  -->
                                <el-select v-model="formAgreement.groupName" style="width: 250px;" placeholder="">
                                    <el-option label="PERFORMING RIGHT" value="PER"></el-option>
                                    <el-option label="MECHANICAL RIGHT" value="MEC"></el-option>
                                    <el-option label="SYNCHRONIZATION RIGHT" value="ZYN"></el-option>
                                    <el-option label="New MEDIA RIGHT" value="NOD"></el-option>
                                    <el-option label="Other RIGHT" value="OR"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-form>
                        <el-table
                            :data="ipRight[formAgreement.groupName]"
                            border
                            stripe
                            class="f-l"
                            style="margin-left: 20px;width: 800px;border-radius: 4px;max-height:300px;border: 1px solid #ddd;">
                            <el-table-column
                                prop="societyCode"
                                label="Soc"
                                width="70px">
                                <template slot-scope="scope">
                                    <el-input v-model=scope.row.societyCode placeholder="" :readonly="disableshow && tabShow"></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="rightCode"
                                label="Right"
                                width="70px">
                                <template slot-scope="scope">
                                    <el-select v-model="scope.row.rightCode" placeholder="" :disabled="disableshow && tabShow">
                                        <el-option :label="item.rightCode" :value="item.rightCode" v-for="(item,index) in rightData" :key="index"></el-option>
                                    </el-select>
                                    <!-- <el-input v-model=scope.row.rightCode placeholder="" :readonly="disableshow && tabShow"></el-input> -->
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="roleCode"
                                label="Role"
                                width="70px">
                                <template slot-scope="scope">
                                    <el-select v-model="scope.row.roleCode" placeholder="" :disabled="disableshow && tabShow">
                                        <el-option :label="item.roleCode" :value="item.roleCode" v-for="(item,index) in roleData" :key="index"></el-option>
                                    </el-select>
                                    <!-- <el-input v-model=scope.row.roleCode placeholder="" :readonly="disableshow && tabShow"></el-input> -->
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="validFrom"
                                label="Valid From"
                                width="140px">
                                <template slot-scope="scope">
                                    <!-- <el-input v-model=scope.row.validFrom placeholder="" v-dateFmt :readonly="disableshow"></el-input> -->
                                    <date-picker v-model="scope.row.validFrom" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd" style="width: 120px !important" :readonly="disableshow && tabShow"></date-picker>
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="validTo"
                                label="Valid To"
                                width="140px">
                                <template slot-scope="scope">
                                    <!-- <el-input v-model=scope.row.validTo placeholder="" v-dateFmt :readonly="disableshow"></el-input> -->
                                    <date-picker v-model="scope.row.validTo" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd" style="width: 120px !important" :readonly="disableshow && tabShow"></date-picker>
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="membershipShare"
                                label="Share"
                                width="70px">
                                <template slot-scope="scope">
                                    <el-input v-model=scope.row.membershipShare placeholder="" :readonly="disableshow && tabShow"></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="signatureDate"
                                label="Signature Date">
                                <template slot-scope="scope">
                                    <!-- <el-input v-model=scope.row.signatureDate placeholder="" v-dateFmt :readonly="disableshow"></el-input> -->
                                    <date-picker v-model="scope.row.signatureDate" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd" style="width: 120px !important" :readonly="disableshow && tabShow"></date-picker>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="operation"
                                width="100">
                                <template slot-scope="scope">
                                    <span style="width: 100%;display: inline-block;text-align: center">
                                        <i class="el-icon-delete" v-if="isAuth('member:ipi:change')" @click="deletedata1(scope.$index,ipRight[formAgreement.groupName])"></i>
                                    </span>
                                </template>
                            </el-table-column>
                        </el-table>

                    </div>
                    

                </div>
            </el-tab-pane>
            <el-tab-pane label="IPI Info" name="3" class="step-jump">
                <div v-if="!tabShow" style="margin-bottom:20px">
                    <el-button type="primary" @click="adddata2()" v-if="isAuth('member:ipi:change')">新 增</el-button>
                </div>
                <div>
                    <el-table
                        :data="tableData2"
                        border
                        stripe
                        style="width: 100%">
                        <el-table-column
                            prop="lastName"
                            label="LastName">
                            <template slot-scope="scope">
                                <el-input v-model=scope.row.lastName placeholder="" :readonly="dummyShow && !!scope.row.id"></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="firstName"
                            label="FirstName">
                            <template slot-scope="scope">
                                <el-input v-model=scope.row.firstName placeholder="" :readonly="dummyShow && !!scope.row.id"></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="chineseLastName"
                            label="ch_lastName">
                            <template slot-scope="scope">
                                <el-input v-model=scope.row.chineseLastName placeholder=""></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="chineseFirstName"
                            label="ch_firstName">
                            <template slot-scope="scope">
                                <el-input v-model=scope.row.chineseFirstName placeholder=""></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="nameNo"
                            label="IP Name No">
                            <template slot-scope="scope">
                                <el-input v-model=scope.row.nameNo placeholder="" readonly></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="nameType"
                            label="nameType">
                            <template slot-scope="scope">
                                <el-select v-model=scope.row.nameType placeholder="" :disabled="dummyShow && !!scope.row.id">
                                        <el-option label="PA" value="PA"></el-option>
                                        <el-option label="PP" value="PP"></el-option>
                                        <el-option label="MO" value="MO"></el-option>
                                        <el-option label="DF" value="DF"></el-option>
                                        <el-option label="OR" value="OR"></el-option>
                                        <el-option label="PG" value="PG"></el-option>
                                        <el-option label="HR" value="HR"></el-option>
                                    </el-select>
                            </template>
                        </el-table-column>
                        <el-table-column
                                label="operation"
                                width="100">
                                <template slot-scope="scope">
                                    <span style="width: 100%;display: inline-block;text-align: center">
                                        <i class="el-icon-delete" v-if="isAuth('member:ipi:change')" @click="deletedata2(scope.$index,tableData2)"></i>
                                    </span>
                                </template>
                            </el-table-column>
                    </el-table>
                    
                </div>
            </el-tab-pane>
            <!--Member ID Info 位置-->
            <el-tab-pane label="Member ID Info"  v-if="tabShow && formDummy.ipType == 'N'"  class="step-jump" name="4">
                <div style="margin-top: 20px">
                    <div class="box-card">
                        <el-form :inline="true" :model="formMemberInfo" label-position="right" label-width="100px" class="demo-form-inline">
                            <div class="boxline">
                                <el-form-item label="ID No" label-width="60px">
                                    <el-input v-model="formMemberInfo.idCardNo" placeholder=""></el-input>
                                </el-form-item>
                                <el-form-item label="Language">
                                    <el-select v-model="formMemberInfo.language" placeholder="">
                                        <el-option label="" value=""></el-option>
                                        <el-option label="mandarin" value="M"></el-option>
                                        <el-option label="english" value="E"></el-option>
                                        <el-option label="both" value="B"></el-option>
                                    </el-select>
                                </el-form-item>
                            </div>
                            <div>
                                <div class="boxline" style="margin-top: 0;">
                                    <div style="margin-bottom: 16px;padding-left: 10px;">
                                        <span class="mytitle" style="color: #333;font-size: 16px;"> ARC Info</span>
                                    </div>

                                    <el-form-item label="No" label-width="60px">
                                        <el-input v-model="formMemberInfo.permanentNo" placeholder=""></el-input>
                                    </el-form-item>
                                    <el-form-item label="Valid from">
                                        <!-- <el-input v-model="formMemberInfo.permanentValidFrom" v-dateFmt></el-input> -->
                                        <date-picker v-model="formMemberInfo.permanentValidFrom" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd"></date-picker>
                                    </el-form-item>
                                    <el-form-item label="Valid To">
                                        <!-- <el-input v-model="formMemberInfo.permanentValidTo" v-dateFmt></el-input> -->
                                        <date-picker v-model="formMemberInfo.permanentValidTo" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd"></date-picker>
                                    </el-form-item>
                                </div>
                            </div>
                        </el-form>
                    </div>
                </div>
            </el-tab-pane>
            <!--Member Detail位置-->
            <el-tab-pane label="Member Detail"  v-if="tabShow"  class="step-jump" name="5">
                <div>
                    <el-form :inline="true" :model="formMemberDetail" label-width="110px" label-position="right" class="demo-form-inline">
                        <div class="boxline">
                            <el-form-item label="Co. IP Base">
                                <el-input v-model="formMemberDetail.correspIpiNo" placeholder="雙擊查找" style="width: 120px" maxlength="11" @dblclick.native="getIp('coip', formMemberDetail.associateIpiNo)" clearable></el-input>
                                <el-input v-model="formMemberDetail.correspIpiName" style="width: 300px;" clearable></el-input>
                            </el-form-item>
                            <el-form-item label="AdminIPBase">
                                <el-input v-model="formMemberDetail.adminIpiNo" placeholder="雙擊查找" style="width: 120px" maxlength="11" @dblclick.native="getIp('adminip',formMemberDetail.adminIpiNo)" clearable></el-input>
                                <el-input v-model="formMemberDetail.adminIpiName" style="width: 300px;" clearable></el-input>
                            </el-form-item>
                            <el-form-item label="Role">
                                <el-select v-model="formMemberDetail.role" placeholder="">
                                    <el-option label="" value=""></el-option>
                                    <el-option label="Composer&Author" value="CA"></el-option>
                                    <el-option label="Author" value="A"></el-option>
                                    <el-option label="Publisher" value="E"></el-option>
                                    <el-option label="Composer" value="C"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="CI No">
                                <el-input v-model="formMemberDetail.ciNo" placeholder="" ></el-input>
                            </el-form-item>
                            <el-form-item label="Tax ID No">
                                <el-input v-model="formMemberDetail.brNo" placeholder=""></el-input>
                            </el-form-item>
                            <el-form-item label="Owner">
                                <el-input v-model="formMemberDetail.brName" placeholder=""></el-input>
                            </el-form-item>
                            <el-form-item label="Member No">
                                <el-input v-model="formMemberDetail.memberNo" placeholder=""></el-input>
                            </el-form-item>
                            <el-form-item label="Joint Date">
                                <!-- <el-input type="text" v-model="formMemberDetail.jointDate" v-dateFmt></el-input> -->
                                <date-picker v-model="formMemberDetail.jointDate" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd"></date-picker>
                            </el-form-item>
                            <el-form-item label="UPA Year">
                                <el-input v-model="formMemberDetail.upaYear" placeholder=""></el-input>
                            </el-form-item>
                            <!-- <el-form-item label="Basic Votes">
                                <el-input v-model="formMemberDetail.votes" placeholder=""></el-input>
                            </el-form-item> -->
                            <!-- <el-form-item label="Tax Rate">
                                <el-input v-model="formMemberDetail.taxRate" placeholder="" @input.native="taxRateChange()"></el-input>
                            </el-form-item> -->

                            <!-- <el-form-item style="margin-left: 40px">
                                <el-checkbox v-model="formMemberDetail.specialExchangeRate">Special exchange rate</el-checkbox>
                            </el-form-item>
                            <el-form-item class="countryName" label="Country Name" v-if="formMemberDetail.specialExchangeRate">
                                <el-input v-model="formMemberDetail.specialExchangeCountry" placeholder="" readonly @dblclick.native="getSocietyTaxRate()"></el-input>
                            </el-form-item> -->
                        </div>
                        <div class="boxline" style="margin-top: 0px;">
                            <div style="margin-left: 10px;margin-bottom: 6px;"><span class="mytitle">Termination</span></div>
                            <el-form-item label="Date">
                                <!-- <el-input v-model="formMemberDetail.terminateDate" placeholder="" v-dateFmt></el-input> -->
                                <date-picker v-model="formMemberDetail.terminateDate" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd"></date-picker>
                            </el-form-item>
                            <el-form-item label="Reason">
                                <el-select v-model="formMemberDetail.terminateReason" placeholder="" >
                                    <el-option label="" value=""></el-option>
                                    <el-option label="discharge" value="D"></el-option>
                                    <el-option label="withdraw" value="W"></el-option>
                                </el-select>
                            </el-form-item>
                        </div>
                    </el-form>
                    <div class="boxline" style="margin-top: 0px;">
                        <span class="mytitle" style="margin-left: 10px;">Assign Right</span>
                        <el-form :inline="true" :model="formMemberDetail1"    class="demo-form-inline">
                            <el-form-item style="margin-left: 40px">
                                <el-checkbox v-model="formMemberDetail1.deedSignP" disabled>P</el-checkbox>
                            </el-form-item>
                            <el-form-item>
                                <el-checkbox v-model="formMemberDetail1.deedSignM" disabled>M</el-checkbox>
                            </el-form-item>
                            <el-form-item>
                                <el-checkbox v-model="formMemberDetail1.deedSignCp" disabled>CP</el-checkbox>
                            </el-form-item>
                            <el-form-item>
                                <el-checkbox v-model="formMemberDetail1.deedSignMa" disabled>MA</el-checkbox>
                            </el-form-item>
                            <!--<el-form-item class="right"><el-button type="primary" @click="adddata4()">新 增</el-button></el-form-item>-->
                        </el-form>
                    </div>
                    <div class="boxline" style="margin-top: 00px">
                        <div >
                            <!--<el-button type="primary" @click="adddata4()">新 增</el-button>-->
                        </div>
                        <el-table
                            :data="tableData4"
                            border
                            stripe
                            style="width: 100%">
                            <el-table-column
                                prop="validFrom"
                                label="Valid From">
                                <template slot-scope="scope">
                                    <!-- <el-input v-model='scope.row.validFrom' readonly v-dateFmt></el-input> -->
                                    <date-picker v-model="scope.row.validFrom" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd" readonly></date-picker>
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="validTo"
                                label="Valid To">
                                <template slot-scope="scope">
                                    <!-- <el-input v-model='scope.row.validTo' readonly v-dateFmt></el-input> -->
                                    <date-picker v-model="scope.row.validTo" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd" readonly></date-picker>
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="right"
                                label="Right">
                                <template slot-scope="scope">
                                    <el-input v-model='scope.row.right' readonly></el-input>
                                </template>
                            </el-table-column>
                        </el-table>
                        
                    </div>
                    <div class="boxline" v-if="update5">
                        <div>
                            <span class="mytitle" style="margin-left: 10px;">Membership</span>
                            <el-button type="primary" v-if="isAuth('member:ipi:change')" @click="adddata5()" style="float: right;margin: 0 10px 10px;padding: 8px 16px;">新 增</el-button>
                        </div>
                        <el-table
                            :data="tableData5"
                            border
                            stripe
                            style="width: 100%">
                            <el-table-column
                                prop="type"
                                label="Type"
                                width="180">
                                <template slot-scope="scope">
                                    <!-- <el-input v-model=scope.row.type placeholder=""></el-input> -->
                                    <el-select v-model=scope.row.type>
                                        <el-option value="FW" label="full writer"></el-option>
                                        <el-option value="FP" label="full publisher"></el-option>
                                        <el-option value="AW" label="associate writer"></el-option>
                                        <el-option value="AP" label="associate publiser"></el-option>
                                        <el-option value="NM" label="non member"></el-option>
                                        <el-option value="RE" label="reject"></el-option>
                                        <el-option value="WA" label="waiting"></el-option>
                                    </el-select>
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="validFrom"
                                label="ValidFrom"
                                width="120">
                                <template slot-scope="scope">
                                    <!-- <el-input v-model=scope.row.validFrom placeholder="" v-dateFmt></el-input> -->
                                    <date-picker v-model="scope.row.validFrom" type="date" style="width: 100px !important;" value-format="yyyy-MM-dd" format="yyyyMMdd"></date-picker>
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="validTo"
                                label="ValidTo"
                                width="120">
                                <template slot-scope="scope">
                                    <!-- <el-input v-model=scope.row.validTo placeholder="" v-dateFmt></el-input> -->
                                    <date-picker v-model="scope.row.validTo" type="date" style="width: 100px !important;" value-format="yyyy-MM-dd" format="yyyyMMdd"></date-picker>
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="amendTime"
                                label="Amend Date">
                                <template slot-scope="scope">
                                    <!-- <el-input v-model=scope.row.amendTime placeholder="" v-dateFmt></el-input> -->
                                    <date-picker v-model="scope.row.amendTime" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd" readonly></date-picker>
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="amendUser"
                                label="Amend User">
                                <template slot-scope="scope">
                                    <el-input v-model=scope.row.amendUser placeholder="" readonly></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="operation"
                                width="100">
                                <template slot-scope="scope">
                                    <span class="spanright">
                                        <i class="el-icon-delete" v-if="isAuth('member:ipi:change')" @click="deletedata5(scope.$index,tableData5)"></i>
                                    </span>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </div>
            </el-tab-pane>
             <!-- Contact 位置-->
            <el-tab-pane label="Contact"  v-if="tabShow"  class="step-jump" name="6">
                <el-form :inline="true" label-position="right" label-width="140px" class="demo-form-inline">
                    <div class="boxline">
                        <div>
                            <el-form-item label="Type">
                                <el-select v-model="contactType">
                                    <el-option label="" value=""></el-option>
                                    <el-option label="Home" value="HM"></el-option>
                                    <el-option label="Office" value="OF"></el-option>
                                    <el-option label="Domicile" value="DO"></el-option>
                                    <el-option label="Other" value="OT"></el-option>
                                    <el-option label="Assistant" value="AT"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="Correspondence" class="bold-item">
                                <!-- <el-input v-model="formContact.correspType"></el-input> -->
                                <el-select v-model="formMemberDetail.correspondType" placeholder="">
                                    <el-option label="" value=""></el-option>
                                    <el-option label="Home" value="HM"></el-option>
                                    <el-option label="Office" value="OF"></el-option>
                                    <el-option label="Domicile" value="DO"></el-option>
                                    <el-option label="Other" value="OT"></el-option>
                                    <el-option label="Assistant" value="AT"></el-option>
                                </el-select>
                            </el-form-item>
                        </div>
                        <div>
                            <el-form-item label="Mail Name">
                                <el-input v-model="formMemberDetail.mailName" placeholder=""></el-input>
                            </el-form-item>
                            <el-form-item label="Contact Person">
                                <el-input v-model="contactData.person" placeholder=""></el-input>
                            </el-form-item>
                            <el-form-item label="Telephone No">
                                <el-input v-model="contactData.telephoneNo" placeholder=""></el-input>
                            </el-form-item>
                            <el-form-item label="Mobile No">
                                <el-input v-model="contactData.mobileNo" placeholder=""></el-input>
                            </el-form-item>
                            <el-form-item label="Fax No">
                                <el-input v-model="contactData.faxNo" placeholder=""></el-input>
                            </el-form-item>
                            <el-form-item label="Other No">
                                <el-input v-model="contactData.otherNo" placeholder=""></el-input>
                            </el-form-item>
                            <el-form-item label="Email">
                                <el-input v-model="contactData.email" placeholder=""></el-input>
                            </el-form-item>
                            <!-- <el-form-item label="ContactPeriod">
                                <el-input v-model="contactData.contactPeriod" placeholder=""></el-input>
                            </el-form-item> -->
                            <el-form-item label="URL">
                                <el-input v-model="contactData.url" placeholder=""></el-input>
                            </el-form-item>
                            <el-form-item label="Memo">
                                <el-input type="textarea" v-model="contactData.period" placeholder="" style="width: 500px;"></el-input>
                            </el-form-item>
                        </div>
                    </div>
                </el-form>
            </el-tab-pane>
            <el-tab-pane label="Address"  v-if="tabShow"  class="step-jump" name="10">
                <el-form :inline="true" label-position="right" label-width="110px" class="demo-form-inline">
                    <div class="boxline">
                        <div>
                            <el-form-item label="Type">
                                <el-select v-model="addressType">
                                    <el-option label="" value=""></el-option>
                                    <el-option label="Home" value="HM"></el-option>
                                    <el-option label="Office" value="OF"></el-option>
                                    <el-option label="Domicile" value="DO"></el-option>
                                    <el-option label="Other" value="OT"></el-option>
                                    <el-option label="Assistant" value="AT"></el-option>
                                </el-select>
                            </el-form-item>
                        </div>
                        <div>
                            <el-form-item label="Care of">
                                <el-input v-model="addressData.careof" placeholder=""></el-input>
                            </el-form-item>
                            <!-- <el-form-item label="Country">
                                <el-input v-model="addressData.country" placeholder=""></el-input>
                            </el-form-item> -->
                            <el-form-item label="Country">
                                <el-input v-model="addressData.countryCode" style="width: 80px;"  placeholder="雙擊查詢" @dblclick.native="getCountry('address')" clearable></el-input>
                            <!-- </el-form-item>
                            <el-form-item label="Country"> -->
                                <el-input v-model="addressData.country" style="width: 260px;" placeholder="雙擊查詢" @dblclick.native="getCountry('address')" clearable></el-input>
                            </el-form-item>
                            <el-form-item label="County">
                                <el-input v-model="addressData.county" placeholder="雙擊查詢" @dblclick.native="getCounty()" clearable></el-input>
                            </el-form-item>
                            <el-form-item label="District">
                                <el-input v-model="addressData.district" placeholder="" @dblclick.native="getDistrict()" @change="districtChange" clearable></el-input>
                            </el-form-item>
                            <el-form-item label="ZIP Code">
                                <el-input v-model="addressData.zipCode" placeholder=""></el-input>
                            </el-form-item>
                            <el-form-item label="Village/Street">
                                <el-input v-model="addressData.street" placeholder=""></el-input>
                            </el-form-item>
                            <el-form-item label="Section">
                                <el-input v-model="addressData.section" placeholder=""></el-input>
                            </el-form-item>
                            <el-form-item label="Lane">
                                <el-input v-model="addressData.lane" placeholder=""></el-input>
                            </el-form-item>
                            <el-form-item label="Alley">
                                <el-input v-model="addressData.alley" placeholder=""></el-input>
                            </el-form-item>
                            <el-form-item label="No">
                                <el-input v-model="addressData.no" placeholder=""></el-input>
                            </el-form-item>
                            <el-form-item label="Floor">
                                <el-input v-model="addressData.floor" placeholder=""></el-input>
                            </el-form-item>
                            <el-form-item label="Room">
                                <el-input v-model="addressData.room" placeholder=""></el-input>
                            </el-form-item>
                            <el-form-item label="Mail Box">
                                <el-input v-model="addressData.mailBox" placeholder="" style="width: 250px"></el-input>
                            </el-form-item>
                        </div>
                    </div>
                </el-form>
            </el-tab-pane>
            <!-- Bank 位置-->
            <el-tab-pane label="Bank"  v-if="tabShow"  class="step-jump" name="7">
                <div class="boxline">
                    <span class="mytitle">Member and Successors</span>
                    <el-table
                        :data="memberList"
                        border
                        stripe
                        style="width: 100%" v-if="update">
                        <el-table-column
                            prop="type"
                            label="Type">
                            <template slot-scope="scope">
                                <el-input v-model=scope.row.info.type readonly></el-input>
                                <!-- <date-picker v-model="scope.row.info.type" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd"></date-picker> -->
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="name"
                            label="Name">
                            <template slot-scope="scope">
                                <el-input v-model=scope.row.info.name readonly></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="sex"
                            label="Sex">
                            <template slot-scope="scope">
                                <el-input v-model=scope.row.info.sex readonly></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="idCardNo"
                            label="ID No">
                            <template slot-scope="scope">
                                <el-input v-model=scope.row.info.idCardNo readonly></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="relationship"
                            label="Relationship">
                            <template slot-scope="scope">
                                <el-input v-model=scope.row.info.relationship readonly></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="language"
                            label="Language">
                            <template slot-scope="scope">
                                <el-input v-model=scope.row.info.language placeholder="" readonly></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="share"
                            label="Share">
                            <template slot-scope="scope">
                                <el-input v-model=scope.row.info.share placeholder=""></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column
                            label="operation"
                            width="150">
                            <template slot-scope="scope">
                                <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                                    <el-button @click="editMemberBank(scope.$index,scope.row)" type="text" size="small">edit</el-button>
                                    <el-button @click="viewBankHistory(scope.$index,scope.row,'member')" type="text" size="small">history</el-button>
                                </span>
                            </template>
                        </el-table-column>
                    </el-table>
                    <el-table
                        :data="tableData7"
                        border
                        stripe
                        :show-header="false"
                        style="width: 100%" v-if="update">
                        <span slot="empty"></span>
                        <el-table-column
                            prop="type"
                            label="Type">
                            <template slot-scope="scope">
                                {{scope.row.info.seq}}
                                <!-- <el-input v-model=scope.row.info.type readonly></el-input> -->
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="name"
                            label="Name">
                            <template slot-scope="scope">
                                <el-input v-model=scope.row.info.name readonly></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="sex"
                            label="Sex">
                            <template slot-scope="scope">
                                <el-input v-model=scope.row.info.sex readonly></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="idCardNo"
                            label="ID No">
                            <template slot-scope="scope">
                                <el-input v-model=scope.row.info.idCardNo readonly></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="relationship"
                            label="Relationship">
                            <template slot-scope="scope">
                                <el-input v-model=scope.row.info.relationship readonly></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="language"
                            label="Language">
                            <template slot-scope="scope">
                                <el-input v-model=scope.row.info.language placeholder="" readonly></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="royShare"
                            label="Share">
                            <template slot-scope="scope">
                                <el-input v-model=scope.row.info.royShare placeholder=""></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column
                            label="operation"
                            width="150">
                            <template slot-scope="scope">
                                <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                                    <el-button @click="editBank(scope.$index,scope.row)" type="text" size="small" v-if="isAuth('member:ipi:change')">edit</el-button>
                                       <el-button @click="viewBankHistory(scope.$index,scope.row,'successor')" type="text" size="small">history</el-button>
                                </span>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </el-tab-pane>
            <!--Successor 位置 -->
            <el-tab-pane label="Successor"  v-if="tabShow && deathShow"  class="step-jump" name="8">
                <div>
                    <div class="boxline">
                        <div>
                            <el-button type="primary" @click="addSuccessor()" v-if="isAuth('member:ipi:change')">新 增</el-button>
                        </div>
                        <el-table
                            :data="tableData7"
                            border
                            stripe
                            style="width: 100%">
                            <el-table-column
                                label="Seq"
                                width="80">
                                <template slot-scope="scope">
                                    <!-- <el-input v-model=scope.row.info.seq    disabled></el-input> -->
                                    {{scope.row.info.seq}}
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="Name"
                                width="180">
                                <template slot-scope="scope">
                                    <!-- <el-input v-model=scope.row.info.name    @dblclick.native="successorName(scope.$index, scope.row)" disabled></el-input> -->
                                    {{scope.row.info.name}}
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="sex"
                                label="Sex">
                                <template slot-scope="scope">
                                    <!-- <el-input v-model=scope.row.info.sex disabled></el-input> -->
                                    {{scope.row.info.sex}}
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="relationship"
                                label="Relationship">
                                <template slot-scope="scope">
                                    <!-- <el-input v-model=scope.row.info.relationship disabled></el-input> -->
                                    {{scope.row.info.relationship}}
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="jointDate"
                                label="Joint Date">
                                <template slot-scope="scope">
                                    <!-- <el-input v-model=scope.row.info.jointDate disabled></el-input> -->
                                    {{$utils.DATE(scope.row.info.jointDate, 'yyyyMMdd')}}
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="validFrom"
                                label="Valid From">
                                <template slot-scope="scope">
                                    <!-- <el-input v-model=scope.row.info.validFrom disabled></el-input> -->
                                    {{$utils.DATE(scope.row.info.validFrom, 'yyyyMMdd')}}
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="validTo"
                                label="Valid To">
                                <template slot-scope="scope">
                                    <!-- <el-input v-model=scope.row.info.validTo disabled></el-input> -->
                                    {{$utils.DATE(scope.row.info.validTo, 'yyyyMMdd')}}
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="royShare"
                                label="Share %">
                                <template slot-scope="scope">
                                    <!-- <el-input v-model=scope.row.info.share disabled></el-input> -->
                                    {{scope.row.info.royShare}}
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="operation"
                                width="150">
                                <template slot-scope="scope">
                                    <span style="width: 40%;display: inline-block;text-align: center;cursor: pointer">
                                        <el-button @click="editSuccessor(scope.$index,scope.row)" type="text">查看</el-button>
                                    </span>
                                    <span style="width: 40%;display: inline-block;text-align: center;cursor: pointer">
                                        <i class="el-icon-delete" v-if="isAuth('member:ipi:change')" @click="deletedata7(scope.$index,tableData7)"></i>
                                    </span>
                                </template>
                            </el-table-column>
                        </el-table>
                        
                    </div>
                </div>
            </el-tab-pane>
            <!--Remark位置-->
            <el-tab-pane label="Remark"  v-if="tabShow"  class="step-jump component-class" name="9">
                <div>
                    <div>
                        <div>
                            <el-button type="primary" @click="adddata8()" v-if="isAuth('member:ipi:change')">新 增</el-button>
                        </div>
                        <!-- <span class="mytitle">Remark</span> -->
                        <el-form :inline="true" :model="formRemark" class="demo-form-inline">
                            <el-form-item label="Remark Type">
                                <el-select v-model="formRemark.type" placeholder="">
                                    <el-option label="All" value="all"></el-option>
                                    <el-option label="Award" value="A"></el-option>
                                    <el-option label="Bad News" value="BN"></el-option>
                                    <el-option label="Changed Name" value="CN"></el-option>
                                    <el-option label="Good News" value="GN"></el-option>
                                    <el-option label="Others" value="OT"></el-option>
                                    <el-option label="Representative" value="REP"></el-option>
                                    <el-option label="Successor Membership Effective Date" value="SME"></el-option>
                                    <el-option label="Successor to Withdrawal Publisher" value="SWP"></el-option>
                                    <el-option label="Update" value="UP"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-form>
                        <el-table
                            :data="remarksData[formRemark.type]"
                            border
                            stripe
                            style="width: 100%">
                            <el-table-column
                                prop="createTime"
                                label="Date"
                                width="200">
                                <template slot-scope="scope">
                                    <!-- <el-input v-model="scope.row.createTime" v-dateFmt></el-input> -->
                                    <date-picker v-model="scope.row.createTime" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd" readonly></date-picker>
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="createUserName"
                                label="User ID"
                                width="180">
                                <template slot-scope="scope">
                                    <el-input type="text" v-model="scope.row.createUserName" readonly></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="type"
                                label="Type">
                                <template slot-scope="scope">
                                    <el-select v-model="scope.row.type"  placeholder="">
                                        <el-option label="Award" value="A"></el-option>
                                        <el-option label="Bad News" value="BN"></el-option>
                                        <el-option label="Changed Name" value="CN"></el-option>
                                        <el-option label="Good News" value="GN"></el-option>
                                        <el-option label="Others" value="OT"></el-option>
                                        <el-option label="Representative" value="REP"></el-option>
                                        <el-option label="Successor Membership Effective Date" value="SME"></el-option>
                                        <el-option label="Successor to Withdrawal Publisher" value="SWP"></el-option>
                                        <el-option label="Update" value="UP"></el-option>
                                    </el-select>
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="content"
                                label="Remark">
                                <template slot-scope="scope">
                                    <el-input type="textarea" v-model="scope.row.content"></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="operation"
                                width="100">
                                <template slot-scope="scope">
                                    <span class="spanright">
                                        <i class="el-icon-delete" v-if="isAuth('member:ipi:change')" @click="deletedata8(scope.$index, remarksData[formRemark.type])"></i>
                                    </span>
                                </template>
                            </el-table-column>
                        </el-table>
                        
                    </div>
                </div>
            </el-tab-pane>
        </el-tabs>

        <el-dialog title="配製匯率" :visible.sync="dialogTableVisible" :close-on-click-modal="false" width='750px'>
            <!-- <div class="add-box">
                <el-button type="primary" @click="showRateForm()">新增</el-button>
            </div> -->
            <el-table :empty-text="tableresult"   :data="rateTable">
                <el-table-column property="countryCode" label="countryCode" width='150px'></el-table-column>
                <el-table-column property="countryName" label="countryName" width='250px'></el-table-column>
                <el-table-column property="taxRate" label="匯率" width='150px'></el-table-column>
                <el-table-column label="operation" width='150px'>
                    <template slot-scope="scope">
                        <i class="el-icon-check pointer" v-if="isAuth('member:ipi:change')" @click="selectRate(scope.$index,scope.row)"></i>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                background
                layout="prev, pager, next"
                :total=rateTotal @current-change="handleCurrentChange" :current-page="currentPage">
            </el-pagination>
        </el-dialog>

        <!-- 继承人弹框 -->
        <el-dialog title="Successor" :visible.sync="SuccessorVisible" top="5vh" width="1200px" center :close-on-click-modal="false">
            <edit-successor-dialog ref="successorDialog" :info="successorInfo" :successorOptionType="successorOptionType" @saveSuccessorInfo="saveSuccessorInfo" @cancel="closeSuccessor"></edit-successor-dialog>
        </el-dialog>
        <!-- transfer 弹框 -->
        <transfer-dialog :ip-info="transferInfo" :show-transfer="showTransfer" @change="changeValue" v-if="showTransfer"></transfer-dialog>
        <edit-bank-dialog 
        ref="editBankDialog" 
        :show-edit-bank="showEditBank" 
        :options="editBankInfoOptions" 
        :bank-info="editBankInfo" 
        :bank-dtls-info="bankDtlsInfo" 
        @saveChangeBank='saveChangeBank'
        @updateBank="updateBank" 
        @updateBankCtls="updateBankCtls" 
        @change="changeValue" 
        @cancel='showEditBank=false'
        @getSocietyTaxRate='getSocietyTaxRate'
        @changeSpecialExchangeRate='changeSpecialExchangeRate'
        v-show="showEditBank"></edit-bank-dialog>
        <list-bank-dialog 
        :show-edit-bank="showBankList" 
        :options="editBankInfoOptions" 
        :bank-info="editBankInfo" 
        :bank-dtls-info="bankDtlsInfo"
        @updateBank="updateBank" 
        @updateBankCtls="updateBankCtls"
        @change="changeValue" 
        v-if="showBankList"></list-bank-dialog>
        <!-- getip -->
        <select-ip v-if="selectIpShow" :search="ipSearch" ref="selectIpCom" @checkIp="checkIp"></select-ip>
        <select-country v-if="countryShow" ref="selectCountry" :search="countrySearch" @checkCountry="checkCountry"></select-country>
        <select-county v-if="countyShow" ref="selectCounty" :search="countySearch" @checkCounty="checkCounty"></select-county>
        <select-district v-if="districtShow" ref="selectDistrict" :search="districtSearch" @checkDistrict="checkDistrict"></select-district>
        <el-dialog
        title="nationality"
        :visible.sync="showNationality"
        width="60%"
        :close-on-click-modal="false">
            <el-table :data="nationalities" highlight-current-row >
                <el-table-column property="countryCode" label="countryCode" width="200px"></el-table-column>
                <el-table-column property="country" label="country" ></el-table-column>
                <el-table-column property="validFrom" label="validFrom" ></el-table-column>
                <el-table-column property="validTo" label="validTo" ></el-table-column>
            </el-table>
        </el-dialog>
        <el-dialog  title="合約明細" :visible.sync="showRightDetail" width="55%" :before-close='cancelEdit'>
            <el-form :inline="true" label-position="right" label-width="100px" :model="agreementDetail" class="demo-form-inline f-l">
                <div>
                    <el-form-item label="IP Base No">
                    <el-input v-model="formDummy.ipBaseNo" readonly></el-input>
                </el-form-item>
                <el-form-item label="Right">
                    <!--  -->
                    <el-select v-model="agreementDetail.groupName" style="width: 250px;" placeholder="">
                        <el-option label="PERFORMING RIGHT" value="PER"></el-option>
                        <el-option label="MECHANICAL RIGHT" value="MEC"></el-option>
                        <el-option label="SYNCHRONIZATION RIGHT" value="ZYN"></el-option>
                        <el-option label="New MEDIA RIGHT" value="NOD"></el-option>
                        <el-option label="Other RIGHT" value="OR"></el-option>
                    </el-select>
                </el-form-item>
                </div>
                <div>
                    <el-form-item label="Name">
                        <el-input v-model="detailName" style="width: 585px" readonly></el-input>
                    </el-form-item>
                </div>
            </el-form>
            <el-table
                :data="ipRightAll[agreementDetail.groupName]"
                border
                stripe
                @row-click="openDetails" height="400px">
                <el-table-column sortable prop="societyCode" label="Soc" width="70px"></el-table-column>
                <el-table-column sortable prop="rightCode" label="Right" width="85px"></el-table-column>
                <el-table-column sortable prop="roleCode" label="Role" width="79px"></el-table-column>
                <el-table-column sortable prop="validFrom" label="Valid From" width="200px"></el-table-column>
                <el-table-column sortable prop="validTo" label="Valid To" width="200px"></el-table-column>
                <el-table-column sortable prop="signatureDate" label="Signature Date" width="200px"></el-table-column>
                <el-table-column sortable prop="summary" label="summary" width="170px"></el-table-column>
            </el-table>

            <el-row :gutter="20">
                <el-col :span="12">
                    <span>Include</span>
                    <el-table 
                        :data="includes"
                        border
                        stripe
                        width="40%">
                        <el-table-column  prop="Code" label="code" ></el-table-column>
                        <el-table-column  prop="Description" label="Description" ></el-table-column>
                    </el-table>
                </el-col>
                <el-col :span="12">
                    <span>Exclude</span>
                    <el-table 
                        :data="excludes"
                        border
                        stripe
                        width="40%">
                        <el-table-column  prop="Code" label="code" ></el-table-column>
                        <el-table-column  prop="Description" label="Description" ></el-table-column>
                    </el-table>
                </el-col>
            </el-row>
        </el-dialog>
    </div>
</template>

<script>
    import axios from '../../utils/httpRequest'
    import transferDialog from './dialog/transfer'
    import listBankDialog from './dialog/bank'
    import editBankDialog from './dialog/bank_edit'
    import editSuccessorDialog from "./dialog/successor";
    import selectIp from '../../components/select-ip'
    import selectCountry from '@/components/select-country'
    import selectCounty from '@/components/select-county'
    import selectDistrict from '@/components/select-district'

    const cityOptions = ['P', 'M', 'CP', 'MA']
    export default {
        name: 'info',
        inject: ['reload'],
        data () {
            var validateYearInput = (rule, value, callback) => {
                if (!Number.isInteger(value)) {
                    callback(new Error('請輸入數字值'))
                } else {
                    if (value < 1000 || value > 2100) {
                        callback(new Error('不能大於2100或者小於1000！'))
                    } else {
                        callback()
                    }
                }
            }
            var validateMounthInput = (rule, value, callback) => {
                if (!Number.isInteger(value)) {
                    callback(new Error('請輸入數字值'))
                } else {
                    if (value < 1 || value > 12) {
                        callback(new Error('不能大於12或者小於0！'))
                    } else {
                        callback()
                    }
                }
            }
            var validateDayInput = (rule, value, callback) => {
                if (!Number.isInteger(value)) {
                    callback(new Error('請輸入數字值'))
                } else {
                    if (value < 1 || value > 31) {
                        callback(new Error('不能大於31或者小於0！'))
                    } else {
                        callback()
                    }
                }
            }
            return {
                top_barShow:true,
                loading: false,
                tabShow: false,
                peopleBank: '',
                update5: true,
                // correspond_type: 'Home',
                myBankData: [],
                benBank: [],
                options: [],
                rulesFormDummy: {
                    birthYear: [{ validator: validateYearInput, trigger: 'blur' }],
                    birthMounth: [{ validator: validateMounthInput, trigger: 'blur' }],
                    birthDay: [{ validator: validateDayInput, trigger: 'blur' }]
                },
                activeStep: 1,
                update: true,
                steps1: [
                    {
                        name: 'IPBase'
                    }, {
                        name: 'IP Right'
                    }, {
                        name: 'IPI Info'
                    }
                ],
                steps: [
                    {
                        name: 'IPBase'
                    }, {
                        name: 'IP Right'
                    }, {
                        name: 'IPI Info'
                    }, {
                        name: 'MemberIDInfo'
                    },
                    {
                        name: ' MemberDetail'
                    },
                    {
                        name: 'Contact'
                    },
                    {
                        name: 'Address'
                    },
                    {
                        name: 'Bank'
                    },
                    {
                        name: 'Successor'
                    },
                    {
                        name: 'Remark'
                    }
                ],
                stepshow: true,
                scrollTop: '',
                deathShow: true,
                scrollTopIndex: '',
                contactIndex: '',
                addressIndex: '',
                tableData6Index: '',
                tableData66Index: '',
                tableData7Index: '',
                successorBigindex: '',
                activeNames: '2',
                activeNames1:['1'],
                centerDialogVisible: false,
                SuccessorVisible: false,
                showRightDetail: false,
                dummyDialogVisible: false,
                dummyShow: true,
                checkedCities1: ['P', 'M'],
                contactTableVisible: false,
                address11TableVisible: false,
                tabledata6Visible: false,
                tabledata7Visible: false,
                dialogRemarkConfig:{
                    visible:false,
                    index:null,
                    data:{}
                },
                nShow: false,
                cities: cityOptions,
                checkedDummy: false,
                pchecked: true,
                mchecked: true,
                cpchecked: true,
                machecked: true,
                disableshow: true,
                formInline: {
                    user: null,
                    region: null
                },
                formDummy: {
                    ipBaseNo: this.$route.query.ipBaseNo,
                    ipType: '',
                    sex: '',
                    birthPlace: '',
                    birthState: '',
                    birthCountryName: '',
                    correspondType: '',
                    status: '',
                    birthYear: '',
                    birthMounth: '',
                    birthDay: '',
                    birthDate: '',
                    deathDate: '',
                    deathYear: '',
                    deathMounth: '',
                    deathDay: '',
                    id: this.$route.query.ipId
                },
                formAgreement: {
                    groupName: 'PER'
                },
                agreementDetail: {
                    groupName: ''
                },
                formContact: {
                    correspType: null,
                    mailName: null,
                    telephoneNo: null,
                    mobileNo: null,
                    faxNo: null,
                    otherNo: null,
                    email: null,
                    contactPeriod: null,
                    url: null
                },
                formContactAddress: {
                    correspType: null,
                    careof: null,
                    country: null,
                    countryCode: null,
                    county: null,
                    district: null,
                    zipCode: null,
                    villageStreet: null,
                    section: null,
                    lane: null,
                    alley: null,
                    building: null,
                    floor: null,
                    room: null,
                    mailBox: null
                },
                formMemberInfo: {
                    idCardNo: null,
                    language: null,
                    birthDate: null,
                    deathDate: null,
                    birthCountryName: null,
                    permanentNo: null,
                    permanentValidFrom: null,
                    permanentValidTo: null
                },
                formMemberDetail: {
                    associateIpiNo: null,
                    adminIpiNo: null,
                    role: 'CA',
                    ciNo: null,
                    brNo: null,
                    memberNo: null,
                    jointDate: null,
                    upaYear: null,
                    votes: null,
                    deedSignP: false,
                    deedSignM: false,
                    deedSignCp: false,
                    deedSignMa: false,
                    terminateDate: null,
                    terminateReason: null
                },
                formMemberDetail1: {
                    deedSignP: false,
                    deedSignM: false,
                    deedSignCp: false,
                    deedSignMa: false,
                    terminateDate: null,
                    terminateReason: null
                },
                formTermination: {
                    user: null,
                    region: null
                },
                formAddress: {
                    user: null,
                    region: null
                },
                formRemark: {
                    type: 'all'
                },
                formRecipient: {
                    user: null,
                    region: null
                },
                datalist: {},
                ipRight: {},
                ipRightAll: {},
                tableData: [],
                mySuccessArray: [],
                tableData2: [],
                tableData4: [],
                tableData5: [],
                tableData6: [],
                tableData7: [],
                successBaseInfo: { seq: '', name: '', sex: '', relationship: '', jointDate: '', validFrom: '', validTo: '', share: '',idCardNo: null,
                    language: null,
                    birthDate: null,
                    amendTime: null,
                    ipBaseNo: null },
                // tableData8: [],
                // contactData: [],
                succContact: [],
                successorInfo: {},
                table10Data: [],
                contactType: '',
                contactData: {},
                succAddress: [],
                table11Data: [],
                addressData: {},
                addressType: '',
                table6Data: [],
                table66Data: [
                    {
                        bankName: '',
                        accountName: '',
                        accountNo: ''
                    }
                ],
                table666Data: [],

                /**
                 * 新写的
                 */
                showTransfer: false,
                transferInfo: {},
                showEditBank: false,
                showBankList: false,
                memberList: [],
                editBankInfo: [],
                bankDtlsInfo: [],
                editBankInfoOptions: {index:0, type: 'member'},
                successorOptionType: 'add',
                editSuccessorIndex: 0,

                remarksData: {
                    all: [],
                    A: [],
                    BN: [],
                    CN: [],
                    GN: [],
                    OT: [],
                    REP: [],
                    SME: [],
                    STW: [],
                    UP: []
                },


                /**
                 *
                 * end
                 */
                //
                selfData: [],
                // 缓存字段，感觉多余
                table6DataTemp: [],
                bankDataTemp: [],

                // getip
                selectIpShow: false,
                ipSearch: {},
                selectIpType: '',

                countrySearch: {
                    tisN: ''
                },
                countryShow: false,
                countryType: '',

                countySearch: {
                    county: '',
                    countyCode: ''
                },
                countyShow: false,

                districtSearch: {
                    countyCode: ''
                },
                districtShow: false,

                rightData: [],
                roleData: [],

                dialogTableVisible:false,
                rateTable:[],tableresult:' ',
                rateTitle:'匯率',
                rateTotal: 0,
                currentPage: 1,
                nationalities: [],
                showNationality: false,
                includes: [],
                excludes: [],
                detailName: ''

            }
        },
        watch:{
            contactType: function(newVal, oldVal){
                this.contactData = {};
                let flag = false;
                this.table10Data.forEach( item => {
                    if(item.type == newVal){
                        this.contactData = item;
                        flag = true;
                    }
                })
                if(!flag){

                    let info = {
                        type: newVal,
                        mailName: '',
                        telephoneNo: '',
                        mobileNo: '',
                        faxNo: '',
                        otherNo: '',
                        email: '',
                        contactPeriod: '',
                        url: ''
                    }
                    this.contactData = info;
                    this.table10Data.push(info);
                }
            },
            addressType: function(newVal, oldVal){
                this.addressData = {};
                let flag = false;
                this.table11Data.forEach( item => {
                    if(item.type == newVal){
                        this.addressData = item;
                        flag = true;
                    }
                })
                if(!flag){
                    let info = {
                        type: newVal,
                        careof: null,
                        country: null,
                        countryCode: null,
                        county: null,
                        district: null,
                        zipCode: null,
                        villageStreet: null,
                        section: null,
                        lane: null,
                        alley: null,
                        building: null,
                        floor: null,
                        room: null,
                        mailBox: null
                    }
                    this.addressData = info;
                    this.table11Data.push(info);
                }
            }

        },
        components: {
            transferDialog,
            editBankDialog,
            listBankDialog,
            editSuccessorDialog,
            selectIp,
            selectCountry,
            selectCounty,
            selectDistrict
        },
        methods: {
            getCountry(type){
                
                this.countryType = type;
                this.countrySearch = {
                    // tisN: this.formDummy.countryCode
                    tisN: ''
                }
                this.countryShow = true;
                this.$nextTick( () => {
                    this.$refs.selectCountry.init();
                })
            },
            showCountry(){
                if(this.nationalities.length > 1){
                    this.showNationality = true
                }
            },
            checkCountry(info){
                if(this.countryType == 'nationality'){
                    this.$set(this.formDummy, 'countryCode', info.tisN);
                    this.$set(this.formDummy, 'nationality', info.name);
                }else if(this.countryType == 'birth'){
                    this.$set(this.formDummy, 'birthCountryCode', info.tisN);
                    this.$set(this.formDummy, 'birthCountryName', info.name);
                }else if(this.countryType == 'address'){
                    this.$set(this.addressData, 'countryCode', info.tisN);
                    this.$set(this.addressData, 'country', info.name);
                }

            },

            getCounty(){
                this.countySearch = {
                    county: this.addressData.county
                }
                this.countyShow = true;
                this.$nextTick( () => {
                    this.$refs.selectCounty.init();
                })
            },
            checkCounty(info){
                console.log('?: ', info);
                this.$set(this.addressData, 'countyCode', info.countyCode);
                this.$set(this.addressData, 'county', info.county);
                this.districtChange();
            },
            
            getIp(type, baseNo){
                this.ipSearch = {
                    ip_no: baseNo
                }
                this.selectIpType = type;
                this.selectIpShow = true;
                this.$nextTick( () =>{
                    this.$refs.selectIpCom.init();
                })
            },
            checkIp(info){
                if(this.selectIpType == 'coip'){
                    this.$set(this.formMemberDetail, 'correspIpiNo', info.ip_base_no);
                    this.$set(this.formMemberDetail, 'correspIpiName', info.name);
                }else{
                    this.$set(this.formMemberDetail, 'adminIpiNo', info.ip_base_no);
                    this.$set(this.formMemberDetail, 'adminIpiName', info.name);
                }
            },
            getDistrict(){
                this.districtSearch = {
                    countyCode: this.addressData.countyCode
                }
                this.districtShow = true;
                this.$nextTick( () => {
                    this.$refs.selectDistrict.init();
                })
            },
            checkDistrict(info){
                this.$set(this.addressData, 'district', info);
                this.districtChange();
            },
            changeSpecialExchangeRate(status){
                this.formMemberDetail.specialExchangeRate = status;
            },
            // 編輯銀行信息
            editBank(index,row){
                console.log(index);
                this.showEditBank = true;
                this.editBankInfoOptions.editBankInfoIndex = index;
                this.editBankInfoOptions.type = 'successor';
                this.editBankInfo = this.tableData7[index].bankAccs;
              
                var rowData0;
                var t_index =0;
                this.editBankInfo.forEach((item,index)=>{
                    if(item.status==1){
                        rowData0=item;
                        t_index=index;
                        return;
                    }
                });
                if(!rowData0 && this.editBankInfo.length > 0){
                    //获取第一条
                    rowData0=this.editBankInfo[0];
                }
                
                console.log(rowData0);
                this.$refs.editBankDialog.init(t_index,rowData0);
            },
            editMemberBank(index, value){
                console.log(index,'index');
                this.showEditBank = true;
                this.editBankInfoOptions.editBankInfoIndex = index;
                this.editBankInfoOptions.type = 'member';
                this.editBankInfo = this.memberList[0].bankAccs;
                var rowData0;
                var t_index =0;
                // this.editBankInfo.forEach((item,index)=>{
                //     if(item.status==0){
                //         rowData0=item;
                //         t_index=index;
                //         return;
                //     }
                // });
                if(!rowData0 && this.editBankInfo.length > 0){
                    //获取第一条
                    rowData0=this.editBankInfo[0];
                }
                this.$refs.editBankDialog.init(t_index,rowData0,'',this.formMemberDetail);
            },
             viewBankHistory(index, value,type){
                 this.editBankInfoOptions.editBankInfoIndex = index;
                 this.editBankInfoOptions.type = type;
                 let editBankInfo;
                 if(!type || type==='member'){
                    editBankInfo = this.memberList[0].bankAccs;
                 }else if(type==="successor"){
                    editBankInfo = this.tableData7[index].bankAccs;
                 }
                 console.log('viewBankHistory:this.editBankInfo:', editBankInfo);

                 this.editBankInfo = this.fixBankHistory(editBankInfo);
                 this.showBankList = true;
           
            },
            fixBankHistory(bankInfo) {
                let statusAvaliableFlag = false;
                const tableData = this.$utils.copy(bankInfo);
                // 修复编辑后查看历史出现两个status=1的有效记录的问题；
                const fixedTableData = tableData.map((d, i) => {
                    if (d.status) {
                        // 看看它是不是唯一一个 status = 1 的数据；
                        // console.log('tableData.map:d.status=1:', d)
                        if (statusAvaliableFlag) {
                            // 如果有id说明不是当前新的数据；
                            if (d.id) {
                                // 有id的数据是后台请求来的，有新编辑的数据（新编辑的没有id），旧的数据就不应该显示状态为生效了。
                                d.status = 'invalid';
                                console.log('fix history status = 0:', d);
                            }
                        } 
                        statusAvaliableFlag = true;
                    }
                    return d;
                })

                console.log('fixBankHistory:TableData:', fixedTableData);
                return fixedTableData;
            },
            updateBank(info){
                console.log('updateBank');
                if(info.type == 'member'){
                    this.memberList[0].bankAccs = info.value;
                }else{
                    this.tableData7[info.index].bankAccs = info.value;
                }
            },
            updateBankCtls(list){
                console.log('updateBankCtls: ', list);
                this.bankDtlsInfo = list;
            },
            adddata1 () {
                let myArray = { societyCode: '', rightCode: '', roleCode: '', validFrom: '', validTo: '', membershipShare: '', signatureDate: '', ipBaseNo: '' };
                if(!this.ipRight[this.formAgreement.groupName]){
                    this.$set(this.ipRight, this.formAgreement.groupName, []);
                }
                this.ipRight[this.formAgreement.groupName].push(myArray);
            },
            showDetail(){
                let _this = this
                _this.showRightDetail = true
                // this.dataListLoading = true
                _this.includes = [] 
                _this.excludes = []
                _this.$http.get("/ip/getIpAgreement/" + _this.formDummy.ipBaseNo).then(res => {
                    // let _data = res.data.data || []
                    // _data.forEach(d=> {
                    //     _this.ipRightAll[d.groupName] =d.agreements
                    // })
                    _this.ipRightAll = res.data.data || {}
                    _this.agreementDetail.groupName = 'PER'
                    console.log(_this.ipRightAll)
                    // this.dataListLoading = false
                })

                var paName =  _this.tableData2.find(item => item.nameType == 'PA') || {}
                this.detailName = paName.name
            },
            openDetails(row){
                console.log(row)
                this.includes = row.includes|| []
                this.excludes = row.excludes|| []
            },
            cancelEdit(){
                this.showRightDetail = false
                this.ipRightAll = {}
                this.includes = []
                this.excludes = []
            },
            deletedata1 (index, rows) {
                this.$msgbox.confirm(`確定進行[删除]操作?`, '提示', {
                    confirmButtonText: '確定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    rows.splice(index, 1)
                })
            },
            adddata2 () {
                this.$http.get('/ref/sequence', {params:{prefix: '00', seq_length: 11 }}).then( res => {
                    if(res.success){
                        let myArray = [{lastName: '', firstName: '', chineseFirstName: '', chineseLastName: '', nameNo: res.data, nameType: ''}]
                        this.tableData2 = [...this.tableData2, ...myArray]
                    }
                })

            },
            deletedata2 (index, rows) {
                this.$msgbox.confirm(`確定進行[删除]操作?`, '提示', {
                    confirmButtonText: '確定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    rows.splice(index, 1)
                })
            },
            adddata4 () {
                let myArray = [{validFrom: null, validTo: null, right: null}]
                this.tableData4 = [...this.tableData4, ...myArray]
            },
            deletedata4 (index, rows) {
                this.$msgbox.confirm(`確定進行[删除]操作?`, '提示', {
                    confirmButtonText: '確定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    rows.splice(index, 1)
                })
            },
            adddata5 () {
                let myArray = [{type: null, validFrom: null, validTo: null, amendTime: null, amendUser: null}]
                this.tableData5 = [...this.tableData5, ...myArray]
            },
            deletedata5 (index, rows) {
                this.$msgbox.confirm(`確定進行[删除]操作?`, '提示', {
                    confirmButtonText: '確定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    rows.splice(index, 1)
                })
            },

            addSuccessor () {
                this.successorOptionType = 'add';
                this.SuccessorVisible = true
                this.successBaseInfo = { seq: '', name: '', sex: '', relationship: '', jointDate: '', validFrom: '', validTo: '', share: '', };
                this.succContact = [
                    // {
                    //     telephoneNo: '',
                    //     defEmail: '',
                    //     faxNo: '',
                    //     mobileNo: '',
                    //     correspType: ''
                    // }
                ]
                this.succAddress = [
                    // {
                    //     country: '',
                    //     lane: '',
                    //     district: '',
                    //     correspType: ''
                    // }
                ]
                this.$nextTick( () => {
                    this.$refs.successorDialog.init();
                })
                this.successorInfo = {info: this.successBaseInfo, contacts: this.succContact, addresses: this.succAddress};

            },
            // addSuccessorContact(){
            //     this.succContact.push({
            //             telephoneNo: '',
            //             email: '',
            //             faxNo: '',
            //             mobileNo: '',
            //             correspType: ''
            //         })
            // },
            // deleteSuccessorContact(index){
            //     this.$msgbox.confirm(`確定進行[删除]操作?`, '提示', {
            //         confirmButtonText: '確定',

            //         cancelButtonText: '取消',
            //         type: 'warning'
            //     }).then(() => {
            //         this.succContact.splice(index, 1)
            //     })

            // },
            // addSuccessorAddress(){
            //     this.succAddress.push({
            //             country: '',
            //             lane: '',
            //             district: '',
            //             correspType: ''
            //         })
            // },
            // deleteSuccessorAddress(index){
            //     this.$msgbox.confirm(`確定進行[删除]操作?`, '提示', {
            //         confirmButtonText: '確定',
            //         cancelButtonText: '取消',
            //         type: 'warning'
            //     }).then(() => {
            //        this.succAddress.splice(index, 1)
            //     })

            // },
            deletedata7 (index, rows) {
                this.$msgbox.confirm(`確定進行[删除]操作?`, '提示', {
                    confirmButtonText: '確定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.tableData7.splice(index, 1)
                    // this.tableData6 = [...this.tableData6, ...this.tableData7]
                    this.update = false
                    this.$nextTick(() => {
                        this.update = true
                    })
                    this.tableData6 = [...this.selfData, ...this.tableData7]
                    // this.table6Data.splice(index, 1)
                    this.options = []
                    this.options = this.tableData6
                })
            },
            editSuccessor (index, rows) {
                console.log('index: ', index);
                this.successorOptionType = 'edit';
                this.editSuccessorIndex = index;
                this.SuccessorVisible = true;
                this.$nextTick( () => {
                    this.$refs.successorDialog.init();
                })
                // this.succContact = rows.contacts;
                // this.succAddress = rows.addresses;
                // this.successBaseInfo = rows.info;
                this.successorInfo = rows;
            },
            saveSuccessorInfo(info,successorOptionType){
                if(successorOptionType === 'edit'){
                    this.tableData7[this.editSuccessorIndex].info = info.info||{}
                    this.tableData7[this.editSuccessorIndex].addresses = info.addresses||{}
                    this.tableData7[this.editSuccessorIndex].contacts = info.contacts||{}
                    this.tableData7[this.editSuccessorIndex].bankAccs = info.bankAccs||{}
                }else if (successorOptionType === 'add'){
                    this.tableData7.push(info)
                }
            },
            closeSuccessor(){
                this.SuccessorVisible = false;
            },
            adddata8 () {
                // let createDate = new Date();
                let myArray = [{createTime: "", createUserName: "", type: this.formRemark.type == 'all' ? 'A' : this.formRemark.type, content: null}]
                this.remarksData[myArray[0].type] = [...this.remarksData[myArray[0].type], ...myArray]
                this.remarksData.all = [...this.remarksData.all, ...myArray]
            },
            deletedata8 (index, rows) {
                this.$msgbox.confirm(`確定進行[删除]操作?`, '提示', {
                    confirmButtonText: '確定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    rows.splice(index, 1)
                })
            },
            transfer () {
                console.log('showTransfer');
                // 弹框显示此ip信息 點擊保存时，保存將次ip transfer
                this.transferInfo = {
                    ipiInfo: this.tableData2,
                    baseInfo: this.formDummy
                }
                this.showTransfer = true;
            },
            // 主要用来修改show
            changeValue(obj){
                console.log(obj.name,'--', obj.value);
                this[obj.name] = obj.value;
            },
            saveChangeBank(a,bank,option){
                a.id=''
                console.log(bank)
                console.log(this.bankDtlsInfo)
                if(option.type === 'member'){
                    let index =this.memberList[0].bankAccs.findIndex((value)=>value.validFrom==bank.validFrom);
                    console.log(index != '-1')
                    if(index != '-1'){
                        this.bankDtlsInfo[index]=bank
                    }else{
                        bank.id=''
                        this.bankDtlsInfo.push(bank)
                    }
                    this.memberList[0].bankAccs.unshift(a)
                }else {
                    let editBankInfoIndex = option.editBankInfoIndex || 0;
                    let index =this.tableData7[editBankInfoIndex].bankAccs.findIndex((value)=>value.validFrom==bank.validFrom);
                    let successorId = this.tableData7[editBankInfoIndex].info.successorId
                    bank.successorId = successorId
                    a.successorId = successorId
                    if(index != '-1'){
                        this.bankDtlsInfo[index]=bank
                    }else{
                        bank.id=''
                        this.bankDtlsInfo.push(bank)
                    }
                    this.memberList[0].bankAccs.unshift(a)
                    this.tableData7[editBankInfoIndex].bankAccs.unshift(a)
                }
                
                this.showEditBank=false
            },
            saveIp () {
                // 保存的时候formMemberInfo+formMemberDetail=info
                if (this.dummychecked) {
                    this.formDummy.dummy = 'Y'
                }
                if (!this.dummychecked) {
                    this.formDummy.dummy = 'N'
                }

                if(!this.formDummy.birthDate){
                    // this.$toast({tips: '請選擇出生日期'});
                    // return;
                }else{
                    this.formDummy.birthYear = new Date(this.formDummy.birthDate).getFullYear();
                    this.formDummy.birthMounth = new Date(this.formDummy.birthDate).getMonth() + 1;
                    this.formDummy.birthDay = new Date(this.formDummy.birthDate).getDate();
                }
                if(this.formDummy.deathDate){
                    this.formDummy.deathYear = new Date(this.formDummy.deathDate).getFullYear();
                    this.formDummy.deathMounth = new Date(this.formDummy.deathDate).getMonth() + 1;
                    this.formDummy.deathDay = new Date(this.formDummy.deathDate).getDate();

                }
                // this.formDummy.correspondType = this.correspond_type
                this.tableData7.forEach((item, index) => {
                    if(!item.info.royShare && item.info.royShare+'' != '0'){
                        delete this.tableData7[index].info.royShare
                    }
                })
                let remarks = [];
                remarks = this.remarksData.all;
                // for(let key in this.remarksData){
                //     if(key != 'all'){
                //         remarks = [...remarks, ...this.remarksData[key]];
                //     }
                // }
                let rights = [];
                for(let key in this.ipRight){
                    rights.push({agreements: this.ipRight[key], groupName: key});
                }
                // if (this.formMemberInfo == null || this.formMemberDetail == null) {
                if(!this.tabShow){
                    let params = {
                        'bankAccDtls': this.memberList[0] ? this.memberList[0].bankDtlsInfo : [],
                        'bankAccs': this.memberList[0] ? this.memberList[0].bankAccs : [],
                        'contacts': this.table10Data,
                        'addresses': this.table11Data,
                        'info': null,
                        'ip': {
                            'agreementGroups': rights,
                            'info': this.formDummy,
                            'names': this.tableData2
                        },
                        'memberships': this.tableData5,
                        'remarks': remarks,
                        'rights': this.tableData4,
                        // 'successors': JSON.parse(localStorage.getItem('successorData'))
                        successors: this.tableData7
                    }
                    axios.post('/member', params).then(res => {
                        if (res.status === 200) {
                            this.$toast({tips: '保存成功'})
                            this.$bus.$emit('closeCurrentTab', () => { this.$router.push({name: 'member-ipi', query: {update: true}})});
                        }
                    })
                }

                let tempDetail = this.$utils.copy(this.formMemberDetail);
                if(tempDetail.specialExchangeRate){
                    tempDetail.specialExchangeRate = 'Y';
                }else{
                    tempDetail.specialExchangeRate = 'N';
                    tempDetail.specialExchangeCountry = '';
                }
                if (this.tabShow) {
                    let params = {
                        'bankAccDtls': this.bankDtlsInfo,
                        'bankAccs':  this.memberList[0] ? this.memberList[0].bankAccs : [],
                        'contacts': this.table10Data,
                        'addresses': this.table11Data,
                        'info': Object.assign(tempDetail, this.formMemberInfo),
                        'ip': {
                            'agreementGroups': rights,
                            'info': this.formDummy,
                            'names': this.tableData2
                        },
                        'memberships': this.tableData5,
                        'remarks': remarks,
                        'rights': this.tableData4,
                        successors: this.tableData7
                    }

                    axios.post('/member', params).then(res => {
                        if (res.status === 200 && (!res.data.code || res.data.code == 200)) {
                            this.$toast({tips: '保存成功'})
                            this.$bus.$emit('closeCurrentTab', () => { this.$router.push({name: 'member-ipi', query: {update: true}})});

                        }else{
                            this.$toast({tips: res.data.message})
                        }
                    })
                }
            },
            successorCommit () {
                this.SuccessorVisible = false;
                this.formDummy.dummy = this.dummychecked ? 'Y' : 'N';
                let obj = {}
                let myObj = {}
                myObj = this.successBaseInfo;
                obj.info = myObj;
                obj.contacts = this.succContact;
                obj.addresses = this.succAddress;

                /**
                 * 此處拿到弹框數據，
                 * 判断添加还是修改，添加则push一條數據，修改，则更新原數據
                 *
                 */
                if(this.successorOptionType == 'add'){
                    this.tableData7.push(obj)
                    // 同时 memberList 里添加一條新的
                    // this.memberList.push(obj);
                }else{
                    this.tableData7[this.editSuccessorIndex].info = obj.info;
                    this.tableData7[this.editSuccessorIndex].contacts = obj.contacts;
                    this.tableData7[this.editSuccessorIndex].addresses = obj.addresses;
                    //保留原有successor的bank info
                }
            },
            //    继承人维护
            successorName (index, item) {
                this.successorBigindex = index
                console.log(this.table6Data)
                // 切換後初始化數據
                if (this.table6Data && this.table6Data.length > 0) {
                    this.table6Data.forEach((subitem, subindex) => {
                        this.succAddress = this.table6Data[index].addresses
                        this.succContact = this.table6Data[index].contacts
                        if (!this.table6Data[index].info) {
                            this.successBaseInfo = {}
                        }
                        if (this.table6Data[index].info) {
                            this.successBaseInfo = this.table6Data[index].info
                        }
                        if (!this.table6Data[index].contacts) {
                            this.succContact = []
                        }
                        if (this.table6Data[index].contacts) {
                            this.succContact = this.table6Data[index].contacts
                        }
                        if (!this.table6Data[index].addresses) {
                            this.succAddress = []
                        }
                        if (this.table6Data[index].addresses) {
                            this.succAddress = this.table6Data[index].addresses
                        }
                    })
                }
            },
            ipTypeChange(){
                this.formDummy.sex = '';
            },
            taxRateChange(){
                console.log('111: ',this.formMemberDetail.taxRate);
                this.formMemberDetail.taxRate = this.formMemberDetail.taxRate.replace(/[^\d.]/g, '');
            },
            getRightSoc () {
                let params = this.formAgreement.groupName
                this.tableData1 = []
                axios.get('/ip/right?cash_right=' + params).then(res => {
                    if (res.status === 200) {
                        this.rightData = res.data;
                    }
                })
            },
            getRoleList () {
                axios.get('/ref/role').then(res => {
                    if (res.status === 200) {
                        this.roleData = res.data
                    }
                })
            },
            // 根据县市查zipCode
            districtChange(){
                if(this.addressData.district.trim() == '' || !this.addressData.countyCode){
                    this.addressData.zipCode = '';
                    return;
                }
                this.$http.get('/member/getZipCode', {params: {countyCode: this.addressData.countyCode, district: this.addressData.district}}).then(res => {
                    if(res.data.code == 200){
                        this.addressData.zipCode = res.data.data;
                    }
                })
            },
            handleNum(num){
                if(!num){
                    return '00';
                }
                num = parseInt(num);
                if(num < 10){
                    return '0' + num;
                }
                return num;
            },


            handleCurrentChange (val) {
                this.getSocietyTaxRate(val)
            },
            getSocietyTaxRate(page){
                page = page ? page : 1;
                this.tableresult = '數據加載中...'
                this.$http.get('/ref/society/tax/rate',{params:{countryCode:'',countryName:'', page_num: page, page_size: 10}}).then(res => {
                    if(res.success && res.status == 200){
                        this.rateTable = res.data.list;
                        this.dialogTableVisible = true;
                        this.rateTotal = res.data.total;
                        console.log('curr: ', page);
                        this.currentPage = page ? page : 1;
                    }else{
                        this.$toast({tips: res.statusText})
                    }
                        this.tableresult = this.rateTable.length == 0 ? '暫無數據' : ' '
                })
            },
            selectRate(index,row){
                this.formMemberDetail.specialExchangeCountry = row.countryCode;
                this.$refs.editBankDialog.selectRate(row);
                this.dialogTableVisible = false
            },

        },
        mounted () {
            this.top_barShow = this.$route.query.type ? false :true
            this.tabShow = this.$route.query.tabShow === 'true' ? true : false;
            this.getRightSoc();
            this.getRoleList();
            // 最後一次修改不要在变化了銀行信息不可能在改了
            // 在頁面加載时初始化 銀行人員信息不能在变化时更新銀行人員信息否则继承人信息無非追加
            this.$nextTick(function () {
                window.addEventListener('scroll', this.onScroll)
            })
            let params = this.$route.query.ipBaseNo;
            this.loading = true;
            axios.get('/member/' + params).then(res => {
                this.loading = false;
                // console.log('ipinfo 拿到數據: ', new Date().getTime());
                if (res.status === 200) {
                    this.datalist = res.data.ip;
                    let agreementGroups = res.data.ip.agreementGroups ? res.data.ip.agreementGroups : [];

                    agreementGroups.forEach( item => {
                        this.ipRight[item.groupName] = item.agreements;
                    })

                    
                    // res.data.info = res.data.info ? res.data.info : {};
                    this.formMemberDetail = this.$utils.copy(res.data.info ? res.data.info : {});
                    let temp = this.$utils.copy(res.data.info ? res.data.info : {});

                    this.formMemberInfo = {
                        idCardNo: temp.idCardNo,
                        language: temp.language,
                        permanentNo: temp.permanentNo,
                        permanentValidFrom: temp.permanentValidFrom,
                        permanentValidTo: temp.permanentValidTo
                    }


                    this.formMemberInfo.birthCountryName = res.data.ip.info.birthCountryName;
                    this.tableData2 = this.datalist.names
                    this.tableData5 = res.data.memberships
                    this.tableData5.forEach( item => {
                        item.validFrom = item.validFrom ? item.validFrom.split(' ')[0] : '';
                        item.validTo = item.validTo ? item.validTo.split(' ')[0] : '';
                        item.amendTime = item.amendTime ? item.amendTime.split(' ')[0] : '';
                    })
                    this.tableData4 = res.data.rights
                    this.formDummy = res.data.ip.info;
                    if(this.formDummy.ipType != 'N'){
                        this.steps.splice(3, 1);
                    }
                    this.checkedDummy = res.data.ip.info.dummyIpiFlag == 'Y' ? true : false;
                    //soc 161 且不为dummyIP 才能为ture
                    this.tabShow = this.tabShow && (res.data.ip.info.dummyIpiFlag != 'Y');
                    //或
                    if(res.data.info && res.data.ip.info.dummyIpiFlag != 'Y'){
                        this.tabShow = true;
                    }
                    console.log('tabshow: ', this.tabShow);
                    // this.dummyhandle();
                    if(this.formDummy.birthYear){
                        this.$set(this.formDummy, 'birthDate', this.formDummy.birthYear + this.handleNum(this.formDummy.birthMounth) + this.handleNum(this.formDummy.birthDay))
                    }else{
                        this.$set(this.formDummy, 'birthDate', '')
                    }
                    // 死亡日期不存在，则此會員未死亡，不显示继承人
                    if(!res.data.ip.info.deathYear){
                        this.deathShow = false;
                        this.steps.splice(8, 1);
                    }else{
                        this.$set(this.formDummy, 'deathDate' , this.formDummy.deathYear + this.handleNum(this.formDummy.deathMounth) + this.handleNum(this.formDummy.deathDay));
                    }

                    /**
                     * bank 部分
                     * 展示會員信息 及 继承人信息
                     */
                    //加入會員自己信息
                    let name = {};

                    res.data.ip.names.forEach( item => {
                        if(item.nameType == 'PA'){
                            name = item;
                        }
                    })

                    this.nationalities = res.data.ip.nationalities || []
                    if(this.nationalities.length > 1){
                        document.getElementById('countryCode').style.color='red';
                        document.getElementById('nationality').style.color='red';
                        
                    }

                    let tempObj = {
                        info: {
                            type: res.data.memberships[0] ? res.data.memberships[0].type : '',
                            name: name ? (name.chineseName ? name.chineseName : (name.lastName + ' '+ name.firstName)) : '',
                            sex: res.data.ip.info.sex,
                            idCardNo: res.data.info.idCardNo ? res.data.info.idCardNo : '',
                            language: res.data.info.language ? res.data.info.language : '',
                            share: '100'
                        },
                        bankAccs: res.data.bankAccs
                    }
                    console.log('tempObj')
                    console.log(tempObj)
                    console.log(this.formDummy)
                    if(this.formDummy.deathDate){
                        tempObj.info.share = '0';
                    }else{
                        tempObj.info.share = '100';
                    }

                    this.memberList.push(tempObj);
                    console.log(this.memberList)
                    /** Bank Account Detail for Telegraphic Tranfer ||  bankAccDtls*/
                    this.bankDtlsInfo = res.data.bankAccDtls;

                    //加入继承人信息

                    let myArray = []
                    let obj = {}
                    obj.type = res.data.ip.info.ipType
                    // let name = res.data.ip.names[0];
                    if(name){
                        if(name.chineseName){
                            obj.name = name.chineseName;
                        }else{
                            obj.name = name.lastName + ' '+ name.firstName;
                        }
                    }else{
                        obj.name = '';
                    }
                    // obj.name = name ? (name.chineseName ? name.chineseName : (name.lastName + ' '+ name.firstName)) : '';
                    obj.sex = res.data.ip.info.sex;
                    obj.idCardNo = res.data.info.idCardNo ? res.data.info.idCardNo : '';
                    obj.language = res.data.info.language ? res.data.info.language : '';
                    if(this.formDummy.deathDate){
                        obj.share = '0';
                    }else{
                        obj.share = '100';
                    }
                    myArray.push(obj)

                    this.selfData = JSON.parse(JSON.stringify(myArray));
                    this.tableData6 = JSON.parse(JSON.stringify(myArray));

                    this.tableData5 = res.data.memberships
                    this.tableData4 = res.data.rights
                    this.tableData7 = res.data.successors;

                    this.options = []
                    this.options = this.tableData6
                    this.peopleBank = this.tableData6[0].name
                    res.data.ip.agreementGroups.forEach((item, index) => {
                        if (this.formAgreement.groupName === item.groupName) {
                            this.tableData1 = res.data.ip.agreementGroups[index].agreements
                        }
                    })
                    this.tableData2 = this.datalist.names
                    if (this.datalist.info.dummyIpiFlag === 'N') {
                        this.dummyShow = true
                    } if (this.datalist.info.dummyIpiFlag === 'Y') {
                        this.dummyShow = false
                    }
                    if (res.data.info.deedSignP === 'Y') {
                        this.formMemberDetail1.deedSignP = true
                    }
                    if (res.data.info.deedSignP === 'N') {
                        this.formMemberDetail1.deedSignP = false
                    }
                    if (res.data.info.deedSignM === 'Y') {
                        this.formMemberDetail1.deedSignM = true
                    }
                    if (res.data.info.deedSignM === 'N') {
                        this.formMemberDetail1.deedSignM = false
                    }
                    if (res.data.info.deedSignCp === 'Y') {
                        this.formMemberDetail1.deedSignCp = true
                    }
                    if (res.data.info.deedSignCp === 'N') {
                        this.formMemberDetail1.deedSignCp = false
                    }
                    if (res.data.info.deedSignMa === 'Y') {
                        this.formMemberDetail1.deedSignMa = true
                    }
                    if (res.data.info.deedSignMa === 'N') {
                        this.formMemberDetail1.deedSignMa = false
                    }
                    if (res.data.info.specialExchangeRate === 'N') {
                        this.$set(this.formMemberDetail, 'specialExchangeRate', false);
                        // this.formMemberDetail1.specialExchangeRate = false
                    }
                    if (res.data.info.specialExchangeRate === 'Y') {
                        this.$set(this.formMemberDetail, 'specialExchangeRate', true);
                        // this.formMemberDetail1.specialExchangeRate = false
                    }
                }
                this.table11Data = res.data.addresses;

                this.table10Data = res.data.contacts;
                res.data.remarks.forEach( item => {
                    if(!this.remarksData[item.type]){
                        this.remarksData[item.type] = [];
                    }
                    this.remarksData[item.type].push(item);
                    this.remarksData['all'].push(item);
                })
                this.$set(this, 'addressType', this.formMemberDetail.correspondType ? this.formMemberDetail.correspondType : 'HM');
                this.$set(this, 'contactType', this.formMemberDetail.correspondType ? this.formMemberDetail.correspondType : 'HM');

            }).catch( () => {
                this.loading = false;
            })

        },
        computed: {

            sidebarFold: {
                get () { return this.$store.state.common.sidebarFold }
            }
        },
    }
</script>

<style>
    .position{
        position: fixed;
        top:150px;
        right: 20px;
        width:170px;
        text-align: left;
        background-color: #FFFFFF;
        z-index: 199999999;
    }
    li{
        list-style: none;
        cursor: pointer;
    }
    .frombox{
        margin-top: 20px;
    }
    .el-card {
        margin-bottom: 20px;
    }
    .memberdetail {
        width: 100%;
    }
    .memberdetail .el-form-item__content {
        width: calc(100% - 150px) !important;
    }
    .el-icon-circle-plus-outline {
        cursor: pointer;
    }
    .el-icon-delete {
        cursor: pointer;
    }
    .button-box{
        margin-top: 20px;
        width: 100%;
        text-align: center;
    }
    .el-step{
        cursor: pointer;
    }
    .el-form-item__label{
        margin-left: 20px;
    }
    .el-input.is-disabled .el-input__inner{
        color: #666 !important;
    }
    .contentColor.el-input .el-input__inner{
       color: red;
    }
</style>
<style lang="scss" scoped>
    @import "../../assets/scss/works.scss";
    /deep/ .el-dialog__header{
        text-align: left;
        // padding-top: 0;
    }
    .title-logo{
        float: left;
        width: 6px;
        height: 24px;
        background: #17B3A3;
    }
    h3{
        margin-left: 14px;
    }
    .boxline{
        margin-top: 20px;
    }
    /deep/ .el-dialog{
        width: 1000px;
    }
    /deep/ .el-dialog--center .el-dialog__body{
        padding: 10px 10px 25px 10px;
        /deep/ .el-form-item{
            margin-bottom: 14px;
        }
    }
    /deep/ .bold-item .el-form-item__label{
        font-weight: bold;
    }
    .el-table::before { z-index: inherit; }
    .countryName /deep/ .el-form-item__label{
        width: 130px!important;
    }
    .step-jump{
        position: relative;
    }

    .el-row {
        margin-top: 20px;
        margin-bottom: 20px;
        &:last-child {
        margin-bottom: 0;
        }
    }
    .el-col {
        border-radius: 4px;
    }
</style>
