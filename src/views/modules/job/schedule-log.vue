<template>
    <div>
        <el-dialog
            title="日誌列表"
            :visible.sync="visible"
            width="80%"
            @close="closeDialog"
             :close-on-click-modal="false"
        >
            <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
                <el-form-item>
                    <el-input v-model="dataForm.id" placeholder="任務ID" clearable></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="getDataList()">搜索</el-button>
                </el-form-item>
            </el-form>
            <el-table
                :data="dataList"
                border
                v-loading="dataListLoading"
                height="460"
                @row-dblclick="getFile"
                style="width: 100%;">
                <el-table-column
                    prop="id"
                    header-align="center"
                    align="center"
                    width="80"
                    label="日誌ID">
                </el-table-column>
                <el-table-column
                    prop="jobId"
                    header-align="center"
                    align="center"
                    width="120"
                    label="任務ID">
                </el-table-column>
                <el-table-column
                    prop="jobName"
                    header-align="center"
                    align="center"
                    label="name">
                </el-table-column>
                <el-table-column
                    prop="jobNameEn"
                    header-align="center"
                    align="center"
                    label="nameEn">
                </el-table-column>
                <el-table-column
                    prop="logPath"
                    header-align="center"
                    align="center"
                    width="250px"
                    label="日誌地址">
                </el-table-column>
                <el-table-column
                    prop="status"
                    header-align="center"
                    align="center"
                    width="90px"
                    label="狀態">
                    <template slot-scope="scope">
                        <el-tag v-if="scope.row.status == 1" size="small">執行中</el-tag>
                        <el-tag v-else-if="scope.row.status == 2" size="small">成功</el-tag>
                        <el-tag v-else size="small" type="danger">失敗</el-tag>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="jobElapsedTime"
                    header-align="center"
                    align="center"
                    width="90px"
                    label="耗时(單位: 毫秒)">
                </el-table-column>
                <el-table-column
                    prop="createTime"
                    header-align="center"
                    align="center"
                    width="180"
                    label="执行時間">
                </el-table-column>
            </el-table>
            <el-pagination
                @size-change="sizeChangeHandle"
                @current-change="currentChangeHandle"
                :current-page="pageIndex"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="pageSize"
                :total="totalPage"
                layout="total, sizes, prev, pager, next, jumper">
            </el-pagination>
        </el-dialog>
        <el-dialog
            title="任務日志"
            :visible.sync="logVisible"
            width="50%"
             :close-on-click-modal="false"
            >
            <div class="log-text">{{logText}}</div>
            <span slot="footer" class="dialog-footer">
    <el-button type="primary" @click="logVisible = false">确 定</el-button>
  </span>
        </el-dialog>
    </div>
</template>

<script>
    export default {
        data () {
            return {
                visible: false,
                logVisible:false,
                logText:'',
                dataForm: {
                    id: ''
                },
                dataList: [],
                pageIndex: 1,
                pageSize: 10,
                totalPage: 0,
                dataListLoading: false
            }
        },
        mounted(){

        },
        methods: {
            init () {
                this.visible = true
                this.getDataList()
            },
            // 获取数据列表
            getDataList () {
                this.dataListLoading = false
                let params = {
                    jobId: this.dataForm.id,
                    page_num: this.pageIndex,
                    page_size: this.pageSize
                }
                this.$http.get('/sys/job/log/List', {params}).then(res => {
                    console.log(res)
                    if (res.success) {
                        this.dataListLoading = false
                        this.dataList = res.data.data.list
                        this.totalPage = res.data.data.total
                    }
                })
            },
            getFile(row){
                console.log(row)
                let params = {
                    logPath:row.logPath
                }
                this.$http.get('/sys/job/log/path', {params}).then(res => {
                    console.log(res)
                    if (res.success && res.data) {
                        this.logText = res.data
                        this.logVisible = true
                    }
                })
            },
            // 每页数
            sizeChangeHandle (val) {
                this.pageSize = val
                this.pageIndex = 1
                this.getDataList()
            },
            // 当前页
            currentChangeHandle (val) {
                this.pageIndex = val
                this.getDataList()
            },
            // 失败信息
            showErrorInfo (id) {
                this.$http({
                    url: this.$http.adornUrl(`/sys/scheduleLog/info/${id}`),
                    method: 'get',
                    params: this.$http.adornParams()
                }).then(({data}) => {
                    if (data && data.code === 0) {
                        this.$alert(data.log.error)
                    } else {
                        this.$message.error(data.msg)
                    }
                })
            },
            closeDialog(){
                this.dataForm.id = ''
            }
        }
    }
</script>
<style scoped>
    /deep/ .el-dialog__body .log-text{
        height: 500px;
        overflow-y: auto;
    }
</style>
