<template>
    <div>
        <el-form :inline="true" class="demo-form-inline" @keyup.enter.native="searchFn(1)">
            <el-form-item prop="">
                <el-input v-model="formInline.distNo" placeholder="DistNo" style="width: 200px;"></el-input>
            </el-form-item>
            <el-form-item>
                <el-input type="text" v-model="formInline.producerCode" placeholder="ProducerCode" style="width: 260px;"></el-input>
            </el-form-item>
            <el-form-item>
                <el-input type="text" v-model="formInline.producerName" placeholder="ProducerName" style="width: 260px;"></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="searchFn(1)" v-if="isAuth('mechanical-right:reset:list:find')">查询</el-button>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="addFn()" v-if="isAuth('mechanical-right:reset:list:add')">添加</el-button>
            </el-form-item>
            <el-form-item>
                <span class="clear-search" @click="clearSearch()" v-if="isAuth('mechanical-right:reset:list:find')">清除搜索</span>
            </el-form-item>
        </el-form>
        <el-table
            :data="tableData"
            border
            stripe
            style="width: 100%"
            v-loading="loading"
            :empty-text="emptyText">
            <el-table-column
                prop="distNo"
                label="Dist No"
                min-width="100px">
            </el-table-column>
            <el-table-column
                prop="producerCode"
                label="Producer Code"
                min-width="140">
            </el-table-column>
            <el-table-column
                prop="producerName"
                label="Producer Name"
                min-width="140">
            </el-table-column>
            <el-table-column
                prop="batchNo"
                label="Batch No"
                min-width="100">
            </el-table-column>
            <el-table-column
                prop="amount"
                label="Amount"
                min-width="90">
            </el-table-column>
            <el-table-column
                prop="currency"
                label="Currency"
                min-width="100">
            </el-table-column>
            <el-table-column
                label="Local Equivalent"
                min-width="150">
                <template slot-scope="scope">
                    <!-- {{scope.row.localEquivlent}} -->
                    {{Number(scope.row.localEquivlent).toFixed(0)}}
                </template>
            </el-table-column>
            <el-table-column
                prop="createTime"
                label="Create Time"
                min-width="120">
            </el-table-column>
            <el-table-column
                prop="amendTime"
                label="Amend Time"
                min-width="140">
            </el-table-column>
            <el-table-column
                label="OP"
                width="140px"
                fixed="right">
                <template slot-scope="scope">
                    <el-button type="text" size="small" @click="editFn(scope.row)" v-if="isAuth('mechanical-right:reset:list:change')">編輯</el-button>
                    <el-button type="text" size="small" @click="editListFn(scope.row)" v-if="isAuth('mechanical-right:reset:list:changeList')">編輯清單</el-button>
                    <!-- <el-button type="text" size="small" @click="deleteFn(scope.row)">删除</el-button> -->
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :total="total" @current-change="searchFn" :current-page="currentPage">
        </el-pagination>
    </div>
</template>

<script>
    export default {
        name: 'list',
        data () {
            return {
                formInline: {
                    distNo: '',
                    producerCode: '',
                    producerName: '',
                    page_num: 1,
                    page_size: 10
                },
                tableData: [],
                total: 0,
                currentPage: 1,
                loading: false,
                emptyText: '數據加載中',
            }
        },
        mounted () {
            this.searchFn();
        },
        activated(){
            // if(this.$route.query.update){
            //     this.searchFn();
            // }
            this.$nextTick( () => {
                if(this.$route.query.update){
                    let query = this.$route.query;
                    delete query.update;
                    this.searchFn();
                }
            })
        },
        methods: {
            clearSearch(){
                this.formInline = {
                    distNo: '',
                    producerCode: '',
                    producerName: '',
                    page_num: 1,
                    page_size: 10
                }
                this.searchFn(1);
            },
            searchFn(page) {
                let ajaxData = this.$utils.copy(this.formInline);
                ajaxData.page_num = page ? page : 1;
                ajaxData.page_size = 10;
                this.loading = true;
                this.emptyText = '數據加載中';
                this.$http.get('/dist/mrd/listDistMrdSalesWithPage', {params: ajaxData}).then( res => {
                    this.loading = false;
                    if(res.success){
                        this.tableData = res.data.data.list;
                        if(! this.tableData || this.tableData.length == 0){
                            this.emptyText = '暫無數據';
                        }
                        this.total = res.data.data.total;
                        this.currentPage = ajaxData.page_num;
                        
                    }
                })
            },
            addFn(){
                this.$router.push({name: 'resetAdd'});
            },
            editFn (row) {
                this.$router.push({name: 'resetEdit', query: {id: row.id, nameId: row.id, title: row.distNo}});
            },
            editListFn (row) {
                this.$router.push({name: 'resetEditList', query: {id: row.id, nameId: row.id, title: row.distNo}});
            },
            deleteFn () {
                this.$msgbox.confirm(`確定進行[删除]操作?`, '提示', {
                    confirmButtonText: '確定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {

                }).catch( () => {
                    
                })
            }
        }
        
    }
</script>
<style lang="scss" scoped>
/deep/ .el-dialog{
    width: 500px;
}
</style>

