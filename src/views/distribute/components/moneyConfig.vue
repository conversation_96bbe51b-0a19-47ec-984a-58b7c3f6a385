<template>
  <!-- 分配金額配置  -->
  <div>
    <el-form :inline="true" label-position="left">
      <el-form-item label="總額">
        <el-input v-model="totalMoney" placeholder="Total" readonly></el-input>
      </el-form-item>
      <el-form-item label="Total Other Income">
        <el-input type="number" v-model.number="totalIncome" placeholder="" @input="totalIncomeChange()"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="addFn" :disabled="status==1||status==11||status==3||status==31||status==41||status==7||status==2||status==4||status==6||status==61">添加</el-button>
      </el-form-item>
    </el-form>
    <el-table :empty-text="emptyText" :data="tableData" border stripe style="width: 100%">
      <el-table-column prop="listSourceName" label="List Source">
      </el-table-column>
      <el-table-column prop="unexpired" label="Unexpired">
        <template slot-scope="scope">
          <el-input type="number" v-model.number="scope.row.unexpired" @input="unexpiredChange(scope.row, scope.$index)"></el-input>
        </template>
      </el-table-column>
      <el-table-column prop="otherIncome" label="Other Income">
        <template slot-scope="scope">
          <el-input type="number" v-model.number="scope.row.otherIncome" :readonly="!otherIncomeEdit" @input="otherIncomeChange(scope.row, scope.$index)"></el-input>
        </template>
      </el-table-column>
      <el-table-column prop="perOtherIncome" label="Per Other Income">
      </el-table-column>
      <el-table-column prop="netDistIncome" label="Net Dist Income">
      </el-table-column>
      <el-table-column label="operation">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="deleteAmount(scope.$index,scope.row)" :disabled="status==1||status==11||status==3||status==31||status==41||status==7||status==2||status==4||status==6||status==61">刪除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div style="margin-bottom: 20px;width: 100%;text-align: center;margin-top: 20px">
      <el-button type="primary" @click="saveMoneyData" :disabled="status==1||status==11||status==3||status==31||status==41||status==7||status==2||status==4||status==6||status==61">save</el-button>
    </div>
    <el-dialog title="添加分配金額" :visible.sync="moneyTableShow" :close-on-click-modal="false">
      <div style="width: 300px;margin: auto;">
        <el-form :inline="true" :model="addSearch">
          <el-form-item label="">
            <el-input placeholder="請輸入Source Name" v-model="addSearch.sourceName" class="input-with-select">
              <el-button slot="append" icon="el-icon-search" @click="getCategoryList()"></el-button>
            </el-input>
          </el-form-item>
        </el-form>
      </div>
      <el-table :empty-text="emptyText" stripe :data="moneyTableData">
        <el-table-column property="id" label="ID"></el-table-column>
        <el-table-column property="sourceName" label="Name"></el-table-column>
        <el-table-column property="sourceDesc" label="Desc"></el-table-column>
        <el-table-column label="operation" width="120px">
          <template slot-scope="scope">
            <i class="el-icon-check pointer" @click="checkList(scope.row)"></i>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background layout="prev, pager, next" :total='moneyTotal' @current-change="getCategoryList">
      </el-pagination>
    </el-dialog>
  </div>
</template>

<script>
import bus from './Eventbus.js'
export default {
  data() {
    return {
      distNo: '',
      emptyText: '暫無數據',
      totalIncome: 0,
      tableData: [],
      moneyTableShow: false,
      addSearch: {},
      moneyTableData: [],
      moneyTotal: 0,
      status: '',
      otherIncomeEdit: true,
      delData:[],
    }
  },
  computed: {
    totalMoney: function () {
      let total = 0;
      this.tableData.forEach(item => {
        let netDistIncome = new BigNumber((item.netDistIncome && item.netDistIncome != 'NaN') ? item.netDistIncome : 0);
        total = netDistIncome.plus(new BigNumber(total)).toFixed(6) || 0;
      })
      return total;
    },
    totalUnexpired: function () {
      let total = 0;
      this.tableData.forEach(item => {
        item.unexpired = item.unexpired ? item.unexpired : 0;
        let unexpired = new BigNumber(item.unexpired)
        total = unexpired.plus(new BigNumber(total)).toFixed(6);
      })
      return total;
    }
  },
  activated() {
    this.distNo = this.$route.query.distNo;
    this.status = this.$route.query.status;
    this.queryInfo();
  },
  created() {
    this.distNo = this.$route.query.distNo
    this.status = this.$route.query.status;
    this.queryInfo()
  },
  methods: {
    totalIncomeChange() {
      let newVal = this.totalIncome;
      // console.log('newval00000',newVal)
      if (!newVal && newVal != 0) {
        this.tableData.map(item => {
          item.otherIncome = 0;
          item.netDistIncome = 0;
          item.perOtherIncome = 0;
        })
        this.otherIncomeEdit = true;
        return;
      }
      if (newVal && newVal < 0) {
        this.$toast({ tips: 'Total Other Income 不能為負數' });
        return;
      }
      if (newVal == '') {
        // this.$toast({tips: 'Total Other Income 不能为空'});
        // return;
        newVal = 0
      }
      if (newVal || newVal === 0) {
        this.otherIncomeEdit = false;
      } else {
        this.otherIncomeEdit = true;
      }
      let _this = this
      this.tableData.forEach(item => {
        item.unexpired = item.unexpired ? item.unexpired : 0;
        if (item.unexpired <= 0) {
          return false
        }
        let unexpired = new BigNumber(item.unexpired);
        let totalUnexpired = new BigNumber(_this.totalUnexpired);


        item.otherIncome = unexpired.dividedBy(totalUnexpired).multipliedBy(new BigNumber(newVal)).toFixed(6);
        //  (item.unexpired / this.totalUnexpired) * newVal;

        item.netDistIncome = new BigNumber(item.otherIncome).plus(new BigNumber(item.unexpired)).toFixed(6);
        item.perOtherIncome = item.netDistIncome;
      })
    },
    unexpiredChange(row, index) {
      // if(!this.totalIncome){
      //     return
      // }
      if (this.totalIncome < 0) {
        this.$toast({ tips: 'Total Other Income 不能小于0' });
        return;
      }
      // 如果totalIncome 有值，為系統計算模式，所以所有數據重新計算
      let _this = this
      if ((this.totalIncome || this.totalIncome == 0) || !row) {
        this.tableData.forEach(item => {
          // 如果totalUnexpired为0不能被除直接赋值0
          if (Number(_this.totalUnexpired)) {
            item.unexpired = item.unexpired ? item.unexpired : 0;
            item.otherIncome = new BigNumber(item.unexpired).dividedBy(new BigNumber(_this.totalUnexpired)).multipliedBy(new BigNumber(_this.totalIncome)).toFixed(6);
            //  (item.unexpired / this.totalUnexpired) * this.totalIncome;
            item.netDistIncome = new BigNumber(item.otherIncome).plus(new BigNumber(item.unexpired)).toFixed(6);
            item.perOtherIncome = item.netDistIncome;
          } else {
            item.otherIncome = 0
            item.perOtherIncome = item.netDistIncome = 0;
          }
        })
      } else {
        // 如果totalIncome 無數據, 则相当于 自行填寫ohter Income模式，只需要計算当前行數據即可
        row.netDistIncome = new BigNumber(row.otherIncome).plus(new BigNumber(row.unexpired ? row.unexpired : 0)).toFixed(6);
        row.perOtherIncome = row.netDistIncome;
      }
    },
    otherIncomeChange(row, index) {
      // this.totalIncome = 0;
      // this.tableData.forEach(item => {
      //   this.totalIncome = new BigNumber(this.totalIncome || 0).plus(new BigNumber(item.otherIncome || 0)).toFixed(6);
      // })

        let _this = this
        var _totalIncome = 0
        _this.tableData.forEach((item,table_index) => {
          _totalIncome += Number(item.otherIncome)
          if(table_index == index){              
              item.perOtherIncome = item.unexpired + item.otherIncome
              item.netDistIncome = item.unexpired + item.otherIncome
          }
        })

        this.totalIncome = _totalIncome;

    },
    queryInfo() {
      this.emptyText = '數據加載中'
      this.$http.get('/dist/param/source?distNo=' + this.distNo).then(res => {
        if (res.success) {
          this.tableData = res.data.distParamSourceList;
          // bus.$emit('changelist',this.tableData)
          this.totalIncome = 0;
          this.tableData.forEach(item => {
            this.totalIncome = new BigNumber(item.otherIncome).plus(new BigNumber(this.totalIncome)).toFixed(6);
          })
        }
        this.emptyText = '暫無數據'
      })
    },
    deleteAmount(index, row) {
      console.log(row)
      this.$msgbox.confirm(`確定進行[删除]操作?`, '提示', {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // if (row.id) {
          // this.$http.delete('/dist/param/source/' + row.id).then(res => {
          //   if (res.success) {
          //     this.$toast({ tips: '删除成功' })
          //     this.tableData.splice(index, 1);
          //     // 触发列表重新計算
          //     this.unexpiredChange();
          //   }
          // })
        // } else {
          // this.tableData.splice(index, 1);
          // 触发列表重新計算
          // this.unexpiredChange();
        // }
        if(row.id){
          this.delData.push(row.id)
        }
        this.tableData.splice(index, 1);
        // 触发列表重新計算
        this.unexpiredChange();
      })
    },
    addFn() {
      this.moneyTableShow = true;
      this.getCategoryList(1);
    },
    getCategoryList(page) {

      let data = this.$utils.copy(this.addSearch);
      data.page_size = 10;
      data.page_num = page ? page : 1;
      // console.warn('datasearch', data, '((((', this.addSearch)
      this.emptyText = '數據加載中'
      this.$http.get('/list/source', { params: data }).then(res => {
        if (res.success) {
          this.moneyTableData = res.data.data.list;
          this.moneyTotal = res.data.data.total;
          // this.totalIncome= 0;
        }
        this.emptyText = '暫無數據'
      })
    },
    checkList(row) {
      if (this.tableData.length == 0) {
        this.tableData.push({
          distNo: this.distNo,
          unexpired: 0,
          listSourceName: row.sourceName,
          listSourceId: row.id || '',
          otherIncome: this.totalIncome,
          perOtherIncome: 0,
          netDistIncome: 0
        });
        this.moneyTableShow = false;
        return
      }
      this.tableData.push({
        distNo: this.distNo,
        unexpired: 0,
        listSourceName: row.sourceName,
        listSourceId: row.id || '',
        otherIncome: 0,
        perOtherIncome: 0,
        netDistIncome: 0
      });
      this.moneyTableShow = false;

      // console.log('****',this.tableData,'row',row)
    },
    saveMoneyData() {
      let ajaxData = this.$utils.copy(this.tableData);
      let flag = true;
      ajaxData.forEach(item => {
        if (!item.unexpired && item.unexpired != 0) {
          this.$toast({ tips: 'Unexpired 必填' })
          flag = false;
        } else {
          if (item.unexpired < 0) {
            this.$toast({ tips: 'Unexpired 不能為負數' })
            flag = false;
          }
        }

      })
      if (!flag || (ajaxData.length  == 0 && !this.delData.length)) {
        this.$toast({ tips: '没有需要保存的数据' })
        return;
      }
      if(this.delData.length){
        this.delData.forEach((item,index)=>{
          this.$http.delete('/dist/param/source/' + item).then(res => {
            if (res.success) {
              if(index+1 == this.delData.length){
                this.$http.post('/dist/param/source', ajaxData).then(res => {
                  if (res.success) {
                    if (res.data.code && res.data.code != 200) {
                      this.$toast({ tips: res.data.message });
                    } else {
                      this.$toast({ tips: '保存成功' });
                      this.tableData = res.data
                    }
                  }
                })
              }
            }
          })
        })
      }else{
        this.$http.post('/dist/param/source', ajaxData).then(res => {
          if (res.success) {
            if (res.data.code && res.data.code != 200) {
              this.$toast({ tips: res.data.message });
            } else {
              this.$toast({ tips: '保存成功' });
              this.tableData = res.data
            }
          }
        })
      }
      
      
    }
  },
  components: {
    bus
  }
}
</script>
