<template>
    <div>
        <el-form :inline="true" :model="formTransfer" ref="form" class="demo-form-inline" @keyup.enter.native="searchFn()">
            <el-form-item prop="ipSrcBaseNo">
                <el-input v-model="formTransfer.ipSrcBaseNo" placeholder="srcBaseNo" style="width: 140px;"></el-input>
            </el-form-item>
            <el-form-item prop="srcIpNameNo">
                <el-input v-model="formTransfer.srcIpNameNo" placeholder="srcNameNo" style="width: 140px;"></el-input>
            </el-form-item>
            <el-form-item prop="ipDestBaseNo">
                <el-input v-model="formTransfer.destIpBaseNo" placeholder="destBaseNo" style="width: 140px;"></el-input>
            </el-form-item>
            <el-form-item prop="ipDestNameNo">
                <el-input v-model="formTransfer.destIpNameNo" placeholder="destNameNo" style="width: 140px;"></el-input>
            </el-form-item>
            <el-form-item prop="status">
                <el-select v-model="formTransfer.status" placeholder="狀態" style="width: 120px;">
                    <el-option label="全部" value=""></el-option>
                    <el-option label="等待轉換" value="W"></el-option>
                    <el-option label="成功" value="T"></el-option>
                    <el-option label="已撤銷" value="U"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item prop="createTime">
                <date-picker
                    v-model="formTransfer.startTime"
                    type="date"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    format="yyyyMMdd"
                    placeholder="startTime" style="width: 160px;">
                </date-picker>
                -
                <date-picker
                    v-model="formTransfer.endTime"
                    type="date"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    format="yyyyMMdd"
                    placeholder="endTime" style="width: 160px;">
                </date-picker>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="searchFn()" v-if="isAuth('member:transfer:find')">搜索</el-button>
            </el-form-item>
            <el-form-item>
                <span class="clear-search" @click="clearSearch()" v-if="isAuth('member:transfer:find')">清除搜索</span>
            </el-form-item>
        </el-form>
        <el-table
            :data="tableData"
            border
            stripe
            style="width: 100%" 
            v-loading="loading"
            :empty-text="emptyText">
            <el-table-column
                prop="ipSrcBaseNo"
                label="srcIpBaseNo"
                width="150">
            </el-table-column>
            <el-table-column
                prop="ipSrcNameNo"
                label="srcIpNameNo"
                width="150">
            </el-table-column>
            <el-table-column
                prop="srcIpName"
                label="srcIpName"
                min-width="150">
            </el-table-column>
            <el-table-column
                prop="ipSrcType"
                label="srcType"
                width="110">
            </el-table-column>
            <el-table-column
                prop="ipDestBaseNo"
                label="destIpBaseNo"
                width="150">
            </el-table-column>
            <el-table-column
                prop="ipDestNameNo"
                label="destIpNameNo"
                width="150">
            </el-table-column>
            <el-table-column
                prop="descIpName"
                label="descIpName"
                min-width="150">
            </el-table-column>
            <el-table-column
                prop="ipDestType"
                label="descType"
                width="110">
            </el-table-column>
            <el-table-column
                prop="status"
                label="status"
                width="90">
                <template slot-scope="scope">
                    {{Status(scope.row.status)}}
                </template>
            </el-table-column>
            <el-table-column
                label="CreateTime"
                width="120">
                <template slot-scope="scope">
                    {{scope.row.createTime}}
                </template>
            </el-table-column>
            <el-table-column
                label="TransferTime"
                width="140">
                <template slot-scope="scope">
                    {{scope.row.transferTime}}
                </template>
            </el-table-column>
            <el-table-column
                fixed="right"
                label="operation"
                width="100">
                <template slot-scope="scope">
                    <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer" v-if="isAuth('member:transfer:del')&&scope.row.status == 'W'" title="撤銷">
                        <span class="a-red" @click="undo(scope.row, scope.$index)">撤銷</span>
                    </span>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :total=total @current-change="searchFn" :current-page="currentPage">
        </el-pagination>
    </div>
</template>

<script>
    // import axios from '../../utils/httpRequest'
    import qs from 'qs'
    export default {
        name: 'transfer',
        data () {
            return {
                config: {
                    status: {
                        W: '等待轉換',
                        T: '成功',
                        U: '已撤銷'
                    }
                },
                formTransfer: {
                    ipSrcBaseNo: '',
                    srcIpNameNo: '',
                    destIpBaseNo: '',
                    destIpNameNo: '',
                    status: '',
                    startTime: '',
                    endTime: ''
                },
                data: [],
                loading: false,
                tableData: [],
                total: 1,
                currentPage:1,
                emptyText: '數據加載中',
            }
        },
        mounted(){
            this.searchFn(1);
        },
        methods: {
            clearSearch(){
                this.formTransfer = {
                    ipSrcBaseNo: '',
                    srcIpNameNo: '',
                    destIpBaseNo: '',
                    destIpNameNo: '',
                    status: '',
                    startTime: '',
                    endTime: ''
                }
                this.searchFn();
            },
            searchFn(num){
                this.loading = true;
                let page_num = num ? num : 1;
                this.formTransfer.page_num = page_num;
                this.formTransfer.page_size = 10;
                this.formTransfer.endTime = this.formTransfer.endTime ? (this.formTransfer.endTime.replace('00:00:00', '23:59:59')) : '';
                this.emptyText = '數據加載中';
                this.$http.get('/ip/transfer',    {params: this.formTransfer}).then(res => {
                    if(res.status == 200){
                        this.tableData = res.data.list;
                        if(! this.tableData || this.tableData.length == 0){
                            this.emptyText = '暫無數據';
                        }
                        this.total = res.data.total;
                        this.currentPage = num ? num : 1
                        this.loading = false;
                    }
                })
            },
            Status(status){
                return this.config.status[status];
            },
            undo(value, index){
                this.$msgbox.confirm('確定撤銷transfer?', '提示', {
                    confirmButtonText: '確定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.undoFn(value, index);
                })
            },
            undoFn(value, index){
                this.$http.post('/ip/transfer/cancel?ipBaseNo='+value.ipSrcBaseNo,{ipBaseNo: value.ipSrcBaseNo}).then((res)=>{
                    if(res.status == 200){
                        this.$toast({tips: '撤銷成功'});
                        this.tableData[index].status = 'U';
                    }
                })
            }
        }
    }
</script>

<style scoped>

</style>
