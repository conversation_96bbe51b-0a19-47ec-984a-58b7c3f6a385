<template>
<!-- 可支付明細 -->
    <div>
        <el-form :inline="true" :model="searchForm" class="demo-form-inline" @keyup.enter.native="getList">
            <el-form-item label="Society Code">
                <el-input type="text" v-model="searchForm.societyCode"></el-input>
            </el-form-item>
            <el-form-item label="Dist no">
                <el-input type="text" v-model="searchForm.distNo"></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="getList">搜 索</el-button>
            </el-form-item>
            <el-form-item>
                <span class="clear-search" @click="clearSearch()">清除搜索</span>
            </el-form-item>
        </el-form>
        <el-table
            :data="tableData"
            border
            style="width: 100%"
            :empty-text="emptyText"
        >
            <el-table-column
                prop="id"
                label="ID">
            </el-table-column>
            <el-table-column
                prop="societyCode"
                label="Soc Code">
            </el-table-column>
            <el-table-column
                prop="societyName"
                label="Name">
            </el-table-column>
            <el-table-column
                prop="distNo"
                label="Dist No">
            </el-table-column>
            <el-table-column
                prop="pay"
                label="Is Pay">
            </el-table-column>
            <el-table-column
                prop="payAmount"
                label="Net Payment">
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :total=total @current-change="handleCurrentChange">
        </el-pagination>
    </div>
</template>

<script>
    export default {
        name: 'paymentlist',
        data () {
            return {
                searchForm: {page_num: 1},
                tableData: [],
                total: 1,
                emptyText: '數據加載中',
            }
        },
        methods: {
            clearSearch(){
                this.searchForm = {}
                this.getList()
            },
            getList () {
                this.emptyText = '數據加載中';
                this.$http
                    .get('/dist/autopay/listDistAutopayNetPaySociety', {
                        params: this.searchForm
                    })
                    .then(res => {
                        if (res.success) {
                            this.tableData = res.data.data.list
                            this.total = res.data.data.total
                            if(! this.tableData || this.tableData.length == 0){
                                this.emptyText = '暫無數據';
                            }
                        }
                    })
            },
            handleCurrentChange (page) {
                this.searchForm.page_num = page
                this.getList()
            }
        },
        mounted () {
            let socCode = this.$route.query.societyCode
            this.searchForm.societyCode = socCode
            this.getList()
        }
    }
</script>

<style scoped>

</style>
