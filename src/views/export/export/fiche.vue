<template>
    <div>
        <el-form :inline="true" class="demo-form-inline">
            <el-form-item>
                <el-button type="primary" @click="getWork()">新增</el-button>
            </el-form-item>
            <el-form-item>
                <el-button type="success" @click="confirmEditFn()">生成文件</el-button>
            </el-form-item>
            <el-form-item>
                <el-button type="danger" @click="confirmDelFn()">批量刪除</el-button>
            </el-form-item>
        </el-form>
        <el-table
            :data="tableData"
            border
            stripe
            max-height="521px"
            style="width: 100%"
            @selection-change="handleSelectionChange">
            <el-table-column
                type="selection"
                width="55">
            </el-table-column>
            <el-table-column
                prop=""
                label="Work Title"
                >
                <template slot-scope="scope">
                  <span :title="scope.row.title">{{scope.row.title || scope.row.title_en}}</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="work_id"
                label="Work No"
                width="200px">
            </el-table-column>
            <el-table-column
                prop="work_society_code"
                label="Soc"
                width="160px">
            </el-table-column>
            <el-table-column
                prop="work_type"
                label="WorkType"
                width="160px">
            </el-table-column>
            <el-table-column
                label="operation"
                width="180px">
                <template slot-scope="scope">
                    <el-button type="text" size="small" @click="deleteFn(scope.row, scope.$index)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-form :inline="true" class="demo-form-inline" style="margin-top:30px">
            <el-form-item label="Dist Year">
                <el-input ref="year" @blur="changeYear(year)" v-model="year" placeholder=""></el-input>
            </el-form-item>
            <el-form-item label="Territory">
                <el-input v-model="countryCode" placeholder="雙擊" @dblclick.native="getTerritory" style="width:65px" readonly></el-input>
                <el-input v-model="countryName" placeholder="雙擊查詢" @dblclick.native="getTerritory" style="width:120px" readonly></el-input>
            </el-form-item>
            <el-form-item label="Right">
                <el-select v-model="rightType" placeholder="" style="width: 246px;">
                    <el-option label="ALL" value=""></el-option>
                    <el-option label="PERFORMING RIGHT" value="PER"></el-option>
                    <el-option label="MECHANICAL RIGHT" value="MEC"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="Language">
                <el-select v-model="language" placeholder="" style="width: 246px;">
                    <el-option label="ALL" value=""></el-option>
                    <el-option label="Chinese" value="ZHO"></el-option>
                    <el-option label="English" value="ENG"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="AT information">
                <el-checkbox v-model="atFlag"></el-checkbox>
            </el-form-item>
        </el-form>

        <select-work v-if="workTableVisible" ref="selectWorkCom" :search="workSearch" :workType='workType' @checkWork="checkWork"></select-work>
        <select-territory v-if="terrotoryTableVisible" ref="selectTerritoryCom" :search="{}" @checkTerritory="checkTerritory"></select-territory>
    </div>
</template>

<script>
    import selectWork from '../components/fichSelectWork';
    import selectTerritory from '../components/selectTerritory';
    export default {
        name: 'fiche',
        data () {
            return {
                tableData: [],
                workTableVisible: false,
                rootPath: '',
                showPath: false,
                year:'',
                rightType:'',
                workType:'ORG',
                deleteData:[],
                terrotoryTableVisible:false,
                countryCode:'TW',
                countryName:'TAIWAN',
                searchData:{},
                atFlag: false,
                language:''
            }
        },
        mounted () {
        },
        methods: {
            checkTerritory(data){
                this.countryCode = data.tisA
                this.countryName = data.description
            },
            getTerritory(){
                this.searchData = {
                    tisA:this.countryCode,
                }
                this.terrotoryTableVisible = true;
                this.$nextTick( () => {
                    this.$refs.selectTerritoryCom.init();
                })
            },
            handleSelectionChange(data){
                this.deleteData=data
            },
            confirmDelFn(){
                if(this.deleteData.length){
                    this.$confirm(`確定進行[删除]操作?`, '提示', {
                        confirmButtonText: '確定',
                        cancelButtonText: '取消',
                        closeOnClickModal:false,
                        type: 'warning'
                    }).then(() => {
                        this.deleteData.forEach(item=>{
                            this.tableData.forEach((item1,index)=>{
                                if(item.id == item1.id){
                                    this.tableData.splice(index, 1);
                                }
                            })
                        })
                        this.$toast({tips: '刪除成功'})
                    }).catch( () => {

                    })
                }else{
                    this.$toast({tips: '請至少選擇一條作品信息'})
                    return;
                }
                
            },
            changeYear(data){
                if(data){
                    if((1000>data || data>9999)){
                        this.$toast({tips: '请输入正确的年份'})
                        this.$refs.year.focus()
                        return
                    }
                    if(!(/(^[1-9]\d*$)/.test(data))){
                        this.$toast({tips:'年份为正整数'})
                        this.$refs.year.focus()
                        return
                    }
                }
                
            },
            /**
             * 选取作品
             */
            getWork(index, row) {
                // this.editIndex = index;
                this.workTableVisible= true;
                this.workSearch = {
                    title: '',
                    soc: '',
                    workId: '',
                    workType:this.workType
                }
                this.$nextTick( () => {
                    this.$refs.selectWorkCom.init();
                })
            },
            tableDataPush(info){
                info.forEach((item,index)=>{
                    // let obj={}
                    let obj=item
                    let next = true
                    // obj.title = item.title ? item.title : item.title_en
                    // obj.workId = item.work_id
                    // obj.workSocietyCode = item.work_society_code
                    this.tableData.forEach(item1=>{
                        if(item1.id==obj.id){
                            next=false
                        }
                    })
                    if(next){
                        this.tableData.push(obj)
                    }
                })
            },
            checkWork(info,type){
                this.workType = type
                if(this.tableData.length && info.length){
                    let isAV = this.tableData.some(item=>{
                        return item.work_type=='AV'
                    })
                    let isadd =  info.some(item=>{
                        return item.work_type=='AV'
                    })
                    if((isAV && !isadd) || (!isAV && isadd)){
                        if(isAV){
                            this.workType = 'AV'
                        }
                        if(isadd){
                            this.workType = 'ORG'
                        }
                        this.$toast({tips: 'AV類和非AV類不可同時導出'})
                        return
                    }else{
                        this.tableDataPush(info)
                    }
                }else{
                    this.tableDataPush(info)
                }
                // let index = this.tableData.length;
                // this.tableData.push({
                //     title: '',
                //     workId: '',
                //     workSocietyCode: ''
                // }) 
                // this.$set(this.tableData[index], 'title', info.title ? info.title : info.title_en);
                // this.$set(this.tableData[index], 'workId', info.work_id);
                // this.$set(this.tableData[index], 'workSocietyCode', info.work_society_code);
            },
            confirmEditFn(){
                if(this.tableData.length == 0){
                    this.$toast({tips: '請至少選擇一條作品信息'})
                    return;
                }
                this.confirmPath()
            },
            confirmPath(){
                let ajaxData = {}
                let workData=[]
                this.tableData.forEach(item=>{
                    let obj={
                        // artistName:item.title,
                        // author:JSON.stringify(item.author),
                        // composer:JSON.stringify(item.composer),
                        // episodeNo:item.episode_no,
                        // genre:item.genre_code,
                        // ipBaseNo:JSON.stringify(item.ip_base_no),
                        // ipNameNo:JSON.stringify(item.ip_name_no),
                        isNotAv:item.work_type=='AV',
                        // iswc:item.iswc,
                        soc:item.work_society_code,
                        // title:item.title?item.title:item.title_en,
                        // titleType:item.title,
                        workId:item.work_id,
                        workType:item.work_type
                    }
                    workData.push(obj)
                })
                ajaxData = Object.assign(ajaxData, {workDtoList:workData,year:this.year,countryCode:this.countryCode,rightType:this.rightType,
                atFlag:this.atFlag,language:this.language});
                this.$http.post('/export/pdf/workList', ajaxData,{responseType:'blob'}).then( res => {
                    if(res.data.type!='application/pdf'){
                        this.$toast({tips: '導出失敗'})
                    }else{
                        this.$utils.downloadByBlobPDF(res.data,res.headers["content-disposition"] ? res.headers["content-disposition"].split(';')[1] : '=large_fiche歌卡導出')
                    }
                })

            },
            b64EncodeUnicode(str) {
                return btoa(encodeURIComponent(str).replace(/%([0-9A-F]{2})/g, function(match, p1) {
                    return String.fromCharCode('0x' + p1);
                }));
            },
            deleteFn (row, index) {
                this.$confirm(`確定進行[删除]操作?`, '提示', {
                    confirmButtonText: '確定',
                    cancelButtonText: '取消',
                    closeOnClickModal:false,
                    type: 'warning'
                }).then(() => {
                    this.tableData.splice(index, 1);
                    this.$toast({tips: '刪除成功'})
                }).catch( () => {

                })
            }
        },
        components: {
            selectWork,
            selectTerritory
        }
    }
</script>
<style lang="scss" scoped>
    @import "../../../assets/scss/works.scss";

/deep/ .el-dialog{
    width: 500px;
}
</style>

