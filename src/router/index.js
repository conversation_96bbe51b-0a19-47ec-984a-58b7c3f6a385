/**
 * 全站路由配置
 *
 * 建议:
 * 1. 代码中路由统一使用name属性跳转(不使用path属性)
 */
// import en from '@/i18n/lang/en'
import Vue from 'vue'
import Router from 'vue-router'
import axios from '../utils/httpRequest'
// import http from '@/utils/httpRequest'
import { isURL } from '@/utils/validate'
import store from '@/store'
// import { clearLoginInfo } from '@/utils'


// let routeNmae = en.routeNmae
// 开发环境不使用懒加载, 因为懒加载页面太多的话会造成webpack热更新太慢, 所以只有生产环境使用懒加载
const _import = require('./import-' + process.env.NODE_ENV)
// 全局路由(无需嵌套上左右整体布局)
const globalRoutes = [
    { path: '/404', component: _import('common/404'), name: '404', meta: { title: '404未找到' } },
    { path: '/login', component: _import('common/login'), name: 'login', meta: { title: '登录' } }
]

// 主入口路由(需嵌套上左右整体布局)
const mainRoutes = {
    path: '/',
    component: _import('main'),
    name: 'main',
    redirect: { name: 'home' },
    meta: { title: '主入口整体布局' },
    children: [
        // 通过meta对象设置路由展示方式
        // 1. isTab: 是否通过tab展示内容, true: 是, false: 否
        // 2. iframeUrl: 是否通过iframe嵌套展示内容, '以http[s]://开头': 是, '': 否
        // 提示: 如需要通过iframe嵌套展示内容, 但不通过tab打开, 请自行创建组件使用iframe处理!
        { path: '/home', component: _import('common/home'), name: 'home', meta: { title: '首页' } },
        { path: '/theme', component: _import('common/theme'), name: 'theme', meta: { title: '主题' } },
        { path: '/member-ipi', component: _import('member/ipi'), name: 'member-ipi', meta: { title: 'member-ipi', isTab: true } },
        { path: '/member-ipi-height', component: _import('member/ipi-height'), name: 'member-ipi-height', meta: { title: 'member-ipi-h', isTab: true } },
        { path: '/member-info', component: _import('member/info'), name: 'member-info', meta: { title: 'member-info', isTab: true } },
        { path: '/member-info2', component: _import('member/info2'), name: 'member-info2', meta: { title: 'member-info', isTab: true } },
        { path: '/member-transfer', component: _import('member/transfer'), name: 'member-transfer', meta: { title: 'member-transfer', isTab: true } },
        /* { path: '/member-info', component: _import('member/info'), name: 'member-info', meta: { title: 'member-info', isTab: true } }, */
        { path: '/member-top', component: _import('member/top'), name: 'member-top', meta: { title: 'member-top', isTab: true } },
        { path: '/member-dummyinfo', component: _import('member/dummyinfo'), name: 'member-dummyinfo', meta: { title: 'member-dummyinfo', isTab: true } },

        { path: '/actor', component: _import('works/actor/actor'), name: 'works-actor', meta: { title: 'works-actor', isTab: true } },
        { path: '/actor-add', component: _import('works/actor/addinfo'), name: 'addActor', meta: { title: 'add-works-actor', isTab: true } },
        { path: '/actor-edit', component: _import('works/actor/addinfo'), name: 'editActor', meta: { title: 'edit-works-actor', isTab: true } },
        { path: '/actor-transfer', component: _import('works/actor/transfer'), name: 'transferActor', meta: { title: 'transfer-works-actor', isTab: true } },
        // { path: '/works-info', component: _import('works/actor/addinfo'), name: 'works-info', meta: { title: 'works-info', isTab: true } },
        { path: '/works-list', component: _import('works/base'), name: 'works-list', meta: { title: 'works-list', isTab: true } },
        { path: '/rel-list', component: _import('works/relList'), name: 'rel-list', meta: { title: 'rel-list', isTab: true } },
        // 编辑
        { path: '/works-baseinfo', component: _import('works/edit/baseinfo'), name: 'works-baseinfo', meta: { title: 'works-baseinfo', isTab: true } },
        { path: '/works-baseinfo2', component: _import('works/edit2/baseinfo'), name: 'works-baseinfo2', meta: { title: 'works-baseinfo', isTab: true } },
        { path: '/works-baseinfoarr', component: _import('works/edit/baseinfoarr'), name: 'works-baseinfoarr', meta: { title: 'works-baseinfoarr', isTab: true } },
        { path: '/works-baseinfoarr2', component: _import('works/edit2/baseinfoarr'), name: 'works-baseinfoarr2', meta: { title: 'works-baseinfoarr', isTab: true } },
        { path: '/works-baseinfoadp', component: _import('works/edit/baseinfoadp'), name: 'works-baseinfoadp', meta: { title: 'works-baseinfoadp', isTab: true } },
        { path: '/works-baseinfoadp2', component: _import('works/edit2/baseinfoadp'), name: 'works-baseinfoadp2', meta: { title: 'works-baseinfoadp', isTab: true } },
        { path: '/works-baseinfoav', component: _import('works/edit/baseinfoav'), name: 'works-baseinfoav', meta: { title: 'works-baseinfoav', isTab: true } },
        { path: '/works-baseinfoav2', component: _import('works/edit2/baseinfoav'), name: 'works-baseinfoav2', meta: { title: 'works-baseinfoav', isTab: true } },
        { path: '/works-baseinfotv', component: _import('works/edit/baseinfotv'), name: 'works-baseinfotv', meta: { title: 'works-baseinfotv', isTab: true } },
        { path: '/works-baseinfotv2', component: _import('works/edit2/baseinfotv'), name: 'works-baseinfotv2', meta: { title: 'works-baseinfotv', isTab: true } },
        {
            path: '/worksBatchCreate',
            component: _import('works/batchCreate'),
            name: 'worksBatchCreate',
            meta: { title: 'works-batchCreate', isTab: true }
        },
        //    { path: '/works-baseinfo', component: _import('works/baseinfo'), name: 'works-baseinfo' + localStorage.getItem('workId'), meta: { title: 'works-baseinfo' + localStorage.getItem('workId'), isTab: true } },
        // { path: '/works-adaptedinfo', component: _import('works/adaptedinfo'), name: 'works-adaptedinfo', meta: { title: 'works-adaptedinfo', isTab: true } },
        // 添加
        { path: '/works-arrangedinfo', component: _import('works/edit2/baseinfoarr'), name: 'works-arrangedinfo', meta: { title: 'works-arrangedinfo', isTab: true } },
        { path: '/works-adaptedinfo', component: _import('works/edit2/baseinfoadp'), name: 'works-adaptedinfo', meta: { title: 'works-adaptedinfo', isTab: true } },
        { path: '/works-originalinfo', component: _import('works/edit2/baseinfo'), name: 'works-originalinfo', meta: { title: 'works-originalinfo', isTab: true } },
        { path: '/works-medleyinfo', component: _import('works/edit2/baseinfoav'), name: 'works-medleyinfo', meta: { title: 'works-medleyinfo', isTab: true } },
        { path: '/works-suiteinfo', component: _import('works/edit2/baseinfoav'), name: 'works-suiteinfo', meta: { title: 'works-suiteinfo', isTab: true } },

        { path: '/works-avinfo', component: _import('works/edit2/baseinfoav'), name: 'works-avinfo', meta: { title: 'works-avinfo', isTab: true } },
        { path: '/works-tvinfo', component: _import('works/edit2/baseinfotv'), name: 'works-tvinfo', meta: { title: 'works-tvinfo', isTab: true } },
        { path: '/works-transfer', component: _import('works/transfer'), name: 'works-transfer', meta: { title: 'works-transfer', isTab: true } },
        { path: '/works-refChinDict', component: _import('works/refChinDict'), name: 'works-refChinDict', meta: { title: 'works-refChinDict', isTab: true } },

        // 作品去重
        {
            path: '/removalListCWR',
            component: _import('duplicateRemoval/uploadList'),
            name: 'removalListCWR',
            meta: { title: 'removalListCWR', isTab: true }
        },
        {
            path: '/removalListAVR',
            component: _import('duplicateRemoval/uploadList'),
            name: 'removalListAVR',
            meta: { title: 'removalListAVR', isTab: true }
        },
        {
            path: '/removalAuditlist',
            component: _import('duplicateRemoval/auditList'),
            name: 'removalAuditlist',
            meta: { title: 'removalAuditlist', isTab: true }
        },
        {
            path: '/removalAudit',
            component: _import('duplicateRemoval/audit'),
            name: 'removalAudit',
            meta: { title: 'removalAudit', isTab: true }
        },
        {
            path: '/removalAuditAvr',
            component: _import('duplicateRemoval/audit-avr'),
            name: 'removalAuditAvr',
            meta: { title: 'removalAuditAvr', isTab: true }
        },

        // 合約
        { path: '/contract-contract', component: _import('contract/contract'), name: 'contractList', meta: { title: 'Search-Agreement', isTab: true } },
        { path: '/contract-add', component: _import('contract/info'), name: 'addContract', meta: { title: 'contract-baseinfo', isTab: true } },
        { path: '/contract-info', component: _import('contract/info'), name: 'contractInfo', meta: { title: 'contract-info', isTab: true } },
        { path: '/contract-info2', component: _import('contract/info2'), name: 'contractInfo2', meta: { title: 'contract-info', isTab: true } },
        // { path: '/distribute-listupload', component: _import('distribute/listupload'), name: 'distribute-listupload', meta: { title: 'distribute-listupload', isTab: true } },
        // { path: '/distribute-olistupload', component: _import('distribute/olistupload'), name: 'distribute-olistupload', meta: { title: 'distribute-olistupload', isTab: true } },
        // { path: '/distribute-listdetails', component: _import('distribute/listdetails'), name: 'distribute-listdetails', meta: { title: 'distribute-listdetails', isTab: true } },
        {
            path: '/distribute-createdistribution',
            component: _import('distribute/createdistribution'),
            name: 'distribute-createdistribution',
            meta: { title: '創建分配', isTab: true }
        },{
            path: '/distribute-info',
            component: _import('distribute/info'),
            name: 'distribute-info',
            meta: { title: 'distribute-info', isTab: true }
        },{
            path: '/distribute-list',
            component: _import('distribute/list'),
            name: 'distribute-list',
            meta: { title: '分配列表', isTab: true }
        },{
            path: '/distribute-check',
            component: _import('distribute/checklist'),
            name: 'distribute-check',
            meta: { title: 'distribute-check', isTab: true }
        },
        // { path: '/distribute-olist', component: _import('distribute/olist'), name: 'distribute-olist', meta: { title: 'distribute-olist', isTab: true } },
        // { path: '/distribute-omatch', component: _import('distribute/omatch'), name: 'distribute-omatch', meta: { title: 'distribute-omatch', isTab: true } },
        // { path: '/distribute-oinfo', component: _import('distribute/oinfo'), name: 'distribute-oinfo', meta: { title: 'distribute-oinfo', isTab: true } },
        // { path: '/distribute-opayments', component: _import('distribute/opayments'), name: 'distribute-opayments', meta: { title: 'distribute-opayments', isTab: true } },
        // { path: '/distribute-oreceiptrecord', component: _import('distribute/oreceiptrecord'), name: 'distribute-oreceiptrecord', meta: { title: 'distribute-oreceiptrecord', isTab: true } },
        // { path: '/distribute-odetail', component: _import('distribute/odetail'), name: 'distribute-odetail', meta: { title: 'distribute-odetail', isTab: true } },
        // { path: '/distribute-mlist', component: _import('distribute/mlist'), name: 'distribute-mlist', meta: { title: 'distribute-mlist', isTab: true } },
        // { path: '/distribute-minfo', component: _import('distribute/minfo'), name: 'distribute-minfo', meta: { title: 'distribute-minfo', isTab: true } },
        // { path: '/distribute-mmatch', component: _import('distribute/mmatch'), name: 'distribute-mmatch', meta: { title: 'distribute-mmatch', isTab: true } },
        // { path: '/distribute-mdetailinfo', component: _import('distribute/mdetailinfo'), name: 'distribute-mdetailinfo', meta: { title: 'distribute-mdetailinfo', isTab: true } },
        // { path: '/distribute-mresetlist', component: _import('distribute/mresetlist'), name: 'distribute-mresetlist', meta: { title: 'distribute-mresetlist', isTab: true } },
        // { path: '/distribute-ilistupload', component: _import('distribute/ilistupload'), name: 'distribute-ilistupload', meta: { title: 'distribute-ilistupload', isTab: true } },
        // { path: '/distribute-iinfo', component: _import('distribute/iinfo'), name: 'distribute-iinfo', meta: { title: 'distribute-iinfo', isTab: true } },
        { path: '/distribute-claim', component: _import('distribute/claim'), name: 'distribute-claim', meta: { title: 'distribute-claim', isTab: true } },
        { path: '/distribute-claimlist', component: _import('distribute/claimlist'), name: 'distribute-claimlist', meta: { title: 'distribute-claimlist', isTab: true } },
        // {
        //     path: '/claimAudit',
        //     component: _import('distribute/claimAudit/audit'),
        //     name: 'claimAudit',
        //     meta: { title: 'claimAudit', isTab: true }
        // },{
        //     path: '/claimAuditList',
        //     component: _import('distribute/claimAudit/auditList'),
        //     name: 'claimAuditList',
        //     meta: { title: 'claimAuditList', isTab: true }
        // },


        // { path: '/distribute-adjust', component: _import('distribute/adjust'), name: 'distribute-adjust', meta: { title: 'distribute-adjust', isTab: true } },
        { path: '/distribute-report', component: _import('distribute/reportlist'), name: 'distribute-report', meta: { title: 'claim-report', isTab: true } },
        { path: '/ClaimFilterWork', component: _import('distribute/ClaimFilterWorklist'), name: 'ClaimFilterWork', meta: { title: 'ClaimFilterSet', isTab: true } },
        { path: '/createreport', component: _import('distribute/createreport'), name: 'distribute-createreport', meta: { title: 'claim-createreport', isTab: true } },
        // 调整分配
        {
            path: '/distribute-sd',
            name: 'distribute-sd',
            component: _import('distribute/sd/list'),
            meta: { title: 'SD調整', isTab: true}
        },
        {
            path: '/distribute-sr',
            name: 'distribute-sr',
            component: _import('distribute/sr/list'),
            meta: { title: 'SR調整', isTab: true}
        },
        {
            path: '/distribute-sd-detail',
            name: 'distribute-sd-detail',
            component: _import('distribute/sd/ipshare'),
            meta: { title: 'ipshare-sd', isTab: true}
        },
        {
            path: '/distribute-sr-detail',
            name: 'distribute-sr-detail',
            component: _import('distribute/sr/ipshare'),
            meta: { title: 'ipshare-sr', isTab: true}
        },
        //new Media
        {
            path: '/ipSet',
            component: _import('newMedia/ipSet'),
            name: 'ipSet',
            meta: { title: 'ipSet', isTab: true }
        },{
            path: '/numberSet',
            component: _import('newMedia/numberSet'),
            name: 'numberSet',
            meta: { title: 'numberSet', isTab: true }
        },{
            path: '/mediaVersion',
            component: _import('newMedia/mediaVersion'),
            name: 'mediaVersion',
            meta: { title: 'mediaVersion', isTab: true }
        },
        // Mechanical Right
        //    产品管理
        {
            path: '/mechanicalList',
            component: _import('mechanical-right/product/list'),
            name: 'mechanicalList',
            meta: { title: 'producerList', isTab: true }
        },
        // {
        //     path: '/mechanicalEdit',
        //     component: _import('mechanical-right/product/edit'),
        //     name: 'mechanicalEdit',
        //     meta: { title: 'edit', isTab: true }
        // },
        //  产品类别
        {
            path: '/productCategoryList',
            component: _import('mechanical-right/category/list'),
            name: 'productCategoryList',
            meta: { title: 'productTypeList', isTab: true }
        },
        //   重制清单
        {
            path: '/resetList',
            component: _import('mechanical-right/reset/list'),
            name: 'resetList',
            meta: { title: 'mecSalesMaintenanceList', isTab: true }
        },{
            path: '/resetEdit',
            component: _import('mechanical-right/reset/edit'),
            name: 'resetEdit',
            meta: { title: 'resetEdit', isTab: true }
        },{
            path: '/resetEditList',
            component: _import('mechanical-right/reset/edit-list'),
            name: 'resetEditList',
            meta: { title: 'resetEditList', isTab: true }
        },{
            path: '/resetAdd',
            component: _import('mechanical-right/reset/edit'),
            name: 'resetAdd',
            meta: { title: 'resetAdd', isTab: true }
        },{
            path: '/orderEdit',
            component: _import('mechanical-right/reset/order'),
            name: 'orderEdit',
            meta: { title: 'orderEdit', isTab: true }
        },
        {
            path: '/orderAdd',
            component: _import('mechanical-right/reset/order'),
            name: 'orderAdd',
            meta: { title: 'orderAdd', isTab: true }
        },
        //   作品授权
        {
            path: '/salesList',
            component: _import('mechanical-right/sales/list'),
            name: 'salesList',
            meta: { title: 'salesList', isTab: true }
        },{
            path: '/salesEdit',
            component: _import('mechanical-right/sales/edit'),
            name: 'salesEdit',
            meta: { title: 'salesEdit', isTab: true }
        },
        {
            path: '/salesAdd',
            component: _import('mechanical-right/sales/edit'),
            name: 'salesAdd',
            meta: { title: 'salesAdd', isTab: true }
        },
        {
            path: '/label',

            component: _import('mechanical-right/label/list'),
            name: 'label',
            meta: { title: 'label', isTab: true }
        },




        { path: '/distribute-condition', component: _import('distribute/condition'), name: 'distribute-condition', meta: { title: 'distribute-condition', isTab: true } },
        { path: '/distribute-addcopy', component: _import('distribute/conditionlist/addcopy'), name: 'distribute-addcopy', meta: { title: 'addCopy', isTab: true } },
        { path: '/distribute-editcopy', component: _import('distribute/conditionlist/addcopy'), name: 'distribute-editcopy', meta: { title: 'editCopy', isTab: true } },
        { path: '/distribute-addwork', component: _import('distribute/conditionlist/addwork'), name: 'distribute-addwork', meta: { title: 'addWork', isTab: true } },
        { path: '/distribute-editwork', component: _import('distribute/conditionlist/addwork'), name: 'distribute-editwork', meta: { title: 'editWork', isTab: true } },
        { path: '/distribute-addip', component: _import('distribute/conditionlist/addip'), name: 'distribute-addip', meta: { title: 'addIp', isTab: true } },
        { path: '/distribute-editip', component: _import('distribute/conditionlist/addip'), name: 'distribute-editip', meta: { title: 'editIp', isTab: true } },
        { path: '/distribute-addsoc', component: _import('distribute/conditionlist/addsoc'), name: 'distribute-addsoc', meta: { title: 'addSocdistribute-list', isTab: true } },
        { path: '/distribute-editsoc', component: _import('distribute/conditionlist/addsoc'), name: 'distribute-editsoc', meta: { title: 'editSocdistribute-list', isTab: true } },

        { path: '/payment-paymentlist', component: _import('payment/paymentlist'), name: 'payment-paymentlist', meta: { title: 'payment-list', isTab: true } },
        { path: '/payment-edit', component: _import('payment/edit'), name: 'paymentEdit', meta: { title: 'payment-edit', isTab: true } },
        { path: '/payment-detail', component: _import('payment/payDetailInfo'), name: 'paymentDetailInfo', meta: { title: 'payment-detail', isTab: true } },
        { path: '/income-Member', component: _import('payment/incomeDetail'), name: 'incomeMember', meta: { title: 'income Member', isTab: true } },
        { path: '/income-Society', component: _import('payment/incomeDetail'), name: 'incomeSociety', meta: { title: 'income Society', isTab: true } },
        { path: '/income-detail', component: _import('payment/incomeDetail'), name: 'incomeDetail', meta: { title: 'income detail', isTab: true } },
        { path: '/payment-tobePay', component: _import('payment/tobePay'), name: 'tobePay', meta: { title: 'tobePay', isTab: true } },
        { path: '/payment-tobePaySoc', component: _import('payment/tobePaySoc'), name: 'tobePaySoc', meta: { title: 'tobePaySoc', isTab: true } },
        { path: '/payment-info', component: _import('payment/info'), name: 'payment-info', meta: { title: 'payment-info', isTab: true } },
        { path: '/payment-salesList', component: _import('payment/salesTax/salesList'), name: 'salesTaxList', meta: { title: 'salesTaxList', isTab: true } },
        { path: '/payment-invoice', component: _import('payment/salesTax/invoice'), name: 'invoice', meta: { title: 'invoice', isTab: true } },
        { path: '/payment-nameTransferList', component: _import('payment/nameTransferList'), name: 'payment-nameTransferList', meta: { title: 'name-transfer-list', isTab: true } },
        //royalties transaction statement 
        {
            path: '/royalties-member',
            component: _import('royalties/member'),
            name: 'royalties-member',
            meta: { title: 'royalties-member', isTab: true }
        },{
            path: '/royalties-society',
            component: _import('royalties/society'),
            name: 'royalties-society',
            meta: { title: 'royalties-society', isTab: true }
        },
        {
            path: '/royalties-add-member',
            component: _import('royalties/add-member'),
            name: 'royalties-add-member',
            meta: { title: 'royalties-add-member', isTab: true }
        },
        {
            path: '/royalties-add-society',
            component: _import('royalties/add-society'),
            name: 'royalties-add-society',
            meta: { title: 'royalties-add-society', isTab: true }
        },
        // 系统权限管理
        {
            path: '/role',
            component: _import('authorization/role/role'),
            name: 'role',
            meta: { title: 'role', isTab: true }
        },{
            path: '/user',
            component: _import('authorization/user/user'),
            name: 'user',
            meta: { title: 'user', isTab: true }
        },,{
            path: '/menu',
            component: _import('authorization/menu/menu'),
            name: 'menu',
            meta: { title: 'menu', isTab: true }
        },
        // list Manage
        {
            path: '/sampleDateList',
            name: 'sampleDateList',
            component: _import('list-manage/sample-date/list'),
            meta: { title: 'sampleDateList', isTab: true}
        },{
            path: '/sampleDateAdd',
            name: 'sampleDateAdd',
            component: _import('list-manage/sample-date/add'),
            meta: { title: 'sampleDateAdd', isTab: true}
        },{
            path: '/sampleDateEdit',
            name: 'sampleDateEdit',
            component: _import('list-manage/sample-date/edit'),
            meta: { title: 'sampleDateEdit', isTab: true}
        },
        // 文件上传及拆分
        {
            path: '/preClaim',
            name: 'uploadList',
            component: _import('list-manage/general-list/upload/uploadList'),
            meta: { title: '一般清單文件列表', isTab: true}
        },

        {   //新一般清单-文件上传列表
            path: '/uploadList',
            name: 'normalListUploadList',
            component: _import('list-manage/normal-list/upload/uploadList'),
            meta: { title: '文件上传列表', isTab: true}
        },
        {   //新一般清单-文件上传页面
            path: '/upload',
            name: 'normalListUpload',
            component: _import('list-manage/normal-list/upload/upload'),
            meta: { title: '文件上传', isTab: true}
        },
        {   //新一般清单-资料夹目录上传
            path: '/classificationProcess',
            name: 'classificationProcess',
            component: _import('list-manage/normal-list/upload/classification-process'),
            meta: { title: '資料夾上傳', isTab: true}
        },
        {   //新一般清单-资料夹合并加总
            path: '/categoryMerge',
            name: 'categoryMerge',
            component: _import('list-manage/normal-list/special-sample/index'),
            meta: { title: '資料夾合并加总', isTab: true}
        },
        {   //新一般清单-模板配置
            path: '/newTemplateConfig',
            name: 'newTemplateConfig',
            component: _import('list-manage/normal-list/template-config/template-config'),
            meta: { title: '清单模板配置', isTab: true}
        },
        {   //新一般清单-模板详情及字段设置
            path: '/newTemplateConfigDetail',
            name: 'newTemplateConfigDetail',
            component: _import('list-manage/normal-list/template-config/template-config-detail'),
            meta: { title: '模板详情配置', isTab: true}
        },
        {
            path: '/normalListFileList',
            name: 'normalListFileList',
            component: _import('list-manage/normal-list/upload/fileList'),
            meta: { title: '文件列表', isTab: true}
        },
        {
            path: '/normalListFileDetail',
            name: 'normalListFileDetail',
            component: _import('list-manage/normal-list/upload/fileDetail'),
            meta: { title: '文件明細', isTab: true}
        },

        {
            path:'/normalListSampleDate',
            name:'normalListSampleDate',
            component: _import('list-manage/normal-list/sample-date/list'),
            meta: { title: '抽樣管理', isTab: true}
        },{
            path: '/normalListSampleDateAdd',
            name: 'normalListSampleDateAdd',
            component: _import('list-manage/normal-list/sample-date/add'),
            meta: { title: 'sampleDateAdd', isTab: true}
        },{
            path: '/normalListSampleDateEdit',
            name: 'normalListSampleDateEdit',
            component: _import('list-manage/normal-list/sample-date/edit'),
            meta: { title: 'sampleDateEdit', isTab: true}
        },{
            path: '/normalListSampleDateDetail',
            name: 'normalListSampleDateDetail',
            component: _import('list-manage/normal-list/sample-date/detail'),
            meta: { title: 'sampleDateDetail', isTab: true}
        },

        {
            path: '/fileList',
            name: 'fileList',
            component: _import('list-manage/general-list/upload/fileList'),
            meta: { title: '文件列表', isTab: true}
        },
        {
            path: '/listUpload',
            name: 'listUpload',
            component: _import('list-manage/general-list/upload/upload'),
            meta: { title: '清單上傳', isTab: true}
        },{
            path: '/fileDetail',
            name: 'fileDetail',
            component: _import('list-manage/general-list/upload/fileDetail'),
            meta: { title: '文件明細', isTab: true}
        },
        // claim文件拆分列表
        {
            path: '/claimUploadList',
            name: 'claimUploadList',
            component: _import('list-manage/claim-list/upload/uploadList'),
            meta: { title: 'claim清單文件列表', isTab: true}
        },{
            path: '/claimFileList',
            name: 'claimFileList',
            component: _import('list-manage/claim-list/upload/fileList'),
            meta: { title: '文件列表', isTab: true}
        },{
            path: '/claimFileDetail',
            name: 'claimFileDetail',
            component: _import('list-manage/claim-list/upload/fileDetail'),
            meta: { title: '文件明細', isTab: true}
        },
        //claim清單處理及審核

        {
            path: '/ClaimHistory',
            name: 'ClaimHistory',
            component: _import('list-manage/claim-list/history/historyList'),
            meta: { title: 'ClaimHistory', isTab: true}
        },
        {
            path: '/ClaimHistoryLog',
            name: 'ClaimHistoryLog',
            component: _import('list-manage/claim-list/history/HistoryLog'),
            meta: { title: 'ClaimHistoryLog', isTab: true}
        },
        {
            path: '/claimListUpload',
            name: 'claimListUpload',
            component: _import('list-manage/claim-list/upload/upload'),
            meta: { title: 'claim清單上傳', isTab: true}
        },
        {
            path: '/claimAuditList',
            name: 'claimAuditList',
            component: _import('list-manage/claim-list/audit/auditList'),
            meta: { title: 'claim审核清單', isTab: true}
        },{
            path: '/claimListAudit',
            name: 'claimListAudit',
            component: _import('list-manage/claim-list/audit/audit'),
            meta: { title: 'claim清單审核', isTab: true}
        },
        // 清单match及审核
        {
            path: '/auditList',
            name: 'auditList',
            component: _import('list-manage/general-list/audit/auditList'),
            meta: { title: '审核清單', isTab: true}
        },
        // 新-清单match及审核
        {
            path: '/newAuditList',
            name: 'newAuditList',
            component: _import('list-manage/normal-list/audit/auditList'),
            meta: { title: '审核清單', isTab: true}
        },
        {
            path: '/singList',
            name: 'singList',
            component: _import('list-manage/general-list/sing/singList'),
            meta: { title: '單場次清單審核', isTab: true}
        },
        {
            path: '/singListaudit',
            name: 'singListaudit',
            component: _import('list-manage/general-list/sing/singListaudit'),
            // meta: { title: '單場次審核', isTab: true,keepAlive: true}
            meta: { title: '單場次審核', isTab: true}

        },
        {
            path: '/listAudit',
            name: 'listAudit',
            component: _import('list-manage/general-list/audit/audit'),
            meta: { title: '清單审核', isTab: true}
        },
        {
            path: '/newListAudit',
            name: 'newListAudit',
            component: _import('list-manage/normal-list/audit/audit'),
            meta: { title: '清單审核', isTab: true}
        },
        {
            path: '/generalHistory',
            name: 'generalHistory',
            component: _import('list-manage/general-list/history/historyList'),
            meta: { title: 'generalHistory', isTab: true}
        },
        // 新清单管理 History
        {
            path: '/newGeneralHistory',
            name: 'newGeneralHistory',
            component: _import('list-manage/normal-list/history/historyList'),
            meta: { title: 'generalHistory', isTab: true}
        }
        ,
        // 新清单管理 History Log
        {
            path: '/P2HistoryLog',
            name: 'P2HistoryLog',
            component: _import('list-manage/normal-list/history/HistoryLog'),
            meta: { title: 'P2HistoryLog', isTab: true}
        },
        // O清单列表
        {
            path: '/o-list',
            name: 'oList',
            component: _import('list-manage/o-list/list'),
            meta: { title: 'O水單列表', isTab: true}
        },{
            path: '/o-detaillist',
            name: 'oDetailList',
            component: _import('list-manage/o-list/detaillist'),
            meta: { title: 'O清單明細列表', isTab: true}
        },{
            path: '/oListStep1',
            name: 'oListStep1',
            component: _import('list-manage/o-list/add/config-list'),
            meta: { title: '配置收款水單', isTab: true}
        },{
            path: '/oListStep2',
            name: 'oListStep2',
            component: _import('list-manage/o-list/add/upload'),
            meta: { title: 'O清單上传', isTab: true}
        },{
            path: '/oListStep3',
            name: 'oListStep3',
            component: _import('list-manage/o-list/add/detail'),
            meta: { title: 'O水單编辑明細', isTab: true}
        },{
            path: '/collection',
            name: 'collection',
            component: _import('list-manage/o-list/collectionlist'),
            meta: { title: 'O清單列表', isTab: true}
        },{
            path: '/bill',
            name: 'billList',
            component: _import('list-manage/o-list/billlist'),
            meta: { title: 'O清單檔案', isTab: true,keepAlive: true}
        },
        //O清单处理
        {
            path: '/oAuditList',
            name: 'oAuditList',
            component: _import('list-manage/o-audit/auditList'),
            meta: { title: 'O清單作品審核', isTab: true}
        },
        {
            path: '/oAuditDetail',
            name: 'oAuditDetail',
            component: _import('list-manage/o-audit/audit'),
            meta: { title: 'O清單審核明細', isTab: true}
        },
        {
            path: '/oAuditSample',
            name: 'oAuditSample',
            component: _import('list-manage/o-audit/sample-detail'),
            meta: { title: '合併清單明細', isTab: true,}
        },
        {
            path: '/oAuditIp',
            name: 'oAuditIp',
            component: _import('list-manage/o-audit/ip'),
            meta: { title: 'O清單IP審核', isTab: true}
        },
        {
            path: '/oadddata',
            name: 'oAddData',
            component: _import('list-manage/o-list/add-data/index'),
            meta: { title: '手動添加數據', isTab: true}
        },
        {
            path: '/oaddaudit',
            name: 'oAddData',
            component: _import('list-manage/o-list/add-data/audit'),
            meta: { title: '手動單審核', isTab: true}
        },
        // 清单编号生成
        {
            path: '/merge',
            name: 'merge',
            component: _import('list-manage/special-sample/index'),
            meta: { title: '資料夾合併加總', isTab: true}
        },
        // 清單合併加總
        {
            path: '/listnumber',
            name: 'listNumber',
            component: _import('list-manage/list-number/index'),
            meta: { title: '清單編號生成', isTab: true}
        },
        // 協會管理
        {
            path: '/societyList',
            name: 'societyList',
            component: _import('society/list'),
            meta: { title: '協會列表', isTab: true}
        },{
            path: '/societyEdit',
            name: 'societyEdit',
            component: _import('society/edit'),
            meta: { title: '編輯協會', isTab: true}
        },{
            path: '/societyAdd',
            name: 'societyAdd',
            component: _import('society/edit'),
            meta: { title: '新增協會', isTab: true}
        },
        {
            path: '/bankList',
            name: 'bankList',
            component: _import('maintenance/bank/list'),
            meta: { title: '銀行列表', isTab: true}
        },
        {
            path: '/branchList',
            name: 'branchList',
            component: _import('maintenance/branch/list'),
            meta: { title: '分行列表', isTab: true}
        },
        {
            path: '/iswc',
            name: 'iswc',
            component: _import('maintenance/iswc/index'),
            meta: { title: 'iswc', isTab: true}
        },
        // dictionaries
        {
            path: '/dictionaries',
            name: 'dictionaries',
            component: _import('maintenance/dictionaries/list'),
            meta: { title: 'dictionaries', isTab: true}
        },
        /**税率 */

        {
            path: '/rateSoc',
            name: 'rateSoc',
            component: _import('maintenance/rate/rate-soc'),
            meta: { title: '特殊稅率配置', isTab: true}
        },
        {
            path: '/rateIpi',
            name: 'rateIpi',
            component: _import('maintenance/rate/rate-ipi'),
            meta: { title: '特殊汇率配置（会员）', isTab: true}
        },
        //系统参数配置
        {
            path: '/parameter-category',
            name: 'parameterCategory',
            component: _import('parameter-config/list-category'),
            meta: { title: 'list-category', isTab: true}
        },
        {
            path: '/parameter-source',
            name: 'parameterSource',
            component: _import('parameter-config/list-source'),
            meta: { title: 'list-source', isTab: true}
        },
        //定時器任務
        {
            path: '/settimeout',
            name: 'setTimeOut',
            component: _import('modules/job/schedule'),
            meta: { title: '定時任務', isTab: true}
        },
        {
            path: '/sampleList',
            name: 'sampleList',
            component: _import('fingerprint/sampleList'),
            meta: { title: 'sampleList', isTab: true}
        },
        {
            path: '/comparison',
            name: 'comparison',
            component: _import('fingerprint/comparison'),
            meta: { title: 'DNA Search', isTab: true}
        },
        //清单模板配置
        {
            path: '/templateConfig',
            name: 'templateConfig',
            component: _import('list-manage/template-config/template-config'),
            meta: { title: '清單分類模板配置', isTab: true}
        },
        {
            path: '/templateConfigDetail',
            name: 'templateConfigDetail',
            component: _import('list-manage/template-config/template-config-detail'),
            meta: { title: '配置條件', isTab: true}
        },
        {
            path: '/mapConfig',
            name: 'mapConfig',
            component: _import('list-manage/template-config/map-config'),
            meta: { title: '清單解析映射關係配置', isTab: true}
        },
        {
            path: '/classification',
            name: 'classification',
            component: _import('list-manage/template-config/classification-process'),
            meta: { title: '清單分類處理', isTab: true}
        },

        {
            path: '/convertPinyin',
            name: 'convertPinyin',
            component: _import('utils/convert-pinyin'),
            meta: { title: 'Chinese Words Convertor', isTab: true}
        },

        {
            path: '/exportDiy',
            name: 'exportDiy',
            component: _import('export/export-diy/list'),
            meta: { title: 'Export Diy', isTab: true}
        },
        {
            path: '/editSqlDiy',
            name: 'editSqlDiy',
            component: _import('export/export-diy/sqlEdit'),
            meta: { title: 'Edit SQL Template', isTab: true}
        },
        {
            path: '/addDiy',
            name: 'addDiy',
            component: _import('export/export-diy/edit'),
            meta: { title: 'Add Template', isTab: true}
        },
        {
            path: '/addSqlDiy',
            name: 'addSqlDiy',
            component: _import('export/export-diy/sqlEdit'),
            meta: { title: 'Add SQL Template', isTab: true}
        },
        {
            path: '/editDiy',
            name: 'editDiy',
            component: _import('export/export-diy/edit'),
            meta: { title: 'Edit Template', isTab: true}
        },
        {
            path: '/exportReport',
            name: 'exportReport',
            component: _import('export/export/list'),
            meta: { title: 'Export List', isTab: true}
        },
        {
            path: '/exportDetail',
            name: 'exportDetail',
            component: _import('export/export/detail'),
            meta: { title: 'Export', isTab: true}
        },
        {
            path: '/allTasks',
            name: 'allTasks',
            component: _import('management/allTasks'),
            meta: { title: 'AllTasks', isTab: true}
        },
        {
            path: '/myTasks',
            name: 'myTasks',
            component: _import('management/myTasks'),
            meta: { title: 'MyTasks', isTab: true}
        },
    ]
}

const router = new Router({
    mode: 'hash',
    scrollBehavior: () => ({ y: 0 }),
    isAddDynamicMenuRoutes: false, // 是否已经添加动态(菜单)路由
    routes: globalRoutes.concat(mainRoutes)
})
let res = []
router.beforeEach((to, from, next) => {
    // 添加动态(菜单)路由
    // 1. 已经添加 or 全局路由, 直接访问
    // 2. 获取菜单列表, 添加并保存本地存储
    // if (from.path.includes('works-base')) {
    //     console.log(store.state.lang.id)
    // }
    // localStorage.removeItem('PER')
    // localStorage.removeItem('ZYN')
    // localStorage.removeItem('MEC')
    // localStorage.removeItem('NOD')
    // localStorage.removeItem('NDB')
    // console.log('router.beforeEach:', to);
    // console.log('store:', store);

    // 解决TAB重复打开详情页面时，页面不刷新的问题
    if (to.meta && to.meta.isTab) {
        console.log('to.meta.isTab:', to.meta.isTab,"方法执行-------------------");
        const mainTabs = [ ...store.state.common.mainTabs ];
        // console.log('-- mainTabs:', mainTabs);

        const findToNameInMainTabsIndex = mainTabs.findIndex(item => item.name === to.name);
        // console.log('findToNameInMainTabsIndex:', findToNameInMainTabsIndex)
        if (findToNameInMainTabsIndex !== -1) {
            // 特殊处理：normalListFileList页面强制刷新参数
            if (to.name === 'normalListFileList') {
                const findToNameInMainTabs = mainTabs[findToNameInMainTabsIndex];
                const newTabObject = {
                    ...findToNameInMainTabs,
                    params: {
                        key: Date.now(),
                        ...to.params
                    },
                    query: JSON.parse(JSON.stringify(to.query)) // 强制使用新的query参数
                }
                mainTabs[findToNameInMainTabsIndex] = newTabObject;
            } else {
                // tab 组件不支持对象操作，只能根据名字删除，所以该代码因组件不能良好支持tab生命周期被禁用
                if (to.query && to.query.title) {
                    // should do nothing
                } else {
                    const findToNameInMainTabs = mainTabs[findToNameInMainTabsIndex];
                    const newTabObject = {
                        ...findToNameInMainTabs,
                        params: {
                            key: Date.now(),
                            ...to.params
                        },
                        query: JSON.parse(JSON.stringify(to.query))
                    }
                    // findToNameInMainTabs.params = to.params;
                    // findToNameInMainTabs.query = to.query;
                    mainTabs[findToNameInMainTabsIndex] = newTabObject;
                }
            }
            // console.log('next mainTabs:', mainTabs);
            store.commit('common/updateMainTabs', mainTabs);
        } else {
            to.params.key = Date.now();
        }
        next();
        return;
    }


    if (router.options.isAddDynamicMenuRoutes || fnCurrentRouteType(to, globalRoutes) === 'global') {
        next()
    } else {
        axios.get('/index').then(res => {
            console.log(res.data.menus)
            if (res.data && res.status === 200) {
                fnAddDynamicMenuRoutes(res.data.menus)
                router.options.isAddDynamicMenuRoutes = true
                sessionStorage.setItem('menuList', JSON.stringify(res.data.menuList || '[]'))
                sessionStorage.setItem('permissions', JSON.stringify(findList(res.data.menus) || '[]'))
                next({ ...to, replace: true })
            } else {
                sessionStorage.setItem('menuList', '[]')
                sessionStorage.setItem('permissions', '[]')
                next()
            }
        }).catch((e) => {
            console.log(`%c${e} 请求菜单列表和权限失败，跳转至登录页！！`, 'color:blue')
            router.push({ name: 'login' })
        })
    }
})

function findList(data){

    for(let i = 0;i<data.length;i++){
        if(data[i].perms){
            res.push(data[i].perms)
        }else{
            if(data[i].childs&&data[i].childs.length>0){
                findList(data[i].childs)
            }
        }
    }
    return res
}
/**
 * 判断当前路由類型, global: 全局路由, main: 主入口路由
 * @param {*} route 当前路由
 */
function fnCurrentRouteType (route, globalRoutes = []) {
    var temp = []
    for (var i = 0; i < globalRoutes.length; i++) {
        if (route.path === globalRoutes[i].path) {
            return 'global'
        } else if (globalRoutes[i].children && globalRoutes[i].children.length >= 1) {
            temp = temp.concat(globalRoutes[i].children)
        }
    }
    return temp.length >= 1 ? fnCurrentRouteType(route, temp) : 'main'
}

/**
 * 添加动态(菜单)路由
 * @param {*} menuList 菜单列表
 * @param {*} routes 递归创建的动态(菜单)路由
 */
function fnAddDynamicMenuRoutes (menuList = [], routes = []) {
    var temp = []
    for (var i = 0; i < menuList.length; i++) {
        if (menuList[i].list && menuList[i].list.length >= 1) {
            temp = temp.concat(menuList[i].list)
        } else if (menuList[i].url && /\S/.test(menuList[i].url)) {
            menuList[i].url = menuList[i].url.replace(/^\//, '')
            var route = {
                path: menuList[i].url.replace('/', '-'),
                component: null,
                name: menuList[i].url.replace('/', '-'),
                meta: {
                    menuId: menuList[i].menuId,
                    title: menuList[i].name,
                    isDynamic: true,
                    isTab: true,
                    iframeUrl: ''
                }
            }
            // url以http[s]://开头, 通过iframe展示
            if (isURL(menuList[i].url)) {
                route['path'] = `i-${menuList[i].menuId}`
                route['name'] = `i-${menuList[i].menuId}`
                route['meta']['iframeUrl'] = menuList[i].url
            } else {
                try {
                    route['component'] = _import(`modules/${menuList[i].url}`) || null
                } catch (e) {}
            }
            routes.push(route)
        }
    }
    if (temp.length >= 1) {
        fnAddDynamicMenuRoutes(temp, routes)
        // console.log(mainRoutes)
    } else {
        // console.log(mainRoutes)
        mainRoutes.name = 'main-dynamic'
        mainRoutes.children = routes
        router.addRoutes([
            mainRoutes,
            { path: '*', redirect: { name: '404' } }
        ])
        sessionStorage.setItem('dynamicMenuRoutes', JSON.stringify(mainRoutes.children || '[]'))
        // console.log('\n')
        // console.log('%c!<-------------------- 动态(菜单)路由 s -------------------->', 'color:blue')
        // console.log(mainRoutes.children)
        // console.log('%c!<-------------------- 动态(菜单)路由 e -------------------->', 'color:blue')
    }
}

Vue.use(Router)

export default router
