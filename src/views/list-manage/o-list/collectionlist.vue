<template>
  <div>
    <el-form :inline="true" :model="formData" ref="form" class="demo-form-inline"  @keyup.enter.native='searchFn()'>
      <el-form-item>
        <el-input v-model="formData.source_society_code" placeholder="協會代碼" style="width: 100px;"></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="formData.dist_no" placeholder="分配代號" style="width: 100px;"></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="formData.source_dist_no" placeholder="國外分配代號" style="width: 120px;"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="searchFn()">搜索</el-button>
        <el-button type="primary" @click="addDetail">新增</el-button>
      </el-form-item>
    </el-form>
    <el-table :empty-text="tableresult" stripe :data="tableData" border style="width: 100%" v-loading="loading">
      <el-table-column fixed prop="id" label="清單序號">
      </el-table-column>
      <el-table-column prop="receiptId" label="税單序號">
      </el-table-column>
      <el-table-column prop="sourceSocietyCode" label="協會代碼">
      </el-table-column>
      <el-table-column prop="distNo" label="分配代號">
      </el-table-column>
      <el-table-column prop="name_type" label="分配序號">
      </el-table-column>
      <el-table-column prop="sourceDistNo" label="國外分配代號">
      </el-table-column>
      <el-table-column prop="sourceDistYear" label="國外分配年度">
      </el-table-column>
      <el-table-column prop="messageDate" label="寄送日期">
      </el-table-column>
      <el-table-column prop="distCurrencyCode" label="分配幣別">
      </el-table-column>
      <el-table-column prop="name_type" label="分配金額">
      </el-table-column>
      <el-table-column prop="summaryCommissionAmount" label="管理費">
      </el-table-column>
      <el-table-column prop="exchangeRate" label="税額">
      </el-table-column>
      <el-table-column prop="receiptChargeAmount" label="手續費">
      </el-table-column>
      <el-table-column prop="receiptAmount" label="匯款金額">
      </el-table-column>

      <el-table-column fixed="right" label="operation" width="200">
        <template slot-scope="scope">
          <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
            <el-button @click="() => {$router.push({name: 'oListStep3',params:{row:scope.row}})}" type="text" size="small">編輯</el-button>
            <el-button @click="() => {$router.push({name: 'oListStep2',params:{receiptDetailsId:scope.row.id}})}" type="text" size="small">上传文件</el-button>
            <el-button @click="() => {$router.push({name: 'bill',params:{receiptDetailsId:scope.row.id}})}" type="text" size="small">文件列表</el-button>
            <el-button @click="" type="text" size="small">程序校验</el-button>
          </span>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination background layout="prev, pager, next" :total="total" @current-change="handleCurrentChange">
    </el-pagination>
  </div>
</template>

<script>
export default {
  name: 'collectionlist',
  data() {
    return {
      formData: {
        dist_no: '',
        source_dist_no: '',
        source_society_code: '',
        page: {
          pageNum: 1,
          pageSize: 10
        }
      },
      tableData: [], tableresult: ' ',
      total: 1,
      loading: false
    }
  },
  mounted() {
    let formData = this.$route.params.formData
    this.formData = {
      dist_no: formData.distNo,
      source_dist_no: '',
      source_society_code: formData.sourceSoc,
      page: {
        pageNum: 1,
        pageSize: 10
      }
    }
    this.searchFn()
  },
  methods: {
    searchFn() {
      this.tableresult = '數據加載中...'
      this.$http.post('/listOverseasReceiptDetails/getListOverseasReceiptDetailsList', this.formData).then(res => {
        console.log(res)
        if (res.success) {
          this.tableData = res.data.list
          this.total = res.data.total
        }
        this.tableresult = this.tableData.length == 0 ? '暫無數據' : ' '
      })
    },
    addDetail() {
      //新增和编辑
      this.$router.push({ name: 'oListStep3', params: { row: this.$route.params.formData } })

    },
    handleCurrentChange(val) {
      this.formData.page.pageNum = val
      this.searchFn()
    },
    handleClick(row) {
      this.$router.push({ name: 'bill-list' })
    },

  }
}
</script>

<style scoped>
</style>
