<template>
  <div class="mod-user">
    <el-form :inline="true" :model="dataForm" @submit.native.prevent>
      <el-form-item>
        <el-input v-model.trim="dataForm.account" placeholder="用戶名" @keyup.enter.native="getDataList()"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getDataList()" v-if="ButtonMenuList.find">搜索</el-button>
        <el-button type="primary" @click="addOrUpdateHandle()" v-if="ButtonMenuList.add">新增</el-button>
        <el-button type="danger" @click="deleteBatchHandle()" :disabled="dataListSelections.length <= 0"  v-if="ButtonMenuList.del">批量删除</el-button>
      </el-form-item>
    </el-form>
    <el-table :empty-text="emptyText" :data="dataList" border stripe v-loading="dataListLoading" @selection-change="selectionChangeHandle" style="width: 100%;">
      <el-table-column type="selection" header-align="center" align="center" width="50">
      </el-table-column>
      <el-table-column prop="id" header-align="center" align="center" width="80" label="ID">
      </el-table-column>
      <el-table-column prop="account" header-align="center" align="center" label="用戶名">
      </el-table-column>
      <el-table-column prop="name" header-align="center" align="center" label="姓名">
      </el-table-column>
      <el-table-column prop="nameEn" header-align="center" align="center" label="姓名En">
      </el-table-column>
      <el-table-column prop="email" header-align="center" align="center" label="郵箱">
      </el-table-column>
      <el-table-column prop="mobile" header-align="center" align="center" label="手機號">
      </el-table-column>
      <!-- <el-table-column
                prop="status"
                header-align="center"
                align="center"
                label="狀態">
                <template slot-scope="scope">
                    <el-tag v-if="scope.row.status === 0" size="small" type="danger">禁用</el-tag>
                    <el-tag v-else size="small">正常</el-tag>
                </template>
            </el-table-column> -->
      <el-table-column prop="createTime" header-align="center" align="center" width="180" label="創建時間">
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="150" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id, scope.$index)"  v-if="ButtonMenuList.change">修改</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id, scope.$index)" v-if="ButtonMenuList.del">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination background layout="prev, pager, next" :total=total @current-change="getDataList" :current-page="currentPage">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <!-- <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @update="getDataList()"></add-or-update> -->
    <add-or-update ref="addOrUpdate" @update="getDataList()"></add-or-update>
  </div>
</template>

<script>
import AddOrUpdate from './userEdit'
export default {
  data() {
    return {
      dataForm: {
        name: ''
      },
      emptyText:'暫無數據',
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      total: 0,
      currentPage:1,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      ButtonMenuList:{
        add:false,
        del:false,
        change:false,
        find:false,
      }
    }
  },
  components: {
    AddOrUpdate
  },
  activated() {
    this.getDataList()
    this.getButtonMenu()
  },
  methods: {
    //获取页面按钮权限
    getButtonMenu(){
      var that = this
      var pageName 
      this.$http.get('/sys/menus/nav', ).then(res => {
        if (res.status == 200) {
            pageName = that.findList(res.data.data.menuList,that.$route.query.id)
            if (pageName.childs&&pageName.childs.length>0) {
              pageName.childs.forEach(item=>{
                if (item.name=='修改') {
                  that.ButtonMenuList.change = true
                }
                if (item.name=='查看') {
                  that.ButtonMenuList.find = true
                }
                if (item.name=='删除') {
                  that.ButtonMenuList.del = true
                }
                if (item.name=='新增') {
                  that.ButtonMenuList.add = true
                }
              })
            }

        }
      })
    },
    findList(data,id){
      let res = null
      for(let i = 0;i<data.length;i++){
          if(data[i].id == id){
              res =  data[i]
              
              return data[i]
          }
          else if(data[i].childs&&data[i].childs.length>0){
              res = this.findList(data[i].childs,id)
          }
          if(res) break
      }
      return res
    },
    // 獲取數據列表
    getDataList(page) {
      this.dataListLoading = true;
      let ajaxData = {
        page_num: page ? page : 1,
        page_size: 10,
        account: this.dataForm.account
      }
      this.emptyText = '數據加載中';
      this.$http.get('/sys/users', { params: ajaxData }).then(res => {
        if (res.success) {
          this.dataList = res.data.data.list;
          this.total = res.data.data.total;
          this.currentPage = page ? page : 1
        } else {
          this.dataList = [];
          this.total = 0;
          this.currentPage = 1
        }
        if(! this.dataList || this.dataList.length == 0){
          this.emptyText = '暫無數據';
        }
        this.dataListLoading = false;
      })
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      // this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })

    },
    // 删除
    deleteHandle(id, index) {
      this.$msgbox.confirm('確定刪除?', '提示', {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteFn(id, index);
      }).catch(() => {
      });
    },
    deleteFn(id, index) {
      this.$http.delete('/sys/users/' + id).then(res => {
        if (res.success) {
          this.dataList.splice(index, 1);
          this.$toast({ tips: '刪除成功' })
        }
      })
    },
    deleteBatchHandle() {
      this.$msgbox.confirm('確定批量刪除?', '提示', {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteBatchFn();
      }).catch(() => {
      });
    },
    deleteBatchFn() {

      let ajaxData = [];
      this.dataListSelections.forEach(item => {
        ajaxData.push(item.id)
      })
      console.log(ajaxData);
      this.$http.post('/sys/users/batch', ajaxData).then(res => {
        this.$toast({ tips: '刪除成功' });
        this.getDataList();
      })

    }

  }
}
</script>
