
<template>
    <div class="work-transfer">
        <el-dialog :visible.sync="show" :width="'1140px'" center title="Distribution" :modal-append-to-body="false" :lock-scroll="false" :close-on-click-modal="false">
            <el-form :inline="true" :model="formInline" class="demo-form-inline search-list" style="text-align: center;" @keyup.enter.native="onSubmit(1)">
                <el-form-item label="Year">
                    <el-input type="number" v-model.trim="formInline.year" placeholder="Year" style="width: 80px;"></el-input>
                </el-form-item>
                <el-form-item label="Right">
                    <el-select v-model="formInline.rightType" placeholder="Type" style="width: 246px;">
                        <el-option label="PERFORMING RIGHT" value="PER"></el-option>
                        <el-option label="MECHANICAL RIGHT" value="MEC"></el-option>
                        <el-option label="SYNCHRONIZATION RIGHT" value="ZYN"></el-option>
                        <el-option label="OD RIGHT" value="NOD"></el-option>
                        <el-option label="DB RIGHT" value="NDB"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSubmit(1)">搜索</el-button>
                </el-form-item>
            </el-form>
            <el-table
                :data="tableData"
                border
                stripe
                show-summary
                :summary-method="getSummaries"
                :empty-text="emptyText"
                >
                <el-table-column
                    width="80px">
                    <template slot-scope="scope">
                        <i class="el-icon-star-on" style="color: red;" v-if="scope.row.agrNo"></i>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="groupIndicator"
                    label="Gp"
                    width="50px">
                </el-table-column>
                <el-table-column
                    prop="name"
                    label="Name"
                    min-width="120px">
                </el-table-column>
                <el-table-column
                    prop="chineseName"
                    label="Chinese Name"
                    min-width="120px">
                </el-table-column>
                <el-table-column
                    prop="ipNameNo"
                    label="Ip Name No">
                </el-table-column>
                <el-table-column
                    prop="workIpRole"
                    label="RoleCode"
                    width="100">
                </el-table-column>
                <el-table-column
                    prop="ipSocietyCode"
                    label="Soc"
                    width="100">
                </el-table-column>
                <el-table-column
                    prop="ipShare"
                    label="Ip Share"
                    width="100">
                    <template slot-scope="scope">
                        <el-input v-model=scope.row.ipShare placeholder="Ip Share" @dblclick.native="editContract(scope.row, scope.$index)" readonly></el-input>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="sdcheck"
                    label="Sd"
                    width="80px">
                    <template slot-scope="scope">
                        <el-checkbox v-model=scope.row.sdcheck disabled></el-checkbox>
                    </template>
                </el-table-column>
            </el-table>
        </el-dialog>
    </div>
</template>
<script>
import {Loading} from 'element-ui'
export default {
    data(){
        return {
            show: false,
            formInline: {
                year: '2020',
                rightType: 'PER'
            },
            tableData: [],
            emptyText: '數據加載中',
            total: 0,
            publisherTotal: 0
        }
    },
    methods: {
        init(){
            this.formInline.year = new Date().getFullYear();
            this.show = true;
            this.onSubmit();
        },
        onSubmit(){
             this.loadingB = Loading.service({
                lock: true,
                text: 'Loading',
                background: 'rgba(0,0,0,0.3)'
            })
            let ajaxData = {
                workId : this.$route.query.id,
                workSocietyCode : this.$route.query.socId,
                year: this.formInline.year,
                rightType: this.formInline.rightType
            }
            this.tableData = [];
            this.total = 0
            this.publisherTotal = 0
            this.emptyText = '數據加載中';
            this.$http.get('/wrk/getWrkWorkIpShareByYear', {params: ajaxData}).then( res => {
                if(res.success){
                    this.loadingB.close()
                    this.tableData = res.data.data;
                    if(! this.tableData || this.tableData.length == 0){
                        this.emptyText = '暫無數據';
                        return
                    }
                    this.tableData.forEach( (item) => {
                        if (item.sd === 'Y') {
                            item.sdcheck = true
                        }
                        if (item.sd === 'N') {
                            item.sdcheck = false
                        }
                        if(!item.ipNameNo){
                            item.name = item.dummyNameRoman;
                            item.chineseName = item.dummyName;
                        }

                        this.total += item.ipShare
                        if(['E','SE'].includes(item.workIpRole)){
                            this.publisherTotal += item.ipShare
                        }

                    })
                }
            })
        },
        getSummaries (param) {
            const { columns, data } = param
            const sums = []
            sums[2] = 'Writer Total: ' + (this.total - this.publisherTotal)
            sums[3] = 'Publisher Total: ' + this.publisherTotal
            sums[7]= 'Total: ' + this.total
            return sums
        },
        editContract(row, index){
            if(row.agrNo){
                // this.show = false;
                this.$router.push({ name: 'contractInfo', query: {contractNo: row.agrNo, nameId: row.agrNo, title: row.agrNo} })
            }
            
        }
    }
}
</script>
<style lang="scss" scoped>
    /deep/ .el-dialog{
        padding-bottom: 20px;
        margin-top: 15vh !important;
    }
    /deep/ .el-dialog__body{
        max-height: 500px;
        overflow-y: auto;
        overflow-x: hidden;
    }
    /deep/ .from{
        .el-input{
            width: 60%;
            margin-left: 8px;
        }
        label{
            display: inline-block;
            width: 84px;
            text-align: right;
            padding-right: 4px;
        }
        
        .select{
            padding: 0 !important;
            .cell{
                height: 100%;
                padding: 0 !important;
                .el-input{
                    margin-left: 0;
                    width: 100% !important;
                    input{
                        border: 0;
                    }
                }
            }
        }
    }
    /deep/ .to .el-input{
        width: 54%;
        margin-left: 2px;
        input{
            padding-left: 6px;
        }
    }
    /deep/ .cell{
        padding: 8px 8px !important;
        line-height: 18px;
    }
    /deep/ .el-dialog__body{
        padding: 14px 25px;
    }
    /deep/ .el-dialog__header{
        font-weight: bold;
        text-align: left;
        height: 50px;
    }

    /deep/ .el-table__header tr th{
        background: #eee !important;
    }

</style>