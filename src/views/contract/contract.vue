<template>
    <div class="contractbox">
        <el-card class="box-card">
            <el-form :inline="true" :model="formContract" label-width="120px" label-position="right" ref="form" class="demo-form-inline" @keyup.enter.native="onSubmit()">
                <el-form-item label="Writer/publisher" class="f14">
                    <el-input v-model="formContract.name" placeholder="雙擊查詢" @dblclick.native="getIp()" readonly></el-input>
                </el-form-item>
                <el-form-item label="Ip Name NO">
                    <el-input v-model="formContract.nameNo" placeholder="雙擊查詢" @dblclick.native="getIp()" readonly></el-input>
                </el-form-item>
                <el-form-item label="Agr NO">
                    <el-input v-model.trim="formContract.agrNo" placeholder=""></el-input>
                </el-form-item>
                <el-form-item label="Type">
                    <el-select v-model="formContract.agrType" placeholder="Type" style="width: 207px;">
                        <el-option label="默認選項" value=""></el-option>
                        <el-option label="Writer General" value="OG"></el-option>
                        <el-option label="Writer Specific" value="OS"></el-option>
                        <el-option label="Publisher General" value="PG"></el-option>
                        <el-option label="Publisher Specific" value="PS"></el-option>
                    </el-select>
                </el-form-item>
                <!-- <div> -->  
                    <el-form-item label="Work Title">
                        <el-input v-model="formContract.title" placeholder="雙擊查詢" @dblclick.native="getWork" readonly></el-input>
                    </el-form-item>
                    <el-form-item label="Work No">
                        <el-input v-model="formContract.worksnum" placeholder="雙擊查詢" @dblclick.native="getWork" readonly></el-input>
                    </el-form-item>
                    <el-form-item label="Soc">
                        <el-input v-model="formContract.inputSoc" placeholder="雙擊查詢" @dblclick.native="getWork" readonly></el-input>
                    </el-form-item>
                    <el-form-item label="Territory">
                        <el-input v-model="formContract.tisA" placeholder="可雙擊查詢" @dblclick.native="getTerritory"></el-input>
                    </el-form-item>
                    <el-form-item label="Type ID">
                        <el-input v-model.trim="formContract.agrTypeId" placeholder=""></el-input>
                    </el-form-item>
                    <el-form-item label="Year">
                        <el-input v-model.trim="formContract.date" placeholder=""></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="onSubmit" v-if="isAuth('contract:contract:find')">搜索</el-button>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="addContract" v-if="isAuth('contract:contract:add')">新增</el-button>
                    </el-form-item>
                    <el-form-item>
                        <span class="clear-search" @click="clearSearch()" v-if="isAuth('contract:contract:find')">清除搜索</span>
                    </el-form-item>
                <!-- </div> -->

            </el-form>
        </el-card>
                <!-- wrapperClosable 是否外面可以關閉!!!!!!!!!!!!!!!!! -->
        <div v-if="hackReset" class="treeCard">
            <el-tree
                :data="newTree"
                :props="contractList"
                :load="loadNode"
                node-key="id"
                lazy
                @node-click="handleNodeClick"
                :render-content="renderContent">
            </el-tree>
        </div>
        <!-- 预览 -->
        <el-drawer
        :visible.sync="drawer.show"
        :title="drawer.title"
        direction="rtl"
        size="50%"
        >
            <view-contact ref="viewContact" :contractNo="drawer.contractNo"></view-contact>
        </el-drawer>
        <select-ip v-if="IpTableVisible" ref="selectIpCom" :search="ipSearch" @checkIp="checkIp"></select-ip>
        <select-work v-if="workTableVisible" ref="selectWorkCom" :search="workSearch" @checkWork="checkWork"></select-work>
        <select-territory v-if="terrotoryTableVisible" ref="selectTerritoryCom" :search="{}" @checkTerritory="checkTerritory"></select-territory>

    </div>
</template>

<script>
    import axios from '../../utils/httpRequest'
    import qs from 'qs'
    import inputSelect from '../../components/input-select'
    import selectWork from '../../components/select-work';
    import viewContact from './component/viewContact'
    import selectIp from '../../components/select-ip';
    import selectTerritory from '../../components/select-territory';

    export default {
        name: 'contract',
        data () {
            return {
                loading: false,
                contractList: {
                    label: 'titleName',
                    children: 'zones',
                    isLeaf: 'leaf'
                },
                titleData: [],
                hackReset: true,
                workTableVisible: false,
                workGridData: [],
                modules: [],
                writerInput: '',
                // workInput: '',
                agrNo: '',
                formContract: {
                    name: '',
                    nameNo: '',
                    oipBaseNo: '',
                    agrNo: '',
                    agrType: '',
                    agrTypeId: '',
                    date: '',
                    inputSoc: '',
                    tisA: 'TW',
                    title: '',
                    worksnum: ''
                },
                agreementList: [],
                // showInputSelect: false,

                // 接口返回數據转化後的樹形數據
                tempData: [],
                firstArg: [],

                totalList: [],
                newTree: [],

                // 预览
                drawer: {
                    show: false,
                    contractNo: ''
                },
                // 查詢ip
                selectIpType: '', //哪里查找會員的  info, assignee, extend
                editIpIndex: 0,
                IpTableVisible: false,
                ipSearch: {},

                workSearch: {},
                workTableVisible: false,

                terrotoryTableVisible: false,
            }
        },
        components: {
            inputSelect,
            viewContact,
            selectIp,
            selectWork,
            selectTerritory
        },
        activated () {
            setTimeout( () => {
                if(this.$route.query.update){
                    let query = this.$route.query;
                    delete query.update;
                    this.onSubmit();
                }
            }, 500)
        },
        methods: {
            clearSearch(){
                this.formContract = {
                    name: '',
                    nameNo: '',
                    oipBaseNo: '',
                    agrNo: '',
                    agrType: '',
                    agrTypeId: '',
                    date: '',
                    inputSoc: '',
                    tisA: 'TW',
                    title: '',
                    worksnum: ''
                };
                this.newTree = []
            },
             /**
             * 
             * 选取IP
             */
            getIp(){
                this.IpTableVisible = true;
                this.ipSearch = {
                    name_no: this.formContract.nameNo,
                    name: this.formContract.name,
                    soc: this.formContract.inputSoc
                }
                this.$nextTick( () => {
                    this.$refs.selectIpCom.init();
                })
            },
            checkIp(info){   
                this.$set(this.formContract, 'nameNo', info.ip_name_no);         
                this.$set(this.formContract, 'name', info.name); 
            },
            /**
             * 选取作品
             */
            getWork(index, row) {
                // this.editIndex = index;
                this.workTableVisible= true;
                this.workSearch = {
                    workId: this.formContract.worksnum,
                    soc: this.formContract.inputSoc,
                    title: this.formContract.title
                }
                this.$nextTick( () => {
                    this.$refs.selectWorkCom.init();
                })
            },
            checkWork(info){
                this.$set(this.formContract, 'title', info.title ? info.title : info.title_en);
                this.$set(this.formContract, 'worksnum', info.work_id);
                this.$set(this.formContract, 'inputSoc', info.work_society_code);
            },
            getTerritory(){
                this.terrotoryTableVisible = true;
                this.$nextTick( () => {
                    this.$refs.selectTerritoryCom.init();
                })
            },
            checkTerritory(info){
                this.$set(this.formContract, 'tisA', info.tisA)
            },
            changeValue(obj){
                this[obj.name] = obj.value;
            },
            // 樹數據加載
            loadNode (node, resolve) {
                if (node.level === 0) {
                    return resolve(this.titleData)
                }
                setTimeout(() => {
                    resolve(this.agreementList)
                }, 500)
            },
            // 控制赋值避免重复
            handleNodeClick (val) {
                if (val.titleName === '合約樹') {
                    this.onSubmit()
                } else {
                    this.agreementList = [];
                    val.level = val.level || 0;
                    val.children && val.children.forEach(item => {
                        item.level = val.level + 1;
                        this.agreementList.push(item);
                    })
                }
            },
            renderContent: function (createElement, { node, data, store }) {
                var self = this
                data.level = data.level ? data.level : 0;
                return createElement('div', [
                    createElement('i', {attrs: {
                        class: node.expanded ? 'el-icon-caret-bottom' : 'el-icon-caret-right',
                        style: 'float: left;text-align: right;left: ' + ((data.level)*30-15) + 'px;position: absolute;margin-top: 13px;' + (data.children.length==0 ? 'display:none;' : '')
                    }}),
                    // 合約類型
                    createElement('div',{attrs:{
                        class: 'tree-class', style:"text-align: left;width: 17%;border-left: 0;padding-left: "+ (data.level)*30 + "px;"
                    }}, data.agrType),
                    createElement('div',{attrs:{
                        class: 'tree-class', style:"width: 11%;", title: data.agrNo
                    }}, [ createElement('i',{
                        attrs:{
                            class: 'el-icon-info',
                            // title: data.titleName
                        },
                        on:{
                            click: function(e){
                                self.quickView(e, data.agrNo, data.titleName)
                            }
                        }
                    }), data.agrNo]),
                    createElement('div',{attrs:{
                        class: 'tree-class', style:"width: 21%;",
                        title: data.oipChinName ? data.oipChinName : data.oipName
                    }}, data.oipChinName ? data.oipChinName : data.oipName),
                    createElement('div',{attrs:{
                        class: 'tree-class', style:"width: 20%;",
                        title: data.sipChinName ? data.sipChinName : data.sipName
                    }}, data.sipChinName ? data.sipChinName : data.sipName),
                    // 合約title
                    // createElement('div',{attrs:{
                    //     class: 'tree-class', style:"width: 52%;text-align: left;padding-left: " + (data.level-1)*30 + "px;",
                    //     title: data.titleName
                    // }}, [ createElement('i',{
                    //     attrs:{
                    //         class: 'el-icon-info',
                    //         // title: data.titleName
                    //     },
                    //     on:{
                    //         click: function(e){
                    //             self.quickView(e, data.contractNo, data.titleName)
                    //         }
                    //     }
                    // }), data.titleName]),
                    // 開始日期
                    createElement('div',{attrs:{
                        class: 'tree-class', style:"width: 11%;"
                    }}, data.agrSdate ? data.agrSdate.substring(0, 10) : ''),
                    // 結束日期
                    createElement('div',{attrs:{
                        class: 'tree-class', style:"width: 11%;"
                    }}, data.agrEdate ? data.agrEdate.substring(0, 10) : ''),
                    // 操作
                    createElement('div', {attrs: {
                        style: 'float: left;text-align: center;width: 9%;border:1px solid #eee; border-bottom: 0;border-right: 0;'
                    }}, [
                        createElement('el-button', {attrs: {
                            size: 'mini'
                        },
                        on: {
                            click: function () {
                                // localStorage.setItem('agrNo', data.agrNo)
                                self.$router.push({ name: 'contractInfo2', query: {contractNo: data.agrNo, nameId: data.agrNo, title: data.agrNo} })
                            }
                        }}, '編輯')
                    ])
                ])
            },
            onSubmit () {
                if(!this.formContract.nameNo && !this.formContract.agrNo && !this.formContract.worksnum){
                    this.$toast({tips: "請輸入查詢條件"})
                    return;
                }
                // if(!this.formContract.tisA){
                //     this.$toast({tips: "請輸入Territory"})
                //     return;
                // }
                this.agreementList = []
                this.titleData= [];
                this.hackReset = false
                this.$nextTick(() => {
                    this.hackReset = true
                })
                this.$utils.trim(this.formContract);
                let formData = qs.stringify(this.formContract)
                let config = {headers: { 'content-type': 'application/x-www-form-urlencoded' }}
                const loading = this.$loading()
                axios.post('/agreementTree/getAgreementTreeList', formData, config).then(res => {
                    loading.close();
                    if (res.status === 200) {
                        if(res.data.code == 200){
                            console.log('resresres====',res)
                            this.newTree = res.data.data || [];
                            this.newTree.forEach( item => {
                                item.level = 1;
                            })
                        }                        
                    }
                })
            },
            addContract () {
                this.$router.push({ name: 'addContract' })
            },
            // 预览合同
            quickView(e, contractNo, titleName){
                e.stopPropagation();
                this.drawer.show = true;
                this.drawer.contractNo = contractNo;
                this.drawer.title = titleName;
                // console.log('=======',contractNo,titleName)
                this.$nextTick( () => {
                    this.$refs['viewContact'].init(contractNo);
                })
                
            }
        },
        mounted () {
            var date = new Date()
            var year = date.getFullYear()
            var month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1
            var day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate()
            this.formContract.date = year;
        }
    }
</script>

<style>
    .agreementbox{
        margin-top: 20px;
        height: 40px;
        line-height: 40px;
        position: relative;
    }
    .contractbox{
        padding-left: 20px;
        width: 100%;
        /*height: 40px;*/
        /* border-bottom: 1px solid #f0f0f0; */
        line-height: 40px;
        position: relative;

    }
    .buttonright{
        position: absolute;
        right: 20px;
    }
    .el-card {
        margin-bottom: 20px;
    }
    .el-tree{
        border: 1px solid #eee;
        border-top: 0;
        /* border-bottom: 1px solid #eee; */
    }
    .el-tree>div>.el-tree-node__content:first-child>div>*{
        border-top: 0;
    }
    .el-tree .el-checkbox{
        display: none;
    }
    .el-tree-node__content{
        height: 40px;
        line-height: 40px;
    }
    .el-tree-node__expand-icon.expanded{
        display: none!important;
    }
    .el-tree-node__expand-icon{
        display: none!important;
    }
    .tree-class{
        float: left;
        text-align: center;
        border-top: 1px solid #eee !important;
        border-left: 1px solid #eee !important;
        overflow: hidden;
        text-overflow:ellipsis;
        white-space: nowrap;
    }
    .el-tree-node__content>div{
        width: 100%;
        position: relative;
    }

</style>
<style lang="scss" scoped>
    
    /deep/ .el-tree-node__content{
        padding-left: 0 !important;
    }
    /deep/ .treeCard button span{
        color: #333;
    }
    /deep/ .f14{
        label{
            font-size: 14px;
        }
    }
    /deep/ .el-icon-info{
        padding-top: 12px;
        margin: 0 4px 0 8px;
    }
    /deep/ .el-drawer__header{
        margin-bottom: 0;
        padding: 0px 20px;
    }
    /deep/ .el-drawer__body{
        overflow-y: auto;
    }

</style>
