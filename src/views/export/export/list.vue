<template>
  <div>
    <el-form :inline="true" class="demo-form-inline">
      <el-form-item>
        <el-button type="primary" @click="addFn()" v-if="isAuth('export:export:list:add')">新增</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="tableData" border stripe style="width: 100%" @row-dblclick="exportFn" v-loading="loading" :empty-text="emptyText">
      <el-table-column prop="id" label="ID" width="60px">
      </el-table-column>
      <el-table-column prop="reportName" label="報告名稱" min-width="180px">
      </el-table-column>
      <el-table-column prop="reportType" label="報告類別">
      </el-table-column>
      <el-table-column prop="webUrl" label="前端映射地址">
      </el-table-column>
      <el-table-column prop="createUserName" label="創建用戶">
      </el-table-column>
      <el-table-column label="operation">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="exportFn(scope.row, scope.$index)" v-if="isAuth('export:export:list:exportout')">導出</el-button>
          <el-button type="text" size="small" @click="editFn(scope.row, scope.$index)" v-if="isAuth('export:export:list:change')">编辑</el-button>
          <el-button type="text" size="small" @click="deleteFn(scope.row, scope.$index)" v-if="isAuth('export:export:list:del')">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination background layout="prev, pager, next" :total="total" @current-change="searchFn" :current-page="currentPage">
    </el-pagination>
    <!-- 編輯、添加 -->
    <el-dialog :title="(opType == 'add' ? '添加' : '編輯') + '導出'" :visible.sync="show" v-if="show" :width="'500px'" :close-on-click-modal="false">
      <el-form ref="editForm" :inline="true" :model="edit" :rules="rules" label-width="120px">
        <el-form-item label="報告名稱" prop="reportName">
          <el-input ref="editIp" type="text" v-model="edit.reportName">
          </el-input>
        </el-form-item>
        <el-form-item label="報告類別" prop="reportType">
          <!-- <el-input ref="editIp" type="text" v-model="edit.reportType">
                    </el-input> -->
          <el-select v-model="edit.reportType" style="width:207px;">
            <el-option value="MBR" label="MBR"></el-option>
            <el-option value="WRK" label="WRK"></el-option>
            <el-option value="AGR" label="AGR"></el-option>
            <el-option value="DIST" label="DIST"></el-option>
            <el-option value="LIST" label="LIST"></el-option>
            <el-option value="OTHER" label="OTHER"></el-option>
            <el-option value="ALL" label="ALL"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="前端映射地址" prop="webUrl">
          <el-input ref="editIp" type="text" v-model="edit.webUrl">
          </el-input>
        </el-form-item>
        <div class="t-c">
          <el-form-item>
            <el-button type="primary" @click="confirmEdit">確定</el-button>
          </el-form-item>
        </div>
      </el-form>
    </el-dialog>
    <category ref='category'></category>
    <address-export ref='address'></address-export>
    <remittance-export ref='remittance'></remittance-export>
    <payment-details ref='paymentDetails'></payment-details>
    <payment-summary ref='paymentSummary'></payment-summary>
    <overseas-receipts ref='overseasReceipts'></overseas-receipts>
    <receipt ref='receipt'></receipt>
    <sales-tax ref='salesTax'></sales-tax>
    <bank-txt ref='bankTxt'></bank-txt>
    <payment-statistics ref='paymentStatistics'></payment-statistics>
  </div>
</template>

<script>
import category from '../components/Category.vue';
import addressExport from '../components/Address';
import remittanceExport from '../components/remittance';
import paymentDetails from '../components/PaymentDetails';
import paymentSummary from '../components/PaymentSummary';
import overseasReceipts from '../components/OverseasReceipts';
import receipt from '../components/receipt';
import salesTax from '../components/salesTax';
import bankTxt from '../components/bankTxt';
import paymentStatistics from '../components/PaymentStatistics';

// 
export default {
  name: 'list',
  data() {
    return {
      tableData: [],
      total: 1,
      currentPage: 1,
      show: false,
      opType: 'add',
      edit: {
        reportName: '',
        reportType: '',
        webUrl: ''
      },
      rules: {
        reportName: [{ required: true, message: '請輸入reportName', trigger: 'blur' }],
        reportType: [{ required: true, message: '請輸入reportType', trigger: 'blur' }],
        webUrl: [{ required: true, message: '請輸入webUrl', trigger: 'blur' }],
      },
      loading: false,
      emptyText: '數據加載中',
    }
  },
  components: { 
    category,
    addressExport,
    remittanceExport,
    paymentDetails,
    paymentSummary,
    overseasReceipts,
    receipt,
    salesTax,
    bankTxt,
    paymentStatistics
  },
  mounted() {
    this.searchFn();
  },
  methods: {
    searchFn(page) {
      let ajaxData = {};
      ajaxData.pageNum = page ? page : 1;
      ajaxData.pageSize = 10;
      this.loading = true;
      this.emptyText = '數據加載中';
      this.$http.get('/sysReport', { params: ajaxData }).then(res => {
        this.loading = false;
        if (res.success) {
          this.tableData = res.data.data.list;
          if(! this.tableData || this.tableData.length == 0){
            this.emptyText = '暫無數據';
          }
          this.total = res.data.data.total;
          this.currentPage = ajaxData.pageNum;
          if(!this.tableData.length && this.currentPage!=1){
            this.searchFn(this.currentPage-1)
          }
        }
              this.tableresult = this.tableData.length == 0 ? '暫無數據' : ' '
      })
    },
    addFn() {
      this.edit = {
        reportName: '',
        reportType: '',
        webUrl: ''
      };
      this.opType = 'add';
      this.show = true;
    },
    editFn(row) {
      this.opType = 'edit';
      this.edit = this.$utils.copy(row);
      this.show = true;
    },
    confirmEdit() {
      this.$refs.editForm.validate((valid) => {
        if (valid) {
          this.confirmEditFn();
        }
      })
    },
    confirmEditFn() {
      let ajaxData = this.$utils.copy(this.edit);
      this.$http.post('/sysReport', ajaxData).then(res => {
        if (res.success) {
          if (res.data.code && res.data.code != 200) {
            this.$toast({ tips: res.data.message })
          } else {
            this.$toast({ tips: (this.opType == 'add' ? '添加' : '編輯') + '成功' });
            this.show = false;
            this.searchFn(1);
          }
        }
      })
    },
    deleteFn(row, index) {
      this.$msgbox.confirm(`確定進行[删除]操作?`, '提示', {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.delete('/sysReport/' + row.id).then(res => {
          if (res.success) {
            this.$toast({ tips: '删除成功' })
            // this.tableData.splice(index, 1);
            this.searchFn(this.currentPage)
          }
        })

      }).catch(() => {

      })
    },
    exportFn(row,secondeparam) {
      console.log('----------------', row,secondeparam);
      if (row.webUrl == 'parameterCategory') {
        this.$refs.category.exportList()
        return
      }
      if (row.webUrl == 'address') {
        this.$refs.address.exportList()
        return
      }
      if (row.webUrl == 'remittance') {
        this.$refs.remittance.exportList()
        return
      }
      if (row.webUrl == 'paymentDetails') {
        this.$refs.paymentDetails.exportList()
        return
      }
      if (row.webUrl == 'paymentSummary') {
        this.$refs.paymentSummary.exportList()
        return
      }
      if (row.webUrl == 'overseasReceipts') {
        this.$refs.overseasReceipts.exportList()
        return
      }
      if (row.webUrl == 'receipt') {
        this.$refs.receipt.exportList()
        return
      }
      if (row.webUrl == 'salesTax') {
        this.$refs.salesTax.exportList()
        return
      }

      if (row.webUrl == 'bankTxt') {
        this.$refs.bankTxt.exportList()
        return
      }

      if (row.webUrl == 'paymentStatistics') {
        this.$refs.paymentStatistics.exportList()
        return
      }
      
      this.$router.push({
        name: 'exportDetail',
        query: { type: row.webUrl, title: row.webUrl, nameId: row.webUrl ,id:row.id}
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import "../../../assets/scss/works.scss";

/deep/ .el-dialog {
  width: 500px;
}
</style>

