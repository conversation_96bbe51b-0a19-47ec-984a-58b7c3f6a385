<template>
    <div>
        <el-form :inline="true" :model="searchForm" ref="searchForm" :rules="rules" class="demo-form-inline" @keyup.enter.native="getList(1)">
            <el-form-item label="dist no" prop="distNo">
                <el-input v-model.trim="searchForm.distNo" placeholder="" style="width: 100px;"></el-input>
            </el-form-item>
            <el-form-item label="ip base no" prop="ipBaseNo">
                <!-- <el-input v-model.trim="searchForm.ipBaseNo" placeholder="" @blur="getNameByBaseNo" style="width: 140px;"></el-input> -->
                <el-input v-model.trim="searchForm.ipBaseNo" placeholder="雙擊查詢" @dblclick.native="getPaName()" @change="changePa" style="width: 140px;" readonly></el-input>
            </el-form-item>
            <el-form-item label="pa name no" prop="paNameNo">
                <el-input v-model.trim="searchForm.paNameNo" disabled style="width: 140px;"></el-input>
            </el-form-item>
            <el-form-item label="name" prop="paName"  >
                <el-input v-model.trim="searchForm.paName" disabled></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="getList(1)" v-if="isAuth('royalties:member:find')">搜索</el-button>
            </el-form-item>
            <el-form-item>
                <el-button type="success" @click="add" v-if="isAuth('royalties:member:add') && addFlag">新增</el-button>
            </el-form-item>
            <el-form-item>
                <span class="clear-search" @click="clearSearch()" v-if="isAuth('royalties:member:find')">清除搜索</span>
            </el-form-item>
        </el-form>
        <el-table
            :data="tableData"
            border
            stripe
            style="width:100%"
            :empty-text="emptyText">
            <el-table-column
                type="index"
                label="序號"
                 width="80px">
            </el-table-column>
            <el-table-column
                prop="distNo"
                label="dist no"
                 width="100px">
            </el-table-column>
            <el-table-column
                prop="tranType"
                label="type"
                width="80px">
            </el-table-column>
            <el-table-column
                prop="tranDescpt"
                label="description"
                width="330px">
                <template slot="header" slot-scope="scope">
                    <span title="description">description</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="tranAmt"
                label="amount">
            </el-table-column>
            <el-table-column
                prop="nonTaxableIncomeInd"
                label="Non-Roy/Non-Tax.Income">
                <template slot="header" slot-scope="scope">
                    <span title="deduction">Non-Roy/Non-Tax.Income</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="commissionInd"
                label="Apply Comm">
            </el-table-column>
            <el-table-column
                prop="withheldTaxInd"
                label="Apply Tax">
            </el-table-column>
            <el-table-column
                prop="addAfterDistInd"
                label="After Dist">
            </el-table-column>
            <el-table-column label="operation" width="200px">
              <template slot-scope="scope"> 
                <el-button @click="edit(scope.row)" type="text" size="small" v-if="isAuth('royalties:member:change') && scope.row.payStatus != 'P'">編輯</el-button>
                <el-button @click="del(scope.row)" type="text" size="small"  v-if="isAuth('royalties:member:del') && scope.row.payStatus != 'P'">刪除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :current-page="currentpage"
            :total="total" @current-change="handleCurrentChange">
        </el-pagination>
        <select-ip v-if="IpTableVisible" ref="selectIpCom" :search="ipSearch" @checkIp="checkIp"></select-ip>
        <el-dialog :title="dialogTitle" :visible.sync="dialogFormVisible" width="480px" @close="clearForm" :close-on-click-modal="false">
            <el-form :inline="true" :model="formData" :rules="rules2" ref="editForm" label-width="120px" label-position="right" >
                <el-form-item style="dialog-el-form" label="Type" prop="tranType">
                    <el-input v-model="formData.tranType" ref="typeInput" placeholder="請雙擊" :readonly="true" @dblclick.native="getTypeList()"></el-input>
                </el-form-item>
                <el-form-item label="Description">
                    <el-input v-model="formData.tranDescpt" placeholder="請輸入"></el-input>
                </el-form-item>
                <el-form-item label="Amount" prop="tranAmt">
                    <el-input v-model="formData.tranAmt" placeholder="請輸入" onkeyup="this.value=this.value.match(/\d+\.?\d{0,6}/);this.dispatchEvent(new Event('input'))"></el-input>
                </el-form-item>
                <el-form-item label="" class="is-required">
                    <el-checkbox-group v-model="checkList" @change="selectNonTax">
                        <el-checkbox style="margin-left:60px;" 
                        v-for="item in checkGroup" :disabled="item.disabled" :label="item.label" :key="item.label" >{{item.label}}</el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="cancelEdit">取 消</el-button>
                <el-button type="primary" @click="editConfirm">確 定</el-button>
            </span>
        </el-dialog>
        <el-dialog title="Transaction Type" :visible.sync="showTypeDialog" width="50%" :close-on-click-modal="false">
            <el-table :empty-text="emptyText"   :data="typeList" stripe style="width: 100%">
                <el-table-column prop="tranType" label="tranType">
                </el-table-column>
                <el-table-column prop="tranDescpt" label="description" min-width="350">
                </el-table-column>
                <el-table-column label="option" align="center">
                    <template slot-scope="scope">
                        <span
                            style="
                                width: 100%;
                                display: inline-block;
                                text-align: center;
                                cursor: pointer;
                            "
                            >
                                <i class="el-icon-check" @click="chooseType(scope.row)"></i>
                            </span>
                    </template>
                </el-table-column>
            </el-table>
        </el-dialog>
        <pa-name ref="paName" :paNameNo='paNameNo' @checkPaName='checkPaName' @flag='ipFlag=true' ></pa-name>
    </div>
</template>

<script>
    import AddDialog from './add-member'
    import paName from './paName';
    export default {
        name: 'member',
        components:{
          AddDialog,paName
        },
        data(){
            return {
                searchForm:{
                    page_num:1,
                    page_size:10,
                    distNo:'',
                    ipBaseNo:'',
                    paName:'',
                    paNameNo:''
                },
                total:0,
                currentpage:1,
                tableData:[],
                ipNoPage:1,
                IpTableVisible: false,
                ipFlag:true,
                paNameNo:'',
                ipSearch: {},
                ipSearch: {},
                emptyText: '查無資料',
                dialogFormVisible: false,
                dialogTitle: 'MEMBER Transaction Statment',
                addFlag: true,
                formData: {
                    tranAmt: 0
                },
                distInfo: {
                    distNo: '',
                    status: -1
                },
                checkList: [],
                // checkGroup: ['Non-Roy/Non-Tax.Income', 'Apply Comm', 'Apply Tax', 'After Dist'],
                checkGroup: [
                    {label: 'Non-Roy/Non-Tax.Income', disabled: undefined, change: 'selectNonTax',property:'nonTaxableIncomeInd'},
                    {label: 'Apply Comm', disabled: undefined, change: 'selectApply',property:'commissionInd'},
                    {label: 'Apply Tax', disabled: undefined, change: 'selectApply',property:'withheldTaxInd'},
                    {label: 'After Dist', disabled: 'disabled', change: '',property:'addAfterDistInd'},
                ],
                showTypeDialog: false,
                typeList: [],
                rules: {
                    distNo: [
                    { required: true, message: '请输入distNo', trigger: 'change' }
                    ],
                    ipBaseNo: [
                    { required: true, message: '请输入ipBaseNo', trigger: 'change' }
                    ]
                    // paNameNo: [
                    // { required: true, message: '请输入paNameNo', trigger: 'blur' }
                    // ],
                    // paName: [
                    // { required: true, message: '请输入paName', trigger: 'blur' }
                    // ]
                },
                rules2: {
                    tranType: [
                    { required: true, message: '请输入类型', trigger: 'blur' }
                    ],
                    tranAmt: [
                    { required: true, message: '请输入金额', trigger: 'blur' }
                    ]
                },
            }            
        },
        mounted () {
            console.log('this is mounted');
            console.log(this.$route.query)
            if(this.$route.query.distNo){
                this.searchForm.distNo = this.$route.query.distNo;
                this.searchForm.ipBaseNo = this.$route.query.ipBaseNo;
                this.searchForm.paNameNo = this.$route.query.paNameNo;
                this.searchForm.paName = this.$route.query.paName;
                this.getList(1);
            }
            // this.$nextTick( () => {
            //     // this.addListener();
            // })
        },
        methods:{
            clearSearch(){
                this.searchForm = {
                    distNo:'',
                    ipBaseNo:'',
                    paName:'',
                    paNameNo:''
                }
                // this.getList();
            },

            selectNonTax(checked){
                if(checked.includes(this.checkGroup[0].label)){
                    this.checkGroup[1].disabled = 'disabled'
                    this.checkGroup[2].disabled = 'disabled'
                    this.checkList = checked
                } else if(checked.includes(this.checkGroup[1].label) || checked.includes(this.checkGroup[2].label)){
                    this.checkGroup[0].disabled = 'disabled'
                    this.checkList = checked
                } else {
                    this.checkGroup.forEach(item => {if(item.label != 'After Dist'){item.disabled = undefined}})
                    this.checkList = checked
                }
            },  
            selectApply(checked){
                if(checked){
                    this.checkGroup[0].disabled = 'disabled'
                    this.checkList = this.checkList.filter(item => item != 'Non-Roy/Non-Tax.Income')
                } else {
                    this.checkGroup[0].disabled = undefined
                }
            },      
            getList(page){
                this.$refs.searchForm.validate(validate => {
                    console.log(validate,'validate');
                    if (validate) {
                        this.searchForm.page_num=page?page:this.currentpage
                        if (this.searchForm.distNo||this.searchForm.ipBaseNo||this.searchForm.paNameNo||this.searchForm.paName) {
                            this.searchForm.page_num=1
                        }
                        
                        let params = this.searchForm
                        console.log(this.searchForm)
                        let _this = this
                        this.emptyText = '數據加載中';
                        this.$http.get('/transaction/statement/member',{params}).then(res => {
                            console.log(res)
                            if(res.success){
                                _this.tableData = res.data.data.list
                                _this.total = res.data.data.total
                                _this.currentpage=res.data.data.pageNum
                                if(! _this.tableData || _this.tableData.length == 0){
                                    _this.emptyText = '查無資料';
                                } else {
                                    if(_this.tableData[0].getPayStatus == 'P'){
                                        _this.addFlag = false
                                    } else {
                                        _this.addFlag = true
                                    }
                                }
                            }
                        })
                    }
                })
            },
            handleCurrentChange(val){
                console.log('valalva',val);
                this.currentpage = val
                this.getList()
            },
            getNameByBaseNo(){
                if(this.searchForm.ipBaseNo){
                    this.$http.post('/ip/name/es?ip_no='+this.searchForm.ipBaseNo+'&page_num='+this.ipNoPage+'&page_size=15').then(res => {
                        console.log(res)
                        if(res.success){
                            let list = res.data.list
                            if(list.length){
                                list.map(item => {
                                    if(item.name_type === 'PA'){
                                        this.searchForm.paName = item.name
                                        this.searchForm.paNameNo = item.ip_name_no
                                    }
                                })
                            }else{
                                this.$message({
                                    message: 'IP_BASE_NO無效',
                                    type: 'warning'
                                });
                            }
                        }
                    })
                }

            },
            add(){
                // let data = this.searchForm

                if(!this.searchForm.distNo){
                    this.$toast({ tips: 'distNo不能為空！' })
                    return;
                }

                console.log(this.formData)
                let _this = this
                if(this.distInfo.status == -1 || this.distInfo.distNo != this.searchForm.distNo){
                    this.$http.get('/dist/findParamInfoStatus',{params: {distNo: this.searchForm.distNo}}).then(res => {
                        console.log(res)
                        if(res.success){
                            _this.distInfo.distNo = _this.searchForm.distNo
                            _this.distInfo.status = res.data

                            _this.dialogTitle = '新增MEMBER Transaction Statment'
                            _this.dialogFormVisible = true
                            _this.formData = _this.$utils.copy(_this.searchForm)
                            _this.formData.id = undefined

                            if(_this.distInfo.status == 7){
                                _this.checkList = ['After Dist']
                            } else {
                                _this.checkList = []
                            }
                        } else {
                            _this.distInfo.distNo = _this.searchForm.distNo
                            _this.distInfo.status = -1
                            _this.checkList = []

                            this.$message({
                                message: '分配編號不存在',
                                type: 'warning'
                            });
                        }
                    })
                } else {
                    _this.dialogTitle = '新增MEMBER Transaction Statment'
                    _this.dialogFormVisible = true
                    _this.formData = _this.$utils.copy(_this.searchForm)
                    _this.formData.id = undefined

                    if(_this.distInfo.status == 7){
                        _this.checkList = ['After Dist']
                    } else {
                        _this.checkList = []
                    }
                }
            },

            edit(row) {
                this.dialogTitle = '編輯MEMBER Transaction Statment'
                this.dialogFormVisible = true
                this.formData = this.$utils.copy(row)
                
                let _this = this
                _this.checkGroup.forEach(c => {
                    if(_this.formData[c.property] == 'Y'){
                        _this.checkList.push(c.label)
                    }
                })

                if(_this.checkList.some(item => {
                    return item == _this.checkGroup[1].label || item == _this.checkGroup[2].label
                })){
                    _this.checkGroup[0].disabled = 'disabled'
                } else if(_this.checkList.includes(_this.checkGroup[0].label)){
                    _this.checkGroup[1].disabled = 'disabled'
                    _this.checkGroup[2].disabled = 'disabled'
                }

            },
            clearForm() {
                this.$refs.addDialog && this.$refs.addDialog.clearValidate()
                this.checkList = []
                this.checkGroup.forEach((ch,index) => {
                    if(index < 3){
                        ch.disabled = undefined
                    }
                })
            },
            getTypeList() {
                this.emptyText = '數據加載中';
                this.$http.get('/transaction/statement/tran/type/all').then(res => {
                    if (res.success) {
                        this.showTypeDialog = true
                        this.typeList = res.data
                        console.log(res.data)
                        if(! this.typeList || this.typeList.length == 0){
                            this.emptyText = '暫無數據';
                        }
                    }
                })
            },
            editConfirm() {
                this.$refs.editForm.validate(validate => {
                    this.checkList.indexOf('Non-Roy/Non-Tax.Income') > -1 ? this.formData.nonTaxableIncomeInd = 'Y' : this.formData.nonTaxableIncomeInd = 'N'
                    this.checkList.indexOf('Apply Comm') > -1 ? this.formData.commissionInd = 'Y' : this.formData.commissionInd = 'N'
                    this.checkList.indexOf('Apply Tax') > -1 ? this.formData.withheldTaxInd = 'Y' : this.formData.withheldTaxInd = 'N'
                    this.checkList.indexOf('After Dist') > -1 ? this.formData.addAfterDistInd = 'Y' : this.formData.addAfterDistInd = 'N'
                    let _message = ''
                    if(this.formData.commissionInd == 'Y' || this.formData.withheldTaxInd == 'Y'){
                        _message = '請重新執行分配'
                    }
                    console.log(this.formData)
                    this.$refs.editForm.validate(validate => {
                        if (validate) {
                            this.$http.post('/transaction/statement/member', this.formData).then(res => {
                                console.log(res)
                                if (res.success) {
                                    if (res.data.code === 200) {
                                        this.dialogFormVisible = false
                                        this.getList(this.currentpage)
                                        this.$message({
                                            message: '存檔成功!' + _message,
                                            type: 'success',
                                            type: 'success', duration: 3000,
                                        })
                                    } else {
                                        this.$message({
                                            message: res.data.message,
                                            type: 'warning'
                                        })
                                    }
                                }
                            })
                        }
                    })
                })                
            },
            chooseType(row) {
                this.$set(this.formData, 'tranType', row.tranType)
                this.$set(this.formData, 'tranDescpt', row.tranDescpt)
                this.showTypeDialog = false
                this.$refs.typeInput.focus()
                this.$refs.typeInput.blur()
            },
            cancelEdit () {
                this.dialogFormVisible = false
                this.$refs.editForm.resetFields()
            },
            del(row){
                this.$msgbox.confirm("是否確認刪除?", "提示", {
                    confirmButtonText: "確定",
                    cancelButtonText: "取消",
                    type: "warning",
                }).then(() => {
                    this.$http.delete('/transaction/statement/member/' + row.id).then(res => {
                        console.log(res)
                        this.getList(1)
                        if (res.data.code == 200) {
                            this.$message({
                            message: '刪除成功',
                            type: 'success'
                        });
                        } else {
                            this.$toast({ tips: res.data.message })
                        }
                        })
                    })
                return;
            },
            getDistStatus(){
                if(this.distInfo.status == -1 || this.distInfo.distNo != this.searchForm.distNo){
                    this.$http.get('/dist/findParamInfoStatus',{params: {distNo: this.searchForm.distNo}}).then(res => {
                        console.log(res)
                        if(res.success){
                            this.distInfo.distNo = this.searchForm.distNo
                            this.distInfo.status = res.data
                        } else {
                            this.distInfo.distNo = this.searchForm.distNo
                            this.distInfo.status = -1
                        }
                    })
                }
            },
            getPaName(){
                if(this.ipFlag){
                    this.paNameNo = ''
                    this.ipFlag=false
                    this.$refs.paName.paNameDataD()
                }
            },
            changePa(data){
                if(data){
                if(this.ipFlag){
                    this.paNameNo = data
                    this.ipFlag=false
                    this.$nextTick(()=>{
                    this.$refs.paName.paNameDataC()
                    })
                }
                }else{
                this.$set(this.searchForm,'paName','')
                }
            },
            checkPaName(data){
                this.ipFlag=true
                this.$set(this.searchForm,'ipBaseNo',data.ip_base_no)
                this.$set(this.searchForm,'paNameNo',data.ip_name_no)
                this.$set(this.searchForm,'paName',data.name)
            }
        },
        // beforeRouteEnter (to, from, next) {
        //     next(vm => {
        //         vm.getList(vm.searchForm.page_num)

        //     })
        // }
    }
</script>
  
<style lang="scss" scoped>
    el-table-custom-list {
        width: 100%
    }
    el-table-custom-edit {
        width: 80%
    }
    dialog-el-form {
        .el-input {
            width: 350px;
        }
    }
</style>
