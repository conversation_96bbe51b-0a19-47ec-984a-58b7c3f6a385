<template>
  <div class="tab-content filter-work">
    <el-form :inline="true" :model="searchForm" ref="form" :rules="rules" class="demo-form-inline" @keyup.enter.native="getClaimList()">
      <el-form-item label="">
        <el-checkbox style="margin-left: 2px;" v-model="searchForm.per">PER</el-checkbox>
      </el-form-item>
      <el-form-item label="">
        <el-checkbox v-model="searchForm.mec">MEC</el-checkbox>
      </el-form-item>
      <el-form-item label="">
        <el-input v-model.trim="searchForm.workId" placeholder="work_id"></el-input>
      </el-form-item>
      <el-form-item label="">
        <el-input v-model.trim="searchForm.workSoc" placeholder="work_society_code"></el-input>
      </el-form-item>
      <el-form-item label="">
        <el-input v-model.trim="searchForm.ipBaseNo" placeholder="IP BASE NO"></el-input>
      </el-form-item>
      <el-form-item label="">
        <el-input v-model.trim="searchForm.name" placeholder=" IP NAME"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getClaimList()" v-if="isAuth('distribute:ClaimFilterWorklist:find')">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="success" @click="handlePopupAddClaim">新 增</el-button>
      </el-form-item>
      <!-- <el-form-item>
        <el-upload class="upload-demo" ref="upload" action="" accept=".xls,.xlsx" :auto-upload="false" :on-change="uploadChange" :show-file-list="false">
          <el-button size="small" type="primary" style="background-color: #b240ff;border-color: #b240ff;"  v-if="isAuth('distribute:ClaimFilterWorklist:uploading')">上传EXCEL</el-button>
        </el-upload>
      </el-form-item> -->
      <el-form-item>
        <el-button type="danger" @click="handleBatchDelete">批量删除</el-button>
      </el-form-item>
      <el-form-item>
        <span class="clear-search" @click="clearSearch()" v-if="isAuth('distribute:ClaimFilterWorklist:find')">清除搜索</span>
      </el-form-item>
    </el-form>
<el-table class="main-table" :empty-text="tableresult" :data="tableData" border stripe style="width: 100%" height="550px" v-loading="loading" @selection-change="handleSelectionChange">
      <el-table-column  type="selection" width="55"></el-table-column>
      <el-table-column prop="company" label="company">
      </el-table-column>
      <el-table-column prop="ipBaseNo" label="Ip Base No">
      </el-table-column>
      <el-table-column prop="name" label="name">
      </el-table-column>
      <el-table-column prop="workId" label="work_id">
      </el-table-column>
      <el-table-column prop="workSoc" label="work_society_code">
      </el-table-column>
      <el-table-column prop="right" label="Right">
        <template slot-scope="scope">
          <span v-if="scope.row.per" class="right-item">
            <i class="el-icon-check" style="color: #17b3a3;"></i>
            <span>PER</span>
          </span>
          <span v-if="scope.row.mec" class="right-item">
            <i class="el-icon-check" style="color: #b240ff;"></i>
            <span>MEC</span>
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="extInfo" label="ext_info">
      </el-table-column>
      <el-table-column prop="createTime" label="create_time">
      </el-table-column>
      <el-table-column label="operation">
        <template slot-scope="scope">
          <el-button @click="handlePopupUpdateClaim(scope.row)" type="text" size="small" v-if="isAuth('distribute:ClaimFilterWorklist:del')">修改</el-button>
          <el-button @click="showReportDialog(scope.row)" type="text" size="small" v-if="isAuth('distribute:ClaimFilterWorklist:del')">刪除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- <el-pagination background layout="prev, pager, next" :total='claimTotal' :current-page="searchForm.pageNum" @current-change="handleCurrentdetails">
    </el-pagination> -->

    <el-pagination background layout="sizes, prev, pager, next" :total="claimTotal" :current-page="searchForm.pageNum" @current-change="handleCurrentdetails"
      :page-sizes="[10, 20, 50, 100]" :page-size="20" @size-change="handleSizeChange">
      </el-pagination>
  </div>
</template>

<script>
import dialogAddFilterWork from './dialog-add-filter-ip.vue';

export default {
  name: 'TabFilterWork',
  props: {
    company: {
      type: String,
      default: 'Youtube'
    }
  },
  data() {
    return {
      tableresult:' ',
      claimTotal: 10,
      searchForm: {
        pageNum: 1,
        pageSize: 20,
        workId: '',
        workSoc: '',
        per: false,
        mec: false,
        ipBaseNo: '',
        name: '',
        company: this.company
      },
      selectTableList:[],
      tableData: [],
      showReport: false,
      productList: [],
      products: [],
      dateRange: [],
      loading: false,
      rules: {
        company: [
          { required: true, message: '請輸入company', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    company(newVal) {
      this.searchForm.company = newVal;
    }
  },
  computed: {
    startTime() {
      if (this.valueDate) {
        let date = this.valueDate[0]
        let year = date.getFullYear()
        let month = date.getMonth() + 1
        let day = date.getDate()
        let startValue = `${year}-${month}-${day} 00:00:00`
        return startValue
      } else {
        return '1990-01-01 00:00:00'
      }
    },
    endTime() {
      if (this.valueDate) {
        let date = this.valueDate[1]
        let year = date.getFullYear()
        let month = date.getMonth() + 1
        let day = date.getDate()
        let startValue = `${year}-${month}-${day} 00:00:00`
        return startValue
      } else {
        return '2100-12-31 23:59:59'
      }
    }
  },
  methods: {
    uploadChange(file) {
      this.$utils.uploadFile({flie:file.raw}, '/claimFilterWork/importClaimFilterOtherWork', this)
    },
    clearSearch() {
      this.searchForm.workId = ''
      this.searchForm.workSoc = ''
      this.searchForm.ipBaseNo = ''
      this.searchForm.name = ''
      this.searchForm.per= false
      this.searchForm.mec= false
      this.getClaimList()
    },
    showReportDialog(row) {
      if (row.id) {
        this.$alert('確定要刪除嗎？', '刪除', {
          confirmButtonText: '确定',
          showCancelButton: true,
          cancelButtonText: '取消',
          callback: action => {
            if (action == 'confirm') {
              this.$http.delete('/claimFilterIp/deleteClaimFilterOtherIp/' + row.id).then(res => {
                if (res.success) {
                  this.getClaimList()
                  this.$toast({ tips: '刪除成功' })
                } else {
                  this.$toast({ tips: res.data.message })
                }
              })
            }
          }
        });
      }
    },
    createReport() {
      console.log('新增params', this.createReportData)
      this.$refs.form.validate(validate => {
        if (validate) {
          this.$http.post('/claimFilterIp/addClaimFilterOtherIp', this.createReportData).then(res => {
            console.log(res)
            if (res.data.code == 200) {
              this.showReport = false
              this.$toast({ tips: '新增成功!' })
              this.getClaimList()
              this.createReportData = {
                amendTime: '',
                createTime: '',
                extInfo: '',
                id: '',
                workId: '',
                workSoc: '',
                workUniqueKey: ''
              }
            } else {
              this.$toast({ tips: res.data.message })
            }
          }).then((res) => {
            console.log(res)
          })
        }
      })
    },
    handleSelectionChange(tableList) {
      this.selectTableList = tableList
    },
    handleCurrentdetails(val) {
      this.searchForm.pageNum = val
      this.getClaimList(val)
    },
    getClaimList(page) {
      this.$refs.form.validate(validate => {
        if (validate) {
          this.tableresult='數據加載中...'
          let params = this.searchForm
          params.pageNum=page || this.searchForm.pageNum
          this.loading = true
          this.$http.get('/claimFilterIp/getClaimFilterOtherIp', { params }).then(res => {
            this.loading = false
            if (res.data.code == 200) {
              this.tableData = res.data.data.list
              this.tableresult=this.tableData.length==0?'暫無數據':' '
              this.claimTotal = res.data.data.total
            }
          })
        }
      })

    },
    handlePopupAddClaim() {
      this.$fnModal({
        title: '新增',
        width: '500px',
        centered: 'headup',  // true = 居中， 'headup' = 抬头居中
        content: dialogAddFilterWork,
        data: {
          // createReport: this.createReport,
          createReportData: {
            company: this.searchForm.company
          },
          callback: this.getClaimList
        }
      })
    },
    handlePopupUpdateClaim(row){
      console.log("this.searchForm.company",row)

      this.$fnModal({
        title: '修改',
        width: '500px',
        centered: 'headup',  // true = 居中， 'headup' = 抬头居中
        content: dialogAddFilterWork,
        data: {
          // createReport: this.createReport,
          createReportData: Object.assign({}, row, {per: row.per == 1,mec: row.mec == 1}),
          update: true,
          callback: this.getClaimList
        }
      })
    },
    handleBatchDelete() {
      if(this.selectTableList.length == 0){
        this.$toast({ tips: '請先選擇要刪除的數據！' })
        return
      }
      this.$alert('確定要刪除嗎？', '批量刪除', {
        confirmButtonText: '确定',
        showCancelButton: true,
        cancelButtonText: '取消',
        callback: action => {
          if (action == 'confirm') {
            let ids = this.$utils.getIds(this.selectTableList, 'id')
            this.$http.delete('/claimFilterIp/deleteList', { params: { id: ids.toString() } }).then(res => {
              console.log(res)
              if (res.data.code == 200) {
                this.$toast({ tips: '刪除成功' })
                this.getClaimList()
              } else {
                this.$toast({ tips: res.data.message })
              }
            })
          }
        }
      });

    },
    handleSizeChange(val) {
      console.log(val);
      this.searchForm.pageSize = val
      this.getClaimList(this.searchForm.pageNum);
    },
  },

  activated() {
    this.getClaimList()
  }
}
</script>
<style lang="scss" scoped>
.tab-content {
  .main-table {

    .right-item {
      & + .right-item {
        margin-left: 12px;
      }
      .el-icon-check {
        font-weight: bold;
        font-size: 20px;
      }
    }
  }
}
</style>
