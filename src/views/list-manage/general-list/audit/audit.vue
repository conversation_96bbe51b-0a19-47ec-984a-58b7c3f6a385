<template>
  <div>
    <!-- <el-button @click="testClick">test刷新</el-button> -->
    <el-collapse v-model="activeNames" class="clear" style="border-bottom: 0;">
      <el-collapse-item class="step-jump" title="File Work Info" name="1" :disabled="true">
        <div class="boxline p-t-10">
          <el-form :inline="true" label-position="left" label-width="100px">
            <div v-if="listType != 'pg'">
              <el-form-item label="WorkTitle">
                <el-input :title="fileInfo.title" v-model="fileInfo.title" readonly></el-input>
              </el-form-item>
              <el-form-item label="Composer">
                <el-input :title="fileInfo.composers" v-model="fileInfo.composers" readonly></el-input>
              </el-form-item>
              <el-form-item label="Author">
                <el-input :title="fileInfo.authors" v-model="fileInfo.authors" readonly></el-input>
              </el-form-item>
              <el-form-item label="ISWC">
                <el-input :title="fileInfo.iswc" v-model="fileInfo.iswc" readonly></el-input>
              </el-form-item>
              <el-form-item label="Performer">
                <el-input :title="fileInfo.artists" v-model="fileInfo.artists" readonly></el-input>
              </el-form-item>
              <el-form-item label="ISRC">
                <el-input :title="fileInfo.isrc" v-model="fileInfo.isrc" readonly></el-input>
              </el-form-item>
              <el-form-item label="ClickNumber">
                <el-input :title="fileInfo.artists" v-model="fileInfo.clickNumber" readonly></el-input>
              </el-form-item>
              <el-form-item label="ExtJson">
                <el-input :title="fileInfo.extJson" v-model="fileInfo.extJson" readonly></el-input>
              </el-form-item>
              <!-- <el-form-item label="NetRevenue">
                <el-input v-model="fileInfo.workPrice" readonly></el-input>
              </el-form-item> -->
            </div>
            <div v-else>
              <el-form-item label="WorkTitle" label-width="110px">
                <el-input v-model="fileInfo.title" readonly></el-input>
              </el-form-item>
              <el-form-item label="Eposide No" label-width="110px">
                <el-input v-model="fileInfo.episodeNo" readonly></el-input>
              </el-form-item>
              <el-form-item label="TV Name" label-width="110px">
                <el-input v-model="fileInfo.tvName" readonly></el-input>
              </el-form-item>
              <el-form-item class="f12" label="Channel Name" label-width="110px">
                <el-input v-model="fileInfo.channelName" readonly></el-input>
              </el-form-item>
            </div>
          </el-form>
        </div>
      </el-collapse-item>
      <el-collapse-item class="step-jump" title="Match Work Info" name="2" :disabled="true" :style="listType == 'pg' ? 'width: 600px' : ''">
        <div class="boxline">
          <el-table 
            :empty-text="emptyText" 
            :data="matchTable" 
            v-loading="loading" 
            border 
            stripe 
            height="220px" 
            highlight-current-row 
            ref="matchTableRef" 
            @row-click="changeWorkother" 
            @row-dblclick="workDrawer" 
            class="match-table" 
            style="max-height: 240px;overflow-y: auto;">
            <el-table-column prop="matchWorkId" label="WorkNo">
              <!-- <template slot-scope="scope">
                <span @click="changeWork(scope.row)">{{scope.row.matchWorkId}}</span>
              </template> -->
            </el-table-column>
            <!-- <el-table-column label="operation">
              <template slot-scope="scope">
                <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                  <i class="el-icon-check" @click="changeWork(scope.row)"></i>
                </span>
              </template>
            </el-table-column> -->
            <el-table-column prop="matchWorkSocietyCode" label="WorkSoc">
              <template slot-scope="scope">
                {{scope.row.matchWorkSocietyCode}}
              </template>
            </el-table-column>
            <el-table-column prop="matchTitle" label="WorkTitle">
              <template slot-scope="scope">
                {{scope.row.matchTitle}}
              </template>
            </el-table-column>
            <el-table-column prop="matchScore" label="Score">
              <template slot-scope="scope">
                {{scope.row.matchScore}}
              </template>
            </el-table-column>
          </el-table>
          <el-form class="match-info" :inline="true" label-position="left" label-width="90px">
            <div v-if="listType != 'pg'">
              <el-form-item label="Composer">
                <el-input v-model="matchInfo.matchComposers" style="width: 320px;" readonly></el-input>
              </el-form-item>
              <el-form-item label="Author">
                <el-input v-model="matchInfo.matchAuthors" style="width: 320px;" readonly></el-input>
              </el-form-item>
              <el-form-item label="ISWC">
                <el-input v-model.number="matchInfo.matchIswc" style="width: 320px;" readonly></el-input>
              </el-form-item>
            </div>
            <div v-else>
              <el-form-item label="Eposide No" label-width="110px">
                <el-input v-model="matchInfo.episode_no" style="width: 350px;" readonly></el-input>
              </el-form-item>
            </div>
            <div v-if="listType != 'pg'">
              <div class="f-l" style="width: 49%;padding-left: 20px;box-sizing: border-box;">
                <div>Performer</div>
                <el-form-item>
                  <ul class="list" style="width: 190px;max-height: 300px;min-height:35px; margin-bottom: -5px;overflow-y: auto;">
                    <li class="el-sl" v-for="(item, index) in matchInfo.performerList" :key="index">{{item}}</li>
                    <li v-if="!matchInfo.performerList || matchInfo.performerList.length == 0" style="border-bottom:''">暂無數據</li>
                  </ul>
                </el-form-item>
              </div>
              <div class="f-l" style="width: 51%;">
                <div>ISRC</div>
                <el-form-item>
                  <ul class="list" style="width: 190px;max-height: 300px;overflow-y: auto;min-height:35px;    margin-bottom: -5px;">
                    <li class="el-sl" v-for="(item, index) in matchInfo.isrcList" :key="index">{{item}}</li>
                    <li v-if="!matchInfo.isrcList || matchInfo.isrcList.length == 0">暂無數據</li>
                  </ul>
                </el-form-item>
              </div>
            </div>
            <div class="component" v-else>
              <el-form-item label="Component">
                <el-table :empty-text="emptyText1" stripe :data="matchInfo.componentList" border>
                  <!-- todo-guan 等周兵接口添加字段後，確認字段 -->
                  <el-table-column prop="title" label="Title">
                  </el-table-column>
                  <el-table-column prop="componentWorkId" label="Work No" width="120px">
                  </el-table-column>
                  <el-table-column prop="comWorkSociety" label="WorkSoc" width="90px">
                  </el-table-column>
                  <!-- todo-guan 等周兵接口添加字段後，確認字段 -->
                  <el-table-column prop="genre" label="Genre" width="70px">
                  </el-table-column>
                  <el-table-column prop="usageType" label="Usage" width="70px">
                  </el-table-column>
                </el-table>
              </el-form-item>
            </div>
          </el-form>
        </div>
      </el-collapse-item>
    </el-collapse>
    <div class="t-c p-t-40" style="width: 840px;">
      <el-button type="primary" @click="submitFn(1)">確認</el-button>
      <el-button type="primary" @click="submitFn(2)">拒絕</el-button>
      <el-button type="primary" @click="selectWorkFn()">指定作品</el-button>
    </div>

    <!-- 指定作品 查找作品 弹框 -->
    <!-- 輸入查詢条件，查詢結果在match work info 列表里展示 -->
    <!-- <el-dialog :visible.sync="selectWork.selectWorkShow">
        <div style="width: 600px;margin: auto;margin-bottom: 20px">
            <el-input style="width: 140px" placeholder="Work No" v-model='selectWork.workId'></el-input>
            <el-input style="width: 60px" placeholder="Soc" v-model='selectWork.soc'></el-input>
            <el-input style="width: 240px" placeholder="Title" v-model='selectWork.title'></el-input>
            <el-button slot="append" icon="el-icon-search" @click="querySelectWorkList()"></el-button>
        </div>
    </el-dialog> -->
    <el-dialog :visible.sync="show" width="1000px" title="指定作品" :close-on-click-modal="false">
      <div style="width: 600px;margin: auto;margin-bottom: 20px">
        <el-input  @keyup.enter.native="onSubmit()" v-model="searchInfo.title" placeholder="Title" style="width: 220px;"></el-input>
        <el-input  @keyup.enter.native="onSubmit()" v-model="searchInfo.workId" placeholder="Work No" style="width: 128px;"></el-input>
        <el-input  @keyup.enter.native="onSubmit()" v-model="searchInfo.soc" placeholder="Soc" style="width: 60px;"></el-input>
        <el-button type="primary" @click="onSubmit()">查詢</el-button>
        <span class="clear-search" @click="clearSearch()">清除搜索</span>
      </div>
      <el-table :empty-text="emptyText2" :data="tableData">
        <el-table-column property="title" label="title">
          <template slot-scope="scope">
            {{scope.row.title||scope.row.title_en}}
          </template>
        </el-table-column>
        <el-table-column property="work_id" label="workId" width="120"></el-table-column>
        <el-table-column property="work_society_code" label="workSoc" width="90"></el-table-column>
        <el-table-column property="genre_code" label="Genre" width="80"></el-table-column>
        <el-table-column property="genre_code" label="Composer" width="150">
          <template slot-scope="scope">
            {{scope.row.composer && scope.row.composer.join('、')}}
          </template>
        </el-table-column>
        <el-table-column property="genre_code" label="Author" width="150"><template slot-scope="scope">
            {{scope.row.author && scope.row.author.join('、')}}
          </template></el-table-column>
        <el-table-column label="operation" width="100">
          <template slot-scope="scope">
            <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer" title="指定作品">
              <i class="el-icon-check" @click="checkedWork(scope.$index,scope.row)"></i>
            </span>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background layout="prev, pager, next" :total="total" @current-change="onSubmit">
      </el-pagination>
    </el-dialog>
    <work-list ref="workList" :workId='selectWorkNo.toString()' :workSocietyCode='selectWorkSoc.toString()'></work-list>
  </div>
</template>
<script>
import workList from '../../../demo/workList.vue'
export default {
  data() {
    return {
      showrow: [],
      listType: '',
      queryparam: "",
      queryindex: "",
      // file work info
      fileInfo: {},
      // 选中的work 的信息
      matchInfo: {},
      activeNames: ['1', '2'],
      matchTable: [],
      isrcList: [],
      performerList: [],
      selectWorkTitleId: '',
      selectWork: {
        workId: '',
        soc: '',
        title: '',
        selectWorkShow: false,
        list: []
      },
      show: false,
      searchInfo: {
        workId: '',
        title: '',
        soc: ''
      },
      tableData: [],
      total: 0,
      loading: false,
      perList: {},
      performer: {},
      emptyText:'暫無數據',
      emptyText1:'暫無數據',
      emptyText2:'暫無數據',
      selectWorkNo:'',
      selectWorkSoc:'',
    }
  },
  components:{ workList },
  activated() {
    this.listType = this.$route.query.listType;
    this.queryparam = this.$route.query.usetabledata;
    this.queryindex = this.$route.query.useparams;
    this.queryInfo();
  },
  created() {

  },
  destroyed() {
    localStorage.removeItem("tempindex");
    localStorage.removeItem("temprow");
    localStorage.removeItem("temprow1");
    localStorage.removeItem("temprow2");

  },
  methods: {
    workDrawer(row){
      console.log(row)
      this.selectWorkNo = row.matchWorkId;
      this.selectWorkSoc = row.matchWorkSocietyCode;
      this.$nextTick(()=>{
          this.$refs.workList.init()
      })
    },
    queryInfo(index, nextone) {
      if (nextone == 2) {
        // console.warn('*****',this.$route.query.useparams+1,'^^^^^',this.$route.query.usetabledata,"$$$$$$",this.$route.query.usetabledata[this.$route.query.useparams+1])
        if (index >= this.queryparam.length) {
          this.$toast({ tips: '這已經是最後一條數據了!' })
          return;
        } else {
          this.$http
            .get('/listmatch/getListMatchDataBasicById', {
              params: { id: this.queryparam[index].id },
            })
            .then((res) => {
              //    alert(index)
              localStorage.setItem("tempindex", index);

              //    console.warn('localstorage',localStorage.getItem('tempindex'))
              if (res.success) {
                this.fileInfo = res.data;
                this.fileInfo.extJson = res.data.extJson == '{}' ? '' : res.data.extJson
                this.queryMatchWorks(res.data.fileMappingId);
                // this.fileMappingId = res.data.fileMappingId;
                // this.queryMatchWorks(this.fileMappingId);
                // this.getFileWorkIpShare();
              }
            });
        }
      } else {
        this.$http.get('/listmatch/getListMatchDataBasicById', { params: { id: this.$route.query.id } }).then(res => {
          if (res.success) {
            this.fileInfo = res.data;
            this.fileInfo.extJson = res.data.extJson == '{}' ? '' : res.data.extJson
            this.queryMatchWorks(res.data.fileMappingId);

          }
        })
      }
    },
    queryMatchWorks(matchBaseId) {
      this.loading = true
      this.emptyText = '數據加載中';
      this.$http.get('/listmatch/getListMatchDataBasicMatchWorkByMatchBaseId', { params: { matchBaseId: matchBaseId } }).then(res => {
        if (res.success) {
          this.matchTable = res.data ? res.data : [];
          this.loading = false
          if (this.matchTable.length > 0) {
            this.$refs.matchTableRef.setCurrentRow(this.matchTable[0]);
            this.matchTable.length && this.matchTable.forEach(item => {
              this.$set(item, 'right', 'PER&' + item.matchWorkId)
            })
            //默认选中第一行
            let initcheck = localStorage.getItem('temprow') ? localStorage.getItem('temprow') : 0
            if (localStorage.getItem('temprow') != 'null') {
              this.changeWorkInit(this.matchTable[initcheck]);
              this.$refs.matchTableRef.setCurrentRow(this.matchTable[initcheck]);
            } else {
              this.changeWorkInit(this.matchTable[initcheck]);
            }
          }
        }
        if(! this.matchTable || this.matchTable.length == 0){
          this.emptyText = '暫無數據';
        }
      })
    },
    changeWorkInit(row, column, event) {
      console.log('nininini', row, column, event);
      // localStorage.setItem('temprow',JSON.parse(JSON.stringify(row)))

      this.matchInfo = {};
      this.selectWorkTitleId = row.id;

      //如果類型為!pg，  那么不需要查詢，直接把table里的值复制過去就行了
      //如果類型為pg， 则，把table值复制過去，且 查詢component
      if (this.listType != 'pg') {
        // alert('pgpgpgpg')
        console.warn('rowrworow===', row, this.matchTable)
        this.matchInfo = row;
        if (this.matchInfo.type != 'm') { //如果不是用户指定作品查找的，就转化數據
          // this.matchInfo.performerList = this.matchInfo.matchArtists.split(';');
          // this.matchInfo.isrcList = this.matchInfo.matchIsrc.split(';');
          this.matchInfo.performerList = this.matchInfo.matchArtists != "" ? this.matchInfo.matchArtists.split(";") : [];
          this.matchInfo.isrcList = this.matchInfo.matchIsrc != "" ? this.matchInfo.matchIsrc.split(";") : [];
        }
        this.showrow = row
      } else {
        this.matchInfo = row;
        // let params = {
        //   workId: row.matchWorkId,
        //   workSocietyCode: row.matchWorkSocietyCode
        // }
        // this.$http.get('/wrk/getWrkWorkById', { params }).then(res => {
        //   if (res.success) {
        //     this.matchInfo.componentList = res.data.wwcList;
        //     this.$set(this.matchInfo, 'componentlist', res.data.wwcList);
        //   }
        // })
      }

      console.log('============matchinfo', this.matchInfo);
    },
    changeWorkother(row, column, event) {
      let index = this.matchTable.indexOf(row)
      // console.log('***********', row, column, event, '00000', index);


      localStorage.setItem("temprow", index);
      localStorage.setItem("temprow1", JSON.stringify(column));
      localStorage.setItem("temprow2", JSON.stringify(event));



      // this.matchInfo = {};
      this.selectWorkTitleId = row.id;

      //如果類型為!pg，  那么不需要查詢，直接把table里的值复制過去就行了
      //如果類型為pg， 则，把table值复制過去，且 查詢component
      if (this.listType != 'pg') {
        // alert('pgpgpgpg')
        // console.log('rowrworow===', row, this.matchTable)
        this.matchInfo = row;
        if (this.matchInfo.type != 'm') { //如果不是用户指定作品查找的，就转化數據
          // this.matchInfo.performerList = this.matchInfo.matchArtists.split(';');
          // this.matchInfo.isrcList = this.matchInfo.matchIsrc.split(';');
          this.matchInfo.performerList = this.matchInfo.matchArtists != "" ? this.matchInfo.matchArtists.split(";") : [];
          this.matchInfo.isrcList = this.matchInfo.matchIsrc != "" ? this.matchInfo.matchIsrc.split(";") : [];
        }
        this.showrow = row

        // this.matchInfo = row;
        // let params = {
        //   workId: row.matchWorkId,
        //   workSocietyCode: row.matchWorkSocietyCode
        // }
        // this.$http.get('/wrk/getWrkWorkById', { params }).then(res => {
        //   if (res.success) {
        //     this.performer[this.matchInfo.matchWorkId] = res.data.waList;
        //     console.log('performer', this.performer)
        //     this.matchInfo.componentList = res.data.wwcList;
        //     this.$set(this.matchInfo, 'componentlist', res.data.wwcList);
        //   }
        // })

      } else {
        // this.matchInfo = row;
        // let params = {
        //   workId: row.matchWorkId,
        //   workSocietyCode: row.matchWorkSocietyCode
        // }
        // this.$http.get('/wrk/getWrkWorkById', { params }).then(res => {
        //   if (res.success) {
        //     this.matchInfo.componentList = res.data.wwcList;
        //     this.$set(this.matchInfo, 'componentlist', res.data.wwcList);
        //   }
        // })

      }
    },
    changeWork(row, column, event) {
     
    },
    selectWorkFn() {
      this.searchInfo = {
        workId: '',
        title: '',
        soc: ''
      };
      this.tableData = [];
      this.total = 0;
      this.onSubmit()
      this.show = true;
    },
    clearSearch() {
      this.searchInfo = {
        soc: "",
        title: "",
        workId: "",
      };
      this.$nextTick(() => {
        this.onSubmit();
      })
    },
    onSubmit(page) {
      let params = this.searchInfo;
      params.page = {
        pageNum: page ? page : 1,
        pageSize: 10
      }
      this.loading = true;
      this.emptyText2 = '數據加載中';
      this.$http.post('/wrk/queryWrkWorkListEs', params).then(res => {
        this.loading = false;
        if (res.success) {
          this.tableData = res.data.list;
          this.total = res.data.total;
        }
        if(! this.tableData || this.tableData.length == 0){
          this.emptyText2 = '暫無數據';
        }
      })
    },
    checkedWork(index, row) {
      this.$msgbox.confirm('確定指定此作品?', '提示', {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.selectWorkTitleId = row.id;
        this.submitFn(3);
      }).catch(() => {
      });
    },
    queryWorkInfo() {
      this.selectWork.list = [];
      // 查詢此work no 的ipshare
      let params = {
        workId: this.selectWork.workId,
        workSocietyCode: this.selectWork.soc
      }
      this.$http.get('/wrk/getWrkWorkById', { params }).then(res => {
        if (res.success) {
          this.selectWork.list.push(res.data.wrkWork);
          this.selectWork.workInfo = res.data.wrkWork
        }
      })
    },
    submitFn(status) {
      console.log(this.matchTable,this.selectWorkTitleId)
      if (this.matchTable.length > 1 && !this.selectWorkTitleId) {
        this.$toast({ tips: '請選擇一個匹配作品' })
        return;
      }
      //  0 默认  1通過  2拒絕
      let ajaxData = {
        status: status,
        uniqueKeyMd5: this.fileInfo.uniqueKeyMd5,
        matchDspMappingId: this.selectWorkTitleId,
        fileBaseId: this.fileInfo.fileBaseId
      }
      this.$http.post('/listmatch/checkListMatchDataBasic', ajaxData).then(res => {
        if (res.data.code == 200) {
          if (this.queryindex >= this.queryparam.length - 1) {
            this.$msgbox
              .confirm(`操作成功，继续审核下一条？`, "提示", {
                confirmButtonText: "是", closeOnClickModal: false,
                cancelButtonText: "否",
                type: "warning",
              })
              .then(() => {
                if (this.queryparam.length < 10) {
                  this.$route.query.thissearchparam.pageNum = 1
                }
                this.$http.post('/listmatch/getListMatchList', this.$route.query.thissearchparam).then(res => {
                  if (res.data.code == 200) {
                    this.queryparam = res.data.data.list || [];
                    this.queryindex = 0
                    this.queryInfo(this.queryindex, 2);
                  }
                }).catch(res => {

                })
                //   this.$bus.$emit("closeCurrentTab", () => {
                //     this.$router.push({
                //       name: "auditList",
                //       query: { update: true },
                //     });
                //   });
              })
              .catch(() => {

              });
            return;
          } else {

            this.$msgbox
              .confirm(`操作成功，是否繼續審核下一筆?`, "提示", {
                confirmButtonText: "是", closeOnClickModal: false,
                cancelButtonText: "否",
                type: "warning",
              })
              .then(() => {
                // this.$toast({tips: '操作成功'})
                this.queryindex += 1;
                console.warn("indexquery", this.queryindex, 'legnrth', this.queryparam.length);

                this.queryInfo(this.queryindex, 2);
              })
              .catch(() => {
                this.$bus.$emit('closeCurrentTab', () => { this.$router.push({ name: 'auditList', query: { update: true } }) });
              });
          }
          // this.$toast({tips: '操作成功'});
          // this.$bus.$emit('closeCurrentTab', () => { this.$router.push({name: 'auditList', query: {update: true}})});
        } else {
          this.$toast({ tips: res.data.message });
        }
      })
    },
    testClick() {
      this.$bus.$emit('closeCurrentTab', () => {

        this.$router.push({ name: 'auditList', query: { update: true } })
      });
    }
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      console.log("*****", vm);
      // vm.formData.distNo = to.params.distNo
      if (localStorage.getItem("tempindex")) {
        vm.queryInfo(localStorage.getItem("tempindex"), 2);
      }
      // if (localStorage.getItem("temprow")) {
      //   vm.changeWorkInit(localStorage.getItem("temprow"));
      // }
    });
  },

}
</script>
<style lang="scss" scoped>
@import "../../../../assets/scss/works.scss";
.el-collapse-item {
  width: 480px;
  margin-right: 40px;
  float: left;
}
.match-table {
  width: 100%;
}
/deep/ .match-table thead .el-checkbox {
  display: none;
}
/deep/ .match-info {
  margin-top: 10px;
}
/deep/ .match-info .el-table tr {
  background: #fff;
}
/deep/ .el-collapse-item__arrow {
  display: none;
}
/deep/ .component .el-form-item__content {
  width: 100%;
}
/deep/ .el-input.is-disabled .el-input__inner {
  background: #fff;
  color: #333;
}
/deep/ .el-table__body tr.current-row > td {
  background-color: #17b3a3;
}
/deep/ .el-form-item.f12 label {
  font-size: 13px;
}

ul.list {
  background: #fff;
  padding: 0;
  border: 1px solid #ddd;
  margin-top: 10px;
  border-radius: 4px;
  li {
    list-style: none;
    border-bottom: 1px solid #ddd;
    line-height: 26px;
    padding: 0 4px;
  }
  &:last-child {
    border-bottom: 0;
  }
}

/deep/ .el-input {
  width: 350px;
}

.justforstyle /deep/ .el-drawer__header > span {
  color: #444 !important;
  font-size: 18px !important;
  font-weight: 600 !important;
}
</style>
