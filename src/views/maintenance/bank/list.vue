<template>
    <div>
        <el-form :inline="true" :model="formInline" class="demo-form-inline" @keyup.enter.native="searchList()">
            <el-form-item>
                <el-input v-model="formInline.bankNo" placeholder="bankNo" style="width: 120px;"></el-input>
            </el-form-item>
            <el-form-item>
                <el-input v-model="formInline.bankName" placeholder="bankName" style="width: 120px;"></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="searchList()">搜索</el-button>
            </el-form-item>
            <el-form-item>
                <el-button type="success" @click="addFn">新增</el-button>
            </el-form-item>
            <el-form-item>
                <span class="clear-search" @click="clearSearch()">清除搜索</span>
            </el-form-item>
        </el-form>
        <el-table
            :data="bankTableData"
            border
            stripe
            style="width: 100%;" 
            v-loading="bankLoading"
            :empty-text="emptyText"
            :row-class-name="handleRowClass" 
            @row-dblclick="getBranchTableData">
            <el-table-column
                prop="bankNo"
                label="Bank No"
                min-width="140">
            </el-table-column>
            <el-table-column
                prop="bankName"
                label="Bank Name"
                min-width="150">
            </el-table-column>
            <el-table-column
                fixed="right"
                label="operation"
                width="200">
                <template slot-scope="scope">
                    <div class="option">
                        <el-button @click.native.stop="bankEdit(scope.row)" type="text" size="small">編輯</el-button>
                        <el-button @click.native.stop="bankDelete(scope.row, scope.$index)" type="text" size="small">刪除</el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :total='bankTotal' :page-size='10' @current-change="bankCurrentChange" :current-page="bankPage">
        </el-pagination>
        
        <!--編輯弹框-->
        <el-dialog :title="isbankEdit?'編輯':'新增'" :visible="bankShow" width="500px" :show-close="false" :close-on-click-modal="false">
            <el-form label-position="right" label-width="140px" ref="bankForm" :model="bankTableItem" class="demo-form-inline" :rules="rules" @keyup.enter.native="addBankItem">
                <el-form-item label="Bank No" prop="bankNo">
                    <el-input v-model="bankTableItem.bankNo" placeholder="Bank No"></el-input>
                </el-form-item>
                <el-form-item label="Bank Name" prop="bankName">
                    <el-input v-model="bankTableItem.bankName" placeholder="Bank Name"></el-input>
                </el-form-item>
            </el-form>
            <div style="text-align: center;margin-top: 50px;">
                <el-button @click="bankCancelDialog">取消</el-button>
                <el-button type="primary" @click="addBankItem">確定</el-button>
            </div>
        </el-dialog>
        <branch ref="branch"></branch>
    </div>
</template>

<script>
    import branch from './components/branch.vue'
    export default {
        name: 'list-bank',
        data () {
            return {
                bankLoading: false,
                formInline: {
                    bankName: '',
                    bankNo: '',
                },
                bankPage: 1,
                bankTotal: 0,
                bankTableData: [],
                bankShow:false, //显示新增弹框
                isbankEdit:false,
                rules:{
                    bankName:[{ required: true, message: '请输入Bank Name', trigger: 'blur' }],
                    bankNo:[{ required: true, message: '请输入Bank No', trigger: 'blur' }],
                },
                oldbankTableItem:{},
                bankTableItem: {},
                isBranchEdit:false,
                show:false,
                emptyText: '暫無數據',
                bankData:{},
                branchSearch:{},
            }
        },
        activated(){
            this.getBankList()
        },
        components:{
            branch
        },
        methods: {
            getBranchTableData(row){
                this.bankData=row
                this.$nextTick(()=>{
                    this.$refs.branch.getBranch(row.bankNo)
                })
            },
            handleRowClass(row, index) {
              if (row.row == this.bankData) {
                return 'current'
              } else {
                return 'no';
              }
            },
            addFn(){
                this.bankShow = true
                this.isbankEdit = false
                this.bankTableItem = {
                    bankNo: '',
                    bankName: ''
                }
                if(this.$refs.bankForm){
                    this.$refs.bankForm.resetFields()
                }
            },
            
            searchList(){
                this.getBankList();
            },
            clearSearch(){
                this.formInline= {
                    bankName: '',
                    bankNo: '',
                };
                this.getBankList();
            },
            bankCurrentChange (val) {
                this.getBankList(val)
            },
            bankEdit(row){
                this.bankShow = true;
                this.isbankEdit = true;
                this.bankTableItem = this.$utils.copy(row);
                this.oldbankTableItem = this.$utils.copy(row);
            },
            getBankList(page){
                let params = this.$utils.copy(this.formInline);
                params.page_num = page ? page : 1;
                params.page_size = 10;
                
                this.bankLoading = true;
                this.emptyText = '數據加載中';
                this.$http.get('/ref/bank/page',{params}).then(res => {
                    this.bankLoading = false;
                    if(res.success){
                        if (res.data.code == 200) {
                            this.bankTableData = res.data.data.list || [];
                            this.bankTotal = res.data.data.total
                            this.bankPage =  params.page_num;
                        }else{
                            this.bankTableData=[]
                            this.bankTotal=1
                            this.bankPage=1
                            this.$toast({ tips: res.data.message });
                        }
                        if(! this.bankTableData || this.bankTableData.length == 0){
                            this.emptyText = '暫無數據';
                        }
                    }
                })
            },
            bankCancelDialog(){
                this.bankShow = false
                this.$refs.bankForm.resetFields()
            },
            addBankItem(){
                this.$refs.bankForm.validate(validate => {
                    if(validate){
                        if(this.isbankEdit){
                            if((this.oldbankTableItem.bankNo != this.bankTableItem.bankNo)){
                                let params={
                                    oldBankNo: this.oldbankTableItem.bankNo
                                }
                                this.bankLoading = true;
                                this.$http.get('/ref/bank/checkIfHavingBranch',{params}).then(res => {
                                    if(res.success){
                                        if(res.data.code == 200){
                                            this.bankLoading = false;
                                            if(res.data.data){
                                                this.$msgbox.confirm('下屬分行bank no都會同步更新，確定修改嗎?', '提示', {
                                                    confirmButtonText: '確定',
                                                    cancelButtonText: '取消',
                                                    type: 'warning',
                                                    closeOnClickModal: false
                                                }).then(() => {
                                                    this.addBankItemFn()
                                                }).catch(() => {        
                                                });
                                            }
                                        }
                                    }
                                })
                            }else{
                                this.addBankItemFn()
                            }
                        }else{
                            this.addBankItemFn()
                        }
                    }
                })
            },
            addBankItemFn(){
                this.$http.post('/ref/bank/edit',this.bankTableItem).then(res => {
                    if(res.success){
                        this.$message({
                            message:res.data.message,
                            type:res.data.code === 200?'success':'warning'
                        })
                        if(res.data.code === 200){
                            this.bankCancelDialog()
                            this.getBankList()
                        }
                    }
                })
                
            },
            reset(){
                
            },
            bankDelete(row, index){
                let params={
                    oldBankNo: row.bankNo
                }
                this.bankLoading = true;
                this.$http.get('/ref/bank/checkIfHavingBranch',{params}).then(res => {
                    if(res.success){
                        if(res.data.code == 200){
                            this.bankLoading = false;
                            if(res.data.data){
                                this.$msgbox.confirm('刪除bank信息後，所有下屬分行都會被刪除，確定删除嗎?', '提示', {
                                    confirmButtonText: '確定',
                                    cancelButtonText: '取消',
                                    type: 'warning',
                                    closeOnClickModal: false
                                }).then(() => {
                                    this.bankDeleteFn(row, index);
                                }).catch(() => {        
                                });
                            }else{
                                this.$msgbox.confirm('確定刪除?', '提示', {
                                    confirmButtonText: '確定',
                                    cancelButtonText: '取消',
                                    type: 'warning',
                                    closeOnClickModal: false
                                }).then(() => {
                                    this.bankDeleteFn(row, index);
                                }).catch(() => {        
                                });
                            }
                        }
                    }
                })
            },
            bankDeleteFn(row, index){
                this.$http.delete('/ref/bank/'+ row.id).then( res => {
                    if(res.success){
                        this.getBankList(this.bankPage)
                        // this.bankTableData.splice(index, 1);
                        this.$toast({tips: '刪除成功'})
                    }    
                })
            },
            
            
        }
    }
</script>

<style lang="scss" scoped>
    /deep/ .el-table .cell{
        text-align: center;
    }
    .option span{
        display: inline-block;
        padding: 0 5px;

    }
    .option span:nth-child(1){
        border-right: 1px solid #ccc;
    }
    /deep/ .el-form-item__label {
        margin-left: 0 !important;
    }
    .selectButton {
        text-align: center;
        button {
            width: 200px;
            height: 50px;
        }
    }
    .selectButton:nth-of-type(2) {
        margin-top: 20px;
    }
    /deep/ tr.current > td {
        background-color: #17b3a3 !important;
    }
    /deep/ tr.current > td .option .el-button--text span {
        color: #fff !important;
    }
</style>
