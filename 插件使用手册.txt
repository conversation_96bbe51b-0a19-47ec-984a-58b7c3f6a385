
bigNumber 使用方法
    let t1 = new BigNumber(0.1);
    let t2 = new BigNumber(0.2);
    t1 + t2  => t1.plus(t2)
    t1 - t2  => t1.minus(t2)
    t1 / t2  => t1.dividedBy(t2)
    t1 * t2  => t1.multipliedBy(t2)
    保留小数 .toFixed(n)



this.$bus.$emit('closeCurrentTab', () => { this.$router.push({name: 'member-ipi', query: {update: true}})});

/*
    日期格式化 添加属性 v-dateFmt
    双击查询的  placeholder="雙擊查詢"， css会依据这个属性找到并修改样式
*/
<el-date-picker v-model="" type="date" format="yyyyMMdd"></el-date-picker>

/*
标题不同的相同页面
保证 nameId 唯一，title是显示的title的名字
*/
this.$router.push({ name: 'member-info',query: {nameId:item.ip_base_no + item.ip_name_no, title: item.chinese_name}})

全局设置表格内容一行显示，最后一列不做操作，如果有数据在最后一列需要手动设置。
