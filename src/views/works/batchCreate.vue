<template>
    <div class="originalbox">
        <top-bar :noShow="false" :steps="steps" @save="saveWrk"></top-bar>
        <el-collapse v-model="activeNames" class="contentbox">
            <el-collapse-item class="step-jump" title="Batch create" name="1">
                <el-form :inline="true" :model="formShare" class="p-t-10" style="padding-left: 20px;">
                    <el-form-item>
                        <el-checkbox v-model="copyArtist">Copy Artist</el-checkbox>
                    </el-form-item>
                </el-form>
                <div class="boxline">
                    <el-form ref="tableForm" :model="tableForm">
                        <el-table
                            :data="tableForm.batchCreateData"
                            border
                            style="width: 100%"
                            stripe>
                            <el-table-column
                                label="Soc"
                                width="90">
                                <template slot-scope="scope">
                                    <el-form-item>
                                        <el-input v-model=scope.row.socId readonly></el-input>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="title"
                                label="Name">
                                <template slot-scope="scope">
                                    <el-form-item :prop="`batchCreateData.${scope.$index}.title`" :rules="rules.title">
                                        <el-input v-model=scope.row.title></el-input>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="Title Language">
                                <template slot-scope="scope">
                                    <el-form-item>
                                        <el-input v-model=scope.row.titleLanguage placeholder="雙擊查詢" @dblclick.native="languageInit('title', scope.$index)"></el-input>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="Genre">
                                <template slot-scope="scope">
                                    <el-form-item>
                                        <el-input v-model=scope.row.genre placeholder="雙擊查詢" @dblclick.native="genreInit(scope.$index)"></el-input>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="Perform Language">
                                <template slot-scope="scope">
                                    <el-form-item>
                                        <el-input v-model=scope.row.performLanguage placeholder="雙擊查詢" @dblclick.native="languageInit('pre', scope.$index)"></el-input>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="Duration"
                                label="Duration"
                                width="190">
                                <template slot-scope="scope">
                                    <el-form-item :prop="`batchCreateData.${scope.$index}.durationM`" :rules="rules.durationM" style="float: left;">
                                        <el-input style="width: 70px" v-model.number="scope.row.durationM" placeholder=""></el-input>:
                                    </el-form-item>
                                    <el-form-item style="float: left;" :prop="`batchCreateData.${scope.$index}.durationS`" :rules="rules.durationS"><el-input style="width: 70px"  v-model.number="scope.row.durationS" placeholder=""></el-input>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="operation"
                                width="100">
                                <template slot-scope="scope">
                                    <span class="spanright">
                                        <i class="el-icon-delete" @click="deleteWork(scope.$index)"></i>
                                    </span>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-form>
                </div>
                <div class="add-new">
                    <el-button type="primary" @click="addWork()">新 增</el-button>
                </div>
            </el-collapse-item>
            <el-collapse-item class="step-jump" title="works" name="2">
                <div class="boxline p-t-10">
                    <el-form :inline="true" :model="formOriginal" ref="formOriginal" label-width="70px" label-position="left" class="demo-form-inline p-t-10" disabled>
                        <div>
                            <el-form-item label="Work No" class="w-100">
                                <el-input v-model="formOriginal.workId"></el-input>
                            </el-form-item>
                            <el-form-item label="Soc">
                                <el-input v-model="formOriginal.workSocietyCode"></el-input>
                            </el-form-item>
                            <el-form-item label="Title">
                                <el-input v-model="formOriginal.title"></el-input>
                            </el-form-item>
                        </div>
                        <div>
                            <el-form-item label="To Work No" prop="refWorkId" class="w-100">
                                <el-input v-model="formOriginal.refWorkId"></el-input>
                            </el-form-item>
                            <el-form-item label="To Soc" prop="refWorkSociety">
                                <el-input v-model="formOriginal.refWorkSociety"></el-input>
                            </el-form-item>
                            <el-form-item label="To Title" >
                                <el-input v-model="formOriginal.totitle"></el-input>
                            </el-form-item>
                        </div>
                        <div>
                            <el-form-item label="Local">
                                <el-checkbox v-model="localchecked">Local</el-checkbox>
                            </el-form-item>
                            <el-form-item label="Genre">
                                <el-input v-model="formOriginal.genre"></el-input>
                            </el-form-item>
                            <el-form-item label="Duration" prop="durationM" required label-width="90px">
                                <el-input style="width: 80px" v-model.number="formOriginal.durationM" ></el-input>
                            </el-form-item>
                            :
                            <el-form-item style="margin-left: 5px" prop="durationS" required>
                                <el-input style="width: 80px" v-model.number="formOriginal.durationS" ></el-input>
                            </el-form-item>
                        </div>
                    </el-form>
                    <el-form :inline="true" label-position="left" class="demo-form-inline" disabled>
                        <el-form-item label="PerLanguage" class="w-100">
                            <el-input v-model="formOriginal.performLanguage"></el-input>
                        </el-form-item>
                        <el-form-item label="PublishDate" >
                            <!-- <el-input v-model="formOriginal.publishAirDate" v-dateFmt></el-input> -->
                            <date-picker v-model="formOriginal.publishAirDate" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd"></date-picker>
                        </el-form-item>
                        <el-form-item label="ISWC">
                            <el-input v-model="formOriginal.iswc" ></el-input>
                        </el-form-item>
                    </el-form>
                    <el-form :inline="true" label-position="left" class="demo-form-inline" disabled>
                        <el-form-item label="Artist">
                            <el-input v-model="Artist" style="width: 800px;"></el-input>
                        </el-form-item>
                    </el-form>
                </div>
            </el-collapse-item>
            <!--IP Share-->
            <el-collapse-item class="step-jump" title="IP Share" name="3">
                <el-form :inline="true" :model="formShare" class="demo-form-inline p-t-10">
                    <el-form-item label="Type">
                        <el-select v-model="formShare.rightType" placeholder="">
                            <el-option label="PERFORMING RIGHT" value="PER"></el-option>
                            <el-option label="MECHANICAL RIGHT" value="MEC"></el-option>
                            <el-option label="SYNCHRONIZATION RIGHT" value="ZYN"></el-option>
                            <el-option label="OD RIGHT" value="NOD"></el-option>
                            <el-option label="DB RIGHT" value="NDB"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-checkbox v-model="dischecked">Distributable</el-checkbox>
                    </el-form-item>
                    <el-form-item label="Share Type">
                        <el-select v-model="formShare.autoCalc" placeholder="">
                            <el-option label="Manual" value="manual"></el-option>
                            <el-option label="Auto" value="auto"></el-option>
                        </el-select>
                    </el-form-item>
                </el-form>
                <div class="boxline">
                    <el-table
                        :data="ipShareData[this.formShare.rightType]"
                        border
                        stripe
                        show-summary
                        :summary-method="getSummaries"
                        style="width: 100%">
                        <el-table-column
                            prop="groupIndicator"
                            label="Gp">
                            <template slot-scope="scope">
                                <el-input v-model=scope.row.groupIndicator readonly></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="dummyName"
                            label="Name">
                            <template slot-scope="scope">
                                <el-input v-model=scope.row.dummyName readonly></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="ipNameNo"
                            label="Ip Name No">
                            <template slot-scope="scope">
                                <el-input v-model=scope.row.ipNameNo readonly></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="workIpRole"
                            label="Role">
                            <template slot-scope="scope">
                                <el-input v-model=scope.row.workIpRole readonly></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="ipSocietyCode"
                            label="Soc">
                            <template slot-scope="scope">
                                <el-input v-model=scope.row.ipSocietyCode readonly></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="ipShare"
                            label="Ip Share">
                            <template slot-scope="scope">
                                <el-input v-model=scope.row.ipShare readonly></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="sdcheck"
                            label="Sd"
                            width="80px">
                            <template slot-scope="scope">
                                <el-checkbox :value="scope.row.sd == 'Y'" readonly></el-checkbox>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </el-collapse-item>
        </el-collapse>
        <el-dialog :visible.sync="genreShow" :close-on-click-modal="false" width="750px">
            <div style="width: 500px;margin: auto;margin-bottom: 20px">
                <el-input placeholder="請輸入曲風" @keyup.enter.native="workSearch" v-model="workInput" class="input-with-select">
                    <el-button slot="append" icon="el-icon-search" @click="workSearch"></el-button>
                </el-input>
            </div>
            <el-table :empty-text="tableresult"   stripe :data="genreData">
                <el-table-column property="genreDetail" label="曲風詳情" width="120"></el-table-column>
                <el-table-column property="workType" label="作品類型" width="120"></el-table-column>
                <el-table-column property="usageFlag" label="使用標識" width="120"></el-table-column>
                <el-table-column property="description" label="描述"></el-table-column>
                <el-table-column
                    label="operation"
                    width="100">
                    <template slot-scope="scope">
                        <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                            <i class="el-icon-check" @click="checkedGenre(scope.$index,scope.row)"></i>
                        </span>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                background
                layout="prev, pager, next"
                :total=genreTotal    @current-change="genreHandleCurrentChange">
            </el-pagination>
        </el-dialog>
        <el-dialog :visible.sync="languageShow" :close-on-click-modal="false">
            <div style="width: 500px;margin: auto;margin-bottom: 20px">
                <el-input placeholder="請輸入語言编碼" @keyup.enter.native="languageSearch" v-model="languageCodeInput" class="input-with-select">
                    <el-button slot="append" icon="el-icon-search" @click="languageSearch"></el-button>
                </el-input>
            </div>
            <el-table :empty-text="tableresult"   stripe :data="languageData">
                <el-table-column property="languageCode" label="語言编碼"    ></el-table-column>
                <el-table-column property="description" label="描述" ></el-table-column>
                <el-table-column
                    label="operation"
                    width="100">
                    <template slot-scope="scope">
                        <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                            <i class="el-icon-check" @click="checkedLanguage(scope.$index,scope.row)"></i>
                        </span>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                background
                layout="prev, pager, next"
                :total=languageTotal @current-change="languageHandleCurrentChange">
            </el-pagination>
        </el-dialog>

    </div>
</template>

<script>
    import axios from '../../utils/httpRequest'
    import qs from 'qs'

    export default {
        name: 'batchCreate',
        data () {
            return {
                workId: '',
                sodId: '',
                Artist: '',
                // ipshare的數據
                ipShareData: {},
                rschecked: '',
                steps: [
                    {
                        name: 'works'
                    }, {
                        name: 'Performer'
                    }, {
                        name: 'IP Share'
                    }
                ],
                title: '',
                stepshow: true,
                dischecked: true,
                genreShow: false,
                languageShow: false,
                titleLanguageShow: true,
                // 当前編輯的語言數據的index
                languageIndex: 0,
                genreIndex: 0,

                workInput: '',
                languageCodeInput: '',
                languageTotal: 1,
                genreTotal: 1,
                pageInfo: {
                    page: {
                        pageNum: 0,
                        pageSize: 10
                    }
                },
                genreData: [],tableresult:' ',
                languageData: [],
                worknoTableData: [],
                RSchecked: false,
                toWorkShow: false,
                checked: true,
                localchecked: false,
                activeNames: ['1', '2', '3'],
                formOriginal: {
                    id: '',
                    title: '',
                    local: '',
                    genre: '',
                    durationM: '',
                    durationS: '',
                    performLanguage: '',
                    publishAirDate: '',
                    iswc: '',
                    // 根據route query 的type区分
                    workType: 'ARR',
                    toid: '',
                    totitle: ''
                },
                formShare: {
                    rightType: 'PER',
                    autoCalc: 'manual'
                },
                tableForm: {
                    batchCreateData: []
                },
                copyArtist: false,

                rules: {
                    title: [{required: true, message: '請填寫Work Name', trigger: 'blur'}],
                    durationM: [{required: true, message: '請填寫時長', trigger: 'blur'}],
                    durationS: [{required: true, message: '請填寫時長', trigger: 'blur'}]
                }
            }
        },
        mounted () {
            this.workId = this.$route.query.id;
            this.socId = this.$route.query.socId;
            this.getworklistinfo();
        },
        methods: {
            addWork(){
                this.tableForm.batchCreateData.push({socId: '161', title: '', titleLanguage: '', genre: '', performLanguage: '', durationM: '', durationS: '', 
                            workType: this.$route.query.type, refWorkId: this.formOriginal.refWorkId, 
                            refWorkSociety: this.formOriginal.refWorkSociety, toTitle: this.formOriginal.totitle});
            },
            deleteWork(index){
                this.tableForm.batchCreateData.splice(index, 1);
            },
            getSummaries (param) {
                const { columns, data } = param
                const sums = []
                columns.forEach((column, index) => {
                    if (index === 0) {
                        sums[index] = '總計'
                    } else if (index === 5) {
                        const values = data.map(item => Number(item[column.property]))
                        if (!values.every(value => isNaN(value))) {
                            sums[index] = values.reduce((prev, curr) => {
                                const value = Number(curr)
                                if (!isNaN(value)) {
                                    return prev + curr
                                } else {
                                    return prev
                                }
                            }, 0)
                        } else {
                            sums[index] = 'N/A'
                        }
                    } else {
                        sums[index] = '--'
                    }
                })
                return sums
            },
            saveWrk(){
                this.$refs.tableForm.validate( (validate) => {
                    if(validate){
                        this.saveWrkFn();
                    }
                    
                })
            },
            saveWrkFn () {
                if(!this.validateData(this.tableForm.batchCreateData)){
                    return;
                }

                let ajaxData = {
                    copyArtist: this.copyArtist,
                    workId: this.workId,
                    workSocietyCode: this.socId,
                    list: this.$utils.copy(this.tableForm.batchCreateData)
                };
                ajaxData.list.forEach( item => {
                    delete item.socId
                })
                this.$http.post('/wrk/batchSaveWrkWork', ajaxData).then( res => {
                    if(res.success){
                        this.$toast({tips: '批量創建成功'})
                        // this.$router.push({name: 'works-list'});
                        this.$bus.$emit('closeCurrentTab', () => {this.$router.push({name: 'works-list', query: {update: true}})});
                    }
                })
            },
            validateData(data){
                if(data.length == 0){
                    this.$toast({tips: '請至少添加一條作品'})
                    return false;
                }
                let flag = true;
                var checkDurationM = (value) => {
                    if (!Number.isInteger(value)) {
                        callback(new Error('請輸入數字'))
                    } else {
                        if (value < 0 || value > 1000) {
                            callback(new Error('不能大於1000min或者小於0min！'))
                        } else {
                            callback()
                        }
                    }
                }
                var checkDurationS = (rule, value, callback) => {
                    if (!Number.isInteger(value)) {
                        callback(new Error('請輸入數字'))
                    } else {
                        if (value < 0 || value > 60) {
                            callback(new Error('不能大於60s或者小於0s！'))
                        } else {
                            callback()
                        }
                    }
                }
               
                for(let i=0;i<data.length;i++){
                    // if(!data[i].title){
                    //     this.$toast({tips: '新建作品的名稱必須填寫'})
                    //     flag = false;
                    //     break;
                    // }else{
                        // if(!Number.isInteger(data[i].durationM) || !Number.isInteger(data[i].durationS) ){
                        //     this.$toast({tips: '新建作品的時長必須填寫'})
                        //     flag = false;
                        //     break;
                        // }else{
                            if(data[i].durationM > 1000 || data[i].durationM < 0){
                                this.$toast({tips: '新建作品的時長分鐘數不能大於1000min或者小於0min'})
                                flag = false;
                                break;
                            }
                            if(data[i].durationS < 0 || data[i].durationS > 60){
                                this.$toast({tips: '新建作品的時長秒數不能大於60s或者小於0s'})
                                flag = false;
                                break;
                            }
                        // }
                    // }
                }
                return flag;

            },
            genreInit (index) {
                this.genreIndex = index;
                this.genreShow = true
                    this.tableresult = '數據加載中...'
                axios.post('/ref/getRefGenreDtlByDetail', this.pageInfo).then(res => {
                    if (res.status === 200) {
                        this.genreData = res.data.list
                        this.genreTotal = res.data.total
                    }
                        this.tableresult = '數據加載中...'
                          this.tableresult = this.genreData.length == 0 ? '暫無數據' : ' '
                })
            },
            checkedGenre (index, item) {
                this.tableForm.batchCreateData[this.genreIndex].genre = item.genreDetail
                this.genreShow = false
            },
            workSearch () {
                this.pageInfo.genreDetail = this.workInput
                this.tableresult = '數據加載中...'
                axios.post('/ref/getRefGenreDtlByDetail', this.pageInfo).then(res => {
                    if (res.status === 200) {
                        this.genreData = res.data.list
                        this.genreTotal = res.data.total
                    }
                        this.tableresult = this.genreData.length == 0 ? '暫無數據' : ' '
                })
            },
            languageInit (str, index) {
                this.languageIndex = index;
                this.languageShow = true
                if (str === 'title') {
                    this.titleLanguageShow = true
                } else {
                    this.titleLanguageShow = false
                }
                      this.tableresult = '數據加載中...'
                axios.post('/ref/languageByCode', this.pageInfo).then(res => {
                    if (res.status === 200) {
                        this.languageData = res.data.list
                        this.languageTotal = res.data.total
                    }
                        // this.tableresult = '數據加載中...'
                          this.tableresult = this.languageData.length == 0 ? '暫無數據' : ' '
                })
            },
            languageSearch () {
                this.pageInfo.languageCode = this.languageCodeInput
                this.tableresult = '數據加載中...'
                axios.post('/ref/languageByCode', this.pageInfo).then(res => {
                    if (res.status === 200) {
                        this.languageData = res.data.list
                        this.languageTotal = res.data.total
                    }
                        this.tableresult = this.languageData.length == 0 ? '暫無數據' : ' '
                })
            },
            checkedLanguage (index, item) {
                if (this.titleLanguageShow) {
                    this.tableForm.batchCreateData[this.languageIndex].titleLanguage = item.languageCode
                } else {
                    this.tableForm.batchCreateData[this.languageIndex].performLanguage = item.languageCode
                }
                this.languageShow = false;
            },
            genreHandleCurrentChange (val) {
                this.pageInfo.page.pageNum = val
                this.pageInfo.genreDetail = this.workInput
                      this.tableresult = '數據加載中...'
                axios.post('/ref/getRefGenreDtlByDetail', this.pageInfo).then(res => {
                    if (res.status === 200) {
                        this.genreData = res.data.list
                        this.genreTotal = res.data.total
                    }
                          this.tableresult = this.genreData.length == 0 ? '暫無數據' : ' '
                })
            },
            languageHandleCurrentChange (val) {
                this.pageInfo.page.pageNum = val
                this.pageInfo.languageCode = this.languageCodeInput
                this.tableresult = '數據加載中...'
                axios.post('/ref/languageByCode', this.pageInfo).then(res => {
                    if (res.status === 200) {
                        this.languageData = res.data.list
                        this.languageTotal = res.data.total
                    }
                        this.tableresult = this.languageData.length == 0 ? '暫無數據' : ' '
                })
            },
            getworklistinfo () {
                let params = {
                    workId: this.workId,
                    workSocietyCode: this.socId
                }
                axios.get('/wrk/getWrkWorkById', {params}).then(res => {
                    if (res.status === 200) {
                        this.Artist = '';
                        let artistArray = [];
                        res.data.waList.forEach((item, index) => {
                            let name = item.artistName;
                            if(item.artistName && item.chineseName){
                                name += '(' + item.chineseName + ')';
                            }else{
                                name += item.chineseName;
                            }
                            artistArray.push(name);
                        })
                        this.Artist = artistArray.join('/');
                        this.ipShareData = res.data.wwisList[0];
                        this.formOriginal = res.data.wrkWork;
                        if(this.formOriginal.local == 'L'){
                            this.localchecked = true;
                        }else{
                            this.localchecked = false;
                        }
                        this.formOriginal.workType = this.$route.query.type;

                        if(this.$route.query.type == 'ADP'){
                            this.formOriginal.refWorkId = this.formOriginal.workId;
                            this.formOriginal.workId = '';

                            this.formOriginal.refWorkSociety = this.formOriginal.workSocietyCode;
                            this.formOriginal.workSocietyCode = '';

                            this.formOriginal.totitle = this.formOriginal.title;
                            this.formOriginal.title = '';
                        }
                        
                    }
                })
            },
        }
    }
</script>

<style lang="scss" scoped>
    @import "../../assets/scss/works.scss";
    .el-icon-delete{
        cursor: pointer;
    }
    /deep/ .w-100 label{
        width: 100px !important;
        font-size: 14px;
    }
    /deep/ .el-input.is-disabled .el-input__inner, /deep/ .el-checkbox__input.is-disabled+span.el-checkbox__label{
        color: #000;
    }
</style>
