<template>
    <div>
        <el-dialog :visible.sync="showD"  width="1000px">
            <el-table :data="resultData" >
                <el-table-column property="ip_base_no" label="ipBaseNo"></el-table-column>
                <el-table-column property="name" label="name"></el-table-column>
                <el-table-column property="ip_name_no" label="ipNameNo"></el-table-column>
                <el-table-column property="name_type" label="nameType"></el-table-column>
                <el-table-column property="chinese_name" label="chineseName"></el-table-column>
                <el-table-column property="society_code" label="Soc"></el-table-column>
                <el-table-column
                    label="operation"
                    width="100">
                    <template slot-scope="scope">
                        <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                            <i class="el-icon-check" @click="checkedWriter(scope.$index,scope.row)"></i>
                        </span>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
            background
            layout="prev, pager, next"
            :total="total"  @current-change="handleCurrentChange">
        </el-pagination>
        </el-dialog>
    </div>
</template>
<script>

export default {
    props: {
        show: {
            type: Boolean,
            default: false
        },
        resultData: {
            type: Array,
            default: []
        },
        total: {
            type: Number,
            default: 1
        }
    },
    data(){
        return {
            writerInput: '',
            modules: {},
            writerGridData: [],
            showD: this.show
        }

    },
    watch:{
        showD(){
            // this.show = this.showD;
            this.$emit('updateShow', {name: 'showResult', value: this.showD})
        },
        show(){
            this.showD = this.show
        }
    },
    methods: {
        checkedWriter (val, item) {
            this.$emit('selectIp', item);
            this.showD = false
        },
        handleCurrentChange(val){
            this.$emit('searchNextPage', val)
        }
    }
}
</script>
<style>
</style>


