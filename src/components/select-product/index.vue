<template>
<div>
    <el-dialog :visible.sync="show" width="1000px" title="Producer" :close-on-click-modal="false">
        <!-- <div style="width: 600px;margin: auto;margin-bottom: 20px">
            <el-input v-model="searchInfo.name" placeholder="Name" style="width: 160px;"></el-input>
            <el-input v-model="searchInfo.ip_no" placeholder="IP Base No" style="width: 140px;"></el-input>
            <el-input v-model="searchInfo.name_no" placeholder="IP Name No" style="width: 140px;"></el-input>
            <el-input v-model="searchInfo.soc" placeholder="soc" style="width: 60px;"></el-input>
            <el-button type="primary" @click="onSubmit()">查詢</el-button>
        </div> -->
        <el-table :empty-text="emptyText" :data="tableData">
            <el-table-column property="id" label="产品編碼"></el-table-column>
            <el-table-column property="producerName" label="产品名稱"></el-table-column>
            <el-table-column property="companyName" label="公司名稱"></el-table-column>
            <el-table-column property="label" label="Label"></el-table-column>
            <el-table-column
                label="operation">
                <template slot-scope="scope">
                    <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                        <i class="el-icon-check" @click="checkedProduct(scope.$index,scope.row)"></i>
                    </span>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :total="total" @current-change="onSubmit" :current-page="currentPage">
        </el-pagination>
    </el-dialog>
</div>
</template>
<script>
import qs from 'qs'

export default {
    data(){
        return{
            show:false,
            searchInfo: {},
            tableData: [],
            total: 0,
            currentPage: 1,
            emptyText: '暫無數據',
        }
    },
    props: {
        search: {
            type: Object,
            default: () => {
                return {
                    // name_no: '',
                    // name: '',
                    // soc: '',
                    // ip_no: '', //ipBaseNo
                }

            }
        }
    },

    methods: {
        /**
         * dialog 组件调用方式： 
         * 1、父级用v-if在html里加载组件,
         * 2、父级调用子级的init方法，将数据初始化，prop传给data等
         * 
         */
        init(){
            this.show = true;
            this.searchInfo = this.$utils.copy(this.search);
            this.onSubmit();
        },
        onSubmit(page){
            let ajaxData = this.searchInfo;
            this.$utils.trim(ajaxData);
            ajaxData.page_num = page ? page : 1;
            ajaxData.page_size = 10;
            this.emptyText = '數據加載中';
            this.$http.get('/dist/mrd/listDistDistMrdProducerWithPage', {params: ajaxData}).then( res => {
                if(res.success){
                    this.tableData = res.data.data.list;
                    if(! this.tableData || this.tableData.length == 0){
                        this.emptyText = '暫無數據';
                    }
                    this.total = res.data.data.total;
                    this.currentPage = page ? page : 1;
                }
            })
        },
        checkedProduct(index, row){
            this.$emit('checkProduct', row);
            this.show = false;
        }
    }
    
}
</script>