<template>
  <div>
    <el-form :model="formData" class="form clear" label-position="left" label-width="150px" ref="form" :rules="rules">
      <el-form-item label="Name" class="w-92" style="width:500px">
        <el-input type="text" v-model="formData.name"></el-input>
      </el-form-item>
      <el-form-item label="Year" prop="year" class="w-92" style="width:500px">
        <el-input v-model.number="formData.year" @input="onYearChange"></el-input>
      </el-form-item>
      <el-form-item label="Category Code" prop="categoryCode" style="width:500px">
        <el-input type="text" v-model="formData.categoryCode" ref="category" placeholder="雙擊選擇" @dblclick.native="addCategory"></el-input>
      </el-form-item>
      <el-form-item label="使用日期範圍" class="w-92" required>
        <el-form-item prop="startDate" style="width:181px;display: inline-block">
          <el-input v-model="formData.startDate" readonly style="width:200px" placeholder="start"></el-input>
        </el-form-item>
        <span>-</span>
        <el-form-item prop="endDate" style="width:181px;display: inline-block">
          <el-input v-model="formData.endDate" readonly style="width:200px" placeholder="end"></el-input>
        </el-form-item>

      </el-form-item>
      <el-form-item label="Frequency" class="w-92" required>
        <el-form-item prop="dayExtracted" style="width:100px;display: inline-block">
          <el-input type="number" v-model.number="formData.dayExtracted" class="w-100"></el-input>
        </el-form-item>
        <span>/</span>
        <el-form-item prop="dayBase" style="width:100px;display: inline-block">
          <el-input type="number" v-model.number="formData.dayBase" class="w-100"></el-input>
        </el-form-item>
        days
      </el-form-item>
    </el-form>
    <div style="padding-left: 25%;">
      <el-button type="primary" @click="saveData">生成</el-button>
    </div>
    <el-dialog :visible.sync="moneyTableShow" :close-on-click-modal="false">
      <div style="width: 500px;margin: auto;">
        <el-form :inline="true" :model="addSearch" @keyup.enter.native='getCategoryList()'>
          <el-form-item label="">
            <el-input placeholder="請輸入Source Name" v-model="addSearch.sourceName" class="input-with-select">
              <el-button slot="append" icon="el-icon-search" @click="getCategoryList()"></el-button>
            </el-input>
          </el-form-item>
        </el-form>
      </div>
      <el-table :empty-text="tableresult" stripe :data="moneyTableData">
        <el-table-column property="id" label="ID"></el-table-column>
        <el-table-column property="sourceName" label="sourceName"></el-table-column>
        <el-table-column property="categoryCode" label="categoryCode"></el-table-column>
        <el-table-column property="categoryDesc" label="categoryDesc"></el-table-column>
        <el-table-column label="operation" width="120px">
          <template slot-scope="scope">
            <i class="el-icon-check pointer" @click="chooseCategory(scope.row)"></i>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background layout="prev, pager, next" :total=moneyTotal @current-change="getCategoryList">
      </el-pagination>
    </el-dialog>
  </div>
</template>

<script>
import Calendar from 'vue-calendar-component';
export default {

  // function IsDate(rule, str, callback) {
  //   var reg = /19\d{2}|2\d{3}/;
  //   // var arr = reg.exec(str);
  //   //  if (str=="") return true;
  //   if (!reg.test(str)) {
  //     //  alert("请保证输入的日期格式为2019-01-01类型的正确日期!");
  //     //  callback(new Error('不能大於1000min或者小於0min！'))
  //     //  return false;
  //     callback(new Error('请保证输入的日期格式为2019-01-01类型的正确日期!'))

  //   } else {

  //     callback()
  //   }
  // }


  data() {
    return {
      formData: {},
      markDate: [],
      moneyTableShow: false,
      addSearch: {},
      moneyTableData: [], tableresult: ' ',
      moneyTotal: 0,
      targetYear: '',
      rules: {
        name: [
          { required: true, message: '请输入name', trigger: 'blur' }
        ],
        year: [
          // { required: true, message: '请输入合法年份', trigger: 'blur',pattern:/^[1-2]\d{3}/ }
          { required: true, message: '请输入合法年份', trigger: 'blur', pattern: /^(19|20)\d{2}$/ }
        ],
        categoryCode: [
          { required: true, message: '请输入categoryCode', trigger: 'blur' }
        ],
        startDate: [
          //{ required: true, message: '請輸入日期（例：20200101）', trigger: 'blur' }
            { required: true, message: '日期默認僅只讀，根據輸入的年份自動填寫', trigger: 'blur' }
        ],
        endDate: [
          //{ required: true, message: '請輸入日期（例：20200101）', trigger: 'blur' }
            { required: true, message: '日期默認僅只讀，根據輸入的年份自動填寫', trigger: 'blur' }
        ],
        dayExtracted: [
          { type: 'number', required: true, message: '请输入合法天数', trigger: 'blur', min: 1 }
        ],
        dayBase: [
          { type: 'number', required: true, message: '请输入31以內的整數', trigger: 'blur', max: 31 }
        ]
      }
    }
  },
  components: {
    Calendar
  },
  watch: {
    'formData.year': {
      handler(newYear) {
        if (newYear) {
          this.updateDateRange()
        }
      },
      immediate: false
    }
  },
  mounted() {

  },
  methods: {
    // 年份变化时的处理
    onYearChange() {
      // 自动设置日期范围为年初到年末
      this.updateDateRange()
    },

    // 更新日期范围为选中年份的年初到年末
    updateDateRange() {
      const year = this.formData.year
      if (year && year >= 1900 && year <= 2100) {
        this.formData.startDate = `${year}-01-01`
        this.formData.endDate = `${year}-12-31`
      }
    },

    formatTime(time) {
      let newTime = ''
      if (time.indexOf(' ') !== -1) {
        let arr = time.split(' ')
        newTime = arr[0]
      } else {
        newTime = time
      }
      return newTime
    },
    chooseDay(date) {
      console.log(date)
      let index = this.markDate.indexOf(date)
      if (index == -1) {
        this.markDate.push(date);
      } else {
        this.markDate.splice(index, 1);
      }
    },
    chooseMonth() {
      if (this.targetYear) {
        // 使用月中的日期（15号）避免自动选中1号
        const yearMonth = this.targetYear.substring(0, 7) // "2024-01-01" -> "2024-01"
        this.$refs.calendar.ChoseMonth(yearMonth + '-15');
      }
    },
    addCategory() {
      this.moneyTableShow = true;
      this.getCategoryList(1);
    },
    getCategoryList(page) {
      let data = this.$utils.copy(this.addSearch)
      data.page_size = 10
      data.page_num = page ? page : 1
      this.tableresult = '數據加載中...'
      this.$http.get('/list/categorys', { params: data }).then(res => {
        if (res.success) {
          this.moneyTableData = res.data.data.list
          this.moneyTotal = res.data.data.total
        }
        this.tableresult = this.moneyTableData.length == 0 ? '暫無數據' : ' '
      })
    },
    chooseCategory(row) {
      this.$set(this.formData, 'categoryCode', row.categoryCode)
      this.moneyTableShow = false
      this.$refs.category.focus()
      this.$refs.category.blur()
    },
    saveData() {
      if (this.formData.dayExtracted > this.formData.dayBase) {
        this.$toast({ tips: '抽樣天數不能大於總天數' })
        return
      }
      if (this.formData.startDate > this.formData.endDate) {
        this.$toast({ tips: '開始時間不能大於結束時間,請重新填寫!' })
        return
      }
      if (this.formData.startDate.split('-')[0] != this.formData.year || this.formData.endDate.split('-')[0] != this.formData.year) {
        this.$toast({ tips: '日期範圍和抽樣年份不一致!' })
        return
      }
      this.$refs.form.validate(validate => {
        if (validate) {
          this.$http.post('/list2SampleRule/saveOrUpdateListSampleRuleBase', this.formData).then(res => {
            console.log(res)
            if (res.success && res.data.indexOf('成功') !== -1) {
              this.$message({
                message: '保存成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.$bus.$emit('closeCurrentTab', () => {
                    this.$router.push({ name: 'normalListSampleDate', query: { update: true } });
                  })

                }
              })

            } else {
              this.$message.error(res.data)
            }
          })
        }
      })

    },
    getDateList(id) {
      this.$http.get('/listSampleRule/getListSampleRuleBaseById', { params: { baseId: id } }).then(res => {
        console.log(res)
        let list = res.data.listSampleRuleMappingList
        if (list.length) {
          list.map(item => {
            this.markDate.push(this.formatTime(item.sampleDate))
          })
          let date = list[0].sampleDate
          date = date.split(' ')[0]
          this.targetYear = date
          this.chooseMonth()
        } else {
          this.markDate = []

        }
      })
    }

  }
}
</script>
<style lang="scss" scoped>
@import "../../../../assets/scss/works.scss";
/deep/ .el-date-editor .el-range-separator {
  width: 6%;
}
.w-100 {
  width: 100px;
}
/deep/ .wh_content_all {
  // background: #eee;
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 6px;
}
/deep/ .wh_isToday {
  background: #fff !important;
}
/deep/ .wh_item_date:hover {
  background: #17b3a3 !important;
  border-radius: 50%;
}
/deep/ .wh_content_item,
/deep/ .wh_top_changge li {
  color: #333;
}
/deep/ .wh_jiantou2,
/deep/ .wh_jiantou1 {
  border-color: #333 !important;
}
/deep/ .wh_chose_day {
  background: #fff !important;
}
/* 选中的日期的颜色*/
/deep/ .wh_content_item > .wh_isMark {
  background: #71c7a5 !important;
}

/* 除当月意外的日期隐藏 */
/deep/ .wh_other_dayhide {
  display: none;
}
.date-box {
  padding-bottom: 20px;
}

/deep/ .el-form-item__label {
  margin-left: 0;
}
.line {
  border-bottom: 0 none;
  text-align: center;
  margin-right: 13px;
}
</style>
