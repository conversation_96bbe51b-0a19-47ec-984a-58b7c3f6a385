# 自定义日期编辑功能说明

## 功能概述

在抽样规则编辑页面（`src/views/list-manage/normal-list/sample-date/edit.vue`）中新增了自定义日期编辑功能，包含以下组件：

### 1. 主要组件和接口

#### A组件（预览组件）
- **位置**: 页面顶部的"原有抽样规则日期预览"区域
- **功能**: 显示从后端获取的原有抽样规则日期，只读模式
- **特点**: 
  - 不可编辑，仅用于预览
  - 带有半透明遮罩层，防止用户误操作
  - 鼠标悬停时显示禁用状态

#### B组件（编辑组件）
- **位置**: 开启自定义编辑后显示的"自定义编辑日期"区域
- **功能**: 允许用户自定义选择和编辑日期
- **特点**:
  - 完全可编辑的日历组件
  - 支持从A组件复制日期
  - 提供清空和重新生成功能

#### 开关控制
- **组件**: `el-switch`
- **功能**: 控制B组件的显示/隐藏
- **状态**: 
  - 关闭：只显示A组件（预览模式）
  - 开启：显示A组件 + B组件（编辑模式）

### 2. 路由配置

无需额外路由配置，功能集成在现有的编辑页面中。

### 3. 数据传递

#### 3.1 数据流关系
```
后端API → A组件(markDate) → 复制操作 → B组件(customMarkDate) → 提交API
```

#### 3.2 参数传递
- `markDate`: A组件的日期数据（只读）
- `customMarkDate`: B组件的日期数据（可编辑）
- `enableCustomEdit`: 自定义编辑开关状态
- `targetYear`: A组件的目标年份
- `customTargetYear`: B组件的目标年份

### 4. 功能模块描述

#### 4.1 主要功能

1. **预览功能**
   - 显示原有抽样规则的日期选择
   - 只读模式，不允许修改
   - 提供日期跳转功能

2. **自定义编辑功能**
   - 开关控制显示/隐藏
   - 从预览复制日期到编辑组件
   - 自由选择和编辑日期
   - 清空选择功能

3. **重新生成功能**
   - 使用自定义选择的日期
   - 调用后端接口（预留位置）
   - 确认对话框防止误操作

#### 4.2 数据结构

```javascript
// 组件数据结构
data() {
  return {
    markDate: [],           // A组件的日期数据（只读预览）
    customMarkDate: [],     // B组件的日期数据（可编辑）
    enableCustomEdit: false, // 自定义编辑开关
    targetYear: '',         // A组件的目标年份
    customTargetYear: '',   // B组件的目标年份
    // ... 其他现有数据
  }
}
```

## 使用方法

1. **查看原有日期**: 页面加载后自动显示原有抽样规则的日期
2. **开启编辑**: 点击"开启自定义编辑"开关
3. **复制日期**: 点击"从预览复制日期"按钮将A组件的日期复制到B组件
4. **编辑日期**: 在B组件中自由选择或取消选择日期
5. **重新生成**: 点击"重新生成"按钮提交自定义日期

## 接口集成

在 `regenerateWithCustomDates()` 方法中预留了接口调用位置：

```javascript
// TODO: 调用后端接口提交自定义日期
const customDateData = {
  baseId: this.formData.id,
  customDates: this.customMarkDate,
  // 其他必要参数...
}

this.$http.post('/api/regenerateWithCustomDates', customDateData).then(res => {
  if (res.success) {
    this.$message.success('重新生成成功')
    // 可能需要刷新数据或跳转页面
  } else {
    this.$message.error(res.message || '重新生成失败')
  }
}).catch(err => {
  this.$message.error('请求失败：' + err.message)
})
```

## 样式说明

- A组件使用灰色背景，表示只读状态
- B组件使用蓝色背景，表示可编辑状态
- 开关区域使用浅蓝色背景突出显示
- 只读日历添加半透明遮罩和禁用鼠标样式

## 注意事项

1. 接口调用部分需要根据实际后端API进行调整
2. 日期格式统一使用 `YYYY-MM-DD` 格式
3. 自定义编辑功能默认关闭，需要用户主动开启
4. 重新生成操作会覆盖原有的抽样规则，请谨慎使用
