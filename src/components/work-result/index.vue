<template>
    <div>
        <el-dialog :visible.sync="showD" width="1000px">
            <el-table :empty-text="emptyText"   :data="resultData" >
                <el-table-column label="Title" width="260">
                    <template slot-scope="scope">
                        {{scope.row.title||scope.row.title_en}}
                    </template>
                </el-table-column>
                <el-table-column property="name" label="episode">
                    <template slot-scope="scope">
                        <span v-if="scope.row.episode_no==0">
                            -
                        </span>
                    <span v-else>
                        {{scope.row.episode_no}}
                    </span>
                    </template>
                </el-table-column>
                <el-table-column property="work_id" label="work no"></el-table-column>
                <el-table-column property="work_society_code" label="Soc" width="70"></el-table-column>
                <el-table-column property="work_type" label="workType"></el-table-column>
                <el-table-column property="genre_code" label="Genr"></el-table-column>
                <el-table-column
                    label="operation"
                    width="100">
                    <template slot-scope="scope">
                        <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                            <i class="el-icon-check" @click="checkedWriter(scope.$index,scope.row)"></i>
                        </span>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
            background
            layout="prev, pager, next"
            :total="total"  @current-change="handleCurrentChange">
        </el-pagination>
        </el-dialog>
    </div>
</template>
<script>

export default {
    props: {
        show: {
            type: Boolean,
            default: false
        },
        resultData: {
            type: Array,
            default: []
        },
        total: {
            type: Number,
            default: 1
        }
    },
    data(){
        return { 
            emptyText:'暫無數據',
            writerInput: '',
            modules: {},
            writerGridData: [],
            showD: this.show
        }

    },
    watch:{
        showD(){
            // this.show = this.showD;
            this.$emit('updateShow', {name: 'showResult', value: this.showD})
        },
        show(){
            this.showD = this.show
        }
    },
    methods: {
        checkedWriter (val, item) {
            this.$emit('selectWork', item);
            this.showD = false
        },
        handleCurrentChange(val){
            this.$emit('searchNextPage', val)
        }
    }
}
</script>
<style>
</style>


