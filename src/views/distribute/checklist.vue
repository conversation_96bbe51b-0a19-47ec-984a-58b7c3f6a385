<template>
    <div>
        <el-form :inline="true" :model="formData" class="demo-form-inline" @keyup.enter.native="searchList()">
            <el-form-item label="error type">
                <el-select v-model="formData.errorType" placeholder="Type" style="width:100px">
                    <el-option label="全部" value=""></el-option>
                    <el-option label="LST" value="LST"></el-option>
                    <el-option label="WRK" value="WRK"></el-option>
                    <el-option label="AGR" value="AGR"></el-option>
                    <el-option label="IPI" value="IPI"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="error code">
                <el-input :value="errorCode" :key="errorCode" placeholder="雙擊查詢" @dblclick.native="searchErrorCode()" readonly></el-input>
                <!-- <el-select v-model="formData.errorCode" placeholder="errorCode" style="width:150px">
                    <el-option label="全部" value=""></el-option>
                    <el-option v-for="item in errorCodes" :key="item" :label="item" :value="item"></el-option>
                </el-select> -->
                <!-- <el-autocomplete
                popper-class="my-autocomplete"
                v-model="formData.errorCode"
                :fetch-suggestions="querySearch"
                placeholder="请输入内容"
                @select="handleSelect">
                    <template slot-scope="{ item }">
                        <div class="name">{{ item.errCode }}</div>
                        <span class="addr">{{ item.errDesc }}</span>
                    </template>
                </el-autocomplete> -->
            </el-form-item>
            <el-form-item label="dist no">
                <el-input v-model.trim="formData.distNo" placeholder="" @blur="searchErrorCode()" style="width:150px"></el-input>
            </el-form-item>
            <el-form-item label="item soc">
                <el-input v-model.trim="formData.itemSoc" placeholder="" style="width:150px"></el-input>
            </el-form-item>
            <el-form-item label="check status">
                <el-select v-model="formData.checked" placeholder="Type" style="width:100px">
                    <el-option label="Yes" value="Y"></el-option>
                    <el-option label="No" value="N"></el-option>
                    <el-option label="Ignore" value="I"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="handleSearchButton">搜索</el-button>
            </el-form-item>
            <el-form-item>
                <span class="clear-search" @click="clearSearch()" v-if="isAuth('works:base:find')">清除搜索</span>
            </el-form-item>
        </el-form>
        <el-table
            :data="tableData"
            border
            stripe
            @row-dblclick="rowClick"
            style="width: 100%"
            :empty-text="emptyText">
            <el-table-column
                prop="distNo"
                label="Dist No"
                width="120px">
            </el-table-column>
            <el-table-column
                prop="errorType"
                label="Error Type"
                width="120px">
            </el-table-column>
            <el-table-column
                prop="errorCode"
                label="Error Code"
                width="120px">
            </el-table-column>
            <el-table-column
                prop="description"
                label="Error Description"
                width="160"
            >
                <template slot-scope="scope">
                    <span :title="scope.row.description">{{scope.row.description}}</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="errorContent"
                label="Error Content">
                <template slot-scope="scope">
                    <span :title="scope.row.errorContent">{{scope.row.errorContent}}</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="itemSoc"
                label="Soc"
                width="60px">
            </el-table-column>
            <el-table-column
                prop="checked"
                label="Status"
                width="80px">
            </el-table-column>
            <el-table-column
                label="Operation"
                width="160"
                >
                <template slot-scope="scope">
                    <el-button type="text" size="small" @click="check(scope.row.id,'Y')">check</el-button>
                    |
                    <el-button type="text" size="small" @click="check(scope.row.id,'I')">ignore</el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :total="total" @current-change="handleChange">
        </el-pagination>
        <el-dialog
        title="error code"
        :visible.sync="dialogVisible"
        width="60%"
        :close-on-click-modal="false">
            <el-table :empty-text="emptyText" :data="errorCodes" highlight-current-row @current-change="curChange">
                <el-table-column property="errCode" label="errCode" width="200px"></el-table-column>
                <el-table-column property="errDesc" label="errDesc" ></el-table-column>
                <el-table-column property="errType" label="errType" width="120px"></el-table-column>
            </el-table>
            <el-pagination
                background
                layout="prev, pager, next"
                :total="total1" @current-change="handleChange1">
            </el-pagination>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="changeErrCode">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
    export default {
        name: 'checklist',
        data(){
            return {
                formData:{
                    distNo:'',
                    errorType:'',
                    errorCode:'',
                    itemSoc:'',
                    checked:'N',
                    page:{
                        pageNum:1,
                        pageSize:10
                    }
                },
                errorCode:'',
                distNo:'',
                errorCodes:[],
                tableData:[],
                total:0,
                total1:0,
                emptyText: '數據加載中',
                dialogVisible:false,
            }
        },
        mounted () {
            this.formData.distNo = this.$route.query.distNo
            this.distNo = this.formData.distNo
            // this.searchErrorCode()
            this.searchList()
        },
        methods:{
            init(){
                this.formData.page_num = 1
                this.formData.page.pageNum = 1
                this.total = 0
                this.searchList()
            },
            rowClick(row){
                console.log(row)
                let errorType = row.errorType
                let data = JSON.parse(row.errorExtJson)
                if(errorType == 'WRK'){
                    let routeName = 'works-baseinfo';
                    if (data.work_type === 'ARR') {
                        routeName = 'works-baseinfoarr2';
                    } else if (data.work_type === 'ORG') {
                        routeName = 'works-baseinfo2';
                    } else if (data.work_type === 'ADP') {
                        routeName = 'works-baseinfoadp2';
                    } else if (data.work_type === 'AV') {
                        routeName = 'works-baseinfoav2';
                    } else if ( data.work_type == 'TV') {
                        routeName = 'works-baseinfotv2';
                    } else if (data.work_type == 'ME') {
                        routeName = 'works-medleyinfo'
                    }
                    this.$router.push({name:routeName,query:{id:data.work_id,socId:data.work_soc,nameId:data.work_id,title:data.work_id}})
                }else if(errorType == 'AGR'){
                    this.$router.push({name:'contractInfo',query:{contractNo:data.agr_no,nameId:data.agr_no,title:data.agr_no}})
                }else if(errorType == 'IPI'){
                    this.$router.push({name:'member-info',query:{ipBaseNo:data.ip_base_no,tabShow:false,nameId:data.ip_base_no,title:data.ip_base_no}})
                }
            },
            searchList(){
                this.emptyText = '數據加載中';
                this.$http.post('/dist/getDistDistributionCheckList',this.formData).then(res => {
                    console.log(res)
                    if(res.success){
                        this.tableData = res.data.list
                        this.total = res.data.total
                        if(! this.tableData || this.tableData.length == 0){
                            this.emptyText = '暫無數據';
                        }
                    }
                })
            },
            handleSearchButton() {
                this.formData.page_num = 1
                this.formData.page.pageNum = 1
                this.total = 0
                this.searchList();
            },
            searchErrorCode(page){
                // if(this.formData.distNo === this.distNo && this.errorCodes.length > 0){
                //     return
                // }
                var page = page||1
                this.distNo = this.formData.distNo
                this.errorCodes = []
                let _param = {
                    "distNo":this.formData.distNo,
                    page:{pageSize:10,pageNum:page}
                }
                this.$http.post('/dist/getErrorCodeByDistNo',_param).then(res => {
                    console.log(res)
                    if(res.success){
                        // debugger
                        console.log(res.data.list)
                        this.errorCodes = res.data.list || []
                        this.total1 = res.data.total
                        this.dialogVisible = true
                    }
                })
            },
            curChange(row){
                this.formData.errorCode = row.errCode
            },
            changeErrCode(){
                this.errorCode = this.formData.errorCode
                this.dialogVisible = false
            },
            handleChange(val){
                this.formData.page.pageNum = val
                this.searchList()
            },
            handleChange1(val){
                this.searchErrorCode(val)
            },
            check(id,checked){
                this.$http.get('/dist/updateCheckDistDistributionCheck',{params:{checked:checked,distDistributionCheckId:id}}).then(res => {
                    if(res.success){
                        this.$message.success(res.data)
                        this.init()
                    }
                }) 
            },
            clearSearch() {
            this.errorCode = ''
            // this.onSubmit();
            },
        },
         beforeRouteEnter (to, from, next) {
            next(vm => {
                if(to.params.distNo){
                    vm.formData.distNo = to.params.distNo
                }

            })
        }
    }
</script>

<style scoped>
.my-autocomplete li{
    line-height: normal;
    padding: 7px;
}
.my-autocomplete li .name{
    text-overflow: ellipsis;
      overflow: hidden;
}
.my-autocomplete li .addr{
    font-size: 12px;
      color: #b4b4b4;
}
.my-autocomplete li .highlighted .addr{
     color: #ddd;
}

</style>
<style>

</style>