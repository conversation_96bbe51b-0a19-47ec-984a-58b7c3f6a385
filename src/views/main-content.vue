<template>
    <main class="site-content" :class="{ 'site-content--tabs': $route.meta.isTab }" style="padding-top: 42px;">
        <!-- 主入口标签頁 s -->
        <el-tabs
            v-if="$route.meta.isTab"
            v-model="mainTabsActiveName"
            :closable="true"
            @tab-click="selectedTabHandle"
            @tab-remove="removeTabHandle">
            <el-dropdown class="site-tabs__tools" :show-timeout="0">
                <i class="el-icon-arrow-down el-icon--right"></i>
                <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item @click.native="tabsCloseCurrentHandle">關闭当前标签頁</el-dropdown-item>
                    <el-dropdown-item @click.native="tabsCloseOtherHandle">關闭其它标签頁</el-dropdown-item>
                    <el-dropdown-item @click.native="tabsCloseAllHandle">關闭全部标签頁</el-dropdown-item>
                    <el-dropdown-item @click.native="tabsRefreshCurrentHandle">刷新当前标签頁</el-dropdown-item>
                </el-dropdown-menu>
            </el-dropdown>
            <el-tab-pane
                v-for="item in mainTabs"
                :key="`${item.extraName || item.name}_${(item.params && item.params.key)||0}`"
                :label="(item.params && item.params.title)||item.title"
                :name="item.extraName || item.name">
                <el-card :body-style="siteContentViewHeight">
                    <!-- <span>{{ `${item.extraName || item.name}_${(item.params && item.params.key)||0}` }}</span> -->
                    <iframe
                        v-if="item.type === 'iframe'"
                        :src="item.iframeUrl"
                        :key="`route_ifm_${item.extraName || item.name}_${(item.params && item.params.key)||0}`"
                        width="100%" height="100%" frameborder="0" scrolling="yes">
                    </iframe>
                    <keep-alive v-else>
                        <router-view :key="`route_v_${item.extraName || item.name}_${(item.params && item.params.key)||0}`" v-if="(item.extraName || item.name) === mainTabsActiveName" />
                    </keep-alive>
                </el-card>
            </el-tab-pane>
        </el-tabs>
        <!-- 主入口标签頁 e -->
        <el-card v-else :body-style="siteContentViewHeight">
            <keep-alive>
                <router-view />
            </keep-alive>
        </el-card>
    </main>
</template>

<script>
    // import { isURL } from '@/utils/validate'
    export default {
        name: 'mainContent',
        inject: ['refresh'],
        data () {
            return {
                siteContentViewHeight: {
                    minHeight: 'calc(100vh - 106px)'
                }
            }
        },
        computed: {
            // documentClientHeight: {
            //     get () { return this.$store.state.common.documentClientHeight }
            // },
            menuActiveName: {
                get () { return this.$store.state.common.menuActiveName },
                set (val) { this.$store.commit('common/updateMenuActiveName', val) }
            },
            mainTabs: {
                get () { return this.$store.state.common.mainTabs },
                set (val) { this.$store.commit('common/updateMainTabs', val) }
            },
            mainTabsActiveName: {
                get () { return this.$store.state.common.mainTabsActiveName },
                set (val) { this.$store.commit('common/updateMainTabsActiveName', val) }
            },
            // siteContentViewHeight () {
            //     var height = this.documentClientHeight - 50 - 30 - 2
            //     if (this.$route.meta.isTab) {
            //         height -= 40
            //         return isURL(this.$route.meta.iframeUrl) ? { height: height + 'px' } : { minHeight: height + 'px' }
            //     }
            //     return { minHeight: height + 'px' }
            // }
        },
        created(){
            // 關閉當前tab
            this.$bus.$on('closeCurrentTab', (callback) => {
                this.tabsCloseCurrentHandle();
                this.$nextTick( () => {
                    if(callback){
                        callback();
                    }
                })

            })
        },
        updated () {
            // log mainTabs
            console.log('mainTabs: ',this.mainTabs);
        },
        beforeDestroy(){
            this.$bus.$off('closeCurrentTab');
        },
        methods: {

            // tabs, 选中tab
            selectedTabHandle (tab) {
                tab = this.mainTabs.filter(item => item.extraName ? item.extraName === tab.name : item.name === tab.name)
                // tab = this.mainTabs.filter(item => item === tab);
                console.log('selectedTabHandle:tab:', tab);
                console.log(JSON.stringify(tab));
                if (tab.length >= 1) {
                    this.$router.replace({ name: tab[0].name, query: tab[0].query, params: tab[0].params })
                    // tab[0].socId && this.$store.commit('lang/updateSocId', tab[0].socId)
                }
            },
            // tabs, 删除tab
            removeTabHandle (tabName) {
              
                this.mainTabs = this.mainTabs.filter(item => item.extraName ? item.extraName !== tabName : item.name !== tabName)
                // console.log('this.mainTabs: ',this.mainTabs);
                if (this.mainTabs.length >= 1) {
                    // 当前选中tab被删除
                    if (tabName === this.mainTabsActiveName) {
                        var tab = this.mainTabs[this.mainTabs.length - 1]
                        this.$router.replace({ name: tab.name, query: tab.query, params: tab.params }, () => {
                            this.mainTabsActiveName = this.$route.extraName || this.$route.name
                        })
                    }
                } else {
                    this.menuActiveName = ''
                    this.$router.push({ name: 'home' })
                }
            },
            // tabs, 關闭当前
            tabsCloseCurrentHandle () {
                this.removeTabHandle(this.mainTabsActiveName)
            },
            // tabs, 關闭其它
            tabsCloseOtherHandle () {
                this.mainTabs = this.mainTabs.filter(item => {
                    return item.extraName ? (item.extraName === this.mainTabsActiveName) : (item.name === this.mainTabsActiveName)
                })
            },
            // tabs, 關闭全部
            tabsCloseAllHandle () {
                this.mainTabs = []
                this.menuActiveName = ''
                this.$router.push({ name: 'home' })
            },
            // tabs, 刷新当前
            tabsRefreshCurrentHandle () {
                var tab = this.$route
                let extraName = tab.name
                //兼容自定义tab
                if(tab.query.nameId){
                    extraName = tab.name + tab.query.nameId
                }

                this.removeTabHandle(extraName)
                this.$nextTick(() => {
                    this.$router.push({ name: tab.name, query: tab.query, params: tab.params })
                })
            }
        }
    }
</script>
<style lang="scss" scoped>
/deep/ .el-tabs__item{
    overflow: hidden;
    text-overflow:ellipsis;
    white-space: nowrap;
    max-width: 240px;
}
/deep/ .el-tabs__item{
    padding-right: 20px !important;
    height: 34px;
    line-height: 36px;
}
/deep/ .el-tabs__item .el-icon-close{
    position: absolute;
    right: 0;
    top: 12px;
}
</style>

