<template>
  <el-dialog
    title="修改密碼"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :append-to-body="true"
    width="600px">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
      <el-form-item label="賬號">
        <span>{{ userName }}</span>
      </el-form-item>
      <el-form-item label="原密碼" prop="password">
        <el-input type="password" v-model="dataForm.password"></el-input>
      </el-form-item>
      <el-form-item label="新密碼" prop="newPassword">
        <el-input type="password" v-model="dataForm.newPassword"></el-input>
      </el-form-item>
      <el-form-item label="確認密碼" prop="confirmPassword">
        <el-input type="password" v-model="dataForm.confirmPassword"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">確定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import { clearLoginInfo } from '@/utils'
  export default {
    data () {
      var validateConfirmPassword = (rule, value, callback) => {
        if (this.dataForm.newPassword !== value) {
          callback(new Error('確認密碼与新密碼不一致'))
        } else {
          callback()
        }
      }
      var validateNewPassword = (rule, value, callback) => {
        if (this.dataForm.password === value) {
          callback(new Error('原密碼與新密碼相同'))
        } else {
          callback()
        }
      }
      return {
        visible: false,
        dataForm: {
          password: '',
          newPassword: '',
          confirmPassword: ''
        },
        dataRule: {
          password: [
            { required: true, message: '原密碼不能為空', trigger: 'blur' }
          ],
          newPassword: [
            { required: true, message: '新密碼不能為空', trigger: 'blur' },
            { validator: validateNewPassword, trigger: 'blur' }
          ],
          confirmPassword: [
            { required: true, message: '確認密碼不能為空', trigger: 'blur' },
            { validator: validateConfirmPassword, trigger: 'blur' }
          ]
        }
      }
    },
    computed: {
      userName: {
        get () { return this.$store.state.user.name }
      },
      mainTabs: {
        get () { return this.$store.state.common.mainTabs },
        set (val) { this.$store.commit('common/updateMainTabs', val) }
      }
    },
    methods: {
      // 初始化
      init () {
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
        })
      },
      // 表單提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            let params = {
              'password': this.dataForm.password,
              'newPassword': this.dataForm.newPassword
            }
            this.$http.post('/sys/users/password', params).then(res => {
                console.log(res)
              if (res.success && res.data.code==200) {
                  this.$message({
                    message: '操作成功',
                    type: 'success',
                    duration: 1500,
                    onClose: () => {
                      this.visible = false
                      this.$nextTick(() => {
                        this.mainTabs = []
                        clearLoginInfo()
                        // this.$store.commit('common/updateMainTabs', []);
                        this.$http.post('/logout').then(res => {
                        if (res.status === 200) {
                            this.$store.commit('common/updateMainTabs', []);
                            this.$router.replace({ name: 'login' })
                        }
                    })
                      })
                    }
                  })
              }else{
                  this.$message.error(res.data.message)
              }
            })
          }
        })
      }
    }
  }
</script>
<style scoped>
>>> .el-input--medium{
  max-width: 400px;
}
</style>
