<template>
  <div class="ipibpox">
    <el-form :inline="true" :model="search" ref="form" class="demo-form-inline"  @keyup.enter.native='searchFn(1)'>
      <el-form-item prop="name">
        <el-input v-model.trim="search.name" placeholder="Name"></el-input>
      </el-form-item>
      <el-form-item prop="categoryCode">
        <el-input v-model.trim="search.categoryCode" placeholder="Category Code"></el-input>
      </el-form-item>
      <el-form-item prop="year">
        <el-input v-model="search.year" placeholder="Year"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="searchFn(1)"  v-if="isAuth('list-manage:normal-list:sample-date:list:search')">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="addFn" v-if="isAuth('list-manage:normal-list:sample-date:list:add')">新增</el-button>
      </el-form-item>
      <el-form-item>
        <span class="clear-search" @click="init()" v-if="isAuth('list-manage:normal-list:sample-date:list:clearSearch')">清除搜索</span>
      </el-form-item>
      <el-form-item>
<!--          v-if="isAuth('list-manage:normal-list:sample-date:list:viewSampleDates')"-->
        <el-button type="info" :style="{ backgroundColor: '#008B8B'}" @click="openSampleDateDrawer" >查看抽样日期列表</el-button>
      </el-form-item>
    </el-form>
    <el-table :empty-text="tableresult"   stripe :data="tableData" border style="width: 100%" v-loading="loading">
      <el-table-column prop="id" label="RuleId">
      </el-table-column>
      <el-table-column prop="name" label="Name">
      </el-table-column>
      <el-table-column prop="categoryCode" label="Category Code" min-width="100px">
      </el-table-column>
      <el-table-column prop="year" label="Year">
      </el-table-column>
      <el-table-column prop="startDate" label="Start Date" min-width="100px">
        <template slot-scope="scope">
          {{splitTime(scope.row.startDate)}}
        </template>
      </el-table-column>
      <el-table-column prop="endDate" label="EndDate" min-width="100px">
        <template slot-scope="scope">
          {{splitTime(scope.row.endDate)}}
        </template>
      </el-table-column>
      <el-table-column prop="amendTime" label="Amend Time" min-width="100px">
        <template slot-scope="scope">
          {{splitTime(scope.row.amendTime)}}
        </template>
      </el-table-column>
      <el-table-column label="Frequency/Days" min-width="100px">
        <template slot-scope="scope">
          {{scope.row.dayExtracted}}/{{scope.row.dayBase}}
        </template>
      </el-table-column>

      <el-table-column prop="clickNumber" label="Click Number" min-width="80px">
        <template slot-scope="scope">
          {{formatInteger(scope.row.clickNumber)}}
        </template>
      </el-table-column>
      <el-table-column prop="numberOfLines" label="Number of Lines" min-width="80px">
        <template slot-scope="scope">
          {{formatInteger(scope.row.numberOfLines)}}
        </template>
      </el-table-column>
        <el-table-column prop="matched" label="Matched" min-width="100px">

        </el-table-column>
        <el-table-column prop="unmatched" label="Unmatched" min-width="100px">

        </el-table-column>

      <el-table-column prop="status" label="抽樣進度" min-width="100px">
        <template slot-scope="scope">
          {{getStatusText(scope.row.status)}}
        </template>
        </el-table-column>
      <el-table-column fixed="right" label="operation" width="250">
        <template slot-scope="scope">
          <span style="width: 100%;display:flex;text-align: center;cursor: pointer">
            <!-- 抽樣完成时显示查看明細按鈕 -->
            <el-button @click="viewDetail(scope.row)" type="text" size="small" v-if="scope.row.status === 1">查看明細</el-button>
            <!-- 抽樣完成或抽樣失敗时显示重新抽樣按鈕 -->
            <el-button @click="resampleData(scope.row)" type="text" size="small" v-if="(scope.row.status === 1 || scope.row.status === -1) && isAuth('list-manage:sample-date:list:resample')">重新抽樣</el-button>
            <!-- 抽样执行时不显示编辑和删除按钮 -->
            <el-button @click="editFn(scope.row)" type="text" size="small" v-if="scope.row.status !== 2 && isAuth('list-manage:sample-date:list:change')">編輯</el-button>
            <el-button @click="deleteData(scope.row)" type="text" size="small" v-if="scope.row.status !== 2 && isAuth('list-manage:sample-date:list:del')">刪除</el-button>
          </span>

        </template>
      </el-table-column>
    </el-table>
    <el-pagination background layout="prev, pager, next" :total="total" :current-page="search.page.pageNum" @current-change="handleCurrentChange">
    </el-pagination>

    <!-- 抽样日期列表弹窗 -->
    <el-dialog
      :visible.sync="sampleDateDialog.show"
      title="抽样日期列表"
      width="60%"
      :close-on-click-modal="false"
      :close-on-press-escape="true"
      top="5vh"
    >
      <!-- 搜索区域 -->
      <el-form :inline="true" :model="dialogSearch" class="demo-form-inline" @keyup.enter.native="searchSampleDates">
        <el-form-item label="Category Code">
          <el-input v-model.trim="dialogSearch.categoryCode" ref="category" placeholder="雙擊選擇" style="width: 200px;" @dblclick.native="addCategory"></el-input>
        </el-form-item>
        <el-form-item label="年份">
          <el-input v-model="dialogSearch.year" placeholder="Year" style="width: 150px;"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchSampleDates">搜索</el-button>
        </el-form-item>
        <el-form-item>
          <el-button @click="clearDialogSearch">清除</el-button>
        </el-form-item>
      </el-form>

      <!-- 选中的年份和CategoryCode显示区域 -->
      <div v-if="selectedSampleData" style="margin-bottom: 20px; padding: 15px; background-color: #f5f7fa; border-radius: 4px;">
        <div style="font-size: 16px; font-weight: bold; color: #303133;">
          <span style="margin-right: 30px;">Category Code: {{selectedSampleData.categoryCode}}</span>
          <span>Year: {{selectedSampleData.year}}</span>
        </div>
      </div>

      <!-- 表格区域 -->
      <el-table
        :data="monthlyTableData"
        border
        style="width: 100%"
        v-loading="dialogLoading"
        :empty-text="dialogTableResult"
        :show-header="false"
        max-height="600"
      >
        <el-table-column prop="monthName" label="Month" width="120" align="center">
          <template slot-scope="scope">
            <div
              style="font-weight: bold; color: #409EFF; cursor: pointer; text-decoration: underline;"
              @click="goToEditPage(scope.row.monthName)"
              :title="'点击跳转到' + scope.row.monthName + '的抽样规则编辑页面'"
            >
              {{scope.row.monthName}}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="sampleDates" label="Sample Dates">
          <template slot-scope="scope">
            <div style="padding: 8px 0;">
              {{formatSampleDates(scope.row.sampleDates)}}
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 数据选择列表 -->
<!--      <div v-if="sampleDateTableData.length > 1" style="margin-top: 20px;">-->
<!--        <h4>选择要查看的数据：</h4>-->
<!--        <el-radio-group v-model="selectedDataIndex" @change="updateMonthlyTable">-->
<!--          <el-radio-->
<!--            v-for="(item, index) in sampleDateTableData"-->
<!--            :key="index"-->
<!--            :label="index"-->
<!--            style="display: block; margin-bottom: 10px;"-->
<!--          >-->
<!--            {{item.categoryCode}} - {{item.year}}-->
<!--          </el-radio>-->
<!--        </el-radio-group>-->
<!--      </div>-->

      <span slot="footer" class="dialog-footer">
        <el-button @click="sampleDateDialog.show = false">关闭</el-button>
      </span>
    </el-dialog>

    <!-- Category选择弹窗 -->
    <el-dialog :visible.sync="moneyTableShow" :close-on-click-modal="false" title="Category選擇">
      <div style="width: 500px;margin: auto;">
        <el-form :inline="true" :model="addSearch" @keyup.enter.native='getCategoryList()'>
          <el-form-item label="">
            <el-input placeholder="請輸入Source Name" v-model="addSearch.sourceName" class="input-with-select">
              <el-button slot="append" icon="el-icon-search" @click="getCategoryList()"></el-button>
            </el-input>
          </el-form-item>
        </el-form>
      </div>
      <el-table :empty-text="tableresult1" stripe :data="moneyTableData">
        <el-table-column property="id" label="ID"></el-table-column>
        <el-table-column property="sourceName" label="sourceName"></el-table-column>
        <el-table-column property="categoryCode" label="categoryCode"></el-table-column>
        <el-table-column property="categoryDesc" label="categoryDesc"></el-table-column>
        <el-table-column label="operation" width="120px">
          <template slot-scope="scope">
            <i class="el-icon-check pointer" @click="chooseCategory(scope.row)"></i>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background layout="prev, pager, next" :total=moneyTotal @current-change="getCategoryList">
      </el-pagination>
    </el-dialog>
  </div>
</template>

<script>
import {isAuth} from "../../../../utils";

export default {
  name: 'sampleDateList',
  data() {
    return {
      tableData: [],tableresult:' ',
      total: 0,
      search: {
        name: '',
        categoryCode: '',
        year: '',
        page: {
          pageNum: 1,
          pageSize: 10
        }
      },
      loading: false,
      // 抽样日期弹窗相关数据
      sampleDateDialog: {
        show: false
      },
      dialogSearch: {
        categoryCode: '',
        year: ''
      },
      sampleDateTableData: [],
      monthlyTableData: [],
      selectedSampleData: null,
      selectedDataIndex: 0,
      dialogLoading: false,
      dialogTableResult: ' ',
      // Category选择弹窗相关数据
      moneyTableShow: false,
      addSearch: {},
      moneyTableData: [],
      moneyTotal: 0,
      tableresult1: ' '
    }
  },
  mounted() {
    this.searchFn();
  },
  activated() {
    // if(this.$route.query.update){
    //     this.init();
    //     this.$router.push({name: 'sampleDateList', query: {update: false}});
    // }
    this.$nextTick(() => {
      if (this.$route.query.update) {
        let query = this.$route.query;
        delete query.update;
        this.init();
      }
    })
  },

  methods: {
      isAuth,
    init() {
      this.search = {
        name: '',
        categoryCode: '',
        year: '',
        page: {
          pageNum: 1,
          pageSize: 10
        }
      }
      this.total = 0
      this.searchFn();
    },
    splitTime(time) {
      return time.split(' ')[0]
    },
    getStatusText(status) {
      switch (status) {
        case -1:
          return '抽樣失敗'
        case 0:
          return '未抽樣'
        case 1:
          return '抽樣完成'
        case 2:
          return '抽樣執行中'
        default:
          return '未知狀態'
      }
    },
    // 格式化整数，去除小数位
    formatInteger(value) {
      if (value === null || value === undefined || value === '') {
        return '-'
      }
      // 转换为整数，去除小数位
      const intValue = parseInt(value)
      if (isNaN(intValue)) {
        return '-'
      }
      return intValue.toString()
    },
    searchFn(page) {
      // 只有在主动搜索时才重置页码（page参数为1），分页切换时不重置
      if (page === 1) {
        this.search.page.pageNum = 1
      }
      this.tableresult = '數據加載中...'
      this.$http.post('/list2SampleRule/getListSampleRuleBaseList', this.search).then(res => {
        console.log(res)
        if (res.success) {
          this.tableData = res.data.list
          this.total = res.data.total
        }
        this.tableresult = this.tableData.length == 0 ? '暫無數據' : ' '
      })
    },
    handleCurrentChange(val) {
      this.search.page.pageNum = val
      this.searchFn();
    },
    handleClick(item) {
      this.$router.push({ name: '', query: {} })
    },
    editFn(row) {
      this.$router.push({ name: 'normalListSampleDateEdit', params: { row: row } })
    },
    viewDetail(row) {
      // 跳轉到明細頁面
      this.$router.push({ name: 'normalListSampleDateDetail', params: { row: row } })
    },
    resampleData(row) {
      this.$msgbox.confirm('確定要重新抽樣嗎？這將把規則狀態重置為未抽樣，等待定時任務重新執行抽樣。', '提示', {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.post('/list2SampleRule/resample/' + row.id).then(res => {
          console.log('resample result:', res)
          if (res.success) {
            this.$message({
              message: '重新抽樣請求已提交，規則狀態已重置為未抽樣',
              type: 'success'
            })
            this.searchFn()
          } else {
            this.$message({
              message: res.message || '重新抽樣失敗',
              type: 'error'
            })
          }
        }).catch(error => {
          console.error('resample error:', error)
          this.$message({
            message: '重新抽樣請求失敗',
            type: 'error'
          })
        })
      }).catch(() => {
        // 用户取消操作
      });
    },
     deleteData(row) {
       console.log('row====',row)
      this.$msgbox.confirm('確定刪除?', '提示', {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
          this.$http.delete('/list2SampleRule/delete/' + row.id).then(res => {
            console.log('resres', res)
            if (res.success && res.data.code == 200) {
              this.$message({
                message: '删除成功',
                type: 'success'
              })
              this.searchFn()
              // this.$bus.$emit('closeCurrentTab', () => { this.$router.push({ name: 'sampleDateList', query: { update: true } }) });
            } else {
              this.$message({
                message: '删除失敗',
                type: 'error'
              })
            }

          })
      }).catch(() => {
      });
    },
    addFn() {
      this.$router.push({ name: 'normalListSampleDateAdd' })
    },
    // 打开抽样日期弹窗
    openSampleDateDrawer() {
      this.sampleDateDialog.show = true
      //this.searchSampleDates()
        // 打开弹窗时清空搜索条件和数据，不自动搜索
        this.dialogSearch.categoryCode = ''
        this.dialogSearch.year = ''
        this.sampleDateTableData = []
        this.monthlyTableData = []
        this.selectedSampleData = null
        this.dialogTableResult = '請先選擇Category Code並點擊搜索'
    },
    // 搜索抽样日期
    async searchSampleDates() {
        if (!this.dialogSearch.categoryCode) {
            this.$message.warning('請先選擇Category Code')
            return
        }
      this.dialogLoading = true
      this.dialogTableResult = '數據加載中...'

      try {
        // 构建搜索参数
        const searchParams = {
          categoryCode: this.dialogSearch.categoryCode,
          year: this.dialogSearch.year,
          page: {
            pageNum: 1,
            pageSize: 1000 // 获取所有数据用于分组
          }
        }

        const res = await this.$http.post('/list2SampleRule/getListSampleRuleBaseList', searchParams)
        console.log('抽样日期数据:', res)

        if (res.success && res.data.list && res.data.list.length > 0) {
          await this.processSampleDateData(res.data.list)
          if (this.sampleDateTableData.length > 0) {
            this.selectedDataIndex = 0
            this.updateMonthlyTable()
          } else {
            // 即使API返回了数据，但处理后没有有效的抽样日期数据
            this.monthlyTableData = []
            this.selectedSampleData = null
          }
        } else {
          // API没有返回数据或返回空数组
          this.sampleDateTableData = []
          this.monthlyTableData = []
          this.selectedSampleData = null
        }
        console.log('处理后的数据:', this.sampleDateTableData)
        this.dialogTableResult = this.sampleDateTableData.length === 0 ? '暫無數據' : ' '
      } catch (error) {
        console.error('获取抽样日期数据失败:', error)
        this.sampleDateTableData = []
        this.dialogTableResult = '數據加載失敗'
      } finally {
        this.dialogLoading = false
      }
    },
    // 处理抽样日期数据，按CategoryCode和年份分组
    async processSampleDateData(dataList) {
      const groupedData = {}

      // 使用Promise.all来并行获取所有规则的抽样日期
      const promises = dataList.map(async (rule) => {
        const key = `${rule.categoryCode}_${rule.year}`

        if (!groupedData[key]) {
          groupedData[key] = {
            categoryCode: rule.categoryCode,
            year: rule.year,
            january: [],
            february: [],
            march: [],
            april: [],
            may: [],
            june: [],
            july: [],
            august: [],
            september: [],
            october: [],
            november: [],
            december: []
          }
        }

        // 获取该规则的具体抽样日期
        await this.getSampleDatesForRule(rule.id, groupedData[key])
      })

      try {
        await Promise.all(promises)

        // 对每个分组的月份日期进行排序
        Object.values(groupedData).forEach(group => {
          const monthNames = ['january', 'february', 'march', 'april', 'may', 'june',
                            'july', 'august', 'september', 'october', 'november', 'december']
          monthNames.forEach(monthKey => {
            if (Array.isArray(group[monthKey])) {
              group[monthKey].sort((a, b) => a - b)
            }
          })
        })

        // 转换为数组格式
        this.sampleDateTableData = Object.values(groupedData)
      } catch (error) {
        console.error('处理抽样日期数据失败:', error)
        this.sampleDateTableData = []
      }
    },
    // 获取特定规则的抽样日期
    async getSampleDatesForRule(ruleId, groupData) {
      try {
        const res = await this.$http.get('/list2SampleRule/getListSampleRuleBaseById', { params: { baseId: ruleId } })

        if (res.success && res.data.listSampleRuleMappingList) {
          const sampleDates = res.data.listSampleRuleMappingList

          sampleDates.forEach(dateItem => {
            const date = new Date(dateItem.sampleDate)
            const month = date.getMonth() + 1 // getMonth() 返回 0-11
            const day = date.getDate()

            const monthNames = ['january', 'february', 'march', 'april', 'may', 'june',
                              'july', 'august', 'september', 'october', 'november', 'december']

            if (month >= 1 && month <= 12) {
              const monthKey = monthNames[month - 1]
              if (!groupData[monthKey].includes(day)) {
                groupData[monthKey].push(day)
              }
            }
          })
        }
      } catch (error) {
        console.error('获取规则抽样日期失败:', error)
      }
    },
    // 格式化抽样日期显示
    formatSampleDates(dates) {
      if (!dates || !Array.isArray(dates) || dates.length === 0) {
        return '-'
      }
      return dates.join(', ')
    },
    // 清除弹窗搜索
    clearDialogSearch() {
      this.dialogSearch = {
        categoryCode: '',
        year: ''
      }
      this.searchSampleDates()
    },
    // 更新月份表格数据
    updateMonthlyTable() {
      if (this.sampleDateTableData.length === 0) {
        this.monthlyTableData = []
        this.selectedSampleData = null
        return
      }

      const selectedData = this.sampleDateTableData[this.selectedDataIndex]
      this.selectedSampleData = selectedData

      const monthNames = [
        { key: 'january', name: 'January' },
        { key: 'february', name: 'February' },
        { key: 'march', name: 'March' },
        { key: 'april', name: 'April' },
        { key: 'may', name: 'May' },
        { key: 'june', name: 'June' },
        { key: 'july', name: 'July' },
        { key: 'august', name: 'August' },
        { key: 'september', name: 'September' },
        { key: 'october', name: 'October' },
        { key: 'november', name: 'November' },
        { key: 'december', name: 'December' }
      ]

      this.monthlyTableData = monthNames.map(month => ({
        monthName: month.name,
        sampleDates: selectedData[month.key] || []
      }))
    },
    // 跳转到编辑页面并设置指定月份
    goToEditPage(monthName) {
      if (!this.selectedSampleData) {
        this.$message.warning('请先选择要编辑的数据')
        return
      }

      // 根据CategoryCode和年份找到对应的抽样规则
      const targetRule = this.tableData.find(rule =>
        rule.categoryCode === this.selectedSampleData.categoryCode &&
        rule.year == this.selectedSampleData.year
      )

      if (!targetRule) {
        this.$message.error('未找到对应的抽样规则')
        return
      }

      // 将英文月份名转换为月份数字
      const monthMap = {
        'January': 1, 'February': 2, 'March': 3, 'April': 4,
        'May': 5, 'June': 6, 'July': 7, 'August': 8,
        'September': 9, 'October': 10, 'November': 11, 'December': 12
      }

      const monthNumber = monthMap[monthName]
      if (!monthNumber) {
        this.$message.error('无效的月份')
        return
      }

      // 构造目标月份字符串 (YYYY-MM-01格式)
      const targetMonth = `${this.selectedSampleData.year}-${String(monthNumber).padStart(2, '0')}-01`

      // 关闭弹窗
      this.sampleDateDialog.show = false

      // 跳转到编辑页面，传递规则数据和目标月份
      this.$router.push({
        name: 'normalListSampleDateEdit',
        params: {
          row: targetRule,
          targetMonth: targetMonth // 传递目标月份参数
        }
      })
    },
    // Category选择相关方法
    addCategory() {
      this.moneyTableShow = true;
      this.getCategoryList(1);
    },
    getCategoryList(page) {
      let data = this.$utils.copy(this.addSearch)
      data.page_size = 10
      data.page_num = page ? page : 1
      this.tableresult = '數據加載中...'
      this.$http.get('/list/categorys', { params: data }).then(res => {
        if (res.success) {
          this.moneyTableData = res.data.data.list
          this.moneyTotal = res.data.data.total
        }
        this.tableresult = this.moneyTableData.length == 0 ? '暫無數據' : ' '
      })
    },
    chooseCategory(row) {
      this.$set(this.dialogSearch, 'categoryCode', row.categoryCode)
      this.moneyTableShow = false
      this.$refs.category.focus()
      this.$refs.category.blur()
    }
  }
}
</script>

<style>
</style>
