<template>
    <div>
        <el-button type="primary" class="btn" @click="add">新增</el-button>
        <el-table
            :data="tableData"
            border
            stripe
            style="width: 100%"
            :empty-text="emptyText">
            <el-table-column
                prop="distNo"
                label="dist no">
            </el-table-column>
            <el-table-column
                prop="listSourceCode"
                label="list source">
            </el-table-column>
            <el-table-column
                prop="listCategoryCode"
                label="list category">
            </el-table-column>
            <el-table-column
                prop="workRightType"
                label="work right">
            </el-table-column>
            <el-table-column
                prop="workTitle"
                label="work title">
            </el-table-column>
            <el-table-column
                prop="workId"
                label="work no">
            </el-table-column>
            <el-table-column
                prop="workSocietyCode"
                label="work society code">
            </el-table-column>
            <el-table-column
                prop="workArtists"
                label="artists">
            </el-table-column>
            <el-table-column
                prop="workAuthors" width="400px"
                label="author|composer">
                <template slot-scope="scope" >
                    <span v-if="scope.row.workAuthors==''&&scope.row.workComposers==''">
                    </span>
                    <span v-else-if="scope.row.workAuthors!=''||scope.row.workComposers!=''">
                    {{scope.row.workAuthors}}|{{scope.row.workComposers}}
                    </span>
                    <span v-else>
                    {{scope.row.workAuthors}}{{scope.row.workComposers}}
                    </span>
                </template>
            </el-table-column>
            <el-table-column
                prop="isInclude"
                label="is include">
            </el-table-column>
            <el-table-column
                label="operation"
                width="110px">
                <template slot-scope="scope">
                    <el-button @click="editRow(scope.row)" type="text" size="small">编辑</el-button>
                    <el-button @click="deleteRow(scope.row)" type="text" size="small">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :current-page="page_num"
            :total="total" @current-change="handleCurrentChange">
        </el-pagination>
    </div>
</template>

<script>
    export default {
        name: 'filterWork',
        data () {
            return {
                tableData:[],
                page_num:1,
                total:1,
                emptyText: '暫無數據',
            }
        },
        props: ['distNo'],
        mounted(){
            this.getList()
        },
        methods:{
            editRow(row){
                console.log(row)
                this.$router.push({name:'distribute-editwork',query: {row:JSON.stringify(row),nameId:row.id, title: row.distNo}})
            },
            deleteRow(row){
                this.$alert('確定要刪除嗎', '刪除', {
                    confirmButtonText: '确定',
                    showCancelButton:true,
                    cancelButtonText:'取消',
                    callback: action => {
                        if(action == 'confirm'){
                            this.$http.delete('/distributionSpecialCondition/delListDistListFilterWork/'+row.id).then(res => {
                                console.log(res)
                                if(res.data.code === 200){
                                    this.$message({
                                        message:'删除成功',
                                        type:'success'
                                    })
                                    this.getList(this.page_num)
                                }else{
                                    this.$message({
                                        message:'删除失敗',
                                        type:'error'
                                    })
                                }
                            })
                        }
                    }
                });
            },
            getList(num){
                console.log('work')
                let params = {
                    distNo:this.distNo,
                    page_num:num?num:1
                }
                this.emptyText = '數據加載中';
                this.$http.get('/distributionSpecialCondition/listDistListFilterWorkWithPage',{params}).then(res => {
                    console.log(res)
                    if(res.success){
                        this.tableData = res.data.data.list
                        this.total = res.data.data.total
                        this.page_num = params.page_num
                        if(! this.tableData || this.tableData.length == 0){
                            this.emptyText = '暫無數據';
                        }
                    }
                })
            },
            handleCurrentChange(val){
                this.page_num = val
                this.getList(val)
            },
            add(){
                this.$router.push({name:'distribute-addwork',params: {distNo:this.distNo}})
            }
        }
    }
</script>

<style scoped>
    .btn{
        margin-bottom: 10px;
    }
</style>
