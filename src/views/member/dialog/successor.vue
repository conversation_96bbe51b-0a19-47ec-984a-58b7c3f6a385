<template>
    <div>
        <div class="boxline" style="margin-top: 10px">
            <div class="title-logo"></div><h3>Base Info</h3>
            <el-form :inline="true" :model="successBaseInfo" label-width="110px" label-position="left" class="demo-form-inline">
                <!-- tab 1 -->
                <el-form-item label="seq">
                    <el-input v-model="successBaseInfo.seq" placeholder="seq"></el-input>
                </el-form-item>
                <el-form-item label="name">
                    <el-input v-model="successBaseInfo.name" placeholder="name"></el-input>
                </el-form-item>
                <el-form-item label="sex">
                    <el-input v-model="successBaseInfo.sex" placeholder="sex"></el-input>
                </el-form-item>
                <el-form-item label="Relationship">
                    <el-input v-model="successBaseInfo.relationship" placeholder="Relationship"></el-input>
                </el-form-item>
                <el-form-item label="Joint Date">
                    <date-picker v-model="successBaseInfo.jointDate" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd"></date-picker>
                </el-form-item>
                <el-form-item label="Valid From">
                    <date-picker v-model="successBaseInfo.validFrom" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd"></date-picker>
                </el-form-item>
                <el-form-item label="Valid To">
                    <date-picker v-model="successBaseInfo.validTo" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd"></date-picker>
                </el-form-item>
                <el-form-item label="Share(%)">
                    <el-input v-model="successBaseInfo.royShare" placeholder="Share(%)"></el-input>
                </el-form-item>
                <!-- tab 2 -->
                <el-form-item label="ID No">
                    <el-input v-model="successBaseInfo.idCardNo" placeholder=""></el-input>
                </el-form-item>
                <el-form-item label="Language">
                    <el-select v-model="successBaseInfo.language" placeholder="">
                        <el-option label="mandarin" value="M"></el-option>
                        <el-option label="english" value="E"></el-option>
                        <el-option label="both" value="B"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="birth Date">
                    <date-picker v-model="successBaseInfo.birthDate" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd"></date-picker>
                </el-form-item>
                <el-form-item label="Expiry Year">
                    <date-picker v-model="successBaseInfo.validTo" type="year" value-format="yyyy" format="yyyy" readonly></date-picker>
                </el-form-item>
                <el-form-item label="Successor IP Base No">
                    <el-input v-model="successBaseInfo.successorIpBaseNo" placeholder=""></el-input>
                </el-form-item>
            </el-form>
        </div>
        <!--继承人的 Contact-->
        <div class="boxline relative" style="margin-top: 10px">
            <div class="title-logo"></div><h3>Contact</h3>
            <el-form :inline="true" label-position="right" label-width="150px" class="demo-form-inline">
                <div class="boxline">
                    <div>
                        <el-form-item label="Type">
                            <el-select v-model="contactType">
                                <el-option label="Home" value="HM"></el-option>
                                <el-option label="Office" value="OF"></el-option>
                                <el-option label="Domicile" value="DO"></el-option>
                                <el-option label="Other" value="OT"></el-option>
                                <el-option label="Assistant" value="AT"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="Correspondence" class="bold-item">
                            <el-select v-model="successBaseInfo.correspondType" placeholder="">
                                <el-option label="Home" value="HM"></el-option>
                                <el-option label="Office" value="OF"></el-option>
                                <el-option label="Domicile" value="DO"></el-option>
                                <el-option label="Other" value="OT"></el-option>
                                <el-option label="Assistant" value="AT"></el-option>
                            </el-select>
                        </el-form-item>
                    </div>
                    <div>
                        <!-- <el-form-item label="Mail Name">
                            <el-input v-model="contactData.mailName" placeholder=""></el-input>
                        </el-form-item> -->
                        <el-form-item label="Contact Person">
                            <el-input v-model="contactData.person" placeholder=""></el-input>
                        </el-form-item>
                        <el-form-item label="Telephone No">
                            <el-input v-model="contactData.telephoneNo" placeholder=""></el-input>
                        </el-form-item>
                        <el-form-item label="Mobile No">
                            <el-input v-model="contactData.mobileNo" placeholder=""></el-input>
                        </el-form-item>
                        <el-form-item label="Fax No">
                            <el-input v-model="contactData.faxNo" placeholder=""></el-input>
                        </el-form-item>
                        <el-form-item label="Other No">
                            <el-input v-model="contactData.otherNo" placeholder=""></el-input>
                        </el-form-item>
                        <el-form-item label="Email">
                            <el-input v-model="contactData.email" placeholder=""></el-input>
                        </el-form-item>
                        <el-form-item label="URL">
                            <el-input v-model="contactData.url" placeholder=""></el-input>
                        </el-form-item>
                        <el-form-item label="Remark">
                            <el-input type="textarea" v-model="contactData.remark" placeholder="" style="width: 500px;"></el-input>
                        </el-form-item>
                    </div>
                </div>
            </el-form>
        </div>
        <!--继承人的 Address-->
        <div class="relative" style="margin-top: 30px">
            <div class="title-logo"></div><h3>Address</h3>
            <el-form :inline="true" label-position="right" label-width="120px" class="demo-form-inline">
                <div class="boxline">
                    <div>
                        <el-form-item label="Type">
                            <el-select v-model="addressType">
                                <el-option label="Home" value="HM"></el-option>
                                <el-option label="Office" value="OF"></el-option>
                                <el-option label="Domicile" value="DO"></el-option>
                                <el-option label="Other" value="OT"></el-option>
                                <el-option label="Assistant" value="AT"></el-option>
                            </el-select>
                        </el-form-item>
                    </div>
                    <div>
                        <el-form-item label="Care of">
                            <el-input v-model="addressData.careof" placeholder=""></el-input>
                        </el-form-item>
                        <el-form-item label="Country">
                            <el-input v-model="addressData.countryCode" style="width: 80px;"  placeholder="雙擊查詢" @dblclick.native="getCountry('address')" readonly></el-input>
                            <el-input v-model="addressData.country" style="width: 260px;" placeholder="雙擊查詢" @dblclick.native="getCountry('address')" readonly></el-input>
                        </el-form-item>
                        <el-form-item label="County">
                            <el-input v-model="addressData.county" placeholder=""></el-input>
                        </el-form-item>
                        <el-form-item label="District">
                            <el-input v-model="addressData.district" placeholder=""></el-input>
                        </el-form-item>
                        <el-form-item label="ZIP Code">
                            <el-input v-model="addressData.zipCode" placeholder=""></el-input>
                        </el-form-item>
                        <el-form-item label="Village/Street">
                            <el-input v-model="addressData.street" placeholder=""></el-input>
                        </el-form-item>
                        <el-form-item label="Section">
                            <el-input v-model="addressData.section" placeholder=""></el-input>
                        </el-form-item>
                        <el-form-item label="Lane">
                            <el-input v-model="addressData.lane" placeholder=""></el-input>
                        </el-form-item>
                        <el-form-item label="Alley">
                            <el-input v-model="addressData.alley" placeholder=""></el-input>
                        </el-form-item>
                        <el-form-item label="No">
                            <el-input v-model="addressData.no" placeholder=""></el-input>
                        </el-form-item>
                        <el-form-item label="Floor">
                            <el-input v-model="addressData.floor" placeholder=""></el-input>
                        </el-form-item>
                        <el-form-item label="Room">
                            <el-input v-model="addressData.room" placeholder=""></el-input>
                        </el-form-item>
                        <el-form-item label="Mail Box">
                            <el-input v-model="addressData.mailBox" placeholder="" style="width: 250px"></el-input>
                        </el-form-item>
                    </div>
                </div>
            </el-form>
        </div>
        <div slot="footer" class="dialog-footer" style="text-align: center;">
            <el-button @click="cancel">取 消</el-button>
            <el-button type="primary" @click="successorCommit()">確 定</el-button>
        </div>
        <select-country v-if="countryShow" ref="selectCountry" :search="countrySearch" @checkCountry="checkCountry" :modal="false"></select-country>
    </div>
</template>
<script>
    import selectCountry from '@/components/select-country'

export default{
    props: {
        info: {
            type: Object,
            default: () => {}
        },
        successorOptionType: {
            type: String
        }
    },
    data(){
        return{
            successBaseInfo: {},
            allContact: [],
            contactData: {},
            contactType: '',

            addressData:{},
            addressType: '',
            allAddress: [],

            countrySearch: {
                tisN: ''
            },
            countryShow: false,
        }
    },
    watch: {
        contactType: function(newVal, oldVal){
            this.contactData = {};
            let flag = false;
            this.allContact.forEach( item => {
                if(item.type == newVal){
                    this.contactData = item;
                    flag = true;
                }
            })
            if(!flag){
                let info = {
                    type: newVal,
                    mailName: '',
                    telephoneNo: '',
                    mobileNo: '',
                    faxNo: '',
                    otherNo: '',
                    email: '',
                    contactPeriod: '',
                    url: ''
                }
                this.contactData = info;
                this.allContact.push(info);
            }
        },
        addressType: function(newVal, oldVal){
            this.addressData = {};
            let flag = false;
            this.allAddress.forEach( item => {
                if(item.type == newVal){
                    this.addressData = item;
                    flag = true;
                }
            })
            if(!flag){
                let info = {
                    type: newVal,
                    careof: null,
                    country: null,
                    countryCode: null,
                    county: null,
                    district: null,
                    zipCode: null,
                    villageStreet: null,
                    section: null,
                    lane: null,
                    alley: null,
                    building: null,
                    floor: null,
                    room: null,
                    mailBox: null
                }
                this.addressData = info;
                this.allAddress.push(info);
            }
        }
    },
    components: {
        selectCountry
    },
    methods: {
        init(){
            this.successBaseInfo = this.$utils.copy(this.info.info);
            this.allContact = this.$utils.copy(this.info.contacts);
            this.allAddress = this.$utils.copy(this.info.addresses);
            this.$set(this, "contactType", this.successBaseInfo.correspondType ? this.successBaseInfo.correspondType : 'HM');
            this.$set(this, "addressType", this.successBaseInfo.correspondType ? this.successBaseInfo.correspondType : 'HM');
        },
        getCountry(type){
            this.countryType = type;
            this.countrySearch = {
                tisN: this.addressData.countryCode
            }
            this.countryShow = true;
            this.$nextTick( () => {
                this.$refs.selectCountry.init();
            })
        },
        checkCountry(info){
            this.$set(this.addressData, 'countryCode', info.tisN);
            this.$set(this.addressData, 'country', info.name);
        },
        successorCommit(){
            console.log(this.successorOptionType)
            this.$emit('saveSuccessorInfo', {addresses: this.allAddress, contacts: this.allContact, info: this.successBaseInfo},this.successorOptionType);
            this.$emit("cancel");
        },
        cancel(){
            this.$emit("cancel")
        }


    }
}
</script>
<style lang="scss" scoped>

</style>