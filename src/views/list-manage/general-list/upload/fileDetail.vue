<template>
  <!-- 原文件 處理後 拆分成的多個小文件 -->
  <div class="ipibpox">
    <el-form :inline="true" :model="formData" ref="form" class="demo-form-inline"  @keyup.enter.native='searchFn(1)'>
      <el-form-item prop="workTitle">
        <el-input v-model.trim="formData.title" placeholder="work title"></el-input>
      </el-form-item>
      <el-form-item prop="performer">
        <el-input v-model.trim="formData.performer" placeholder="performer"></el-input>
      </el-form-item>
      <el-form-item prop="author">
        <el-input v-model.trim="formData.author" placeholder="author"></el-input>
      </el-form-item>
      <el-form-item prop="composer">
        <el-input v-model.trim="formData.composer" placeholder="composer"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="searchFn(1)">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <span class="clear-search" @click="clearSearch()">清除搜索</span>
      </el-form-item>
    </el-form>
    <el-table :empty-text="tableresult"   stripe :data="tableData" height="600" border style="width: 100%" v-if="fileType == 'P'">
      <el-table-column prop="id" width="100" label="ID">
      </el-table-column>
      <el-table-column prop="baseId" label="FID">
      </el-table-column>
      <el-table-column prop="tvName" width="140px" label="節目名稱">
        <template slot-scope="scope">
          <span class="over-line" :title="scope.row.tvName">{{scope.row.tvName}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="episodeNo" label="劇集">
      </el-table-column>
      <el-table-column prop="title" width="140px" label="標題">
        <template slot-scope="scope">
          <span :title="scope.row.title" class="over-line">{{scope.row.title}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="artists" width="200" label="表演者">
      </el-table-column>
      <el-table-column prop="authors" label="作詞">
        <template slot-scope="scope">
          <span class="over-line" :title="scope.row.authors">{{scope.row.authors | addSpace}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="composers" label="作曲">
        <template slot-scope="scope">
          <span class="over-line" :title="scope.row.composers">{{scope.row.composers | addSpace}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="albumTitle" label="專輯">
        <template slot-scope="scope">
          <span class="over-line" :title="scope.row.albumTitle">{{scope.row.albumTitle}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="uploadType" label="音樂類型">
      </el-table-column>
      <el-table-column prop="performTime" width="120px" label="使用日期">
        <template slot-scope="scope">{{scope.row.performTime | splitDate}}</template>
      </el-table-column>
      <el-table-column prop="durationM" label="時長（分）">
      </el-table-column>
      <el-table-column prop="durationS" label="時長（秒）">
      </el-table-column>
      <el-table-column prop="clickNumber" label="使用次數">
      </el-table-column>
      <el-table-column prop="isrc" width="120" label="isrc">
      </el-table-column>
      <el-table-column prop="iswc" width="120" label="iswc">
      </el-table-column>
      <el-table-column prop="workId" width="120" label="work id">
      </el-table-column>
      <el-table-column prop="workSocietyCode" label="work code">
      </el-table-column>
    </el-table>
    <div v-else>
      <div class="base-info">
        <p><span>活動名稱：</span>{{$route.query.socName}}</p>
        <p><span>主辦方：</span>{{$route.query.company}}</p>
        <div class="clear">
          <p class="f-l"><span>category code：</span>{{tableData.length > 0?tableData[0].categoryCode:'--'}}</p>
          <p class="f-l"><span>表演日期：</span>{{tableData.length > 0?tableData[0].performTime:'' | splitDate}}</p>
        </div>

      </div>
      <el-table :empty-text="tableresult"   stripe :data="tableData" height="600" border style="width: 100%">
        <el-table-column prop="id" label="ID">
        </el-table-column>
        <el-table-column prop="baseId" label="FID">
        </el-table-column>
        <el-table-column prop="title" width="140px" label="歌曲名稱">
          <template slot-scope="scope">
            <span class="over-line" :title="scope.row.title">{{scope.row.title}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="artists" label="表演者">
          <template slot-scope="scope">
            <span class="over-line" :title="scope.row.artists">{{scope.row.artists | addSpace}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="authors" label="作詞">
          <template slot-scope="scope">
            <span class="over-line" :title="scope.row.authors">{{scope.row.authors | addSpace}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="composers" label="作曲">
          <template slot-scope="scope">
            <span class="over-line" :title="scope.row.composers">{{scope.row.composers | addSpace}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="albumTitle" label="專輯">
        </el-table-column>
        <el-table-column prop="clickNumber" label="使用次數">
        </el-table-column>
      </el-table>

    </div>
    <el-pagination background layout="sizes,prev, pager, next" @size-change="handleSizeChange" :page-sizes="[50, 100, 500, 1000]" :page-size="50" :current-page="formData.page.pageNum" :total="total" @current-change="handleCurrentChange">
    </el-pagination>
  </div>
</template>

<script>
export default {
  name: 'sampleDateList',
  data() {
    return {
      fileType: '',
      tableData: [],tableresult:' ',
      total: 0,
      formData: {
        baseId: '',
        categoryCode: '',
        title: '',
        page: {
          pageNum: 1,
          pageSize: 50
        }
      },
    }
  },
  beforeMount() {
    this.fileType = this.$route.query.type
  },
  mounted() {
    this.init();
    // this.formData.title=this.$route.query.socName?this.$route.query.socName:''
    // this.formData.title=this.$route.query.socName?this.$route.query.socName:''
    // this.formData.title=this.$route.query.socName?this.$route.query.socName:''
    // this.formData.title=this.$route.query.socName?this.$route.query.socName:''
      this.searchFn(1);

  },
  methods: {
    init() {
      this.formData = {
        baseId: this.$route.query.id,
        title: '',
        performEndTime: '',
        performStartTime: '',
        author: '',
        composer: '',
        performer: '',
        page: {
          pageNum: 1,
          pageSize: 50
        }
      }
    },
    clearSearch() {
      this.init()
      this.searchFn(1);
    },
    handleSelect(item) {
      console.log(item);
    },
    handleSizeChange(val) {
      this.formData.page.pageSize = val
      this.searchFn(1)
    },
    searchFn(pageNum) {
      this.formData.page.pageNum = pageNum
            this.tableresult = '數據加載中...'
      this.$http.post('/listBasicFileDataMapping/getListBasicFileDataMappingList', this.formData).then(res => {
        let list = res.data.list
        // console.log('===============',res)
        // list && list.map(item => {
          //   if (item.extJson) {
            //     let remarkJson = JSON.parse(item.extJson)
        //     item.remark = item.extJson
        //   } else {
          //     item.remark = ''
        //   }
        // })
        this.tableData = list
            this.tableresult = this.tableData.length == 0 ? '暫無數據' : ' '
        this.total = res.data.total
      })
    },
    handleCurrentChange(val) {
      this.searchFn(val);
    },
    audit(row) {
      this.$router.push({ name: 'fileDetail', params: { row: row } })
    }
  }
}
</script>

<style scoped lang="scss">
.base-info {
  div {
    p {
      margin: 0 30px 20px 0;
    }
  }
}
</style>
