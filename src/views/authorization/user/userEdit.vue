<template>
    <el-dialog
        :title="!dataForm.id ? '新增' : '修改'"
        :close-on-click-modal="false"
        :visible.sync="visible"
        :before-close='channelVisible'>
        <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
            <el-form-item label="用戶名" prop="account">
                <el-input type="text" v-model="dataForm.account" placeholder="登錄賬號"></el-input>
            </el-form-item>
            <el-form-item label="姓名" prop="name">
                <el-input type="text" v-model="dataForm.name" placeholder="姓名"></el-input>
            </el-form-item>
            <el-form-item label="姓名En" prop="nameEn">
                <el-input type="text" v-model="dataForm.nameEn" placeholder="姓名En"></el-input>
            </el-form-item>
            <el-form-item label="密碼" prop="password" :class="{ 'is-required': !dataForm.id }" v-if="!dataForm.id">
                <el-input v-model="dataForm.password" type="password" placeholder="密碼"></el-input>
            </el-form-item>
            <el-form-item label="確認密碼" prop="comfirmPassword" :class="{ 'is-required': !dataForm.id }" v-if="!dataForm.id">
                <el-input v-model="dataForm.comfirmPassword" type="password" placeholder="確認密碼"></el-input>
            </el-form-item>
            <el-form-item label="郵箱" prop="email">
                <el-input v-model="dataForm.email" placeholder="郵箱"></el-input>
            </el-form-item>
            <el-form-item label="手机號" prop="mobile">
                <el-input v-model="dataForm.mobile" placeholder="手机號"></el-input>
            </el-form-item>
            <el-form-item label="角色" size="mini" prop="" required>
                <!-- <el-checkbox-group v-model="dataForm.sysRoleList">
                    <el-checkbox v-for="role in roleList" :key="role.id" :label="role.id">{{ role.name }}</el-checkbox>
                </el-checkbox-group> -->
                <el-radio-group v-model="sysRoleData">
                    <el-radio v-for="role in roleList" :key="role.id" :label="role.id">{{ role.name }}</el-radio>
                </el-radio-group>
            </el-form-item>
            <!-- <el-form-item label="狀態" size="mini" prop="status">
                <el-radio-group v-model="dataForm.status">
                    <el-radio :label="0">禁用</el-radio>
                    <el-radio :label="1">正常</el-radio>
                </el-radio-group>
            </el-form-item> -->
        </el-form>
        <span slot="footer" class="dialog-footer">
            <el-button @click="channelVisible()">取消</el-button>
            <el-button type="primary" @click="dataFormSubmit()">確定</el-button>
        </span>
    </el-dialog>
</template>

<script>
    import { isEmail, isMobile } from '@/utils/validate'
    export default {
        data () {
            var validatePassword = (rule, value, callback) => {
                if(value==undefined){
                    value=''
                }
                if (!this.dataForm.id && !/\S/.test(value)) {
                    callback(new Error('密碼不能為空'))
                } else {
                    callback()
                }
            }
            var validateComfirmPassword = (rule, value, callback) => {
                if(value==undefined){
                    value=''
                }
                if (!this.dataForm.id && !/\S/.test(value)) {
                    callback(new Error('確認密碼不能為空'))
                } else if (this.dataForm.password !== value) {
                    callback(new Error('確認密碼与密碼輸入不一致'))
                } else {
                    callback()
                }
            }
            var validateEmail = (rule, value, callback) => {
                if (!isEmail(value)) {
                    callback(new Error('邮箱格式錯誤'))
                } else {
                    callback()
                }
            }
            var validateMobile = (rule, value, callback) => {
                if (!isMobile(value)) {
                    callback(new Error('手机號格式錯誤'))
                } else {
                    callback()
                }
            }
            return {
                visible: false,
                roleList: [],
                dataForm: {
                    sysRoleList: []
                },
                sysRoleData:'',
                dataRule: {
                    account: [
                        { required: true, message: '登錄名不能為空', trigger: 'blur' }
                    ],
                    name: [
                        { required: true, message: '姓名不能為空', trigger: 'blur' }
                    ],
                    nameEn: [
                        { required: true, message: '英文姓名不能為空', trigger: 'blur' }
                    ],
                    password: [
                        { validator: validatePassword, trigger: 'blur' }
                    ],
                    comfirmPassword: [
                        { validator: validateComfirmPassword, trigger: 'blur' }
                    ],
                    email: [
                        { required: true, message: '郵箱不能為空', trigger: 'blur' },
                        { validator: validateEmail, trigger: 'blur' }
                    ],
                    mobile: [
                        { required: true, message: '手機號不能為空', trigger: 'blur' },
                        { validator: validateMobile, trigger: 'blur' }
                    ]
                }
            }
        },
        methods: {
            channelVisible(){
                this.visible = false
                this.$nextTick(()=>{
                    this.$refs['dataForm'].resetFields();
                    this.sysRoleData = this.roleList[0].id
                })
            },
            init (id) {
                this.dataForm = {sysRoleList: []};
                this.$http.get('/sys/roles', {params: {page_num: 1, page_size: 9999}}).then( res => {
                    if(res.success){
                        this.roleList = res.data.data.list;
                        this.sysRoleData = this.roleList[0].id
                        this.visible = true;
                        this.getInfo(id);
                    }
                })
            },
            getInfo(id){
                if(!id){
                    return ;
                }
                this.$http.get('/sys/users/'+id).then( res => {
                    if(res.success){
                        console.log(res.data.data);
                        this.dataForm = res.data.data;
                        this.dataForm.sysRoleList = res.data.data.sysRoleList;
                        this.sysRoleData = this.dataForm.sysRoleList[0]
                    }
                })
            },
            // 表單提交
            dataFormSubmit () {
                this.$refs['dataForm'].validate((valid) => {
                    if (valid) {
                        let method = 'post';
                        let ajaxData = this.dataForm;
                        ajaxData.sysRoleList = [this.sysRoleData]

                        if(!this.sysRoleData){
                            this.$toast({tips: '請選擇角色'})
                            return;
                        }
                        if(!this.dataForm.id){
                            method = 'put';
                        }
                        console.log(ajaxData)
                        this.$http[method]('/sys/users', ajaxData).then( res => {
                            console.log(res)
                            if(res.success){
                                if (res.data.code ==200) {
                                    this.$toast({tips: this.dataForm.id ? '修改成功' : '新增成功'});
                                    this.$emit('update');
                                    this.visible = false;
                                }else{
                                    this.$toast({tips: res.data.message});
                                }
                                
                            }else{

                            }
                        })
                    }
                })
            }
        }
    }
</script>
<style lang="scss" scoped>
    /deep/ *{
        font-size: 14px;
    }
    /deep/ .el-form-item__label{
        margin-left: 0;
    }
    /deep/ .el-tabs button span{
        color: #333 !important;
    }
    /deep/ .el-radio+.el-radio{
        line-height: 25px;
        margin-left: 0;
    }
</style>
