<template>
    <!-- 原文件 處理後 拆分成的多個小文件 -->
    <div class="ipibpox">
        <el-form :inline="true" :model="formData" ref="form" class="demo-form-inline"  @keyup.enter.native='searchFn()'>
            <el-form-item prop="FID">
                <el-input v-model="formData.fileBaseId" placeholder="FID"></el-input>
            </el-form-item>
            <el-form-item prop="title">
                <el-input v-model="formData.title" placeholder="work title"></el-input>
            </el-form-item>
            <el-form-item prop="author">
                <el-input v-model="formData.author" placeholder="author"></el-input>
            </el-form-item>
            <el-form-item prop="composer">
                <el-input v-model="formData.composer" placeholder="composer"></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="searchFn()">搜索</el-button>
            </el-form-item>
            <el-form-item>
                <span class="clear-search" @click="clearSearch()">清除搜索</span>
            </el-form-item>
        </el-form>
        <div style="overflow-x: auto;">
            <el-table :empty-text="tableresult"   stripe :data="tableData" border style="width: 2500px;max-width: initial;" v-loading="loading">
                <el-table-column
                    prop="id"
                    label="id">
                </el-table-column>
                <el-table-column
                    prop="fileBaseId"
                    width="60"
                    label="FID">
                </el-table-column>
                <el-table-column
                    prop="episodeNo"
                    label="劇集">
                </el-table-column>
                <el-table-column
                    prop="title"
                    label="標題">
                </el-table-column>
                <el-table-column
                    prop="workArtist"
                    label="表演者">
                </el-table-column>
                <el-table-column
                    prop="author"
                    label="作詞">
                </el-table-column>
                <el-table-column
                    prop="composer"
                    label="作曲">
                </el-table-column>
                <el-table-column
                    width="235"
                    label="使用日期">
                    <template slot-scope="scope">
                        {{scope.row.listFileStartTime | splitDate}}
                        <span v-if="scope.row.listFileStartTime && scope.row.listFileEndTime">~</span>
                        {{scope.row.listFileEndTime | splitDate}}
                    </template>
                </el-table-column>
                <el-table-column
                    prop="durationM"
                    label="時長(分)"
                    width="90px">
                </el-table-column>
                <el-table-column
                    prop="durationS"
                    label="時長(秒)"
                    width="90px">
                </el-table-column>
                <el-table-column
                    prop="clickNumber"
                    label="使用次數"
                    width="90px">
                    <template slot-scope="scope">
                        {{scope.row.clickNumber || 1}}
                    </template>
                </el-table-column>
                <el-table-column
                    prop="businessId"
                    width="120"
                    label="business id">
                </el-table-column>
                <el-table-column
                    prop="label"
                    label="標籤">
                </el-table-column>
                <el-table-column
                    prop="product"
                    label="產品"
                    width="100px">
                </el-table-column>
                <el-table-column
                    prop="albumTitle"
                    label="album">
                </el-table-column>
                <el-table-column
                    prop="compTitle"
                    label="組曲標題">
                </el-table-column>
                <el-table-column
                    prop="compId"
                    label="組曲workId">
                </el-table-column>
                <el-table-column
                    prop="isrc"
                    label="isrc">
                </el-table-column>
                <el-table-column
                    prop="iswc"
                    label="iswc">
                </el-table-column>
                <!-- <el-table-column
                    prop="clickNumber"
                    label="使用次數">
                </el-table-column> -->
                <el-table-column
                    label="work id">
                    <template slot-scope="scope">
                        {{scope.row.extJson?JSON.parse(scope.row.extJson).workId:''}}
                    </template>
                </el-table-column>
                <el-table-column
                    label="work society code">
                    <template slot-scope="scope">
                        {{scope.row.extJson?JSON.parse(scope.row.extJson).workSocietyCode:''}}
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <el-pagination
            background
            layout="prev, pager, next"
            :total="total" @current-change="searchFn">
        </el-pagination>
    </div>
</template>

<script>
    export default {
        name: 'sampleDateList',
        data () {
            return {
                tableData: [],tableresult:' ',
                total: 0,
                formData: {
                    author:'',
                    composer:'',
                    fileBaseId:'',
                    fileName:'',
                    title:'',
                    workArtist:'',
                    page_num:1,
                    page_size:10
                },
                loading: false
            }
        },
        activated(){
            this.init();
        },
        methods: {
            clearSearch(){
                let id = this.$route.query.baseId;
                this.formData = {
                    author:'',
                    composer:'',
                    fileBaseId:id,
                    fileName:'',
                    title:'',
                    workArtist:'',
                    page_num:1,
                    page_size:10
                }
                this.searchFn();
            },
            init(){
                let id = this.$route.query.baseId;
                this.formData.fileBaseId = id
                this.total = 0
                this.searchFn();
            },
            handleSelect(item) {
                console.log(item);
            },
            searchFn (page) {
                this.loading = true;
                this.formData.page_num = page ? page : 1;
                      this.tableresult = '數據加載中...'
                this.$http.get('/list/dsp/file/listListDspFileDataMappingWithPage',{params:this.formData}).then(res => {
                    if(res.success){
                        this.tableData = res.data.data.list
                        this.total = res.data.data.total
                    }
                    this.loading = false;
                          this.tableresult = this.tableData.length == 0 ? '暫無數據' : ' '
                })
            },
            handleCurrentChange (val) {
                this.formData.page_num = val
                this.searchFn();
            }
        }
    }
</script>

<style>
</style>
