<template>
    <!-- 可支付列表 -->
    <div>
        <div style="margin-top: 20px">
            <el-tabs v-model="activeName">
                <!--個人會員Tabs-->
                <el-tab-pane label="member" name="first">
                    <el-form :inline="true" :model="memberSearch" class="demo-form-inline" @keyup.enter.native="queryMemberList">
                        <el-form-item label="ip base no">
                            <el-input v-model="memberSearch.autopayNo" placeholder ></el-input>
                        </el-form-item>
                        <el-form-item label="name">
                            <el-input v-model="memberSearch.autopayNo" placeholder ></el-input>
                        </el-form-item>
                        <el-form-item label="tax">
                            <el-input v-model="memberSearch.autopayNo" placeholder ></el-input>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="queryMemberList">搜 索</el-button>
                        </el-form-item>
                        <el-form-item>
                            <span class="clear-search" @click="clearMemberSearch()">清除搜索</span>
                        </el-form-item>
                    </el-form>
                    <el-table
                        :data="memberData"
                        border
                        style="width: 100%"
                        :empty-text="emptyText">
                        <el-table-column
                            prop="tax"
                            label="tax">
                        </el-table-column>
                        <el-table-column
                            prop="distNo"
                            label="支付日期">
                        </el-table-column>
                        <el-table-column
                            prop="distNo"
                            label="distNo">
                        </el-table-column>
                        <el-table-column
                            prop="overseasRoy"
                            label="Local Roy">
                        </el-table-column>
                        <el-table-column
                            prop="overseasRoy"
                            label="Overseas Roy">
                        </el-table-column>
                        <el-table-column
                            prop="rayabelAmount"
                            label="Payable Amount">
                        </el-table-column>
                        <el-table-column
                            prop="salesTax"
                            label="Sales Tax">
                        </el-table-column>
                        <el-table-column
                            prop="widthheldTax"
                            label="Withheld Tax">
                        </el-table-column>
                        <el-table-column
                            prop="administration"
                            label="Administration">
                            <template slot="header" slot-scope="scope">
                                <span title="Administration">Administration</span>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="deluction"
                            label="Deduction">
                            <template slot="header" slot-scope="scope">
                                <span title="Deduction">Deduction</span>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="deluction"
                            label="Advance/Neg">
                            <template slot="header" slot-scope="scope">
                                <span title="Advance/Neg">Advance/Neg</span>
                            </template>
                        </el-table-column>
                    </el-table>
                    <el-pagination
                        background
                        layout="prev, pager, next"
                        :total=memberTotal @current-change="handleMemberChange">
                    </el-pagination>
                </el-tab-pane>

                <!--團體會員Tabs-->
                <el-tab-pane label="society" name="second">
                    <el-form :inline="true" :model="socSearch" class="demo-form-inline">
                        <el-form-item label="Tax">
                            <el-input v-model="memberSearch.autopayNo" placeholder style="width: 80px;"></el-input>
                        </el-form-item>
                        <el-form-item label="Soc No">
                            <el-input v-model="memberSearch.autopayNo" placeholder style="width: 80px;"></el-input>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="querySocList">搜 索</el-button>
                        </el-form-item>
                        <el-form-item>
                            <span class="clear-search" @click="clearSocSearch()">清除搜索</span>
                        </el-form-item>
                    </el-form>
                    <el-table
                        :data="socData"
                        border
                        style="width: 100%">
                        <el-table-column
                            prop="distNo"
                            label="Tax">
                        </el-table-column>
                        <el-table-column
                            prop="distNo"
                            label="Payment Date">
                        </el-table-column>
                        <el-table-column
                            prop="distNo"
                            label="DistNo">
                        </el-table-column>
                        <el-table-column
                            prop="bankCharge"
                            label="Bank Charge">
                        </el-table-column>
                        <el-table-column
                            prop="currName"
                            label="Currency Code">
                        </el-table-column>
                        <el-table-column
                            prop="bankPayment"
                            label="Currency Name">
                        </el-table-column>
                        <el-table-column
                            prop="bankPayment"
                            label="bank Payment">
                        </el-table-column>
                    </el-table>
                    <el-pagination
                        background
                        layout="prev, pager, next"
                        :total=socTotal @current-change="handleSocChange">
                    </el-pagination>
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</template>

<script>
    export default {
        name: 'incomeDetail',
        data () {
            return {
                memberSearch:{},
                socSearch:{},
                memberTotal:0,
                socTotal:0,
                memberData:[],
                socData:[],
                activeName:'first'

            }
        },
        methods: {
            queryMemberList () {

            },
            querySocList() {

            },
            clearMemberSearch () {
                this.memberSearch = {}
                this.queryMemberList()
            },
            handleMemberChange (val) {
                this.memberSearch.page_num = val
                this.queryMemberList()
            },
            clearSocSearch () {
                this.socSearch = {}
                this.querySocList()
            },
            handleSocChange (val) {
                this.socSearch.page_num = val
                this.querySocList()
            },
        },

        mounted () {
            this.queryMemberList()
            this.querySocList()
        }
    }
</script>

<style>
    .mydetail {
        width: 100% !important;
        display: block !important;
    }

    .mydateform .el-input--suffix {
        width: 200px !important;
    }

    .mydetail .el-form-item__content {
        width: calc(100% - 150px) !important;
    }
</style>
