<template>
    <div
        class="site-wrapper"
        :class="{ 'site-sidebar--fold': sidebarFold }"
        v-loading.fullscreen.lock="loading"
        element-loading-text="加載中">
        <template v-if="!loading">
            <main-navbar />
            <main-sidebar @sendlistFlag='getlistFlag' @sendUploadFlag='getUploadFlag'/>
            <div class="site-content__wrapper" :style="{ 'min-height': documentClientHeight }">
                <main-content v-if="!$store.state.common.contentIsNeedRefresh"/>
            </div>
        </template>

        <el-dialog
            title="請選擇分配類型"
            :visible.sync="allotDialogVisible"
            width="30%"
            class="workdialog"
            :close-on-click-modal="false"
            center>

            <div>
                <el-table
                    :data="allotData"
                    stripe
                    style="width: 100%" class="typeTable">
                    <el-table-column
                        prop="name">
                        <template slot-scope="scope">
                            <span style="cursor: pointer" @click="selectAllot(scope.$index,scope.row)">{{scope.row.name}}</span>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <span slot="footer" class="dialog-footer">
        <el-button @click="allotDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="allotDialogVisible = false">確 定</el-button>
    </span>
        </el-dialog>
        <el-dialog
            title="請選擇分配類型"
            :visible.sync="uploadDialogVisible"
            width="30%"
            class="workdialog workdialog1"
            :close-on-click-modal="false"
            style=""
            center>

            <div>
                <el-table
                    :data="allotData"
                    stripe
                    style="width: 100%" class="typeTable">
                    <el-table-column
                        prop="name">
                        <template slot-scope="scope">
                            <span style="cursor: pointer" @click="selectUpload(scope.$index,scope.row)">{{scope.row.name}}</span>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <span slot="footer" class="dialog-footer">
        <el-button @click="uploadDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="uploadDialogVisible = false">確 定</el-button>
    </span>
        </el-dialog>
    </div>
</template>

<script>
    import axios from '../utils/httpRequest'
    import MainNavbar from './main-navbar'
    import MainSidebar from './main-sidebar'
    import MainContent from './main-content'
    export default {
        provide () {
            return {
                // 刷新
                refresh () {
                    this.$store.commit('common/updateContentIsNeedRefresh', true)
                    this.$nextTick(() => {
                        this.$store.commit('common/updateContentIsNeedRefresh', false)
                    })
                }
            }
        },
        data () {
            return {
                documentClientHeight: 'calc(100vh - 95px)',
                loading: true,
                allotDialogVisible: false,
                uploadDialogVisible: false,
                allotData: [
                {name: 'P分配'},
                {name: 'O分配'},
                {name: 'M分配'},
                {name: 'I分配'},
                {name: '保留款分配'},
                {name: '調整分配'}
                ]
            }
        },
        components: {
            MainNavbar,
            MainSidebar,
            MainContent
        },
        computed: {
            // documentClientHeight: {
            //     get () { return this.$store.state.common.documentClientHeight },
            //     set (val) { this.$store.commit('common/updateDocumentClientHeight', val) }
            // },
            sidebarFold: {
                get () { return this.$store.state.common.sidebarFold }
            },
            userId: {
                get () { return this.$store.state.user.id },
                set (val) { this.$store.commit('user/updateId', val) }
            },
            userName: {
                get () { return this.$store.state.user.name },
                set (val) { this.$store.commit('user/updateName', val) }
            }
        },
        created () {
            this.getUserInfo()
        },
        mounted () {
            // this.resetDocumentClientHeight()
        },
        methods: {
            // 重置窗口可视高度
            // resetDocumentClientHeight () {
            //     this.documentClientHeight = document.documentElement['clientHeight']
            //     window.onresize = () => {
            //         this.documentClientHeight = document.documentElement['clientHeight']
            //     }
            // },
            // 獲取当前管理員信息
            getUserInfo () {
                axios.get('/index').then(res => {
                    if (res.status === 200) {
                        this.loading = false;
                        if(res.data){
                                if(res.data.menus && res.data.menus.length){
                                    res.data.menus[0].childs.forEach(item => {
                                        // console.log(item)
                                        //此處為所有的權限
                                    })
                                }
                                if(res.data.user){
                                    this.userId = res.data.user.userId
                                    this.userName = res.data.user.name
                                }
                        }
                    }
                })
            },
            // getFlag (data) {
            //     this.worksDialogVisible = data
            // },
            getlistFlag (data) {
                this.allotDialogVisible = data
            },
            getUploadFlag (data) {
                this.uploadDialogVisible = data
            },
            selectAllot (index, item) {
                this.allotDialogVisible = false
                if (index === 0) {
                    this.$router.push({ name: 'distribute-list' })
                }
                if (index === 1) {
                    this.$router.push({ name: 'distribute-olist' })
                }
            },
            selectUpload (index, item) {
                this.uploadDialogVisible = false
                if (index === 0) {
                    this.$router.push({ name: 'distribute-listupload' })
                }
                if (index === 1) {
                    this.$router.push({ name: 'distribute-olistupload' })
                }
                if (index === 2) {
                    this.$router.push({ name: 'distribute-mmatch' })
                }
                if (index === 3) {
                    this.$router.push({ name: 'distribute-ilistupload' })
                }
                if (index === 4) {
                    this.$router.push({ name: 'distribute-ilistupload' })
                }
                if (index === 5) {
                    this.$router.push({ name: 'distribute-adjust' })
                }
            }
        }
    }
</script>
<style>
    .workdialog .el-dialog__body{
        height: 380px;
        text-align: center;
    }
    .workdialog1 .el-dialog__body{
        height: 290px!important;
        text-align: center;

    }
    .worksbutton{
        margin: auto;
    }
    .typeTable .is-leaf{
        display: none;
    }
    .site-content__wrapper {
        box-shadow: 0 1px 5px rgba(20, 20, 20, 0.1);
    }
    .site-content__wrapper .site-content .el-tabs .el-tabs__content {
        padding: 0;
    }
</style>
