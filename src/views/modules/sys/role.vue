<template>
    <div class="mod-role">
        <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
            <el-form-item>
                <el-input v-model="dataForm.roleName" placeholder="角色名稱" clearable></el-input>
            </el-form-item>
            <el-form-item>
                <el-button @click="getDataList()">搜索</el-button>
<!--                <el-button v-if="isAuth('sys:role:save')" type="primary" @click="addOrUpdateHandle()">新增</el-button>-->
                <el-button type="primary" @click="addOrUpdateHandle()">新增</el-button>
<!--                <el-button v-if="isAuth('sys:role:delete')" type="danger" @click="deleteHandle()"-->
<!--                           :disabled="dataListSelections.length <= 0">批量删除-->
<!--                </el-button>-->
                <el-button type="danger" @click="deleteHandle()"
                           :disabled="dataListSelections.length <= 0">批量删除
                </el-button>
            </el-form-item>
        </el-form>
        <el-table
            :data="dataList"
            border
            stripe
            v-loading="dataListLoading"
            @selection-change="selectionChangeHandle"
            style="width: 100%;">
            <el-table-column
                type="selection"
                header-align="center"
                align="center"
                width="50">
            </el-table-column>
            <el-table-column
                prop="roleId"
                header-align="center"
                align="center"
                width="80"
                label="ID">
            </el-table-column>
            <el-table-column
                prop="roleName"
                header-align="center"
                align="center"
                label="角色名稱">
            </el-table-column>
            <el-table-column
                prop="remark"
                header-align="center"
                align="center"
                label="備註">
            </el-table-column>
            <el-table-column
                prop="createTime"
                header-align="center"
                align="center"
                width="180"
                label="創建時間">
            </el-table-column>
            <el-table-column
                fixed="right"
                header-align="center"
                align="center"
                width="150"
                label="操作">
                <template slot-scope="scope">
                    <el-button v-if="isAuth('sys:role:update')" type="text" size="small"
                               @click="addOrUpdateHandle(scope.row.roleId)">修改
                    </el-button>
                    <el-button v-if="isAuth('sys:role:delete')" type="text" size="small"
                               @click="deleteHandle(scope.row.roleId)">删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            @size-change="sizeChangeHandle"
            @current-change="currentChangeHandle"
            :current-page="pageIndex"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pageSize"
            :total="totalPage"
            layout="total, sizes, prev, pager, next, jumper">
        </el-pagination>
        <!-- 弹窗, 新增 / 修改 -->
        <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    </div>
</template>

<script>
    import AddOrUpdate from './role-add-or-update'

    export default {
        data () {
            return {
                dataForm: {
                    roleName: ''
                },
                dataList: [],
                pageIndex: 1,
                pageSize: 10,
                totalPage: 0,
                dataListLoading: false,
                dataListSelections: [],
                addOrUpdateVisible: false
            }
        },
        components: {
            AddOrUpdate
        },
        activated () {
            this.getDataList()
        },
        methods: {
            // 獲取數據列表
            getDataList () {
                return
                this.dataListLoading = true
                this.$http({
                    url: this.$http.adornUrl('/sys/role/list'),
                    method: 'get',
                    params: this.$http.adornParams({
                        'page': this.pageIndex,
                        'limit': this.pageSize,
                        'roleName': this.dataForm.roleName
                    })
                }).then(({data}) => {
                    if (data && data.code === 0) {
                        this.dataList = data.page.list
                        this.totalPage = data.page.totalCount
                    } else {
                        this.dataList = []
                        this.totalPage = 0
                    }
                    this.dataListLoading = false
                })
            },
            // 每頁數
            sizeChangeHandle (val) {
                this.pageSize = val
                this.pageIndex = 1
                this.getDataList()
            },
            // 当前頁
            currentChangeHandle (val) {
                this.pageIndex = val
                this.getDataList()
            },
            // 多选
            selectionChangeHandle (val) {
                this.dataListSelections = val
            },
            // 新增 / 修改
            addOrUpdateHandle (id) {
                this.addOrUpdateVisible = true
                this.$nextTick(() => {
                    this.$refs.addOrUpdate.init(id)
                })
            },
            // 删除
            deleteHandle (id) {
                var ids = id ? [id] : this.dataListSelections.map(item => {
                    return item.roleId
                })
                this.$msgbox.confirm(`確定对[id=${ids.join(',')}]進行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
                    confirmButtonText: '確定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$http({
                        url: this.$http.adornUrl('/sys/role/delete'),
                        method: 'post',
                        data: this.$http.adornData(ids, false)
                    }).then(({data}) => {
                        if (data && data.code === 0) {
                            this.$message({
                                message: '操作成功',
                                type: 'success',
                                duration: 1500,
                                onClose: () => {
                                    this.getDataList()
                                }
                            })
                        } else {
                            this.$message.error(data.msg)
                        }
                    })
                }).catch(() => {
                })
            }
        }
    }
</script>
