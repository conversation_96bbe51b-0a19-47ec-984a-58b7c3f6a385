# 自定义日期更新接口集成说明

## 接口信息

### 后端接口
- **路径**: `/list2SampleRule/updateSampleDates`
- **方法**: `POST`
- **Content-Type**: `application/json;charset=utf-8`

### 方法签名
```java
@PostMapping(value = "/updateSampleDates", produces = "application/json;charset=utf-8") 
public Result<String> updateSampleDates(@RequestBody List2CustomSampleDatesVo customSampleDatesVo)
```

### 请求参数类 (List2CustomSampleDatesVo)
```java
public class List2CustomSampleDatesVo {
    private Long sampleRuleBaseId;        // 抽样规则基础ID
    private List<Date> customSampleDates; // 自定义抽样日期列表
}
```

## 前端实现

### 1. 触发按钮
```html
<el-button type="primary" @click="regenerateWithCustomDates">重新生成(自定义日期)</el-button>
```

### 2. 主要方法

#### regenerateWithCustomDates()
- **功能**: 用户确认操作的入口方法
- **验证**: 检查是否选择了自定义日期和抽样规则ID
- **确认**: 显示确认对话框

#### callUpdateSampleDatesApi()
- **功能**: 实际调用后端接口
- **数据转换**: 将前端日期字符串转换为后端需要的Date对象
- **错误处理**: 完整的成功/失败处理逻辑

#### handleUpdateSuccess()
- **功能**: 更新成功后的后续处理
- **选项**: 提供多种后续操作选择

### 3. 数据转换逻辑

```javascript
// 前端数据格式
this.customMarkDate = ["2024-01-15", "2024-01-20", "2024-01-25"]

// 转换为后端需要的格式
const requestData = {
  sampleRuleBaseId: this.formData.id,  // Long类型
  customSampleDates: this.customMarkDate.map(dateStr => {
    const normalizedDate = this.normalizeDate(dateStr)
    return new Date(normalizedDate + 'T00:00:00.000Z') // Date对象
  })
}
```

### 4. 请求示例

```javascript
// 发送的请求数据
{
  "sampleRuleBaseId": 123,
  "customSampleDates": [
    "2024-01-15T00:00:00.000Z",
    "2024-01-20T00:00:00.000Z", 
    "2024-01-25T00:00:00.000Z"
  ]
}
```

## 功能流程

### 1. 用户操作流程
1. 开启自定义编辑开关
2. 从预览复制日期或手动选择日期
3. 点击"重新生成(自定义日期)"按钮
4. 确认操作
5. 等待接口响应
6. 查看更新结果

### 2. 代码执行流程
```
regenerateWithCustomDates() 
  ↓ (验证数据)
  ↓ (用户确认)
callUpdateSampleDatesApi()
  ↓ (构造请求数据)
  ↓ (调用后端接口)
  ↓ (处理响应)
handleUpdateSuccess()
  ↓ (刷新数据/跳转页面)
```

## 错误处理

### 1. 前端验证
- 检查是否选择了自定义日期
- 检查是否存在抽样规则ID
- 日期格式标准化处理

### 2. 接口调用错误
- 网络错误处理
- 后端业务错误处理
- 加载状态管理

### 3. 用户提示
- 成功提示：显示更新成功消息
- 失败提示：显示具体错误信息
- 加载提示：显示处理进度

## 配置选项

### handleUpdateSuccess() 方法中的选项

#### 选项1：刷新当前页面数据（默认）
```javascript
if (this.formData.id) {
  this.getDateList(this.formData.id)
}
```

#### 选项2：关闭当前标签页并返回列表
```javascript
this.$bus.$emit('closeCurrentTab', () => {
  this.$router.push({ name: 'normalListSampleDate', query: { update: true } });
})
```

#### 选项3：重置自定义编辑状态
```javascript
this.enableCustomEdit = false
this.customMarkDate = []
this.customTargetYear = ''
```

## 调试信息

### 控制台输出
- 准备提交的自定义日期
- 发送到后端的请求数据
- 后端响应信息
- 错误信息（如果有）

### 调试步骤
1. 打开浏览器开发者工具
2. 切换到Console标签
3. 执行更新操作
4. 查看控制台输出的调试信息

## 注意事项

### 1. 时区处理
- 使用 `T00:00:00.000Z` 格式避免时区问题
- 确保前后端时区一致性

### 2. 数据类型
- `sampleRuleBaseId`: 确保是Long类型（数字）
- `customSampleDates`: 确保是Date对象数组

### 3. 错误处理
- 网络异常时显示友好提示
- 后端业务错误时显示具体错误信息

### 4. 用户体验
- 显示加载状态防止重复提交
- 提供确认对话框防止误操作
- 更新成功后及时反馈给用户

## 测试建议

### 1. 正常流程测试
- 选择日期后成功更新
- 验证后端数据是否正确更新

### 2. 异常情况测试
- 未选择日期时的提示
- 网络异常时的处理
- 后端返回错误时的处理

### 3. 边界情况测试
- 选择大量日期时的性能
- 日期格式边界情况
- 并发操作的处理
