<template>
    <!-- Name列表 -->
    <div>
        <el-form :inline="true" :model="searchForm" class="demo-form-inline" @keyup.enter.native="queryList(1)">
            <el-form-item label="Chinese Word:">
                <el-input v-model="searchForm.chinWord" placeholder></el-input>
            </el-form-item>
            <el-form-item label="Roman Word:">
                <el-input v-model="searchForm.romanWord" placeholder></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="queryList(1)" v-if="isAuth('works:refChinDict:find')">搜 索</el-button>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="add" v-if="isAuth('works:refChinDict:add')">新 增</el-button>
            </el-form-item>
            <el-form-item>
                <span class="clear-search" @click="clearSearch()" v-if="isAuth('works:refChinDict:find')">清除搜索</span>
            </el-form-item>
        </el-form>
        <el-table :empty-text="tableresult" :data="tableData" border style="width: 100%">
            <el-table-column prop="chinWord" label="Chinese Word"></el-table-column>
            <el-table-column prop="romanWord" label="Roman Word"></el-table-column>
            <el-table-column prop="id" label="Chin ID"></el-table-column>
            <el-table-column prop="pinYin" label="Pin Yin"></el-table-column>
            <el-table-column label="operation" width="250">
                <template slot-scope="scope">
                    <span class="a-blue" @click="edit(scope.row)" v-if="isAuth('works:refChinDict:change')">編輯</span>
                    <span class="a-blue" @click="del(scope.row)" v-if="isAuth('works:refChinDict:del') ">删除</span>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination background layout="prev, pager, next" :total="total" @current-change="queryList" :current-page="currentPage"></el-pagination>
        <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="600px" :close-on-click-modal="false" :before-close='cancelEdit'>
            <el-form
                :inline="true"
                ref="editForm"
                :model="editForm"
                label-width="150px"
                label-position="right"
                class="demo-form-inline"
                :rules="rules"
            >
                <div>
                    <el-form-item label="Chinese Word" prop="chinWord">
                        <el-input v-model="editForm.chinWord" placeholder=""></el-input>
                    </el-form-item>
                    <el-form-item label="Roman Word" prop="romanWord">
                        <el-input v-model="editForm.romanWord" placeholder=""></el-input>
                    </el-form-item>
                    <el-form-item label="Roman Word" prop="romanWord">
                        <el-select  v-model="editForm.pinYin" placeholder="">
                            <el-option key="P" label="P" value="P"></el-option>
                            <el-option key="H" label="H" value="H"></el-option>
                        </el-select >
                    </el-form-item>
                </div>
            </el-form>
            <span slot="footer" class="dialog-footer">
        <el-button @click="cancelEdit">取 消</el-button>
        <el-button type="primary" @click="editConfirm">確 定</el-button>
      </span>
        </el-dialog>
    </div>
</template>

<script>
    export default {
        data: () => {
            return {
                searchForm: {
                    chinWord: '',
                    romanWord: '',
                    pinYin: 'P'
                },
                tableData: [],tableresult:' ',
                editForm: {
                    chinWord: '',
                    romanWord: '',
                    pinYin: ''
                },
                isEdit: false,
                dialogVisible: false,
                distNoDetails: [],
                total: 1,
                currentPage:1,
                dialogTitle:'',
                loading: null,
                dialogVisible: false,
                tableData1: [],
                flag:true,
                flag2:true,
                paNameNo: '',
                paNameNo2: '',
                rules:{
                    chinWord:[
                        { required: true, message: '请输入', trigger: 'blur' },
                    ],
                    romanWord:[
                        { required: true, message: '请输入', trigger: 'blur' },
                    ]
                }
            }
        },
        methods: {

            clearSearch () {
                this.searchForm = {
                    chinWord: '',
                    romanWord: '',
                    pinYin: 'P'
                }
                this.queryList()
            },
            queryList (num) {
                let ajaxData = {
                    page_num: num ? num : 1,
                    page_size: 10
                }
                ajaxData = Object.assign(ajaxData, this.searchForm)
                const loading = this.$loading()
                this.tableresult = '數據加載中...'
                this.$http
                  .get('/ref/chin/dict/getListWithPage', {params: ajaxData})
                  .then(res => {
                      if (res.success) {
                          this.tableData = res.data.data.list || []
                          this.total = res.data.data.total
                          this.currentPage = num
                          // todo-guan  分页
                          loading.close()
                      }
                          this.tableresult = this.tableData.length == 0 ? '暫無數據' : ' '
                  })
            },
            edit (item) {
                this.isEdit = true
                this.dialogTitle='编辑Chinese Word'
                // this.$router.push({name: 'paymentEdit'})
                this.dialogVisible = true
                this.editForm = this.$utils.copy(item)

                // this.editForm.paNameNo = item.paNameNo
                // this.editForm.name = item.name
                // this.editForm.splitName = item.splitName
                // this.editForm.remark = item.remark
            },
            add () {
                this.dialogTitle='新增Chinese Word'
                this.dialogVisible = true
                this.isEdit = false

                this.$refs.editForm.clearValidate()
            },
            editConfirm () {
                this.$refs.editForm.validate(validate => {
                    if(validate){
                        let params = this.$utils.copy(this.editForm)
                        const loading = this.$loading()
                        this.$http
                          .post('/ref/chin/dict/saveRefChinDict', params)
                          .then(res => {
                              loading.close()
                              if (res.success) {
                                  if (res.status === 200) {
                                    let message = res.data ? res.data : '操作成功'
                                      this.$toast({tips: message})
                                      this.dialogVisible = false
                                      this.queryList()
                                  } else {
                                      this.$toast({tips: res.data.message})
                                  }

                                  this.$refs.editForm.resetFields()
                              }
                          })
                    }
                })

            },
            cancelEdit () {
                this.dialogVisible = false
                 this.editForm = {
                    chinWord: '',
                    romanWord: '',
                    pinYin: 'P'
                }
            },
            del(row){
                console.log(row)
                this.$msgbox.confirm(`確定進行[删除]操作?`, '提示', {
                    confirmButtonText: '確定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    const loading = this.$loading()
                    this.$http.delete('/ref/chin/dict/deleteRefChinDict/' + row.id).then(res => {
                        loading.close()
                        if (res.success) {
                            this.$toast({ tips: '删除成功' })
                            this.queryList(this.currentPage)
                        }

                    })
                }).catch(() => {

                })
            },
        },
        mounted () {
            this.queryList()
        }
    }
</script>

<style scoped>
    .cell-detail {
        display: block;
        cursor: pointer;
    }
</style>
