<template>
  <div style="width:900px">
    <el-form label-position="right" label-width="140px" :model="formData" ref="form" :rules="rules" class="demo-form-inline">
      <el-form-item label="Dist No" prop="distNo">
        <el-input v-model="formData.distNo" placeholder="請輸入"></el-input>
      </el-form-item>
      <el-form-item label="soc code" prop="societyCode">
        <el-input v-model="formData.societyCode" ref="socInput" :readonly="true" placeholder="請雙擊" @dblclick.native="showSocList"></el-input>
      </el-form-item>
      <el-form-item label="name" prop="societyName">
        <el-input v-model="formData.societyName" ref="socNameInput" :readonly="true" @dblclick.native="showSocList" placeholder="請雙擊"></el-input>
      </el-form-item>
      <el-form-item label="Type" prop="tranType">
        <el-input v-model="formData.tranType" ref="typeInput" :readonly="true" placeholder="請雙擊" @dblclick.native="getTypeList"></el-input>
      </el-form-item>
      <el-form-item label="Description">
        <el-input v-model="formData.tranDescpt" placeholder="" readonly></el-input>
      </el-form-item>
      <el-form-item label="Amount" prop="tranAmt">
        <el-input v-model="formData.tranAmt" placeholder="請輸入" @keydown.native="$inputNumber"></el-input>
      </el-form-item>
      <el-form-item label="">
        <el-checkbox-group v-model="checkList" :min="1">
          <el-checkbox v-for="item in checkGroup" :label="item" :key="item">{{item}}</el-checkbox>
        </el-checkbox-group>

      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="save">Save</el-button>
      </el-form-item>
    </el-form>
    <!--        协会列表-->
    <el-dialog title="協會列表" :visible.sync="showSocDialog" width="50%" @close="closeSoc" :close-on-click-modal="false">
      <el-input placeholder="请输入societyCode" v-model="socCode" class="input-soc" @keyup.enter.native="getSocList">
        <el-button slot="append" icon="el-icon-search" @click="getSocList(1)"></el-button>
      </el-input>
      <el-table :empty-text="emptyText" :data="socList" stripe style="width: 100%">
        <el-table-column prop="societyCode" label="societyCode">
        </el-table-column>
        <el-table-column prop="societyName" label="name" width="400px">
        </el-table-column>
        <el-table-column label="operation" align="center">
          <template slot-scope="scope">
            <i class="el-icon-check check" @click="chooseSoc(scope.row)"></i>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background layout="prev, pager, next" :total="socTotal" @current-change="handleSocChange">
      </el-pagination>
    </el-dialog>

    <el-dialog title="選擇type" :visible.sync="showTypeDialog" width="50%" :close-on-click-modal="false">
      <el-table :empty-text="emptyText1"   :data="typeList" stripe style="width: 100%">
        <el-table-column prop="tranType" label="tranType">
        </el-table-column>
        <el-table-column prop="tranDescpt" label="description" min-width="350">
        </el-table-column>
        <el-table-column label="operation" align="center">
          <template slot-scope="scope">
            <i class="el-icon-check check" @click="chooseType(scope.row)"></i>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'add',
  data() {
    return {
      formData: {},
      checkList: ['Deduction'],
      checkGroup: ['Deduction', 'Apply Comm', 'Apply Tax', 'After Dist'],
      type: '',
      showTypeDialog: false,
      typeList: [],
      showSocDialog: false,
      socList: [],
      socTotal: 0,
      socCode: '',
      rules: {
        distNo: [
          { required: true, message: '请输入distNo', trigger: 'blur' }
        ],
        societyCode: [
          { required: true, message: '请雙擊', trigger: 'blur' }
        ],
        societyName: [
          { required: true, message: '请雙擊', trigger: 'blur' }
        ],
        tranType: [
          { required: true, message: '请雙擊', trigger: 'blur' }
        ],
        tranAmt: [
          { required: true, message: '請輸入整數或小數', trigger: 'blur' }
        ]
      },
      emptyText:'暫無數據',
      emptyText1:'暫無數據',
    }
  },
  beforeMount() {
    this.formData = JSON.parse(JSON.stringify(this.$store.state.common.royalityAdd.society))
  },
  methods: {
    save() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.checkList.indexOf('Deduction') > -1 ? this.formData.nonTaxableIncomeInd = 'Y' : this.formData.nonTaxableIncomeInd = 'N'
          this.checkList.indexOf('Apply Comm') > -1 ? this.formData.commissionInd = 'Y' : this.formData.commissionInd = 'N'
          this.checkList.indexOf('Apply Tax') > -1 ? this.formData.withheldTaxInd = 'Y' : this.formData.withheldTaxInd = 'N'
          this.checkList.indexOf('After Dist') > -1 ? this.formData.addAfterDistInd = 'Y' : this.formData.addAfterDistInd = 'N'
          this.$http.post('/transaction/statement/society', this.formData).then(res => {
            if (res.success) {
              if (res.data.code === 200) {
                this.$message({
                  message: '新增成功',
                  type: 'success', duration: 500,
                  onClose: () => {
                    this.$bus.$emit('closeCurrentTab', () => { this.$router.push({ name: 'royalties-society' }) });
                  }
                })

              } else {
                this.$message({
                  message: res.data.message,
                  type: 'warning'
                })
              }
            }
          })
        }

      })

    },
    getTypeList() {
      this.emptyText1 = '數據加載中';
      this.$http.get('/transaction/statement/tran/type/all').then(res => {
        if (res.success) {
          this.showTypeDialog = true
          this.typeList = res.data
          console.log(res.data)
          if(! this.typeList || this.typeList.length == 0){
            this.emptyText1 = '暫無數據';
          }
        }
        
      })
    },
    chooseType(row) {
      this.$set(this.formData, 'tranType', row.tranType);
      this.$set(this.formData, 'tranDescpt', row.tranDescpt);
      this.showTypeDialog = false 
      this.$refs.typeInput.focus()
      this.$refs.typeInput.blur()
    },
    showSocList() {
      this.showSocDialog = true
      this.socCode = this.socCode || this.formData.societyCode
      this.getSocList()
    },
    getSocList(page = 1) {
      let ajaxData = {
        data: {
          societyCode: this.socCode
        },
        page: {
          pageNum: page,
          pageSize: 10
        }
      }
      this.emptyText = '數據加載中';
      this.$http.post('/ref/society/getSocietyList', ajaxData).then(res => {
        console.log(res)
        if (res.success) {
          this.socList = res.data.list
          this.socTotal = res.data.total
          if(! this.socList || this.socList.length == 0){
            this.emptyText = '暫無數據';
          }
        }
        
      })

    },
    chooseSoc(row) {
      this.showSocDialog = false
      this.$set(this.formData, 'societyCode', row.societyCode);
      this.$set(this.formData, 'societyName', row.societyName);
      this.$refs.socInput.focus()
      this.$refs.socInput.blur()
      if (row.societyName) {
        this.$refs.socNameInput.focus()
        this.$refs.socNameInput.blur()
      }
    },
    handleSocChange(val) {
      this.getSocList(val)
    },
    closeSoc() {
      this.socCode = ''
    }
  },
  beforeRouteLeave(to, from, next) {
    this.$store.commit('common/updateRoyalityAdd', { type: 'society', data: this.formData })
    next()
  }
}
</script>

<style scoped>
/deep/ .el-form-item__label {
  margin-left: 0 !important;
}
.input-soc {
  margin-bottom: 20px;
}
.check {
  cursor: pointer;
}
</style>
