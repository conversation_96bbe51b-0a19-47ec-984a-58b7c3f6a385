<template>
    <div class="p-t-40">
        <top-bar :steps="steps" @save="save"></top-bar>
        <el-collapse v-model="activeNames">
            <!-- baseInfo -->
            <el-collapse-item title="Base Info" class="step-jump" name="1">
                <el-form :inline="true" ref="form" :model="baseInfo" :rules="rules" class="p-t-20" label-width="160px">
                    <div>
                        <el-form-item prop="societyCode" label="Society No.">
                            <el-input type="text" v-model="baseInfo.societyCode"></el-input>
                        </el-form-item>
                        <el-form-item prop="societyName" label="Name">
                            <el-input type="text" v-model="baseInfo.societyName"></el-input>
                        </el-form-item>
                        <el-form-item label="Full Name">
                            <el-input type="text" v-model="baseInfo.societyFullName"></el-input>
                        </el-form-item>
                    </div>
                    <div>
                        <el-form-item label="Payment Method" prop="societyPaymentMethod">
                            <el-select v-model="baseInfo.societyPaymentMethod" style="width: 190px;">
                                <el-option label="" value=""></el-option>
                                <el-option label="CABLE TRANFER" value="c"></el-option>
                                <el-option label="DEMAND DRAFT" value="d"></el-option>
                                <el-option label="PENDING" value="p"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="Country" prop="countryCode">
                            <el-input ref="country" type="text" style="width: 65px;" v-model="baseInfo.countryCode" @dblclick.native="getCountry('1')" readonly placeholder="雙擊"></el-input>
                            <el-input type="text" style="width: 120px;" v-model="baseInfo.countryName" @dblclick.native="getCountry('1')" readonly placeholder="雙擊查詢"></el-input>
                        </el-form-item>
                        <el-form-item label="Currency Code" prop="societyCurrencyCode">
                            <el-input ref="currency" type="text" style="width: 80px;" v-model="baseInfo.societyCurrencyCode" @dblclick.native="getCurrency" readonly placeholder="雙擊"></el-input>
                            <el-input type="text" style="width: 105px;" v-model="baseInfo.currencyName" @dblclick.native="getCurrency" readonly placeholder="雙擊查詢"></el-input>
                        </el-form-item>
                    </div>
                    <div>
                        <el-form-item label="Descpt">
                            <el-input type="textarea" style="width: 600px;" v-model="baseInfo.socDescpt"></el-input>
                        </el-form-item>
                    </div>
                </el-form>
            </el-collapse-item>
            <!-- right -->
            <el-collapse-item title="Right" class="step-jump" name="2">
                 <el-table :empty-text="tableresult"   stripe :data="rightData" border>
                    <el-table-column label="Right Type">
                        <template slot-scope="scope">
                            <el-select @change="RightType(scope.row,scope.$index,'rightType')" v-model="scope.row.rightType" style="width: 100%;">
                                <el-option label="Performing" value="PER"></el-option>
                                <el-option label="Mechanical" value="MEC"></el-option>
                                <el-option label="Synchoronization" value="ZYN"></el-option>
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column label="Right">
                        <template slot-scope="scope">
                            <el-select v-model="scope.row.right_code" style="width: 100%;">
                                <el-option label="exclusive" value="E"></el-option>
                                <el-option label="non-Exclusive" value="N"></el-option>
                                <el-option label="unilateral" value="U"></el-option>
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column label="Agr date" width="140">
                        <template slot-scope="scope">
                            <date-picker v-model="scope.row.agrDate" @change="RightType(scope.row,scope.$index,'agrDate')" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd" style="width: 120px !important;"></date-picker>
                        </template>
                    </el-table-column>
                    <el-table-column label="Valid From" width="140">
                        <template slot-scope="scope">
                            <date-picker v-model="scope.row.validFrom" @change="RightValid(scope.row,'validFrom')" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd" style="width: 120px !important;"></date-picker>
                        </template>
                    </el-table-column>
                    <el-table-column label="Valid To" width="140">
                        <template slot-scope="scope">
                            <date-picker v-model="scope.row.validTo" @change="RightValid(scope.row,'validTo')" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd" style="width: 120px !important;"></date-picker>
                        </template>
                    </el-table-column>
                    <el-table-column label="Reciprocal Rate">
                        <template slot-scope="scope">
                            <el-input type="text" v-model="scope.row.commissionRate"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="Affi soc">
                        <template slot-scope="scope">
                            <el-input type="text" v-model="scope.row.affiliatedSocCode" @change="queryName(scope.$index)"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="Affi.Soc.name">
                        <template slot-scope="scope">
                            <el-input type="text" v-model="scope.row.affiliatedSoc" readonly></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="operation">
                        <template slot-scope="scope">
                            <span class="operation">
                                <i class="el-icon-delete" @click="deleteData(scope.$index,rightData,'delRight')"></i>
                            </span>
                        </template>
                    </el-table-column>
                </el-table>
                <div class="add-new"><el-button type="primary" @click="addData(rightData)">新 增</el-button></div>
            </el-collapse-item>
            <!-- address -->
            <el-collapse-item title="Address" class="step-jump" name="3">
                <el-form :inline="true" class="p-t-20">
                    <el-form-item label="Tel">
                        <el-input type="tel" v-model="baseInfo.telNo"></el-input>
                    </el-form-item>
                    <el-form-item label="Email">
                        <el-input type="email" v-model="baseInfo.emailAddress"></el-input>
                    </el-form-item>
                    <el-form-item label="Fax">
                        <el-input type="tel" v-model="baseInfo.faxNo"></el-input>
                    </el-form-item>
                    <el-form-item label="Url">
                        <el-input type="url" v-model="baseInfo.url"></el-input>
                    </el-form-item>
                    <!-- <el-form-item label="Corresp Filter">
                        <el-select v-model="baseInfo.corresp">
                            <el-option label="Office" :value="1"></el-option>
                            <el-option label="N/A" :value="0"></el-option>
                        </el-select>
                    </el-form-item> -->
                </el-form>
                <div class="dashed-line"></div>
                <div class="clear">
                    <el-form :inline="true" class="clear f-l">
                        <div style="width: 620px;" class="clear">
                            <el-form-item label="Corresp" label-width="84px" class="f-l">
                                <el-input :value="'Office'" readonly></el-input>
                            </el-form-item>
                            <el-form-item label="Org Name" label-width="94px" class="f-l">
                                <el-input type="text" v-model="addressData.office.orgName"></el-input>
                            </el-form-item>
                        </div>
                        <div style="width: 620px;" class="address">
                            <el-form-item label="Address" label-width="84px">
                                <el-input type="text" v-model="addressData.office.address1"></el-input>
                            </el-form-item>
                            <el-form-item label=" " label-width="84px">
                                <el-input type="text" v-model="addressData.office.address2"></el-input>
                            </el-form-item>
                            <el-form-item label=" " label-width="84px">
                                <el-input type="text" v-model="addressData.office.address3"></el-input>
                            </el-form-item>
                            <el-form-item label=" " label-width="84px">
                                <el-input type="text" v-model="addressData.office.address4"></el-input>
                            </el-form-item>
                            <el-form-item label=" " label-width="84px">
                                <el-input type="text" v-model="addressData.office.address5"></el-input>
                            </el-form-item>
                        </div>
                    </el-form>
                    <el-form :inline="true" class="clear f-l" style="margin-bottom: 20px;">
                        <div style="width: 620px;" class="clear">
                            <el-form-item label="Corresp" label-width="84px" class="f-l">
                                <el-input :value="'N/A'" readonly></el-input>
                            </el-form-item>
                            <el-form-item label="Org Name" label-width="94px" class="f-l">
                                <el-input type="text" v-model="addressData.na.orgName"></el-input>
                            </el-form-item>
                        </div>
                        <div class="address" style="width: 620px;">
                            <el-form-item label="Address" label-width="84px">
                                <el-input type="text" v-model="addressData.na.address1"></el-input>
                            </el-form-item>
                            <el-form-item label=" " label-width="84px">
                                <el-input type="text" v-model="addressData.na.address2"></el-input>
                            </el-form-item>
                            <el-form-item label=" " label-width="84px">
                                <el-input type="text" v-model="addressData.na.address3"></el-input>
                            </el-form-item>
                            <el-form-item label=" " label-width="84px">
                                <el-input type="text" v-model="addressData.na.address4"></el-input>
                            </el-form-item>
                            <el-form-item label=" " label-width="84px">
                                <el-input type="text" v-model="addressData.na.address5"></el-input>
                            </el-form-item>
                        </div>
                    </el-form>
                </div>
                
                <!-- <el-form :inline="true">
                    <el-form-item label="Corresp">
                        <el-input :value="'Office'" readonly></el-input>
                    </el-form-item>
                    <el-form-item label="Org Name">
                        <el-input type="text" v-model="addressData.office.orgName"></el-input>
                    </el-form-item>
                    <span style="cursor: pointer">
                        <i class="el-icon-location-outline" @click="editAddress(addressData.office, 'office')"></i>
                    </span>
                </el-form> -->

            </el-collapse-item>
            <!-- contact -->
            <el-collapse-item title="Contact" class="step-jump" name="4">
                <el-table
                    :data="contactData"
                    border
                    stripe
                    style="width: 100%"
                    class="big-size">
                    <el-table-column
                        label="ID"
                        width="80">
                        <template slot-scope="scope">
                            <!-- {{scope.row.id}} -->
                            <el-input v-model="scope.row.id" readonly=""></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column
                        label="Name"
                        prop="contactName">
                        <template slot-scope="scope">
                            <!-- {{scope.row.contactName}} -->
                            <el-input v-model="scope.row.contactName"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column
                        label="Title"
                        prop="contactTitle">
                        <template slot-scope="scope">
                            <!-- {{scope.row.contactTitle}} -->
                            <el-input v-model="scope.row.contactTitle"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column
                        label="Contact Dept"
                        >
                        <template slot-scope="scope">
                            <!-- tableData -->
                            <el-select v-model="scope.row.contactDept" 
                            style="width: 100%;" 
                            filterable 
                            :remote-method="onSubmit"
                            @change="deptChange">
                                <template v-for="(item,index) in tableData">
                                    <el-option :label="item.dept" :value="item.dept" :key="index"></el-option>
                                </template>
                            </el-select>
                            <!-- <el-input v-model="scope.row.contactDept" placeholder="雙擊查詢" @dblclick.native="getDept('contact')" readonly style="width: 180px;"></el-input> -->
                            <!-- {{scope.row.contactDept}} -->
                        </template>
                    </el-table-column>
                    <el-table-column
                        label="Local Dept"
                        >
                        <template slot-scope="scope">
                            <el-select 
                            v-model="scope.row.locDept" 
                            style="width: 100%;"
                            filterable 
                            :remote-method="onSubmit"
                            @change="deptChange">
                                <template v-for="(item,index) in tableData">
                                    <el-option :label="item.dept" :value="item.dept" :key="index"></el-option>
                                </template>
                            </el-select>
                            <!-- <el-input v-model="scope.row.locDept" placeholder="雙擊查詢" @dblclick.native="getDept('contact')" readonly style="width: 180px;"></el-input> -->
                            <!-- {{scope.row.contactDept}} -->
                        </template>
                    </el-table-column>
                    <el-table-column
                        label="Remark">
                        <template slot-scope="scope">
                            <!-- <span :title="scope.row.remark">{{scope.row.remark}}</span> -->
                            <el-input v-model="scope.row.remark"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column
                        label="Corresp">
                        <template slot-scope="scope">
                            <!-- {{scope.row.correspId == 1 ? 'Office' : 'N/A'}} -->
                            <el-select v-model="scope.row.correspId" style="width: 100%;">
                                <el-option label="Office" :value="1"></el-option>
                                <el-option label="N/A" :value="0"></el-option>
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column
                        label="operation"
                        width="120">
                        <template slot-scope="scope">
                            <!-- <span style="width: 40%;display: inline-block;text-align: center;cursor: pointer">
                                <i class="el-icon-edit" @click="editContact(scope.$index,scope.row,'Edit Contact')"></i>
                            </span> -->
                            <span style="width: 40%;display: inline-block;text-align: center;cursor: pointer">
                                <i class="el-icon-delete" @click="deleteData(scope.$index,contactData,'delContact')"></i>
                            </span>
                        </template>
                    </el-table-column>
                </el-table>
                <div class="add-new">
                    <el-button type="primary" @click="addContact(contactData)">新 增</el-button>
                    <!-- <el-button type="primary" @click="editContact(contactData.length, {},'Add Contact' )">新 增</el-button> -->
                </div>
            </el-collapse-item>
            <!-- Beneficiary Bank -->
            <el-collapse-item title="Beneficiary Bank" class="step-jump" name="5">
                <el-form :inline="true" class="p-t-20" label-width="150px">
                    <div>
                        <el-form-item label="Bank No" >
                            <el-input type="tel" v-model="baseInfo.bankNo"></el-input>
                            <!-- <el-input v-model="baseInfo.bankNo" placeholder="雙擊查詢" @dblclick.native="getBank('bankNo')" readonly></el-input> -->

                        </el-form-item>
                        <el-form-item label="Branch No">
                            <!-- <el-input v-model="baseInfo.bankBranchNo" placeholder="雙擊查詢" @dblclick.native="getBranch('bankBranchNo')" readonly></el-input> -->
                            <el-input type="email" v-model="baseInfo.bankBranchNo"></el-input>
                        </el-form-item>
                        <el-form-item label="Name" >
                            <el-input type="tel" v-model="baseInfo.bankName"></el-input>
                        </el-form-item>
                    </div>
                    <div>
                        <el-form-item label="Drawn On">
                            <el-input type="url" v-model="baseInfo.drawnOnCountryCode" style="width: 65px;" placeholder="雙擊查詢" @dblclick.native="getCountry('2')" readonly></el-input>
                            <el-input type="url" v-model="baseInfo.drawnOnCountry" style="width: 120px;" placeholder="雙擊查詢" @dblclick.native="getCountry('2')" readonly></el-input>
                        </el-form-item>
                    </div>
                    <div>
                        <el-form-item label="Bank A/C No">
                            <el-input type="url" v-model="baseInfo.bankAccountNo"></el-input>
                        </el-form-item>
                        <el-form-item label="Bank A/C Name">
                            <el-input type="url" v-model="baseInfo.bankAccountName"></el-input>
                        </el-form-item>
                    </div>
                    <div>
                        <el-form-item label="Description">
                            <el-input type="url" v-model="baseInfo.bankDescpt"></el-input>
                        </el-form-item>
                        <el-form-item label="Swift">
                            <el-input type="url" v-model="baseInfo.bankSwiftCode"></el-input>
                        </el-form-item>
                        <el-form-item label="IBAN">
                            <el-input type="url" v-model="baseInfo.bankIbanCode"></el-input>
                        </el-form-item>
                    </div>
                    <div>
                        <el-form-item label="Address">
                            <el-input type="text" v-model="baseInfo.bankAddress1"></el-input>
                        </el-form-item>
                        <el-form-item label="">
                            <el-input type="text" v-model="baseInfo.bankAddress2"></el-input>
                        </el-form-item>
                        <el-form-item label="">
                            <el-input type="text" v-model="baseInfo.bankAddress3"></el-input>
                        </el-form-item>
                        <el-form-item label="">
                            <el-input type="text" v-model="baseInfo.bankAddress4"></el-input>
                        </el-form-item>
                    </div>
                </el-form>
            </el-collapse-item>
            <!-- Intermediary Bank -->
            <el-collapse-item title="Intermediary Bank" class="step-jump" name="5">
                <el-form :inline="true" class="p-t-20" label-width="150px">
                    <div>
                        <el-form-item label="Bank No">
                            <!-- <el-input type="tel" v-model="baseInfo.intBankNo"></el-input> -->
                            <el-input v-model="baseInfo.intBankNo" placeholder="雙擊查詢" @dblclick.native="getBank('intBankNo')" readonly></el-input>
                        </el-form-item>
                        <el-form-item label="Branch No">
                            <el-input v-model="baseInfo.intBankBranchNo" placeholder="雙擊查詢" @dblclick.native="getBranch('intBankBranchNo')" readonly></el-input>
                            <!-- <el-input type="email" v-model="baseInfo.intBankBranchNo"></el-input> -->
                        </el-form-item>
                        <el-form-item label="Name">
                            <el-input type="tel" v-model="baseInfo.intBankName"></el-input>
                        </el-form-item>
                    </div>
                    <div>
                        <el-form-item label="Bank A/C No">
                            <el-input type="url" v-model="baseInfo.intBankAccountNo"></el-input>
                        </el-form-item>
                        <el-form-item label="Bank A/C Name">
                            <el-input type="url" v-model="baseInfo.intBankAccountName"></el-input>
                        </el-form-item>
                    </div>
                    <div>
                        <el-form-item label="Description">
                            <el-input type="url" v-model="baseInfo.intBankDescpt"></el-input>
                        </el-form-item>
                        <el-form-item label="Swift">
                            <el-input type="url" v-model="baseInfo.intBankSwiftCode"></el-input>
                        </el-form-item>
                        <el-form-item label="IBAN">
                            <el-input type="url" v-model="baseInfo.intBankIbanCode"></el-input>
                        </el-form-item>
                    </div>
                    <div>
                        <el-form-item label="Address">
                            <el-input type="text" v-model="baseInfo.intBankAddress1"></el-input>
                        </el-form-item>
                        <el-form-item label="">
                            <el-input type="text" v-model="baseInfo.intBankAddress2"></el-input>
                        </el-form-item>
                        <el-form-item label="">
                            <el-input type="text" v-model="baseInfo.intBankAddress3"></el-input>
                        </el-form-item>
                        <el-form-item label="">
                            <el-input type="text" v-model="baseInfo.intBankAddress4"></el-input>
                        </el-form-item>
                    </div>
                </el-form>
            </el-collapse-item>
            <!-- end -->
        </el-collapse>
        <!-- edit address -->
        <!-- <el-dialog :title="'Edit '+ (editAddressType == 'office' ? 'Office' : 'N/A') +' Address'" :visible.sync="show.address" width="500px">
            <el-form :inline="true">
                <el-form-item label="address 1">
                    <el-input type="text" class="address" v-model="editAddressData.address1"></el-input>
                </el-form-item>
                <el-form-item label="address 2">
                    <el-input type="text" class="address" v-model="editAddressData.address2"></el-input>
                </el-form-item>
                <el-form-item label="address 3">
                    <el-input type="text" class="address" v-model="editAddressData.address3"></el-input>
                </el-form-item>
                <el-form-item label="address 4">
                    <el-input type="text" class="address" v-model="editAddressData.address4"></el-input>
                </el-form-item>
                <el-form-item label="address 5">
                    <el-input type="text" class="address" v-model="editAddressData.address5"></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer">
                <el-button type="warning" @click="editAddressCancel">取消</el-button>
                <el-button type="primary" @click="editAddressConfirm">確定</el-button>
            </span>
        </el-dialog> -->
        <!-- eidt contact -->
        <el-dialog :title="editContactTitle" :visible.sync="show.contact" width="600px" :close-on-click-modal="false">
            <el-form :inline="true" label-width="180px">
                <el-form-item label="Name">
                    <el-input type="text" v-model="editContactData.contactName"></el-input>
                </el-form-item>
                <el-form-item label="Title">
                    <el-input type="text" v-model="editContactData.contactTitle"></el-input>
                </el-form-item>
                <el-form-item label="Contact Dept">
                    <el-input v-model="editContactData.contactDeptId" placeholder="雙擊查詢" @dblclick.native="getDept('contact')" readonly style="width: 80px;"></el-input>
                    <el-input v-model="editContactData.contactDept" placeholder="雙擊查詢" @dblclick.native="getDept('contact')" readonly style="width: 180px;"></el-input>
                </el-form-item>
                <el-form-item label="Local Dept">
                    <el-input v-model="editContactData.locDeptId" placeholder="雙擊查詢" @dblclick.native="getDept('loc')" readonly style="width: 80px;"></el-input>
                    <el-input v-model="editContactData.locDept" placeholder="雙擊查詢" @dblclick.native="getDept('loc')" readonly style="width: 180px;"></el-input>
                </el-form-item>
                <el-form-item label="Email">
                    <el-input type="text" v-model="editContactData.emailAddress"></el-input>
                </el-form-item>
                <el-form-item label="Tel">
                    <el-input type="text" v-model="editContactData.contactTelNo"></el-input>
                </el-form-item>
                <el-form-item label="Fax">
                    <el-input type="text" v-model="editContactData.contactFaxNo"></el-input>
                </el-form-item>
                <el-form-item label="Remark">
                    <el-input type="textarea" v-model="editContactData.remark"></el-input>
                </el-form-item>
                <el-form-item label="Conesp">
                    <el-select v-model="editContactData.correspId">
                        <el-option label="Office" :value="1"></el-option>
                        <el-option label="N/A" :value="0"></el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <span slot="footer">
                <el-button type="warning" @click="show.contact = false">取消</el-button>
                <el-button type="primary" @click="closeContact(editContactTitle)">確定</el-button>
            </span>
        </el-dialog>
        <select-currency v-if="currencyShow" :search="currencySearch" ref="selectCurrency" @checkCurrency="checkCurrency"></select-currency>
        <select-country v-if="countryShow" :search="countrySearch" ref="selectCountry" @checkCountry="checkCountry"></select-country>
        <select-dept v-if="deptShow" :search="deptSearch" ref="selectDept" @checkDept="checkDept"></select-dept>
            <select-bank v-if="bankShow" ref="selectBank" :search="bankSearch" @checkBank="checkBank"></select-bank>
    <select-branch v-if="branchShow" ref="selectBranch" :search="branchSearch" @checkBranch="checkBranch"></select-branch>
    </div>
</template>
<script>
import selectCurrency from '@/components/select-currency';
import selectCountry from '@/components/select-country';
import selectDept from '@/components/select-dept';
import selectBank from '@/components/select-bank'
import selectBranch from '@/components/select-branch'
export default {
    data(){
        return {
            countryNum:'',
            bankSearch: {
            },
            bankShow: false,
            bankType: '',
            
            branchSearch: {

            },
            branchType: '',
            branchShow: false,
            tableData:[],
            editContactTitle:'',
            editContactIndex:0,
            baseInfo: {
                societyCode: '',
                societyName: '',
                societyFullName: '',
                societyPaymentMethod: '',
                countryCode: '',
                countryName: '',
                societyCurrencyCode: '',
                currencyName: '',
                socDescpt: '',
                corresp: 0
            },
            steps: [
                {
                    name: 'Base Info'
                },{
                    name: 'Right'
                },{
                    name: 'Address'
                },{
                    name: 'Contact'
                },{
                    name: 'Beneficiary'
                },{
                    name: 'Intermediary'
                }
            ],
            activeNames: ['1', '2', '3', '4', '5', '6'],
            rightData: [],tableresult:' ',
            addressData: {
                na: {},
                office: {}
            },
            contactData: [],

            show: {
                address: false,
                contact: false
            },
            // editAddressType: 0,
            // editAddressData: {},

            editContactData: {},

            currencyShow: false,
            currencySearch: {
                currencyCode: ''
            },

            countryShow: false,
            countrySearch: {
                tisN: ''
            },

            deptShow: false,
            deptSearch: {},
            deptType: '',

            rules: {
                societyCode: [
                    { required: true, message: '請輸入Society No.', trigger: 'blur' }
                ],
                societyName: [
                    { required: true, message: '請輸入Name', trigger: 'blur' }
                ],
                // societyPaymentMethod: [
                //     { required: true, message: '請選擇Payment Method', trigger: ['blur', 'change'] }
                // ],
                countryCode: [
                    { required: true, message: '請選擇Country', trigger: 'blur' }
                ],
                societyCurrencyCode: [
                    { required: true, message: '請選擇Currency Code', trigger: 'blur' }
                ],
            }
        }
    },
    components:{
        selectCurrency,
        selectCountry,
        selectDept,
        selectBank,
        selectBranch
    },
    created(){
        this.init();
    },
    methods: {
        RightType(data,index,title){
            if(data.rightType && data.agrDate){
                let next = false
                this.rightData.forEach((item,num)=>{
                    if((num != index) && (item.rightType == data.rightType) && (item.agrDate == data.agrDate)){
                        next=true
                    }
                })
                if(next){
                    this.$alert('兩條數據同樣的Right Type,Agr date不能完全一樣', '提示');
                    // this.$toast({tips: '兩條數據同樣的Right Type,Agr date不能完全一樣'});
                    if(title == 'rightType'){
                        data.rightType=''
                    }
                    // if(title=='agrDate'){
                    //     this.$set(this.rightData[index],'agrDate','')
                    // }
                }
            }
        },
        RightValid(data,title){
            if(data.validFrom && data.validTo){
                if(data.validTo<data.validFrom){
                    // this.$toast({tips: 'valid from 不能大於valid to'});
                    this.$alert('valid from 不能大於valid to', '提示');
                    // data[title]=''
                    return;
                    // data[title]=''
                    // if(title=='validFrom'){
                    //     console.log('**********')
                    //     this.$set(data,'validFrom','')
                    //     data.validFrom=''
                    // }else{
                    //     console.log('*************')
                    //     this.$set(data,'validTo','')
                    //     data.validTo=''
                    // }
                }
            }
        },
        getBank(type){
            console.log(type)
            this.bankType = type;
            this.bankSearch = {
                bankNo: '',
                bankName: ''
            }
            this.bankShow = true;
            this.$nextTick( () => {
                this.$refs.selectBank.init();
            })
        },
        checkBank(info){
            this.$set(this.baseInfo, this.bankType, info.bankNo);
            // intBankNo
        },
         /**/
        getBranch(type){
            this.branchType=type
            let bankNo = 0
            if(type=='bankBranchNo'){
                bankNo = this.baseInfo.bankNo
            }else if(type=='intBankBranchNo'){
                bankNo = this.baseInfo.intBankNo
            }
            this.branchSearch = {
                bankNo: bankNo,
                branchNo: '',
                branchName: ''
            }
            this.branchShow = true;
            this.$nextTick( () => {
                this.$refs.selectBranch.init();
            })
        },
        checkBranch(info){
            this.$set(this.baseInfo, this.branchType, info.branchNo);
            // intBankBranchNo
        },
        init(){
            console.log(this.$route.query.societyCode)
            if(this.$route.query.societyCode){
                this.queryInfo();
            }
            this.onSubmit()
        },
        deptChange(row){
            console.log('row: ', row);
            // this.tableData.forEach( item => {
            //     if(item.dept == row){
            //         this.edit.producerName = item.producerName;
            //     }
            // })
        },
        onSubmit(){
            let params ={}
            params.page = {
                pageNum: 1,
                pageSize: 999
            }
            this.$http.post('/ref/getRefSocietyContactDept', params).then(res => {
                if (res.success) {
                    this.tableData = res.data.list;
                }
            })
        },
        queryInfo(){
            this.tableresult = '數據加載中...'
            this.$http.get('/ref/society/getSocietyBySocietyCode', {params: {societyCode: this.$route.query.societyCode}}).then( res => {
                if(res.success){
                    this.baseInfo = res.data.data.refSociety;

                    if(this.baseInfo.bankNo){
                        let no1 = '';
                        for(let i =0; i<3-(this.baseInfo.bankNo + '').length;i++){
                            no1 += '0';
                        }
                        no1 += this.baseInfo.bankNo;
                        this.baseInfo.bankNo = no1;
                    }
                    if(this.baseInfo.bankBranchNo.length<3){
                        let no2 = '';
                        for(let i =0; i<7-(this.baseInfo.bankBranchNo + '').length;i++){
                            no2 += '0';
                        }
                        no2 += this.baseInfo.bankBranchNo;
                        this.baseInfo.bankBranchNo = no2;
                    }

                    if(this.baseInfo.intBankNo){
                        let no1 = '';
                        for(let i =0; i<3-(this.baseInfo.intBankNo + '').length;i++){
                            no1 += '0';
                        }
                        no1 += this.baseInfo.intBankNo;
                        this.baseInfo.intBankNo = no1;
                    }
                    
                    if(this.baseInfo.intBankBranchNo){
                        let no2 = '';
                        for(let i =0; i<7-(this.baseInfo.intBankBranchNo + '').length;i++){
                            no2 += '0';
                        }
                        no2 += this.baseInfo.intBankBranchNo;
                        this.baseInfo.intBankBranchNo = no2;
                    }
                
                    console.log(res.data.data.refSocietyRightList)

                    this.rightData = res.data.data.refSocietyRightList;
                    this.rightData.forEach( (item,index) => {
                        item.agrDate = item.agrDate ? item.agrDate.split(' ')[0] : '';
                        item.validFrom = item.validFrom ? item.validFrom.split(' ')[0] : '';
                        item.validTo = item.validTo ? item.validTo.split(' ')[0] : '';
                    })
                    res.data.data.refSocietyCorrespList.forEach( item => {
                        if(item.correspId == 1){
                            this.addressData.office = item;
                        }else if(item.correspId == '0'){
                            this.addressData.na = item;
                        }
                    })

                    // this.addressData = res.data.refSocietyCorrespList;


                    this.contactData = res.data.data.refSocietyContactList;
                }
                            this.tableresult =this.rightData.length == 0 ? '暫無數據' : ' '
            })
            
        },
        queryName(index){
            console.log('query name: ', this.rightData[index]);
            if(this.rightData[index].affiliatedSocCode){
                this.$http.get('/ref/society/getSocietyBySocietyCode', {params: {societyCode: this.rightData[index].affiliatedSocCode}}).then( res => {
                    if(res.success && res.data.code == 200){
                        if(res.data.data.refSociety){
                            this.$set(this.rightData[index], 'affiliatedSoc' , res.data.data.refSociety.societyName);
                        }else{
                            this.$toast({tips: '未查詢到此協會'})
                        }
                    }
                })
            }
        },
        addData(data){
            console.log(data)
            data.push({ affiliatedSocCode: '', affiliatedSoc: '', commissionRate: '', validTo: '', validFrom: '', agrDate: '', right_code: '', rightType: ''})
            // this.editContact(this.contactData.length - 1, this.contactData[this.contactData.length-1],'Add Contact' )
        },
        addContact(data){
            data.push({ id: '', contactName: '', contactTitle: '', contactDept: '', remark: '', correspId: ''})
        },
        deleteData(index, data,title){
            console.log(data)
            this.$msgbox.confirm('確認刪除?', '提示', {
                confirmButtonText: '確認',
                cancelButtonText: '取消',
                type: 'warning'
            }).then( () => {
                if(data[index].id){
                    this.$http.delete(`/ref/society/${title}/`+ data[index].id).then( res => {
                        if(res.success){
                            data.splice(index, 1);
                            this.$toast({tips: '刪除成功'})
                        }    
                    })
                }else{
                    data.splice(index,1)
                }
            })

        },
        // editAddress(item, type){
        //     this.show.address = true;
        //     this.editAddressType = type;
        //     this.editAddressData = this.$utils.copy(item);
        // },
        // editAddressCancel(){
        //     this.show.address = false;
        // },
        // editAddressConfirm(){
        //     this.addressData[this.editAddressType == 'office' ? 'office' : 'na'] = this.editAddressData;
        //     this.show.address = false;
        // },
        editContact(index, row,title){
            console.log(row)
            console.log(this.contactData)
            this.editContactIndex=index
            this.editContactTitle=title
            this.show.contact = true;
            this.editContactData = this.$utils.copy(row);
            // this.editContactData = row;
        },
        closeContact(){
            if(this.editContactTitle == 'Add Contact'){
                this.contactData.push(this.editContactData)
            }else{
                // this.contactData[this.editContactIndex]=this.editContactData
                this.$set(this.contactData[this.editContactIndex], 'contactName', this.editContactData.contactName);
                this.$set(this.contactData[this.editContactIndex], 'contactTitle', this.editContactData.contactTitle);
                this.$set(this.contactData[this.editContactIndex], 'contactDeptId', this.editContactData.contactDeptId);
                this.$set(this.contactData[this.editContactIndex], 'contactDept', this.editContactData.contactDept);
                this.$set(this.contactData[this.editContactIndex], 'locDeptId', this.editContactData.locDeptId);
                this.$set(this.contactData[this.editContactIndex], 'locDept', this.editContactData.locDept);
                this.$set(this.contactData[this.editContactIndex], 'emailAddress', this.editContactData.emailAddress);
                this.$set(this.contactData[this.editContactIndex], 'contactTelNo', this.editContactData.contactTelNo);
                this.$set(this.contactData[this.editContactIndex], 'contactFaxNo', this.editContactData.contactFaxNo);
                this.$set(this.contactData[this.editContactIndex], 'contactDept', this.editContactData.contactDept);
                this.$set(this.contactData[this.editContactIndex], 'remark', this.editContactData.remark);
                this.$set(this.contactData[this.editContactIndex], 'correspId', this.editContactData.correspId);
            }

            this.show.contact = false;
        },
        save(){
            this.$refs.form.validate( valid => {
                if(valid){
                    this.saveFn();
                }
            })
        },
        saveFn(){
            /**
             * addressData obj => array
             */
            console.log(this.rightData)
            let fleg1 = true
            let fleg2 = true
            this.rightData.forEach((item,index)=>{
                this.rightData.forEach((item1,index1)=>{
                    if(index!=index1){
                        if((item.rightType == item1.rightType) && (item.agrDate == item1.agrDate)){
                            fleg1 = false
                        }
                        
                    }
                })
                if(item.validTo<item.validFrom){
                    fleg2 = false
                }
            })
            if(!fleg1){
                this.$toast({tips: 'Right區域,兩條數據同樣的Right Type,Agr date不能完全一樣'});
                return
            }else if(!fleg2){
                this.$toast({tips: 'Right區域,valid from 不能大於valid to'});
                return
            }
            let ajaxData = {
                refSociety: this.baseInfo,
                refSocietyContactList: this.contactData,
                refSocietyRightList: this.rightData
            }
            ajaxData.refSocietyCorrespList = [];
            for(let key in this.addressData){
                if(JSON.stringify(this.addressData[key]) != '{}'){
                    this.addressData[key].correspId = key == 'office' ? 1 : 0;
                    this.addressData[key].societyCode = this.$route.query.societyCode;
                    ajaxData.refSocietyCorrespList.push(this.addressData[key]);
                }
            }
            let url = '/ref/society/updateSociety';
            if(!this.$route.query.societyCode){
                url = '/ref/society/addSociety';
                this.$http.post(url, ajaxData).then( (res) => {
                    if(res.success && res.data.code == 200){
                        this.$toast({tips: '添加成功'});
                        this.$bus.$emit('closeCurrentTab', () => { this.$router.push({name: 'societyList', query: {update: true}})});
                    }else{
                        this.$toast({tips: res.data.message})
                    }
                })

            }else{
                url = '/ref/society/updateSociety';
                this.$http.post(url, ajaxData).then( (res) => {
                    if(res.success){
                        if(res.data.code){
                            if(res.data.code == 200){
                                this.$toast({tips: '編輯成功'});
                                this.$bus.$emit('closeCurrentTab', () => { this.$router.push({name: 'societyList', query: {update: true}})});
                            }else{
                                this.$toast({tips: res.data.message})
                            }
                        }else{
                            this.$toast({tips: '編輯成功'});
                            this.$bus.$emit('closeCurrentTab', () => { this.$router.push({name: 'societyList', query: {update: true}})});
                        }
                        
                    }else{
                        this.$toast({tips: res.data.message})
                    }
                })
            }
        },
        getCountry(data){
            this.countryNum = data
            this.countryShow = true;
            this.$nextTick( () => {
                this.$refs.selectCountry.init();
            })

        },
        checkCountry(info){
            if(this.countryNum=='1'){
                this.baseInfo.countryCode = info.countryCode;
                this.baseInfo.countryName = info.name;
                this.$refs.country.focus();
                this.$refs.country.blur();
            }else{
                this.baseInfo.drawnOnCountryCode = info.countryCode;
                this.baseInfo.drawnOnCountry = info.name;
            }
            this.countryShow = false;
        },
        getCurrency(){
            this.currencyShow = true;
            this.$nextTick( () => {
                this.$refs.selectCurrency.init();
            })
        },
        checkCurrency(info){
            this.baseInfo.societyCurrencyCode = info.currencyCode;
            this.baseInfo.currencyName = info.currencyName;
            this.currencyShow = false;
            this.$refs.currency.focus();
            this.$refs.currency.blur();
        },

        getDept(type){
            this.deptType = type;
            this.deptSearch = {
                deptId:  type == 'contact' ? this.editContactData.contactDeptId : this.editContactData.locDeptId,
                dept:  type == 'contact' ? this.editContactData.contactDept : this.editContactData.locDept,
            }
            this.deptShow = true;
            this.$nextTick( () => {
                this.$refs.selectDept.init();
            })
        },
        checkDept(info){
            if(this.deptType == 'contact'){
                this.$set(this.editContactData, 'contactDeptId', info.deptId);
                this.$set(this.editContactData, 'contactDept', info.dept);
            }else{
                this.$set(this.editContactData, 'locDeptId', info.deptId);
                this.$set(this.editContactData, 'locDept', info.dept);
            }
        }
    }
}
</script>
<style lang="scss" scoped>
    @import '../../assets/scss/works.scss';
    /deep/ .address{
        display: block;
        width: 300px;
        margin-bottom: 10px;
    }
    /deep/ .el-select{
        width: 207px;
    }
    /deep/ .el-dialog__footer{
        text-align: center;
    }
    /deep/ .el-dialog__header{
        font-weight: 500;
        // border-bottom: 1px solid #eee;
    }
    /deep/ .big-size{
        .cell{
            line-height: 34px;
        }
    }
    /deep/ .el-dialog__header{
        height: 50px;
    }
    /deep/ .el-form--inline .el-form-item__content{
        width: 190px;
    }
    .address /deep/ .el-form-item__content{
        width: 500px;
    }
</style>

