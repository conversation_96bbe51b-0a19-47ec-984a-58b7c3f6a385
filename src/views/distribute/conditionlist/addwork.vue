<template>
    <div>
        <div style="width:80%">
            <el-form label-position="right" label-width="180px" :model="formData" class="demo-form-inline">
                <el-form-item label="dist no" prop="distNo" :rules="[{ required: true, message: '請輸入distNo', trigger: 'blur' }]">
                    <el-input
                        v-model="formData.distNo"
                        placeholder="請輸入"
                    ></el-input>
                </el-form-item>
                <el-form-item label="is include">
                    <el-switch
                        v-model="formData.isInclude"
                        active-color="#13ce66"
                        inactive-color="#e9e9e9"
                        active-value="Y"
                        inactive-value="N"
                    >
                    </el-switch>

                </el-form-item>
                <el-form-item label="list source" :rules="[{ required: true, message: '請輸入list category', trigger: 'blur' }]">
                    <el-select 
                        v-model="formData.listSourceCode" 
                        placeholder="請輸入内容" 
                        filterable 
                        remote 
                        :remote-method="getSourceList" 
                        @change="handleSelectSource" 
                        style="width:220px;">
                      <el-option v-for="item in SourceOptions" :key="item.sourceDesc" :value="item.sourceName" style="height:68px">
                          <p style="margin:0;">sourceName：{{item.sourceName}}</p>
                          <p style="margin:0;">sourceDesc：{{item.sourceDesc}}</p>
                      </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="list source desc">
                    <el-input v-model="formData.sourceDescription" placeholder="選擇list source後自動獲取" :readonly="true"></el-input>
                </el-form-item>
                <el-form-item label="list category" :rules="[{ required: true, message: '請輸入list category', trigger: 'blur' }]">
                    <el-select 
                        v-model="formData.listCategoryCode" 
                        placeholder="請輸入内容" 
                        filterable 
                        remote 
                        :remote-method="getCategoryList" 
                        @change="handleSelect" 
                        style="width:220px;">
                        <el-option v-for="item in CategoryOptions" :key="item.categoryDesc" :value="item.categoryCode" style="height:68px">
                            <p style="margin:0;">category：{{item.categoryCode}}</p>
                            <p style="margin:0;">desc：{{item.categoryDesc}}</p>
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="list category desc">
                    <el-input v-model="formData.categoryDescription" placeholder="選擇list category後自動獲取" :readonly="true"></el-input>
                </el-form-item>
                <el-form-item label="work right type">
                    <el-select v-model="formData.workRightType" placeholder="Type">
                        <el-option label="PERFORMING RIGHT" value="PER"></el-option>
                        <el-option label="MECHANICAL RIGHT" value="MEC"></el-option>
                        <el-option label="SYNCHRONIZATION RIGHT" value="ZYN"></el-option>
                        <el-option label="OD RIGHT" value="NOD"></el-option>
                        <el-option label="DB RIGHT" value="NDB"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="work options" :rules="[{ required: true, message: '請輸入搜索条件', trigger: 'blur' }]">
                    <el-input v-model="formData.workTitle" placeholder="work title"
                              style="width: 160px;margin-right: 20px;"></el-input>
                    <el-input v-model="formData.workId" placeholder="work no"
                              style="width: 160px;margin-right: 20px;"></el-input>
                    <el-input v-model="formData.workSocietyCode" placeholder="work soc"
                              style="width: 160px;margin-right: 20px;"></el-input>
                    <el-button type="primary" @click="optionsShow">F</el-button>
                    <!-- <el-button type="primary" @click="getWork(1)">F</el-button> -->
                </el-form-item>
                <el-form-item label="artists">
                    <el-input v-model="formData.workArtists" placeholder="" :readonly="true"></el-input>
                </el-form-item>
                <el-form-item label="composer">
                    <el-input v-model="formData.workComposers" placeholder="" :readonly="true"></el-input>
                </el-form-item>
                <el-form-item label="author">
                    <el-input v-model="formData.workAuthors" placeholder="" :readonly="true"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="save">確定</el-button>
                </el-form-item>
            </el-form>
            <!-- work options弹窗 -->
            <el-dialog title="Work" :visible.sync="showWorkDialog" width="70%" :close-on-click-modal="false" :before-close='cancelEdit'>
                <div style="width: 100%;display: flex;justify-content: center;">
                    <el-input @keyup.enter.native="getWork(1)" v-model="searchWorks.title" placeholder="work title"
                              style="width: 160px;margin-right: 20px;"></el-input>
                    <el-input @keyup.enter.native="getWork(1)" v-model="searchWorks.workId" placeholder="work no"
                              style="width: 160px;margin-right: 20px;"></el-input>
                    <el-input @keyup.enter.native="getWork(1)" v-model="searchWorks.soc" placeholder="work soc"
                              style="width: 160px;margin-right: 20px;"></el-input>
                    <el-button type="primary" @click="getWork(1)">F</el-button>
                    <span style="padding-top: 15px;margin-left: 10px;" class="clear-search" @click="clearSearch()">清除搜索</span>
                </div>
                <el-table
                    :data="workList"
                    stripe
                    style="width: 100%;margin-top: 10px;"
                    :empty-text="emptyText">
                    <el-table-column
                        prop="title"
                        label="title"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="title_en"
                        label="title_en"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="work_id"
                        label="work no"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="work_society_code"
                        label="soc"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="artist"
                        label="artist"
                    >
                        <template slot-scope="scope">
                            {{scope.row.artist.join(';')}}
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="composer"
                        label="composer"
                    >
                        <template slot-scope="scope">
                            {{scope.row.composer.join(';')}}
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="author"
                        label="author"
                    >
                        <template slot-scope="scope">
                            {{scope.row.author.join(';')}}
                        </template>
                    </el-table-column>
                    <el-table-column
                        label="operation"
                        align="center"
                    >
                        <template slot-scope="scope">
                            <i class="el-icon-check" @click="chooseWork(scope.row)" style="cursor:pointer;"></i>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination
                    background
                    layout="prev, pager, next"
                    :total="workTotal" @current-change="handleWorkChange" :current-page="workPage">
                </el-pagination>
            </el-dialog>
        </div>
    </div>
</template>

<script>
import {Loading} from 'element-ui'
    export default {
        name: 'addWork',
        data () {
            return {
                formData: {
                    distNo: '',
                    listSourceCode:'',
                    sourceDescription:'',
                    listCategoryCode: '',
                    categoryDescription: '',
                    workRightType: '',
                    workTitle: '',
                    workId: '',
                    workSocietyCode: '',
                    workArtists: '',
                    workAuthors: <AUTHORS>
                    isInclude: 'N',
                    workComposers: '',
                },
                searchWorks:{
                    workTitle:'',
                    workId:'',
                    workSocietyCode:'',
                },
                showWorkDialog: false,
                workTotal: 0,
                workPage:1,
                workList: [],
                pageNum: 1,
                pageSize: 10,
                isEdit:false,
                emptyText: '暫無數據',
                SourceOptions:[],
                CategoryOptions:[],
                loading:null
            }
        },
        mounted(){
            this.isEdit = this.$route.name == 'distribute-editwork'
            if(this.isEdit){
                this.formData = JSON.parse(this.$route.query.row)
                this.getSourceList(this.formData.listSourceCode,data=>{
                    if(data.length){
                        this.formData.listSourceCode = data[0].sourceName
                        this.$set(this.formData,'sourceDescription',data[0].sourceDesc)
                    }
                })
                this.getCategoryList(this.formData.listCategoryCode,data=>{
                    if(data.length){
                        this.formData.listCategoryCode = data[0].categoryCode
                        this.$set(this.formData,'categoryDescription',data[0].categoryDesc)
                    }
                })
            }
            
        },
        methods: {
            clearSearch(){
                this.searchWorks={
                    title:'',
                    workId:'',
                    soc:''
                }
                this.getWork()
            },
            // 獲取list category列表
            getCategoryList (query, cb) {
                if (!query) {
                    this.$set(this.formData,'categoryDescription',val.sourceDesc)
                    return false
                }
                this.$http.get('/distributionSpecialCondition/listCategoryList', {params: {categoryCode: query}}).then(res => {
                    if (res.success) {
                        if(res.data.code==200){
                            this.CategoryOptions = res.data.data.filter((item,i,arr)=>item.sourceDesc==this.formData.sourceDescription||item.sourceName==this.formData.listSourceCode)
                            cb(res.data.data.filter((item,i,arr)=>item.sourceDesc==this.formData.sourceDescription||item.sourceName==this.formData.listSourceCode))
                        }else{
                            this.CategoryOptions=[]
                            cb([])
                        }
                    }
                })
            },
            getSourceList (query, cb) {
                if (!query) {
                    this.$set(this.formData,'sourceDescription',query)
                    return false
                }
                this.$http.get('/distributionSpecialCondition/listSourceList',{params: {sourceName: query}}).then(res => {
                    console.log(res)
                    if (res.success) {
                        if (res.data.code == 200) {
                            this.SourceOptions = res.data.data
                            cb(res.data.data)
                        } else {
                            this.SourceOptions = []
                        }
                        
                    }
                })

            },
            handleSelectSource(val){
                console.log('1111',val)
                console.log(this.SourceOptions)
                this.SourceOptions.forEach(item=>{
                    if(item.sourceName == val){
                        this.formData.listSourceCode = item.sourceName
                        this.$set(this.formData,'sourceDescription',item.sourceDesc)
                        this.$set(this.formData,'listCategoryCode','')
                        this.$set(this.formData,'categoryDescription','')
                        this.CategoryOptions=[]
                    }

                })
                // this.formData.listSourceCode = val.sourceName
                // // this.formData.sourceDescription = val.sourceDesc
                // this.$set(this.formData,'sourceDescription',val.sourceDesc)
            },
            chooseWork (row) {
                console.log(row)
                this.formData.workId = row.work_id
                this.formData.workSocietyCode = row.work_society_code
                this.formData.workTitle = row.title || row.title_en
                this.formData.workArtists = row.artist.join(';')
                this.formData.workComposers = row.composer.join(';')
                this.formData.workAuthors = row.author.join(';')
                this.showWorkDialog = false
            },
            handleSelect (val) {
                console.log('2222222',val)
                // formData.listSourceCode
                this.CategoryOptions.forEach(item=>{
                    if(item.categoryCode == val){
                        this.formData.listCategoryCode = item.categoryCode
                        this.$set(this.formData,'categoryDescription',item.categoryDesc)
                    }

                })
                // this.formData.listCategoryCode = val.categoryCode
                // this.formData.categoryDescription = val.categoryDesc
                // this.$set(this.formData,'categoryDescription',val.categoryDesc)
            },
            cancelEdit(){
                this.showWorkDialog = false
                this.loading.close();

            },
            optionsShow(){
                this.showWorkDialog = true
                this.searchWorks={
                    title:this.formData.workTitle,
                    workId:this.formData.workId,
                    soc:this.formData.workSocietyCode
                }
                this.workList = []
                this.workTotal = 1
                this.workPage = 1
                this.emptyText = '暫無數據';
                this.getWork()
            },
            getWork (num) {
                this.loading = Loading.service({
                    lock: true,
                    text: 'Loading',
                    background: 'rgba(0,0,0,0.3)'
                })
                let params = this.$utils.copy(this.searchWorks)
                params.page={
                    pageNum: num ? num : 1,
                    pageSize: this.pageSize
                }
                // let params = {
                //     soc: this.formData.workSocietyCode,
                //     title: this.formData.workTitle,
                //     workId: this.formData.workId,
                //     page: {
                //         // pageNum: this.pageNum,
                //         pageNum: num ? num : 1,
                //         pageSize: this.pageSize
                //     }
                // }
                this.emptyText = '數據加載中';
                this.$http.post('/wrk/queryWrkWorkListEs', params).then(res => {
                    console.log(res)
                    this.loading.close();
                    if (res.success) {
                        let list = res.data.list
                        list.map(item => {
                            if (!item.author) {
                                item.author = []
                            }
                        })
                        this.workList = list
                        this.workTotal = res.data.total
                        this.workPage = num ? num : 1
                        if(! this.workList || this.workList.length == 0){
                            this.emptyText = '暫無數據';
                        }
                    }
                })
            },
            handleWorkChange (page) {
                // this.pageNum = page
                this.getWork(page)
            },
            save () {
                this.$http.post('/distributionSpecialCondition/saveDistListFilterWork', this.formData).then(res => {
                    console.log(res)
                    if (res.success) {
                        if (res.data.code === 200) {
                            this.$message({
                                message: this.isEdit?'修改成功':'新增成功',
                                type: 'success',
                                duration:2000,
                                onClose:() => {
                                    this.$bus.$emit('closeCurrentTab', () => { this.$router.push({name: 'distribute-condition'})});
                                }
                            })
                        } else {
                            this.$message({
                                message: res.data.message,
                                type: 'warning'
                            })
                        }
                    }
                })
            }
        },
        beforeRouteEnter (to, from, next) {
            next(vm => {
                if(to.params.distNo){
                    vm.formData.distNo = to.params.distNo
                }

            })
        }
    }
</script>

<style scoped>
    /deep/ .el-form-item__label {
        margin-left: 0 !important;
    }

    /deep/ .el-input.is-disabled .el-input__inner {
        color: #000;
    }
</style>
