<template>
    <div class="ipibpox">
        <el-form :inline="true" :model="search" ref="form" class="demo-form-inline" @keyup.enter.native="searchFn(1)">
            <!-- <el-form-item>
                <el-select v-model="dataType" placeholder="所有類型" style="width: 120px;">
                    <el-option value="AVR" label="AVR"></el-option>
                    <el-option value="CWR" label="CWR"></el-option>
                </el-select>
            </el-form-item> -->
      <el-form-item prop="id">
        <el-input v-model.trim="search.id" placeholder="ID" style="width: 180px;"></el-input>
      </el-form-item>
      <el-form-item prop="File Name">
        <el-input v-model.trim="search.name" placeholder="File Name" style="width: 180px;"></el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="search.status" placeholder="所有狀態" style="width: 120px;">
          <el-option label="全部狀態" value=""></el-option>
          <el-option v-for=" (value, key) in config.status" :key="key" :value="key" :label="value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="searchFn(1)" v-if="isAuth('duplicateRemoval:uploadList:findCWR')||isAuth('duplicateRemoval:uploadList:findAVR')">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <!-- <el-upload
                    class="upload-demo"
                    :action="'api' + uploadUrl"
                    :on-success="uploadSuccess"> -->
                    <!-- <el-button size="small" type="success">上傳{{dataType == 'cwr' ? 'CWR' : 'AVR'}}</el-button> -->
                    <el-button size="small" type="success" @click="uploadFn" v-if="isAuth('duplicateRemoval:uploadList:exportAVR')||isAuth('duplicateRemoval:uploadList:exportCWR')">上傳{{dataType == 'cwr' ? 'CWR' : 'AVR'}}</el-button>
                <!-- </el-upload> -->
            </el-form-item>
            <el-form-item>
                <span class="clear-search" @click="clearSearch()" v-if="isAuth('duplicateRemoval:uploadList:findCWR')||isAuth('duplicateRemoval:uploadList:findAVR')">清除搜索</span>
            </el-form-item>
        </el-form>
        <el-table stripe :data="tableData" border style="width: 100%" :empty-text="emptyText">
            <el-table-column
                prop="id"
                label="ID"
                width="80px">
            </el-table-column>
            <el-table-column
                prop="name"
                label="File Name">
                <template slot-scope="scope">
                    <span :title="scope.row.name">{{scope.row.name}}</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="amendUserName"
                label="Amend Name">
            </el-table-column>
            <el-table-column
                prop="createTime"
                label="Create Time">
                <template slot-scope="scope">
                    {{scope.row.createTime ? scope.row.createTime : ''}}
                </template>
            </el-table-column>
            <el-table-column
                prop="errorMsg"
                label="Error Msg"
                min-width="200px">
            </el-table-column>
            <el-table-column
                prop="status"
                label="Status"
                width="100px">
                <template slot-scope="scope">
                    {{scope.row.status | Status}}
                </template>
            </el-table-column>
            <el-table-column
                fixed="right"
                label="operation"
                width="100">
                <template slot-scope="scope">
                    <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer" v-if="(isAuth('duplicateRemoval:uploadList:delCWR')||isAuth('duplicateRemoval:uploadList:delAVR'))&&scope.row.status == 2">
                        <el-button @click="handleDelete(scope.row, scope.$index)" type="text" size="small">删除</el-button>
                    </span>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :total="total" @current-change="searchFn" :current-page="currentPage">
        </el-pagination>
        <el-dialog :close-on-click-modal='false' :visible.sync="showUpload" width="600px" :title="'上傳' + (dataType == 'cwr' ? 'CWR' : 'AVR') + '文件'">
            <el-upload
                class="upload-demo"
                drag
                :file-list="fileList"
                :action="'api' + uploadUrl"
                :on-success="uploadSuccess">
                <i class="el-icon-upload"></i>
                <div class="el-upload__text">將文件拖到此處，或<em>點擊上傳</em></div>
            </el-upload>
        </el-dialog>
    </div>
</template>

<script>
    export default {
        name: 'sampleDateList',
        data () {
            return {
                tableData: [],
                total: 0,
                currentPage: 1,
                dataType: 'AVR',
                search: {
                    name: '',
                    status: ''
                },
                dateRange:[],
                loading: false,
                config: {
                    status: {
                        0: '等待處理',
                        1: '處理完成',
                        2: '處理失敗'
                    }
                },
                fileList: [],
                showUpload: false,
                emptyText: '數據加載中',
            }
        },
        created(){
            this.init();
        },
        filters: {
            Status: function(status){
                let config = {
                    0: '等待處理',
                    1: '處理完成',
                    2: '處理失敗'
                }
                return config[status];
            }
        },
        watch: {
            dataType: function(){
                
            }
        },
        methods: {
            uploadFn(){
                this.showUpload = true;
            },
            clearSearch(){
                this.search = {
                    name: '',
                    status: ''
                }
                this.searchFn();
            },
            init(){
                this.dataType = this.$route.query.type;
                this.queryUrl = this.dataType == 'cwr' ? '/cwr/cwrFile/getAvrFileList' : '/avr/cwrFile/getAvrFileList';
                this.uploadUrl = this.dataType == 'cwr' ? '/cwr/cwrFile/upload' : '/avr/cwrFile/upload';
                this.searchFn();
            },
            searchFn (page) {
                this.loading = true;
                let ajaxData = {};
                ajaxData.data = this.$utils.copy(this.search);
                ajaxData.page= {
                    pageNum: page ? page : 1,
                    pageSize: 10
                };
                this.emptyText = '數據加載中';
                this.$http.post(this.queryUrl, ajaxData).then(res => {
                    if (res.success) {
                        this.tableData = res.data.list;
                        if(! this.tableData || this.tableData.length == 0){
                            this.emptyText = '暫無數據';
                        }
                        this.total = res.data.total;
                        this.currentPage = page ? page : 1;
                    }
                    this.loading = false;
                })
            },
            uploadSuccess(res){
                if(res){
                    if(res.code == 200){
                        this.$toast({tips: '上傳成功'});
                        this.searchFn(1);
                        this.showUpload = false;
                        this.fileList = [];
                    }else{
                        this.$toast({tips: res.message});
                        this.fileList = [];
                    }
                    
                }
                
            },
            // fileChange (file, fileList) {
            //     this.fileList = []
            //     fileList.map(item => {
            //         this.fileList.push(item.raw)
            //     })
            //     console.log('------------')
            // },
            // 删除
            handleDelete (row, index) {
                this.$msgbox.confirm('確定刪除?', '提示', {
                    confirmButtonText: '確定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.deleteFn(row.id, index);
                }).catch(() => {        
                });
            },
            deleteFn(id, index){
                let url = '';
                if(this.dataType == 'cwr'){
                    url = '/cwr/cwrFile/del/'
                }else{
                    url = "/avr/cwrFile/del/"
                }
                this.$http.delete( url + id).then( res => {
                    if(res.success){
                        this.tableData.splice(index, 1);
                        this.$toast({tips: '刪除成功'})
                    }    
                })
            }
        }
    //   })
    }
//   }
// }
</script>

<style>
</style>
