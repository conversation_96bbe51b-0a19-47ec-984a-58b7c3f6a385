<template>
  <!-- 分配参數配置  -->
  <div>
    <el-form :inline="true" :model="configureForm" :rules="rules" ref="editForm" label-position="right" class="demo-form-inline" label-width="150px">
      <div>
        <div>
          <el-form-item label="分配編號：" prop="distNo">
            {{configureForm.distNo}}
          </el-form-item>
        </div>
        <el-form-item label="清單起始時間" prop="distListStartTime">
          <date-picker v-model="configureForm.distListStartTime" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd" @change='StartTimeChange(configureForm.distListStartTime)'>
          </date-picker>
        </el-form-item>
        <el-form-item label="清單結束時間" prop="distListEndTime">
          <date-picker v-model="configureForm.distListEndTime" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd" @change='EndTimeChange(configureForm.distListEndTime)'>
          </date-picker>
        </el-form-item>
        <el-form-item label="分配時間">
          {{(configureForm.distDate && configureForm.distDate.split(' ')[0]) || '--'}}
        </el-form-item>
        <el-form-item label="分配年份">
          <el-input v-model="configureForm.distYear" readonly></el-input>
        </el-form-item>
      </div>
      <div>
        <el-form-item label="基準匯率" prop="baseExchangeRate">
          <el-input v-model="configureForm.baseExchangeRate" placeholder="exchange rate"></el-input>
        </el-form-item>
        <el-form-item label="基準貨幣" prop="baseCurrency">
          <el-input ref="currencyInput" v-model="configureForm.baseCurrency" placeholder="雙擊查詢" style="width: 100px;" readonly @dblclick.native="getCurrency()"></el-input>
          <el-input v-model="configureForm.baseCurrencyName" placeholder="雙擊查詢" style="width: 180px;" readonly @dblclick.native="getCurrency()"></el-input>
        </el-form-item>
      </div>
      <div class="halfdisc">
        <el-form-item label="分配描述" class="mydetail">
          <el-input type="textarea" v-model="distDescribe"></el-input>
        </el-form-item>
        <!-- <el-form-item label="備註" class="mydetail">
          <el-input type="textarea" v-model="configureForm.remark"></el-input>
        </el-form-item> -->

      </div>
      <p>稅率配置</p>
      <div>
        <!-- <el-form-item label="Tax Year" prop="taxYear">
          <el-input v-model="configureForm.taxYear" maxlength="4" placeholder="Tax Year"></el-input>
        </el-form-item> -->
        <el-form-item label="Sales Tax Rate(%)">
          <el-input v-model="configureForm.salesTaxRate" @keydown.native="$inputNumber" @blur="fmtRate($event,'salesTaxRate')"></el-input>
        </el-form-item>
        <el-form-item label="Local Limit">
          <el-input v-model="configureForm.localLimit" @keydown.native="$inputNumber"></el-input>
        </el-form-item>
        <el-form-item label="Below Limit(%)">
          <el-input v-model="configureForm.belowLimitRate" @keydown.native="$inputNumber" @blur="fmtRate($event,'belowLimitRate')"></el-input>
        </el-form-item>
        <el-form-item label="Above Limit(%)">
          <el-input v-model="configureForm.aboveLimitRate" @keydown.native="$inputNumber" @blur="fmtRate($event,'aboveLimitRate')"></el-input>
        </el-form-item>
        <el-form-item label="Commission(%)">
          <el-input v-model="configureForm.commission" @keydown.native="$inputNumber" @blur="fmtRate($event,'commission')"></el-input>
        </el-form-item>
        <el-form-item label="extra(%)" class="mydetail">
          <el-input type="number" v-model="configureForm.extra" @keydown.native="$inputNumber" @blur="fmtRate($event,'extra')"></el-input>
        </el-form-item>
        <el-form-item label="Adj(%)">
          <el-input v-model="configureForm.adj" @keydown.native="$inputNumber" @blur="fmtRate($event,'adj')"></el-input>
        </el-form-item>
        <el-form-item label="Overseas(%)">
          <el-input v-model="configureForm.overseas" @keydown.native="$inputNumber" @blur="fmtRate($event,'overseas')"></el-input>
        </el-form-item>
        <el-form-item label="SocietyTaxRate(%)">
          <el-input v-model="configureForm.societyTaxRate" @keydown.native="$inputNumber" @blur="fmtRate($event,'societyTaxRate')" @dblclick.native="getSocietyTaxRate()"></el-input>
        </el-form-item >
      </div>
    </el-form>
    <div style="margin-top: 20px" v-if="this.distType !== 'o'">
      <div>
        <el-checkbox v-model="configureForm.upaDist">配置UPA参數</el-checkbox>
        <div v-if="configureForm.upaDist" style="margin-top: 20px;">
          <el-form :inline="true" :model="upaForm" ref="upaForm" :rules="rulesupa" label-position="right" label-width="180px" class="demo-form-inline">
            <el-form-item label="Local Default Amt" prop="localDefaultAmount" class="m20">
              <el-input v-model="upaForm.localDefaultAmount"></el-input>
            </el-form-item>
            <el-form-item label="Overseas Ratio" prop="overseasRatio" class="m20">
              <el-input v-model="upaForm.overseasRatio"></el-input>
            </el-form-item>
            <el-form-item label="Local Total Amt(A)" prop="localTotalAmount" class="m20">
              <el-input v-model="upaForm.localTotalAmount"></el-input>
            </el-form-item>
            <el-form-item label="Overseas Total Amt(A)" prop="overseasTotalAmount" class="m20">
              <el-input v-model="upaForm.overseasTotalAmount"></el-input>
            </el-form-item>
            <el-form-item label="Roy Average" prop="royAverage" class="m20">
              <el-input v-model="upaForm.royAverage"></el-input>
            </el-form-item>
            <el-form-item label="Local Dist Amt(B)" prop="localDistAmount" class="m20">
              <el-input v-model="upaForm.localDistAmount"></el-input>
            </el-form-item>
            <el-form-item label="Overseas Dist Amt(C)" prop="overseasDistAmount" class="m20">
              <el-input v-model="upaForm.overseasDistAmount"></el-input>
            </el-form-item>
            <el-form-item label="Diff(A-B-C)" prop="diff" class="m20">
              <el-input v-model="upaForm.diff"></el-input>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
    <div class="t-c" style="margin-top: 20px;">
      <el-button type="primary" @click="saveInfo" :disabled="status==1||status==11||status==3||status==31||status==41||status==7||status==2||status==4||status==6||status==61">save</el-button>
    </div>
    <el-dialog title="配製税率" :visible.sync="dialogTableVisible" :close-on-click-modal="false">
      <div style="width: 100%;display: flex;justify-content: center;">
        <el-input @keyup.enter.native="getSocietyTaxRate()" placeholder="countryCode" v-model="searchcode" style="width: 160px;margin-right: 20px;"></el-input>
        <el-input @keyup.enter.native="getSocietyTaxRate()" placeholder="countryName" v-model="searchname" style="width: 160px;margin-right: 20px;"></el-input>
        <el-button type="primary" @click="getSocietyTaxRate()">查詢</el-button>
        <el-button type="primary" @click="showRateForm()">新增</el-button>
      </div>
      <!-- <div class="add-box">
            </div> -->
      <el-table :empty-text="tableresult"   :data="rateTable">
        <el-table-column property="countryCode" label="countryCode" width="150"></el-table-column>
        <el-table-column property="countryName" label="countryName" width="300"></el-table-column>
        <el-table-column property="taxRate" label="税率"></el-table-column>
        <el-table-column label="operation" width="100px">
          <template slot-scope="scope">
            <i class="el-icon-edit pointer" @click="showRateForm(scope.row)"></i>
            <i class="el-icon-close pointer delete" @click="deleteRate(scope.row)"></i>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background layout="prev, pager, next" :total='rateTotal' @current-change="handleCurrentChange" :current-page="currentPage">
      </el-pagination>
    </el-dialog>
    <el-dialog :title="rateTitle" :visible.sync="addRateFormVisible" @close="clearForm('rateForm')" :close-on-click-modal="false">
      <div class="add-box">
        <el-form :model="rateForm" ref="rateForm" :rules="rateRules" label-position="left" width="30%" label-width="130px" class="demo-form-inline">
          <el-form-item label="國家代碼" prop="countryCode" class="m20">
            <el-input v-model="rateForm.countryCode" placeholder="請填寫" :disabled="isEdit"></el-input>
          </el-form-item>
          <el-form-item label="國家名稱" prop="countryName" class="m20">
            <el-input v-model="rateForm.countryName" placeholder="請填寫" :disabled="isEdit"></el-input>
          </el-form-item>
          <el-form-item label="税率" prop="taxRate" class="m20">
            <el-input v-model="rateForm.taxRate" placeholder="請填寫"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="addRateForm">確定</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-dialog>
    <select-currency ref="selectCurrency" :search="currencySearch" v-if="currencyShow" @checkCurrency="checkCurrency"></select-currency>
  </div>
</template>

<script>
import selectCurrency from '@/components/select-currency'
export default {
  data() {
    return {
      tableresult: ' ',
      searchcode: '',
      searchname: '',
      distDescribe: '',
      configureForm: {
        upaDist: false,
        distYear: '',
        baseCurrency: 'TWD',
        baseCurrencyName: 'TAIWAN DOLLAR',
        baseExchangeRate: 1
      },
      distType:'',
      upaForm: {},
      dialogTableVisible: false,
      addRateFormVisible: false,
      rateTable: [],
      rateForm: {

      },
      rateTitle: '新增税率',
      isEdit: false,
      distNo: '',
      status: '',
      rules: {
        distNo: [
          { required: true, message: '请輸入distNo', trigger: 'blur' }
        ],
        distListStartTime: [
          { required: true, message: '请輸入起始時間', trigger: 'blur' },
          { message: '例：20200101', trigger: 'blur' }
        ],
        distListEndTime: [
          { required: true, message: '请輸入結束時間', trigger: 'blur' },
          { message: '例：20200101', trigger: 'blur' }
        ],
        baseCurrency: [
          { required: true, message: '请輸入基準貨幣', trigger: 'blur' }
        ],
        baseExchangeRate: [
          // { required: true, message: '请輸入合法基準匯率', trigger: 'blur',pattern:/^[0-9]+(.[0-9]+)?$/ },
          { required: true, message: '请輸入合法基準匯率', trigger: 'blur', pattern: /^\d+(\.\d{0,6})?$/ },
        ]
        // taxYear: [
        //   { required: true, message: '请輸正确年份', trigger: 'blur', pattern: /^[12][0-9]{3}$/ },
        // ]
      },
      rules1: {
        localAmount: [
          { required: true, message: '請輸入', trigger: 'blur' }
        ],
        amountPercent: [
          { required: true, message: '請輸入', trigger: 'blur' }
        ],
        localTotalAmount: [
          { required: true, message: '請輸入', trigger: 'blur' }
        ],
        overseasTotalAmount: [
          { required: true, message: '請輸入', trigger: 'blur' }
        ],
      },
      rulesupa: {
        localDefaultAmount: [
          { required: true, message: '請輸入', trigger: 'blur' }
        ],
        localTotalAmount: [
          { required: true, message: '請輸入', trigger: 'blur' }
        ],
      },
      rateRules: {
        countryCode: [
          { required: true, message: '請輸入', trigger: 'blur' }
        ],
        countryName: [
          { required: true, message: '請輸入', trigger: 'blur' }
        ],
        taxRate: [
          { required: true, message: '請輸入', trigger: 'blur' }
        ]
      },

      currencySearch: {},
      currencyShow: false,

      rateTotal: 0,
      currentPage: 1
    }
  },
  components: {
    selectCurrency
  },
  created() {
    this.distNo = this.$route.query.distNo;
    this.status = this.$route.query.status;
    this.distType = this.$route.query.distNo.slice(0, 1).toLowerCase();
    this.queryInfo();
  },
  activated() {
    if (this.distNo != this.$route.query.distNo) {
      this.distNo = this.$route.query.distNo;
      this.status = this.$route.query.status;
      this.queryInfo();
    }
  },
  methods: {
    StartTimeChange(data) {
      this.$emit('StartTime', data);
    },
    EndTimeChange(data) {
      this.$emit('EndTime', data);
    },
    getCurrency() {
      this.currencySearch = {
        currencyCode: this.configureForm.baseCurrency
      }
      this.currencyShow = true;
      this.$nextTick(() => {
        this.$refs.selectCurrency.init(); 
      })
    },
    fmtRate(e, prop) {
      // console.log('&&',e,'**',prop)
      let val = Number(e.target.value)
      if (val > 100) {
        val = '100.00'
      } else if (val < 0) {
        val = '0.00'
      } else {
        val = String(val.toFixed(2))
      }
      this.configureForm[prop] = val
    },
    checkCurrency(info) {
      this.$set(this.configureForm, 'baseCurrency', info.currencyCode);
      this.$set(this.configureForm, 'baseCurrencyName', info.currencyName);
      this.$refs.currencyInput.focus();
      this.$refs.currencyInput.blur();

    },
    queryInfo() {
      this.$http.get('/dist/param/info/distNo?distNo=' + this.$route.query.distNo).then(res => {
        if (res.success) {
          this.distDescribe = res.data.distDescribe;
          this.configureForm = res.data.distParamInfo ? res.data.distParamInfo : { upaDist: false, baseCurrency: 'TWD', baseExchangeRate: 1 };
          if (res.data.distParamInfo) {
            this.configureForm = res.data.distParamInfo;
            this.$emit('StartTime', this.configureForm.distListStartTime);
            this.$emit('EndTime', this.configureForm.distListEndTime);
            this.configureForm.salesTaxRate = (+this.configureForm.salesTaxRate).toFixed(2)
            this.configureForm.belowLimitRate = (+this.configureForm.belowLimitRate).toFixed(2)
            this.configureForm.aboveLimitRate = (+this.configureForm.aboveLimitRate).toFixed(2)
            this.configureForm.commission = (+this.configureForm.commission).toFixed(2)
            this.configureForm.extra = (+this.configureForm.extra).toFixed(2)
            this.configureForm.adj = (+this.configureForm.adj).toFixed(2)
            this.configureForm.overseas = (+this.configureForm.overseas).toFixed(2)
            this.configureForm.societyTaxRate = (+this.configureForm.societyTaxRate).toFixed(2)
            //  console.warn("&&&",this.configureForm)
            this.configureForm.baseCurrency = this.configureForm.baseCurrency ? this.configureForm.baseCurrency : 'TWD';
            this.configureForm.baseCurrencyName = this.configureForm.baseCurrencyName ? this.configureForm.baseCurrencyName : 'TAIWAN DOLLAR';
            this.configureForm.baseExchangeRate = this.configureForm.baseExchangeRate ? this.configureForm.baseExchangeRate : 1;
            this.configureForm.upaDist = this.configureForm.upaDist ? this.configureForm.upaDist : false;
            // this.configureForm.distYear = res.data.distYear || '';
             this.$set(this.configureForm, 'distYear', res.data.distYear);
            // this.configureForm.distYear = 1111
          } else {
            this.configureForm = { upaDist: false, baseCurrency: 'TWD', baseCurrencyName: 'TAIWAN DOLLAR', baseExchangeRate: 1 };
          }
          this.configureForm.upaDist = (this.configureForm.upaDist && this.configureForm.upaDist !== '0') ? true : false;
          this.upaForm = res.data.distUpaParam ? res.data.distUpaParam : {};
        }
      })
    },
    saveInfo() {
      let canSave = true
      if (this.configureForm.upaDist) {
        this.$refs.upaForm.validate(validate => {
          if (validate) {
            canSave = true
          } else {
            canSave = false
          }
        })
      }
      this.$refs.editForm.validate(validate => {
        if (validate && canSave) {
          let ajaxData = {
            distDescribe: this.distDescribe,
            distParamInfo: this.$utils.copy(this.configureForm),
            distUpaParam: this.$utils.copy(this.upaForm),
            distYear: this.configureForm.distYear
          }
          let arr = Object.keys(ajaxData.distUpaParam);
          if (arr.length == 0) {
            ajaxData.distUpaParam = null;
          }
          ajaxData.distParamInfo.upaDist = ajaxData.distParamInfo.upaDist ? 1 : 0;
          this.$http.post('/dist/param/info', ajaxData).then(res => {
            console.warn("resresres", res)
            if (res.data.code && res.data.code != 200) {
              this.$toast({ tips: res.data.message });
            } else {
              this.$toast({ tips: '分配参數保存成功' });
              console.log(res.data)
              //保存成功后，用返回的数据 刷新页面数据，以将新建 改为 修改
              this.distDescribe = res.data.distDescribe;
              this.configureForm = res.data.distParamInfo ? res.data.distParamInfo : { upaDist: false, baseCurrency: 'TWD', baseExchangeRate: 1 };
              this.configureForm.upaDist = res.data.distUpaParam ? true : false;
              this.upaForm = res.data.distUpaParam ? res.data.distUpaParam : {};
              this.queryInfo()
            }
          })
        }
      })


    },
    handleCurrentChange(val) {
      this.currentPage=val
      this.getSocietyTaxRate(val)
    },
    getSocietyTaxRate(page) {
      page = page ? page : 1;
            this.tableresult = '數據加載中...'
      this.$http.get('/ref/society/tax/rate', { params: { countryCode: this.searchcode, countryName: this.searchname, page_num: page, page_size: 10 } }).then(res => {
        if (res.success) {
          this.rateTable = res.data.list;
          this.dialogTableVisible = true;
          this.rateTotal = res.data.total;
          console.log('curr: ', page);
          this.currentPage = page ? page : 1;
            this.tableresult = this.rateTable.length == 0 ? '暫無數據' : ' '
        }
      })
    },
    selectRate(index, row) {
      this.configureForm.societyTaxRate = row.taxRate
      this.dialogTableVisible = false
    },
    showRateForm(row = {}) {
      this.addRateFormVisible = true
      if (row.id) {
        this.rateTitle = '編輯税率'
        this.isEdit = true
      } else {
        this.rateTitle = '新增税率'
        this.isEdit = false
      }
      this.rateForm = row
    },
    addRateForm() {
      this.$refs.rateForm.validate(validate => {
        if (validate) {
          this.$http.post('/ref/society/tax/rate/saveOrUpdateRefSocietyTaxRate', this.rateForm).then(res => {
            if (res.success) {
              if (res.data.id) {
                this.$message({
                  message: this.rateTitle + '成功',
                  type: 'success',
                  duration: 500,
                  onClose: () => {
                    this.addRateFormVisible = false
                    this.getSocietyTaxRate()
                  }
                });
              } else {
                this.$message.error(res.data.message);
              }
            } else {

            }

          })
        }
      })
    },
    deleteRate(row) {
      this.$alert('確定要刪除嗎？', '刪除', {
        confirmButtonText: '确定',
        showCancelButton: true,
        callback: action => {
          if (action == 'confirm') {
            this.$http.delete('/ref/society/tax/rate/' + row.id).then(res => {
              if (res.success) {
                this.$message({
                  message: '刪除成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.getSocietyTaxRate()
                  }

                });

              } else {
                this.$message.error('刪除失敗');
              }
            })
          }
        }
      });

    },
    clearForm(name) {
      // this.$refs[name].resetFields()
      this.getSocietyTaxRate()
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/ .el-form-item__label {
  margin-left: 0;
}
.add-box {
  padding-bottom: 20px;
}
.delete {
  margin-left: 20px;
}
.m20 {
  margin-bottom: 25px;
}
#halfdisc {
  margin-left: 300px;
}
</style>
