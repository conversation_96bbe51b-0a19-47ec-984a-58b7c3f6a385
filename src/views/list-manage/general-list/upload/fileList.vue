<template>
  <!-- 原文件 處理後 拆分成的多個小文件 -->
  <div class="ipibpox">
    <el-form :inline="true" :model="formData" ref="form" class="demo-form-inline"  @keyup.enter.native='searchFn()'>
      <el-form-item prop="fid">
        <el-input v-model="formData.fid" placeholder="FID" style="width: 120px;"></el-input>
      </el-form-item>
      <el-form-item prop="fileQueueId">
        <el-input v-model="formData.fileQueueId" placeholder="fileQueueId" style="width: 120px;"></el-input>
      </el-form-item>
      <el-form-item prop="upload type" v-if="fileType == 'P'">
        <el-input v-model="formData.uploadType" placeholder="upload type"></el-input>
      </el-form-item>
      <el-form-item prop="title">
        <el-input v-model="formData.title" placeholder="title"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="searchFn()">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <span class="clear-search" @click="clearSearch()">清除搜索</span>
      </el-form-item>
    </el-form>
    <el-table :empty-text="tableresult"   stripe :data="tableData" border style="width: 100%">
      <el-table-column prop="id" label="FID">
      </el-table-column>
      <el-table-column prop="fileQueueId" label="file_quene_id">
        <template slot="header" slot-scope="scope">
          <span title="file_quene_id">file_quene_id</span>
        </template>
      </el-table-column>
      <el-table-column prop="title" label="title" width="300px">
        <template slot-scope="scope">
          <p class="over-line" :title="scope.row.title" v-if="fileType == 'P'">{{scope.row.title}}</p>
          <p class="over-line" :title="scope.row.title+'-'+scope.row.sourceCompany" v-else>{{scope.row.sourceCompany+'-'+scope.row.title}}</p>
        </template>
      </el-table-column>
      <!-- todo -->
      <el-table-column prop="categoryCode" label="Category" width="126px">
        <template slot="header" slot-scope="scope">
          <span title="Category">Category</span>
        </template>
        <template slot-scope="scope">
          <span :title="scope.row.categoryCode">{{scope.row.categoryCode}}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="fileType == 'P'" prop="uploadType" label="upload type">
      </el-table-column>
      <el-table-column prop="numberOfLines" label="number_of_lines">
        <template slot="header" slot-scope="scope">
          <span title="number_of_lines">number_of_lines</span>
        </template>
      </el-table-column>
      <el-table-column prop="uploadUserName" label="upload user">
      </el-table-column>
      <el-table-column prop="createTime" width="180" label="upload time">
        <template slot-scope="scope">
          <span :title="scope.row.createTime">{{scope.row.createTime}}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="fileType != 'P'" prop="listFileEndTime" width="120" label="perform date">
        <template slot-scope="scope">{{scope.row.listFileEndTime | splitDate}}</template>
      </el-table-column>
      <el-table-column prop="matched" label="matched">
        <template slot="header" slot-scope="scope">
          <span title="matched">matched</span>
        </template>
      </el-table-column>
      <el-table-column prop="unmatched" label="unmatched">
        <template slot="header" slot-scope="scope">
          <span title="unmatched">unmatched</span>
        </template>
      </el-table-column>
      <el-table-column prop="clickNumber" label="click number">
      </el-table-column>
      <el-table-column v-if="fileType != 'P'" prop="dist" label="is dist">

      </el-table-column>
      <el-table-column fixed="right" label="operation" width="200">
        <template slot-scope="scope">
          <el-button @click="switchDist(scope.row)" type="text" v-if="fileType != 'P'" size="small">dist</el-button>
          <el-button @click="audit(scope.row)" type="text" size="small">查看明細</el-button>
          <el-button @click="editBase(scope.row)" v-if="fileType != 'S'" type="text" size="small">修改類型</el-button>
          <el-button @click="editCategory(scope.row)" v-if="fileType != 'S'" type="text" size="small">修改Category</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination background layout="prev, pager, next" :total="total" @current-change="handleCurrentChange">
    </el-pagination>
    <el-dialog title="修改upload type" :visible.sync="dialogVisible" width="40%" :close-on-click-modal="false">
      <el-select v-model="uploadType" placeholder="请选择">
        <el-option label="PG" value="PG"></el-option>
        <el-option label="MS" value="MS"></el-option>
        <el-option label="FW" value="FW"></el-option>
        <el-option label="CJ" value="CJ"></el-option>
      </el-select>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="saveBase">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="修改Category Code" :visible.sync="dialogVisibleCode" width="40%" :close-on-click-modal="false">
      <el-select v-model="categoryCode" placeholder="Category Code" filterable >
        <el-option
          v-for="item in categoryCodeList"
          :key="item.id"
          :label="item.categoryCode"
          :value="item.categoryCode">
          <span style="float: left">{{ item.categoryCode }}</span>
          <span style="float: right; color: #8492a6; font-size: 13px">{{ item.categoryDesc }}</span>
      </el-option>
      </el-select>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleCode = false">取 消</el-button>
        <el-button type="primary" @click="saveCategory">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'sampleDateList',
  data() {
    return {
      fileType: '',
      tableData: [],
      tableresult:' ',
      total: 0,
      formData: {
        fileQueueId: '',
        categoryCode: '',
        title: '',
        fid: '',
        uploadType: '',
        page: {
          pageNum: 1,
          pageSize: 10
        }
      },
      targetRow: null,
      uploadType: '',
      dialogVisible: false,
      dialogVisibleCode: false,
      categoryCode: '',
      categoryCodeList:[]
    }
  },
  beforeMount() {
    this.fileType = this.$route.query.type
  },
  mounted() {
    this.init();
  },
  methods: {
    clearSearch() {
      this.init()
    },
    init() {
      this.formData = {
        fileQueueId: this.$route.query.id,
        categoryCode: '',
        title: '',
        fid: '',
        uploadType: '',
        page: {
          pageNum: 1,
          pageSize: 10
        }
      }
      this.searchFn();
      this.initSelect();
    },
    initSelect(){
        this.$http.get('/list/categorys/getAll').then(res => {
            if(res.success){
                console.log(res)
                this.categoryCodeList = res.data.data
            }
        })
    },
    handleSelect(item) {
      console.log(item);
    },
    switchDist(row) {
      let dist = row.dist == 'N' ? 'Y' : 'N'
      let params = {
        fid: row.id,
        isDist: dist,
        categoryCode: row.categoryCode
      }
      this.$http.post('/listBasicFileBase/updateIsDist', params).then(res => {
        this.$toast({ tips: res.data.message })
        if (res.data.code == 200) {
          row.dist = dist
        }
      })
    },
    searchFn() {
            this.tableresult = '數據加載中...'
      this.$http.post('/listBasicFileBase/getListBasicFileBaseList', this.formData).then(res => {
        console.log(res)
        let list = res.data.data.list
        list.map(item => {
          if (item.extJson) {
            item.playDate = JSON.parse(item.extJson).playDate
          }
        })
        this.tableData = list
              this.tableresult = this.tableData.length == 0 ? '暫無數據' : ' '
        this.total = res.data.data.total
      })
    },
    handleCurrentChange(val) {
      this.formData.page.pageNum = val
      this.searchFn();
    },
    audit(row) {
      if (this.fileType == 'S') {
        this.$router.push({ name: 'fileDetail', query: { id: row.id, nameId: row.id, title: row.id, type: this.fileType, socName: row.title, company: row.sourceCompany } })
      } else {
        this.$router.push({ name: 'fileDetail', query: { id: row.id, nameId: row.id, title: row.id, type: this.fileType } })
      }

    },
    editBase(row) {
      console.log(row)
      this.targetRow = row
      this.dialogVisible = true
      this.uploadType = row.uploadType
    },
    editCategory(row) {
      console.log(row)
      this.targetRow = row
      this.dialogVisibleCode = true
      this.categoryCode = row.categoryCode
    },
    saveBase() {
      if (this.uploadType != this.targetRow.uploadType) {
        this.$http.post('/listBasicFileBase/updateBase', { id: this.targetRow.id, uploadType: this.uploadType }).then(res => {
          console.log(res)
          if (res.data.code == 200) {
            this.dialogVisible = false
            this.searchFn()
          } else {
            this.$toast({ tips: res.data.message })
          }
        })
      } else {
        this.$toast({ tips: '當前的upload type已經為已選擇的類型，無須修改。' })
      }
    },
    saveCategory() {
      if (this.categoryCode != this.targetRow.categoryCode) {
        this.$http.post('/listBasicFileBase/updateCategoryCode', { id: this.targetRow.id, categoryCode: this.categoryCode }).then(res => {
          console.log(res)
          if (res.data.code == 200) {
            this.dialogVisibleCode = false
            this.searchFn()
          } else {
            this.$toast({ tips: res.data.message })
          }
        })
      } else {
        this.$toast({ tips: '當前的categoryCode已經為已選擇的類型，無須修改。' })
      }
    }
  }
}
</script>

<style>
</style>
