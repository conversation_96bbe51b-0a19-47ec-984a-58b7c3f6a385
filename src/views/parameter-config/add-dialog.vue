<template>
    <el-dialog class="parameter-add" title="新增" :visible.sync="isShow" width="40%" :show-close="false" :close-on-click-modal="false">
        <el-form label-position="right" label-width="130px" :model="formInline" class="demo-form-inline" @keyup.enter.native="addItem">
            <el-form-item label="category code:">
                <el-input v-model="formInline.code" placeholder="category code" label="category code"></el-input>
            </el-form-item>
            <el-form-item label="category desc:">
                <el-input v-model="formInline.desc" placeholder="category desc"></el-input>
            </el-form-item>
            <el-form-item label="source name:">
                <el-input v-model="formInline.name" placeholder="source name"></el-input>
            </el-form-item>
            <el-form-item label="dist type:">
                <el-select v-model="formInline.distTypeValue" placeholder="dist type">
                    <el-option
                        v-for="item in distType"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="pool code:">
                <el-select v-model="formInline.poolCodeValue" placeholder="dist type" @change="getPoolRight">
                    <el-option
                        v-for="item in poolCodeList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="pool right:">
                <el-select v-model="formInline.poolRightValue" placeholder="dist type">
                    <el-option
                        v-for="item in poolRightList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="is dist:">
                <el-checkbox v-model="formInline.checked">is dist</el-checkbox>
            </el-form-item>
        </el-form>
        <div style="text-align: center">
            <el-button @click="cancelAdd">取消</el-button>
            <el-button type="primary" @click="addItem">確定</el-button>
        </div>
    </el-dialog>
</template>

<script>
    export default {
        name: 'add-dialog',
        props: {
            isShow: {
                type: Boolean,
                required: true
            }
        },
        data () {
            return {
                formInline:{
                    code: null,
                    desc: null,
                    name: null,
                    poolCodeValue: null,
                    poolRightValue: '',
                    distTypeValue: '',
                    checked: false
                },
                distType: [
                    {
                        value: 'd',
                        label: 'DI'
                    }, {
                        value: 'i',
                        label: 'Int'
                    }, {
                        value: 'm',
                        label: 'Mech'
                    }, {
                        value: 'p',
                        label: 'Perf'
                    }, {
                        value: 'T',
                        label: 'Tvs'
                    }
                ],
                poolCodeList: [
                    {
                        value: 'C',
                        label: 'CONCERT'
                    }, {
                        value: 'D',
                        label: 'download'
                    }, {
                        value: 'F',
                        label: 'Film'
                    }, {
                        value: 'G',
                        label: 'AIRLINE'
                    }, {
                        value: 'I',
                        label: 'PUBLIC TRANSMISSION'
                    }, {
                        value: 'K',
                        label: 'KARAOKE'
                    }, {
                        value: 'O',
                        label: 'OTHERS'
                    }, {
                        value: 'P',
                        label: 'Pre-claim'
                    }, {
                        value: 'R',
                        label: 'RADIO'
                    }, {
                        value: 'T',
                        label: 'TV'
                    }
                ],
                poolRightList: []
            }
        },

        methods: {
            getPoolRight (e) {
                if (e === 'C' || e === 'K' || e === 'F') {
                    this.poolRightList = [
                        {
                            value: 'PR',
                            label: 'PR'
                        }
                    ]
                } else if (e === 'D') {
                    this.poolRightList = [
                        {
                            value: 'DB',
                            label: 'DB'
                        },
                        {
                            value: 'OD',
                            label: 'OD'
                        }
                    ]
                } else if (e === 'G') {
                    this.poolRightList = [
                        {
                            value: 'PC',
                            label: 'PC'
                        }
                    ]
                } else if (e === 'I' || e === 'O') {
                    this.poolRightList = [
                        {
                            value: 'OD',
                            label: 'OD'
                        },
                        {
                            value: 'PC',
                            label: 'PC'
                        }
                    ]
                } else if (e === 'R') {
                    this.poolRightList = [
                        {
                            value: 'RB',
                            label: 'RB'
                        },
                        {
                            value: 'TB',
                            label: 'TB'
                        },
                        {
                            value: 'TO',
                            label: 'TO'
                        },
                        {
                            value: 'TV',
                            label: 'TV'
                        }
                    ]
                } else if (e === 'T') {
                    this.poolRightList = [
                        {
                            value: 'SY',
                            label: 'SY'
                        },
                        {
                            value: 'TB',
                            label: 'TB'
                        },
                        {
                            value: 'TV',
                            label: 'TV'
                        }
                    ]
                }
            },
            cancelAdd(){
                this.$emit('hideAddDialog')
                this.resetDialog()
            },
            addItem(){
                this.$emit('addAction',this.formInline)
                this.resetDialog()
            },
            resetDialog(){
                this.formInline = {
                    code: null,
                    desc: null,
                    name: null,
                    poolCodeValue: null,
                    poolRightValue: '',
                    distTypeValue: '',
                    checked: false
                }
            }
        }
    }
</script>

<style scoped lang="scss">
    /deep/ .el-form-item__label {
        margin-left: 0 !important;
    }
</style>
