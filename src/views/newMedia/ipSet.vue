<template>
    <div>
        <el-form :inline="true" :model="search" class="demo-form-inline" @keyup.enter.native="searchFn()">
            <el-form-item label="Version">
                <el-select v-model="search.versionId" style="width: 150px;">
                    <el-option label="所有" value=""></el-option>
                    <el-option v-for="(value, key) in versions" :label="value.versionName" :value="value.id" :key="key"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="paName">
                <el-input v-model="search.paName" placeholder="" style="width: 140px;"></el-input>
            </el-form-item>
            <el-form-item label="IP Base No">
                <el-input v-model="search.ipBaseNo" placeholder="" style="width: 130px;"></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="searchFn()" v-if="isAuth('newMedia:ipSet:find')">搜索</el-button>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="addFn()" v-if="isAuth('newMedia:ipSet:add')">創建</el-button>
            </el-form-item>
            <el-form-item>
                <span class="clear-search" @click="clearSearch()" v-if="isAuth('newMedia:ipSet:find')">清除搜索</span>
            </el-form-item>
        </el-form>
        <el-table
            ref="test"
            :data="tableData"
            border
            stripe
            style="width: 100%"
            v-loading="loading"
            :empty-text="emptyText">
            <el-table-column
                prop="ipBaseNo"
                label="IP Base No"
                width="180px">
            </el-table-column>
            <el-table-column
                prop="paNameNo"
                label="PA Name No">
            </el-table-column>
            <el-table-column
                prop="paName"
                label="Name">
            </el-table-column>
            <el-table-column
                label="Status">
                <template slot-scope="scope">
                    {{scope.row.status == '1' ? '啟用' : '禁用'}}
                </template>
            </el-table-column>
            <el-table-column
                prop="validFrom"
                label="Valid From">
                <template slot-scope="scope">
                    {{scope.row.validFrom.split(' ')[0]}}
                </template>
            </el-table-column>
            <el-table-column
                prop="validTo"
                label="Valid To">
                <template slot-scope="scope">
                    {{scope.row.validTo.split(' ')[0]}}
                </template>
            </el-table-column>
            <el-table-column
                label="Version">
                <template slot-scope="scope">
                    {{filterVersion(scope.row.versionId)}}
                </template>
            </el-table-column>
            <el-table-column
                label="operation">
                <template slot-scope="scope">
                    <el-button type="text" size="small" @click="editFn(scope.row, scope.$index)" v-if="isAuth('newMedia:ipSet:change')">編輯</el-button>
                    <el-button type="text" size="small" @click="deleteFn(scope.row, scope.$index)" v-if="isAuth('newMedia:ipSet:del')">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :total="total" @current-change="searchFn" :current-page="currentPage">
        </el-pagination>
        <!-- 編輯、添加 -->
        <el-dialog :title="(opType == 'add' ? '添加' : '編輯') + '委任IP'" :visible.sync="show" v-if="show" :width="'500px'" :close-on-click-modal="false">
            <el-form ref="editForm" :inline="true" :model="edit" :rules="rules" label-width="120px" @keyup.enter.native="confirmEdit">
                <el-form-item label="IP Base No" prop="ipBaseNo" >
                    <el-input ref="editIp" type="text" v-model="edit.ipBaseNo" placeholder="雙擊查找" @dblclick.native="getIp()" readonly>
                        <!-- <el-button slot="append" icon="el-icon-search" @click="queryMember"></el-button> -->
                    </el-input>
                </el-form-item>
                <el-form-item label="PA Name No" prop="paNameNo">
                    <el-input type="text" v-model="edit.paNameNo" placeholder="雙擊查找" @dblclick.native="getIp()" readonly></el-input>
                </el-form-item>
                <el-form-item label="Name" prop="paName">
                    <el-input type="text" v-model="edit.paName" placeholder="雙擊查找" @dblclick.native="getIp()" readonly></el-input>
                </el-form-item>
                <el-form-item label="Status" prop="status">
                    <el-select v-model="edit.status">
                        <el-option :value="1" label="啟用"></el-option>
                        <el-option :value="0" label="禁用"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="Valid From" prop="validFrom" >
                   <!-- <el-input v-model="edit.validFrom" v-dateFmt></el-input> -->
                   <date-picker v-model="edit.validFrom" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd"></date-picker>
                </el-form-item>
                <el-form-item label="Valid To" prop="validTo">
                    <!-- <el-input v-model="edit.validTo" v-dateFmt></el-input> -->
                    <date-picker ref="validTo" v-model="edit.validTo" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd" style="display: inline-block;width: 184px;"></date-picker>
                    <el-checkbox v-model="edit.validToLong" style="width: auto;" @change="longChange">不限</el-checkbox>
                </el-form-item>
                <el-form-item label="Version" prop="versionId" >
                    <el-select v-model="edit.versionId">
                        <el-option v-for="(value, key) in versions" :label="value.versionName" :value="value.id" :key="key"></el-option>
                    </el-select>
                </el-form-item>
                <div class="t-c">
                    <el-form-item>
                        <el-button type="primary" @click="confirmEdit">確定</el-button>
                    </el-form-item>
                </div>
            </el-form>
        </el-dialog>
        <!-- getip -->
        <select-ip v-if="selectIpShow" :search="ipSearch" ref="selectIpCom" @checkIp="checkIp"></select-ip>
    </div>
</template>

<script>
    import selectIp from '../../components/select-ip'

    export default {
        name: 'list',
        data () {
            // var validateValidTo = (rule, value, callback) => {
            //     if(!value && !this.edit.validToLong){
            //         return callback(new Error('請輸入Valid To 或 選擇不限'))
            //     }else{
            //         callback();
            //     }
            // }
            return {
                versions: [],
                search: {versionId: '', ipBaseNo: '', paName: ''},
                tableData: [],
                total: 1,
                currentPage: 1,
                pageShow: true,
                // dialog
                show: false,
                opType: 'add',
                edit: {
                    ipBaseNo:'',
                    paNameNo: '',
                    paName: '',
                    status: '',
                    validFrom: '',
                    validTo: '',
                    validToLong: false,
                    versionId: ''
                },
                rules: {
                    ipBaseNo: [{required: true, message: '請選擇Ip Base No', trigger: 'blur'}],
                    status: [{required: true, message: '請選擇Status', trigger: 'change'}],
                    validFrom: [{required: true, message: '請輸入Valid From', trigger: 'blur'}],
                    // validTo: [{validator: validateValidTo, trigger: 'change'}],
                    versionId: [{required: true, message: '請選擇', trigger: 'change'}],
                },
                // getip
                selectIpShow: false,
                ipSearch: {},
                loading: false,
                emptyText: '數據加載中',
            }
        },
        components: {
            selectIp
        },
        watch: {
            'edit.validToLong': function(newVal, oldVal){
                if(newVal){
                    this.edit.validTo = '';
                }
            },
            'edit.validTo': function(newVal, oldVal){
                if(newVal){
                    this.edit.validToLong = false;
                }
            }
        },
        mounted () {
            this.queryVersion();
            this.searchFn();
        },
        methods: {
            clearSearch(){
                this.search= {
                    versionId: '',
                    ipBaseNo: '',
                    paName: '',
                    ip_type: 'L'
                }
                this.searchFn();
            },
            getIp(){
                this.ipSearch = {
                    ip_no: this.edit.ipBaseNo,
                    name_no: this.edit.paNameNo,
                    name: this.edit.paName,
                    ip_type: 'L',
                    name_type: 'PA'
                }
                this.selectIpShow = true;
                this.$nextTick( () =>{
                    this.$refs.selectIpCom.init();
                })
            },
            checkIp(info){
                // this.edit.ipBaseNo = info.ip_base_no;
                // this.edit.paName = info.name;
                // this.edit.paNameNo = info.ip_name_no;
                this.$set(this.edit, 'ipBaseNo', info.ip_base_no);
                this.$set(this.edit, 'paName', info.name);
                this.$set(this.edit, 'paNameNo', info.ip_name_no);
                this.$refs.editIp.focus();
                this.$refs.editIp.blur();
            },
            longChange(){
                // console.log('111');
                // this.$refs.validTo.focus();
                // this.$nextTick( () => {
                //     this.$refs.validTo.blur();
                // })
                
            },
            filterVersion(id){
                let name = '';
                this.versions.forEach( item => {
                    if(item.id == id){
                        name = item.versionName;
                    }
                })
                return name;
            },
            queryMember(){
                if(!this.edit.ipBaseNo){
                    return;
                }
                let params = this.edit.ipBaseNo;
                this.$http.get('/member/' + params).then(res => {
                    if (res.status === 200) {
                        if(res.data){
                            res.data.ip.names.forEach( item => {
                                if(item.nameType == 'PA'){
                                    this.edit.paNameNo = item.nameNo;
                                    this.edit.paName = item.name;
                                }
                            })
                        }else{
                            this.$toast({tips: '請確認IP Base No是否正確'});
                        }

                    }
                })
            },
            queryVersion() {
                let ajaxData = {};
                ajaxData.page_num = 1;
                ajaxData.page_size = 999999;
                this.$http.get('/dist/media/listDistMediaVersionWithPage', {params: ajaxData}).then( res => {
                    if(res.success){
                        this.versions = res.data.data.list;
                    }
                })
            },
            searchFn(page) {
                let ajaxData = this.$utils.copy(this.search);
                ajaxData.page_num = page ? page : 1;
                ajaxData.page_size = 10;
                this.loading = true;
                this.emptyText = '數據加載中';
                this.$http.get('/dist/media/listDistMediaPublisherWithPage', {params: ajaxData}).then( res => {
                    this.loading = false;
                    if(res.success){
                        this.tableData = res.data.data.list;
                        this.total = res.data.data.total;
                        this.currentPage = ajaxData.page_num;
                        if(! this.tableData || this.tableData.length == 0){
                            this.emptyText = '暫無數據';
                        }
                        this.tableData.forEach( item => {
                            if(item.validTo.indexOf('9999') != -1){
                                this.$set(item, 'validToLong', true);
                            }else{
                                this.$set(item, 'validToLong', false);
                            }
                        })
                    }
                })
            },
            addFn(){
                this.edit = {
                    ipBaseNo:'',
                    paNameNo: '',
                    paName: '',
                    status: 1,
                    validFrom: '',
                    validTo: '',
                    validToLong: false,
                    versionId: ''
                };
                this.opType = 'add';
                this.show = true;
                this.$nextTick( () => {
                    this.edit.validToLong = false;
                })
            },
            editFn (row) {
                this.opType = 'edit';
                this.edit = this.$utils.copy(row);
                if(this.edit.validTo.indexOf('9999') != -1){
                    console.log('yes?');
                    this.edit.validTo = '';
                    this.$set(this.edit, 'validToLong', true);
                }else{
                    this.$set(this.edit, 'validToLong', false);
                }
                this.show = true;
            },
            confirmEdit(){
                this.$refs.editForm.validate( (valid) => {
                    if(valid){
                        this.confirmEditFn();
                    }
                })
            },
            confirmEditFn(){
                let ajaxData = this.$utils.copy(this.edit);
                if(!ajaxData.validToLong && !ajaxData.validTo){
                    this.$toast({tips: '請輸入Valid To 或 選擇不限'}) 
                    return;
                }
                if(ajaxData.validToLong){
                    ajaxData.validTo = '9999-12-31 00:00:00';
                }else{
                    if(new Date(ajaxData.validFrom).getTime() >= new Date(ajaxData.validTo).getTime()){
                        this.$toast({tips: '結束日期需大於開始日期'})
                        return;
                    }
                }

                delete ajaxData.validToLong;
                this.$http.post('/dist/media/saveDistMediaPublisher', ajaxData).then(res => {
                    if(res.success){
                        if(res.data.code && res.data.code != 200){
                            this.$toast({tips: res.data.message})
                        }else{
                            this.$toast({tips: (this.opType == 'add'? '添加' : '編輯') +'成功'});
                            this.show = false;
                            this.searchFn(1);
                        }
                    }
                })
            },
            deleteFn (row, index) {
                this.$msgbox.confirm(`確定進行[删除]操作?`, '提示', {
                    confirmButtonText: '確定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$http.post('/dist/media/DelDistMediaPublisher/' + row.id).then( res => {
                        if(res.success){
                            this.$toast({tips: '删除成功'})
                            // this.tableData.splice(index, 1);
                            this.searchFn();
                        }
                    })

                }).catch( () => {

                })
            }
        }
    }
</script>
<style lang="scss" scoped>
    @import "../../assets/scss/works.scss";

/deep/ .el-dialog{
    width: 500px;
}
</style>

