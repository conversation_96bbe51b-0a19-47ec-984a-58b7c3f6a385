/* here modify common directives for this project */

export default function installDirectives(Vue) {
  /* attach this directive to el-table, to make table's body height better fit your browser window. */
  /* How to use:
   * <el-table v-table-flex="{topFix: 150}" height="180"></el-table>
   * attribute height is nessessary to enable el-table's fixed table scroll sync with body table.
   */
  Vue.directive('table-flex', {
    inserted: function (el, binding = {}, vnode) {
      // console.log('v-table-flex:', binding, vnode);
      const directiveValue = binding.value;
      /* judge if the element is a el-table */
      if (vnode.componentOptions.tag !== 'el-table') {
        console.error('v-table-flex: the vnode you use this directive is not a el-table:', vnode);
        return;
      }
      let basePaddings = {
        top: 90,
        bottom: 100
      };
      if (directiveValue.basePaddings) {
        basePaddings = Object.assign(basePaddings, directiveValue.basePaddings);
      }
      if (directiveValue.topFix !== undefined || directiveValue.bottomFix !== undefined) {
        if (!el.classList.contains('v-table-flex')) {
          el.classList.add('v-table-flex');
        }
        const elTable = el;
        const elTableHeader = el.querySelector('.el-table__header');
        const elTableBodyWrapper = el.querySelector('.el-table__body-wrapper');
        // console.log('elTableBodyWrapper:', elTableBodyWrapper);
        if (elTable && elTableHeader && elTableBodyWrapper) {
          const tableHeaderHeight = elTableHeader.offsetHeight;
          const heightFix = (basePaddings.top||0) + (basePaddings.bottom||0) + (directiveValue.topFix||0) + (directiveValue.bottomFix||0);
          const bodyHeightFix = (heightFix||0) - (tableHeaderHeight||44);
          setTimeout(() => {
            elTable.style.height = `calc(100vh - ${heightFix || 0}px)`;
            elTableBodyWrapper.style.height = `calc(100vh - ${heightFix + bodyHeightFix || 0}px)`;
            // console.log('elTable:', elTable.style.height);
            // console.log('elTableBodyWrapper:', elTableBodyWrapper.style.height);
          }, 100);
        } else {
          console.error('v-table-flex: the el-table is not ready yet, please wait for a while and try again.');
        }
      }
    }
  })
}
