    .el-table thead{
        color: #888;
    }
    .el-form-item{
        margin-bottom: 18px ;
    }
    /deep/ .el-collapse-item__wrap{
        border: 1px solid #ddd !important;
        border-radius: 5px;
    }
    .step-jump{
        position: relative;
    }
    .add-new{
        position: absolute;
        right: 50px;
        top: 8px;
        text-align: center;
    }
    .add-new button{
        padding: 8px 16px;
    }
    /deep/ .step-jump .el-table{
        width: 100%; 
        max-height: 320px;
        overflow: auto;
    }
    /deep/ .el-table--medium td, /deep/ .el-table--medium th{
        padding: 2px 0 ;
    }
    /deep/ .el-table .cell, /deep/ .el-table th div, /deep/ .el-table--border td:first-child .cell, /deep/ .el-table--border th:first-child .cell{
        padding-left: 4px;
    }

    /deep/ .el-collapse-item__content{
        padding-bottom: 0;
    }
    /deep/ .component-class label{
        margin-left: 12px;
    }
    /deep/ .component-class .el-form-item{
        margin: 10px 0 20px 0;
    }
    /deep/ .component-class thead th{
        border-top: 1px solid #eee;
    }
    /deep/ .el-form-item{
        margin-right: 4px;
        .el-input{
            /deep/ input{
                padding: 0 4px;
            }
        }
        .el-date-editor{
            span.el-input__prefix{
                left: 0;
            }
            input{
                padding-left: 22px;
            }
        }
        
    }
    /deep/ .el-table .el-input input{
        padding: 0 4px;
    }
    /deep/ .el-form-item__error{
        font-size: 12px;
    }