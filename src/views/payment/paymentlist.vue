<template>
    <!-- 支付列表 -->
    <div>
        <el-form :inline="true" :model="formNum" class="demo-form-inline" @keyup.enter.native="queryList(1)">
            <el-form-item label="支付編號:">
                <el-input v-model="formNum.autopayNo" placeholder style="width: 80px;"></el-input>
            </el-form-item>
            <el-form-item label="支付類別:">
                <el-select v-model="formNum.payType" placeholder="请选择" style="width: 80px">
                    <el-option label="全部" value=""></el-option>
                    <el-option label="會員" value="MBR"></el-option>
                    <el-option label="協會" value="SOC"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="備註:">
                <el-input v-model="formNum.autopayDescription" placeholder></el-input>
            </el-form-item>
            <el-form-item label="支付日期:">
                <date-picker v-model="formNum.autopayDate" value-format="yyyy-MM-dd HH:mm:ss" format="yyyyMMdd" style="width: 160px;"></date-picker>
            </el-form-item>
            <el-form-item label="分配編號:">
                <el-input v-model="formNum.distNoDetails" placeholder style="width: 160px;"></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="queryList(1)" v-if="isAuth('payment:paymentlist:find')">搜 索</el-button>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="addPay" v-if="isAuth('payment:paymentlist:add')">新 增</el-button>
            </el-form-item>
            <el-form-item>
                <span class="clear-search" @click="clearSearch()" v-if="isAuth('payment:paymentlist:find')">清除搜索</span>
            </el-form-item>
        </el-form>
        <el-table :empty-text="tableresult" :data="tableData" border style="width: 100%">
            <el-table-column prop="autopayNo" label="支付編號"></el-table-column>
            <el-table-column prop="payType" label="支付類別">
                <template slot-scope="scope">
                    {{scope.row.payType == 'MBR'?'會員':scope.row.payType == 'SOC'?'協會':scope.row.payType == 'MBR;SOC'||scope.row.payType == 'SOC;MBR'?'會員;協會':''}}
                </template>
            </el-table-column>
            <el-table-column prop="autopayDescription" label="備註"></el-table-column>
            <el-table-column prop="autopayDate" label="支付日期" min-width="100">
                <template slot-scope="scope">
                    {{$utils.DATE(scope.row.autopayDate, 'yyyyMMdd')}}
                </template>
            </el-table-column>
            <el-table-column prop="socBankCharge" label="銀行手續費" min-width="100"></el-table-column>
            <el-table-column prop="exchangeRate" label="匯率"></el-table-column>
            <el-table-column prop="mbrHandleCharge" label="會員手續費" min-width="100"></el-table-column>
            <el-table-column prop="distNoDetails" label="參與支付的分配" min-width="120">
                <template slot-scope="scope">
          <span
              class="cell-detail a-blue"
              @dblclick="viewDistNoDetail(scope.row.distNoDetails)"
          >{{scope.row.distNoDetails}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="status" label="狀態" min-width="120">
                <template slot-scope="scope">
                    <span class="el-sl">{{scope.row.status==0?'待生成':scope.row.status==1?'生成中':scope.row.status==2?'生成完成':scope.row.status==3?'生成失败':''}}</span>
                </template>
            </el-table-column>
            <el-table-column label="operation" width="250">
                <template slot-scope="scope">
                    <span class="a-blue" @click="edit(scope.row)" v-if="isAuth('payment:paymentlist:change') && scope.row.status!=1 && scope.row.isEdit == 1">編輯</span>
                    <!-- <span class="a-blue" @click="look(scope.row)">可支付列表</span> -->
                    <span class="a-blue" @click="payDetail(scope.row)" v-if="isAuth('payment:paymentlist:payDetail') && scope.row.status!=1">支付詳情</span>
                    <span class="a-blue" @click="del(scope.row)" v-if="isAuth('payment:paymentlist:del') && scope.row.status!=1 && scope.row.isEdit == 1">删除</span>
                    <span class="a-blue" @click="pullData(scope.row)" v-if="isAuth('payment:paymentlist:del') &&(scope.row.status==2 || scope.row.status==3) && scope.row.isEdit == 1">重新生成</span>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination background layout="prev, pager, next" :total="total" @current-change="queryList" :current-page="currentPage"></el-pagination>
        <el-dialog :title="dialogTitle" :visible.sync="paylistTableVisible" width="900px" :close-on-click-modal="false" :before-close='cancelEdit'>
            <el-form
                :inline="true"
                ref="editForm"
                :model="addpayForm"
                label-width="120px"
                label-position="right"
                class="demo-form-inline"
                :rules="rules"
                @keyup.enter.native="editConfirm"
            >
                <div>
                    <el-form-item label="支付編號" prop="autopayNo">
                        <el-input v-model="addpayForm.autopayNo" placeholder="支付編號"></el-input>
                    </el-form-item>
                    <el-form-item label="支付類別" prop="payType">
                        <el-select @change="payTypeChange" v-model="addpayForm.payType" multiple placeholder="payType" style="width: 208px !important;">
                            <el-option
                                v-for="item in payType"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="支付日期" prop="autopayDate">
                        <el-date-picker v-model="addpayForm.autopayDate" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd" placeholder="autopayDate" style="width: 208px !important;"></el-date-picker>
                    </el-form-item>
                    <el-form-item label="備註" prop="autopayDescription">
                        <el-input v-model="addpayForm.autopayDescription" placeholder="autopayDescription"></el-input>
                    </el-form-item>
                    <el-form-item label="銀行手續費">
                        <el-input v-model="addpayForm.socBankCharge" placeholder="銀行手續費"></el-input>
                    </el-form-item>
                    <el-form-item label="匯率">
                        <el-input v-model="addpayForm.exchangeRate" placeholder="匯率"></el-input>
                    </el-form-item>
                    <el-form-item label="會員手續費">
                        <el-input v-model="addpayForm.mbrHandleCharge" placeholder="會員手續費"></el-input>
                    </el-form-item>
                    <el-form-item label="支付分配代號">
                        <el-select @change="distNoDetailsChange" v-model="addpayForm.distNoDetails" multiple placeholder="distNo" style="width: 208px !important;">
                            <el-option
                                v-for="item in distNoDetails"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                </div>
            </el-form>
            <span slot="footer" class="dialog-footer">
        <el-button @click="cancelEdit">取 消</el-button>
        <el-button type="primary" @click="editConfirm">確 定</el-button>
      </span>
        </el-dialog>
        <!-- 参与支付分配详情 -->
        <el-dialog title="詳情" :visible.sync="dialogVisible" width="30%" :close-on-click-modal="false">
            <el-table :empty-text="tableresult"   :data="tableData1" style="width: 100%" border>
                <el-table-column prop="id" label="id"></el-table-column>
                <el-table-column prop="distNo" label="dist no"></el-table-column>
                <el-table-column prop="distDescribe" label="description"></el-table-column>
            </el-table>
        </el-dialog>
    </div>
</template>

<script>
    export default {
        name: 'paymentlist',
        data: () => {
            return {
                formNum: {
                    payType:''
                },
                tableData: [],tableresult:' ',
                addpayForm: {
                    socBankCharge: 0,
                    exchangeRate: 0,
                    mbrHandleCharge: 0
                },
                paylistTableVisible: false,
                payType: [
                    {
                        value: 'MBR',
                        label: '會員'
                    },
                    {
                        value: 'SOC',
                        label: '協會'
                    }
                ],
                distNoDetails: [],
                total: 1,
                currentPage:1,
                dialogTitle:'',
                loading: null,
                dialogVisible: false,
                tableData1: [],
                rules:{
                    autopayNo:[
                        { required: true, message: '请输入', trigger: 'blur' },
                    ],
                    payType:[
                        { required: true, message: '请输入', trigger: 'blur' },
                    ],
                    autopayDate:[
                        { required: true, message: '请输入', trigger: 'blur' },
                    ],
                    autopayDescription:[
                        { required: true, message: '请输入', trigger: 'blur' },
                    ],
                }
            }
        },
        methods: {
            payTypeChange(data){
                let tit = 'PAYMENT FOR '
                let distNoDetails = this.addpayForm.distNoDetails || []
                if(distNoDetails.length){
                    distNoDetails.forEach((item,index) => {
                        tit = `${tit}${index==0?'':'+'}${item}`
                    });
                    if(data.length){
                        data.forEach((item,index) => {
                            console.log(item)
                            tit = `${tit}${index==0?'(':''}${index==0?'':'+'}${item=='SOC'?'SOCIETY':item=='MBR'?"MEMBER":''}${index==data.length-1?')':''}`
                        });
                    }
                }else{
                    if(data.length){
                        data.forEach((item,index) => {
                            tit = `${tit}${index==0?'':'+'}${item=='SOC'?'SOCIETY':item=='MBR'?"MEMBER":''}`
                        });
                    }else{
                        tit = ''
                    }
                }
                console.log(tit)
                this.addpayForm.autopayDescription=tit
            },
            distNoDetailsChange(data){
                let tit = 'PAYMENT FOR '
                let payType = this.addpayForm.payType || []
                if(data.length){
                    data.forEach((item,index) => {
                        tit = `${tit}${index==0?'':'+'}${item}`
                    });
                    if(payType.length){
                        payType.forEach((item,index) => {
                            tit = `${tit}${index==0?'(':''}${index==0?'':'+'}${item=='SOC'?'SOCIETY':item=='MBR'?"MEMBER":''}${index==payType.length-1?')':''}`
                        });
                    }
                }else{
                    if(payType.length){
                        payType.forEach((item,index) => {
                            tit = `${tit}${index==0?'':'+'}${item=='SOC'?'SOCIETY':item=='MBR'?"MEMBER":''}`
                        });
                    }else{
                        tit = ''
                    }
                }
                console.log(tit)
                this.addpayForm.autopayDescription=tit
            },
            clearSearch () {
                this.formNum = {
                    payType:''
                }
                this.queryList()
            },
            queryList (num) {
                let ajaxData = {
                    page_num: num ? num : 1,
                    page_size: 10
                }
                ajaxData = Object.assign(ajaxData, this.formNum)
                const loading = this.$loading()
                this.tableresult = '數據加載中...'
                this.$http
                  .get('/dist/autopay/listDistAutopayBaseWithPage', {params: ajaxData})
                  .then(res => {
                      if (res.success) {
                          this.tableData = res.data.data.list || []
                          this.total = res.data.data.total
                          this.tableData.forEach(item => {
                            var payDate=new Date(item.autopayDate);
                            var today = new Date()
                            if (payDate >= today){
                                item.isEdit = 1
                            }else{
                                item.isEdit= 0
                            }
                          })
                          this.currentPage = num
                          // todo-guan  分页
                          loading.close()
                      }
                          this.tableresult = this.tableData.length == 0 ? '暫無數據' : ' '
                  })
            },
            edit (item) {
                this.dialogTitle='编辑支付'
                // this.$router.push({name: 'paymentEdit'})
                this.paylistTableVisible = true
                this.addpayForm = this.$utils.copy(item)
                this.addpayForm.payType = this.formatArray(this.addpayForm.payType)
                this.addpayForm.distNoDetails = this.formatArray(
                  this.addpayForm.distNoDetails
                )
                this.getCalcRetain()
            },
            addPay () {
                this.dialogTitle='新增支付'
                this.paylistTableVisible = true
                this.addpayForm = {
                    socBankCharge: 0,
                    exchangeRate: 0,
                    mbrHandleCharge: 0
                }
                this.getCalcRetain()
            },
            editConfirm () {
                this.$refs.editForm.validate(validate => {
                    if(validate){
                        let params = this.$utils.copy(this.addpayForm)
                        params.payType = this.formatArray(
                          params.payType,
                          'array'
                        )
                        params.distNoDetails = this.formatArray(
                          params.distNoDetails,
                          'array'
                        )
                        params.autopayNo = params.autopayNo.toUpperCase()
                        console.log(params.payType)
                        const loading = this.$loading()
                        this.$http
                          .post('/dist/autopay/saveDistAutopayBase', params)
                          .then(res => {
                              loading.close()
                              if (res.success) {
                                  if (res.data.code === 200) {
                                      this.$toast({tips: '操作成功'})
                                      this.paylistTableVisible = false
                                      this.queryList()
                                  } else {
                                      this.$toast({tips: res.data.message})
                                  }
                              }
                          })
                    }
                })

            },
            cancelEdit () {
                this.paylistTableVisible = false
                this.$refs.editForm.resetFields()
            },
            viewDistNoDetail (details) {

                if (details) {
                    this.dialogVisible = true
                    this.loading1 = true
                    this.tableresult = '數據加載中...'
                    this.$http
                      .get('/dist/autopay/listDistParamNumberWithDistNos', {
                          params: {distNos: details}
                      })
                      .then(res => {
                          this.loading1 = false
                          if (res.success && res.data.code == 200) {
                              
                              this.tableData1 = res.data.data
                          }
                              this.tableresult = this.tableData1.length == 0 ? '暫無數據' : ' '
                      })
                }
            },
            // 獲取支付分配代號列表
            getCalcRetain () {
                this.distNoDetails = []
                this.$http
                  .get('/dist/autopay/listDistinctDistNoFromDistCalcRetain')
                  .then(res => {
                      console.log(res)
                      if (res.success) {
                          let list = res.data.data
                          if (list) {
                              list.map(item => {
                                  let obj = {
                                      value: item,
                                      label: item
                                  }
                                  this.distNoDetails.push(obj)
                              })
                          }
                      }
                  })
            },
            // 查看可支付列表
            look (item) {
                this.$router.push({
                    name: 'payment-info',
                    query: {autopayNo: item.autopayNo, payType: item.payType}
                })
            },
            payDetail (item) {
                console.log(item)
                this.$router.push({
                    name: 'paymentDetailInfo',
                    query: {autopayNo: item.autopayNo, payType: item.payType,isEdit:item.isEdit}
                })
            },
            formatArray (arrOrStr, type = '') {
                //数组转字符串，以分号隔开
                if (type == 'array') {
                    if(!arrOrStr){
                        return ''
                    }
                    let str = arrOrStr.join(';')
                    return str
                } else {
                    // 字符串转数组
                    if(!arrOrStr){
                        return []
                    }
                    let obj = arrOrStr.split(';')
                    return obj
                }
            },
            del(row){
                console.log(row)
                this.$msgbox.confirm(`確定進行[删除]操作?`, '提示', {
                    confirmButtonText: '確定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    const loading = this.$loading()
                    this.$http.delete('/dist/autopay/distAutopayBaseDelete/' + row.autopayNo).then(res => {
                        loading.close()
                        if (res.success) {
                            this.$toast({ tips: '删除成功' })
                            this.queryList(this.currentPage)
                        }
                        
                    })
                }).catch(() => {
                
                })
            },
            pullData(row){
                this.$msgbox.confirm(`確定[重新计算]操作?`, '提示', {
                    confirmButtonText: '確定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    const loading = this.$loading()
                    this.$http.delete('/dist/autopay/distAutopayPullData/' + row.autopayNo).then(res => {
                        loading.close()
                        if (res.success) {
                            this.$toast({ tips: '操作成功' })
                            this.queryList(this.currentPage)
                        }
                        
                    })
                }).catch(() => {
                
                })
            }
        },
        mounted () {
            this.queryList()
        }
    }
</script>

<style scoped>
    .cell-detail {
        display: block;
        cursor: pointer;
    }
</style>
