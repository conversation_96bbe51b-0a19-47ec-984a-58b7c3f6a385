<template>
<div>
    <el-dialog :visible.sync="show" width="800px" title="County" :modal="modal" :close-on-click-modal="false">
        <div style="width: 520px;margin: auto;margin-bottom: 20px; display: inline-block;">
            <el-input v-model="searchInfo.countyCode" @keyup.enter.native="onSubmit()" placeholder="countyCode" style="width: 160px;"></el-input>
            <el-input v-model="searchInfo.county" @keyup.enter.native="onSubmit()" placeholder="county" style="width: 160px;"></el-input>
            <el-button type="primary" @click="onSubmit()">查詢</el-button>
            <span class="clear-search" @click="clearSearch()">清除搜索</span>
        </div>
        <el-table :empty-text="emptyText" :data="tableData">
            <el-table-column property="countyCode" label="countyCode"></el-table-column>
            <el-table-column property="county" label="county"></el-table-column>
            <el-table-column
                label="operation"
                width="120">
                <template slot-scope="scope">
                    <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                        <i class="el-icon-check" @click="checkedCounty(scope.$index,scope.row)"></i>
                    </span>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :total="total" @current-change="onSubmit" :current-page="currentPage">
        </el-pagination>
    </el-dialog>
</div>
</template>
<script>
export default {
    data(){
        return{
            emptyText:'暫無數據',
            show:false,
            searchInfo: {
                countyCode: '',
                county: ''
            },
            tableData: [],
            total: 0,
            currentPage: 1
        }
    },
    props: {
        search: {
            type: Object,
            default: () => {
                return {
                    countyCode: '',
                    county: ''
                }
            }
        },
        modal: {
            type: Boolean,
            default: true
        }
    },

    methods: {
        /**
         * dialog 组件调用方式： 
         * 1、父级用v-if在html里加载组件,
         * 2、父级调用子级的init方法，将数据初始化，prop传给data等
         * 
         */
        init(){
            this.show = true;
            this.searchInfo = this.$utils.copy(this.search);
            this.searchInfo.county = this.searchInfo.county ? this.searchInfo.county : '';
            this.searchInfo.countyCode = this.searchInfo.countyCode ? this.searchInfo.countyCode : '';
            // Object.assign(this.searchInfo, this.searchInfo, this.search);
            this.onSubmit();
        },
        clearSearch(){
            this.searchInfo = {
                county: '',
                countyCode: ''
            };
            this.onSubmit();
        },
        onSubmit(page){
            let params = this.searchInfo;
            params.countyCode = params.countyCode ? params.countyCode.trim() : params.currencyCode;
            if(!params.countyCode){
                delete params.countyCode;
            }
            params.county = params.county ? params.county.trim() : params.county;
            if(!params.county){
                delete params.county;
            }
            params.pageNum = page ? page : 1;
            this.emptyText = '數據加載中'
            params.pageSize = 10;
            this.$http.get('/ref/getRefCounty', {params: params}).then(res => {
                this.tableData = res.data.data.list;
                this.total = res.data.data.total;
                this.currentPage = page ? page : 1;
                if(! this.tableData || this.tableData.length == 0){
                    this.emptyText = '暫無數據';
                }
            })
        },
        checkedCounty(index, row){
            this.$emit('checkCounty', row);
            this.show = false;
        }
    }
    
}
</script>
<style lang="scss" scoped>
    /deep/ .el-dialog__title{
        font-weight: bold;
    }
    /deep/ .el-dialog__body{
        text-align: center;
        padding-top: 0;
    }
</style>