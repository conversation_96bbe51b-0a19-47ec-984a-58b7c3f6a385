<template>
  <!-- 分配清單配置  -->
  <div>
    <el-form :inline="true" label-position="left" class="demo-form-inline">
      <el-form-item label="List Source">
        <el-select v-model="listSource" placeholder="請選擇" @change="listSourceChange">
          <el-option v-for="(item, index) in sourceList" :key="index" :label="item.listSourceName" :value="index">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="Net Dist Income">
        <el-input v-model="netDistIncome" readonly></el-input>
      </el-form-item>
      <el-form-item label="Source Distribute">
        <el-select v-model="sourceDistribute" style="width: 100px;" :disabled='status' @change="distributeChange">
          <el-option label="Y" value="Y"></el-option>
          <el-option label="N" value="N"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="pullList" :disabled="status">拉取</el-button>
        <el-button type="primary" @click="pullListByPage()" :disabled="status">手動添加</el-button>
      </el-form-item>
    </el-form>
    <el-table :empty-text="emptyText" :data="tableData" border stripe style="width: 100%">
      <el-table-column prop="listCategoryCode" label="List Category">
      </el-table-column>
      <el-table-column prop="type" label="Type">
      </el-table-column>
      <el-table-column prop="listStartTime" width="150" label="起始時間">
        <template slot-scope="scope">
          <date-picker v-model="scope.row.listStartTime" type="date" format="yyyyMMdd" value-format="yyyy-MM-dd"></date-picker>
        </template>
      </el-table-column>
      <el-table-column prop="listEndTime" width="150" label="結束時間">
        <template slot-scope="scope">
          <date-picker v-model="scope.row.listEndTime" type="date" format="yyyyMMdd" value-format="yyyy-MM-dd"></date-picker>
        </template>
      </el-table-column>
      <el-table-column prop="radio" label="Ratio">
        <template slot-scope="scope">
          <el-input type="number" v-model=scope.row.radio @input="radioChange(scope.$index, scope.row.radio)"></el-input>
          <!-- <el-input type="number" v-model=scope.row.radio ></el-input> -->
        </template>
      </el-table-column>
      <el-table-column prop="sourceTotalAmount" label="Source Total Amount">
      </el-table-column>
      <el-table-column prop="retainRate" label="Retain Rate%">
        <template slot-scope="scope">
          <el-input type="number" v-model=scope.row.retainRate @input="updateTable"></el-input>
        </template>
      </el-table-column>
      <el-table-column prop="retainAmount" label="Retain Amount">
      </el-table-column>
      <el-table-column prop="netDistAmount" label="Net Dist Amount">
      </el-table-column>
      <el-table-column label="operation">
        <template slot-scope="scope">
          <el-button type="text" size="small" :disabled="status" @click="deleteFn(scope.$index, scope.row)">刪除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div style="margin-bottom: 20px;width: 100%;text-align: center;margin-top: 20px">
      <el-button type="primary" @click="saveFn" :disabled="status">Save</el-button>
    </div>
    <!-- 手动添加 -->
    <el-dialog :visible.sync="listTableShow" :close-on-click-modal="false" @close="clearListTable">
      <el-table :empty-text="emptyText1" stripe :data="listTableData" ref="multipleTable" @select-all="handleSelectionAll" @select="handleSelect">
        <el-table-column type="selection" width="80"></el-table-column>
        <el-table-column property="listCategoryCode" label="list Category"></el-table-column>
      </el-table>
      <el-pagination background :small=true layout="prev, pager, next" :total="total" @current-change="handleCurrentChange">
      </el-pagination>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirmList">確 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import bus from './Eventbus.js'
export default {
  data() {
    return {
      distNo: '',
      emptyText: '暫無數據',
      emptyText1: '暫無數據',
      listSource: 0,
      oldListSource: 0,
      // 金額配置里的列表里的source信息
      sourceList: [],
      totalAmount: 0,
      sourceDistribute: 'Y',
      tableData: [],
      // 弹框变量
      listTableShow: false,
      listTableData: [],
      total: 0,
      currentPage: 1,
      selectData: {},
      //是否編輯了
      /**
       * 逻辑 有变動，且 没保存，则提示切換listsource不會保留已編輯未保存數據
       * 保存後，hasEdit 重置為false
       */
      hasEdit: false,
      status: 0,
      distNo: '',
      delData:[]
    }
  },
  props: {
    startTime: {
      type: String
    },
    endTime: {
      type: String,
      default: ''
    },
  },
  computed: {
    netDistIncome: function () {
      return this.sourceList[this.listSource] ? this.sourceList[this.listSource].netDistIncome : 0;
    },
    noClick() {

    },
  },
  watch: {
  },
  created() {
    this.distNo = this.$route.query.distNo;
    // let status = this.$route.query.distNo;
    let disstatus = this.$route.query.status;
    // console.warn('+++++',this.$route.query,'===',disstatus)
    // status==1||status==11||status==3||status==31||status==41||status==7||status==2||status==4
    // if (disstatus == 7 || disstatus == 1 || disstatus == 2 || disstatus == 3 || disstatus == 4) {
    if (disstatus==1||disstatus==11||disstatus==3||disstatus==31||disstatus==41||disstatus==7||disstatus==2||disstatus==4||disstatus==6||disstatus==61) {
      this.status = true
    } else {
      this.status = false  
    }
    // 先查詢分配金額配置里的source list
    this.querySourceList();
  },
  activated() {
    if (this.distNo != this.$route.query.distNo) {
      this.distNo = this.$route.query.distNo;
      this.status = this.$route.query.status;
      this.querySourceList();
    }
  },
  mounted() {
    console.log('++++')
    //  bus.$on('changelist',(message)=>{
    //      this.sourceList=message
    //  })

  },
  methods: {
    listSourceChange() {
      if (this.hasEdit) {
        this.$msgbox.confirm(`切換 List Source 後，已編輯未保存數據將丢失，確定切換?`, '提示', {
          confirmButtonText: '確定',closeOnClickModal:false,
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.queryInfo();
          this.oldListSource = this.listSource;
        }).catch(() => {
          this.listSource = this.oldListSource;
        })
      } else {
        this.oldListSource = this.listSource;
        this.queryInfo();
      }
    },
    distributeChange() {
      this.$http.post('/dist/param/source/sourceDistribute', { id: this.sourceList[this.listSource].id, sourceDistribute: this.sourceDistribute }).then(res => {
        if (res.success) {
          this.$toast({ tips: '修改成功' })
          this.sourceList[this.listSource].sourceDistribute = this.sourceDistribute;
        }
      })
    },
    radioChange(index, value) {
      value = value + '';
      value = value.replace(/[^\d.]/g, '');
      this.tableData[index].radio = value;
      this.updateTable();
    },
    querySourceList() {
      this.$http.get('/dist/param/source?distNo=' + this.distNo).then(res => {
        if (res.success) {
          this.sourceList = res.data.distParamSourceList;
          console.log(this.sourceList)
          if (this.sourceList.length > 0) {
            this.listSource = 0;
            this.queryInfo();
          }else{
            this.listSource = '';
            this.tableData=[]
          }

        }
      })
    },
    queryInfo() {
      // console.warn("buggbugu",this.sourceList,'***',this.listSource)
      let ajaxData = {
        distNo: this.distNo,
        listSourceId: this.sourceList[this.listSource].listSourceId || '',
        listSourceName: this.sourceList[this.listSource].listSourceName
      }
      this.emptyText = '數據加載中';
      this.$http.get('/dist/param/category', { params: ajaxData }).then(res => {
        if (res.success) {
          this.tableData = res.data.distParamCategoryList;
          this.sourceDistribute = this.sourceList[this.listSource].sourceDistribute || 'Y';
          this.tableData.length && this.tableData.forEach(item => {
            item.retainRate = !item.retainRate && item.retainRate != 0 ? 0.05 : item.retainRate;
          })
          this.hasEdit = false;
          this.updateTable()
        }
        this.emptyText = '暫無數據';
      })
    },
    pullList(type, page = 1) {
      if (this.sourceList.length < 1) {
        this.$toast({ tips: '沒有List Source' });
        return;
      }
      let ajaxData = {
        distNo: this.distNo,
        listSourceId: this.sourceList[this.listSource].listSourceId || '',
        type: 'A'
      }
      this.$http.get('/dist/param/category/pull', { params: ajaxData }).then(res => {
        if (res.success) {
          // this.tableData = [...this.tableData, ...res.data];
          res.data.forEach(item => {
            this.tableData.forEach(d => {
              if (item.listSourceId = d.listSourceId && item.listCategoryId == d.listCategoryId) {
                item.repeat = true;
              }
            })
          })
          res.data.forEach(item => {
            if (!item.repeat) {
              this.tableData.push(item);
            }
          })
          this.tableData.length && this.tableData.forEach(item => {
            item.retainRate = item.retainRate ? item.retainRate : 0.05;
          })
          this.hasEdit = true;
        }
      })
    },
    pullListByPage(page = 1) {
      if (this.sourceList.length < 1) {
        this.$toast({ tips: '沒有List Source' });
        return;
      }
      let ajaxData = {
        distNo: this.distNo,
        listSourceId: this.sourceList[this.listSource].listSourceId || '',
        type: 'M',
        pageNum: page,
        pageSize: 10
      }
      this.emptyText1 = '數據加載中';
      this.$http.get('/dist/param/category/pullByPage', { params: ajaxData }).then(res => {
        if (res.success) {
          // 把數據放在dialog里
          let list = res.data.data.list || {}
          this.listTableData = list;
          this.$nextTick(() => {
            if (this.selectData[page]) {
              this.listTableData.map(item => {
                this.selectData[page].map(select => {
                  if (select.id == item.id) {
                    this.$refs.multipleTable.toggleRowSelection(item);
                  }
                })
              })
            }
          })
          this.total = res.data.data.total
          this.currentPage = res.data.data.pageNum
          this.listTableShow = true;
        }
        this.emptyText1 = '暫無數據';
      })
    },
    handleCurrentChange(val) {
      console.log(this.selectData)
      this.pullListByPage(val)
    },
    handleSelectionAll(list) {
      this.selectData[this.currentPage] = list;
    },
    handleSelect(list, row) {
      this.selectData[this.currentPage] = list;
    },
    clearListTable() {
      this.selectData = {}
    },
    updateTable() {
      let totalRatio = 0;
      this.tableData.forEach(item => {
        let radio = new BigNumber(item.radio ? item.radio : 0);
        totalRatio = new BigNumber(totalRatio).plus(radio).toFixed(6);
      })
      totalRatio = Number(totalRatio)
      console.log(totalRatio)
      this.tableData.forEach(item => {
        let radio = new BigNumber(item.radio ? item.radio : 0);
        if (totalRatio == 0) {
          item.sourceTotalAmount = new BigNumber(this.netDistIncome).toFixed(6);
        } else {
          item.sourceTotalAmount = radio.dividedBy(new BigNumber(totalRatio)).multipliedBy(new BigNumber(this.netDistIncome)).toFixed(6);
        }
        item.retainAmount = new BigNumber(item.sourceTotalAmount).multipliedBy(new BigNumber(item.retainRate ? item.retainRate : 0)).dividedBy(new BigNumber(100)).toFixed(6);
        item.netDistAmount = new BigNumber(item.sourceTotalAmount).minus(new BigNumber(item.retainAmount)).toFixed(6);
      })

      this.hasEdit = true;
    },
    deleteFn(index, row) {
      this.$msgbox.confirm(`確定進行[删除]操作?`, '提示', {
        confirmButtonText: '確定',closeOnClickModal:false,
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // debugger
        // if (row.id) {
        //   this.$http.delete('/dist/param/category/' + row.id).then(res => {
        //     if (res.success) {
        //       this.$toast({ tips: '删除成功' })
        //       this.tableData.splice(index, 1);
        //       // 触发列表重新計算
        //       this.updateTable();
        //     }
        //   })
        // } else {
          // this.tableData.splice(index, 1);
        //   // 触发列表重新計算
        //   this.updateTable();
        // }
        if(row.id){
          this.delData.push(row.id)
        }
        this.tableData.splice(index, 1);
        // 触发列表重新計算
        this.updateTable();
      })
    },
    addFn() {
      this.listTableShow = true
      this.getDetailInfo()
      // this.tableDataPeizhi.push({name: '', type: '', pizhi: '', filenum: '', num: '', auditednum: '', notAuditednum: ''})
    },

    saveFn() {
      let ajaxData = this.$utils.copy(this.tableData);
      let flag = true;
      // console.warn('save00000',ajaxData)
      console.log(this.delData.length)
      if (ajaxData.length == 0 && !this.delData.length) {
        this.$toast({ tips: '没有需要保存的数据' })
        return
      }
      console.log('ajaxData')
      console.log(ajaxData)
      ajaxData.forEach(item => {
        if (!item.radio && item.radio != 0) {
          this.$toast({ tips: 'Ratio 必填' })
          flag = false;
          return
        }
        // console.log(new Date(item.listStartTime).getTime(),new Date(this.startTime).getTime())
        // console.log(new Date(item.listEndTime).getTime(),new Date(this.endTime).getTime())
        // console.log(new Date(item.listStartTime).getTime() < new Date(this.startTime).getTime(),new Date(item.listStartTime).getTime() > new Date(this.endTime).getTime())
        // console.log(new Date(item.listEndTime).getTime() < new Date(this.startTime).getTime(),new Date(item.listEndTime).getTime() > new Date(this.endTime).getTime())
       if (item.listStartTime && item.listEndTime) {
          var startTime = this.startTime
          var endTime = this.endTime
          startTime = /\d{4}-\d{1,2}-\d{1,2}/g.exec(startTime)[0]
          item.listStartTime = /\d{4}-\d{1,2}-\d{1,2}/g.exec(item.listStartTime)[0]
          endTime = /\d{4}(\-|\/|.)\d{1,2}\1\d{1,2}/g.exec(endTime)[0]
          item.listEndTime = /\d{4}(\-|\/|.)\d{1,2}\1\d{1,2}/g.exec(item.listEndTime)[0]
          
          if (item.listStartTime && (new Date(item.listStartTime).getTime() < new Date(startTime).getTime() || new Date(item.listStartTime).getTime() > new Date(startTime).getTime())) {
            flag = false;
            this.$confirm(`配置分配清單時間與分配參數配置時間不一致?`, '提示', {
              confirmButtonText: '確定',
              cancelButtonText: '取消',
              closeOnClickModal:false,
              type: 'warning'
            }).then(() => {
              this.saveAjax(ajaxData)
              return
            }).catch(() => {
              return
            })
          }
          console.log()
          if (item.listEndTime && (new Date(item.listEndTime).getTime() < new Date(endTime).getTime() || new Date(item.listEndTime).getTime() > new Date(endTime).getTime())) {
            flag = false;
            this.$confirm(`配置分配清單時間與分配參數配置時間不一致?`, '提示', {
              confirmButtonText: '確定',closeOnClickModal:false,
              cancelButtonText: '取消',
              closeOnClickModal:false,
              type: 'warning'
            }).then(() => {
              this.saveAjax(ajaxData)
              return
            }).catch(() => {
              return
            })
          }
        }else {  // 至少一个为空，肯定不一致
            flag = false;
            this.$confirm(`配置分配清單時間與分配參數配置時間不一致?`, '提示', {
              confirmButtonText: '確定',
              cancelButtonText: '取消',
              closeOnClickModal:false,
              type: 'warning'
            }).then(() => {
              this.saveAjax(ajaxData)
              return
            }).catch(() => {
              return
            })
        }
        // if(!item.radio && item.radio != 0){
        //     this.$toast({tips: 'Ratio 必填'})
        //     flag = false;
        // }
        // 配置分配清單時間與分配參數配置時間不一致
      })
      if (!flag) {
        return;
      }
      this.saveAjax(ajaxData)
    },
    saveAjax(ajaxData) {
      if(this.delData.length){
        this.delData.forEach((item,index)=>{
          this.$http.delete('/dist/param/category/' + item).then(res => {
            if (res.success) {
              if(index+1 == this.delData.length){
                this.$http.post('/dist/param/category', ajaxData).then(res => {
                  if (res.success) {
                    if (res.data.code && res.data.code == 200) {
                      this.queryInfo()
                      this.$toast({ tips: '保存成功' });
                    } else if(res.data.code==400) {
                    // if (res.data.code && res.data.code != 200) {
                      this.queryInfo()
                      this.$toast({ tips:'保存成功' });
                    // } else {
                      this.hasEdit = false;
                      // this.tableData = res.data
                    }else{
                      this.$toast({ tips: res.data.message });
                    }
                  }
                })
              }
            }
          })
        })
      }else{
        this.$http.post('/dist/param/category', ajaxData).then(res => {
          console.log(res)
          if (res.success) {
            if (res.data.code && res.data.code == 200) {
              this.queryInfo()
              this.$toast({ tips: '保存成功' });
            } else if(res.data.code==400) {
              // if (res.data.code && res.data.code != 200) {
              this.queryInfo()
              this.$toast({ tips:'保存成功' });
            // } else {
              this.hasEdit = false;
              // this.tableData = res.data
            }else{
              this.$toast({ tips: res.data.message });
            }
          }
        })
      }
    },
    confirmList() {
      let arr = new Array()
      Object.keys(this.selectData).map(key => {
        arr = arr.concat(this.selectData[key])
      })
      // console.warn("this.selectData",this.selectData,'***',arr,'99',this.tableData)
      let repeatNum = 0
      if (this.tableData.length > 0) {
        this.tableData.forEach((item, i, arrtable) => {
          arr.forEach((item0, index0, arr0) => {
            if (arr0[index0].listCategoryId == item.listCategoryId) {
              repeatNum++
              arr0[index0] = ''
            }

          })
        })
        let newArr = arr.filter(item => item != '')
        this.tableData = this.tableData.concat(newArr)
      } else {
        this.tableData = arr
      }
      this.tableData.length && this.tableData.forEach(item => {
        item.retainRate = item.retainRate ? item.retainRate : 0.05;

      })

      this.listTableShow = false;
      this.hasEdit = true;
      repeatNum > 0 && this.$toast({ tips: `重复添加${repeatNum}個category` })
    }
  },
  components: {
    bus
  }
}
</script>
<style lang="scss" scoped>
/deep/ .el-dialog {
  width: 600px;
  /deep/ .el-dialog__footer {
    text-align: center;
  }
}
</style>
