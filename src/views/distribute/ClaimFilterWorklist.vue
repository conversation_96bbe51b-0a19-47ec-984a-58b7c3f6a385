<template>
  <div class="claim-filter-worklist">
    <el-form :inline="true" :model="searchForm" class="demo-form-inline" @keyup.enter.native="updateCompany">
      <el-form-item label="">
        <span class="red">*</span>
        <el-select v-model="searchForm.company" placeholder="company" @change="updateCompany">
            <el-option label="Youtube" value="Youtube"></el-option>
            <el-option label="Spotify" value="Spotify"></el-option>
            <el-option label="Apple" value="Apple"></el-option>
            <el-option label="META" value="META"></el-option>
          </el-select>
      </el-form-item>
      <el-form-item label="上傳時要清空的Filter" >
        <el-checkbox v-model="delWorkChecked">Work</el-checkbox>
      </el-form-item>
      <el-form-item>
        <el-checkbox v-model="delIpChecked">IP</el-checkbox>
      </el-form-item>
      <el-form-item>
        <el-checkbox v-model="delSocChecked">SOC</el-checkbox>
      </el-form-item>
      <el-form-item>
        <el-upload class="upload-demo" ref="upload" action="" accept=".xls,.xlsx" :auto-upload="false" :on-change="handleFileChange">
          <el-button size="small" type="primary" style="background-color: #b240ff;border-color: #b240ff;"  v-if="isAuth('distribute:ClaimFilterWorklist:uploading')">上传EXCEL</el-button>
        </el-upload>
      </el-form-item>
    </el-form>
    <el-tabs type="card" @tab-click="handleTabClick">
      <el-tab-pane label="Filter Work">
        <tab-filter-work ref="tabFilterWork" :company="searchForm.company" @updateCompany="updateCompany"></tab-filter-work>
      </el-tab-pane>
      <el-tab-pane label="Filter IP">
        <tab-filter-ip ref="tabFilterIp" :company="searchForm.company" @updateCompany="updateCompany"></tab-filter-ip>
      </el-tab-pane>
      <el-tab-pane label="Filter SOC">
        <tab-filter-soc ref="tabFilterSoc" :company="searchForm.company" @updateCompany="updateCompany"></tab-filter-soc>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import TabFilterWork from './ClaimFilterWorkList/tab-filter-work.vue';
import TabFilterIp from './ClaimFilterWorkList/tab-filter-ip.vue';
import TabFilterSoc from './ClaimFilterWorkList/tab-filter-soc.vue';
import axios from "axios";

export default {
  name: 'ClaimFilterWorklist',
  components: {
    TabFilterWork,
    TabFilterIp,
    TabFilterSoc
  },
  data() {
    return {
      searchForm: {
        company: 'Youtube'
      },
      delWorkChecked: false,
      delSocChecked: false,
      delIpChecked: false,
      activeTab: 'Filter Work' // 记录当前激活的标签页
    };
  },
  methods: {
    updateCompany(company) {
      this.searchForm.company = company;
    },
    handleTabClick(tab) {
      this.activeTab = tab.label;
      // 根据不同的 tab 调用相应的查询方法
      this.refreshData(tab.label)
    },
    refreshData(tabLabel){
      switch (tabLabel) {
        case 'Filter Work':
          this.$refs.tabFilterWork.getClaimList();
          break;
        case 'Filter IP':
          this.$refs.tabFilterIp.getClaimList();
          break;
        case 'Filter SOC':
          this.$refs.tabFilterSoc.getClaimList();
          break;
        default:
          break;
      }
    },
    handleFileChange(file) {
      let formData = new FormData();
      formData.append('file', file.raw);
      var message = 'Company ：     ' + this.searchForm.company + '<br>要清空的Filter'
      + '<br>WORK ：     ' +  (this.delWorkChecked ? '✔' : 'X')
      + '<br>IP ：     ' + (this.delIpChecked ? '✔' : 'X')
      + '<br>SOC  ：      '  + (this.delSocChecked ? '✔' : 'X');
      console.log(message)
      this.$msgbox.confirm(message, '提示', {
                      confirmButtonText: '確定',
                      cancelButtonText: '取消',
                      type: 'warning',
                      dangerouslyUseHTMLString: true, // 允许使用HTML内容
                      customClass: 'custom-message-box' // 应用自定义类
                  }).then(() => {
                    formData.append('delWork', this.delWorkChecked);
                    formData.append('delSoc', this.delSocChecked);
                    formData.append('delIp', this.delIpChecked);
                    formData.append('company', this.searchForm.company);

                    this.uploadFile(formData)
                  }).catch(() => {
                    // 用户选择取消时清除已选择的文件
                    // this.$refs.upload.clearFiles();
                  })
      this.$refs.upload.clearFiles();
    },
    uploadFile(param) {
      let activeTab = this.activeTab
      axios.post('/claimFilterWork/importClaimFilterOtherWork', param).then(res => {
        console.log('^^^^^^^^^^^^^^^', res)

        if (res.data.code == 200) {

            if (Number(res.data.data)) {
                this.$toast({ tips: '上传成功' + res.data.data + '条数据' })
                // that.getClaimList()
            } else {
                let MessageBox = res.data.data || res.data.message
                this.$toast({ tips: MessageBox })
            }
            this.refreshData(activeTab)

        } else {
            let MessageBox = res.data.data || res.data.message
            // setTimeout(function() {
              this.$toast({ tips: MessageBox })
            // }, 0);
            // this.$toast({tips:res.data.message})
        }
      });

    }

  }
}
</script>

<style lang="scss">
.claim-filter-worklist {
  .tab-content {
    .el-form-item__label {
      margin-left: 0 !important;
    }
  }
}

.custom-message-box {
  .el-message-box__message {
    text-align: left; /* 左对齐每一行的内容 */
    width: 80%; /* 设置一个合适的宽度，确保内容不会太挤 */
    max-width: 400px; /* 设置最大宽度，防止内容过长时超出屏幕 */
    margin: 0 auto; /* 水平居中 */
    padding-left: 100px; /* 内边距 */
    box-sizing: border-box; /* 包含内边距和边框 */
    line-height: 1.6; /* 行间距 */
  }
}
</style>
