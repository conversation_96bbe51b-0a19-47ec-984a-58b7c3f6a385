<template>
    <div class="tasks">
        <el-form :inline="true" :model="searchData" class="demo-form-inline search-list" @keyup.enter.native="onSubmit(1)">
            <el-form-item prop="">
                <el-input v-model="searchData.task_name" placeholder="Task Name" style="width: 150px;"></el-input>
            </el-form-item>
            <el-form-item prop="">
                <el-input v-model="searchData.user" placeholder="User" style="width: 150px;"></el-input>
            </el-form-item>
            <el-form-item>
              <el-select v-model="searchData.status" placeholder="status" style="width: 100px;">
                <el-option label="全部" value=""></el-option>
                <el-option label="進行中" :value="1"></el-option>
                <el-option label="已完成" :value="2"></el-option>
                <el-option label="失敗" :value="3"></el-option>
                <el-option label="初始化" :value="0"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-select v-model="searchData.timeType" placeholder="create time" style="width: 140px;">
                <el-option label="create_time" :value="0"></el-option>
                <el-option label="finish_time" :value="1"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="">
                <date-picker v-model="searchData.time_a" type="date" value-format="yyyy-MM-dd HH:mm:ss" format="yyyyMMdd" placeholder="StartDate" style="width: 150px !important;"></date-picker>
                To
                <date-picker v-model="searchData.time_b" type="date" value-format="yyyy-MM-dd HH:mm:ss" format="yyyyMMdd" placeholder="EndDate" style="width: 150px !important;"></date-picker>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="onSubmit(1)">搜索</el-button>
            </el-form-item>
            <el-form-item>
                <span class="clear-search" @click="clearSearch()">清除搜索</span>
            </el-form-item>
        </el-form>
        <el-table
            :data="tasksData"
            border
            style="width: 100%"
            :empty-text="emptyText">
            <el-table-column
                prop="taskName"
                label="Task Name">
            </el-table-column>
            <el-table-column
                prop="user"
                label="User">
            </el-table-column>
            <el-table-column
                prop="taskType"
                label="Task Type"
                width="110px">
                <template slot-scope="scope">
                    <span>{{scope.row.taskType==1?'import':scope.row.taskType==2?'export':scope.row.taskType==3?'general':''}}</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="status"
                label="Status"
                width="80px">
                <template slot-scope="scope">
                    <span>{{scope.row.status==0?'初始化':scope.row.status==1?'進行中':scope.row.status==2?'已完成':scope.row.status==3?'失敗':''}}</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="originalFilename"
                label="Original Filename">
            </el-table-column>
            <el-table-column
                prop="createTime"
                label="Create Time">
            </el-table-column>
            <el-table-column
                prop="amendTime"
                label="Finish Time">
            </el-table-column>
            <el-table-column fixed="right" label="Operation" width="150px">
            <template slot-scope="scope">
                <span style="width: 100%;text-align: center;display: inline-block">
                  <el-button v-if="scope.row.status==2 && scope.row.taskType==2" @click="download(scope.row)" type="text" size="small">Download</el-button>
                  <el-button v-if="scope.row.status==2 || scope.row.status==3" @click="log(scope.row)" type="text" size="small">log</el-button>
                </span>
              </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :total='tasksTotal' @current-change="onSubmit" :current-page.sync="tasksCurrentPage">
        </el-pagination>
        <el-dialog title="log" :visible.sync="show" width="50%" :close-on-click-modal="false">
            <p style="max-height:500px;overflow: auto;">{{logData}}</p>
            <div style="text-align: right;">
                <el-button @click="show=false" type="primary" size="small">確定</el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
export default {
    name:'allTasks',
    data(){
        return{
            searchData:{
                task_name:'',
                user:'',
                status:'',
                timeType:0,
                time_a:'',
                time_b:'',
            },
            tasksData:[],
            tasksTotal:1,
            tasksCurrentPage:1,
            page:1,
            emptyText: '暫無數據',
            show:false,
            logData:''
        }
    },
    created() {
        this.init();
    },
    methods:{
        download(row){
            console.log(row)
            this.$utils.downloadGet('/refTask/getList/download/' + row.id);
        },
        log(row){
            console.log(row)
            this.show = true
            this.logData = row.log
            // this.$alert(row.log, 'log');
        },
        init(){
            this.onSubmit(1)
        },
        onSubmit(page){
            let params = this.$utils.copy(this.searchData);
            // params.time_a = params.time_a.split('-').join('')
            // params.time_b = params.time_b.split('-').join('')
            if(params.time_b<params.time_a){
                this.tasksCurrentPage = this.page
                this.$toast({ tips: "End Date不能小於Start Date" });
                return;
            }
            let time_b = params.time_b
            if (time_b) {
                params.time_b = time_b.split(' ')[0] + ' 23:59:59'
            }
            console.log(params)
            params.page_num = page ? page : 1;
            params.page_size = 10;
            this.emptyText = '數據加載中';
            this.$http.get('/refTask/getList/getList', {params}).then(res => {
                console.log(res.data)
                // this.loading = false;
                if (res.data.code === 200) {
                    this.tasksData = res.data.data.list
                    if(! this.tasksData || this.tasksData.length == 0){
                        this.emptyText = '暫無數據';
                    }
                    this.tasksTotal = res.data.data.total
                    this.tasksCurrentPage = page ? page : 1
                    this.page = this.tasksCurrentPage
                }
            })
        },
        clearSearch(){
            this.searchData={
                task_name:'',
                user:'',
                status:'',
                timeType:0,
                time_a:'',
                time_b:'',
            }
            this.onSubmit(1)
        }
    }
}
</script>
<style lang="scss" scoped>
/deep/ .el-dialog__body{
    padding-top: 0;
}
</style>