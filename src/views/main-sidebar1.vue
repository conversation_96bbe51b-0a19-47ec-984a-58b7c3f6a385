<template>
  <aside class="site-sidebar" :class="'site-sidebar--' + sidebarLayoutSkin">
    <div class="site-sidebar__inner">
      <span style="display: none">{{selectLanguage}}</span>
      <el-menu
        :default-active="menuActiveName || 'home'"
        :collapse="sidebarFold"
        :collapseTransition="false"
        class="site-sidebar__menu">
        <el-menu-item index="home" @click="$router.push({ name: 'home' })">
          <!--<icon-svg name="shouye" class="site-sidebar__menu-icon"></icon-svg>-->
          <div class="homeimgbox"><img src="../assets/img/home/<USER>"></div>
          <span class="homespan" slot="title">{{home}}</span>
        </el-menu-item>
        <el-submenu index="demo">
          <template slot="title">
            <!--<div class="homeimgbox"><img src="../assets/img/home/<USER>"></div>-->
            <span>會員管理</span>
          </template>
          <el-menu-item index="demo-echarts" @click="$router.push({ name: 'member-ipi' })">
            <icon-svg name="tubiao" class="site-sidebar__menu-icon"></icon-svg>
            <span slot="title">會員信息管理</span>
          </el-menu-item>
          <!--<el-menu-item index="demo-ueditor" @click="$router.push({ name: 'demo-ueditor' })">-->
          <!--<icon-svg name="editor" class="site-sidebar__menu-icon"></icon-svg>-->
          <!--<span slot="title">系統會員管理</span>-->
          <!--</el-menu-item>-->
        </el-submenu>
        <el-submenu index="demo1">
          <template slot="title">
            <icon-svg name="shoucang" class="site-sidebar__menu-icon"></icon-svg>
            <span>作品管理</span>
          </template>
          <el-menu-item index="demo-works" @click="$router.push({ name: 'works-actor'})">
            <icon-svg name="tubiao" class="site-sidebar__menu-icon"></icon-svg>
            <span slot="title">作品表演著管理</span>
          </el-menu-item>
          <el-menu-item index="demo-ueditor" @click="$router.push({ name: 'works-list' })">
            <icon-svg name="editor" class="site-sidebar__menu-icon"></icon-svg>
            <span slot="title">作品基本信息管理</span>
          </el-menu-item>
          <el-menu-item index="demo-ueditor" @click="$router.push({ name: 'demo-ueditor' })">
            <icon-svg name="editor" class="site-sidebar__menu-icon"></icon-svg>
            <span slot="title">作品matching管理</span>
          </el-menu-item>
        </el-submenu>
        <el-submenu index="demo2">
          <template slot="title">
            <icon-svg name="shoucang" class="site-sidebar__menu-icon"></icon-svg>
            <span>合約管理</span>
          </template>
          <el-menu-item index="demo-contract" @click="$router.push({ name: 'contract-contract'})">
            <icon-svg name="tubiao" class="site-sidebar__menu-icon"></icon-svg>
            <span slot="title">合約管理</span>
          </el-menu-item>
        </el-submenu>
        <el-submenu index="demo3">
          <template slot="title">
            <icon-svg name="shoucang" class="site-sidebar__menu-icon"></icon-svg>
            <span>分配管理</span>
          </template>
          <el-menu-item index="demo-fmenu" @click="$router.push({ name: 'demo-echarts' })">
            <icon-svg name="tubiao" class="site-sidebar__menu-icon"></icon-svg>
            <span slot="title">分配清單管理</span>
          </el-menu-item>
          <el-menu-item index="demo-ueditor" @click="$router.push({ name: 'demo-ueditor' })">
            <icon-svg name="editor" class="site-sidebar__menu-icon"></icon-svg>
            <span slot="title">分配作品matching</span>
          </el-menu-item>
          <el-menu-item index="demo-ueditor" @click="$router.push({ name: 'demo-ueditor' })">
            <icon-svg name="editor" class="site-sidebar__menu-icon"></icon-svg>
            <span slot="title">分配参數配置</span>
          </el-menu-item>
          <el-menu-item index="demo-ueditor" @click="$router.push({ name: 'demo-ueditor' })">
            <icon-svg name="editor" class="site-sidebar__menu-icon"></icon-svg>
            <span slot="title">P分配</span>
          </el-menu-item>
          <el-menu-item index="demo-ueditor" @click="$router.push({ name: 'demo-ueditor' })">
            <icon-svg name="editor" class="site-sidebar__menu-icon"></icon-svg>
            <span slot="title">I分配</span>
          </el-menu-item>
          <el-menu-item index="demo-ueditor" @click="$router.push({ name: 'demo-ueditor' })">
            <icon-svg name="editor" class="site-sidebar__menu-icon"></icon-svg>
            <span slot="title">O分配</span>
          </el-menu-item>
          <el-menu-item index="demo-ueditor" @click="$router.push({ name: 'demo-ueditor' })">
            <icon-svg name="editor" class="site-sidebar__menu-icon"></icon-svg>
            <span slot="title">M分配</span>
          </el-menu-item>
          <el-menu-item index="demo-ueditor" @click="$router.push({ name: 'demo-ueditor' })">
            <icon-svg name="editor" class="site-sidebar__menu-icon"></icon-svg>
            <span slot="title">調整分配</span>
          </el-menu-item>
          <el-menu-item index="demo-ueditor" @click="$router.push({ name: 'demo-ueditor' })">
            <icon-svg name="editor" class="site-sidebar__menu-icon"></icon-svg>
            <span slot="title">保留款分配</span>
          </el-menu-item>
        </el-submenu>
        <el-submenu index="demo4">
          <template slot="title">
            <icon-svg name="shoucang" class="site-sidebar__menu-icon"></icon-svg>
            <span>支付管理</span>
          </template>
          <el-menu-item index="demo-works" @click="$router.push({ name: 'works-actor'})">
            <icon-svg name="tubiao" class="site-sidebar__menu-icon"></icon-svg>
            <span slot="title">支付管理</span>
          </el-menu-item>
        </el-submenu>
        <el-submenu index="demo5">
          <template slot="title">
            <icon-svg name="shoucang" class="site-sidebar__menu-icon"></icon-svg>
            <span>報表管理</span>
          </template>
          <el-menu-item index="demo-works" @click="$router.push({ name: 'works-actor'})">
            <icon-svg name="tubiao" class="site-sidebar__menu-icon"></icon-svg>
            <span slot="title">報表管理</span>
          </el-menu-item>
        </el-submenu>
        <el-submenu index="demo6">
          <template slot="title">
            <icon-svg name="shoucang" class="site-sidebar__menu-icon"></icon-svg>
            <span>定時器任務管理</span>
          </template>
          <el-menu-item index="demo-works" @click="$router.push({ name: 'works-actor'})">
            <icon-svg name="tubiao" class="site-sidebar__menu-icon"></icon-svg>
            <span slot="title">定時器任務管理</span>
          </el-menu-item>
        </el-submenu>
        <!--<sub-menu-->
        <!--v-for="menu in menuList"-->
        <!--:key="menu.menuId"-->
        <!--:menu="menu"-->
        <!--:dynamicMenuRoutes="dynamicMenuRoutes">-->
        <!--</sub-menu>-->
      </el-menu>
    </div>
  </aside>
</template>

<script>
  import en from '@/i18n/lang/en'
  import cn from '@/i18n/lang/cn'
  import SubMenu from './main-sidebar-sub-menu'
  import { isURL } from '@/utils/validate'
  export default {
    data () {
      return {
        // lang: '',
        home: cn.routeNmae.home,
        dynamicMenuRoutes: []
      }
    },
    components: {
      SubMenu
    },
    computed: {
      sidebarLayoutSkin: {
        get () { return this.$store.state.common.sidebarLayoutSkin }
      },
      sidebarFold: {
        get () { return this.$store.state.common.sidebarFold }
      },
      menuList: {
        get () { return this.$store.state.common.menuList },
        set (val) { this.$store.commit('common/updateMenuList', val) }
      },
      menuActiveName: {
        get () { return this.$store.state.common.menuActiveName },
        set (val) { this.$store.commit('common/updateMenuActiveName', val) }
      },
      mainTabs: {
        get () { return this.$store.state.common.mainTabs },
        set (val) { this.$store.commit('common/updateMainTabs', val) }
      },
      mainTabsActiveName: {
        get () { return this.$store.state.common.mainTabsActiveName },
        set (val) { this.$store.commit('common/updateMainTabsActiveName', val) }
      },
      selectLanguage: {
        get () {
          if (this.$store.state.lang.language === 'cn') {
            this.home = cn.routeNmae.home
          }
          if (this.$store.state.lang.language === 'en') {
            this.home = en.routeNmae.home
          }
          return this.$store.state.lang.language
        }
      }
    },
    watch: {
      $route: 'routeHandle'
    },
    created () {
      this.menuList = JSON.parse(sessionStorage.getItem('menuList') || '[]')
      this.dynamicMenuRoutes = JSON.parse(sessionStorage.getItem('dynamicMenuRoutes') || '[]')
      this.routeHandle(this.$route)
    },
    methods: {
      // 路由操作
      routeHandle (route) {
        if (route.meta.isTab) {
          // tab选中, 不存在先添加
          var tab = this.mainTabs.filter(item => item.name === route.name)[0]
          if (!tab) {
            if (route.meta.isDynamic) {
              route = this.dynamicMenuRoutes.filter(item => item.name === route.name)[0]
              if (!route) {
                return console.error('未能找到可用标签頁!')
              }
            }
            tab = {
              menuId: route.meta.menuId || route.name,
              name: route.name,
              title: route.meta.title,
              type: isURL(route.meta.iframeUrl) ? 'iframe' : 'module',
              iframeUrl: route.meta.iframeUrl || '',
              params: route.params,
              query: route.query
            }
            this.mainTabs = this.mainTabs.concat(tab)
          }
          this.menuActiveName = tab.menuId + ''
          this.mainTabsActiveName = tab.name
        }
        // if (this.mainTabs.length >= 10) {
        //   this.$message({
        //     message: '最多可以打開十個窗口請先關闭部分窗口继續操作',
        //     type: 'warning'
        //   })
        // }
      }
    }
  }
</script>
<style>
  .el-menu-item {
    text-align: center;
    height: 100px;
    position: relative;
  }
  /*.el-submenu{
    text-align: center;
    height: 100px;
    position: relative;
  }*/

  /* .el-tooltip{
     width: 50px;
   }*/
  /* .homeimgbox{
     width: 100%;
     height: 56px!important;
     margin: auto;
   }
   .homeimgbox img{
     height: 50px;
     margin-left: -15px;
   }
   .homespan{
     position: absolute;
     top:50px;
     left: 95px;

   }*/
</style>
















<template>
  <aside class="site-sidebar" :class="'site-sidebar--' + sidebarLayoutSkin">
    <div class="site-sidebar__inner">
      <span style="display: none">{{selectLanguage}}</span>
      <el-menu
        :default-active="menuActiveName || 'home'"
        :collapse="sidebarFold"
        :collapseTransition="false"
        class="site-sidebar__menu">
        <el-menu-item index="home" @click="$router.push({ name: 'home' })">
          <icon-svg name="shouye" class="site-sidebar__menu-icon"></icon-svg>
          <!--<div class="homeimgbox"><img src="../assets/img/home/<USER>"></div>-->
          <span class="homespan" slot="title">{{home}}</span>
        </el-menu-item>
        <el-submenu index="demo" class="alsonitem">
          <template slot="title">
            <!--<icon-svg name="tubiao" class="site-sidebar__menu-icon"></icon-svg>-->
            <div class="homeimgbox"><img src="../assets/img/home/<USER>"></div>
            <span class="alsonspan">會員管理</span>
          </template>
          <el-menu-item index="demo-echarts" @click="$router.push({ name: 'member-ipi' })">
            <icon-svg name="tubiao" class="site-sidebar__menu-icon"></icon-svg>
            <span slot="title">會員信息管理</span>
          </el-menu-item>
        </el-submenu>
        <el-submenu index="demo1">
          <template slot="title">
            <icon-svg name="shoucang" class="site-sidebar__menu-icon"></icon-svg>
            <span>作品管理</span>
          </template>
          <el-menu-item index="demo-works" @click="$router.push({ name: 'works-actor'})">
            <icon-svg name="tubiao" class="site-sidebar__menu-icon"></icon-svg>
            <span slot="title">作品表演著管理</span>
          </el-menu-item>
          <el-menu-item index="demo-ueditor" @click="$router.push({ name: 'works-list' })">
            <icon-svg name="editor" class="site-sidebar__menu-icon"></icon-svg>
            <span slot="title">作品基本信息管理</span>
          </el-menu-item>
          <el-menu-item index="demo-ueditor" @click="$router.push({ name: 'demo-ueditor' })">
            <icon-svg name="editor" class="site-sidebar__menu-icon"></icon-svg>
            <span slot="title">作品matching管理</span>
          </el-menu-item>
        </el-submenu>
        <el-submenu index="demo2">
          <template slot="title">
            <icon-svg name="shoucang" class="site-sidebar__menu-icon"></icon-svg>
            <span>合約管理</span>
          </template>
          <el-menu-item index="demo-contract" @click="$router.push({ name: 'contract-contract'})">
            <icon-svg name="tubiao" class="site-sidebar__menu-icon"></icon-svg>
            <span slot="title">合約管理</span>
          </el-menu-item>
        </el-submenu>
        <el-submenu index="demo3">
          <template slot="title">
            <icon-svg name="shoucang" class="site-sidebar__menu-icon"></icon-svg>
            <span>分配管理</span>
          </template>
          <el-menu-item index="demo-fmenu" @click="$router.push({ name: 'demo-echarts' })">
            <icon-svg name="tubiao" class="site-sidebar__menu-icon"></icon-svg>
            <span slot="title">分配清單管理</span>
          </el-menu-item>
          <el-menu-item index="demo-ueditor" @click="$router.push({ name: 'demo-ueditor' })">
            <icon-svg name="editor" class="site-sidebar__menu-icon"></icon-svg>
            <span slot="title">分配作品matching</span>
          </el-menu-item>
          <el-menu-item index="demo-ueditor" @click="$router.push({ name: 'demo-ueditor' })">
            <icon-svg name="editor" class="site-sidebar__menu-icon"></icon-svg>
            <span slot="title">分配参數配置</span>
          </el-menu-item>
          <el-menu-item index="demo-ueditor" @click="$router.push({ name: 'demo-ueditor' })">
            <icon-svg name="editor" class="site-sidebar__menu-icon"></icon-svg>
            <span slot="title">P分配</span>
          </el-menu-item>
          <el-menu-item index="demo-ueditor" @click="$router.push({ name: 'demo-ueditor' })">
            <icon-svg name="editor" class="site-sidebar__menu-icon"></icon-svg>
            <span slot="title">I分配</span>
          </el-menu-item>
          <el-menu-item index="demo-ueditor" @click="$router.push({ name: 'demo-ueditor' })">
            <icon-svg name="editor" class="site-sidebar__menu-icon"></icon-svg>
            <span slot="title">O分配</span>
          </el-menu-item>
          <el-menu-item index="demo-ueditor" @click="$router.push({ name: 'demo-ueditor' })">
            <icon-svg name="editor" class="site-sidebar__menu-icon"></icon-svg>
            <span slot="title">M分配</span>
          </el-menu-item>
          <el-menu-item index="demo-ueditor" @click="$router.push({ name: 'demo-ueditor' })">
            <icon-svg name="editor" class="site-sidebar__menu-icon"></icon-svg>
            <span slot="title">調整分配</span>
          </el-menu-item>
          <el-menu-item index="demo-ueditor" @click="$router.push({ name: 'demo-ueditor' })">
            <icon-svg name="editor" class="site-sidebar__menu-icon"></icon-svg>
            <span slot="title">保留款分配</span>
          </el-menu-item>
        </el-submenu>
        <el-submenu index="demo4">
          <template slot="title">
            <icon-svg name="shoucang" class="site-sidebar__menu-icon"></icon-svg>
            <span>支付管理</span>
          </template>
          <el-menu-item index="demo-works" @click="$router.push({ name: 'works-actor'})">
            <icon-svg name="tubiao" class="site-sidebar__menu-icon"></icon-svg>
            <span slot="title">支付管理</span>
          </el-menu-item>
        </el-submenu>
        <el-submenu index="demo5">
          <template slot="title">
            <icon-svg name="shoucang" class="site-sidebar__menu-icon"></icon-svg>
            <span>報表管理</span>
          </template>
          <el-menu-item index="demo-works" @click="$router.push({ name: 'works-actor'})">
            <icon-svg name="tubiao" class="site-sidebar__menu-icon"></icon-svg>
            <span slot="title">報表管理</span>
          </el-menu-item>
        </el-submenu>
        <el-submenu index="demo6">
          <template slot="title">
            <icon-svg name="shoucang" class="site-sidebar__menu-icon"></icon-svg>
            <span>定時器任務管理</span>
          </template>
          <el-menu-item index="demo-works" @click="$router.push({ name: 'works-actor'})">
            <icon-svg name="tubiao" class="site-sidebar__menu-icon"></icon-svg>
            <span slot="title">定時器任務管理</span>
          </el-menu-item>
        </el-submenu>
        <!--<sub-menu-->
        <!--v-for="menu in menuList"-->
        <!--:key="menu.menuId"-->
        <!--:menu="menu"-->
        <!--:dynamicMenuRoutes="dynamicMenuRoutes">-->
        <!--</sub-menu>-->
      </el-menu>
    </div>
  </aside>
</template>

<script>
  import en from '@/i18n/lang/en'
  import cn from '@/i18n/lang/cn'
  import SubMenu from './main-sidebar-sub-menu'
  import { isURL } from '@/utils/validate'
  export default {
    data () {
      return {
        // lang: '',
        home: cn.routeNmae.home,
        dynamicMenuRoutes: []
      }
    },
    components: {
      SubMenu
    },
    computed: {
      sidebarLayoutSkin: {
        get () { return this.$store.state.common.sidebarLayoutSkin }
      },
      sidebarFold: {
        get () { return this.$store.state.common.sidebarFold }
      },
      menuList: {
        get () { return this.$store.state.common.menuList },
        set (val) { this.$store.commit('common/updateMenuList', val) }
      },
      menuActiveName: {
        get () { return this.$store.state.common.menuActiveName },
        set (val) { this.$store.commit('common/updateMenuActiveName', val) }
      },
      mainTabs: {
        get () { return this.$store.state.common.mainTabs },
        set (val) { this.$store.commit('common/updateMainTabs', val) }
      },
      mainTabsActiveName: {
        get () { return this.$store.state.common.mainTabsActiveName },
        set (val) { this.$store.commit('common/updateMainTabsActiveName', val) }
      },
      selectLanguage: {
        get () {
          if (this.$store.state.lang.language === 'cn') {
            this.home = cn.routeNmae.home
          }
          if (this.$store.state.lang.language === 'en') {
            this.home = en.routeNmae.home
          }
          return this.$store.state.lang.language
        }
      }
    },
    watch: {
      $route: 'routeHandle'
    },
    created () {
      this.menuList = JSON.parse(sessionStorage.getItem('menuList') || '[]')
      this.dynamicMenuRoutes = JSON.parse(sessionStorage.getItem('dynamicMenuRoutes') || '[]')
      this.routeHandle(this.$route)
    },
    methods: {
      // 路由操作
      routeHandle (route) {
        if (route.meta.isTab) {
          // tab选中, 不存在先添加
          var tab = this.mainTabs.filter(item => item.name === route.name)[0]
          if (!tab) {
            if (route.meta.isDynamic) {
              route = this.dynamicMenuRoutes.filter(item => item.name === route.name)[0]
              if (!route) {
                return console.error('未能找到可用标签頁!')
              }
            }
            tab = {
              menuId: route.meta.menuId || route.name,
              name: route.name,
              title: route.meta.title,
              type: isURL(route.meta.iframeUrl) ? 'iframe' : 'module',
              iframeUrl: route.meta.iframeUrl || '',
              params: route.params,
              query: route.query
            }
            this.mainTabs = this.mainTabs.concat(tab)
          }
          this.menuActiveName = tab.menuId + ''
          this.mainTabsActiveName = tab.name
        }
        // if (this.mainTabs.length >= 10) {
        //   this.$message({
        //     message: '最多可以打開十個窗口請先關闭部分窗口继續操作',
        //     type: 'warning'
        //   })
        // }
      }
    }
  }
</script>
<style>
  .alsonitem{
    text-align: center;
    height: 100px!important;
    position: relative;
  }
  .el-menu-item {
    text-align: center;
    height: 56px;
    position: relative;
  }
  .alsonspan{
    display: block!important;
    width: 60px;
    height: 30px;
    margin-top: -20px;
    overflow: inherit!important;
    visibility: initial!important;
    text-align: center;
  }

  /* .el-tooltip{
     width: 50px;
   }*/
  .homeimgbox{
    width: 100%;
    height: 56px!important;
    margin: auto;
  }
  .homeimgbox img{
    height: 30px;
    margin-left: -5px;
  }

</style>
