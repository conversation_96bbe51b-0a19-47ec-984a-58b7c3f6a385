<template>
    <div>
        <el-form :inline="true" class="demo-form-inline">
            <el-form-item>
                <el-button type="primary" @click="getWork()">新增</el-button>
            </el-form-item>
            <el-form-item>
                <el-button type="success" @click="confirmEditFn()">生成文件</el-button>
            </el-form-item>
            <el-form-item>
                <el-button type="danger" @click="confirmDelFn()">批量刪除</el-button>
            </el-form-item>
        </el-form>
        <el-table
            :data="tableData"
            border
            stripe
            style="width: 100%"
            @selection-change="handleSelectionChange">
            <el-table-column
              type="selection"
              width="55">
            </el-table-column>
            <el-table-column
                prop="title"
                label="Work Title"
                >
            </el-table-column>
            <el-table-column
                prop="workId"
                label="Work No"
                width="200px">
            </el-table-column>
            <el-table-column
                prop="workSocietyCode"
                label="Soc"
                width="160px">
            </el-table-column>
            <el-table-column
                label="operation"
                width="180px">
                <template slot-scope="scope">
                    <el-button type="text" size="small" @click="deleteFn(scope.row, scope.$index)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 編輯、添加 -->
        <el-dialog :title="(opType == 'add' ? '添加' : '編輯') + '導出'" :visible.sync="show" v-if="show" :width="'500px'" :close-on-click-modal="false">
            <el-form ref="editForm" :inline="true" :model="edit" :rules="rules" label-width="120px">
                <el-form-item label="報告名稱" prop="ipBaseNo" >
                    <el-input ref="editIp" type="text" v-model="edit.reportName">
                    </el-input>
                </el-form-item>
                <el-form-item label="報告類別" prop="ipBaseNo" >
                    <el-input ref="editIp" type="text" v-model="edit.reportType">
                    </el-input>
                </el-form-item>
                <el-form-item label="前端映射地址" prop="ipBaseNo" >
                    <el-input ref="editIp" type="text" v-model="edit.webUrl">
                    </el-input>
                </el-form-item>
                <div class="t-c">
                    <el-form-item>
                        <el-button type="primary" @click="confirmEdit">確定</el-button>
                    </el-form-item>
                </div>
            </el-form>
        </el-dialog>
        <el-dialog :title="'RootPath'" :visible.sync="showPath" v-if="showPath" :width="'500px'" :close-on-click-modal="false">
            <el-form ref="editForm" :inline="true"  label-width="120px">
                <el-form-item label="rootPath" prop="rootPath" >
                    <el-input type="text" v-model="rootPath">
                    </el-input>
                </el-form-item>
                <div class="t-c">
                    <el-form-item>
                        <el-button type="primary" @click="confirmPath">確定</el-button>
                    </el-form-item>
                </div>
            </el-form>
        </el-dialog>
        <select-work v-if="workTableVisible" ref="selectWorkCom" :search="workSearch" @checkWork="checkWork"></select-work>
    </div>
</template>

<script>
    import selectWork from '../components/selectWork';
    export default {
        name: 'exportCwr',
        data () {
            return {
                show: false,
                tableData: [],
                workTableVisible: false,
                rootPath: '',
                showPath: false,
                deleteData:[],

            }
        },
        mounted () {
        },
        methods: {
            handleSelectionChange(data){
                this.deleteData=data
            },
            confirmDelFn(){
                if(this.deleteData.length){
                    this.$confirm(`確定進行[删除]操作?`, '提示', {
                        confirmButtonText: '確定',
                        cancelButtonText: '取消',
                        closeOnClickModal:false,
                        type: 'warning'
                    }).then(() => {
                        this.deleteData.forEach(item=>{
                            this.tableData.forEach((item1,index)=>{
                                if(item.workId == item1.workId){
                                    this.tableData.splice(index, 1);
                                }
                            })
                        })
                        this.$toast({tips: '刪除成功'})
                    }).catch( () => {

                    })
                }else{
                    this.$toast({tips: '請至少選擇一條作品信息'})
                    return;
                }
                
            },
            /**
             * 选取作品
             */
            getWork(index, row) {
                // this.editIndex = index;
                this.workTableVisible= true;
                this.workSearch = {
                    notAv: true,
                    work_type:'AV'
                }
                this.$nextTick( () => {
                    this.$refs.selectWorkCom.init();
                })
            },
            checkWork(info){
                console.log('info: ', info);
                info.forEach((item,index)=>{
                    console.log(index)
                    let obj={}
                    let next = true
                    obj.title = item.title ? item.title : item.title_en
                    obj.workId = item.work_id
                    obj.workSocietyCode = item.work_society_code
                    this.tableData.forEach(item1=>{
                        if(item1.workId==obj.workId){
                            next=false
                        }
                    })
                    if(next){
                        this.tableData.push(obj)
                    }
                })
                // let index = this.tableData.length;
                // this.tableData.push({
                //     title: '',
                //     workId: '',
                //     workSocietyCode: ''
                // }) 
                // this.$set(this.tableData[index], 'title', info.title ? info.title : info.title_en);
                // this.$set(this.tableData[index], 'workId', info.work_id);
                // this.$set(this.tableData[index], 'workSocietyCode', info.work_society_code);
            },
            confirmEditFn(){
                if(this.tableData.length == 0){
                    this.$toast({tips: '請至少選擇一條作品信息'})
                    return;
                }
                this.showPath = true;
            },
            confirmPath(){
                if(!this.rootPath){
                    this.$toast({tips: '請輸入rootPath'})
                    return;
                }
                let ajaxData = {
                    rootPath: this.rootPath,
                    workSocietyVoList: this.tableData
                }
                this.$http.post('/cwr/cwrFile/exportCwrFile', ajaxData).then(res => {
                    if(res.success){
                        if(res.data.code && res.data.code != 200){
                            this.$toast({tips: res.data.message})
                        }else{
                            this.$toast({tips: '导出成功'});
                            this.showPath = false;
                        }
                    }
                })
            },
            deleteFn (row, index) {
                this.$confirm(`確定進行[删除]操作?`, '提示', {
                    confirmButtonText: '確定',
                    cancelButtonText: '取消',
                    closeOnClickModal:false,
                    type: 'warning'
                }).then(() => {
                    this.tableData.splice(index, 1);
                    this.$toast({tips: '刪除成功'})
                }).catch( () => {

                })
            }
        },
        components: {
            selectWork
        }
    }
</script>
<style lang="scss" scoped>
    @import "../../../assets/scss/works.scss";

/deep/ .el-dialog{
    width: 500px;
}
</style>

