<template>
<div>
    <el-dialog :visible.sync="show" width="700px" title="Bank" :close-on-click-modal="false">
        <div style="width: 520px;margin: auto;margin-bottom: 20px">
            <el-input v-model.trim="searchInfo.bankNo" @keyup.enter.native="onSubmit()" placeholder="bankNo" style="width: 140px;"></el-input>
            <el-input v-model="searchInfo.bankName" @keyup.enter.native="onSubmit()" placeholder="bankName" style="width: 160px;"></el-input>
            <el-button type="primary" @click="onSubmit()">查詢</el-button>
            <span class="clear-search" @click="clearSearch()">清除搜索</span>
        </div>
        <el-table :empty-text="emptyText" :data="tableData">
            <el-table-column property="bankNo" label="bankNo" width="230px"></el-table-column>
            <el-table-column property="bankName" label="bankName" width="300px"></el-table-column>
            <el-table-column
                label="operation">
                <template slot-scope="scope">
                    <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                        <i class="el-icon-check" @click="checkedBank(scope.$index,scope.row)"></i>
                    </span>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :total="total" @current-change="onSubmit" :current-page="currentPage">
        </el-pagination>
    </el-dialog>
</div>
</template>
<script>

export default {
    data(){
        return{
            show:false,
            searchInfo: {},
            tableData: [],
            total: 0,
            currentPage: 1,
            emptyText: '暫無數據',
        }
    },
    props: {
        search: {
            type: Object,
            default: () => {
                return {
                    bankName: '',
                    bankNo: '',
                }

            }
        },
    },

    methods: {
        /**
         * dialog 组件调用方式： 
         * 1、父级用v-if在html里加载组件,
         * 2、父级调用子级的init方法，将数据初始化，prop传给data等
         * 
         */
        init(){
            this.show = true;
            this.searchInfo = this.$utils.copy(this.search);
            this.onSubmit();
        },
        clearSearch(){
            this.searchInfo = {
                bankName: '',
                bankNo: '',
            };
            this.onSubmit();
        },
        onSubmit(page){
            let params = this.searchInfo;
            params.page_num = page ? page : 1;
            params.page_size = 10;
            this.emptyText = '數據加載中';
            this.$http.get('/ref/bank/page', {params: params}).then(res => {
                if (res.success && res.data.code == '200') {
                    this.tableData = res.data.data.list;
                    if(! this.tableData || this.tableData.length == 0){
                        this.emptyText = '暫無數據';
                    }
                    this.tableData.forEach( (item,index) => {
                        let no = '';
                        for(let i =0; i<3-(item.bankNo + '').length;i++){
                            no += '0';
                        }
                        no += item.bankNo;
                        this.tableData[index].bankNo = no;
                    })
                    this.total = res.data.data.total;
                    this.currentPage = page ? page : 1;
                }else{
                    this.$toast({tips: res.data.message})
                }
            })
        },
        checkedBank(index, row){
            this.$emit('checkBank', row);
            this.show = false;
        }
    }
    
}
</script>
<style lang="scss" scoped>
    /deep/ .el-dialog__title{
        font-weight: bold;
    }
    /deep/ .el-dialog__body{
        text-align: center;
        padding-top: 0;
    }
</style>