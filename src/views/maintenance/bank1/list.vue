<template>
    <div>
        <el-form :inline="true" :model="formInline" class="demo-form-inline">
            <el-form-item>
                <el-input v-model="formInline.bankNo" placeholder="bankNo" style="width: 120px;"></el-input>
            </el-form-item>
            <el-form-item>
                <el-input v-model="formInline.bankName" placeholder="bankName" style="width: 120px;"></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="getList()">搜索</el-button>
            </el-form-item>
            <el-form-item>
                <el-button type="success" @click="configAdd">新增</el-button>
            </el-form-item>
            <el-form-item>
                <span class="clear-search" @click="clearSearch()">清除搜索</span>
            </el-form-item>
        </el-form>
        <el-table
            :data="tableData"
            border
            stripe
            style="width: 100%" v-loading="loading">
            <!-- <el-table-column
                prop="id"
                label="Id"
                width="120">
            </el-table-column> -->
            <el-table-column
                prop="bankNo"
                label="Bank No"
                min-width="140">
            </el-table-column>
            <el-table-column
                prop="bankName"
                label="Bank Name"
                min-width="150">
            </el-table-column>
            <el-table-column
                fixed="right"
                label="operation"
                width="200">
                <template slot-scope="scope">
                    <div class="option">
                        <el-button @click="handleEdit(scope.row)" type="text" size="small">編輯</el-button>
                        <el-button @click="handleDelete(scope.row, scope.$index)" type="text" size="small">刪除</el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :total=total @current-change="handleCurrentChange" :current-page="currentPage">
        </el-pagination>
        <!--編輯弹框-->
        <el-dialog :title="isEdit?'編輯':'新增'" :visible="isShow" width="500px" :show-close="false" :close-on-click-modal="false">
            <el-form label-position="right" label-width="140px" ref="editForm" :model="tableItem" class="demo-form-inline" :rules="rules">
                <el-form-item label="Bank No" prop="bankNo">
                    <el-input v-model="tableItem.bankNo" placeholder="Bank No"></el-input>
                </el-form-item>
                <el-form-item label="Bank Name" prop="bankName">
                    <el-input v-model="tableItem.bankName" placeholder="Bank Name"></el-input>
                </el-form-item>
            </el-form>
            <div style="text-align: center;margin-top: 50px;">
                <el-button @click="cancelDialog">取消</el-button>
                <el-button type="primary" @click="addFormItem">確定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
    export default {
        name: 'list-bank',
        data () {
            return {
                loading: false,
                formInline: {
                    page_size: 10,
                    page_num:1,
                    bankName: '',
                    bankNo: ''
                },
                currentPage: 1,
                total: 0,
                tableData: [],
                isShow:false, //显示新增弹框
                isEdit:false,
                rules:{
                    bankName:[{ required: true, message: '请输入Bank Name', trigger: 'blur' }],
                    bankNo:[{ required: true, message: '请输入Bank No', trigger: 'blur' }],
                },
                oldTableItem:{},
                tableItem: {}
            }
        },
        activated(){
            this.getList()
        },

        methods: {
            clearSearch(){
                this.formInline= {
                    page_size: 10,
                    page_num:1,
                    bankName: '',
                    bankNo: ''
                };
                this.getList();
            },
            handleCurrentChange (val) {
                this.getList(val)
            },
            handleEdit(row){
                this.isShow = true;
                this.isEdit = true;
                this.tableItem = this.$utils.copy(row);
                this.oldTableItem = this.$utils.copy(row);
            },
            getList(page){
                let params = this.$utils.copy(this.formInline);
                params.page_num = page ? page : 1;
                this.loading = true;
                this.$http.get('/ref/bank/page',{params}).then(res => {
                    this.loading = false;
                    if(res.success){
                        this.tableData = res.data.data.list || [];
                        this.total = res.data.data.total
                        this.currentPage =  params.page_num;
                    }
                })
            },
            configAdd(){
                this.isShow = true
                this.isEdit = false
                this.reset()
                if(this.$refs.editForm){
                    this.$refs.editForm.resetFields()
                }

            },
            cancelDialog(){
                this.isShow = false
                this.$refs.editForm.resetFields()
            },
            addFormItem(){
                this.$refs.editForm.validate(validate => {
                    if(validate){
                        if(this.isEdit){
                            if((this.oldTableItem.bankNo != this.tableItem.bankNo)){
                                let params={
                                    oldBankNo: this.oldTableItem.bankNo
                                }
                                this.loading = true;
                                this.$http.get('/ref/bank/checkIfHavingBranch',{params}).then(res => {
                                    if(res.success){
                                        if(res.data.code == 200){
                                            this.loading = false;
                                            if(res.data.data){
                                                this.$msgbox.confirm('下屬分行bank no都會同步更新，確定修改嗎?', '提示', {
                                                    confirmButtonText: '確定',
                                                    cancelButtonText: '取消',
                                                    type: 'warning',
                                                    closeOnClickModal: false
                                                }).then(() => {
                                                    this.addFormItemFn()
                                                }).catch(() => {        
                                                });
                                            }
                                        }
                                    }
                                })
                            }else{
                                this.addFormItemFn()
                            }
                        }else{
                            this.addFormItemFn()
                        }
                    }
                })
            },
            addFormItemFn(){
                this.$http.post('/ref/bank/edit',this.tableItem).then(res => {
                    if(res.success){
                        this.$message({
                            message:res.data.message,
                            type:res.data.code === 200?'success':'warning'
                        })
                        if(res.data.code === 200){
                            this.cancelDialog()
                            this.getList()
                        }
                    }
                })
                
            },
            reset(){
                this.isEdit = false
                this.tableItem = {
                    bankNo: '',
                    bankName: ''
                }
            },
            handleDelete(row, index){
                let params={
                    oldBankNo: row.bankNo
                }
                this.loading = true;
                this.$http.get('/ref/bank/checkIfHavingBranch',{params}).then(res => {
                    if(res.success){
                        if(res.data.code == 200){
                            this.loading = false;
                            if(res.data.data){
                                this.$msgbox.confirm('刪除bank信息後，所有下屬分行都會被刪除，確定修改嗎?', '提示', {
                                    confirmButtonText: '確定',
                                    cancelButtonText: '取消',
                                    type: 'warning',
                                    closeOnClickModal: false
                                }).then(() => {
                                    this.deleteFn(row, index);
                                }).catch(() => {        
                                });
                            }else{
                                this.$msgbox.confirm('確定刪除?', '提示', {
                                    confirmButtonText: '確定',
                                    cancelButtonText: '取消',
                                    type: 'warning',
                                    closeOnClickModal: false
                                }).then(() => {
                                    this.deleteFn(row, index);
                                }).catch(() => {        
                                });
                            }
                        }
                    }
                })
            },
            deleteFn(row, index){
                this.$http.delete('/ref/bank/'+ row.id).then( res => {
                    if(res.success){
                        this.tableData.splice(index, 1);
                        this.$toast({tips: '刪除成功'})
                    }    
                })
            }
        }
    }
</script>

<style lang="scss" scoped>
    /deep/ .el-table .cell{
        text-align: center;
    }
    .option span{
        display: inline-block;
        padding: 0 5px;

    }
    .option span:nth-child(1){
        border-right: 1px solid #ccc;
    }
    /deep/ .el-form-item__label {
        margin-left: 0 !important;
    }
</style>
