<template>
  <div class="upload">
    <top-bar :noShow="false" @save="fileUploadSubmit"></top-bar>
    <el-tabs v-model="isFile" type="border-card">
      <el-tab-pane label="文件導入" name="Y">
        <el-form :inline="true" :model="formType" class="demo-form-inline" label-width="160px" label-position="left">
          <div>
            <el-form-item label="File Type" class="is-required">
              <el-select v-model="formType.fileType" placeholder="File Type" @change="handleSelectFileType">
                <el-option label="一般清單" value="P"></el-option>
                <el-option label="單场次" value="S"></el-option>
              </el-select>
            </el-form-item>
          </div>
          <div v-if="formType.fileType != 'S'">
            <el-form-item label="Category Code" class="is-required">
              <el-autocomplete v-model="formType.categoryCode" :fetch-suggestions="querySearchCategory" placeholder="請輸入内容" :trigger-on-focus="false" @select="handleSelectCategory">
                <template slot-scope="scope">
                  <p>{{scope.item.categoryCode}}</p>
                </template>
              </el-autocomplete>
            </el-form-item>
          </div>
          <div v-if="formType.fileType != 'S'">
            <el-form-item label="Template Id">
              <el-select v-model="formType.templateId" placeholder="Template Folder">
                <el-option :label="item.folder" :value="item.id" v-for="(item,index) in templateIds" :key="index"></el-option>
              </el-select>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="Sequence" class="is-required">
              <el-input v-model="formType.sequence" style="width:207px" type="number" @input="setSequence" placeholder="請輸入1~999之间的數字"></el-input>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="音樂類型" class="is-required">
              <el-select v-model="listType" multiple placeholder="音樂類型" :disabled="listTypeDisabled">
                <el-option label="FW" value="FW"></el-option>
                <el-option label="CJ" value="CJ"></el-option>
                <el-option label="MS" value="MS"></el-option>
                <el-option label="PG" value="PG"></el-option>
              </el-select>
            </el-form-item>
          </div>
          <div style="padding-left: 16px;">
            <el-form-item>
              <el-upload ref="fileUpload" class="upload-demo" drag action="/list/uploadFile" name="files" :data="formType" :auto-upload="false" :on-change="fileChange" :on-remove="fileRemove" :http-request="uploadFile" multiple>
                <i class="el-icon-upload"></i>
                <div class="el-upload__text">
                  將文件(xlsx,xls格式)拖到此處，或
                  <em>點擊上传</em>
                </div>
                <!-- <div class="el-upload__tip" slot="tip">支持擴展名：.xlsx .xls</div> -->
              </el-upload>
            </el-form-item>
          </div>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="目錄導入" name="N">
        <el-form :inline="true" ref="form1" :model="formType1" :rules="rule1" class="demo-form-inline" label-position="left">
          <div>
            <el-form-item label="File Type" prop="fileType">
              <!--<el-select v-model="formType1.fileType" placeholder="File Type" :disabled="true" @change="handleSelectFileType">
                                <el-option label="一般清單" value="P"></el-option>
                                <el-option label="單场次" value="S"></el-option>
                            </el-select>-->
              <el-input value="單場次" :disabled="true" placeholder=""></el-input>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="輸入目錄" prop="filePath">
              <el-input v-model="formType1.filePath" placeholder="請輸入目錄地址"></el-input>
            </el-form-item>
          </div>
        </el-form>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
export default {
  data() {
    return {
      formType: {
        isFile: 'Y',
        filePath: '',
        fileType: '',
        categoryCode: '',
        sequence: 1,
        uploadType: ''
      },
      formType1: {
        isFile: 'N',
        fileType: 'S',
        filePath: '',
        categoryCode: '',
        sequence: '',
        uploadType: ''
      },
      isFile: 'Y',
      fileList: [],
      listType: [],
      listTypeDisabled: false,
      listTypeOptions: [{
        value: 'FW',
        label: 'FW'
      }, {
        value: 'CJ',
        label: 'CJ'
      }, {
        value: 'MS',
        label: 'MS'
      }, {
        value: 'PG',
        label: 'PG'
      }],
      oListTypeOptions: [{
        value: 'E4',
        label: 'E4'
      }, {
        value: 'F2',
        label: 'F2'
      }, {
        value: 'CRD',
        label: 'CRD'
      }],
      uploadType: 'routeType',
      rule1: {
        fileType: [
          { required: true, message: '请選擇', trigger: 'change' },
        ],
        filePath: [
          { required: true, message: '請輸入', trigger: 'blur' },
        ],
      },
      templateIds: []
    };
  },
  methods: {
    handleSelectFileType(val) {
      if (val === 'S') {
        this.listType = ['FW']
        this.listTypeDisabled = true
      } else {
        this.listType = []
        this.listTypeDisabled = false
      }
    },
    uploadFile(params) {
      let file = params.file
      if (file === this.fileList[this.fileList.length - 1]) {
        let formData = new FormData()
        for (let key in this.formType) {
          formData.append(key, this.formType[key])
        }
        this.fileList.map(item => {
          formData.append('files', item)
        })
        const loading = this.$loading({
          lock: true,
          text: 'uploading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        this.$http.post('/list/uploadFile', formData).then(res => {
          loading.close()
          let code = res.data.code,
            type = 'error';
          if (code == 200) {
            type = 'success'
          }
          this.$message({
            message: res.data.message || '上傳成功',
            type,
            duration: 1500,
            onClose: () => {
              if (type == 'success') {
                this.$bus.$emit('closeCurrentTab', () => {
                  this.$router.push({ name: 'uploadList', query: { update: true } });
                })
              } else {
                this.$refs.fileUpload.clearFiles()
              }
            }
          })

        }).catch(res => {
          loading.close()
          this.$toast({ tips: '上传失败' })
        })
      }
    },
    setSequence(val) {
      console.log(val)
      if (val > 999) {
        val = 999
      }
      if (val < 1) {
        val = 1
      }
      this.formType.sequence = val

    },
    fileChange(file, fileList) {
      this.fileList = []
      let type = file.name.split('.')[1].toLowerCase()
      let acceptList = ['xlsx', 'xls']
      if (acceptList.includes(type)) {
        this.fileList = []
        fileList.map(item => {
          this.fileList.push(item.raw)
        })
      } else {
        this.$toast({ tips: `請上傳${acceptList.join(',')}格式的文件` })
        this.$refs.fileUpload.clearFiles()
      }
    },
    fileRemove(file, fileList) {
      this.fileList = []
      fileList.map(item => {
        this.fileList.push(item.raw)
      })
    },
    querySearchCategory(queryString, cb) {
      if (queryString) {
        this.$http.get('/list/categorys/getListCategoryByCode', { params: { categoryCode: queryString } }).then(res => {
          if (res.success) {
            cb(res.data)
          }
        })
      }
    },
    handleSelectCategory(val) {
      this.formType.categoryCode = val.categoryCode
      this.getTemplateIdList()
    },
    fileUploadSubmit() {
      if (this.isFile === 'Y') {
        this.formType.uploadType = this.listType.join(',')
        for (const key in this.formType) {
          if (key != 'filePath' && !this.formType[key]) {
            // fileType 为單場次时，category Code 不是必填
            if (this.formType.fileType == 'S' && key == 'categoryCode') {
            } else {
              let name = key
              if (key == 'uploadType') {
                name = '音樂類型'
              }
              this.$toast({ tips: `${name}不能為空` })
              return
            }

          }
        }
        this.$nextTick(() => {
          this.$refs.fileUpload.submit();
        })
      } else {
        this.$refs.form1.validate(validate => {
          if (validate) {
            let formData = new FormData()
            console.log(this.formType1)
            for (let key in this.formType1) {
              formData.append(key, this.formType1[key])
            }
            formData.append('files', '')
            this.$http.post('/list/uploadFile', formData).then(res => {
              console.log(res)
              let message = res.data.message,
                type = 'error';
              if (res.data.code == 200) {
                message = '上傳成功'
                type = 'success'
              }
              this.$message({
                message,
                type,
                duration: 1500,
                onClose: () => {
                  if (type == 'success') {
                    this.$bus.$emit('closeCurrentTab', () => {
                      this.$router.push({ name: 'uploadList', query: { update: true } });
                    })
                  }
                }
              })

            })
          }

        })

      }


    },
    getTemplateIdList() {
      let ajaxData = {
        categoryCode: this.formType.categoryCode,
        page: {
          pageSize: 100,
          pageNum: 1
        }
      }
      this.$http.post('/listBasicFileTemplate/searchTemplate', ajaxData).then(res => {
        console.log(res)
        if (res.success) {
          this.templateIds = res.data.data.list
         delete this.formType.templateId
        }
      })
    }
  }
};
</script>
<style lang="scss" scoped>
.upload {
  padding-top: 40px;
}
.el-upload__tip {
  font-size: 14px;
  height: 20px;
  line-height: 20px;
  text-align: right;
  margin-top: 0;
}
/deep/ .el-upload-list__item-name {
  text-align: left;
}
/deep/ .el-tabs__content {
  padding-top: 40px;
}
</style>
