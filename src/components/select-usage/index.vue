<template>
<div>
    <el-dialog :visible.sync="show" width="800px" title="Usage" :close-on-click-modal="false">
        <div style="width: 310px;margin: auto;margin-bottom: 20px">
            <el-input v-model="searchInfo.workUsage" @keyup.enter.native="onSubmit()" placeholder="Work Usage" style="width: 160px;"></el-input>
            <el-button type="primary" @click="onSubmit()">查詢</el-button>
            <span class="clear-search" @click="clearSearch()">清除搜索</span>
        </div>
        <el-table :empty-text="emptyText" :data="tableData">
            <el-table-column property="workUsage" label="Usage" width="230px"></el-table-column>
            <el-table-column property="description" label="Description"></el-table-column>
            <el-table-column
                label="operation"
                width="100">
                <template slot-scope="scope">
                    <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                        <i class="el-icon-check" @click="checkedUsage(scope.$index,scope.row)"></i>
                    </span>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :total="total" @current-change="onSubmit" :current-page="currentPage">
        </el-pagination>
    </el-dialog>
</div>
</template>
<script>
import qs from 'qs'

export default {
    data(){
        return{
            emptyText:'暫無數據',
            show:false,
            searchInfo: {},
            tableData: [],
            total: 0,
            currentPage: 1 
        }
    },
    props: {
        search: {
            type: Object,
            default: () => {
                return {
                    workUsage: ''
                }

            }
        },
        showAdd: {
            type: Boolean,
            dafault: false
        }
    },

    methods: {
        /**
         * dialog 组件调用方式： 
         * 1、父级用v-if在html里加载组件,
         * 2、父级调用子级的init方法，将数据初始化，prop传给data等
         * 
         */
        init(){
            this.show = true;
            this.searchInfo = this.$utils.copy(this.search);
            this.onSubmit();
        },
        clearSearch(){
            this.searchInfo = {
                workUsage: ''
            };
            this.onSubmit();
        },
        onSubmit(page){
            let params = this.$utils.copy(this.searchInfo);
            params.page = {
                pageNum: page ? page : 1,
                pageSize: 10
            }
            if(!params.workUsage){
                delete params.workUsage;
            }
            this.emptyText = '數據加載中'
            this.$http.post('/ref/getRefWrkUsageByWorkUsage', params).then(res => {
                // loading.close();
                if (res.success) {
                    this.tableData = res.data.list;
                    this.total = res.data.total;
                    this.currentPage = page ? page : 1;
                    if(! this.tableData || this.tableData.length == 0){
                        this.emptyText = '暫無數據';
                    }
                }
            })
        },
        checkedUsage(index, row){
            this.$emit('checkUsage', row);
            this.show = false;
        }
    }
    
}
</script>