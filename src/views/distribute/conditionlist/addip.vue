<template>
    <div>
        <div style="width:80%">
            <el-form label-position="right" label-width="180px" :model="formData" class="demo-form-inline">
                <el-form-item label="dist no" prop="distNo" :rules="[{ required: true, message: '請輸入distNo', trigger: 'blur' }]">
                    <el-input
                        v-model="formData.distNo"
                        placeholder="請輸入"
                    ></el-input>
                </el-form-item>
                <el-form-item label="is include">
                    <el-switch
                        v-model="formData.isInclude"
                        active-color="#13ce66"
                        inactive-color="#e9e9e9"
                        active-value="Y"
                        inactive-value="N"
                    >
                    </el-switch>
                </el-form-item>
                <el-form-item label="list category" :rules="[{ required: true, message: '請輸入list category', trigger: 'blur' }]">
                    <!-- <el-autocomplete
                        v-model="formData.categoryCode"
                        :fetch-suggestions="getCategoryList"
                        placeholder="請輸入内容"
                        @select="handleSelect"
                        :trigger-on-focus="false"
                    >
                        <template slot-scope="scope">
                            <p style="margin:0;">category：{{scope.item.categoryCode}}</p>
                            <p style="margin:0;">desc：{{scope.item.categoryDesc}}</p>
                        </template>
                    </el-autocomplete> -->
                    <el-select 
                        v-model="formData.categoryCode" 
                        placeholder="請輸入内容" 
                        filterable 
                        remote 
                        :remote-method="getCategoryList" 
                        @change="handleSelect" 
                        style="width:220px;">
                      <el-option v-for="item in CategoryOptions" :key="item.categoryDesc" :value="item.categoryCode" style="height:68px">
                            <p style="margin:0;">category：{{item.categoryCode}}</p>
                            <p style="margin:0;">desc：{{item.categoryDesc}}</p>
                      </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="list category desc">
                    <el-input v-model="formData.categoryDescription" placeholder="選擇list category後自動獲取" :readonly="true"></el-input>
                </el-form-item>
                <el-form-item label="ip base no" class="is-required">
                    <el-input v-model="formData.ipBaseNo" @dblclick.native="showIpName()" :readonly="true" placeholder="双击選擇" :rules="[{ required: true, message: '請輸入ip base no', trigger: 'blur' }]"></el-input>
                </el-form-item>
                <el-form-item label="name">
                    <el-input v-model="formData.ipName" placeholder="" :rules="[{ required: true, message: '請輸入name', trigger: 'blur' }]" readonly></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="save">確定</el-button>
                </el-form-item>
            </el-form>
            <!--list category弹窗-->
            <el-dialog :visible.sync="showDialog" width="70%" :close-on-click-modal="false">
                <el-form :inline="true" :model="searchForm" class="demo-form-inline" @keyup.enter.native="getIpName(1)">
                    <el-form-item label="ip base no">
                        <el-input v-model.trim="searchForm.ip_no" placeholder=""></el-input>
                    </el-form-item>
                    <el-form-item label="name">
                        <el-input v-model.trim="searchForm.name" placeholder=""></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="getIpName(1)">搜索</el-button>
                    </el-form-item>
                    <el-form-item>
                        <span class="clear-search" @click="clearSearch()">清除搜索</span>
                    </el-form-item>
                </el-form>
                <el-table
                    :data="ipNameList"
                    stripe
                    style="width: 100%"
                    :empty-text="emptyText">
                    <el-table-column
                        prop="ip_base_no"
                        label="ip base no"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="chinese_name"
                        label="chinese name"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="name"
                        label="name"
                    >
                    </el-table-column>
                    <el-table-column
                        label="operation"
                        align="center"
                    >
                        <template slot-scope="scope">
                            <i class="el-icon-check" @click="chooseName(scope.row)" style="cursor:pointer;"></i>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination
                    background
                    layout="prev, pager, next" :current-page="currentPage2"
                    :total="total" @current-change="handleChange">
                </el-pagination>
            </el-dialog>
        </div>
    </div>
</template>

<script>
    import qs from 'qs'
    export default {
        name: 'addIp',
        data(){
            return {
                formData:{
                    distNo:'',
                    categoryCode:'',
                    categoryDescription:'',
                    ipBaseNo:'',
                    ipName:'',
                    isInclude:'N',
                },
                searchForm:{
                    ip_no:'',
                    name:''
                },
                showDialog:false,
                total:0,
                ipNameList:[],
                pageNum:1,
                currentPage2:1,
                isEdit:false,
                emptyText: '暫無數據',
                CategoryOptions:[]
            }
        },
        mounted(){
            this.isEdit = this.$route.name == 'distribute-editip'
            if(this.isEdit){
                this.formData = JSON.parse(this.$route.query.row)
                this.getCategoryList(this.formData.categoryCode,data=>{
                    if(data.length){
                        this.$set(this.formData,'categoryDescription',data[0].categoryDesc)
                    }
                })
            }
        },
        methods:{
            clearSearch(){
                this.searchForm={
                    ip_no:'',
                    name:''
                }
                this.getIpName(1)
            },
            showIpName(){
                this.showDialog = true
                this.searchForm={
                    ip_no:this.formData.ipBaseNo,
                    name:this.formData.ipName
                }
                this.getIpName(1)
            },
            getIpName(num){
                let params = this.$utils.copy(this.searchForm)
                params.page_num = num?num:1
                params.page_size = 10
                // let params = {
                //     ip_no:this.searchForm.ipBaseNo,
                //     name:this.searchForm.ipName,
                //     page_num:this.pageNum,
                //     page_size:10
                // }
                // if (searchbutton=='search') {
                //     this.pageNum=1
                //     params.page_num=1
                //     this.currentPage2=1
                // }
                params = qs.stringify(params)
                this.emptyText = '數據加載中';
                let config = {headers: { 'content-type': 'application/x-www-form-urlencoded' }}
                this.$http.post('/ip/name/es',params, config).then(res => {
                    if(res.success){
                        let list = res.data.list
                        this.ipNameList = list
                        this.total = res.data.total
                        this.currentPage2 = num?num:1
                        console.log(this.currentPage2)
                        if(! this.ipNameList || this.ipNameList.length == 0){
                            this.emptyText = '暫無數據';
                        }
                    }
                })
            },
            chooseName(row){
                this.formData.ipBaseNo = row.ip_base_no
                this.formData.ipName = row.name
                this.showDialog = false
            },
            // 獲取list category列表
            getCategoryList(query,cb){
                if(!query){
                    return false
                }
                this.$http.get('/distributionSpecialCondition/listCategoryList',{params:{categoryCode:query}}).then(res => {
                    console.log(res)
                    if(res.success){
                        if (res.data.code == 200) {
                            this.CategoryOptions = res.data.data
                            cb(res.data.data)
                        } else {
                            this.CategoryOptions = []
                        }
                    }
                    
                })
            },
            handleSelect(val){
                console.log(val)
                this.CategoryOptions.forEach(item=>{
                    if(item.categoryCode == val){
                        this.formData.categoryCode = item.categoryCode
                        this.$set(this.formData,'categoryDescription',item.categoryDesc)
                    }

                })
                // this.formData.categoryDescription = val.categoryDesc
            },
            save(){
                this.$http.post('/distributionSpecialCondition/saveDistListDistListFilterIp',this.formData).then(res => {
                    console.log(res)
                    if(res.success){
                        if(res.data.code === 200){
                            this.$message({
                                message: this.isEdit?'修改成功':'新增成功',
                                type:'success',
                                duration:2000,
                                onClose:() => {
                                    this.$bus.$emit('closeCurrentTab', () => { this.$router.push({name: 'distribute-condition'})});
                                }
                            })
                        }else{
                            this.$message({
                                message:res.data.message,
                                type:'warning'
                            })
                        }
                    }
                })
            },
            handleChange(page){
                this.pageNum = page
                // this.currentPage2=page
                this.getIpName(page)
            }
        },
       beforeRouteEnter (to, from, next) {
            next(vm => {
                if(to.params.distNo){
                    vm.formData.distNo = to.params.distNo
                }

            })
        }
    }
</script>

<style lang="scss" scoped>
    /deep/ .el-form-item__label {
        margin-left: 0 !important;
    }
    /deep/ .el-input.is-disabled .el-input__inner{
        color: #000;
    }
</style>
