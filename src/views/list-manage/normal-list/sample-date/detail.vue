<template>
  <div class="ipibpox">
    <!-- 返回按钮 -->
    <div style="margin-bottom: 20px;">
      <el-button @click="goBack" type="primary" icon="el-icon-arrow-left">返回列表</el-button>
    </div>

    <!-- 抽样规则信息 -->
    <el-card class="box-card" style="margin-bottom: 20px;">
      <div slot="header" class="clearfix">
        <span>抽樣規則信息</span>
      </div>
      <div class="rule-info">
        <el-row>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">Name:</span>
              <span class="value">{{ ruleInfo.name || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">Category Code:</span>
              <span class="value">{{ ruleInfo.categoryCode || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">Year:</span>
              <span class="value">{{ ruleInfo.year || '-' }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">Start Date:</span>
              <span class="value">{{ splitTime(ruleInfo.startDate) || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">End Date:</span>
              <span class="value">{{ splitTime(ruleInfo.endDate) || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">Frequency/Days:</span>
              <span class="value">{{ ruleInfo.dayExtracted }}/{{ ruleInfo.dayBase }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
              <div>
                  <span class="label">来源:</span>
                  <span class="value" v-if="tableData.length > 0 && tableData[0].source === 1">已解析数据</span>
                  <span class="value" v-else-if="tableData.length > 0 && tableData[0].source === 2">合并后数据</span>
                  <span class="value" v-else-if="tableData.length > 0 && tableData[0].source === 3">合并排序后数据</span>
                  <span class="value" v-else>暫無數據</span>
              </div>
          </el-col>
          <el-col>
              <div>
                  <span class="label">FID:</span>
                  <span class="value" v-if="ruleInfo.fids">  {{ ruleInfo.fids ? ruleInfo.fids.split(',').join(' , ') : ' ' }}</span>
                  <span class="value" v-else> </span>
              </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 搜索表单 -->
    <el-form :inline="true" :model="search" ref="form" class="demo-form-inline" @keyup.enter.native='searchFn(1)'>
      <el-form-item prop="title">
        <el-input v-model.trim="search.title" placeholder="標題"></el-input>
      </el-form-item>
      <el-form-item prop="artists">
        <el-input v-model.trim="search.artists" placeholder="表演者"></el-input>
      </el-form-item>
      <el-form-item prop="channelName">
        <el-input v-model.trim="search.channelName" placeholder="節目名稱"></el-input>
      </el-form-item>
      <el-form-item prop="performTime">
        <el-date-picker
          v-model="search.performTime"
          type="date"
          placeholder="使用日期"
          value-format="yyyy-MM-dd">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="searchFn(1)">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <span class="clear-search" @click="clearSearch()">清除搜索</span>
      </el-form-item>
    </el-form>

    <!-- 数据表格 -->
    <el-table
      :empty-text="tableresult"
      stripe
      :data="tableData"
      border
      style="width: 100%"
      v-loading="loading">

      <el-table-column prop="id" label="ID" width="80" fixed="left">
      </el-table-column>

<!--     <el-table-column prop="source" label="来源" min-width="120">-->
<!--       <template slot-scope="scope">-->
<!--         <span v-if="scope.row.source == 1">已解析数据</span>-->
<!--         <span v-else-if="scope.row.source == 2">合并后数据</span>-->
<!--         <span v-else-if="scope.row.source == 3">合并排序后数据</span>-->
<!--         <span v-else>{{scope.row.source}}</span>-->
<!--       </template>-->
<!--     </el-table-column>-->

      <el-table-column prop="title" label="標題" min-width="200" show-overflow-tooltip>
      </el-table-column>

      <el-table-column prop="artists" label="表演者" min-width="150" show-overflow-tooltip>
        <template slot-scope="scope">
            {{ scope.row.artists !== null ? formatMultipleValues(scope.row.artists) : ''}}
        </template>
      </el-table-column>

      <el-table-column prop="authors" label="作詞" min-width="150" show-overflow-tooltip>
        <template slot-scope="scope">
          {{ scope.row.authors !== null ? formatMultipleValues(scope.row.authors) : ''}}
        </template>
      </el-table-column>

      <el-table-column prop="composers" label="作曲" min-width="150" show-overflow-tooltip>
        <template slot-scope="scope">
          {{ scope.row.composers !== null ? formatMultipleValues(scope.row.composers) : '' }}
        </template>
      </el-table-column>

      <el-table-column prop="albumTitle" label="專輯" min-width="150" show-overflow-tooltip>
      </el-table-column>

      <el-table-column prop="publisher" label="發行公司" min-width="150" show-overflow-tooltip>
      </el-table-column>

      <el-table-column prop="clickNumber" label="播放次數" min-width="100">
        <template slot-scope="scope">
          {{ formatNumber(scope.row.clickNumber) }}
        </template>
      </el-table-column>

      <el-table-column prop="channelName" label="節目名稱" min-width="150" show-overflow-tooltip>
      </el-table-column>

      <el-table-column prop="performTime" label="使用日期" min-width="120">
        <template slot-scope="scope">
          {{ splitTime(scope.row.performTime) }}
        </template>
      </el-table-column>

      <el-table-column prop="workId" label="作品ID" min-width="100">
      </el-table-column>

      <el-table-column prop="workSocietyCode" label="作品協會代碼" min-width="120">
      </el-table-column>

      <el-table-column prop="uploadType" label="音樂類型" min-width="120">
      </el-table-column>

      <el-table-column prop="isrc" label="ISRC" min-width="120">
      </el-table-column>

      <el-table-column prop="iswc" label="ISWC" min-width="120">
      </el-table-column>


    </el-table>

    <!-- 分页 -->
    <el-pagination
      background
      layout="prev, pager, next"
      :total="total"
      :current-page="search.page.pageNum"
      @current-change="handleCurrentChange">
    </el-pagination>

    <!-- FID弹窗 -->
    <el-dialog
      title="FID详情"
      :visible.sync="fidDialogVisible"
      width="60%"
      :before-close="closeFidDialog">
      <div class="fid-content">
        <el-input
          type="textarea"
          :rows="10"
          :value="ruleInfo.fids"
          readonly
          placeholder="暂无FID数据">
        </el-input>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeFidDialog">关闭</el-button>
        <el-button type="primary" @click="copyFids">复制</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'sampleDateDetail',
  data() {
    return {
      tableData: [],
      tableresult: ' ',
      total: 0,
      ruleInfo: {}, // 抽样规则信息
      search: {
        sampleRuleBaseId: null, // 抽样规则基础ID
        title: '',
        artists: '',
        channelName: '',
        performTime: '',
        page: {
          pageNum: 1,
          pageSize: 10
        }
      },
      loading: false,
      fidDialogVisible: false, // 控制FID弹窗显示
      fids:[]
    }
  },
  mounted() {
    this.init();
  },
  activated() {
    this.$nextTick(() => {
      if (this.$route.query.update) {
        let query = this.$route.query;
        delete query.update;
        this.init();
      }
    })
  },
  methods: {
    init() {
      // 从路由参数获取抽样规则信息
      if (this.$route.params.row) {
        this.ruleInfo = JSON.parse(JSON.stringify(this.$route.params.row));
        this.search.sampleRuleBaseId = this.ruleInfo.id;
        this.searchFn();
      } else {
        this.$message.error('缺少抽樣規則信息');
        this.goBack();
      }
    },
    splitTime(time) {
      if (!time) return '';
      return time.split(' ')[0];
    },
    formatDateTime(time) {
      if (!time) return '';
      return time;
    },
    formatMultipleValues(value) {
      if (!value) return '';
      // 如果是分号分隔的多个值，可以在这里进行格式化处理
      return value;
    },
    formatNumber(number) {
      if (!number && number !== 0) return '';
      // 格式化数字，添加千分位分隔符
      return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    },
    searchFn(page) {
      // 只有在主动搜索时才重置页码（page参数为1），分页切换时不重置
      if (page === 1) {
        this.search.page.pageNum = 1;
      }

      if (!this.search.sampleRuleBaseId) {
        this.$message.error('缺少抽樣規則基礎ID');
        return;
      }

      this.tableresult = '數據加載中...';
      this.loading = true;

      // 构建请求参数
      const queryVo = {
        sampleRuleBaseId: this.search.sampleRuleBaseId,
        title: this.search.title,
        artists: this.search.artists,
        channelName: this.search.channelName,
        performTime: this.search.performTime,
        page: {
          pageNum: this.search.page.pageNum,
          pageSize: this.search.page.pageSize
        }
      };

      this.$http.post('/list2BasicSampleDataMappingQuery/getList2BasicSampleDataMappingBySampleRuleBaseId', queryVo)
        .then(res => {
          this.loading = false;
          console.log(res);
          if (res.success) {
            this.tableData = res.data.list || [];
            this.total = res.data.total || 0;
          } else {
            this.$message.error(res.message || '獲取數據失敗');
            this.tableData = [];
            this.total = 0;
          }
          this.tableresult = this.tableData.length == 0 ? '暫無數據' : ' ';
        })
        .catch(error => {
          this.loading = false;
          console.error('API調用失敗:', error);
          this.$message.error('獲取數據失敗');
          this.tableData = [];
          this.total = 0;
          this.tableresult = '暫無數據';
        });
    },
    handleCurrentChange(val) {
      this.search.page.pageNum = val;
      this.searchFn();
    },
    clearSearch() {
      this.search.title = '';
      this.search.artists = '';
      this.search.channelName = '';
      this.search.performTime = '';
      this.search.page.pageNum = 1;
      this.searchFn();
    },
    goBack() {
      this.$router.go(-1);
    },
    // 显示FID弹窗
    showFidDialog() {
      if (this.ruleInfo.fids) {
        this.fidDialogVisible = true;
      }
    },
    // 关闭FID弹窗
    closeFidDialog() {
      this.fidDialogVisible = false;
    },
    // 复制FID内容
    copyFids() {
      if (this.ruleInfo.fids) {
        // 创建临时textarea元素来复制文本
        const textarea = document.createElement('textarea');
        textarea.value = this.ruleInfo.fids;
        document.body.appendChild(textarea);
        textarea.select();
        document.execCommand('copy');
        document.body.removeChild(textarea);

        this.$message.success('FID内容已复制到剪贴板');
      }
    }
  }
}
</script>

<style scoped>
.rule-info {
  padding: 10px 0;
}

.info-item {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}

.info-item .label {
  font-weight: bold;
  margin-right: 10px;
  min-width: 100px;
  color: #606266;
}

.info-item .value {
  color: #303133;
}

.clear-search {
  color: #409EFF;
  cursor: pointer;
  font-size: 14px;
}

.clear-search:hover {
  color: #66b1ff;
}

.box-card {
  margin-bottom: 20px;
}

.fid-link {
  color: #409EFF;
  cursor: pointer;
  text-decoration: underline;
}

.fid-link:hover {
  color: #66b1ff;
}

.fid-content {
  margin: 20px 0;
}
</style>
