/**
 * element-ui组件主题
 *
 * tips:
 *  1. 此处只包含element-ui组件主题
 *  2. 要达到整站主题修改效果, 请确保[import './element-[#17B3A3]/index.css']当前主题色与[/src/assets/scss/_variables.scss]文件中[$--color-primary]属性值一致
 */
import './element-#409EFF/index.css' // 当前主题色
export default {
  list: [
    '#0BB2D4', // 青色
    '#3E8EF7', // 蓝色
    '#11C26D', // 绿色
    '#17B3A3', // 蓝绿色
    '#667AFA', // 靛青色
    '#997B71', // 棕色
    '#9463F7', // 紫色
    '#757575', // 灰色
    '#EB6709', // 橙色
    '#F74584', // 粉红色
    '#FCB900', // 黄色
    '#FF4C52',  // 红色
    '#409EFF', //浅蓝色
  ]
}
