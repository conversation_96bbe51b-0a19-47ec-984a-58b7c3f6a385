<template>
<!-- 拆分後的文件 用于審核用的 -->
    <div class="ipibpox">
        <el-form :inline="true" :model="search" ref="form" class="demo-form-inline" @keyup.enter.native="searchFn(1)">
            <el-form-item>
                <el-select v-model="search.dataType" placeholder="所有類型" style="width: 120px;">
                    <el-option value="AVR" label="AVR"></el-option>
                    <el-option value="CWR" label="CWR"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item prop="Work Title">
                <el-input v-model.trim="search.fId" placeholder="FID" style="width: 100px;"></el-input>
            </el-form-item>
            <el-form-item prop="Work Title">
                <el-input v-model.trim="search.workTitles" placeholder="title" style="width: 220px;"></el-input>
            </el-form-item>
            <el-form-item>
                <el-button :type="(search.matchScore < 0 && search.matchScore != 99)  ? 'primary' : ''" @click="changeSearchScore(-1)">10分以下</el-button>
                <el-button :type="(search.matchScore == 0 && search.matchScore != 99) ? 'primary' : ''" @click="changeSearchScore(0)">10-20分</el-button>
                <el-button :type="(search.matchScore > 0 && search.matchScore != 99) ? 'primary' : ''" @click="changeSearchScore(1)">20分以上</el-button>
            </el-form-item>
            <el-form-item>
                <el-select v-model="search.status" placeholder="所有狀態" style="width: 120px;">
                    <el-option label="所有狀態" value=""></el-option>
                    <el-option v-for=" (value, key) in config.status" :key="key" :value="key" :label="value"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="searchFn()">搜索</el-button>
            </el-form-item>
            <el-form-item>
                <span class="clear-search" @click="clearSearch()">清除搜索</span>
            </el-form-item>
        </el-form>
        <el-table stripe :data="tableData" border style="width: 100%" :empty-text="emptyText">

            <el-table-column
                prop="fId"
                label="FID"
                width="80px">
            </el-table-column>
            <el-table-column
                prop="workTitles"
                label="Title"
                min-width="280px">
                <template slot-scope="scope">
                    <span :title="scope.row.workTitles">{{scope.row.workTitles}}</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="artistNames"
                label="Aritists">
                <template slot-scope="scope">
                    <span :title="scope.row.artistNames">{{scope.row.artistNames}}</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="authorNames"
                label="Author"
                min-width="90">
                <template slot-scope="scope">
                    <span :title="scope.row.authorNames">{{scope.row.authorNames | addSpace}}</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="composerName"
                label="Composer"
                min-width="120">
                <template slot-scope="scope">
                    <span :title="scope.row.composerName">{{scope.row.composerName | addSpace}}</span>
                </template>
            </el-table-column>
            <el-table-column
                label="Duration"
                width="120px">
                <template slot-scope="scope">
                    <span v-if="scope.row.durationM || scope.row.durationSs">{{scope.row.durationM + ':' + scope.row.durationSs}}</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="matchWorkTitle"
                label="Match Work Titile"
                min-width="170px">
                <template slot-scope="scope">
                    <span :title="scope.row.matchWorkTitle">{{scope.row.matchWorkTitle}}</span>
                </template>
            </el-table-column>
            <el-table-column
                label="Soc-WorkId"
                min-width="130">
                <template slot-scope="scope">
                    {{(scope.row.matchWorkSoc ? scope.row.matchWorkSoc : '') + (scope.row.matchWorkId ? '-'+scope.row.matchWorkId : '')}}
                </template>
            </el-table-column>
            <el-table-column
                prop="matchScore"
                label="Score"
                width="70px">
            </el-table-column>
            <el-table-column
                prop="dataType"
                label="Type"
                width="80px">
            </el-table-column>
            <el-table-column
                prop="status"
                label="status"
                width="80px">
                <template slot-scope="scope">
                    {{scope.row.status?'已審核':'待審核'}}
                </template>
            </el-table-column>
            <el-table-column
                fixed="right"
                label="OP"
                width="60px">
                <template slot-scope="scope">
                    <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer" v-if="!scope.row.status">
                        <span class="a-blue" @click="audit(scope.row)">審核</span>
                    </span>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :total="total" @current-change="searchFn" :current-page="currentPage">
        </el-pagination>
    </div>
</template>

<script>
    export default {
        name: 'sampleDateList',
        data () {
            return {
                tableData: [],
                total: 0,
                currentPage: 1,
                search: {
                    dataType: 'AVR',
                    matchTitles: '',
                    status: '0',

                    matchScore: 99

                },
                loading: false,
                config: {
                    status: {
                        0: '待審核',
                        1: '已審核'
                    },
                    uploadType: ['PG','FW','MS','CJ']
                },
                emptyText: '數據加載中',
            }
        },
        filters: {
            Status: function(status){
                let config = {
                    0: '待審核',
                    1: '已審核'
                }
                return config[status]
            }
        },
        created(){
            this.init();
        },
        activated(){
            // if(this.$route.query.update){
            //     this.init();
            // }
            this.$nextTick( () => {
                if(this.$route.query.update){
                    let query = this.$route.query;
                    delete query.update;
                    this.init()
                }
            })
        },
        methods: {
            clearSearch(){
                this.search = {
                    dataType: 'AVR',
                    matchTitles: '',
                    status: ''
                }
                this.searchFn();
            },
            init(){
                this.searchFn(1);
            },
            searchFn (page) {
                this.loading = true;
                let ajaxData = {};
                ajaxData.page = {
                    pageNum: page ? page : 1,
                    pageSize: 10
                }
                ajaxData.data = this.$utils.copy(this.search);
                if(ajaxData.data.matchScore == 99){
                    delete ajaxData.data.matchScore;
                }
                this.emptyText = '數據加載中';
                this.$http.post('/work/check/getMatchsList', ajaxData).then(res => {
                    if (res.success) {
                        this.tableData = res.data.list;
                        if(! this.tableData || this.tableData.length == 0){
                            this.emptyText = '暫無數據';
                        }
                        this.total = res.data.total;
                        this.currentPage = page ? page : 1;
                    }
                    this.loading = false;
                })
            },
            audit (item) {
                if(item.dataType == 'AVR'){
                    this.$router.push({name: 'removalAuditAvr', query: {id: item.id, type: item.dataType, title: item.workTitles, nameId: item.id,update:true}})
                }else{
                    this.$router.push({name: 'removalAudit', query: {id: item.id, type: item.dataType, title: item.workTitles, nameId: item.id,update:true}})
                }
            },
            changeSearchScore (score) {
                this.search.matchScore = score;
            }
        }
    }
</script>

<style>
</style>
