<template>
  <div>
    <el-dialog
      :visible.sync="show"
      width="500px"
      title="導入"
      :close-on-click-modal="false"
    >
      <el-form class="demo-form-inline">
        <el-form-item label="type：" class="is-required">
          <el-select v-model="type" placeholder="type" style="width: 120px">
            <!-- <el-option label="全部" value=""></el-option> -->
            <el-option label="ADD" :value="'ADD'"></el-option>
            <el-option label="UPDATE" :value="'UPDATE'"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="選擇文件：" class="is-required">
          <el-upload
            ref="fileUpload"
            class="upload-demo"
            action=""
            accept=".xls,.xlsx"
            drag
            :on-change="uploadChange"
            :file-list="fileList"
            :show-file-list="true"
            :http-request="uploadFile"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">將.xlsx/.xls文件拖到此處，或<em>點擊上傳</em>
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item>
          <el-button
            style="margin-left: 80px; width: 130px"
            type="primary"
            @click="uploadFileIswc"
            >upload</el-button
          >
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
export default {
  name: "",
  data() {
    return {
      show: false,
      type: "ADD",
      fileList: [],
      formData: new FormData()
    };
  },
  methods: {
    showPop(){
        this.show=true
    },
    uploadFileIswc() {
      // console.log(this.type);
      if (!this.type) {
        this.$toast({ tips: "請選擇type" });
        return;
      } else if (!this.fileList.length) {
        this.$toast({ tips: "請選擇上傳文件" });
        return;
      }
      this.formData.append("type", this.type);
      const loading = this.$loading({
        lock: true,
        text: "uploading",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      console.log(this.formData)
      this.$http
        .post("/wrkWorkIswc/uploadIswcWorkData", this.formData)
        .then((res) => {
          loading.close();
          if(res.success){
            this.show=false
            this.type= "ADD",
            this.fileList=[]
            this.formData= new FormData()
            if(res.data.code == 200){
              this.$emit('update',res.data.data);
            }else{
              this.$toast({ tips: res.data.message });
            }
          }
        //   let code = res.data.code,
        //     type = "error";
        //   if (code == 200) {
        //     type = "success";
        //   }
        //   this.$message({
        //     message: res.data.message || "上傳成功",
        //     type,
        //     duration: 1500,
        //     onClose: () => {
        //       if (type == "success") {
        //         this.$bus.$emit("closeCurrentTab", () => {
        //           this.$router.push({
        //             name: "iswc",
        //             query: { update: true },
        //           });
        //         });
        //       } else {
        //         this.$refs.fileUpload.clearFiles();
        //       }
        //     },
        //   });
        })
        .catch((res) => {
          loading.close();
          this.$toast({ tips: "上傳失敗" });
        });
    },
    uploadFile(params) {
      let file = params.file;
      if (file === this.fileList[this.fileList.length - 1]) {
        // let formData = new FormData();
        for (let key in this.formType) {
          this.formData.append(key, this.formType[key]);
        }
        this.fileList.map((item) => {
          this.formData.append("file", item);
        });
      }
    },
    uploadChange(file, fileList) {
      // console.log(file);
      // console.log(fileList);
      // let lastIndexOfDot = file.name.lastIndexOf('.')
      // let type = file.name.slice(lastIndexOfDot+1,file.name.length).toLowerCase()
      // let typeList = ['xls','xlsx']
      // if(!typeList.includes(type)){
      //     this.$toast({tips:'請上傳xls,xlsx格式的文件'})
      //     this.$refs['fileUpload'].clearFiles()
      //     return
      // }
      this.fileList = [];
      fileList.map((item) => {
        this.fileList.push(item.raw);
      });
    },
  },
};
</script>
<style scoped>
.el-icon-upload {
  font-size: 50px;
}
/* .hint{
  line-height: 20px;
  margin-top: -5px;
  font-size: 14px;
} */
</style>