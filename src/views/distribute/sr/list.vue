<template>
  <div>
    <el-form :inline="true" :model="searchForm" class="demo-form-inline" @keyup.enter.native="querySearch(1)">
      <el-form-item prop="adjDistNo" label="Dist No" class="is-required">
        <el-input v-model="searchForm.adjDistNo" placeholder="" style="width: 100px"></el-input>
      </el-form-item>
      <el-form-item label="old dist No">
        <el-input v-model="searchForm.oldDistNo" placeholder="" style="width: 100px"></el-input>
      </el-form-item>
      <el-form-item label="work No">
        <el-input v-model="searchForm.workId" placeholder="" style="width: 100px"></el-input>
      </el-form-item>
      <el-form-item label="ip name">
        <el-input v-model="searchForm.ipName" :title="searchForm.ipName" placeholder="雙擊選擇" style="width: 150px" @dblclick.native="getIp()" readonly></el-input>
      </el-form-item>
      <el-form-item label="ip name no">
        <el-input v-model="searchForm.ipNameNo" placeholder="雙擊選擇" style="width: 140px" @dblclick.native="getIp()" readonly></el-input>
      </el-form-item>
      <el-form-item label="ip base No">
        <el-input v-model="searchForm.ipBaseNo" placeholder="雙擊選擇" style="width: 140px" @dblclick.native="getIp()" readonly></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="add" type="primary" v-if="isAuth('distribute:sr:list:add')">新增</el-button>
        <el-button @click="querySearch(1)" type="primary" v-if="isAuth('distribute:sr:list:find')">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <span class="clear-search" @click="clearSearch()" v-if="isAuth('distribute:sr:list:find')">清除搜索</span>
      </el-form-item>
    </el-form>
    <el-table :data="tableData" border stripe v-loading="loading" style="width: 100%" :empty-text="emptyText">
      <el-table-column prop="adjDistNo" label="dist no">
      </el-table-column>
      <el-table-column prop="oldDistNo" label="old dist no">
      </el-table-column>
      <el-table-column prop="categoryCode" label="category code">
      </el-table-column>
      <el-table-column prop="title" label="work title">
      </el-table-column>
      <el-table-column prop="workId" label="work No">
      </el-table-column>
      <el-table-column prop="workSocietyCode" label="work soc">
      </el-table-column>
      <el-table-column prop="title" label="adj work title">
      </el-table-column>
      <el-table-column prop="adjWorkId" label="adj work no">
      </el-table-column>
      <el-table-column prop="adjWorkSocietyCode" label="adj work soc">
      </el-table-column>
      <el-table-column prop="distAmount" label="待分配金額">
      </el-table-column>
    </el-table>
    <el-pagination background layout="prev, pager, next" :current-page="currentPage" :total="total" @current-change="handleCurrentChange">
    </el-pagination>
    <select-ip v-if="IpTableVisible" ref="selectIpCom" :search="ipSearch" @checkIp="checkIp"></select-ip>
  </div>
</template>

<script>
import selectIp from '@/components/select-ip';
export default {
  name: 'distribute-sr',
  data() {
    return {
      searchForm: {
        adjDistNo: '',
        oldDistNo: '',
        adjType: 'SR',
        adjMethod: '',
        distFlag: '',
        workId: '',
        ipName: '',
        ipNameNo: '',
        ipBaseNo: '',
      },
      tableData: [],
      total: 0,
      isPull: false,
      loading: false,
      IpTableVisible: false,
      ipSearch: {},
      emptyText: '暫無數據',
      currentPage:1,

    }
  },
  components: {
    selectIp
  },
  methods: {
    clearSearch() {
      this.searchForm = {
        adjDistNo: '',
        oldDistNo: '',
        adjType: 'SR',
        adjMethod: '',
        distFlag: '',
        workId: '',
        ipName: '',
        ipNameNo: '',
        ipBaseNo: '',
      }
      this.tableData = []
      this.total = 0
    },
    handleCurrentChange(page) {
      if (this.isPull) {
        this.pullData(page)
      } else {
        this.querySearch(page)
      }
    },
    querySearch(pageNum) {
      if (!this.checkDistNo()) {
        return false
      }
      this.loading = true
      this.isPull = false
      let params = this.searchForm
      params.page_num = pageNum
      this.emptyText = '數據加載中';
      this.$http.get('/dist/adj/listDistAdjSdsrHeaderWithPage', { params }).then(res => {
        console.log(res)
        if (res.success) {
          if(res.data.data){
            this.tableData = res.data.data.list;
            this.total = res.data.data.total;
            this.currentPage = params.page_num;
          }else{
            this.tableData=[]
            this.total=1
            this.currentPage=1
          }
          if(! this.tableData || this.tableData.length == 0){
            this.emptyText = '暫無數據';
          }
        }
        this.loading = false
      }).catch(res => {
        this.loading = false
      })
    },
    checkDistNo() {
      let no = this.searchForm.adjDistNo
      if (no) {
        return true;
      } else {
        this.$message({
          message: 'distNo不能為空',
          type: 'warning'
        })
        return false
      }
    },
    add() {

      this.$router.push({ name: 'distribute-sr-detail' })
    },
    getIp() {
      this.IpTableVisible = true;
      this.ipSearch = {
        name_no: '',
        name: '',
        soc: ''
      }
      this.$nextTick(() => {
        this.$refs.selectIpCom.init();
      })
    },
    checkIp(info) {
      this.$set(this.searchForm, 'ipNameNo', info.ip_name_no);
      this.$set(this.searchForm, 'ipName', info.name);
      this.$set(this.searchForm, 'ipBaseNo', info.ip_base_no);
      this.IpTableVisible = false;
    },
  }
}
</script>

<style scoped>
</style>
