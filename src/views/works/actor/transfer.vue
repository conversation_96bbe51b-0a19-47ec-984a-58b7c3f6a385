<template>
    <div>
        <!-- <el-card class="box-card saveright"><el-button type="primary" @click="savewrk">保 存</el-button></el-card> -->
        <top-bar :noShow="false" @save="saveTRansfer"></top-bar>
        <el-collapse v-model="activeNames" class="contentbox">
            <el-collapse-item class="step-jump works" title="Transfer From" name="1">
                <div class="boxline p-t-10">
                    <el-form :inline="true" :model="performFrom" label-width="150px" label-position="right" class="demo-form-inline">
                        <div>
                            <!-- <el-form-item label="id">
                                <el-input v-model="performFrom.id" placeholder="" disabled></el-input>
                            </el-form-item> -->
                            <el-form-item label="Performer ID">
                                <el-input v-model="performFrom.orcalArtistId" placeholder="" readonly></el-input>
                            </el-form-item>
                            <el-form-item label="Ip Name No">
                                <el-input v-model="performFrom.ipNameNo" placeholder="" readonly></el-input>
                            </el-form-item>
                        </div>
                        <div>
                            <el-form-item label="Last Name">
                                <el-input v-model="performFrom.lastName" placeholder="" style="width: 620px;" readonly></el-input>
                            </el-form-item>
                        </div>
                        <div>
                            <el-form-item label="First Name">
                                <el-input v-model="performFrom.firstName" placeholder="" style="width: 620px;" readonly></el-input>
                            </el-form-item>
                        </div>
                        <div>
                            <el-form-item label="Chinese Name">
                                <el-input v-model="performFrom.chineseName" placeholder="" style="width: 620px;" readonly></el-input>
                            </el-form-item>
                        </div>
                    </el-form>
                </div>
            </el-collapse-item>
            <el-collapse-item class="step-jump" title="Transfer To" name="2">
                <div class="boxline p-t-10">
                    <el-form :inline="true" :model="performTo" label-width="150px" label-position="right" class="demo-form-inline">
                        <div>
                            <!-- <el-form-item label="id">
                                <el-input v-model="performFrom.id" placeholder="" disabled></el-input>
                            </el-form-item> -->
                            <el-form-item label="Performer ID">
                                <el-input v-model="performTo.orcalArtistId" placeholder="雙擊查詢" @dblclick.native="getArtist(1)" readonly ></el-input>
                            </el-form-item>
                            <el-form-item label="Ip Name No">
                                <el-input v-model="performTo.ipNameNo" placeholder="雙擊查詢" @dblclick.native="getArtist(1)" readonly></el-input>
                            </el-form-item>
                        </div>
                        <div>
                            <el-form-item label="Last Name">
                                <el-input v-model="performTo.lastName" placeholder="雙擊查詢" @dblclick.native="getArtist(1)" style="width: 620px;" readonly></el-input>
                            </el-form-item>
                        </div>
                        <div>
                            <el-form-item label="First Name">
                                <el-input v-model="performTo.firstName" placeholder="" style="width: 620px;" readonly></el-input>
                            </el-form-item>
                        </div>
                        <div>
                            <el-form-item label="Chinese Name">
                                <el-input v-model="performTo.chineseName" placeholder="" style="width: 620px;" readonly></el-input>
                            </el-form-item>
                        </div>
                    </el-form>
                </div>
            </el-collapse-item>
        </el-collapse>

        <el-dialog :visible.sync="artistDialogVisible" width="1100px" :close-on-click-modal="false" title="Performer">
            <div style="width: 920px;margin: auto;margin-bottom: 20px">
                <el-input @keyup.enter.native="getArtist()" v-model.number.trim="searchData.orcalArtistId" placeholder="Performer ID" style="width: 130px;"></el-input>
                <el-input @keyup.enter.native="getArtist()" v-model.trim="searchData.refArtistId" placeholder="Ref Artist ID" style="width: 130px;"></el-input>
                <el-input @keyup.enter.native="getArtist()" v-model="searchData.romanName" placeholder="Name" style="width:250px;"></el-input>
                <el-input @keyup.enter.native="getArtist()" v-model.trim="searchData.ipNameNo" placeholder="IP Name No" style="width: 180px;"></el-input>                
                <el-button type="primary" @click="getArtist()">查詢</el-button>
                <span class="clear-search" @click="clearSearch()">清除搜索</span>
            </div>
            <el-table :empty-text="emptyText" border :data="artistData">
                <el-table-column property="orcalArtistId" label="Performer ID" width="150px"></el-table-column>
                <el-table-column prop="refArtistId" label="Ref Artist ID" width="150px"></el-table-column>
                <el-table-column property="artistName" label="Name" width="250px"></el-table-column>
                <el-table-column property="chineseName" label="ChineseName" width="250px"></el-table-column>
                <el-table-column property="ipNameNo" label="IP Name No" width="150px"></el-table-column>
                <el-table-column label="operation" width="120">
                <template slot-scope="scope">
                    <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                        <i class="el-icon-check" @click="checkedPerformTo(scope.$index,scope.row)"></i>
                    </span>
                </template>
            </el-table-column>
            </el-table>
            <el-pagination
                background
                layout="prev, pager, next"
                :total="artistTotal" @current-change="getArtist" :current-page="currentPage">
            </el-pagination>
        </el-dialog>
    </div>
</template>

<script>
    // import axios from '../../utils/httpRequest'
    export default {
        name: 'addinfo',
        data () {
            return {
                checked: false,
                activeNames: ['1', '2',],
                activeNames2:'3',
                artistDialogVisible: false,
                performFrom: {
                    // id: '',
                    orcalArtistId: '',
                    firstName: '',
                    lastName: '',
                    chineseName: '',
                    // chinese: '',
                    ipNameNo: ''
                },
                performTo: {
                    // id: '',
                    orcalArtistId: '',
                    firstName: '',
                    lastName: '',
                    chineseName: '',
                    ipNameNo: ''
                },
                searchData:{
                    romanName:'',
                    ipNameNo:'',
                    refArtistId:'',
                    orcalArtistId:'',
                    page: {
                        pageNum: 1,
                        pageSize: 10
                    }
                },
                emptyText: ' ',
                currentPage: 0,
                artistTotal: 0,
                artistData: []

            }
        },
        methods: {   
            getInfo(){
                let params = this.id;
                this.$http.get('/wrk/artist/queryWrkWorkById/?id=' + params).then(res => {
                    if (res.status === 200) {
                        this.performFrom = res.data;
                        if(this.performFrom.band == 'Y'){
                            this.checked = true;
                        }
                    }
                })
            },
            showArtist(){
                this.artistDialogVisible = true
            },
            getArtist (num) {
                // let params = this.pageinfo
                let params = this.searchData
                console.log(params)
                params.page.pageNum = num ? num : 1;
                if(!this.artistDialogVisible){
                    this.artistDialogVisible = true
                    this.clearSearchDialog()
                }
                // params.tisN = this.searchTisN ? this.searchTisN : null;
                this.emptyText = '數據加載中...'
                this.$http.post('/wrk/artist/queryWrkArtistListByParams', params).then(res => {
                    if (res.status === 200) {
                            this.artistData = res.data.list
                            this.artistTotal = res.data.total||1
                            this.currentPage = params.page.pageNum
                            this.emptyText = this.artistData.length == 0 ? '暫無數據' : ' '
                        }
                })
            },
            clearSearch(){
                this.searchData={
                    romanName:'',
                    ipNameNo:'',
                    refArtistId:'',
                    orcalArtistId:'',
                    page: {
                        pageNum: 1,
                        pageSize: 10
                    }
                }
                this.getArtist()
            },
            clearSearchDialog(){
                this.searchData.romanName=''
                this.searchData.orcalArtistId=''
                this.searchData.ipNameNo=''
                this.searchData.refArtistId=''
            },
            saveTRansfer(){
                if(!this.performTo.orcalArtistId){
                    this.$toast({tips: 'Transfer To的Performer ID不能為空！'})
                    return
                }

                if(this.performFrom.orcalArtistId == this.performTo.orcalArtistId){
                    this.$toast({tips: 'Transfer From和Transfer To的Performer ID不能相同！'})
                    return
                }

                let params = {}
                params.from = this.performFrom
                params.to = this.performTo

                this.$http.post('/artistTransfer/transfer', params).then(res => {
                    if (res.status === 200 && res.data.code === 200) {
                            this.$toast({tips: 'Transfer成功'})
                            this.$bus.$emit('closeCurrentTab')
                            this.$router.push({name: 'works-actor'})
                        } else {
                            this.$toast({tips: res.data.message});
                        }
                })

            },
            checkedPerformTo (index, item) {
                this.artistDialogVisible = false;
                this.performTo.orcalArtistId = item.orcalArtistId;
                this.performTo.ipNameNo = item.ipNameNo;
                this.performTo.lastName = item.lastName;
                this.performTo.firstName = item.firstName;
                this.performTo.chineseName = item.chineseName;
                // this.searchTisN = '';
                this.clearSearchDialog()

            },
        },
        mounted () {
          this.id = this.$route.query.id;
            if(this.id){
                this.getInfo();
            }
        },
    }
</script>

<style>
    .right{
        float: right;
    }
    .el-checkbox{
        width: 185px;
    }
    .el-form-item__label{
        margin-left: 40px;
    }
</style>
