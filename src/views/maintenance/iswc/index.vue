<template>
  <div class="iswc">
    <el-form :inline="true" class="demo-form-inline" @keyup.enter.native="searchFn(1)">
      <el-form-item prop="">
        <el-input
          v-model="formInline.workId"
          placeholder="work num"
          style="width: 160px"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-input
          type="text"
          v-model="formInline.workSocietyCode"
          placeholder="work soc"
          style="width: 160px"
        ></el-input>
      </el-form-item>
      <!-- <el-form-item>
        <el-select
          v-model="formInline.sourceType"
          placeholder="type"
          style="width: 120px"
        >
          <el-option label="ADD" :value="'ADD'"></el-option>
          <el-option label="UPDATE" :value="'UPDATE'"></el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <el-select
          v-model="formInline.status"
          placeholder="status"
          style="width: 120px"
        >
          <el-option label="註冊中" :value="0"></el-option>
          <el-option label="註冊成功" :value="1"></el-option>
          <el-option label="註冊失敗" :value="2"></el-option>
          <el-option label="註冊更新" :value="3"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="createTime">
        <date-picker
          v-model="formInline.stateDate"
          type="date"
          value-format="yyyy-MM-dd"
          format="yyyyMMdd"
          placeholder="startDate"
          style="width: 160px"
        >
        </date-picker>
        -
        <date-picker
          v-model="formInline.endDate"
          type="date"
          value-format="yyyy-MM-dd"
          format="yyyyMMdd"
          placeholder="endDate"
          style="width: 160px"
        >
        </date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="searchFn(1)">查詢</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="uploadData()">導入</el-button>
      </el-form-item>
      <el-form-item>
        <span class="clear-search" @click="clearSearch()">清除搜索</span>
      </el-form-item>
    </el-form>
    <el-table
      :data="tableData"
      border
      stripe
      style="width: 100%"
      v-loading="loading"
      :empty-text="emptyText"
    >
      <el-table-column prop="id" label="id" width="80px"> </el-table-column>
      <el-table-column label="work num/soc">
        <template slot-scope="scope">
          {{ `${scope.row.workId}/${scope.row.workSocietyCode}` }}
        </template>
      </el-table-column>
      <!-- <el-table-column
        prop="workSocietyCode"
        label="work soc"
      >
      </el-table-column> -->
      <el-table-column prop="originalTitle" label="title"></el-table-column>
      <el-table-column
        prop="submissionId"
        label="submission_Id"
      ></el-table-column>
      <el-table-column prop="iswc" label="iswc"></el-table-column>
      <el-table-column label="status" width="100px">
        <template slot-scope="scope">
          {{
            scope.row.status == 0
              ? "註冊中"
              : scope.row.status == 1
              ? "註冊成功"
              : scope.row.status == 2
              ? "註冊失敗"
              : "註冊更新"
          }}
        </template>
      </el-table-column>
      <el-table-column prop="message" label="message"></el-table-column>
      <!-- <el-table-column label="Type">
        <template slot-scope="scope">
          {{ scope.row.sourceType }}
        </template>
      </el-table-column> -->
      <el-table-column label="source">
        <template slot-scope="scope">
          {{ scope.row.sourceType==0?'es':scope.row.sourceType==1?'上傳文檔':'其他平臺已註冊' }}
        </template>
      </el-table-column>
      <el-table-column label="sourcedb">
        <template slot-scope="scope">
          {{ scope.row.sourcedb }}
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      background
      layout="prev, pager, next"
      :total="total"
      @current-change="searchFn"
      :current-page="currentPage"
    >
    </el-pagination>
    <upload ref="upload" v-if="uploadingShow" @update='update'></upload>
  </div>
</template>
<script>
import upload from './components/uploading'
export default {
  name: "iswc",
  data() {
    return {
      formInline: {
        workId: "",
        workSocietyCode: "",
        sourceType: "",
        status: "",
        stateDate: "",
        endDate: "",
      },
      tableData: [],
      total: 0,
      currentPage: 1,
      loading: false,
      uploadingShow:false,
      emptyText: '數據加載中',
    };
  },
  components:{
      upload
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      this.getIswc();
    },
    update(data){
      let showtext=''
      data.wrkWorkIswcStatusList.forEach(item=>{
        showtext=`${showtext}${showtext?',':''}${item.workId}/${item.workSocietyCode}`
      })
      this.clearSearch()
      if(data.wrkWorkIswcStatusList.length){
        // this.$toast({ tips: `成功導入${data.count}條數據,${data.wrkWorkIswcStatusList.length}條數據被過濾,`});
        this.$msgbox.confirm(`成功導入${data.count}條數據,${data.wrkWorkIswcStatusList.length}條數據被過濾,數據workId/soc:${showtext}`, '導入成功', {
              confirmButtonText: '確定',
              type: 'warning',
              closeOnClickModal:false,
              showCancelButton:false
          }).then(() => {
            this.uploadingShow=false
          }).catch( () => {
          })
      }else{
        this.uploadingShow=false
        this.$toast({ tips: `成功導入${data.count}條數據`});
      }
    },
    uploadData(){
        this.uploadingShow=true
        // console.log(this.$refs.upload)
        this.$nextTick( () => {
            this.$refs.upload.showPop()
        })
    },
    getIswc(page) {
      // console.log(this.formInline)
      let ajaxData = this.formInline;
      let stateDate = this.formInline.stateDate
      let endDate = this.formInline.endDate
      let sta = stateDate.indexOf(' ')
      let end = endDate.indexOf(' ')
      if(sta==-1){
        ajaxData.stateDate=ajaxData.stateDate?stateDate+' 00:00:00':''
      }else{
        ajaxData.stateDate=ajaxData.stateDate?stateDate.substring(0,stateDate.indexOf(' '))+' 00:00:00':''
      }
      if(end==-1){
        ajaxData.endDate=ajaxData.endDate?endDate+' 23:59:59':''
      }else{
        ajaxData.endDate=ajaxData.endDate?endDate.substring(0,endDate.indexOf(' '))+' 23:59:59':''
      }
      ajaxData.page_num = page ? page : 1;
      ajaxData.page_size = 10;
      this.emptyText = '數據加載中';
      this.$http
        .get("/wrkWorkIswc/listWrkWorkIswcStatusWithPage", { params: ajaxData })
        .then((res) => {
          if (res.success) {
            // console.log(res.data.data);
            if(res.data.data){
              this.tableData = res.data.data.list;
              this.total = res.data.data.total;
              this.currentPage = page ? page : 1;
            }else{
              this.tableData=[]
              this.total=1
              this.currentPage=1
            }
            if(! this.tableData || this.tableData.length == 0){
              this.emptyText = '暫無數據';
            }
          }
        });
    },
    clearSearch() {
      this.formInline = {
        workId: "",
        workSocietyCode: "",
        sourceType: "",
        status: "",
        stateDate: "",
        endDate: "",
      },
      this.getIswc(1);
    },
    searchFn(page) {
      this.getIswc(page);
    },
  },
};
</script>
<style scoped>
</style>