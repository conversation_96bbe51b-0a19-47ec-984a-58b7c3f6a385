<template>
<div style="padding-top: 30px;">
    <el-collapse v-model="activeNames" class="clear" style="border: 0;">
        <el-collapse-item class="step-jump" title="File Work Info" name="1" :disabled="true">
            <div class="boxline p-t-10">
                <el-form :inline="true" label-position="left" label-width="90px">
                    <div>
                        <el-form-item label="WorkTitle">
                            <el-input v-model="fileInfo.workTitles" readonly></el-input>
                        </el-form-item>
                        <el-form-item label="Prod Year">
                            <el-input v-model="fileInfo.prodYear" readonly></el-input>
                        </el-form-item>
                        <el-form-item label="Production No">
                            <el-input v-model="fileInfo.productionNo" readonly></el-input>
                        </el-form-item>
                        <el-form-item label="Episode">
                            <el-input v-model="fileInfo.episodeNo" readonly></el-input>
                        </el-form-item>
                        <el-form-item label="Category">
                            <el-input v-model="fileInfo.category" readonly></el-input>
                        </el-form-item>
                    </div>
                </el-form>
            </div>
            <div class="components">
                <h4 style="margin: 4px 0;">Components</h4>
                    <div style="max-height: 270px;overflow-y: auto;overflow-x: hidden;border-top: 1px solid #eee;">
                        <template v-for="(item, index) in comList">
                            <div class="clear com-item" :key="index" v-if="item.status == 0">
                                <div style="float: left;width: 300px;">
                                    <div>
                                        WorkTitle：
                                    </div>
                                    <div class="el-sl">
                                        <span :title="item.workTitles">{{item.workTitles}}</span>
                                    </div>
                                </div>
                                <div style="float: right; width: 150px;margin-top: 16px;">
                                    <div >
                                        <el-button type="primary" size="mini" @click="openDetail(item)">详情</el-button>
                                        <el-button type="success" size="mini" @click="auditCom(item)">审核</el-button>
                                    </div>
                                    <!-- <el-form-item label="Composer">
                                        {{item.composerName}}
                                    </el-form-item>
                                    <el-form-item label="Author">
                                        {{item.authorNames}}
                                    </el-form-item>
                                    <el-form-item label="ISWC">
                                        {{item.iswc}}
                                    </el-form-item>
                                    <el-form-item label="Performer">
                                        {{item.artistNames}}
                                    </el-form-item>
                                    <el-form-item label="ISRC">
                                        {{item.isrc}}
                                    </el-form-item> -->
                                </div>
                            </div>
                        </template>
                    </div>
            </div>
        </el-collapse-item>
        <el-collapse-item class="step-jump" title="Match Work Info" name="2" :disabled="true">
            <div class="boxline">
                <el-table
                    :data="matchTable" 
                    v-loading='loading'
                    border
                    stripe
                    @row-click="changeWork"
                    @row-dblclick="workDrawer"
                    highlight-current-row
                    ref="matchTableRef"
                    class="match-table">
                    <el-table-column
                        prop="matchWorkId"
                        label="WorkNo"
                        width="120">
                        <template slot-scope="scope">
                            {{scope.row.matchWorkId}}
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="matchWorkSoc"
                        label="WorkSoc"
                        width="90">
                        <template slot-scope="scope">
                            {{scope.row.matchWorkSoc}}
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="matchWorkTitle"
                        label="WorkTitle"
                        min-width="130">
                        <template slot-scope="scope">
                            <span :title="scope.row.matchWorkTitle">{{scope.row.matchWorkTitle}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="matchSocre"
                        label="Score"
                        width="60">
                        <template slot-scope="scope">
                            {{scope.row.matchSocre}}
                        </template>
                    </el-table-column>
                </el-table>
                <el-form class="match-info" :inline="true" label-position="left" label-width="90px">
                    <div>
                        <el-form-item label="Prod Year">
                            <el-input v-model="matchInfo.publishDate" readonly></el-input>
                        </el-form-item>
                        <el-form-item label="Production No">
                            <el-input v-model="matchInfo.productionNo" readonly></el-input>
                        </el-form-item>
                        <el-form-item label="Episode">
                            <el-input v-model="matchInfo.episodeNo" readonly></el-input>
                        </el-form-item>
                        <el-form-item label="Category">
                            <el-input v-model="matchInfo.category" readonly></el-input>
                        </el-form-item>
                    </div>
                    <div>
                        <div class="f-l" style="width: 49%;padding-left: 20px;box-sizing: border-box;">
                            <div>Performer</div>
                            <el-form-item>
                                <ul class="list" style="width: 190px;max-height: 300px;overflow-y: auto;">
                                    <li class="el-sl" v-for="(item, index) in matchInfo.performerList" :key="index" :title="item">{{item}}</li>
                                    <li v-if="!matchInfo.performerList || matchInfo.performerList.length == 0">無數據</li>
                                </ul>
                            </el-form-item>
                        </div>
                        <div class="f-l" style="width: 51%;">
                            <div>ISRC</div>
                            <el-form-item>
                                <ul class="list" style="width: 190px;max-height: 300px;overflow-y: auto;">
                                    <li class="el-sl" v-for="(item, index) in matchInfo.isrcList" :key="index" :title="item">{{item}}</li>
                                    <li v-if="!matchInfo.isrcList || matchInfo.isrcList.length == 0">無數據</li>
                                </ul>
                            </el-form-item>
                        </div>
                    </div>
                </el-form>
            </div>
        </el-collapse-item>
    </el-collapse>
    <div class="options">
        <el-button type="primary" @click="submitFn('merge')">綁定</el-button>
        <el-button type="primary" @click="submitFn('add')">新增</el-button>
        <el-button type="primary" @click="selectWorkFn()">指定作品</el-button>
    </div>
    <el-dialog :visible.sync="show" width="1000px" title="查詢作品" :close-on-click-modal="false">
        <div style="width: 600px;margin: auto;margin-bottom: 20px">
            <el-input v-model="searchInfo.title" @keyup.enter.native="onSubmit()" placeholder="Title" style="width: 220px;"></el-input>
            <el-input v-model="searchInfo.workId" @keyup.enter.native="onSubmit()" placeholder="Work No" style="width: 128px;"></el-input>
            <el-input v-model="searchInfo.soc" @keyup.enter.native="onSubmit()" placeholder="Soc" style="width: 60px;"></el-input>
            <el-button type="primary" @click="onSubmit()">搜索</el-button>
            <span class="clear-search" @click="clearSearch()">清除搜索</span> 
        </div>
        <el-table :empty-text="tableresult"   :data="tableData">
            <el-table-column property="title" label="title">
                <template slot-scope="scope">
                    {{scope.row.title||scope.row.title_en}}
                </template>
            </el-table-column>
            <el-table-column property="work_id" label="workId" width="120"></el-table-column>
            <el-table-column property="work_society_code" label="workSoc" width="90"></el-table-column>
            <el-table-column property="genre_code" label="Genre" width="80"></el-table-column>
            <el-table-column property="genre_code" label="Composer" width="150">
                <template slot-scope="scope">
                    {{scope.row.composer && scope.row.composer.join('、')}}
                </template>
            </el-table-column>
            <el-table-column property="genre_code" label="Author" width="150"><template slot-scope="scope">
                    {{scope.row.author && scope.row.author.join('、')}}
                </template></el-table-column>
            <el-table-column
                label="operation"
                width="100">
                <template slot-scope="scope">
                    <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer" title="指定作品">
                        <span class="a-blue" @click="checkedWork(scope.$index,scope.row)">合併</span>
                    </span>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :total="total" @current-change="onSubmit" :current-page="currentPage">
        </el-pagination>
    </el-dialog>
    <el-drawer
        size="600px"
        :title="editInfo.workTitles"
        :visible.sync="drawer">
        <el-form :inline="true" label-position="left" label-width="90px">
            <el-form-item label="WorkTitle">
                <el-input v-model="editInfo.workTitles" readonly></el-input>
            </el-form-item>
            <el-form-item label="Composer">
                <el-input v-model="editInfo.composerName" readonly></el-input>
            </el-form-item>
            <el-form-item label="Author">
                <el-input v-model="editInfo.authorNames" readonly></el-input>
            </el-form-item>
            <el-form-item label="ISWC">
                <el-input v-model="editInfo.iswc" readonly></el-input>
            </el-form-item>
            <el-form-item label="Performer">
                <el-input v-model="editInfo.artistNames" readonly></el-input>
            </el-form-item>
            <el-form-item label="ISRC">
                <el-input v-model="editInfo.isrc" readonly></el-input>
            </el-form-item>
        </el-form>  
    </el-drawer>
    <work-list ref="workList" :workId='selectWorkNo.toString()' :workSocietyCode='selectWorkSoc.toString()'></work-list>
</div>
</template>
<script>
import workList from '../demo/workList.vue'
export default {
    data(){
        return {
            // file work info
            fileInfo: {},
            // 选中的work 的信息
            matchInfo: {},
            activeNames: ['1', '2'],
            matchTable: [],
            isrcList: [],
            performerList: [],
            selectWorkNo: '',
            selectWorkSoc:'',
            selectWork: {
                workId: '',
                soc: '',
                title: '',
                selectWorkShow: false,
                list: []
            },

            show: false,
            loading: false,
            searchInfo: {
                workId: '',
                title: '',
                soc: ''
            },
            tableData: [],
            tableresult:' ',
            total: 0,
            currentPage:1,
            loading: false,
            comList: [],
            drawer: false,
            editInfo: {}
        }
    },
    components:{ workList },
    created(){
        this.dataType = this.$route.query.type;
        this.queryInfo();
    },
    activated(){
        this.$nextTick( () => {
            if(this.$route.query.update){
                let query = this.$route.query;
                delete query.update;
                this.dataType = this.$route.query.type;
                this.queryInfo();
            }
        })
    },
    methods: {
        queryInfo(){
            this.$http.get('/work/check/getMatchsWorkDetail', {params: {id: this.$route.query.id}}).then( res => {
                if(res.success){
                    this.fileInfo = res.data;
                    this.queryMatchWorks(res.data.dataId);
                    this.comList = res.data.subMatchWorkReDuplicateList || [];
                }
            })
        },
        queryMatchWorks(dataId){
            this.loading=true
            this.$http.get('/work/check/getMatchsWorkList', {params: {dataId : dataId}}).then( res => {
                if(res.success){
                    this.matchTable = res.data;
                    this.loading=false
                    if(this.matchTable.length > 0){
                        //默认选中第一行
                        this.$refs.matchTableRef.setCurrentRow(this.matchTable[0]);
                        this.changeWork(this.matchTable[0]);
                    }

                }
            })
        },
        workDrawer(row){
            console.log(row)
            this.selectWorkNo = row.matchWorkId;
            this.selectWorkSoc = row.matchWorkSoc;
            this.$nextTick(()=>{
                this.$refs.workList.init()
            })
        },
        changeWork(row, column, event){
            console.log('row: ', row);
            this.matchInfo = {};
            this.selectWorkNo = row.matchWorkId;
            this.selectWorkSoc = row.matchWorkSoc;
            this.matchInfo = row;
            if(this.matchInfo.type != 'm'){ //如果不是用户指定作品查找的，就转化數據
                this.matchInfo.performerList = this.matchInfo.matchWorkArtist ? this.matchInfo.matchWorkArtist.split(';') : [];
                this.matchInfo.isrcList = this.matchInfo.matchIsrc ? this.matchInfo.matchIsrc.split(';') : [];
            }
        },
        selectWorkFn(){
            this.searchInfo = {
                workId: '',
                title: '',
                soc: '',
                workType: "AV"
            };
            this.tableData = [];
            this.total = 0;
            this.show = true;
            this.$nextTick( () => {
                this.onSubmit();
            })
        },
        clearSearch(){
            this.searchInfo = {
                workId: '',
                title: '',
                soc: '',
                workType: "AV"
            };
            this.$nextTick( () => {
                this.onSubmit();
            })
        },
        onSubmit(page){
            let params = this.searchInfo;
            params.page = {
                pageNum: page ? page : 1,
                pageSize: 10
            }
            this.loading = true;
            this.tableresult = '數據加載中...'
            this.$http.post('/wrk/queryWrkWorkListEs', params).then(res => {
                this.loading = false;
                if (res.success) {
                    this.tableData = res.data.list;
                    this.total = res.data.total;
                      this.tableresult = this.tableData.length == 0 ? '暫無數據' : ' '
                    this.currentPage = page ? page : 1
                }
            })
        },
        checkedWork(index, row){

             this.$msgbox.confirm('確定指定此作品?', '提示', {
                confirmButtonText: '確定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.selectWorkNo = row.work_id;
                this.selectWorkSoc = row.work_society_code;
                this.submitFn('merge');
            }).catch(() => {
            });
        },
        queryWorkInfo(){
            this.selectWork.list = [];
            // 查詢此work no 的ipshare
            let params = {
                workId: this.selectWork.workId,
                workSocietyCode: this.selectWork.soc
            }
            this.$http.get('/wrk/getWrkWorkById', {params}).then(res => {
                if (res.success) {
                    this.selectWork.list.push(res.data.wrkWork);
                    this.selectWork.workInfo = res.data.wrkWork
                }
            })
        },
        submitFn(opType){


            /**
             * 判斷是否還有未審核的子作品
             *
             */
            let flag = false;
            this.comList.forEach( item =>{
                if(item.status == 0){
                    flag = true;
                }
            })
            if(flag){
                this.$toast({tips: '所有component數據都審核完後，才能審核'})
                return;
            }


            let url = '';

            let ajaxData = {}
            if(opType == 'add'){

                if(this.dataType == 'AVR'){
                    url = '/work/check/avrWorkNew';
                }else{
                    url = '/work/check/cwrWorkNew';
                }
                ajaxData = {
                    matchWorkReDuplicateId: this.$route.query.id,
                    dataId: this.fileInfo.dataId
                }
            }else{
                if(this.dataType == 'AVR'){
                    url = '/work/check/avrWorkUpdate';
                }else{
                    url = '/work/check/cwrWorkUpdate';
                }
                ajaxData = {
                    matchWorkReDuplicateId: this.$route.query.id,
                    dataId: this.fileInfo.dataId,
                    workId: this.selectWorkNo,
                    workSoc: this.selectWorkSoc
                }
            }
            this.loading = this.$loading();
            this.$http.post(url,  ajaxData).then( res => {
                this.loading.close();
                if(res.success){
                    if( (res.data.code && res.data.code == 200) || res.data == 1){
                        console.log('res.data')
                        console.log(res.data)
                        this.$toast({tips: '操作成功'});
                        setTimeout( () => {
                            this.$bus.$emit('closeCurrentTab', () => {
                                this.$router.push({name: 'removalAuditlist', query: {update: true}})
                            });
                        }, 500)
                    }else{
                        this.$toast({tips: res.data.message})
                    }
                }else{
                    this.$toast({tips: res.data.message})
                }
            })
        },
        auditCom(item){
            this.$router.push({name: 'removalAudit', query: {id: item.id, type: item.dataType,
                title: item.workTitles, nameId: item.id, fromUrl: JSON.stringify(
                    {
                        name: this.$route.name,
                        query: this.$route.query
                    }
                )}})
        },
        openDetail(item){
            this.editInfo = item;
            this.drawer = true;
        }
    }

}
</script>
<style lang="scss" scoped>
    @import '../../assets/scss/works.scss';
    .el-collapse-item{
        width: 498px;
        margin-right: 40px;
        float: left;
    }
    .match-table{
        width: 100%;
        max-height: 220px;
    }
    /deep/ .match-table thead .el-checkbox{
        display: none;
    }
    /deep/ .match-info{
        margin-top: 10px;
    }
    /deep/ .match-info .el-table tr{
        background: #fff;
    }
    /deep/ .el-collapse-item__arrow{
        display: none;
    }
    /deep/ .component .el-form-item__content{
        width: 100%;
    }
    /deep/ .el-input.is-disabled .el-input__inner{
        background: #fff;
        color: #333;
    }
    /deep/ .el-table__body tr.current-row>td{
        background-color: #17B3A3;
    }
    /deep/ .el-form-item.f12 label{
        font-size: 13px;
    }

    ul.list{
        background: #fff;
        padding: 0;
        border: 1px solid #ddd;
        margin-top: 10px;
        border-radius: 4px;
        li{
            list-style: none;
            border-bottom: 1px solid #ddd;
            line-height: 26px;
            padding: 0 4px;
        }
        &:last-child{
            border-bottom: 0;
        }
    }
    /deep/ .el-input{
        width: 350px;
    }
    .components{
        padding: 0 10px;
    }
    .com-item{
        border-bottom: 1px solid #eee;
        padding: 10px 0;
    }
    .options{
        position: fixed;
        top: 86px;
        width: 1030px;
        padding: 10px;
        border-bottom: 1px solid #eee;
        text-align: right;
        background: #fff;
    }


</style>
