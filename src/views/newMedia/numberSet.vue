<template>
    <div>
        <el-form :inline="true">
            <el-form-item>
                <el-button type="primary" @click="addFn()" v-if="isAuth('newMedia:numberSet:add')">添加</el-button>
            </el-form-item>
        </el-form>
        <el-table
            :data="tableData"
            border
            stripe
            style="width: 100%"
            v-loading="loading"
            :empty-text="emptyText">
            <el-table-column
                prop="distNo"
                label="Dist No">
            </el-table-column>
            <el-table-column
                prop="description"
                label="Description">
            </el-table-column>
            <el-table-column
                prop="remark"
                label="Remark">
            </el-table-column>
            <el-table-column
                prop="createTime"
                label="Create Time">
            </el-table-column>
            <el-table-column
                label="operation">
                <template slot-scope="scope">
                    <el-button type="text" size="small" @click="editFn(scope.row)" v-if="isAuth('newMedia:numberSet:change')">編輯</el-button>
                    <!-- <el-button type="text" size="small" @click="deleteFn(scope.row)">删除</el-button> -->
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :total="total" @current-change="searchFn" :current-page="currentPage">
        </el-pagination>
        <!-- 編輯、添加 -->
        <el-dialog :title="(opType == 'add' ? '添加' : '編輯') + '分配編號配置'" :visible.sync="show" v-if="show" :close-on-click-modal="false">
            <el-form ref="editForm" :inline="true" :model="edit" label-width="120px" @keyup.enter.native="confirmEdit">
                <el-form-item label="Dist No" prop="distNo" required>
                    <!-- <el-input type="text" v-model="edit.distNo" @change="distNoChange"></el-input> -->
                    <el-input type="text" placeholder="雙擊查詢" v-model="edit.distNo" @dblclick.native="selectChange" readonly=""></el-input>
                </el-form-item>
                <el-form-item label="Description" prop="description">
                    <el-input type="text" v-model="edit.description"></el-input>
                </el-form-item>
                <el-form-item label="Remark" prop="remark">
                    <el-input type="text" v-model="edit.remark"></el-input>
                </el-form-item>
                <div class="t-c">
                    <el-form-item>
                        <el-button type="primary" @click="confirmEdit">確定</el-button>
                    </el-form-item>
                </div>
            </el-form>
        </el-dialog>
        <!-- 創建分配編號 -->
        <el-dialog title="創建分配編號" :visible.sync="createShow" width="400px" :close-on-click-modal="false">
            <el-form :inline="true">
                <el-form-item label="分配編號">
                    <el-input type="text" v-model="addDist.distNo"></el-input>
                </el-form-item>
                <el-form-item label="分配描述">
                    <el-input type="text" v-model="addDist.distDescribe"></el-input>
                </el-form-item>
                <el-form-item class="t-c" style="width: 100%;">
                    <el-button type="primary" @click="saveNo">確認</el-button>
                    <el-button @click="cancel">取消</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
        <select-dist ref='selectDist' :search='{distNo:edit.distNo}' @checkDist='checkDist'></select-dist>
    </div>
</template>

<script>
    import SelectDist from './components/selectDist.vue'
    export default {
        name: 'list',
        data () {
            return {
                tableData: [],
                total: 1,
                currentPage: 1,

                // dialog
                show: false,
                opType: 'add',
                edit: {
                    //固定為M
                    distType: 'M',
                    distNo: '',
                    description: '',
                    remark: ''
                },

                createShow: false,
                addDist: {
                    distNo: '',
                    distDescribe: '',
                    remark: ''
                },
                loading: false,
                distShow:false,
                emptyText: '數據加載中',
            }
        },
        mounted () {
            this.searchFn();
        },
        components: {
            SelectDist
        },
        methods: {
            selectChange(){
                console.log('111111111111')
                this.$refs.selectDist.init()
            },
            checkDist(data){
                console.log(data)
                this.edit.distNo=data.distNo
                this.edit.description=data.description
            },
            // 編輯或添加的distNo，修改後，查詢是否存在
            distNoChange(){
                if(this.edit.distNo){
                    this.$http.get('/dist/param/info/distNo?distNo=' + this.edit.distNo).then( res => {
                        console.log(';', res);
                        if(res.success){
                            if(res.data.code && res.data.code == '20006'){
                                // distNo 不存在 则提示是否創建
                                this.$msgbox.confirm(`此分配編號不存在，是否創建新的分配編號`, '提示', {
                                    confirmButtonText: '確定',
                                    cancelButtonText: '取消',
                                    type: 'warning'
                                }).then(() => {
                                    this.createNo();
                                }).catch( () => {
                                    this.edit.distNo = '';
                                })
                            }
                        }
                    })
                }
            },
            createNo(){
                this.addDist.distNo = '';
                this.addDist.distDescribe = '';
                this.createShow = true;
            },
            saveNo(){
                this.$http.post('/dist/param/number?distNo=' + this.addDist.distNo + '&distDescribe=' + this.addDist.distDescribe).then( res => {
                    if(res.success){
                        if(res.data.code){
                            this.$toast({tips: res.data.message})
                        }else{
                            this.$toast({tips: '創建成功'});
                            this.createShow = false;
                            this.edit.distNo = this.addDist.distNo;
                        }
                        
                    }
                })
            },
            cancel(){
                this.addDist.distNo = '';
                this.addDist.distDescribe = '';
                this.createShow = false;
            },
            searchFn(page) {
                let ajaxData ={};
                ajaxData.page_num = page ? page : 1;
                ajaxData.page_size = 10;
                this.loading = true;
                this.emptyText = '數據加載中';
                this.$http.get('/dist/media/listDistMediaDistNumberWithPage', {params: ajaxData}).then( res => {
                    this.loading = false;
                    if(res.success){
                        this.tableData = res.data.data.list;
                        this.total = res.data.data.total;
                        this.currentPage = ajaxData.page_num;
                        if(! this.tableData || this.tableData.length == 0){
                            this.emptyText = '暫無數據';
                        }
                    }
                })
            },
            addFn(){
                this.opType = 'add';
                this.edit= {
                    distType: 'M',
                    distNo: '',
                    description: '',
                    remark: ''
                }
                this.show = true;
                // this.$refs['editForm'].resetFields();
                // this.$refs['edit'].resetFields();
            },
            confirmAdd(){
                
            },
            editFn (row) {
                this.opType = 'edit';
                this.edit = this.$utils.copy(row);
                console.log(this.edit)
                this.show = true;
            },
            confirmEdit(){
                if(!this.edit.distNo){
                    this.$toast({tips: 'Dist No不能為空'});
                    return
                }
                let ajaxData = this.edit;
                this.$http.post('/dist/media/saveDistMediaDistNumber', ajaxData).then( res => {
                    if(res.success){
                        if(res.data.code && res.data.code != 200){
                            this.$toast({tips: res.data.message})
                        }else{
                            this.$toast({tips: (this.opType == 'add'? '添加' : '編輯') +'成功'});
                            this.show = false;
                            this.searchFn(this.opType == 'add '? 1 : this.currentPage);
                        }
                        
                    }
                })
            },
            deleteFn () {
                this.$msgbox.confirm(`確定進行[删除]操作?`, '提示', {
                    confirmButtonText: '確定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {

                }).catch( () => {
                    
                })
            }
        }
        
    }
</script>
<style lang="scss" scoped>
/deep/ .el-dialog{
    width: 500px;
}
</style>

