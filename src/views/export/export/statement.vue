<template>
  <!-- 拆分後的文件 用于審核用的 -->
  <div class="ipibpox">
    <el-form :inline="true" :model="searchinfo" ref="form" :rules="rules" class="demo-form-inline">
      <!-- <el-form-item prop="fileBaseId">
        <span class="red">*</span>
        <el-input v-model="search.fileBaseId" placeholder="FID" style="width: 80px;"></el-input>
      </el-form-item> -->

      <el-form-item prop="distNo">
        <el-input v-model="searchinfo.distNo" placeholder="分配编号" style="width: 120px;"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="exportFn">導出</el-button>
      </el-form-item>

      <el-form-item>
        <span class="clear-search" @click="clearSearch()">清除搜索</span>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import qs from 'qs'
// import axios from '../../utils/httpRequest';    
export default {
  name: 'exportClaexit',
  data() {
    return {
      tableData: [],
      total: 0,
      currentPage: 1,
      rules: {
        distNo: [
          { required: true, message: '請輸入distNo', trigger: 'blur' }
        ],
        batchIdA: [{ pattern: /^\+?[1-9]\d*$/, message: '必須>0' }],
        batchIdB: [{ pattern: /^\+?[1-9]\d*$/, message: '必須>0' }]
      },
      searchinfo: {
        distNo: '',
        number: ''  
      },
      // search: {
      //   status: '',
      //   title: '',
      //   matchScoreLevel: '',
      //   batchIdA: '',
      //   batchIdB: '',
      //   fileBaseId: '',
      //   quantileBegin: '',
      //   quantileEnd: '',
      //   titleNonNull: false
      // },
      config: {
        status: [
          {
            value: '',
            label: '全部狀態'
          },
          {
            value: 0,
            label: '待審核'
          },
          {
            value: 1,
            label: '已匹配'
          },
          {
            value: 2,
            label: '不匹配'
          },

        ],
        score: [
          {
            value: '',
            label: '全部評分'
          },
          {
            value: -1,
            label: '10分以下'
          },
          {
            value: 0,
            label: '10-20分'
          },
          {
            value: 1,
            label: '20分以上'
          },
        ],
        uploadType: ['PG', 'FW', 'MS', 'CJ']
      },
      selectList: []
    }
  },
  props: ['search', 'exporttype'],

  mounted() {
    this.searchinfo.number = this.$utils.copy(this.search);
    // this.exporttype = this.$utils.copy(this.exporttype);
  },
  methods: {
    clearSearch() {
      this.searchinfo = {
        distNo: ''
      }
    },
    searchFn(page = 1) {
      this.$refs.form.validate(validate => {
        if (validate) {
          if (page == 1) {
            this.total = 0
          }
          let ajaxData = {};
          ajaxData = this.$utils.copy(this.searchinfo);
          ajaxData.page = {
            pageNum: page ? page : 1,
            pageSize: 10
          }
          this.$http.post('/claimmatch/getListMatchDataDspList', ajaxData).then(res => {
            if (res.success) {
              this.tableData = res.data.data.list;
              this.total = res.data.data.total;
              this.currentPage = page ? page : 1;
            }
          })
        }
      })

    },
    audit(item, params) {
      this.$router.push({ name: 'claimListAudit', query: { id: item.id, type: item.matchType, useparams: params, usetabledata: this.tableData } })
    },
    changeSearchScore(score) {
      this.searchinfo.matchScoreLevel = score;
    },
    checkSelectable(row) {
      return !row.status
    },
    handleSelectionChange(list) {
      let array = [];
      list.forEach((item, index) => {
        array.push(item.dataUniqueKey)
      })
      this.selectList = array;
    },
    batchAudit(type) {
      if (!this.selectList.length) {
        this.$toast({ tips: '請至少選擇一項' })
        return;
      }
      this.$http.post('/claimmatch/batchUpdateListMatchDataDspStatusByParams', {
        uniqueKeyMd5List: this.selectList,
        status: type
      }).then(res => {
        if (res.success) {
          if (res.data.code == 200) {
            this.$toast({ tips: '批量審核' + (type == 1 ? '通過' : '拒絕') + '成功' });
            this.searchFn();
          } else {
            this.$toast({ tips: res.data.message })
          }

        }
      })

    },
    uploadChange(file) {
      this.$utils.uploadFile({flie:file.raw}, '/claimmatch/importAutoGenerateCustomId', this)
    },
    autoprod() {
      this.$refs.form.validate(validate => {
        if (validate) {
          this.$msgbox.confirm(`確定生成custom_id操作?`, '提示', {
            confirmButtonText: '確定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            let ajaxData = {};
            ajaxData = this.$utils.copy(this.searchinfo);
            ajaxData.page = {
              pageNum: this.currentPage,
              pageSize: 10
            }
            // if (ajaxData.matchScore == 99) {
            //   delete ajaxData.matchScore;
            // }

            this.$http.post('/claimmatch/autoGenerateCustomId', ajaxData).then(res => {
              console.log('resresrsa', res)
              let Message = res.data.message || res.data.data || '操作失敗!'
              this.$toast({ tips: Message })
              // if (res.data.code=='400') {
              //    this.$toast({tips:res.data.message})
              // }else{
              //   this.$toast({tips:'操作失敗!'})
              // }
            })
          })
        }
      })
    },
    exportFn() {
      this.$refs.form.validate(validate => {
        // console.warn('valisdate',validate)
        if (validate) {
          this.$msgbox.confirm(`是否確定導出?`, '提示', {
            confirmButtonText: '確定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            let ajaxData = {};
            ajaxData = this.$utils.copy(this.searchinfo);
            if (ajaxData.matchScore == 99) {
              delete ajaxData.matchScore;
            }
            //   listOverseas/printStatement   listOverseas/printDetails
            console.warn('url', this.$utils.copy(this.exporttype))
            let url = this.$utils.copy(this.exporttype) == "statement" ? '/listOverseas/printStatement?distNo=' : '/listOverseas/printDetails?distNo='
            this.$http.get(url + ajaxData.distNo + '&number=' + ajaxData.number, { responseType: 'blob' }).then(res => {
              // console.log('=========', res)
              let tempBlob = new Blob([res.data], { type: 'application/json' })
              let reader = new FileReader()
              reader.onload = e => {
                let res1 = e.target.result
                // 此处对fileReader读出的结果进行JSON解析
                // 可能会出现错误，需要进行捕获
                try {
                  let json = JSON.parse(res1)
                  this.$toast({ tips: json.message })
                  //正常json数据格式
                } catch (err) {
                  // 该异常为无法将字符串转为json
                  // 说明返回的数据是一个流文件
                  // 不需要处理该异常，只需要捕获即刻

                  this.$utils.downloadByBlob(res.data, res.headers["content-disposition"])
                }
              }
              // 将blob对象以文本的方式读出，读出完成后将会执行 onload 方法
              reader.readAsText(tempBlob)







            })
          })
        }
      })
    }
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      // console.log('*****out',vm)
      // vm.formData.distNo = to.params.distNo
      vm.searchFn()
    })
  }
}
</script>

<style  scoped>
.el-form-item {
  margin-left: 0;
  margin-right: 0;
}
.el-button {
  margin-left: 0;
  margin-right: 0;
}
</style>
