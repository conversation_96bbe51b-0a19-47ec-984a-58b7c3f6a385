<template>  
    <div>
        <export-avr v-if="type=='avr'"></export-avr>
        <export-cwr v-else-if="type=='cwr'"></export-cwr>
        <export-fiche v-else-if="type=='fiche'"></export-fiche>
        <export-claexit v-else-if="type=='claexit'"></export-claexit>
        <export-statement :search="id" :exporttype='isstatement' v-else-if="type=='statement'||type=='details'"></export-statement>
        <other v-else></other>

    </div>
</template>
<script>
import exportAvr from './avr';
import exportCwr from './cwr';
import exportFiche from './fiche';
import exportClaexit from './claexit';
import exportStatement from './statement';
import other from './other';
export default {
    data(){
        return{
            type: '',
            id:'',
            isstatement:''
        }
    },
    created(){
        console.log('********',this.$route.query)
        this.type = this.$route.query.type;
        this.id = this.$route.query.id;
        this.isstatement = this.$route.query.nameId;
        
    },
    components:{
        exportAvr,
        exportCwr,
        exportClaexit,
        exportStatement,
        exportFiche,
        other
    }
    
}
</script>
<style lang="scss" scoped>

</style>
