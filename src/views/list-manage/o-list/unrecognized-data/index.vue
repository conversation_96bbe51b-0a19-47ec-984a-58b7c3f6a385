<template>
  <div class="mod-config">
    <el-form :inline="true" :model="search" ref="searchForm" class="demo-form-inline">
      <el-form-item label="文件名">
        <el-input v-model="search.fileName" placeholder="文件名" clearable></el-input>
      </el-form-item>
      <el-form-item label="上传时间">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          @change="handleDateChange">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="searchFn(1)">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleUpload">上传文件</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="tableData"
      border
      v-loading="loading"
      style="width: 100%">
      <el-table-column
        prop="id"
        header-align="center"
        align="center"
        width="80"
        label="ID">
      </el-table-column>
      <el-table-column
        prop="fileName"
        header-align="center"
        align="center"
        label="文件名">
      </el-table-column>
      <el-table-column
        prop="uploadTime"
        header-align="center"
        align="center"
        width="180"
        label="上传时间">
      </el-table-column>
      <el-table-column
        prop="status"
        header-align="center"
        align="center"
        width="100"
        label="状态">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === '已处理' ? 'success' : 'warning'">{{ scope.row.status }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="remark"
        header-align="center"
        align="center"
        label="备注">
      </el-table-column>
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作">
        <template slot-scope="scope">
          <el-button @click="handleView(scope.row)" type="text" size="small">查看</el-button>
          <el-button @click="handleProcess(scope.row)" type="text" size="small">处理</el-button>
          <el-button @click="handleDelete(scope.row)" type="text" size="small">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="page.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="page.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total">
    </el-pagination>

    <!-- 上传文件对话框 -->
    <el-dialog title="上传文件" :visible.sync="uploadDialogVisible" width="500px">
      <el-upload
        class="upload-demo"
        drag
        action="/api/file/upload"
        :on-success="handleUploadSuccess"
        :on-error="handleUploadError"
        :before-upload="beforeUpload"
        multiple>
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip" slot="tip">只能上传excel/csv文件，且不超过10MB</div>
      </el-upload>
      <span slot="footer" class="dialog-footer">
        <el-button @click="uploadDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="uploadDialogVisible = false">确定</el-button>
      </span>
    </el-dialog>

    <!-- 查看文件详情对话框 -->
    <el-dialog title="文件详情" :visible.sync="viewDialogVisible" width="80%">
      <el-table
        :data="fileDetailData"
        border
        height="500"
        style="width: 100%">
        <el-table-column
          v-for="(col, index) in fileColumns"
          :key="index"
          :prop="col.prop"
          :label="col.label"
          :width="col.width">
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="viewDialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <!-- 处理文件对话框 -->
    <el-dialog title="处理无法识别数据" :visible.sync="processDialogVisible" width="80%">
      <el-form :model="processForm" label-width="100px">
        <el-form-item label="处理方式">
          <el-radio-group v-model="processForm.method">
            <el-radio :label="1">手动匹配</el-radio>
            <el-radio :label="2">忽略</el-radio>
            <el-radio :label="3">标记为异常</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注">
          <el-input type="textarea" v-model="processForm.remark" rows="3"></el-input>
        </el-form-item>
      </el-form>
      <el-table
        :data="processData"
        border
        height="400"
        style="width: 100%">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column
          v-for="(col, index) in processColumns"
          :key="index"
          :prop="col.prop"
          :label="col.label"
          :width="col.width">
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template slot-scope="scope">
            <el-button @click="handleMatch(scope.row)" type="text" size="small">匹配</el-button>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="processDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitProcess">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      search: {
        fileName: '',
        startDate: '',
        endDate: ''
      },
      dateRange: [],
      tableData: [],
      loading: false,
      total: 0,
      page: {
        pageSize: 10,
        pageNum: 1
      },
      uploadDialogVisible: false,
      viewDialogVisible: false,
      processDialogVisible: false,
      fileDetailData: [],
      fileColumns: [
        { prop: 'column1', label: '列1', width: '' },
        { prop: 'column2', label: '列2', width: '' },
        { prop: 'column3', label: '列3', width: '' },
        { prop: 'column4', label: '列4', width: '' },
        { prop: 'column5', label: '列5', width: '' }
      ],
      processData: [],
      processColumns: [
        { prop: 'column1', label: '列1', width: '' },
        { prop: 'column2', label: '列2', width: '' },
        { prop: 'column3', label: '列3', width: '' },
        { prop: 'column4', label: '列4', width: '' },
        { prop: 'column5', label: '列5', width: '' }
      ],
      processForm: {
        method: 1,
        remark: ''
      },
      currentRow: null
    }
  },
  created() {
    this.getDataList()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.loading = true
      // 模拟API调用
      setTimeout(() => {
        this.tableData = [
          {
            id: 1,
            fileName: '无法识别数据文件1.xlsx',
            uploadTime: '2023-05-15 10:30:45',
            status: '未处理',
            remark: '系统无法自动识别的数据'
          },
          {
            id: 2,
            fileName: '无法识别数据文件2.xlsx',
            uploadTime: '2023-05-16 14:22:33',
            status: '已处理',
            remark: '已手动匹配完成'
          },
          {
            id: 3,
            fileName: '无法识别数据文件3.csv',
            uploadTime: '2023-05-17 09:15:20',
            status: '未处理',
            remark: '待处理'
          }
        ]
        this.total = this.tableData.length
        this.loading = false
      }, 500)
    },
    // 搜索
    searchFn(pageNum) {
      this.page.pageNum = pageNum || 1
      this.getDataList()
    },
    // 日期范围变化
    handleDateChange(val) {
      if (val) {
        this.search.startDate = val[0]
        this.search.endDate = val[1]
      } else {
        this.search.startDate = ''
        this.search.endDate = ''
      }
    },
    // 分页大小变化
    handleSizeChange(val) {
      this.page.pageSize = val
      this.getDataList()
    },
    // 当前页变化
    handleCurrentChange(val) {
      this.page.pageNum = val
      this.getDataList()
    },
    // 上传文件
    handleUpload() {
      this.uploadDialogVisible = true
    },
    // 上传前验证
    beforeUpload(file) {
      const isExcelOrCsv = file.type === 'application/vnd.ms-excel' || 
                          file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                          file.type === 'text/csv'
      const isLt10M = file.size / 1024 / 1024 < 10

      if (!isExcelOrCsv) {
        this.$message.error('上传文件只能是 Excel/CSV 格式!')
      }
      if (!isLt10M) {
        this.$message.error('上传文件大小不能超过 10MB!')
      }
      return isExcelOrCsv && isLt10M
    },
    // 上传成功
    handleUploadSuccess(response, file, fileList) {
      this.$message.success('上传成功')
      this.getDataList()
    },
    // 上传失败
    handleUploadError(err, file, fileList) {
      this.$message.error('上传失败，请重试')
    },
    // 查看文件
    handleView(row) {
      this.currentRow = row
      this.viewDialogVisible = true
      // 模拟获取文件详情数据
      this.fileDetailData = Array(20).fill().map((_, index) => {
        return {
          column1: `数据${index + 1}`,
          column2: `值${index + 1}`,
          column3: `类型${index % 3 + 1}`,
          column4: `来源${index % 5 + 1}`,
          column5: `状态${index % 2 ? '正常' : '异常'}`
        }
      })
    },
    // 处理文件
    handleProcess(row) {
      this.currentRow = row
      this.processDialogVisible = true
      // 模拟获取需要处理的数据
      this.processData = Array(15).fill().map((_, index) => {
        return {
          column1: `未识别数据${index + 1}`,
          column2: `值${index + 1}`,
          column3: `类型${index % 3 + 1}`,
          column4: `来源${index % 5 + 1}`,
          column5: `状态：无法识别`
        }
      })
    },
    // 删除文件
    handleDelete(row) {
      this.$confirm('确认删除该文件吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 模拟删除操作
        this.$message({
          type: 'success',
          message: '删除成功!'
        })
        this.getDataList()
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })          
      })
    },
    // 匹配数据
    handleMatch(row) {
      this.$message.info('打开匹配界面，进行手动匹配')
    },
    // 提交处理
    submitProcess() {
      this.$message.success('处理成功')
      this.processDialogVisible = false
      this.getDataList()
    }
  }
}
</script>

<style scoped>
.mod-config {
  padding: 20px;
}
</style>
