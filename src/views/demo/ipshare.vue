<template>
<!-- ipshare -->
    <div>
        <el-form :inline="true" :model="formShare" class="demo-form-inline">
            <el-form-item label="Type">
                <el-select v-model="formShare.rightType" placeholder="Type" style="width: 246px;">
                    <el-option label="PERFORMING RIGHT" value="PER"></el-option>
                    <el-option label="MECHANICAL RIGHT" value="MEC"></el-option>
                    <el-option label="SYNCHRONIZATION RIGHT" value="ZYN"></el-option>
                    <el-option label="OD RIGHT" value="NOD"></el-option>
                    <el-option label="DB RIGHT" value="NDB"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <!-- <el-checkbox v-model="dischecked" :disabled="!disCanCheck">Distributable</el-checkbox> -->
                <!-- <el-checkbox v-model="rightMap[formShare.rightType].distributable" :disabled="!disCanCheck">Distributable</el-checkbox> -->
                <el-checkbox @change='distributableChange' :checked='rightMap[formShare.rightType].distributable' v-model="rightMap[formShare.rightType].distributable" >Distributable</el-checkbox>
            </el-form-item>
            <el-form-item label="Share Type">
                <el-select @change='shareTypeChange' v-model="rightMap[formShare.rightType].shareType" placeholder="Share Type" style="width:120px">
                    <el-option label="Manual" value="M"></el-option>
                    <el-option label="Auto" value="A"></el-option>
                </el-select>
            </el-form-item>
            <!-- <el-form-item label="Sort by">
             <el-select v-model="formShare.region" placeholder="Share Type">
             <el-option label="Group" value="md"></el-option>
             <el-option label="english" value="en"></el-option>
             <el-option label="both" value="both"></el-option>
             </el-select>
             </el-form-item>-->
            <el-form-item v-if="updateRsd">
                <el-checkbox v-model="rightMap[formShare.rightType].workSd" @change="changeRsd">Right SD</el-checkbox>
            </el-form-item>
            <el-form-item >
                <el-button v-if="isADP && update1" size="mini" style="margin-left:30px" @click="getRefShare()"> F </el-button>
            </el-form-item>
            <div class="right">
                <el-button type="primary" @click="updateData()" v-if="workId&&isAuth('works:base:change')">{{update1?'取消更新':'更 新'}}</el-button>
                <el-button type="primary" @click="copyRight()" v-if="workId&&isAuth('works:base:change') ? true: false">Copy IP</el-button>
                <el-button type="primary" v-if="isAuth('works:base:change')" @click="adddata2()">新 增</el-button>
            </div>
        </el-form>
        <div class="boxline" v-if="update"  style="max-height: 800px; overflow: auto;">
            <el-table
                :data="update1?tableData[formShare.rightType]:rightData[formShare.rightType]"
                border
                stripe
                show-summary
                :summary-method="getSummaries"
                style="max-height: 200px;"
                >
                <el-table-column width="34">
                    <template slot-scope="scope">
                        <div v-if="scope.row.agrNo">
                            <i class="el-icon-star-on" style="color: red;" ></i>
                        </div>
                        <div v-if="scope.row.refIndicator == 'Y' &&  update1">
                            <i class="el-icon-star-off" style="color: red;" ></i>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="groupIndicator"
                    label="Gp"
                    width="70px">
                    <template slot-scope="scope">
                        <el-input v-model='scope.row.groupIndicator' placeholder="Gp"></el-input>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="name"
                    label="Name"
                    min-width="100px">
                    <template slot-scope="scope">
                        <el-input v-model='scope.row.name' placeholder="雙擊查詢" @dblclick.native="getIp(scope.$index, scope.row)" readonly></el-input>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="chineseName"
                    label="Chinese Name">
                    <template slot-scope="scope">
                        <el-input v-model='scope.row.chineseName' placeholder="" readonly></el-input>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="ipNameNo"
                    label="Ip Name No"
                    width="150px">
                    <template slot-scope="scope">
                        <el-input v-model='scope.row.ipNameNo' placeholder="" readonly></el-input>
                        <!-- <el-input v-model=scope.row.ipNameNo placeholder="雙擊查詢" @dblclick.native="getIp(scope.$index, scope.row)" readonly></el-input> -->
                    </template>
                </el-table-column>
                <el-table-column
                    prop="workIpRole"
                    label="RoleCode"
                    width="100">
                    <template slot-scope="scope">
                        <el-input v-model='scope.row.workIpRole' placeholder="Role" :readonly='!workId?false:(scope.row.isadd||update1)?false:true' ></el-input>
                        <!-- <el-input v-model=scope.row.workIpRole placeholder="Role" onkeyup="this.value=this.value.toUpperCase()" ></el-input> -->
                    </template>
                </el-table-column>
                <el-table-column
                    prop="ipSocietyCode"
                    label="Soc"
                    width="70">
                    <template slot-scope="scope">
                        <el-input v-model='scope.row.ipSocietyCode' placeholder="Soc" readonly></el-input>
                    </template>
                </el-table-column>
                <el-table-column
                    prop=""
                    label="Ip Share"
                    width="100">
                    <template slot-scope="scope">
                        <el-input v-model='scope.row[updateIpshare]' @change="changeIpShare(scope.row,scope.$index)" @dblclick.native="editContract(scope.row, scope.$index)" placeholder="Ip Share" :readonly='!workId?false:(scope.row.isadd||update1)?false:true'></el-input>
                        <!-- <el-input v-model=scope.row[updateIpshare] placeholder="Ip Share" @dblclick.native="editContract(scope.row, scope.$index)"></el-input> -->
                    </template>
                </el-table-column>
                <el-table-column
                    prop="sdcheck"
                    label="Sd"
                    width="50px">
                    <template slot-scope="scope">
                        <el-checkbox v-model='scope.row.sdcheck' :disabled='!workId?false:(scope.row.isadd||update1)?false:true'></el-checkbox>
                        <!-- <el-checkbox v-model='scope.row.sdcheck' @change="changeSd(scope.row)"></el-checkbox> -->
                    </template>
                </el-table-column>
                <el-table-column
                    label="operation"
                    width="120">
                    <template slot-scope="scope">
                        <span style="width: 100%;display: inline-block;text-align: center;" >
                            <i class="el-icon-delete" v-if="isAuth('works:base:change')" @click="deletedata2(scope.$index,update1?tableData[formShare.rightType]:rightData[formShare.rightType])"></i>
                        </span>
                        <!-- <span style="width: 40px;display: inline-block" >
                            <i class="el-icon-check"    @click="checkdata2(scope.$index,rightData[formShare.rightType])"></i>
                        </span> -->
                    </template>
                </el-table-column>
            </el-table>
            <div v-if="update1">
                <p class="title">WriterOP</p>
                <el-table
                    :data="WriterOPData[formShare.rightType]"
                    border
                    stripe
                    style="max-height: 200px;"
                    >
                    <el-table-column width="34">
                        <template slot-scope="scope">
                            <i class="el-icon-star-on" style="color: red;" v-if="scope.row.agrNo"></i>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="groupIndicator"
                        label="Gp"
                        width="70px">
                        <template slot-scope="scope">
                            <el-input v-model='scope.row.groupIndicator' placeholder="Gp" readonly></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="name"
                        label="Name"
                        min-width="100px">
                        <template slot-scope="scope">
                            <el-input v-model='scope.row.name' placeholder="" readonly></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="chineseName"
                        label="Chinese Name">
                        <template slot-scope="scope">
                            <el-input v-model='scope.row.chineseName' placeholder="" readonly></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="ipNameNo"
                        label="Ip Name No"
                        width="150px">
                        <template slot-scope="scope">
                            <el-input v-model='scope.row.ipNameNo' placeholder="" readonly></el-input>
                            <!-- <el-input v-model=scope.row.ipNameNo placeholder="雙擊查詢" @dblclick.native="getIp(scope.$index, scope.row)" readonly></el-input> -->
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="workIpRole"
                        label="RoleCode"
                        width="100">
                        <template slot-scope="scope">
                            <el-input v-model='scope.row.workIpRole' placeholder="Role" readonly ></el-input>
                            <!-- <el-input v-model=scope.row.workIpRole placeholder="Role" onkeyup="this.value=this.value.toUpperCase()" ></el-input> -->
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="ipSocietyCode"
                        label="Soc"
                        width="70">
                        <template slot-scope="scope">
                            <el-input v-model='scope.row.ipSocietyCode' placeholder="Soc" readonly></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop=""
                        label="Ip Share"
                        width="100">
                            <el-input placeholder="" @dblclick.native="editContract(scope.row, scope.$index)" readonly></el-input>
                            <!-- <el-input v-model=scope.row[updateIpshare] placeholder="Ip Share" @dblclick.native="editContract(scope.row, scope.$index)"></el-input> -->
                    </el-table-column>
                    <el-table-column
                        prop="sdcheck"
                        label="Sd"
                        width="50px">
                        <template slot-scope="scope">
                            <el-checkbox v-model='scope.row.sdcheck' disabled></el-checkbox>
                            <!-- <el-checkbox v-model='scope.row.sdcheck' @change="changeSd(scope.row)"></el-checkbox> -->
                        </template>
                    </el-table-column>
                    <el-table-column
                        label="operation"
                        width="120">
                        <template slot-scope="scope">
                            <span style="width: 100%x;display: inline-block;text-align: center;" >
                                <i class="el-icon-delete" v-if="isAuth('works:base:change')" @click="deletedata2(scope.$index,tableData[formShare.rightType])"></i>
                            </span>
                            <!-- <span style="width: 40px;display: inline-block" >
                                <i class="el-icon-check"    @click="checkdata2(scope.$index,rightData[formShare.rightType])"></i>
                            </span> -->
                        </template>
                    </el-table-column>
                </el-table>
                <p class="title">SubPublisher</p>
                <el-table
                    :data="SubPublisherData[formShare.rightType]"
                    border
                    stripe
                    style="max-height: 200px;"
                    >
                    <el-table-column width="34">
                        <template slot-scope="scope">
                            <i class="el-icon-star-on" style="color: red;" v-if="scope.row.agrNo"></i>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="groupIndicator"
                        label="Gp"
                        width="70px">
                        <template slot-scope="scope">
                            <el-input v-model='scope.row.groupIndicator' placeholder="Gp" readonly></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="name"
                        label="Name"
                        min-width="100px">
                        <template slot-scope="scope">
                            <el-input v-model='scope.row.name' placeholder="" readonly></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="chineseName"
                        label="Chinese Name">
                        <template slot-scope="scope">
                            <el-input v-model='scope.row.chineseName' placeholder="" readonly></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="ipNameNo"
                        label="Ip Name No"
                        width="150px">
                        <template slot-scope="scope">
                            <el-input v-model='scope.row.ipNameNo' placeholder="" readonly></el-input>
                            <!-- <el-input v-model=scope.row.ipNameNo placeholder="雙擊查詢" @dblclick.native="getIp(scope.$index, scope.row)" readonly></el-input> -->
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="workIpRole"
                        label="RoleCode"
                        width="100">
                        <template slot-scope="scope">
                            <el-input v-model='scope.row.workIpRole' placeholder="Role" readonly ></el-input>
                            <!-- <el-input v-model=scope.row.workIpRole placeholder="Role" onkeyup="this.value=this.value.toUpperCase()" ></el-input> -->
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="ipSocietyCode"
                        label="Soc"
                        width="70">
                        <template slot-scope="scope">
                            <el-input v-model='scope.row.ipSocietyCode' placeholder="Soc" readonly></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop=""
                        label="Ip Share"
                        width="100">
                            <el-input placeholder="" @dblclick.native="editContract(scope.row, scope.$index)" readonly></el-input>
                            <!-- <el-input v-model=scope.row[updateIpshare] placeholder="Ip Share" @dblclick.native="editContract(scope.row, scope.$index)"></el-input> -->
                    </el-table-column>
                    <el-table-column
                        prop="sdcheck"
                        label="Sd"
                        width="50px">
                        <template slot-scope="scope">
                            <el-checkbox v-model='scope.row.sdcheck' disabled></el-checkbox>
                            <!-- <el-checkbox v-model='scope.row.sdcheck' @change="changeSd(scope.row)"></el-checkbox> -->
                        </template>
                    </el-table-column>
                    <el-table-column
                        label="operation"
                        width="120">
                        <template slot-scope="scope">
                            <span style="width: 100%x;display: inline-block;text-align: center;" >
                                <i class="el-icon-delete" v-if="isAuth('works:base:change')" @click="deletedata2(scope.$index,tableData[formShare.rightType])"></i>
                            </span>
                            <!-- <span style="width: 40px;display: inline-block" >
                                <i class="el-icon-check"    @click="checkdata2(scope.$index,rightData[formShare.rightType])"></i>
                            </span> -->
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>
        <select-ip v-if="IpTableVisible" ref="selectIpCom" :search="ipSearch" :showAdd="true" @checkIp="checkIp"></select-ip>
    </div>
</template>

<script>
    import selectIp from '../../components/select-ip';
    import axios from '../../utils/httpRequest'
    import qs from 'qs'
    export default {
        name: 'ipshare',
        data () {
            return {
                formShare: {
                    rightType: 'PER',
                    shareType: ''
                },
                update: true,
                dischecked: true,
                updateRsd: true,
                rschecked: false,
                ipShareCount: {
                    PER: 0,
                    MEC: 0,
                    ZYN: 0,
                    NOD: 0,
                    NDB: 0
                },
                disCanCheck: true,

                rightData: {},

                // 查詢ip
                selectIpType: '', //哪里查找會員的  info, assignee, extend
                editIpIndex: 0,
                IpTableVisible: false,
                ipSearch: {},
                rightMap: {
                    PER: {},
                    MEC: {},
                    ZYN: {},
                    NOD: {},
                    NDB: {}
                },
                updateIpshare:'ipShare',
                // orignalWrkIpShare
                tableData:{},
                WriterOPData:{},
                SubPublisherData:{},
                update1:false,
                changeUpdate1:true,
                // showGetRefShare: true
            }
        },
        props: {
            tableRight: {
                type: Object,
                default: () => {
                    return {};
                }
            },
            shareType: {
                type: String,
                default: ""
            },
            wrkWorkRightMap: {
                type: Object,
                default: () => {
                    return {};
                }
            },
            baseInfo: {
                type: Object,
                default: () => {
                    return {};
                }
            },
            isADP: {
                type: Boolean,
                default: false
            },
            refWrkWorkRightMap:{
                type:Object,
                default: () => {
                    return {};
                }
            }
        },
        watch: {
            'formShare.rightType': {
                handler(newVal, oldVal){
                    // this.getWriterOPData()
                    // this.getSubPublisherData()
                },
                deep:true
            },
            tableRight : function(newVal, oldVal){
                console.log(this.tableRight,'this.tableRight')
                this.rightData = this.$utils.copy(this.tableRight);
                // this.getWriterOPData()
                // this.getSubPublisherData()
            },
            wrkWorkRightMap: function(){
                this.rightMap = this.$utils.copy(this.wrkWorkRightMap);
                let array = ['PER', 'MEC', 'ZYN', 'NOD', 'NDB'];
                array.forEach( item => {
                    this.rightMap[item] = this.rightMap[item] ? this.rightMap[item] : {
                        workId: this.baseInfo.workId,
                        workSociety: this.baseInfo.workSocietyCode,
                        workUniqueKey: this.baseInfo.workUniqueKey
                    };
                    this.rightMap[item].distributable = this.rightMap[item].distributable ? true : false;
                    this.rightMap[item].workSd = (this.rightMap[item].workSd && this.rightMap[item].workSd == 'Y') ? true : false;
                    this.rightMap[item].shareType = this.rightMap[item].shareType ? this.rightMap[item].shareType : 'M';

                })
            },
            rightData: {
                handler(newVal, oldVal){
                    this.checkDisCanCheck();
                    if(!this.update1 && this.changeUpdate1){
                        this.getTableData()
                        this.getWriterOPData()
                        this.getSubPublisherData()
                    }
                },
                deep:true
            },
            shareType(){
                this.formShare.shareType = this.shareType;
            }
        },
        created(){
            this.rightMap = this.$utils.copy(this.wrkWorkRightMap);

            let array = ['PER', 'MEC', 'ZYN', 'NOD', 'NDB'];
            array.forEach( item => {
                this.rightMap[item] = this.rightMap[item] ? this.rightMap[item] : {
                    workId: this.baseInfo.workId,
                    workSociety: this.baseInfo.workSocietyCode,
                    workUniqueKey: this.baseInfo.workUniqueKey
                };
                this.rightMap[item].distributable = this.rightMap[item].distributable ? true : false;
                this.rightMap[item].workSd = (this.rightMap[item].workSd && this.rightMap[item].workSd == 'Y') ? true : false;
                this.rightMap[item].shareType = this.rightMap[item].shareType ? this.rightMap[item].shareType : 'M';

            })

        },
        components: {
            selectIp
        },
        methods: {
            changeIpShare(data,val){
                console.log('data',data,val)
            },
            updateData(){
                this.update1=!this.update1
                this.changeUpdate1=false
                this.updateIpshare = this.update1?'orgWriterShare':'ipShare'
                // if(this.update1){
                //     this.showGetRefShare = true
                // }
                this.getTableData()

            },
            getTableData(){
                let rightData = this.$utils.copy(this.rightData)
                if(JSON.stringify(rightData)!={}){
                    let obj = {}
                    Object.keys(rightData).forEach((key)=>{
                        obj[key]=rightData[key].filter(item => item.oipLinkId==0)
                    });
                    this.tableData=obj
                }else{
                    this.tableData={}
                }
                // if(this.update1){
                //     if(JSON.stringify(rightData)!={}){
                //         Object.keys(rightData).forEach((key)=>{
                //             this.tableData[key]=rightData[key].filter(item => item.oipLinkId==0)
                //         });
                //     }else{
                //         this.tableData={}
                //     }
                //     this.updateIpshare='orignalWrkIpShare'
                // }else{
                //     this.tableData = rightData
                //     this.updateIpshare='ipShare'
                // }
            },
            getWriterOPData(){
                let rightData = this.$utils.copy(this.rightData)
                if(JSON.stringify(rightData)!={}){
                    Object.keys(rightData).forEach((key)=>{
                        this.WriterOPData[key]=rightData[key].filter(item => item.oipLinkId&&item.workIpRole=='E')
                    });
                }else{
                    this.WriterOPData={}
                }
            },
            getSubPublisherData(){
                let rightData = this.$utils.copy(this.rightData)
                if(JSON.stringify(rightData)!={}){
                    Object.keys(rightData).forEach((key)=>{
                        this.SubPublisherData[key]=rightData[key].filter(item => item.workIpRole=='SE' )
                    });
                }else{
                    this.SubPublisherData={}
                }
            },
            distributableChange(data){
                this.update = false
                this.$nextTick(() => {
                    this.update = true
                })
            },
            shareTypeChange(){
                this.update = false
                this.$nextTick(() => {
                    this.update = true
                })
            },
             /**
             *
             * 选取IP
             */
            getIp(index, row){
                this.editIpIndex = index;
                this.IpTableVisible = true;
                this.ipSearch = {
                    name_no: row.ipNameNo,
                    name: row.name,
                    // 因为soc区分ip share类型，不同类型soc值不同，es只存了一个soc值，所以，加载后可能会查不出对应的值，不要加载soc值
                    // soc: row.ipSocietyCode
                }

                this.$nextTick( () => {
                    this.$refs.selectIpCom.init();
                })
            },
            checkIp(info){
                // this.formContract.nameNo = info.ip_name_no;
                // this.formContract.name = info.name;
                let change = ''
                change= this.update1?'tableData':'rightData'
                this.$set(this[change][this.formShare.rightType][this.editIpIndex], 'ipNameNo', info.ip_name_no);
                this.$set(this[change][this.formShare.rightType][this.editIpIndex], 'name', info.name);
                this.$set(this[change][this.formShare.rightType][this.editIpIndex], 'chineseName', info.chinese_name);
                this.$set(this[change][this.formShare.rightType][this.editIpIndex], 'ipSocietyCode', info.society_code);
                this.$set(this[change][this.formShare.rightType][this.editIpIndex], 'workIpRole', info.role_code);
            },
            // 供父级组件調用，獲取当前ipshareData
            getIpShare(){
                //  為当前的列表數據  并不是所有類型的ipshare的數據
                if(this.update1){
                    return this.tableData
                }else{
                    return this.rightData
                }
                // return this.rightData;
            },
            // getShareType(){
            //     return this.formShare.shareType;
            // },
            getRightMap(){
                let tempMap = this.$utils.copy(this.rightMap);
                let array = ['PER', 'MEC', 'ZYN', 'NOD', 'NDB'];
                array.forEach( item => {
                    tempMap[item] = tempMap[item] ? tempMap[item] : {
                        workId: this.baseInfo.workId,
                        workSociety: this.baseInfo.workSocietyCode,
                        workUniqueKey: this.baseInfo.workUniqueKey
                    };
                    tempMap[item].distributable = tempMap[item].distributable ? 1 : 0;
                    tempMap[item].workSd = tempMap[item].workSd ? 1 : 0;
                    tempMap[item].rightType = item;

                })
                console.log('tempMap: ', tempMap);
                return tempMap;
            },
            addDummyIp(){
                this.$router.push({name: 'member-dummyinfo'});
                this.ipShareShow = false;
            },
            checkDisCanCheck (){
                let flag = true;
                // 每次change都检查distributable是否可以點擊
                // 当前type
                let count = 0;
                this.rightData[this.formShare.rightType] && this.rightData[this.formShare.rightType].forEach( (item, index) => {
                    count += Number(item.ipShare);
                    if(!item.groupIndicator && item.groupIndicator != 0){
                        flag = false;
                    }
                })
                if(count != 100){
                    flag = false;
                }
                let types = ['PER', 'MEC', 'ZYN', 'NOD', 'NDB'];
                if(!types.includes(this.formShare.rightType)){
                    count = 0;
                    this.rightData[item] &&  this.rightData[item].forEach( (value) => {
                        count += Number(value.ipShare);
                        if(!value.groupIndicator && value.groupIndicator != 0){
                            flag = false;
                        }
                    })
                    if(count != 100){
                        flag = false;
                    }
                }
                // types.forEach( (item,index) => {
                //     if(item == this.formShare.rightType){
                //         return;
                //     }

                //     count = 0;
                //     this.rightData[item] &&  this.rightData[item].forEach( (value) => {
                //         count += parseFloat(value.ipShare);
                //         if(!value.groupIndicator && value.groupIndicator != 0){
                //             flag = false;
                //         }
                //     })
                //     if(count != 100){
                //         flag = false;
                //     }
                // })
                this.disCanCheck = flag;
            },
            changeRsd (val) {
                this.rschecked = val
                this.update = false
                this.$nextTick(() => {
                    this.update = true
                })
                if (val) {
                    this.rightData[this.formShare.rightType].forEach((item, index) => {
                        item.sdcheck = val
                    })
                }
                if (!val) {
                    this.rightData[this.formShare.rightType].forEach((item, index) => {
                        item.sdcheck = val
                    })
                }
            },
            changeSd (item) {
                this.updateRsd = false
                this.$nextTick(() => {
                    this.updateRsd = true
                })
                if (JSON.stringify(this.rightData[this.formShare.rightType]).includes('false')) {
                    this.rschecked = false
                } else {
                    this.rschecked = true
                }
            },
            deletedata2 (index, rows) {
                console.log(index,rows)
                this.$msgbox.confirm(`確定進行[删除]操作?`, '提示', {
                    confirmButtonText: '確定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    // this.update1?tableData[formShare.rightType]:rightData[formShare.rightType]
                    if(this.update1){
                        this.tableData[this.formShare.rightType].splice(index, 1)
                    }else{
                        this.rightData[this.formShare.rightType].splice(index, 1)
                    }
                })
            },
            getSummaries (param) {
                const { columns, data } = param
                const sums = []
                console.log(data)
                columns.forEach((column, index) => {
                    if (index === 1) {
                        sums[index] = '總計'
                    } else if (index === 7) {
                        // const values = data.map(item => Number(item[column.property]))
                        // console.log(values)
                        // if (!values.every(value => isNaN(value))) {
                        //     sums[index] = values.reduce((prev, curr) => {
                        //         const value = Number(curr)
                        //         if (!isNaN(value)) {
                        //             return prev + curr
                        //         } else {
                        //             return prev
                        //         }
                        //     }, 0)
                        //      sums[index] = sums[index].toFixed(2)
                        // } else {
                        //     sums[index] = 'N/A'
                        // }
                        let val = 0
                        sums[index] = data.forEach(item=>{
                            val = val+Number(item[this.updateIpshare])
                        })
                        sums[index] = val.toFixed(2)
                    } else {
                        sums[index] = ''
                    }
                })
                return sums
            },
            // 复制功能
            copyRight () {
                this.$msgbox.confirm(`copy操作後，將覆蓋其他類型的IP Share`, '提示', {
                    confirmButtonText: '確定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    let change = ''
                    change= this.update1?'tableData':'rightData'
                    let types = ['PER', 'MEC', 'ZYN', 'NOD', 'NDB'];
                    types.forEach( item => {
                        if(item != 'PER'){
                            this[change][item] = this.$utils.copy(this[change]['PER']);
                            this[change][item].forEach( right => {
                                delete right.id;
                            })
                        }
                    })
                    // this.rightData[this.formShare.rightType];
                })
            },
            adddata2 () {
                this.rschecked = false
                let change = ''
                change= this.update1?'tableData':'rightData'
                let myArray = [{ groupIndicator: '', name: '', ipNameNo: '', workSocietyCode: '', ipShare: '', sdcheck: '', ipType: '', ipSocietyCode: '', oipLinkId:'',orignalWrkIpShare:'',isadd:true }]
                if(!this[change][this.formShare.rightType]){
                    this.$set(this[change], this.formShare.rightType, []);
                }
                // this.rightData[this.formShare.rightType] = [...this.rightData[this.formShare.rightType], ...myArray];
                this.$set(this[change], this.formShare.rightType+"", [...this[change][this.formShare.rightType], ...myArray]);
                console.log(this[change])

                // this.getTableData()
            },

            editContract(row, index){
                console.log(row)
                if(row.agrNo){
                    this.$router.push({ name: 'contractInfo', query: {contractNo: row.agrNo, nameId: row.agrNo, title: row.agrNo} })
                }

            },
            getRefShare(){
                // this.showGetRefShare = false
                // this.tableData = Object.assign({},this.refWrkWorkRightMap)
                this.tableData = JSON.parse(JSON.stringify(this.refWrkWorkRightMap))
                // this.deepClone(this.refWrkWorkRightMap,this.tableData)
                console.log("this")
                console.log(this)
            },
            deepClone(obj, newObj) {
                var newObj = newObj || {};
                for (let key in obj) {
                    if (typeof obj[key] == 'object') {
                        newObj[key] = (obj[key].constructor === Array) ? [] : {}
                        this.deepClone(obj[key], newObj[key]);
                    } else {
                        newObj[key] = obj[key]
                    }
                }
                return newObj;
            }
        },
        computed: {
            // tableData:{
            //     get (){
            //         let rightData = this.$utils.copy(this.rightData)
            //         let obj = {}
            //         if(this.update1){
            //             this.updateIpshare='orignalWrkIpShare'
            //             if(JSON.stringify(rightData)!={}){
            //                 Object.keys(rightData).forEach((key)=>{
            //                     obj[key]=rightData[key].filter(item => item.oipLinkId==0)
            //                 });
            //                 return obj
            //             }else{
            //                 return {}
            //             }
            //         }else{
            //             this.updateIpshare='ipShare'
            //             return rightData
            //         }
            //     }
            // },
            id: {
                get () {
                    return this.$store.state.lang.id
                }
            },
            workId: {
                get () {
                    return this.$route.query.id
                }
            },
            socId: {
                get () {
                    return this.$route.query.socId
                }
            }
        },
        mounted () {
            this.rightData = this.$utils.copy(this.tableRight);
            this.formShare.shareType = this.shareType;
            this.$nextTick(()=>{
                this.getTableData()
                this.getWriterOPData()
                this.getSubPublisherData()
            })
        },
        // watch: {

        // }
    }
</script>

<style lang="scss" scoped>
@import "../../assets/scss/works.scss";

    .component-class .demo-form-inline label{
        margin-left: 12px;
    }
    .component-class .demo-form-inline .el-form-item{
        margin: 10px 0;
    }
    .component-class thead th{
         border-top: 1px solid #eee;
    }
    .boxline .title{
        color: #444;
        font-size: 18px;
        font-weight: 600;
        margin-left: 10px;
    }
</style>
