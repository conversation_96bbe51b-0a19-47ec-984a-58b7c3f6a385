<template>
  <!-- 可支付列表 -->
  <div>
    <!-- <div style="margin-top: 20px"> -->
    <div style="">
      <el-tabs v-model="activeName" @tab-click="tabsChange">
        <!--net payment 已支付-->
        <el-tab-pane label="net payment" name="first">
          <el-form
            :inline="true"
            ref="netForm"
            :model="netSearch"
            class="demo-form-inline"
            @keyup.enter.native="searchNetList"
          >
            <el-form-item
              label="ip base no"
              prop="ipBaseNo"
              v-if="name == 'incomeMember'"
            >
              <el-input
                v-model="netSearch.ipBaseNo"
                placeholder
                style="width: 200px"
              ></el-input>
            </el-form-item>
            <el-form-item
              label="soc no"
              prop="societyCode"
              v-if="name == 'incomeSociety'"
            >
              <el-input
                v-model="netSearch.societyCode"
                placeholder
                style="width: 200px"
              ></el-input>
            </el-form-item>
            <el-form-item label="name" prop="name">
              <el-input
                v-model="netSearch.name"
                placeholder
                style="width: 200px"
              ></el-input>
            </el-form-item>
            <el-form-item label="Tax Year Period">
              <el-input
                v-model="netSearch.startYear"
                ref="netStart"
                @blur="changeYear(netSearch.startYear, 'netStart')"
                placeholder="startYear"
                style="width: 120px"
              ></el-input>
            </el-form-item>
            <el-form-item label="">
              <el-input
                v-model="netSearch.endYear"
                ref="netEnd"
                @blur="changeYear(netSearch.endYear, 'netEnd')"
                placeholder="endYear"
                style="width: 120px"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                @click="searchNetList"
                v-if="isAuth('payment:incomeDetail:searchNetList1')"
                >搜 索</el-button
              >
            </el-form-item>
            <el-form-item>
              <span
                class="clear-search"
                @click="clearNetSearch()"
                v-if="isAuth('payment:incomeDetail:searchNetList1')"
                >清除搜索</span
              >
            </el-form-item>
          </el-form>
          <el-table
            :data="
              netData
                ? netData.distAutopayNetPayMemberList ||
                  netData.distAutopayNetPaySocietyList
                : []
            "
            border
            style="width: 100%; margin-top: 15px"
            :empty-text="emptyText"
          >
            <el-table-column prop="taxYear" label="Tax Year"> </el-table-column>
            <el-table-column prop="payDate" label="支付時間">
              <template slot-scope="scope">
                {{ scope.row.payDate ? scope.row.payDate.split(" ")[0] : "" }}
              </template>
            </el-table-column>
            <el-table-column prop="autopayNo" label="autopayNo">
            </el-table-column>
            <el-table-column prop="distNo" label="DistNo"> </el-table-column>
            <el-table-column
              prop="paName"
              label="Name"
              v-if="name == 'incomeMember'"
            >
            </el-table-column>
            <el-table-column
              prop="societyName"
              label="Name"
              v-if="name == 'incomeSociety'"
            >
            </el-table-column>
            <el-table-column prop="localTaxableIncome" label="Local Roy">
            </el-table-column>
            <el-table-column prop="overseasTaxableIncome" label="Overseas Roy">
            </el-table-column>
            <el-table-column prop="royaltyAmount" label="Royalty Amount">
            </el-table-column>
            <!-- <el-table-column
                            v-if="netData.salesTaxAmount"
                            prop="salesTaxAmount"
                            label="Sales Tax">
                        </el-table-column> -->
            <el-table-column prop="salesTaxAmount" label="Sales Tax">
            </el-table-column>
            <el-table-column prop="withheldTax" label="Withheld Tax">
            </el-table-column>
            <el-table-column prop="commissionAmount" label="Administration">
            </el-table-column>
            <el-table-column prop="deduction" label="Deduction">
            </el-table-column>
            <el-table-column prop="advance" label="Advance/Neg">
            </el-table-column>
            <el-table-column label="operation" width="180">
              <template slot-scope="scope">
                <span class="a-blue" @click="details(scope.row)">details</span>
                <span class="a-blue" v-if="scope.row.isEdit == 1">|</span>
                <span class="a-blue" v-if="scope.row.isEdit == 1" @click="redo(scope.row)">redo</span>
                <span class="a-blue" @click="transaction(scope.row)">Transaction</span>
              </template>
            </el-table-column>
          </el-table>
          <div class="netTotalList clearfix">
            <p>Total</p>
            <p>{{ netData ? netData.localTaxableIncome : "" }}</p>
            <p>{{ netData ? netData.overseasTaxableIncome : "" }}</p>
            <p>{{ netData ? netData.royaltyAmount : "" }}</p>
            <p>{{ netData ? netData.salesTaxAmount : "" }}</p>
            <p>{{ netData ? netData.withheldTax : "" }}</p>
            <p>{{ netData ? netData.commissionAmount : "" }}</p>
            <p>{{ netData ? netData.deduction : "" }}</p>
            <p>{{ netData ? netData.advance : "" }}</p>
          </div>
          <div class="netTotalList netTotalList2 clearfix">
            <p>Total Royalty Income</p>
            <p>
              {{
                netData
                  ? (netData.localTaxableIncome || 0) +
                    (netData.overseasTaxableIncome || 0)
                  : ""
              }}
            </p>
          </div>
          <!-- <el-pagination
                        background
                        layout="prev, pager, next"
                        :total='netTotal' @current-change="handleNetChange">
                    </el-pagination> -->
        </el-tab-pane>

        <!--retain payment 待支付-->
        <el-tab-pane label="retain payment" name="second">
          <el-form
            :inline="true"
            :model="retainSearch"
            ref="retainForm"
            class="demo-form-inline"
            @keyup.enter.native="searchRetainList"
          >
            <el-form-item
              label="ip base no"
              prop="ipBaseNo"
              v-if="name == 'incomeMember'"
            >
              <el-input
                v-model="retainSearch.ipBaseNo"
                placeholder
                style="width: 200px"
              ></el-input>
            </el-form-item>
            <el-form-item
              label="soc no"
              prop="societyCode"
              v-if="name == 'incomeSociety'"
            >
              <el-input
                v-model="retainSearch.societyCode"
                placeholder
                style="width: 200px"
              ></el-input>
            </el-form-item>
            <el-form-item label="name" prop="name">
              <el-input
                v-model="retainSearch.name"
                placeholder
                style="width: 200px"
              ></el-input>
            </el-form-item>
            <el-form-item label="Tax Year Period">
              <el-input
                v-model="retainSearch.startYear"
                ref="retainStart"
                @blur="changeYear(retainSearch.startYear, 'retainStart')"
                placeholder="startYear"
                style="width: 120px"
              ></el-input>
            </el-form-item>
            <el-form-item label="">
              <el-input
                v-model="retainSearch.endYear"
                ref="retainEnd"
                @blur="changeYear(retainSearch.endYear, 'retainEnd')"
                placeholder="endYear"
                style="width: 120px"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                @click="searchRetainList"
                v-if="isAuth('payment:incomeDetail:searchRetainList1')"
                >搜 索</el-button
              >
            </el-form-item>
            <el-form-item>
              <span
                class="clear-search"
                @click="clearRetainSearch()"
                v-if="isAuth('payment:incomeDetail:searchRetainList1')"
                >清除搜索</span
              >
            </el-form-item>
          </el-form>
          <el-table
            :data="
              retainData
                ? retainData.distAutopayMemberRetainList ||
                  retainData.distAutopaySocietyRetainList
                : []
            "
            border
            style="width: 100%; margin-top: 15px"
            :empty-text="emptyText1"
          >
            <!-- <el-table-column prop="taxYear" label="Tax Year"> </el-table-column> -->
            <el-table-column prop="payDate" label="支付時間">
              <template slot-scope="scope">
                {{ scope.row.payDate ? scope.row.payDate.split(" ")[0] : "" }}
              </template>
            </el-table-column>
            <el-table-column prop="autopayNo" label="autopayNo">
            </el-table-column>
            <el-table-column prop="distNo" label="DistNo"> </el-table-column>
            <el-table-column
              prop="paName"
              label="Name"
              v-if="name == 'incomeMember'"
            >
            </el-table-column>
            <el-table-column
              prop="societyName"
              label="Name"
              v-if="name == 'incomeSociety'"
            >
            </el-table-column>
            <el-table-column prop="localTaxableIncome" label="Local Roy">
            </el-table-column>
            <el-table-column prop="overseasTaxableIncome" label="Overseas Roy">
            </el-table-column>
            <el-table-column prop="royaltyAmount" label="Royable Amount">
            </el-table-column>
            <el-table-column prop="salesTaxAmount" label="Sales Tax">
            </el-table-column>
            <el-table-column prop="withheldTax" label="Withheld Tax">
            </el-table-column>
            <el-table-column prop="commissionAmount" label="Administration">
            </el-table-column>
            <el-table-column prop="deduction" label="Deduction">
            </el-table-column>
            <el-table-column prop="advance" label="Advance/Neg">
            </el-table-column>
            <el-table-column prop="" label="Hold" width="60">
              <template slot-scope="scope">
                <el-checkbox
                  :value="scope.row.pay"
                  true-label="H"
                  @change="hold(scope.row)"
                ></el-checkbox>
              </template>
            </el-table-column>
            <el-table-column label="operation" width="180">
              <template slot-scope="scope">
                <span class="a-blue" @click="details(scope.row)">details</span>
                <span class="a-blue" @click="transaction(scope.row)">Transaction</span>
              </template>
            </el-table-column>
          </el-table>
          <div class="retainTotalList clearfix">
            <p>Total</p>
            <p>{{ retainData ? retainData.localTaxableIncome : "" }}</p>
            <p>{{ retainData ? retainData.overseasTaxableIncome : "" }}</p>
            <p>{{ retainData ? retainData.royaltyAmount : "" }}</p>
            <p>{{ retainData ? retainData.salesTaxAmount : "" }}</p>
            <p>{{ retainData ? retainData.withheldTax : "" }}</p>
            <p>{{ retainData ? retainData.commissionAmount : "" }}</p>
            <p>{{ retainData ? retainData.deduction : "" }}</p>
            <p>{{ retainData ? retainData.advance : "" }}</p>
          </div>
          <div class="retainTotalList retainTotalList2 clearfix">
            <p>Total Royalty Income</p>
            <p>
              {{
                retainData
                  ? (retainData.localTaxableIncome || 0) +
                    (retainData.overseasTaxableIncome || 0)
                  : ""
              }}
            </p>
          </div>
          <!-- <el-pagination
                        background
                        layout="prev, pager, next"
                        :total='retainTotal' @current-change="handleretainChange">
                    </el-pagination> -->
        </el-tab-pane>
      </el-tabs>
    </div>
    <el-drawer
      :visible.sync="drawer.show"
      :title="drawer.title"
      :wrapperClosable="false"
      direction="rtl"
      size="50%"
    >
      <ul class="clear">
        <li v-for="(item, index) in drawer.detail" :key="index">
          <span class="label">{{ item.key }}:</span>
          <span class="value">{{ item.value }}</span>
        </li>
      </ul>
    </el-drawer>
  </div>
</template>

<script>
export default {
  name: "incomeDetail",
  data() {
    var validate1 = (rule, value, callback) => {
      let _ipBaseNo = '',_name = ''
      if(this.activeName == 'first'){
        _ipBaseNo = this.netSearch.ipBaseNo
        _name = this.netSearch.name
      } else {
        _ipBaseNo = this.retainSearch.ipBaseNo
        _name = this.retainSearch.name
      }
      if (!_ipBaseNo  && !_name) {
        callback(new Error("请至少填写一项1111"));
      } else {
        callback();
      }
    };
    var validate2 = (rule, value, callback) => {
      let _ipBaseNo = '',_name = ''
      if(this.activeName == 'first'){
        _ipBaseNo = this.netSearch.societyCode
        _name = this.netSearch.name
      } else {
        _ipBaseNo = this.retainSearch.societyCode
        _name = this.retainSearch.name
      }
      if (!_ipBaseNo && !_name) {
        callback(new Error("请至少填写一项2222"));
      } else {
        callback();
      }
    };
    return {
      rules1: {
        ipBaseNo: [
          // { required: true, message: "ip base no和name至少输入一项", trigger: "blur" },
          // { validator: validate1 },
        ],
        name: [
          // { required: true, message: "ip base no和name至少输入一项", trigger: "blur" },
          // { validator: validate1 },
        ],
      },
      rules2: {
        societyCode: [
          // { required: true, message: "soc no和name至少输入一项", trigger: "blur" },
          // { validator: validate2 },
        ],
        name: [
          // { required: true, message: "soc no和name至少输入一项", trigger: "blur" },
          // { validator: validate2 },
        ],
      },
      drawer: {
        show: false,
        detail: [],
      },
      retainSearch: {
        ipBaseNo: "",
        societyCode: "",
        name: "",
        startYear: "",
        endYear: "",
      },
      netSearch: {
        ipBaseNo: "",
        societyCode: "",
        name: "",
        startYear: "",
        endYear: "",
      },
      retainTotal: 0,
      netTotal: 0,
      retainData: {},
      netData: {},
      activeName: "second",
      name: this.$route.name,
      title: "",
      emptyText: "暫無數據",
      emptyText1: "暫無數據",
    };
  },
  methods: {
    tabsChange(data) {
      if (data.name == "first" && this.netSearch.societyCode) {
        this.netList();
      } else if (data.name == "second" && this.retainSearch.societyCode) {
        this.retainList();
      }
    },
    changeYear(data, ref) {
      if (data) {
        if (1000 > data || data > 9999) {
          this.$toast({ tips: "请输入正确的年份" });
          this.$refs[ref].focus();
          return;
        }
        if (!/(^[1-9]\d*$)/.test(data)) {
          this.$toast({ tips: "年份为正整数" });
          this.$refs[ref].focus();
          return;
        }
      }
    },
    redo(data) {
      this.$msgbox
        .confirm("確定redo?", "提示", {
          confirmButtonText: "確定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(() => {
          this.$http
            .post(`/dist/autopay/distAutopayNetPay${this.title}Redo/${data.id}`)
            .then((res) => {
              if (res.success && res.data.code == 200) {
                this.$toast({ tips: "操作成功" });
                if (this.title == "Member") {
                  this.netQueryMemberList();
                } else {
                  this.netQuerySocietyList();
                }
              } else {
                this.$toast({ tips: res.data.message });
              }
            });
        })
        .catch(() => {});
    },
    netQueryMemberList() {
      let params = this.$utils.copy(this.netSearch);
      // params.autopayNo = this.autopayNo
      this.emptyText = "數據加載中";
      this.$http
        .get("/dist/autopay/lisDistAutopayNetPayMemberIncome", { params })
        .then((res) => {
          if (res.success && res.data.code == 200) {
            console.log("MemberIncome");
            console.log(res.data.distAutopayNetPayMemberList);
            this.netData = res.data.data;
            if (
              !this.netData ||
              this.netData.distAutopayNetPayMemberList.length == 0
            ) {
              this.emptyText = "暫無數據";
            }

            if(this.activeName == 'first'){
              this.netData.distAutopayNetPayMemberList.forEach(item => {
              if(item.payDate){
                var payDate=new Date(item.payDate);
                var today = new Date()
                if (payDate >= today){
                    item.isEdit = 1
                }else{
                    item.isEdit= 0
                }
              }
            })
            }
            // this.netTotal = res.data.data.total
          } else {
            this.$toast({ tips: res.data.message });
          }
        });
    },
    netQuerySocietyList() {
      let params = this.$utils.copy(this.netSearch);
      // params.autopayNo = this.autopayNo
      this.emptyText = "數據加載中";
      this.$http
        .get("/dist/autopay/listDistAutopayNetPaySocietyIncome", { params })
        .then((res) => {
          if (res.success && res.data.code == 200) {
            this.netData = res.data.data;
            if (
              !this.netData ||
              this.netData.distAutopayNetPaySocietyList.length == 0
            ) {
              this.emptyText = "暫無數據";
            }

            if(this.activeName == 'first'){
              this.netData.distAutopayNetPaySocietyList.forEach(item => {
              if(item.payDate){
                var payDate=new Date(item.payDate);
                var today = new Date()
                if (payDate >= today){
                    item.isEdit = 1
                }else{
                    item.isEdit= 0
                }
              }
            })
            }
            // this.netTotal = res.data.data.total
          } else {
            this.$toast({ tips: res.data.message });
          }
        });
    },
    searchNetList() {
      this.$refs.netForm.validate((validate) => {
        if (validate) {
          this.netList();
        }
      });
    },
    netList() {
      const values = this.$refs.netForm.model
      if (this.name == "incomeMember") {
        if(!values.ipBaseNo && !values.name){
          this.$toast({ tips: 'ip base no和name至少一个不能為空' });
          return
        }
        this.netQueryMemberList();
        this.title = "Member";
      } else if (this.name == "incomeSociety") {
        if(!values.societyCode && !values.name){
          this.$toast({ tips: 'soc no和name至少一个不能為空' });
          return
        }
        this.netQuerySocietyList();
        this.title = "Society";
      }
    },
    hold(data) {
      let pay = data.pay;
      if (data.pay != "H") {
        data.pay = "H";
      } else {
        data.pay = "";
      }
      let params = {
        type: this.name == "incomeMember" ? "M" : "S",
      };
      this.$http
        .get(`/dist/autopay/hold/${data.id}`, { params: params })
        .then((res) => {
          if (res.success) {
            if (res.data.code == 200) {
              this.$toast({ tips: "操作成功" });
            } else {
              data.pay = pay;
              this.$toast({ tips: res.data.message });
            }
          }
        });
    },
    details(data) {
      this.drawer.show = true;
      this.drawer.detail = [];
      Object.keys(data).forEach((key) => {
        let obj = {};
        if (data[key]) {
          obj.key = key;
          obj.value = data[key];
          this.drawer.detail.push(obj);
        }
      });
    },
    transaction(data){
      // let routeName = "royalties-member";
      if(this.name == "incomeMember"){
        this.$router.push({
          name: "royalties-member",
          query: {
            id: data.distNo + data.paNameNo,
            ipBaseNo: data.ipBaseNo,
            distNo: data.distNo,
            paNameNo: data.paNameNo,
            paName: data.paName,
            nameId: data.distNo + data.paNameNo,
            title: data.paName
          },
          params: { id: data.paNameNo + data.distNo },
        });
      } else if(this.name == "incomeSociety"){
        this.$router.push({
          name: "royalties-society",
          query: {
            distNo: data.distNo,
            societyCode: data.societyCode,
            societyName: data.societyName,
            nameId: data.distNo + data.societyCode,
            id: data.distNo + data.societyCode,
            title: data.societyName
          },
          params: { id: data.societyCode + data.distNo },
        });
      }
      
    },
    retainQueryMemberList() {
      let params = this.retainSearch;
      // params.autopayNo = this.autopayNo
      this.emptyText1 = "數據加載中";
      this.$http
        .get("/dist/autopay/lisDistAutopayMemberRetainIncome", { params })
        .then((res) => {
          if (res.success && res.data.code == 200) {
            console.log("MemberRetainIncome");
            console.log(res.data);
            this.retainData = res.data.data;
            if (
              !this.retainData ||
              this.retainData.distAutopayMemberRetainList.length == 0
            ) {
              this.emptyText1 = "暫無數據";
            }
            // this.retainTotal = res.data.data.total
          } else {
            this.$toast({ tips: res.data.message });
          }
        });
    },
    retainQuerySocietyList() {
      let params = this.$utils.copy(this.retainSearch);
      // params.autopayNo = this.autopayNo
      this.emptyText1 = "數據加載中";
      this.$http
        .get("/dist/autopay/lisDistAutopaySocietyRetainIncome", { params })
        .then((res) => {
          if (res.success && res.data.code == 200) {
            this.retainData = res.data.data;
            if (
              !this.retainData ||
              this.retainData.distAutopaySocietyRetainList.length == 0
            ) {
              this.emptyText1 = "暫無數據";
            }
            // this.retainTotal = res.data.data.total
          } else {
            this.$toast({ tips: res.data.message });
          }
        });
    },
    searchRetainList() {
      this.$refs.retainForm.validate((validate) => {
        if (validate) {
          this.retainList();
        }
      });
    },
    retainList() {
      const values = this.$refs.retainForm.model
      if (this.name == "incomeMember") {
        if(!values.ipBaseNo && !values.name){
          this.$toast({ tips: 'ip base no和name至少一个不能為空' });
          return
        }
        this.retainQueryMemberList();
      } else if (this.name == "incomeSociety") {
        if(!values.societyCode && !values.name){
          this.$toast({ tips: 'soc no和name至少一个不能為空' });
          return
        }
        this.retainQuerySocietyList();
      }
    },
    clearNetSearch() {
      this.netSearch = {};
      // this.netList()
    },
    handleNetChange(val) {
      this.netSearch.page_num = val;
      this.netList();
    },
    clearRetainSearch() {
      this.retainSearch = {};
      // this.retainList()
    },
    handleretainChange(val) {
      this.retainSearch.page_num = val;
      this.retainList();
    },
  },

  mounted() {
    // this.retainList()
    // this.netList()
  },
};
</script>

<style>
.mydetail {
  width: 100% !important;
  display: block !important;
}

.mydateform .el-input--suffix {
  width: 200px !important;
}

.mydetail .el-form-item__content {
  width: calc(100% - 150px) !important;
}
</style>
<style scoped>
.retainTotalList {
  width: 100%;
  padding-left: calc((100% - 180px) / 12 * 3);
}
.retainTotalList p {
  width: calc((100% - 180px) / 9);
  float: left;
  border: 1px solid#ebeef5;
  box-sizing: border-box;
  line-height: 44px;
  height: 44px;
  /* text-align: right; */
  padding-left: 5px;
  margin: 5px auto 0;
  border-left: none;
}
.retainTotalList p:first-child {
  border: none;
  border-right: 1px solid#ebeef5;
}
.retainTotalList2 p:first-child {
  width: calc((100% - 180px) / 4.5);
}
.netTotalList {
  width: 100%;
  padding-left: calc((100% - 120px) / 13 * 4);
}
.netTotalList p {
  width: calc((100% - 140px) / 9);
  float: left;
  border: 1px solid#ebeef5;
  box-sizing: border-box;
  line-height: 44px;
  height: 44px;
  /* text-align: right; */
  padding-left: 5px;
  border-left: none;
}
.netTotalList p:first-child {
  border: none;
  border-right: 1px solid#ebeef5;
}
.netTotalList2 p:first-child {
  width: calc((100% - 140px) / 4.5);
}
ul {
  padding: 10px 0;
  padding-left: 20px;
  margin-top: 0;
  margin-bottom: 0;
}
li {
  list-style: none;
  float: left;
  margin-right: 16px;
  margin-top: 20px;
}

.label {
  color: #333;
}
.value {
  border: 1px solid #ccc;
  color: #777;
  border-radius: 4px;
  padding: 3px 8px;
  background: #fff;
  margin-left: 2px;
}
</style>