<template>
    <div>
        <el-form :inline="true" :model="formTransfer" ref="form" class="demo-form-inline" @keyup.enter.native="searchFn(1)">
            <el-form-item prop="workDestNo">
                <el-input v-model.number="formTransfer.work_dest_no" placeholder="workDestNo"></el-input>
            </el-form-item>
            <el-form-item prop="workSourceNo">
                <el-input v-model.number="formTransfer.work_souce_no" placeholder="workSourceNo"></el-input>
            </el-form-item>
            <el-form-item prop="status">
                <el-select v-model="formTransfer.status" placeholder="狀態">
                    <el-option label="全部" value=""></el-option>
                    <el-option label="等待轉換" value="W"></el-option>
                    <el-option label="成功" value="T"></el-option>
                    <el-option label="撤銷" value="U"></el-option>
                    <el-option label="處理中" value="P"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item prop="transferTime">
                <date-picker
                    v-model="formTransfer.transferTime"
                    type="date"
                    value-format="yyyy-MM-dd"
                    format="yyyyMMdd"
                    placeholder="transfer time">
                </date-picker>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="searchFn(1)" v-if="isAuth('works:transfer:find')">搜索</el-button>
            </el-form-item>
            <el-form-item>
                <span class="clear-search" @click="clearSearch()" v-if="isAuth('works:transfer:find')">清除搜索</span>
            </el-form-item>
        </el-form>
        <el-table
            :data="tableData"
            border
            stripe
            style="width: 100%"
            :empty-text="emptyText">
            <el-table-column
                prop="workDestNo"
                label="workDestNo"
                width="180">
            </el-table-column>
            <el-table-column
                prop="workDestNoSociety"
                label="workDestNoSoc"
                width="160">
            </el-table-column>
            <el-table-column
                prop="workSourceNo"
                label="workSourceNo"
                min-width="100">
            </el-table-column>
            <el-table-column
                prop="workSourceNoSociety"
                label="workSourceNoSoc"
                min-width="110">
            </el-table-column>
            <el-table-column
                prop="transferTime"
                label="transferTime">
                <template slot-scope="scope">
                    {{scope.row.transferTime  ? scope.row.transferTime.split(' ')[0] : '' }}
                </template>
            </el-table-column>
            <el-table-column
                prop="status"
                label="status">
                <template slot-scope="scope">
                    {{Status(scope.row.status)}}
                </template>
            </el-table-column>
            <el-table-column
                fixed="right"
                label="operation"
                width="100">
                <template slot-scope="scope">
                    <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer" v-if="isAuth('works:transfer:add')&&scope.row.status == 'W'">
                        <span class="a-red" @click="undo(scope.row, scope.$index)">撤銷</span>
                    </span>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :total=total @current-change="searchFn" :current-page="currentPage">
        </el-pagination>
    </div>
</template>

<script>
    // import axios from '../../utils/httpRequest'
    import qs from 'qs'
    export default {
        name: 'transfer',
        data () {
            return {
                config: {
                    status: {
                        W: '等待轉換',
                        T: '成功',
                        U: '撤銷',
                        P: '處理中'
                    }
                },
                formTransfer: {
                    work_dest_no: '',
                    work_souce_no: '',
                    status: '',
                    transferTime: ''
                },
                data: [],
                tableData: [],
                total: 1,
                currentPage:1,
                emptyText: '數據加載中',
            }
        },
        mounted(){
            this.searchFn(1);
        },
        watch: {
            // 'formTransfer.work_dest_no': function(val){
            //     this.formTransfer.work_dest_no =  val.replace(/\D/g, '');
            // },
            // 'formTransfer.work_souce_no': function(val){
            //     this.formTransfer.work_souce_no =  val.replace(/\D/g, '');
            // }
        },
        methods: {
            clearSearch(){
                this.formTransfer = {
                    work_dest_no: '',
                    work_souce_no: '',
                    status: '',
                    transferTime: ''
                }
                this.searchFn();
            },
            searchFn(num){
                // let page_num = num ? num : 1;
                if(!this.formTransfer.transferTime){
                    this.formTransfer.transferTime = ''
                }
                // let temp ='';
                // temp += '?page.pageNum='+num+'&page.pageSize=10';
                // temp += '&' + this.$utils.parseParams(this.formTransfer);
                this.formTransfer.page= {
                    pageNum: num ? num : 1,
                    pageSize: 10
                }
                this.emptyText = '數據加載中';
                this.$http.post('/wrk/transfer/getWrkWorkTransferByParams', this.formTransfer).then(res => {
                    if(res.status == 200){
                        this.tableData = res.data.list;
                        if(! this.tableData || this.tableData.length == 0){
                            this.emptyText = '暫無數據';
                        }
                        this.total = res.data.total;
                        this.currentPage = num ? num : 1
                    }
                })
            },
            parseParams(data) {
                try {
                    var tempArr = [];
                    for (var i in data) {
                        var key = encodeURIComponent(i);
                        var value = encodeURIComponent(data[i]);
                        tempArr.push(key + '=' + value);
                    }
                    var urlParamsStr = tempArr.join('&');
                    return urlParamsStr;
                } catch (err) {
                    return '';
                }
            },
            Status(status){
                return this.config.status[status];
            },
            undo(value, index){
                this.$msgbox.confirm('確定撤銷transfer?', '提示', {
                    confirmButtonText: '確定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.undoFn(value, index);
                })
            },
            undoFn(value, index){
                this.$http.get('/wrk/transfer/undoWrkWorkTransfer', {params:{workSourceNo: value.workSourceNo, workSourceNoSociety: value.workSourceNoSociety}}).then((res)=>{
                    if(res.status == 200){
                        this.$toast({tips: '撤銷成功'});

                        this.tableData[index].status = 'U';
                    }
                })
            }
        }
    }
</script>

<style scoped>

</style>
