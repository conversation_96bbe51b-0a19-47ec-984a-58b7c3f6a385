<template>
    <div>
        <div class="boxline">
            <div class="add-new">
                <el-button type="primary" v-if="isAuth('works:base:change')" @click="adddata4()">新 增</el-button>
            </div>
            <el-table
                :data="tableData4"
                border
                stripe
                style="width: 100%">
                <el-table-column
                    prop="titleType"
                    label="titleType">
                    <template slot-scope="scope">
                        <el-input v-model=scope.row.titleType placeholder="雙擊查詢" @dblclick.native="getTitleType(scope.$index,scope.row)" readonly></el-input>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="title"
                    label="Title">
                    <template slot-scope="scope">
                        <el-input v-model='scope.row.title' placeholder="Title"></el-input>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="episodeNo"
                    label="episodeNo">
                    <template slot-scope="scope">
                        <el-input v-model='scope.row.episodeNo' placeholder="episodeNo"></el-input>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="languageCode"
                    label="Lang">
                    <template slot-scope="scope">
                        <el-input v-model=scope.row.languageCode placeholder="雙擊查詢" @dblclick.native="getLang(scope.$index,scope.row)" readonly></el-input>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="genreCode"
                    label="Genre">
                    <template slot-scope="scope">
                        <el-input v-model=scope.row.genreCode placeholder="雙擊查詢" @dblclick.native="getGenre(scope.$index,scope.row)" readonly></el-input>
                    </template>
                </el-table-column>
                <!-- <el-table-column
                    prop="durationM"
                    label="DurationM">
                    <template slot-scope="scope">
                        <el-form :model="scope.row" :rules="rulesOriginal">
                            <el-form-item prop="durationM">
                                <el-input v-model.number=scope.row.durationM placeholder="DurationM"></el-input>
                            </el-form-item>
                        </el-form>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="durationS"
                    label="DurationS">
                    <template slot-scope="scope">
                        <el-form :model="scope.row" :rules="rulesOriginal">
                            <el-form-item prop="durationS">
                                <el-input v-model.number=scope.row.durationS placeholder="DurationS"></el-input>
                            </el-form-item>
                        </el-form>
                    </template>
                </el-table-column> -->
                <el-table-column
                    prop="inputSoc"
                    label="Soc"
                    width="90">
                    <template slot-scope="scope">
                        <el-input v-model=scope.row.inputSoc placeholder="Soc"></el-input>
                    </template>
                </el-table-column>
                <el-table-column
                    label="operation"
                    width="100">
                    <template slot-scope="scope">
                    <span class="spanright">
                        <i class="el-icon-delete" v-if="isAuth('works:base:change')" @click="deletedata4(scope.$index,tableData4)"></i>
                    </span>
                    </template>
                </el-table-column>
            </el-table>
        </div>
    <!-- lang -->
        <el-dialog :visible.sync="langShow" title="Lang" :close-on-click-modal="false">
            <div style="width: 500px;margin: auto;margin-bottom: 20px">
                <el-input placeholder="請輸入語言编碼" @keyup.enter.native="langSearch()" v-model="languageCode" class="input-with-select">
                    <el-button slot="append" icon="el-icon-search" @click="langSearch()"></el-button>
                </el-input>
            </div>
            <el-table :empty-text="tableresult"   stripe :data="langData">
                <el-table-column property="languageCode" label="語言编碼"></el-table-column>
                <el-table-column property="description" label="描述"></el-table-column>
                <el-table-column
                    label="operation"
                    width="100">
                    <template slot-scope="scope">
                    <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                      <i class="el-icon-check" v-if="isAuth('works:base:change')" @click="checkedLang(scope.$index,scope.row)"></i>
                    </span>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                background
                layout="prev, pager, next"
                :total=langTotal @current-change="langSearch">
            </el-pagination>
        </el-dialog>
<!-- genre -->
        <el-dialog :visible.sync="genreTableShow" title="Genre" :close-on-click-modal="false" width="750px">
            <div style="width: 500px;margin: auto;margin-bottom: 20px">
                <el-input placeholder="請輸入曲風" @keyup.enter.native="genreSearch()" v-model="generInput" class="input-with-select">
                    <el-button slot="append" icon="el-icon-search" @click="genreSearch()"></el-button>
                </el-input>
            </div>
            <el-table :empty-text="tableresult"   stripe :data="genreTableData">
                <el-table-column property="genreDetail" label="曲風詳情" width="120"></el-table-column>
                <el-table-column property="workType" label="作品類型" width="120"></el-table-column>
                <el-table-column property="usageFlag" label="使用標識" width="120"></el-table-column>
                <el-table-column property="description" label="描述"></el-table-column>
                <el-table-column
                    label="operation"
                    width="100">
                    <template slot-scope="scope">
                    <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                      <i class="el-icon-check" v-if="isAuth('works:base:change')" @click="checkedTableGenre(scope.$index,scope.row)"></i>
                    </span>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                background
                layout="prev, pager, next"
                :total=genreTotal @current-change="genreSearch">
            </el-pagination>
        </el-dialog>
<!-- titleType -->
        <el-dialog :visible.sync="titleShow" title="TitleType" :close-on-click-modal="false">
            <el-table :empty-text="tableresult" stripe :data="titleData">
                <el-table-column property="titleCode" label="titleCode" width="100px"></el-table-column>
                <el-table-column property="titleType" label="titleType" width="120px"></el-table-column>
                <el-table-column property="description" label="description" ></el-table-column>
                <el-table-column
                    label="operation"
                    width="100">
                    <template slot-scope="scope">
                        <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                            <i class="el-icon-check" v-if="isAuth('works:base:change')" @click="checkedTitle(scope.$index,scope.row)"></i>
                        </span>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                background
                layout="prev, pager, next"
                :total=titleTotal @current-change="titleSearch">
            </el-pagination>
        </el-dialog>
    </div>
</template>

<script>
    export default {
        name: 'othertitle',
        props: {
            tableData: {
                type: Array,
                default: () => []
            }
        },
        data () {
            var checkDurationM = (rule, value, callback) => {
                if (!Number.isInteger(value)) {
                    callback(new Error('請輸入數字'))
                } else {
                    if (value < 0 || value > 1000) {
                        callback(new Error('不能大於1000min或者小於0min！'))
                    } else {
                        callback()
                    }
                }
            }
            var checkDurationS = (rule, value, callback) => {
                console.log(value);
                if (!Number.isInteger(value)) {
                    callback(new Error('請輸入數字'))
                } else {
                    if (value < 0 || value > 59) {
                        callback(new Error('不能大於59s或者小於0s！'))
                    } else {
                        callback()
                    }
                }
            }
            return {
                
                tableData4: [
                    // {
                    //     titleType: '',
                    //     title: '',
                    //     languageCode: '',
                    //     genreCode: '',
                    //     durationM: '',
                    //     durationS: '',
                    //     inputSoc: '161'
                    // }
                ],
                genreTableData: [],
                generInput: '',
                pageInfo: {
                    page: {
                        pageNum: 0,
                        pageSize: 10
                    }
                },
                tableresult:' ',
                genreTableShow: false,
                languageShow: false,
                genreIndex: '',
                titleShow: false,
                titleindex: '',
                titleData: [],
                titleTotal: 1,
                langTotal: 1,
                genreTotal: 1,
                langData: [],
                langShow: false,
                langIndex: '',
                languageCode: '',


                rulesOriginal: {
                    durationM: [
                        // { required: true, message: '請輸入分钟', trigger: 'blur' },
                        { validator: checkDurationM, trigger: 'blur' }
                    ],
                    durationS: [
                        // { required: true, message: '請輸入秒', trigger: 'blur' },
                        { validator: checkDurationS, trigger: 'blur' }
                    ]
                },

            }
        },
        methods: {
            adddata4() {
                let myArray = [{
                    group: '',
                    title: '',
                    languageCode: '',
                    genreCode: '',
                    durationM: '',
                    durationS: '',
                    inputSoc: '161'
                }]
                this.tableData4 = [...this.tableData4, ...myArray]
            },
            deletedata4 (index, rows) {
                this.$msgbox.confirm(`確定進行[删除]操作?`, '提示', {
                    confirmButtonText: '確定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.tableData4.splice(index, 1)
                })
            },
            // title start
            spliceOt (arr) {
                arr.forEach((item, index) => {
                    if (item.titleCode === 'OT') {
                        arr.splice(index, 1)
                    }
                })
                this.titleData = arr
            },
            getTitleType(index) {
                this.titleShow = true;
                this.titleindex = index;
                this.titleSearch(1);
                
            },
            checkedTitle (index, item) {
                this.$set(this.tableData4[this.titleindex], 'titleType', item.titleCode);
                this.titleShow = false
            },
            titleSearch (val) {
                let data = {
                    page: {
                        pageNum: val ? val : 1,
                        pageSize: 10
                    }
                }
                this.$http.post('/ref/getRefWrkTitleTypeByCode', data).then(res => {
                    if (res.status === 200) {
                        // this.titleData = res.data.list
                        this.spliceOt(res.data.list);
                        this.titleTotal = res.data.total;
                    }
                })
            },
            // title end
            // lang start
            getLang (index, item) {
                this.langShow = true
                this.langIndex = index;
                this.langSearch(1);
            },
            langSearch (page) {
                this.pageInfo.languageCode = this.languageCode;
                this.pageInfo.page.pageNum = page ? page : 1;
                this.tableresult='數據加載中...'
                this.$http.post('/ref/languageByCode', this.pageInfo).then(res => {
                    if (res.status === 200) {
                        this.langData = res.data.list;
                        this.tableresult=this.langData.length==0?'暫無數據':' '
                        this.langTotal = res.data.total;
                    }
                })
            },
            checkedLang (index, item) {
                this.tableData4[this.langIndex].languageCode = item.languageCode;
                this.langShow = false;
            },
            // lang end
            //genre start 
            getGenre (index, item) {
                this.genreTableShow = true
                this.genreIndex = index
                this.genreSearch(1);
            },
            genreSearch (page) {
                this.pageInfo.page.pageNum = page ? page : 1;
                this.pageInfo.genreDetail = this.generInput;
                 this.tableresult = '數據加載中...'
                this.$http.post('/ref/getRefGenreDtlByDetail', this.pageInfo).then(res => {
                    if (res.status === 200) {
                        this.genreTableData = res.data.list
                        this.tableresult = this.genreTableData.length == 0 ? '暫無數據' : ' '
                        this.genreTotal = res.data.total
                    }
                })
            },
            checkedTableGenre (index, item) {
                this.genreTableShow = false
                this.tableData4[this.genreIndex].genreCode = item.genreDetail
            },
            // genre end
            getData(){
                return this.tableData4;
            }
        },
        watch: {
            tableData: function(){
                this.tableData4 = this.$utils.copy(this.tableData);
            }
        },
        mounted () {
            this.tableData4 = this.$utils.copy(this.tableData);
        }
    }
</script>

<style lang="scss" scoped>
    @import "../../assets/scss/works.scss";
    /deep/ .el-table{
        max-height: 320px !important;
        
    }
    /deep/ .el-table .cell>.el-input{
        margin-bottom: 18px;
    }

</style>
