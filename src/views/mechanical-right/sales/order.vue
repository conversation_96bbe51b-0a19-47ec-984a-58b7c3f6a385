<template>
    <div class="p-t-40">
        <top-bar :noShow="false" @save="saveOrder"></top-bar>
        <el-collapse v-model="activeNames">
            <!-- 订單信息 -->
            <el-collapse-item title="Order Info" class="step-jump" name="1">
                <el-form :inline="true" class="p-t-20">
                    <div>
                        <el-form-item label="Dist No">
                            <el-input type="text" v-model="salesInfo.distNo" readonly></el-input>
                        </el-form-item>
                        <el-form-item label="Producer ID">
                            <el-input type="text" v-model="salesInfo.producerId" readonly></el-input>
                        </el-form-item>

                        <el-form-item label="Producer Company">
                            <el-input type="text" v-model="salesInfo.producerName" readonly></el-input>
                        </el-form-item>
                    </div>
                    <div>
                        <el-form-item label="Batch No">
                           <el-input type="text" v-model="salesInfo.batchNo" readonly></el-input>
                        </el-form-item>
                        <el-form-item label="Total Amount">
                            <el-input type="text" v-model="salesInfo.amount" readonly></el-input>
                        </el-form-item>
                        <el-form-item label="Exchange Rate">
                            <el-input type="text" v-model="salesInfo.exchangeRate" readonly></el-input>
                        </el-form-item>
                    </div>
                    <div>
                        <el-form-item label="Currency">
                            <el-input type="text" v-model="salesInfo.currencyName" readonly></el-input>
                        </el-form-item>
                        <el-form-item label="Local Amount">
                            <el-input type="text" v-model="salesInfo.localEquivlent" readonly></el-input>
                        </el-form-item>
                    </div>
                    <div>
                        <el-form-item label="Sc ID">
                            <el-input type="text" v-model="salesInfo.id" readonly></el-input>
                        </el-form-item>
                        <el-form-item label="Sc No">
                           <el-input type="text" v-model="salesInfo.scNo" readonly></el-input>
                        </el-form-item>
                    </div>
                </el-form>
            </el-collapse-item>
            <!-- 商品信息 -->
            <el-collapse-item title="Base Info" class="step-jump" name="2">
                <el-form :inline="true" class="p-t-20" label-width="146px">
                    <div>
                        <el-form-item label="From Period" required>
                            <!-- <el-input type="text" v-model="baseInfo.fromPeriod"></el-input> -->
                            <!-- <el-input  v-model="baseInfo.fromPeriod" v-dateFmt></el-input> -->
                            <date-picker v-model="baseInfo.fromPeriod" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd"></date-picker>
                        </el-form-item>
                        <el-form-item label="To Period" required>
                            <!-- <el-input type="text" v-model="baseInfo.toPeriod"></el-input> -->
                            <!-- <el-input v-dateFmt v-model="baseInfo.toPeriod"></el-input> -->
                            <date-picker v-model="baseInfo.toPeriod" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd"></date-picker>
                        </el-form-item>
                    </div>
                    <div>
                        <el-form-item label="Unit Price" required>
                           <el-input type="text" v-model="baseInfo.unitPrice"></el-input>
                        </el-form-item>
                         <el-form-item label="Unit Sold" required>
                           <el-input type="text" v-model="baseInfo.unitSold"></el-input>
                        </el-form-item>
                    </div>
                    <div>
                        <el-form-item label="Roy Rate" required>
                            <el-input type="text" v-model="baseInfo.royRate"></el-input>
                        </el-form-item>
                    </div>
                    <div>
                        <el-form-item label="Sum Sc Work Total" label-width="200px">
                            <el-input type="text" v-model="totalAmount" readonly></el-input>
                        </el-form-item>
                    </div>
                </el-form>
            </el-collapse-item>
            <!-- works -->
            <el-collapse-item class="step-jump" name="3">
                 <el-table :empty-text="tableresult"   stripe :data="workData" border>
                    <el-table-column label="Seq">
                        <template slot-scope="scope">
                            <el-input type="text" v-model="scope.row.distMrdSalesWork.workSeqNo"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="Work Title">
                        <template slot-scope="scope">
                            <el-input type="text" v-model="scope.row.distMrdSalesWork.workTitle" @dblclick.native="getWork(scope.$index, scope.row)" readonly></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="Work ID">
                        <template slot-scope="scope">
                            <el-input type="text" v-model="scope.row.distMrdSalesWork.workId" @dblclick.native="getWork(scope.$index, scope.row)" readonly></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="Work Soc">
                        <template slot-scope="scope">
                            <el-input type="text" v-model="scope.row.distMrdSalesWork.workSocietyCode" readonly></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="Genre">
                        <template slot-scope="scope">
                            <el-input type="text" v-model="scope.row.distMrdSalesWork.genreCode" readonly></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="Share">
                        <template slot-scope="scope">
                            <el-input type="text" v-model="scope.row.distMrdSalesWork.ipShare"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="M">
                        <template slot-scope="scope">
                            <el-checkbox :value="scope.row.distMrdSalesWorkIpList.length == 0" readonly></el-checkbox>
                            <el-button @click="editIpShare(scope.row, scope.$index)">編輯</el-button>
                        </template>
                    </el-table-column>
                    <el-table-column label="Work Roy">
                        <template slot-scope="scope">
                            <el-input type="text" v-model="scope.row.distMrdSalesWork.grossWorkRoy"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="opteration">
                        <template slot-scope="scope">
                            <span class="operation">
                                <i class="el-icon-delete" @click="deleteData(scope.$index, workData)"></i>
                            </span>
                        </template>
                    </el-table-column>
                </el-table>
                <div class="add-new"><el-button type="primary" @click="addData(workData)">新 增</el-button></div>
            </el-collapse-item>
            <!-- end -->
        </el-collapse>
        <!-- 編輯 IP Share -->
        <el-dialog :visible.sync="ipShareShow" :close-on-click-modal="false">
            <el-form ref="editForm" :inline="true" label-width="160px">
                <el-table :empty-text="tableresult"   stripe :data="ipShareData" border>
                    <el-table-column label="Name No">
                        <template slot-scope="scope">
                            <el-input type="text" v-model="scope.row.ipNameNo" @dblclick.native="getIp(scope.$index, scope.row)" readonly></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="Name">
                        <template slot-scope="scope">
                            <el-input type="text" v-model="scope.row.name" @dblclick.native="getIp(scope.$index, scope.row)" readonly></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="Chinese Name" width="132px">
                        <template slot-scope="scope">
                            <el-input type="text" v-model="scope.row.chineseName" readonly></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="Work Ip Role">
                        <template slot-scope="scope">
                            <el-input type="text" v-model="scope.row.workIpRole"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="Share">
                        <template slot-scope="scope">
                            <el-input type="text" v-model="scope.row.distShare"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="Work Roy">
                        <template slot-scope="scope">
                            <el-input type="text" v-model="scope.row.grossWorkRoy"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="opteration" width="110px">
                        <template slot-scope="scope">
                            <span class="operation">
                                <i class="el-icon-delete" @click="deleteIpShare(scope.$index, ipShareData)"></i>
                            </span>
                        </template>
                    </el-table-column>
                </el-table>
                <div class="t-c" style="margin-top: 20px;">
                    <el-form-item>
                        <el-button type="primary" @click="addIpShare(ipShareData)">新 增</el-button>
                        <el-button type="primary" @click="saveIpShare()">確定</el-button>
                        <el-button type="" @click="cancelIpShare()">取消</el-button>
                    </el-form-item>
                </div>
            </el-form>
        </el-dialog>

        <select-ip v-if="IpTableVisible" ref="selectIpCom" :search="ipSearch" @checkIp="checkIp"></select-ip>

        <select-work v-if="workTableVisible" ref="selectWorkCom" :search="workSearch" @checkWork="checkWork"></select-work>
    </div>
</template>
<script>
import selectWork from '../../../components/select-work/index'
import selectIp from '../../../components/select-ip/index'

export default {
    data(){
        return {
            salesInfo: {},
            baseInfo: {},
            activeNames: ['1', '2', '3'],
            rightData: [{}],
            contactData: [],
            workData: [],tableresult:' ',
            show: false,

            // 查詢作品
            editIndex: 0,
            workTableVisible: false,
            workSearch: {},

            // 查詢ip
            editIpIndex: 0,
            IpTableVisible: false,
            ipSearch: {},

            editIpShareIndex: 0,
            editIpShareWorkId: '',

            // 作品ipshare手動添加
            ipShareShow: false,
            ipShareData: []

        }
    },
    computed: {
        totalAmount: function(){
           let total = 0;
           this.workData.forEach( item => {
               let amount = item.distMrdSalesWork.grossWorkRoy ? item.distMrdSalesWork.grossWorkRoy : 0;
               total = new BigNumber(total).plus( new BigNumber(amount)).toFixed(6);
           })
           return total;
        }
    },
    components: {
        selectWork,
        selectIp
    },
    created(){
        this.init();
    },
    methods: {
        init(){
            this.queryInfo();
            this.querySalesInfo();
        },
        queryInfo(){
            if(!this.$route.query.id){
                this.baseInfo = {
                    
                };
                this.workData = [];
                return;
            }
                  this.tableresult = '數據加載中...'
            this.$http.get('/dist/mrd/listDistMrdSalesDetails/' + this.$route.query.id).then( res => {
                if(res.success){
                    this.baseInfo = res.data.data.distMrdSalesDetails;
                    this.workData = res.data.data.distMrdSalesWorkVoList;
                }
                    this.tableresult = this.workData.length == 0 ? '暫無數據' : ' '
            })
        },
        querySalesInfo(){
            this.$http.get('/dist/mrd/listDistMrdSalesWithPage/' + this.$route.query.salesId).then( res => {
                if(res.success){
                    this.salesInfo = res.data.data.distMrdSales;
                    if(!this.$route.query.id){
                        this.baseInfo = {
                            producerId: this.salesInfo.producerId,
                            distNo: this.salesInfo.distNo,
                            batchNo: this.salesInfo.batchNo

                        }
                    }
                }
            })
        },
        addData(data){
            data.push({
                distMrdSalesWork: {
                    distNo: this.salesInfo.distNo,
                    producerId: this.baseInfo.producerId,
                    salesDetailId: this.baseInfo.id,
                    batchNo: this.baseInfo.batchNo,
                    batchSeqNo: this.baseInfo.batchSeqNo,
                    salesId: this.baseInfo.salesId,

                    workTitle: '',
                    workId: '',
                    workSocietyCode: '',
                    genreCode: ''

                },
                distMrdSalesWorkIpList: []})
        },
        /**
         * 选取作品
         */
        getWork(index, row) {
            this.editIndex = index;
            this.workTableVisible= true;
            this.workSearch = {
                workId: row.distMrdSalesWork.workId,
                soc: row.distMrdSalesWork.workSocietyCode,
                title: row.distMrdSalesWork.workTitle
            }
            this.$nextTick( () => {
                this.$refs.selectWorkCom.init();
            })
        },
        checkWork(info){
            // this.workData[this.editIndex].distMrdSalesWork.workTitle = info.title ? info.title : info.title_en;
            // this.workData[this.editIndex].distMrdSalesWork.workId = info.work_id;
            // this.workData[this.editIndex].distMrdSalesWork.workSocietyCode = info.work_society_code;
            // this.workData[this.editIndex].distMrdSalesWork.genreCode = info.genre_code;

            this.$set(this.workData[this.editIndex].distMrdSalesWork, 'workTitle', info.title ? info.title : info.title_en);
            this.$set(this.workData[this.editIndex].distMrdSalesWork, 'workId', info.work_id);
            this.$set(this.workData[this.editIndex].distMrdSalesWork, 'workSocietyCode', info.work_society_code);
            this.$set(this.workData[this.editIndex].distMrdSalesWork, 'genreCode', info.genre_code);
        },
        /**
         *
         * 选取IP
         */
        getIp(index, row){
            this.editIpIndex = index;
            this.IpTableVisible = true;
            this.ipSearch = {
                name_no: row.ipNameNo,
                name: row.name
            }
            this.$nextTick( () => {
                this.$refs.selectIpCom.init();
            })
        },
        checkIp(info){
            // this.ipShareData[this.editIpIndex].ipNameNo = info.ip_name_no;
            // this.ipShareData[this.editIpIndex].name = info.name;
            // this.ipShareData[this.editIpIndex].chineseName = info.chinese_name;
            this.$set(this.ipShareData[this.editIpIndex], 'ipNameNo', info.ip_name_no);
            this.$set(this.ipShareData[this.editIpIndex], 'name', info.name);
            this.$set(this.ipShareData[this.editIpIndex], 'chineseName', info.chinese_name);
        },
        /**
         * IPShare 操作
         */
        editIpShare(row, index){
            this.editIpShareIndex = index;
            this.editIpShareWorkId = row.distMrdSalesWork.id;
            this.ipShareShow = true;
            this.ipShareData = this.$utils.copy(row.distMrdSalesWorkIpList);
            console.log('0');
            console.log(this.ipShareData.length);
            console.log(this.ipShareData.ipNameMap);
            this.ipShareData.forEach( item => {
                if(item.ipNameMap){
                    item.name = item.ipNameMap.name;
                    item.chinese_name = item.ipNameMap.chinese_name;
                }
            })

        },
        addIpShare(data){
            data.push({
                ipNameNo: '',
                name: '',
                chineseName: '',
                distNo: this.salesInfo.distNo,
                salesWorkId: this.editIpShareWorkId
            })
        },
        deleteIpShare(index, data){
            this.$msgbox.confirm('確認刪除?', '提示', {
                confirmButtonText: '確認',
                cancelButtonText: '取消',
                type: 'warning'
            }).then( () => {
                data.splice(index,1)
            })
        },
        saveIpShare(){
            this.$set(this.workData[this.editIpShareIndex], 'distMrdSalesWorkIpList', this.$utils.copy(this.ipShareData));
            this.ipShareShow = false;
        },
        cancelIpShare(){
            this.ipShareShow = false;
        },
        deleteData(index, data){
            this.$msgbox.confirm('確認刪除?', '提示', {
                confirmButtonText: '確認',
                cancelButtonText: '取消',
                type: 'warning'
            }).then( () => {

                if(!data[index].distMrdSalesWork.id){
                    data.splice(index,1);
                    return;
                }

                this.$http.delete('/dist/mrd/delDistMrdSalesWork/' + data[index].distMrdSalesWork.id).then( res => {
                    if(res.success){
                        this.$toast({tips: '删除成功'})
                        data.splice(index,1);
                    }
                })
            })

        },

        saveOrder(){
            let ajaxData = {
                distMrdSalesDetails: this.baseInfo,
                distMrdSalesWorkVoList: this.workData
            }
            ajaxData.distMrdSalesDetails.salesId = this.$route.query.salesId;
            console.log('sales: ', this.$route.query.salesId);
            this.$http.post('/dist/mrd/saveDistMrdSalesDetails', ajaxData).then( res => {
                if(!res.success || res.data.code != 200){
                    this.$toast({tips: res.data.message})
                }else{
                    this.$toast({tips: '保存成功'});
                    this.$bus.$emit('updateReset');
                    this.$bus.$emit('closeCurrentTab');
                }
            })
        }
    }
}
</script>
<style lang="scss" scoped>
    @import '../../../assets/scss/works.scss';
</style>

