<template>
<!-- 拆分後的文件 用于審核用的 -->
    <div class="ipibpox">
        <el-form :inline="true" :model="search" ref="form" class="demo-form-inline"  @keyup.enter.native="searchFn()">
            <el-form-item prop="Work Title">
                <el-input v-model.trim="search.title" placeholder="title" style="width: 220px;"></el-input>
            </el-form-item>
            <el-form-item>
                <el-select v-model="search.status" placeholder="所有狀態" style="width: 120px;">
                    <el-option v-for=" (value, key) in config.status" :key="key" :value="key" :label="value"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="searchFn()">搜索</el-button>
            </el-form-item>
        </el-form>
        <el-table :empty-text="tableresult"   stripe :data="tableData" border style="width: 100%" v-loading="loading">

            <el-table-column
                prop="id"
                label="FID"
                width="80px">
            </el-table-column>
            <el-table-column
                prop="workTitles"
                label="Title"
                min-width="280px">
            </el-table-column>
            <el-table-column
                prop="artistNames"
                label="Aritists">
            </el-table-column>
            <el-table-column
                prop="authorNames"
                label="Author"
                min-width="90">
                <template slot-scope="scope">
                    {{scope.row.authorNames | addSpace}}
                </template>
            </el-table-column>
            <el-table-column
                prop="composerName"
                label="Composer"
                min-width="120">
                <template slot-scope="scope">
                    {{scope.row.composerName | addSpace}}
                </template>
            </el-table-column>
            <el-table-column
                label="Duration"
                width="120px">
                <template slot-scope="scope">
                    <span v-if="scope.row.durationM || scope.row.durationSs">{{scope.row.durationM + ':' + scope.row.durationSs}}</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="matchWorkTitle"
                label="Match Work Titile"
                min-width="170px">
            </el-table-column>
            <el-table-column
                label="Soc-WorkId"
                min-width="130">
                <template slot-scope="scope">
                    {{(scope.row.matchWorkSoc ? scope.row.matchWorkSoc : '') + (scope.row.matchWorkId ? '-'+scope.row.matchWorkId : '')}}
                </template>
            </el-table-column>
            <el-table-column
                prop="dataType"
                label="Type"
                width="80px">
            </el-table-column>
            <el-table-column
                fixed="right"
                label="OP"
                width="60px">
                <template slot-scope="scope">
                    <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer" v-if="!scope.row.status">
                        <span class="a-blue" @click="audit(scope.row)">審核</span>
                    </span>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :total="total" @current-change="searchFn">
        </el-pagination>
    </div>
</template>

<script>
    export default {
        name: 'sampleDateList',
        data () {
            return {
                tableData: [],
                tableresult:' ',
                total: 0,
                search: {
                    status: '0'
                },
                loading: false,
                config: {
                    status: {
                        0: '待審核',
                        1: '已匹配',
                        2: '不匹配'
                    },
                    uploadType: ['PG','FW','MS','CJ']
                }
            }
        },
        filters: {
            Status: function(status){
                let config = {
                    0: '待審核',
                    1: '已匹配',
                    2: '不匹配'
                }
                return config[status]
            }
        },
        methods: {
            searchFn (page) {
                this.loading = true;
                let ajaxData = {};
                if(page == 1){
                    this.total = 0
                }
                ajaxData.page = {
                    pageNum: page ? page : 1,
                    pageSize: 10
                }
                      this.tableresult = '數據加載中...'
                ajaxData.listMatchDataDsp = this.$utils.copy(this.search);
                this.$http.post('/claimmatch/getListMatchDataDspList', ajaxData).then(res => {
                    if (res.success) {
                        this.tableData = res.data.data.list;
                        this.total = res.data.data.total;
                    }
                          this.tableresult = this.tableData.length == 0 ? '暫無數據' : ' '
                    this.loading = false;
                })
            },
            audit (item) {
                this.$router.push({name: 'claimAudit', query: {id: item.id, type: item.dataType}})
            },
            changeSearchScore (score) {
                this.search.matchScore = score;
            }
        }
    }
</script>

<style>
</style>
