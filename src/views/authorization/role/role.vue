<template>
    <div class="mod-role">
        <el-form :inline="true" :model="dataForm" @submit.native.prevent>
            <el-form-item>
                <el-input v-model.trim="dataForm.roleName" placeholder="角色名稱" @keyup.enter.native="getDataList()"></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="getDataList()">搜索</el-button>
                <el-button type="primary" @click="addOrUpdateHandle()">新增</el-button>
            </el-form-item>
        </el-form>
        <el-table
            :data="dataList"
            border
            stripe
            v-loading="dataListLoading"
            style="width: 100%;"
            :empty-text="emptyText">
            <el-table-column
                prop="id"
                header-align="center"
                align="center"
                width="80"
                label="ID">
            </el-table-column>
            <el-table-column
                prop="name"
                header-align="center"
                align="center"
                width="240"
                label="角色名稱">
            </el-table-column>
            <el-table-column
                prop="remark"
                header-align="center"
                align="center"
                label="備註">
            </el-table-column>
            <el-table-column
                prop="createTime"
                header-align="center"
                align="center"
                width="240"
                label="創建時間">
            </el-table-column>
            <el-table-column
                fixed="right"
                header-align="center"
                align="center"
                width="150"
                label="操作">
                <template slot-scope="scope">
                    <el-button type="text" size="small"
                        @click="addOrUpdateHandle(scope.row.id)">修改
                    </el-button>
                    <el-button type="text" size="small"
                        @click="deleteHandle(scope.row.id, scope.$index)">删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :total=total  @current-change="getDataList" :current-page="currentPage">
        </el-pagination>
        <!-- 弹窗, 新增 / 修改 -->
        <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @update="getDataList()"></add-or-update>
    </div>
</template>

<script>
    import AddOrUpdate from './roleEdit'

    export default {
        data () {
            return {
                dataForm: {
                    roleName: ''
                },
                dataList: [],
                pageIndex: 1,
                pageSize: 10,
                total: 0,
                currentPage:1,
                dataListLoading: false,
                dataListSelections: [],
                addOrUpdateVisible: false,
                emptyText:'暫無數據'
            }
        },
        components: {
            AddOrUpdate
        },
        mounted () {
            this.getDataList()
        },
        methods: {
            //获取页面按钮权限
            // 獲取數據列表
            getDataList (page) {
                let ajaxData = {
                    page_num: page ? page : 1,
                    page_size: 10,
                    name: this.dataForm.roleName
                }
                this.dataListLoading = true;

                this.emptyText = '數據加載中';
                this.$http.get('/sys/roles', {params: ajaxData}).then( res => {
                    if(res.success){
                        this.dataList = res.data.data.list;
                        this.total = res.data.data.total;
                        this.currentPage = page ? page : 1
                        if(this.dataList.length==0 && this.total!=0){
                            this.getDataList (this.currentPage-1)
                        }
                    }else{
                        this.dataList = [];
                        this.total = 0;
                        this.currentPage = 1
                    }
                    if(! this.dataList || this.dataList.length == 0){
                        this.emptyText = '暫無數據';
                    }
                    this.dataListLoading = false;

                })
            },
            // 新增 / 修改
            addOrUpdateHandle (id) {
                this.addOrUpdateVisible = true
                this.$nextTick(() => {
                    this.$refs.addOrUpdate.init(id)
                })
            },
            // 删除
            deleteHandle (id, index) {
                this.$msgbox.confirm('確定刪除?', '提示', {
                    confirmButtonText: '確定',closeOnClickModal:false,
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.deleteFn(id, index);
                }).catch(() => {        
                });
            },
            deleteFn(id, index){
                this.$http.delete('/sys/roles/'+ id).then( res => {
                    if(res.success){
                        // this.dataList.splice(index-1, 1);
                        this.$toast({tips: '刪除成功'})
                        this.getDataList(this.currentPage)
                    }    
                })
            }
        }
    }
</script>
