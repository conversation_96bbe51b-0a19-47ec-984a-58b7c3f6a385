<template>
  <div>
    <el-dialog
      title="匯款"
      :visible.sync="showExport"
      width="850px"
      :show-close="true"
      :close-on-click-modal="false"
      :before-close='close'
    >
      <el-form :inline="true" class="demo-form-inline" ref="form" @keyup.enter.native="requData()">
        <!-- 第一行：Autopay No 和 Autopay Description -->
        <el-form-item label="Autopay No" label-width="150px" required>
          <el-input
            v-model="exportData.autopayNo"
            placeholder="雙擊查詢"
            @dblclick.native="getAutopay()"
            @change="changeAuto"
            style="width: 150px"
          ></el-input>
        </el-form-item>
        <el-form-item label="Autopay Description" label-width="170px">
          <el-input
            v-model="exportData.autopayDescription"
            placeholder=""
            style="width: 150px"
            readonly
          ></el-input>
        </el-form-item>

        <!-- 第二行：PA Name No 和 PA Name -->
        <el-form-item label="PA Name No" label-width="150px">
          <el-input
            v-model="exportData.paNameNo"
            placeholder="雙擊查詢"
            @dblclick.native="getPaName()"
            @change="changePa"
            style="width: 150px"
          ></el-input>
        </el-form-item>
        <el-form-item label="PA Name" label-width="170px">
          <el-input
            v-model="exportData.name"
            placeholder=""
            readonly
            style="width: 150px"
          ></el-input>
        </el-form-item>

        <el-form-item label="Sort排序" label-width="150px">
          <el-select
            v-model="exportData.orderWay"
            placeholder="請選擇排序方式"
            style="width: 150px"
          >
            <el-option
              label="By PA NAME NO"
              value="paNameNo"
            ></el-option>
            <el-option
              label="By PA NAME"
              value="paName"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="顯示分配號明細" label-width="170px">
          <el-select
            v-model="exportData.showDistNo"
            placeholder="請選擇"
            style="width: 150px"
          >
            <el-option
              label="是"
              :value="true"
            ></el-option>
            <el-option
              label="否"
              :value="false"
            ></el-option>
          </el-select>
        </el-form-item>


      </el-form>

      <!-- 第三行：PA Name No查询列表 -->
      <div style="display:flex;align-items:center;">
        <span style="margin-right:20px;">PA Name No查詢列表:</span>
        <el-table :data="paNameNoList" style="width: 75%;float:right" empty-text=" " :show-header="false">
          <el-table-column property="paNameNo" label="Pa Name No"></el-table-column>
          <el-table-column property="name" label="Name"></el-table-column>
          <el-table-column label="operation">
            <template slot-scope="scope">
              <span
                style="
                  width: 100%;
                  display: inline-block;
                  text-align: center;
                  cursor: pointer;
                "
              >
                <i class="el-icon-close" @click="del(scope.row)"></i>
              </span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div slot="footer">
        <el-button type="primary" @click="requData()">導出</el-button>
      </div>
    </el-dialog>
    <autopay-no ref="autopay" :autopayNo='autopayNo' @checkAutopay='checkAutopay' @flag='flag1=true'></autopay-no>
    <pa-name ref="paName" :paNameNo='paNameNo' @checkPaName='checkPaName' @flag='flag2=true'></pa-name>
  </div>
</template>
<script>
import autopayNo from './autopayNo';
import paName from './paName';
export default {
  name: "",
  data() {
    return {
      showExport: false,
      exportData: {
        autopayNo: "",
        autopayDescription: "",
        distNoDetails:'',
        paNameNo: "",
        name: "",
        orderWay: 'paNameNo',
        showDistNo: false
      },
      paNameNoList: [],
      autopayNo: '',
      paNameNo: '',
      flag1: true,
      flag2: true
    };
  },
  components: { autopayNo, paName },
  methods: {
    close(){
      this.showExport=false
      this.exportData = {
        autopayNo: "",
        autopayDescription: "",
        distNoDetails:'',
        paNameNo: "",
        name: "",
        orderWay: 'paNameNo',
        showDistNo: false
      }
      this.paNameNoList = []
    },
    checkAutopay(data){
      this.flag1=true
      this.$set(this.exportData,'autopayNo',data.autopayNo)
      this.$set(this.exportData,'autopayDescription',data.autopayDescription)
      this.$set(this.exportData,'distNoDetails',data.distNoDetails)
    },
    checkPaName(data){
      this.flag2=true
      this.$set(this.exportData,'paNameNo',data.ip_name_no)
      this.$set(this.exportData,'name',data.name)
      if(this.paNameNoList.length > 0){
        if(!this.paNameNoList.find(p => p.paNameNo === data.ip_name_no)){
          this.paNameNoList.push({'paNameNo':data.ip_name_no,'name':data.name})
        }
      } else {
        this.paNameNoList.push({'paNameNo':data.ip_name_no,'name':data.name})
      }
    },
    changePa(data){
      if(data){
        if(this.flag2){
          this.paNameNo = data
          this.flag2=false
          this.$nextTick(()=>{
            this.$refs.paName.paNameDataC()
          })
        }
      }else{
        this.$set(this.exportData,'name','')
      }
    },
    getPaName(){
      if(this.flag2){
        this.paNameNo = ''
        this.flag2=false
        this.$refs.paName.paNameDataD()
      }
    },
    exportList() {
      this.showExport = true;
    },
    changeAuto(data){
      if(data){
        if(this.flag1){
          this.autopayNo = data
          this.flag1=false
          this.$nextTick(()=>{
            this.$refs.autopay.autopayDataC()
          })
        }
      }else{
        this.$set(this.exportData,'autopayDescription','')
        this.$set(this.exportData,'distNoDetails','')
      }

    },
    getAutopay(){
      if(this.flag1){
        this.autopayNo = ''
        this.flag1=false
        this.$nextTick(()=>{
          this.$refs.autopay.autopayDataD()
        })
      }
    },
    requData() {
      let params = this.$utils.copy(this.exportData);
      if(!params.autopayNo){
        this.$toast({ tips: "Autopay No不能為空" });
        return
      }

      // 添加paNameNoList和Name数组参数
      if(this.paNameNoList.length > 0){
        let paNameNoArray = []
        let nameArray = []
        this.paNameNoList.forEach(item => {
          paNameNoArray.push(item.paNameNo)
          nameArray.push(item.name)
        })
        params.paNameNoList = paNameNoArray
        params.Name = nameArray
      } else {
        params.paNameNoList = []
        params.Name = []
      }

      this.$http.post("/dist/autoPay/Report/reportDistAutoPay750", params, {responseType: "blob"})
        .then((res) => {
          console.log(res.data)
          // this.showExport = false;
          let tempBlob = new Blob([res.data], { type: 'application/json' })
          let reader = new FileReader()
          reader.onload = e => {
            let res1 = e.target.result
            // 此处对fileReader读出的结果进行JSON解析
            // 可能会出现错误，需要进行捕获
            try {
              let json = JSON.parse(res1)
              this.$toast({ tips: json.message })
              // console.log('========',json)
              //正常json数据格式
            } catch (err) {
              // 该异常为无法将字符串转为json
              // 说明返回的数据是一个流文件
              // 不需要处理该异常，只需要捕获即刻
              this.close()
              this.$utils.downloadByBlobPDF(
              res.data,
              res.headers["content-disposition"]
                ? res.headers["content-disposition"].split(";")[1]
                : "=匯款導出"
              );

            }
          }
          // 将blob对象以文本的方式读出，读出完成后将会执行 onload 方法
          reader.readAsText(tempBlob)
          // if (res.data.type != "application/pdf") {
          //   console.log(JSON.parse(res))
          //   this.$toast({ tips: "導出失敗" });
          // } else {
          //   this.$utils.downloadByBlobPDF(
          //     res.data,
          //     res.headers["content-disposition"]
          //       ? res.headers["content-disposition"].split(";")[1]
          //       : "=匯款導出"
          //   );
          // }
        });
    },
    del(data){
      console.log(data)
      if(this.exportData.paNameNo === data.paNameNo){
        this.exportData.paNameNo = ''
        this.exportData.name = ''
      }
      this.paNameNoList = this.paNameNoList.filter(item => item.paNameNo != data.paNameNo)
    }
  },
};
</script>
<style scoped>
/deep/ .el-dialog__body {
  padding-top: 0;
}
/deep/ .el-radio + .el-radio {
  margin-left: 0;
}
</style>
