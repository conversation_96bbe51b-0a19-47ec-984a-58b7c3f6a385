<template>
    <div>
        <el-dialog :visible.sync="showD"   width="1000px">
            <div style="width: 500px;margin: auto;margin-bottom: 20px">
                <el-input placeholder="請輸入name" @keyup.enter.native="search()" v-model="writerInput" class="input-with-select">
                    <el-button slot="append" icon="el-icon-search" @click="search()"></el-button>
                </el-input>
            </div>
            <el-table :empty-text="emptyText" :data="writerGridData">
                <el-table-column property="name" label="name"></el-table-column>
                <el-table-column property="nameNo" label="ipNameNo"></el-table-column>
                <el-table-column property="nameType" label="nameType"></el-table-column>
                <el-table-column property="chineseName" label="chineseName"></el-table-column>
                <el-table-column property="societyCode" label="Soc"></el-table-column>
                <el-table-column
                    label="operation"
                    width="100">
                    <template slot-scope="scope">
                        <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                            <i class="el-icon-check" @click="checkedWriter(scope.$index,scope.row)"></i>
                        </span>
                    </template>
                </el-table-column>
            </el-table>
        </el-dialog>
    </div>
</template>
<script>
  import axios from '../../utils/httpRequest'

export default {
    props: {
        show: {
            type: Boolean,
            default: false
        }
    },
    data(){
        return {
            emptyText:'暫無數據',
            writerInput: '',
            modules: {},
            writerGridData: [],
            showD: this.show
        }
    },
    watch:{
        showD(){
            // this.show = this.showD;
            this.$emit('changValue', {name: 'show', value: this.showD});
        },
        show(){
            this.showD = this.show;
        }

    },
    methods: {
        search(name){
            if(name){
                this.writerInput = name.trim();
            }
            this.emptyText='數據加載中'
            let data = this.writerInput;
            let that = this
            axios.post('/agreement/getIpBaseNoByName?name=' + data).then(res => {
                if (res.status === 200) {
                    this.showD = true
                    that.modules = res.data
                    that.writerGridData = that.modules
                    if(! that.writerGridData || that.writerGridData.length == 0){
                        that.emptyText = '暫無數據';
                    }
                }
            })
        },
        checkedWriter (val, item) {
            this.$emit('updateIpInfo', item);
            this.showD = false

        },
    }
}
</script>
<style>
</style>


