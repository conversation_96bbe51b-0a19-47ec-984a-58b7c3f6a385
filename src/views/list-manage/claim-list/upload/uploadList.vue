<template>
  <!-- 上传的原文件 -->
  <!-- 上传清單 =》 拆分清單 =》 清單明細 -->
  <div class="ipibpox">
    <el-form :inline="true" :model="search" ref="form" class="demo-form-inline" @keyup.enter.native='querySearch(1)'>
      <el-form-item prop="fileName">
        <el-input v-model.trim="search.fileName" placeholder="fileName" style="width: 140px;"></el-input>
      </el-form-item>
      <el-form-item prop="categoryCode">
        <el-input v-model.trim="search.categoryCode" placeholder="categoryCode" style="width: 180px;"></el-input>
      </el-form-item>
      <el-form-item>
        <date-picker type="date" v-model="search.fileUploadStartDate" value-format="yyyy-MM-dd HH:mm:ss" format="yyyyMMdd" placeholder="Upload Time" style="width: 140px;"></date-picker>
        -
        <date-picker type="date" v-model="search.fileUploadEndDate" value-format="yyyy-MM-dd HH:mm:ss" format="yyyyMMdd" placeholder="Upload Time" style="width: 140px;"></date-picker>
      </el-form-item>
      <el-form-item prop="usageTime">
        <date-picker v-model="search.usageTime" placeholder="Usage Time" value-format="yyyy-MM-dd HH:mm:ss" format="yyyyMMdd" style="width: 140px;"></date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="querySearch(1)" v-if="isAuth('list-manage:claim-list:upload:uploadList:find')">查詢</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="addFn()" v-if="isAuth('list-manage:claim-list:upload:uploadList:addFn')">上传</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" :disabled="selectList.length <= 0" @click="createReport()" v-if="isAuth('list-manage:claim-list:upload:uploadList:createReport')">生成報告</el-button>
      </el-form-item>
      <el-form-item>
        <span class="clear-search" @click="clearSearch()" v-if="isAuth('list-manage:claim-list:upload:uploadList:find')">清除搜索</span>
      </el-form-item>
    </el-form>
    <el-table :empty-text="emptyText" stripe :data="tableData" border @selection-change='selectChange' @select-all='selectAll'>
      <el-table-column type="selection" header-align="center" align="center" width="50" :selectable='selectable'>
      </el-table-column>
      <el-table-column prop="id" label="ID">
      </el-table-column>
      <el-table-column prop="fileName" min-width="200" label="File Name">
        <template slot-scope="scope">
          <span :title="scope.row.fileName">{{scope.row.fileName}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="categoryCode" label="category code" min-width="110px">
      </el-table-column>
      <el-table-column prop="company" label="company" min-width="140px">
      </el-table-column>
      <el-table-column prop="createTime" label="Upload Time" width="190">

      </el-table-column>
      <el-table-column prop="amendTime" label="Finished Time" width="190">
        <template slot-scope="scope">
          <span v-if="scope.row.status == 2">{{scope.row.amendTime}}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="filePath" label="file Path" min-width="100px">
        <template slot-scope="scope">
          <span :title="scope.row.filePath">{{scope.row.filePath}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="matchMark" label="匹配註記" width="100px" align="center" :render-header="renderHeader">
      </el-table-column>
      <el-table-column prop="status" label="處理狀態" min-width="100px">
        <template slot-scope="scope">
          <span v-if="scope.row.status === 0">待處理</span>
          <span v-else-if="scope.row.status === 1">處理中</span>
          <span v-else-if="scope.row.status === 2">已完成</span>
          <el-button v-else @click="viewErrorMsg(scope.row.description)" type="text" size="small" style="cursor:pointer;margin-left: 0;" class="err">處理失敗</el-button>
          <!-- <span v-else>處理失敗</span> -->
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="operation" cell-style="operationStyle" width="160">
        <template slot-scope="scope">
          <el-button v-if="scope.row.status !== 0 && scope.row.status !== 3" @click="viewFile(scope.row)" type="text" size="small" style="cursor:pointer;">查看文件</el-button>
          <!-- <el-button v-if="scope.row.status === 3" @click="viewErrorMsg(scope.row.description)" type="text" size="small" style="cursor:pointer;margin-left: 0">失敗原因</el-button> -->
          <el-button v-if="scope.row.status == 3" type="text" size="small" @click="reAnalysis(scope.row.id)">重新解析</el-button>
          <el-button type="text" size="small" @click="deleteFile(scope.row)" v-if="isAuth('list-manage:claim-list:upload:uploadList:del')">刪除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination background layout="prev, pager, next" :total="total" @current-change="handleCurrentChange" :current-page="currentPage">
    </el-pagination>

    <generate-a-report v-if="generateAReportVisible" ref="generateAReport" ></generate-a-report>
  </div>
</template>

<script>
import GenerateAReport from './createreport'

export default {
  name: 'claimUploadList',
  data() {
    return {
      generateAReportVisible:false,
      tableData: [],
      total: 0,
      currentPage: 1,
      search: {
        categoryCode: '',
        fileName: '',
        status: '',
        uploadType: '',
        uploadUserName: '',
        fileType: 'C',
        fileUploadStartDate:'',
        fileUploadEndDate:'',
        usageTime:'',
      },
      dateRange: [],
      config: {
        status: {
          0: '等待處理',
          1: '處理中',
          2: '處理完成',
          3: '處理失敗'
        },
        listType: {
          0: 'PG',
          1: 'CJ',
          2: 'MS',
          3: 'FW'
        }
      },
      isSelect:false,
      selectList:[],
      emptyText: '暫無數據',
    }
  },
  components: {
    GenerateAReport
  },
  activated() {
    this.querySearch(this.currentPage);
  },
  created() {
    this.querySearch(1)
  },
  methods: {
    selectAll(row){
      let company = ''
      let fileExt = ''
      let indexs=[]
      row.forEach((item,index)=>{
        if(!index){
          company=item.company
          fileExt=item.fileExt
        }else{
          if((item.company != company) && (item.fileExt != fileExt)){
            indexs.push(index)
            // row.splice(index, 1)
            return false
          }
        }
      })
      for(let i=indexs.length-1;i>=0;i--){
        row.splice(indexs[i], 1)
      }
      this.selectList = row
    },
    createReport(){
      let fileQueueIds=''
      this.selectList.forEach((item,index)=>{
        if(index==0){
          fileQueueIds = item.id
        }else{
          fileQueueIds = fileQueueIds + ','+item.id
        }
      })
      let row = {
        fileQueueIds,
        company:this.selectList[0].company
      }
      this.generateAReportVisible = true
      this.$nextTick(() => {
        this.$refs.generateAReport.init(row)
      })
      // this.$router.push({name: 'distribute-createreport', query: {data:JSON.stringify(row)}})
    },
    selectChange(row){
      this.selectList = row
      if(this.selectList.length){
        this.isSelect=true
      }else{
        this.isSelect=false
      }
    },
    selectable(row, index){
      if(this.isSelect){
        if((row.company != this.selectList[0].company) && (row.fileExt != this.selectList[0].fileExt)){
          return false
        }
        return true
      }else{
        return true
      }
    },
    clearSearch() {
      this.search = {
        categoryCode: '',
        fileName: '',
        status: '',
        uploadType: '',
        uploadUserName: '',
        fileType: 'C',
        fileUploadStartDate:'',
        fileUploadEndDate:'',
        usageTime:'',
      }
      this.querySearch(1);
    },
    querySearch(page=1) {
      let param = this.$utils.copy(this.search)
      let endDate = param.fileUploadEndDate
      if (endDate) {
        param.fileUploadEndDate = endDate.split(' ')[0] + ' 23:59:59'
      }
      // this.search.page={pageNum:page,pageSize:10}
      // for(let item in this.search){
      //     console.log(item)
      //   if (item!='page'&&item!='fileType'&&this.search[item]) {
      //     console.log('item',item);
      //     this.search.page.pageNum=1
      //   }
      // }
      param.page={
        pageNum:page,
        pageSize:10
      }
      this.emptyText = '數據加載中';
      this.$http.post('/list/getListFileQueueList',param).then(res => {
        if (res.success) {
          this.tableData = res.data.data.list
          this.total = res.data.data.total;
          this.currentPage = param.page.pageNum;
          if(! this.tableData || this.tableData.length == 0){
            this.emptyText = '暫無數據';
          }
        }else{
          this.emptyText = '暫無數據';
        }
        
      })
    },
    handleSelect(item) {
      console.log(item);
    },
    handleCurrentChange(val) {
      let param = this.$utils.copy(this.search)
      param.page={
        pageNum:val,
        pageSize:10
      }

      this.emptyText = '數據加載中';
      this.$http.post('/list/getListFileQueueList',param).then(res => {
        if (res.success) {
          this.tableData = res.data.data.list
          this.total = res.data.data.total;
          this.currentPage = param.page.pageNum;
          if(! this.tableData || this.tableData.length == 0){
            this.emptyText = '暫無數據';
          }
        }else{
          this.emptyText = '暫無數據';
        }
      })
      // this.querySearch(val);
    },
    handleClick(item) {
      this.$router.push({ name: '', query: {} })
    },
    addFn() {
      this.$router.push({ name: 'claimListUpload' })
    },
    // 查看清單處理後的文件列表
    viewFile(row) {
      this.$router.push({ name: 'claimFileList', query: { queueId: row.id } })
    },
    viewErrorMsg(msg) {
      this.$alert(msg, '錯誤信息', {
        confirmButtonText: '確定'
      });
    },
    deleteFile(row) {
      this.$alert('確定要刪除嗎？', '刪除', {
        confirmButtonText: '确定',
        showCancelButton: true,
        cancelButtonText: '取消',
        callback: action => {
          if (action == 'confirm') {
            this.$http.delete('/list/deleteListFileQueue/' + row.id).then(res => {
              if (res.success && res.data.code == 200) {
                this.$toast({ tips: '刪除成功' })
                this.querySearch()
              } else {
                this.$toast({ tips: '刪除失敗' })
              }
            })
          }
        }
      });

    },
    reAnalysis(id) {
      this.$http.get('/listFileQueue/reparse/' + id).then(res => {
        if (res.data.code == 200) {
          this.querySearch()
        }
      })
    },
    renderHeader(h,{column}) {
        return h('div',[
          h('span', column.label),
          h('el-tooltip',{undefined,
            props:{undefined,
              effect:'dark',
              content:"執行DSP匹配處理程序(Y:執行)",
              placement:'top',
            },
          },
          [h('i', {undefined,
            class: 'el-icon-question',
            style: "color:red;margin-left:5px;cursor:pointer;"
          })],
          {content:"執行DSP匹配處理程序(Y:執行)"})

          ]);
      },


  },
  // beforeRouteEnter (to, from, next) {
  //   next(vm => {
  //     vm.querySearch() 
  //   })
  // }
}
</script>

<style scoped>
/deep/.el-table__fixed-right::before,
.el-table__fixed::before {
  border-bottom: 0 none;
}
.err /deep/.el-button--text span{
  color: #409EFF;
}
/* .my-tooltip .el-tooltip__popper{
  background-color:red
} */
.my-tooltip .el-tooltip__content{
  color:red
}
</style>
