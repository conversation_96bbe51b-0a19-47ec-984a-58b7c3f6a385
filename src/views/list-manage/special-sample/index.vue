<template>
  <div>
    <el-form :model="searchForm" :inline="true"  @keyup.enter.native='getList(1)'>
      <el-form-item label="資料夾">
        <el-input v-model.trim="searchForm.listBasicFileCombinedTotal.folder"></el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="searchForm.listBasicFileCombinedTotal.combinedType" placeholder="请选择" style="width: 120px">
          <el-option label="全部" value=""></el-option>
          <el-option label="使用日期" :value="0"></el-option>
          <el-option label="匯入日期" :value="1"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <date-picker v-model="searchForm.listBasicFileCombinedTotal.combinedStartDate" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd" style="width: 130px !important"></date-picker>
        <span>至</span>
        <date-picker v-model="searchForm.listBasicFileCombinedTotal.combinedEndDate" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd" style="width: 130px !important"></date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList()"  v-if="isAuth('list-manage:special-sample:index:find')">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="addList" v-if="isAuth('list-manage:special-sample:index:add')">新增</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="showExport('cue sheet查詢', 1)" v-if="isAuth('list-manage:special-sample:index:cue_sheet')">cue sheet查詢</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="showExport('明細報告', 2)" v-if="isAuth('list-manage:special-sample:index:detail')">明細報告</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="showExport('統計報告', 3)" v-if="isAuth('list-manage:special-sample:index:statistics')">統計報告</el-button>
      </el-form-item>
      <el-form-item>
        <span class="clear-search" @click="initList" v-if="isAuth('list-manage:special-sample:index:find')">清除搜索</span>
      </el-form-item>
    </el-form>
    <!-- 221px -->
    <el-table :empty-text="tableresult" stripe :data="tableData" border style="width: 100%">
      <el-table-column prop="id" label="id"> </el-table-column>
      <el-table-column prop="folder" label="資料夾"> </el-table-column>
      <el-table-column width="220" label="使用日期">
        <template slot-scope="scope">
          <span v-if="scope.row.combinedType == 0">{{ scope.row.combinedStartDate | splitDate }}~{{
              scope.row.combinedEndDate | splitDate
            }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column width="220" label="匯入日期">
        <template slot-scope="scope">
          <span v-if="scope.row.combinedType == 1">{{ scope.row.combinedStartDate | splitDate }}~{{
              scope.row.combinedEndDate | splitDate
            }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="isrc" label="ISRC(FW)">
        <template slot-scope="scope">
          {{ scope.row.isrc == 1 ? "Y" : "N" }}
        </template>
      </el-table-column>
      <el-table-column prop="userUniqueCode" label="User Unique Code">
        <template slot-scope="scope">
          {{ scope.row.userUniqueCode == 1 ? "Y" : "N" }}
        </template>
      </el-table-column>
      <el-table-column prop="amount" label="金額"> </el-table-column>
      <el-table-column prop="dataWeight" label="比重"> </el-table-column>
      <el-table-column prop="userUniqueCode" label="合併進度">
        <template slot-scope="scope">
          <span v-if="scope.row.status == -1">無數據</span>
          <span v-else-if="scope.row.status == 0">待合併</span>
          <span v-else-if="scope.row.status == 1">合併中</span>
          <span v-else-if="scope.row.status == 2">合併完成</span>
          <span v-else-if="scope.row.status == 3">已取消</span>
          <span v-else-if="scope.row.status == 4">合併失敗</span>
          <span v-else-if="scope.row.status == 5">待抽樣</span>
          <span v-else-if="scope.row.status == 6">抽樣中</span>
          <span v-else-if="scope.row.status == 7">抽樣完成</span>
          <span v-else-if="scope.row.status == 8">抽樣失敗</span>
        </template>
      </el-table-column>
      <el-table-column label="operation" width='175'>
        <template slot-scope="scope">
          <el-button v-if="scope.row.status == 0" type="text" @click="cancelAudit(scope.row.id)">取消</el-button>
          <el-button v-if="scope.row.status == -1||scope.row.status==8||scope.row.status==4||scope.row.status==3" type="text" @click="delteAudit(scope.row.id)">删除</el-button>
          <span v-if="scope.row.status == 2">
            <el-button type="text" @click="showAudit(scope.row)">抽樣</el-button>
            <el-button type="text" @click="showAudit2(scope.row)">导出合并</el-button>
          </span>
          <span v-if="scope.row.status==5||scope.row.status==6||scope.row.status==8">

            <el-button type="text" @click="showAudit2(scope.row)">导出合并</el-button>
          </span>
          <span v-if="scope.row.status == 7">
            <el-button type="text" @click="showAudit2(scope.row)">导出合并</el-button>
            <el-button type="text" @click="showAudit7(scope.row)">导出抽樣</el-button>
          </span>
          <!-- <span>-</span> -->
        </template>
      </el-table-column>
    </el-table>
    <el-pagination background layout="prev, pager, next" :total="total" :current-page.sync="currentpage" @current-change="handleCurrentChange">
    </el-pagination>

    <!-- 这个是预览 -->
    <el-dialog title="預覽" :visible.sync="exportVisible2" width="85%" @close="clearExportpriew" :close-on-click-modal="false">
      <div><span>共{{totalpriew}}筆,{{totalbi}}次</span></div>
      <!-- v-loadmore="handelLoadmore" -->
      <el-table :empty-text="tableresult" stripe :data="tableData2" height="300" border style="width: 100%">
        <el-table-column prop="channelName" label="頻道/節目名稱"></el-table-column>
        <el-table-column prop="episodeNo" label="集次">
        </el-table-column>
        <el-table-column prop="extJson" label="檔案名稱">
        </el-table-column>
        <el-table-column prop="performTime" label="使用日期">
        </el-table-column>
        <el-table-column prop="title" label="歌曲名稱">
        </el-table-column>
        <el-table-column prop="artists" label="原演唱者/原演奏者">
        </el-table-column>
        <el-table-column prop="musicLang" label="語別">
        </el-table-column>
        <el-table-column prop="authors" label="作詞者"></el-table-column>
        <el-table-column prop="composers" label="作曲者"></el-table-column>
        <el-table-column prop="publisher" label="發行公司"></el-table-column>
        <el-table-column prop="clickNumber" label="播放次數"></el-table-column>
        <el-table-column prop="null" label="音樂來源"></el-table-column>
        <el-table-column prop="uploadType" label="音樂類型"></el-table-column>
        <el-table-column prop="durationStr" label="播放長度"></el-table-column>
        <el-table-column prop="null" label="其它"></el-table-column>
        <el-table-column prop="id" label="清單編號"></el-table-column>
      </el-table>
      <!-- <el-pagination background layout="prev, pager, next" :total="total" @current-change="handleCurrentChange">
      </el-pagination> -->
    </el-dialog>
    <!--        导出报告sheet查询-->
    <el-dialog :title="exprotTitle" :visible.sync="exportVisible" width="500px" @close="clearExport" :close-on-click-modal="false">
      <el-form :model="exportForm" ref="exportForm" :rules="exportRules" :inline="false" label-width="140px" label-position="right">
        <el-form-item label="開始時間" prop="startDate">
          <date-picker v-model="exportForm.startDate" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd" style="width: 222px !important"></date-picker>
        </el-form-item>
        <el-form-item label="結束時間" prop="endDate">
          <date-picker v-model="exportForm.endDate" value-format="yyyy-MM-dd" format="yyyyMMdd" style="width: 222px !important"></date-picker>
        </el-form-item>
        <!--                cut sheet的资料夹为非必填。设置不同的key触发虚拟dom重新渲染，否则dom不渲染表单校验仍然为旧的规则-->
        <el-form-item label="資料夾" v-if="formType == 1" key="1">
          <el-autocomplete class="inline-input" v-model="exportForm.folder" :fetch-suggestions="queryFolderSearch" placeholder="请输入内容" :trigger-on-focus="false" value-key="folder" @select="handleFolderSelect" style="width: 222px"></el-autocomplete>
        </el-form-item>
        <el-form-item label="資料夾" prop="folder" v-else-if="formType == 3" key="2">
          <el-autocomplete class="inline-input" v-model="exportForm.folder" :fetch-suggestions="queryFolderSearch" placeholder="请输入内容" :trigger-on-focus="false" value-key="folder" @select="handleFolderSelect" style="width: 222px"></el-autocomplete>
        </el-form-item>
        <el-form-item label="categoryCode" v-if="formType == 1">
          <el-input v-model="exportForm.categoryCode" style="width: 222px"></el-input>
        </el-form-item>
        <el-form-item label="歌曲名稱" prop="titleType" v-if="formType == 1">
          <el-select v-model="exportForm.titleType" placeholder="请选择">
            <el-option label="全部" :value="0"></el-option>
            <el-option label="空白" :value="1"></el-option>
            <el-option label="非空白" :value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="音樂類型" prop="musicType" v-if="formType != 2">
          <el-select v-model="exportForm.musicType" placeholder="请选择">
            <el-option label="全部" value=""></el-option>
            <el-option label="PG" value="PG"></el-option>
            <el-option label="MS" value="MS"></el-option>
            <el-option label="FW" value="FW"></el-option>
            <el-option label="CJ" value="CJ"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="日期類型" prop="type">
          <el-select v-model="exportForm.type" @blur.native.capture="blurevent()" @change="selectvolidata()" placeholder="请选择">
            <el-option label="使用日期" :value="0"></el-option>
            <el-option label="匯入日期" :value="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="资料来源" prop="folderResource" v-if="formType == 3">
          <el-radio-group v-model="exportForm.folderResource">
            <el-radio :label="0">原始資料</el-radio>
            <el-radio :label="1">下過比重條件後的資料</el-radio>
            <el-radio :label="2">下過sampleData后的資料</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="節目名稱" prop="channelName" v-if="formType == 1">
          <el-input v-model="exportForm.channelName" style="width: 222px"></el-input>
        </el-form-item>
        <el-form-item label="集次" prop="episodeNo" v-if="formType == 1">
          <el-input v-model="exportForm.episodeNo" style="width: 222px"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="formType == 1" @click=preview()>预 览</el-button>
        <el-button @click="exportVisible = false">取 消</el-button>
        <el-button type="primary" @click="exportSheet">生成報表</el-button>
      </div>
    </el-dialog>
    <el-dialog title="新增" :visible.sync="createVisible" width="35%" :close-on-click-modal="false">
      <el-form :model="addForm" ref="addForm" :inline="true" :rules="createRules" label-width="207px" label-position="right">
        <el-form-item style="margin-left:72px">
          <el-select v-model="addForm.combinedType" placeholder="请选择" style="width: 120px">
            <el-option label="匯入日期" :value="1"></el-option>
            <el-option label="使用日期" :value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-form-item prop="combinedStartDate">
            <date-picker v-model="addForm.combinedStartDate" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd" style="width: 130px !important"></date-picker>
          </el-form-item>
          <span>至</span>
          <el-form-item prop="combinedEndDate">
            <date-picker v-model="addForm.combinedEndDate" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd" style="width: 130px !important"></date-picker>
          </el-form-item>
        </el-form-item>
        <el-form-item label="資料夾" prop="folder">
          <span class="setwidth">
            <el-input v-model="addForm.folder"></el-input>
          </span>
        </el-form-item>
        <el-form-item label="增加FW合併報表欄位">
          <el-checkbox v-model="addForm.isrc" :true-label="1" :false-label="0">ISRC</el-checkbox>
          <el-checkbox v-model="addForm.userUniqueCode" :true-label="1" :false-label="0">USER UNIQUE CODE</el-checkbox>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="createVisible = false">取 消</el-button>
        <el-button type="primary" @click="save">Save</el-button>
      </div>
    </el-dialog>
    <el-dialog title="排序" :visible.sync="auditVisible" width="600px" :close-on-click-modal="false">
      <el-form :model="auditForm" ref="auditForm" :inline="true" :rules="auditRules" label-width="180px" label-position="right">
        <el-form-item label="分配金額" prop="amount">
          <el-input v-model="auditForm.amount" type="number"></el-input>
        </el-form-item>
        <el-form-item label="資料比重" prop="dataWeight">
          <el-input v-model="auditForm.dataWeight" type="number" oninput="if(value>10)value=10"></el-input>
        </el-form-item>
        <el-form-item label="資料夾" prop="folder">
          <el-input v-model="auditForm.folder" :disabled='true'></el-input>
        </el-form-item>
        <el-form-item label="音樂類型" prop="workType">
          <el-select v-model="auditForm.workType" placeholder="请选择">
            <el-option label="ALL" value=""></el-option>
            <el-option label="PG" value="PG"></el-option>
            <el-option label="MS" value="MS"></el-option>
            <el-option label="FW" value="FW"></el-option>
            <el-option label="CJ" value="CJ"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="增加FW合併報表欄位">
          <el-checkbox v-model="auditForm.isrc" :true-label="1" :false-label="0" :disabled='true'>ISRC</el-checkbox>
          <el-checkbox v-model="auditForm.userUniqueCode" :true-label="1" :false-label="0" :disabled='true'>USER UNIQUE CODE</el-checkbox>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="auditVisible = false">取 消</el-button>
        <el-button type="primary" @click="audit">生成排序報表</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import inputLimitNumber from "../../../utils/inputNumber";
export default {
  name: "index",
  data() {
    return {
      alltimer: null,
      searchForm: {
        listBasicFileCombinedTotal: {
          combinedType: "",
          folder: null,
        },
        page: {
          pageNum: 1,
          pageSize: 10,
        },
      },
      currentpage: 1,
      currentStartIndex: 0,
      currentEndIndex: 20,
      total: 0,
      totalpriew: 0,
      totalbi: 0,
      tableData: [], tableresult: ' ',
      // 创建清单
      createVisible: false,
      addForm: {
        combinedType: 1,
        folder: "",
      },
      createRules: {
        folder: [{ required: true, message: "請輸入資料夾", trigger: "blur" }],
        isrc: [{ required: true, message: "請輸入isrc", trigger: ['blur', 'change'] }],
        userUniqueCode: [{ required: true, message: "請輸入userUniqueCode", trigger: ['blur', 'change'] }],
        combinedStartDate: [
          { required: true, message: "請輸入開始時間", trigger: "blur" },
        ],
        combinedEndDate: [
          { required: true, message: "請輸入結束時間", trigger: "blur" },
        ],
      },
      // 抽樣
      auditVisible: false,
      auditForm: {
        isrc: 0,
        userUniqueCode: 0,
      },
      auditRules: {
        amount: [{ required: true, message: "請輸入", trigger: "blur" }],
        dataWeight: [{ required: true, message: "請輸入", trigger: "blur" }],
        folder: [{ required: true, message: "請輸入", trigger: "blur" }],
        workType: [{ required: true, message: "請輸入", trigger: "blur" }],
      },
      // 导出报告
      exprotTitle: "匯入資料",
      exportVisible: false,
      exportVisible2: false,
      tableData2: [],
      formType: 1,
      exportForm: {
        folderResource: 0,
        uploadType: "",
        folder: "",
        categoryCode: "",
        type: ''
      },
      exportRules: {
        startDate: [{ required: true, message: "請輸入", trigger: "blur" }],
        type: [{ required: true, message: "請選擇", trigger: ['blur', 'change'] }],
        endDate: [{ required: true, message: "請輸入", trigger: "blur" }],
        folder: [{ required: true, message: "請輸入", trigger: "blur" }],
        categoryCode: [{ required: true, message: "請輸入", trigger: "blur" }],
        // type: [{ required: true, message: "請選擇", trigger: "blur" }],
      },
    };
  },
  computed: {
    filteredData() {
      return this.tableData.filter((item, index) => {
        if (index < this.currentStartIndex) {
          return false;
        } else if (index > this.currentEndIndex) {
          return false;
        } else {
          return true;
        }
      });
    }
  },
  mounted() {
    this.getList();
  },
  methods: {
    handelLoadmore(currentStartIndex, currentEndIndex) {
      this.currentStartIndex = currentStartIndex;
      this.currentEndIndex = currentEndIndex;
    },
    getTableData() {
      let cont = 0;
      let tableData = [];
      while (cont < 30000) {
        cont = cont + 1;
        let object = {
          date: cont,
          name: '王小虎' + cont,
          address: '上海市普陀区金沙江路 cont 弄'
        }
        tableData.push(object);
      }
      setTimeout(() => {
        this.tableData = tableData;
      }, 2000);
    },
    limitNumber(e) {
      return inputLimitNumber(e);
    },
    initList() {
      this.searchForm = {
        listBasicFileCombinedTotal: {
          combinedType: "",
          folder: null,
        },
        page: {
          pageNum: 1,
          pageSize: 10,
        },
      };
      this.getList();
    },
    getList(page = 1) {
      this.searchForm.page.pageNum = page;

      if (page == 1) {
        this.total = 0;
      }
      console.log(this.searchForm.listBasicFileCombinedTotal);
      // console.warn('hahah',this.searchForm.listBasicFileCombinedTotal.combinedStartDate.substr(-8))
      if (
        this.searchForm.listBasicFileCombinedTotal.combinedStartDate &&
        this.searchForm.listBasicFileCombinedTotal.combinedStartDate.substr(
          -8
        ) != "00:00:00"
      ) {
        if (this.searchForm.listBasicFileCombinedTotal.combinedStartDate) {
          var startDate = this.searchForm.listBasicFileCombinedTotal
            .combinedStartDate;
          this.searchForm.listBasicFileCombinedTotal.combinedStartDate =
            startDate + " 00:00:00";
        }
        if (this.searchForm.listBasicFileCombinedTotal.combinedEndDate) {
          var endDate = this.searchForm.listBasicFileCombinedTotal
            .combinedEndDate;
          this.searchForm.listBasicFileCombinedTotal.combinedEndDate =
            endDate + " 23:59:59";
        }
      }
      this.tableresult = '數據加載中...'
      this.$http
        .post("/listBasicFileCombinedTotal/list", this.searchForm)
        .then((res) => {
          // console.log(res);
          if (res.success) {
            this.tableData = res.data.data.list;
            this.total = res.data.data.total;
          }
          this.tableresult = this.tableData.length == 0 ? '暫無數據' : ' '
        });
    },
    addList() {
      this.createVisible = true;
      this.addForm = {
        combinedType: 1,
        folder: "",
        isrc: 0,
        userUniqueCode: 0,

      };
      if (this.$refs['addForm']) {
        this.$refs["addForm"].resetFields();
      }
    },
    save() {
      this.$refs.addForm.validate((validate) => {
        if (validate) {
          if (this.addForm.combinedStartDate) {
            this.addForm.combinedStartDate += " 00:00:00";
          }
          if (this.addForm.combinedEndDate) {
            this.addForm.combinedEndDate += " 23:59:59";
          }
          this.$http.post("/listBasicFileCombinedTotal/add", this.addForm).then((res) => {
            console.log(res);
            if (res.data.code == 200) {
              this.createVisible = false;
              this.initList();
              this.getList();
            } else {
              this.$toast({ tips: res.data.message });
            }
          });
        }
      });
    },
    cancelAudit(id) {
      this.$alert("確定要取消嗎？", "警告", {
        confirmButtonText: "确定",
        showCancelButton: true,
        cancelButtonText: "取消",
        callback: (action) => {
          if (action == "confirm") {
            this.$http
              .post("/listBasicFileCombinedTotal/cancel/" + id)
              .then((res) => {
                if (res.data.code == 200) {
                  this.getList();
                } else {
                  this.$toast({ tips: res.data.message });
                }
              });
          }
        },
      });
    },
    delteAudit(id) {
      this.$alert("確定要删除嗎？", "警告", {
        confirmButtonText: "确定",
        showCancelButton: true,
        cancelButtonText: "取消",
        callback: (action) => {
          if (action == "confirm") {
            this.$http.delete("/listBasicFileCombinedTotal/delete/" + id).then((res) => {
              if (res.data.code == 200) {
                this.getList(this.currentpage);
              } else {
                this.$toast({ tips: res.data.message });
              }
            });
          }
        },
      });
    },
    showAudit(row) {
      console.log('抽樣', row);
      this.auditForm = this.$utils.copy(row);
      this.auditVisible = true;
      this.$refs.auditForm && this.$refs.auditForm.clearValidate();
    },
    showAudit2(row) {
      // alert('22222222222')
      let params = {
        id: row.id,
      };
      this.$utils.downloadGet("/listBasicFileCombinedTotal/exportByMerge", params)
    },
    showAudit7(row) {
      console.log("====", row);
      let params = {
        id: row.id,
      };
      let titile = ''
      let formdata =
        this.$utils.downloadGet("/listBasicFileCombinedTotal/exportBysample", params)
    },
    audit() {
      this.$refs.auditForm.validate((validate) => {
        if (validate) {
          this.$http
            .post(
              "/listBasicFileCombinedTotal/generateSortReport",
              this.auditForm
            )
            .then((res) => {
              console.log(res);
              if (res.data.code == 200) {
                this.auditVisible = false;
                this.getList();
              } else {
                this.$toast({ tips: res.data.message });
              }
            });
        }
      });
    },
    handleCurrentChange(val) {
      this.currentpage = val
      this.getList(val);
    },
    clearExport() {
      this.exportForm = {
        folderResource: 0,
        uploadType: "",
        folder: "",
        categoryCode: "",
      };
    },
    clearExportpriew() {
      clearInterval(this.alltimer)
      this.exportVisible2 = false
      this.tableData2 = []
      this.totalpriew = 0
      this.totalbi = 0
    },
    showExport(title, type) {
      this.exportVisible = true;
      this.exprotTitle = title;
      this.formType = type;
      this.$refs.exportForm && this.$refs.exportForm.clearValidate();
    },
    validateCueSheet() {
      let folder = this.exportForm.folder;
      let categoryCode = this.exportForm.categoryCode;
      let res = true;
      if (!folder && !categoryCode) {
        this.$toast({ tips: "資料夾和categoryCode至少填写一个" });
        res = false;
      }
      return res;
    },
    preview() {
      // console.warn('***',this.exportForm)
      // let {
      //     categoryCode:categoryCode,
      //     channelName:channelName,
      //     endDate:endDate,
      //     episodeNo:episodeNo,
      //     folder:folder,
      //     musicType:musicType,
      //     startDate:startDate,
      //     titleType:titleType,
      //     type:type,
      // }=this.exportForm
      // let params={
      //     categoryCode:categoryCode,
      //     channelName:channelName,
      //     endDate:endDate,
      //     episodeNo:episodeNo,
      //     folder:folder,
      //     musicType:musicType,
      //     startDate:startDate,
      //     titleType:titleType,
      //     type:type,
      // }
      // console.warn('=====',params,'-------',this.exportForm)
      // this.$http
      //     .post("/list/export/previewListBasicFileDataMapping", params)
      //     .then((res) => {
      //     })

      this.tableData = []
      this.totalpriew = 0
      this.totalbi = 0
      this.$refs.exportForm.validate((validate) => {
        if (validate) {
          if (this.formType == 1) {
            // let canSubmit = this.validateCueSheet();
            // if (!canSubmit) {
            //   return false;
            // }
          }
          // 校验cut sheetcategory或者folder是否填写一个 exportVisible
          let params = this.$utils.copy(this.exportForm);
          let url = "/list/export/previewListBasicFileDataMapping";

          if (params.startDate) {
            params.startDate = params.startDate + " 00:00:00";
          }
          //调整时间
          if (params.endDate) {
            params.endDate = params.endDate + " 23:59:59";
          }

          let formdata = new FormData();
          Object.keys(params).map((key) => {
            formdata.append(key, params[key]);
          });
          this.tableresult = '數據加載中...'
          // this.tableresult = this.tableData.length == 0 ? '暫無數據' : ' '
          this.$http
            .post(url, formdata)
            .then((res) => {
              this.exportVisible2 = true
              if(res.data.data){
                if(res.data.data.length > 100){
                  this.tableData2 = res.data.data.list.slice(0, 100)
                } else {
                  this.tableData2 = res.data.data.list
                }
              }else {
                this.tableresult = '暫無數據'
                return;
              }

              this.totalpriew = res.data.data.total
              this.totalbi = res.data.data.totalClickNumber
              //           let cont = 0;
              // let tableData = [];
              // while (cont < 10000) {
              //   cont = cont + 100;

              //   tableData=tableData.concat(res.data.data.list.slice(cont-100,cont));
              // }
              // setTimeout(() => {
              //   this.tableData2 = tableData;
              //   console.log('$$$',this.this.tableData2)
              // }, 500);
              let cont = 0
              var timer = setInterval(() => {
                cont = cont + 500
                if (cont == 500) {
                  this.tableData2 = res.data.data.list.slice(0, cont)
                } else {
                  this.tableData2 = this.tableData2.concat(res.data.data.list.slice(cont - 100, cont))
                }
                this.alltimer = timer
                if (cont == 10000) {
                  // alert('*****')
                  clearInterval(timer)
                  // this.tableData2=res.data.data.list
                }
                // console.log('%%',cont,this.tableData2)
              }, 200);
              // this.tableresult = '數據加載中...'
              // console.warn("********", res, '++++', this.tableData2);
            });
        }
      });


    },
    selectvolidata() {
      this.$refs.exportForm.validate((validate) => {
        //  console.log('---------',validate)
      })
    },
    blurevent() {
      //  this.$refs.exportForm.validate((validate) => {
      //    console.log('++++++',validate)
      //  })

    },
    exportSheet() {
      this.$refs.exportForm.validate((validate) => {
        if (validate) {
          if (this.formType == 1) {
            let canSubmit = this.validateCueSheet();
            if (!canSubmit) {
              return false;
            }
          }
          // 校验cut sheetcategory或者folder是否填写一个
          let params = this.$utils.copy(this.exportForm);
          let url = "";
          if (this.formType == 1) {
            url = "/list/export/exportListBasicFileDataMapping";
          } else if (this.formType == 2) {
            url = "/list/export/exportListBaiscBaseData";
          } else {
            this.exportForm.uploadType = this.exportForm.musicType;
            url = "/list/export/exportListCombinedStatData";
          }
          if (params.startDate) {
            params.startDate = params.startDate + " 00:00:00";
          }
          //调整时间
          if (params.endDate) {
            params.endDate = params.endDate + " 23:59:59";
          }

          let formdata = new FormData();
          Object.keys(params).map((key) => {
            formdata.append(key, params[key]);
          });
          this.$http.post(url, formdata, { responseType: "blob" }).then((res) => {
            // console.warn("********", res);

            let tempBlob = new Blob([res.data], { type: 'application/json' })
            // 通过 FileReader 读取这个 blob
            let reader = new FileReader()
            reader.onload = e => {
              let res1 = e.target.result
              // 此处对fileReader读出的结果进行JSON解析
              // 可能会出现错误，需要进行捕获
              try {
                let json = JSON.parse(res1)
                this.$toast({ tips: json.message })
                // console.log('========',json)
                //正常json数据格式
              } catch (err) {
                // console.log('&&&&&&&&',err)
                // 该异常为无法将字符串转为json
                // 说明返回的数据是一个流文件
                // 不需要处理该异常，只需要捕获即刻
                this.$utils.downloadByBlob(res.data, res.headers["content-disposition"]);

              }
            }
            // 将blob对象以文本的方式读出，读出完成后将会执行 onload 方法
            reader.readAsText(tempBlob)



            // let title=decodeURI(decodeURI(res.headers["content-disposition"] ? res.headers["content-disposition"].split('=')[1] : '作品列表'))
            // if (res.headers["content-disposition"]) {
            //   this.$utils.downloadByBlob(res.data, res.headers["content-disposition"]);
            // }else{
            //   this.$toast({ tips: '生成報表失敗!' })

            // }
          });
        }
      });
    },
    queryFolderSearch(str, cb) {
      let params = {
        listBasicFileCombinedTotal: {
          combinedType: "",
          folder: str,
        },
        page: {
          pageNum: 1,
          pageSize: 10,
        },
      };
      this.$http
        .post("/listBasicFileCombinedTotal/list", params)
        .then((res) => {
          if (res.success) {
            let prearr = res.data.data.list.filter((item, index, arr) =>
              (item.status == 2 || item.status == 5 || item.status == 6 || item.status == 7 || item.status == 8)
            )
            let temparr0 = []
            let initarr = prearr.map((item, index, arr) => item.folder)
            initarr.map((item0, index0, arr0) => {
              if (arr0.indexOf(item0) === index0) {
                temparr0.push(index0)
              }
            })
            let temparr = []
            temparr0.forEach((item, index, arr) => temparr.push(prearr[item]))
            // console.warn(")))))",initarr,'))))',temparr0,temparr)
            cb(temparr
            );
          }
        });
    },
    handleFolderSelect(val) {
      this.exportForm.folder = val.folder;
      // console.warn('***',val,'===',this.exportForm.folder)
    },
  },
};
</script>

<style scoped lang="scss">
/deep/ .el-form-item__label {
  margin-left: 0;
}
/deep/ .el-radio {
  margin-left: 30px;
  line-height: 30px;
}
.setwidth .el-input--medium /deep/ .el-input__inner {
  width: 295px !important;
}
</style>
