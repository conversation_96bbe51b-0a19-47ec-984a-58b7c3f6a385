<template>
  <div>
    <!-- 表单区域 - 横铺在顶部 -->
    <div class="form-section">
      <el-form :model="formData" class="horizontal-form" label-position="left" label-width="160px" ref="form" :rules="rules">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="Name" class="w-92">
              <el-input type="text" v-model="formData.name"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="Year" prop="year" class="w-92">
              <el-input v-model.number="formData.year" @input="onYearChange"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="分類代碼" prop="categoryCode">
              <el-input type="text" v-model="formData.categoryCode" ref="category" placeholder="雙擊選擇" @dblclick.native="addCategory"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="日期範圍" class="w-92" required>
              <el-form-item prop="startDate" style="width:181px;display: inline-block">
                <el-input v-model="formData.startDate" readonly style="width:200px" placeholder="start"></el-input>
              </el-form-item>
              <span>-</span>
              <el-form-item prop="endDate" style="width:181px;display: inline-block">
                <el-input v-model="formData.endDate" readonly style="width:200px" placeholder="end"></el-input>
              </el-form-item>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="Frequency" class="w-92" required>
              <el-form-item prop="dayExtracted" style="width:100px;display: inline-block">
                <el-input type="number" v-model.number="formData.dayExtracted" class="w-100"></el-input>
              </el-form-item>
              <span>/</span>
              <el-form-item prop="dayBase" style="width:100px;display: inline-block">
                <el-input type="number" v-model.number="formData.dayBase" class="w-100"></el-input>
              </el-form-item>
              days
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <div class="form-buttons">
              <el-tooltip
                :content="enableCustomEdit ? '請關閉自定義編輯後重試' : ''"
                :disabled="!enableCustomEdit"
                placement="top">
                <el-button
                  type="primary"
                  :disabled="enableCustomEdit"
                  @click="saveData">
                    重新生成(隨機日期)
                </el-button>
              </el-tooltip>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- 自定义编辑开关 -->
    <div class="custom-edit-switch">
      <el-switch
        v-model="enableCustomEdit"
        active-text=""
        inactive-text="自定義編輯"
      >
      </el-switch>
    </div>

    <!-- 日历组件区域 - 并排放置 -->
    <div class="calendar-container">
      <!-- A组件：预览原有抽样规则日期（只读） -->
      <div class="calendar-section left-calendar">
        <h3>原有抽樣規則日期預覽</h3>
        <div class="date-box">
          <span>跳轉至指定月份</span>
          <el-select v-model="targetYearMonth" style="width:200px" placeholder="選擇月份" @change="chooseMonth">
            <el-option
              v-for="month in monthOptions"
              :key="month.value"
              :label="month.label"
              :value="month.value">
            </el-option>
          </el-select>
          <span v-if="enableCustomEdit" style="margin-left: 10px; color: #67c23a; font-size: 12px;">
            <i class="el-icon-link"></i> 與自定義編輯月份聯動
          </span>
        </div>
        <div class="readonly-calendar">
          <calendar ref="calendar" :markDate="markDate" @choseDay="previewChooseDay"></calendar>
        </div>
      </div>

      <!-- B组件：自定义编辑日期（可编辑） -->
      <div v-if="enableCustomEdit" class="calendar-section right-calendar">
        <h3>自定义编辑日期</h3>
        <div class="date-box">
          <span>跳轉至指定月份</span>
          <el-select v-model="customTargetYearMonth" style="width:200px" placeholder="選擇月份" @change="chooseCustomMonth">
            <el-option
              v-for="month in monthOptions"
              :key="month.value"
              :label="month.label"
              :value="month.value">
            </el-option>
          </el-select>
          <el-button type="success" @click="copyFromPreview" style="margin-left: 10px;">從預覽複製日期</el-button>
          <span style="margin-left: 10px; color: #67c23a; font-size: 12px;">
            <i class="el-icon-link"></i> 與預覽月份聯動
          </span>
        </div>
        <calendar ref="customCalendar" :markDate="customMarkDate" @choseDay="customChooseDay"></calendar>
        <div style="margin-top: 10px;">
          <el-button type="primary" @click="regenerateWithCustomDates">重新生成(自定義日期)</el-button>
          <el-button type="default" @click="clearCustomDates">清空選擇</el-button>
        </div>
      </div>
    </div>
    <el-dialog :visible.sync="moneyTableShow" :close-on-click-modal="false">
      <div style="width: 500px;margin: auto;">
        <el-form :inline="true" :model="addSearch" @keyup.enter.native='getCategoryList()'>
          <el-form-item label="">
            <el-input placeholder="請輸入Source Name" v-model="addSearch.sourceName" class="input-with-select">
              <el-button slot="append" icon="el-icon-search" @click="getCategoryList()"></el-button>
            </el-input>
          </el-form-item>
        </el-form>
      </div>
      <el-table :empty-text="tableresult" stripe :data="moneyTableData">
        <el-table-column property="id" label="ID"></el-table-column>
        <el-table-column property="sourceName" label="sourceName"></el-table-column>
        <el-table-column property="categoryCode" label="categoryCode"></el-table-column>
        <el-table-column property="categoryDesc" label="categoryDesc"></el-table-column>
        <el-table-column label="operation" width="120px">
          <template slot-scope="scope">
            <i class="el-icon-check pointer" @click="chooseCategory(scope.row)"></i>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background layout="prev, pager, next" :total=moneyTotal @current-change="getCategoryList">
      </el-pagination>
    </el-dialog>
  </div>
</template>

<script>
import Calendar from 'vue-calendar-component';
export default {
  data() {
    function Isyear(rule, str, callback) {
      // var reg =/^[1-2]\d{3}/;
      var reg = /^(19|20)\d{2}$/;
      // var reg =/[0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3}/;
      console.log('校驗------', str, reg.test(str));
      // var arr = reg.exec(str);
      //  if (str=="") return true;
      if (!reg.test(str)) {
        //  alert("请保证输入的日期格式为2019-01-01类型的正确日期!");
        //  callback(new Error('不能大於1000min或者小於0min！'))
        //  return false;
        callback(new Error('请输入合法年份!'))

      } else {

        callback()
      }
    }
    return {
      formData: {},
      markDate: [], // A组件的日期数据（只读预览）
      customMarkDate: [], // B组件的日期数据（可编辑）
      enableCustomEdit: false, // 自定义编辑开关
      isCopyingFromPreview: false, // 标志：是否正在从预览复制日期
      _syncingFromPreview: false, // 标志：是否正在从预览组件同步月份到自定义编辑组件
      moneyTableShow: false,
      addSearch: {},
      moneyTableData: [], tableresult: ' ',
      moneyTotal: 0,
      targetYear: '', // A组件的目标年份（保留用于日历组件）
      customTargetYear: '', // B组件的目标年份（保留用于日历组件）
      targetYearMonth: '', // A组件的目标年月
      customTargetYearMonth: '', // B组件的目标年月
      monthOptions: [], // 月份选项
      rules: {
        name: [
          { required: true, message: '请输入name', trigger: 'blur' }
        ],
        year: [
          // { required: true, message: '请输入合法年份', trigger: ['blur','change'], pattern:/^[1-2]\d{3}/ },
          { validator: Isyear, trigger: ['blur', 'change'] }
        ],
        categoryCode: [
          { required: true, message: '请输入categoryCode', trigger: 'blur' }
        ],
        startDate: [
          { required: true, message: '請輸入日期（例：20200101）', trigger: 'blur' }
        ],
        endDate: [
          { required: true, message: '請輸入日期（例：20200101）', trigger: 'blur' }
        ],
        dayExtracted: [
          { type: 'number', required: true, message: '请输入合法天数', trigger: 'blur', min: 1 }
        ],
        dayBase: [
          { type: 'number', required: true, message: '请输入31以內的整數', trigger: 'blur', max: 31 }
        ]
      }
    }
  },
  components: {
    Calendar
  },
  watch: {
    'formData.year': {
      handler(newYear) {
        if (newYear) {
          this.generateMonthOptions()
          this.updateDateRange()
        }
      },
      immediate: false
    },
    // 监听自定义编辑开关的变化
    enableCustomEdit: {
      handler(newValue, oldValue) {
        // 当从关闭状态切换到开启状态时，自动从预览复制日期
        if (newValue === true && oldValue === false) {
          this.$nextTick(() => {
            if (this.markDate && this.markDate.length > 0) {
              this.copyFromPreview()
            }
          })
        }
      },
      immediate: false
    },
    // 监听A组件（预览）的月份变化，同步到B组件（自定义编辑）
    targetYearMonth: {
      handler(newMonth, oldMonth) {
        // 避免初始化时的无效同步和无限循环
        if (newMonth && oldMonth && this.enableCustomEdit && this.customTargetYearMonth !== newMonth) {
          // 使用标志位避免循环触发
          this._syncingFromPreview = true
          this.customTargetYearMonth = newMonth
          this.$nextTick(() => {
            if (this.$refs.customCalendar) {
              const yearMonth = newMonth.substring(0, 7) // "2024-01-01" -> "2024-01"
              this.$refs.customCalendar.ChoseMonth(yearMonth + '-15')
              this.$refs.customCalendar.ChoseMonth(yearMonth + '-15')

            }
            this._syncingFromPreview = false
          })
        }
      },
      immediate: false
    },
    // 监听B组件（自定义编辑）的月份变化，同步到A组件（预览）
    customTargetYearMonth: {
      handler(newMonth, oldMonth) {
        // 避免初始化时的无效同步、从预览同步时的循环触发和无限循环
        if (newMonth && oldMonth && !this._syncingFromPreview && this.targetYearMonth !== newMonth) {
          this.targetYearMonth = newMonth
          this.$nextTick(() => {
            if (this.$refs.calendar) {
              const yearMonth = newMonth.substring(0, 7) // "2024-01-01" -> "2024-01"
              this.$refs.calendar.ChoseMonth(yearMonth + '-15')
              this.$refs.calendar.ChoseMonth(yearMonth + '-15');
            }
          })
        }
      },
      immediate: false
    }
  },
  methods: {
    init() {
      let row = JSON.parse(JSON.stringify(this.$route.params.row))
      this.formData = row

      // 初始化日期范围（如果有年份的话）
      if (this.formData.year) {
        this.updateDateRange()
      }

      // 初始化月份选项
      this.generateMonthOptions()

      // 初始化自定义编辑相关数据
      this.enableCustomEdit = false
      this.customMarkDate = []
      this.customTargetYear = ''
      this.customTargetYearMonth = ''

      // 检查是否有目标月份参数
      const targetMonth = this.$route.params.targetMonth

      if (row.id) {
        this.getDateList(row.id, targetMonth)
      } else {
        this.markDate = []
        this.targetYear = ''
        this.targetYearMonth = ''

        // 如果有目标月份，设置为默认显示月份
        if (targetMonth) {
          this.targetYearMonth = targetMonth
          this.targetYear = targetMonth
          this.chooseMonth()
        }
      }
    },

    // 生成月份选项（根据选中的年份）
    generateMonthOptions() {
      const options = []
      const year = this.formData.year || new Date().getFullYear()

      // 只生成选中年份的月份选项
      for (let month = 1; month <= 12; month++) {
        const monthStr = month.toString().padStart(2, '0')
        const value = `${year}-${monthStr}-01`
        const label = `${year}年${monthStr}月`
        options.push({ value, label })
      }

      this.monthOptions = options
    },

    // 年份变化时的处理
    onYearChange() {
      // 重新生成月份选项
      this.generateMonthOptions()

      // 自动设置日期范围为年初到年末
      this.updateDateRange()

      // 清空月份选择
      this.targetYearMonth = ''
      this.customTargetYearMonth = ''
    },

    // 更新日期范围为选中年份的年初到年末
    updateDateRange() {
      const year = this.formData.year
      if (year && year >= 1900 && year <= 2100) {
        this.formData.startDate = `${year}-01-01`
        this.formData.endDate = `${year}-12-31`
      }
    },
    formatTime(time) {
      let newTime = ''
      if (time.indexOf(' ') !== -1) {
        let arr = time.split(' ')
        newTime = arr[0]
      } else {
        newTime = time
      }
      return newTime
    },

    // 标准化日期格式，统一为 YYYY-MM-DD 格式
    normalizeDate(date) {
      if (!date) return ''

      // 如果已经是 YYYY-MM-DD 格式
      if (typeof date === 'string' && date.match(/^\d{4}-\d{2}-\d{2}$/)) {
        return date
      }

      // 如果是 YYYYMMDD 格式
      if (typeof date === 'string' && date.match(/^\d{8}$/)) {
        return `${date.substring(0, 4)}-${date.substring(4, 6)}-${date.substring(6, 8)}`
      }

      // 如果是 Date 对象
      if (date instanceof Date) {
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        return `${year}-${month}-${day}`
      }

      // 其他格式尝试转换
      try {
        const dateObj = new Date(date)
        if (!isNaN(dateObj.getTime())) {
          const year = dateObj.getFullYear()
          const month = String(dateObj.getMonth() + 1).padStart(2, '0')
          const day = String(dateObj.getDate()).padStart(2, '0')
          return `${year}-${month}-${day}`
        }
      } catch (e) {
        console.warn('无法解析日期格式:', date)
      }

      return String(date)
    },
    // A组件（预览）的日期选择 - 设为只读，不允许修改
    previewChooseDay(date) {
      // 预览组件不允许修改日期，只用于显示
      console.log('预览组件日期点击（只读）：', date)
      // 可以在这里添加提示用户使用自定义编辑功能
      if (!this.enableCustomEdit) {
        this.$message.info('開啟自定義編輯功能可以修改日期選擇')
      }
    },

    // B组件（自定义编辑）的日期选择
    customChooseDay(date) {
      // 如果正在从预览复制日期，忽略这次选择事件
      if (this.isCopyingFromPreview) {
        console.log('正在复制预览日期，忽略日期选择事件：', date)
        return
      }

      console.log('自定义编辑选中的日期：', this.customMarkDate)
      console.log("自定义编辑chosen data:", date)

      // 标准化日期格式进行比较
      const normalizedDate = this.normalizeDate(date)
      console.log('标准化后的日期：', normalizedDate)

      const index = this.customMarkDate.findIndex(d => {
        const normalizedExisting = this.normalizeDate(d)
        console.log(`比较: ${normalizedExisting} === ${normalizedDate}`, normalizedExisting === normalizedDate)
        return normalizedExisting === normalizedDate
      })

      console.log('找到的索引：', index)

      if (index === -1) {
        this.customMarkDate.push(normalizedDate);
        console.log('添加日期：', normalizedDate)
      } else {
        this.customMarkDate.splice(index, 1);
        console.log('移除日期：', normalizedDate)
      }
      this.customMarkDate = [...this.customMarkDate];
      console.log('更新后的日期数组：', this.customMarkDate)
      this.$forceUpdate();
    },

    chooseMonth(triggerSync = true) {
      if (this.targetYearMonth) {
        // 将选择的年月转换为日历组件需要的格式
        this.targetYear = this.targetYearMonth
        // 使用月中的日期（15号）避免自动选中1号
        const yearMonth = this.targetYearMonth.substring(0, 7) // "2024-01-01" -> "2024-01"
        this.$refs.calendar.ChoseMonth(yearMonth + '-15');
        this.$refs.calendar.ChoseMonth(yearMonth + '-15');


          // 如果启用了自定义编辑且需要触发同步，则同步到自定义编辑组件
        if (triggerSync && this.enableCustomEdit && this.customTargetYearMonth !== this.targetYearMonth) {
          this.customTargetYearMonth = this.targetYearMonth
          this.$nextTick(() => {
            if (this.$refs.customCalendar) {
              this.$refs.customCalendar.ChoseMonth(yearMonth + '-15')
              this.$refs.customCalendar.ChoseMonth(yearMonth + '-15')

            }
          })
        }
      }
    },

    chooseCustomMonth(triggerSync = true) {
      if (this.customTargetYearMonth) {
        // 将选择的年月转换为日历组件需要的格式
        this.customTargetYear = this.customTargetYearMonth

        const yearMonth = this.customTargetYearMonth.substring(0, 7) // "2024-01-01" -> "2024-01"
        this.$refs.customCalendar.ChoseMonth(yearMonth + '-15');
        this.$refs.customCalendar.ChoseMonth(yearMonth + '-15');

        // 如果需要触发同步，则同步到预览组件
        if (triggerSync && this.targetYearMonth !== this.customTargetYearMonth) {
          this.targetYearMonth = this.customTargetYearMonth
          this.$nextTick(() => {
            if (this.$refs.calendar) {
              this.$refs.calendar.ChoseMonth(yearMonth + '-15')
              this.$refs.calendar.ChoseMonth(yearMonth + '-15');
            }
          })
        }
      }
    },
    addCategory() {
      this.moneyTableShow = true;
      this.getCategoryList(1);
    },
    getCategoryList(page) {
      let data = this.$utils.copy(this.addSearch)
      data.page_size = 10
      data.page_num = page ? page : 1
      this.tableresult = '數據加載中...'
      this.$http.get('/list/categorys', { params: data }).then(res => {
        if (res.success) {
          this.moneyTableData = res.data.data.list
          this.moneyTotal = res.data.data.total
        }
        this.tableresult = this.moneyTableData.length == 0 ? '暫無數據' : ' '
      })
    },
    chooseCategory(row) {
      this.$set(this.formData, 'categoryCode', row.categoryCode)
      this.moneyTableShow = false
      this.$refs.category.focus()
      this.$refs.category.blur()
    },

    // 从预览组件复制日期到自定义编辑组件
    copyFromPreview() {
      // 标准化所有日期格式后复制
      const copiedDates = this.markDate.map(date => this.normalizeDate(date))

      // 设置一个标志，表示正在进行复制操作，避免customChooseDay被意外触发
      this.isCopyingFromPreview = true

      this.customMarkDate = copiedDates
      this.customTargetYear = this.targetYear
      this.customTargetYearMonth = this.targetYearMonth
      this.$message.success(`已從預覽複製 ${this.customMarkDate.length} 個日期到自定義編輯`)

      // 同步自定义日历的显示月份
      this.$nextTick(() => {
        if (this.customTargetYearMonth) {
          // 使用chooseCustomMonth方法，但不触发同步避免循环
          this.chooseCustomMonth(false)
        }

        // 延迟重置标志，确保ChoseMonth操作完成
        setTimeout(() => {
          this.isCopyingFromPreview = false
        }, 100)
      })
    },



    // 清空自定义选择的日期
    clearCustomDates() {
      this.customMarkDate = []
      this.$message.success('已清空自定义选择的日期')
    },

    // 使用自定义日期重新生成
    regenerateWithCustomDates() {
      if (this.customMarkDate.length === 0) {
        this.$message.warning('请先选择自定义日期')
        return
      }

      if (!this.formData.id) {
        this.$message.error('缺少抽样规则ID，无法更新')
        return
      }

      console.log('准备提交的自定义日期：', this.customMarkDate)

      this.$confirm(`確認使用選中的 ${this.customMarkDate.length} 個日期重新生成嗎？`, '确认操作', {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 调用后端接口更新自定义日期
        this.callUpdateSampleDatesApi()
      }).catch(() => {
        this.$message.info('已取消操作')
      })
    },

    // 调用后端接口更新抽样日期
    callUpdateSampleDatesApi() {
      // 构造请求参数，符合后端 List2CustomSampleDatesVo 的结构
      const requestData = {
        sampleRuleBaseId: parseInt(this.formData.id), // 确保是数字类型
        customSampleDates: this.customMarkDate.map(dateStr => {
          // 将日期字符串转换为 Date 对象
          // 确保日期格式正确 (YYYY-MM-DD)
          const normalizedDate = this.normalizeDate(dateStr)
          // 创建Date对象，使用本地时间避免时区问题
          const dateObj = new Date(normalizedDate + 'T00:00:00')
          return dateObj.toISOString() // 转换为ISO字符串格式
        })
      }

      console.log('发送到后端的请求数据：', requestData)

      // 显示加载状态
      const loading = this.$loading({
        lock: true,
        text: '正在更新抽样日期...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      // 调用后端接口
      this.$http.post('/list2SampleRule/updateSampleDates', requestData).then(res => {
        loading.close()
        console.log('后端响应：', res)

        if (res.success) {
          this.$message({
            message: '自定义日期更新成功',
            type: 'success',
            duration: 2000,
            onClose: () => {
              // 更新成功后的操作
              this.handleUpdateSuccess()
            }
          })
        } else {
          this.$message.error(res.message || res.data || '更新失败')
        }
      }).catch(err => {
        loading.close()
        console.error('接口调用失败：', err)
        this.$message.error('请求失败：' + (err.message || '网络错误'))
      })
    },

    // 更新成功后的处理
    handleUpdateSuccess() {
      // 关闭当前标签页并返回列表页面
      this.$bus.$emit('closeCurrentTab', () => {
        this.$router.push({ name: 'normalListSampleDate', query: { update: true } });
      })

      // 备选方案：如果不需要关闭标签页，直接跳转
      // this.$router.push({ name: 'normalListSampleDate', query: { update: true } });

      // 其他可选操作：
      // 选项1：刷新当前页面数据（如果不跳转的话）
      // if (this.formData.id) {
      //   this.getDateList(this.formData.id)
      // }

      // 选项2：重置自定义编辑状态（如果不跳转的话）
      // this.enableCustomEdit = false
      // this.customMarkDate = []
      // this.customTargetYear = ''
    },

    saveData() {
      // 如果启用了自定义编辑，使用自定义日期重新生成
      if (this.enableCustomEdit) {
          console.log('使用自定义日期重新生成')
        this.regenerateWithCustomDates()
        return
      }

      // 原有的保存逻辑
      this.$refs.form.validate(validate => {
        if (validate) {
          if (this.formData.startDate.split('-')[0] != this.formData.year || this.formData.endDate.split('-')[0] != this.formData.year) {
            this.$toast({ tips: '日期範圍和抽樣年份不一致!' })
            return
          }

          if (this.formData.dayExtracted > this.formData.dayBase) {
            this.$toast({ tips: '抽樣天數不能大於總天數' })
            return
          }
          if (this.formData.startDate > this.formData.endDate) {
            this.$toast({ tips: '開始時間不能大於結束時間,請重新填寫!' })
            return
          }

          this.$http.put('/list2SampleRule/updateListSampleRuleBase', this.formData).then(res => {
            console.log(res)
            if (res.success && res.data.indexOf('成功') !== -1) {
              this.$message({
                message: '保存成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.$bus.$emit('closeCurrentTab', () => {
                    this.$router.push({ name: 'normalListSampleDate', query: { update: true } });
                  })

                }
              })

            } else {
              this.$message.error(res.data)
            }
          })
        }
      })
    },
    getDateList(id, targetMonth = null) {
      this.$http.get('/list2SampleRule/getListSampleRuleBaseById', { params: { baseId: id } }).then(res => {
        console.log("result of DateList:",res)
        let list = res.data.listSampleRuleMappingList
        if (list.length) {
          // 清空现有数据
          this.markDate = []
          list.map(item => {
            console.log('item:',item.sampleDate)
            // 使用标准化的日期格式
            const formattedDate = this.normalizeDate(this.formatTime(item.sampleDate))
            this.markDate.push(formattedDate)
          })

          // 如果有目标月份参数，优先使用目标月份
          if (targetMonth) {
            this.targetYearMonth = targetMonth
            this.targetYear = targetMonth
            console.log('设置目标月份:', targetMonth)
          } else {
            // 否则使用第一个抽样日期的月份
            let date = list[0].sampleDate
            date = date.split(' ')[0]
            this.targetYear = date

            // 设置月份选择器的值（取年月部分，日期设为01）
            if (date) {
              const dateParts = date.split('-')
              if (dateParts.length >= 2) {
                this.targetYearMonth = `${dateParts[0]}-${dateParts[1]}-01`
              }
            }
          }

          this.chooseMonth()
        } else {
          this.markDate = []

          // 即使没有抽样日期，如果有目标月份也要显示
          if (targetMonth) {
            this.targetYearMonth = targetMonth
            this.targetYear = targetMonth
            this.chooseMonth()
          }
        }
      })
    }

  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      vm.init()
    })
  }
}
</script>
<style lang="scss" scoped>
@import "../../../../assets/scss/works.scss";
/deep/ .el-date-editor .el-range-separator {
  width: 6%;
}
.w-100 {
  width: 100px;
}
/deep/ .w-92 {
  label {
    width: 92px;
  }
}

/deep/ .wh_content_all {
  // background: #eee;
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 6px;
}
/deep/ .wh_isToday {
  background: #fff !important;
}
/deep/ .wh_item_date:hover {
  background: #17b3a3 !important;
  border-radius: 50%;
}
/deep/ .wh_content_item,
/deep/ .wh_top_changge li {
  color: #333;
}
/deep/ .wh_jiantou2,
/deep/ .wh_jiantou1 {
  border-color: #333 !important;
}
/deep/ .wh_chose_day {
  background: #fff !important;
}
/* 选中的日期的颜色*/
/deep/ .wh_content_item > .wh_isMark {
  background: #71c7a5 !important;
}

/* 除当月意外的日期隐藏 */
/deep/ .wh_other_dayhide {
  display: none;
}
.date-box {
  padding-bottom: 20px;
}

/* 表单区域样式 */
.form-section {
  background-color: #f8f9fa;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 20px;
  margin: 20px;
  margin-bottom: 20px;
}

.horizontal-form {
  width: 100%;
}

.horizontal-form .el-form-item__label {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.form-buttons {
  display: flex;
  align-items: center;
  height: 40px;
}

/* 自定义编辑开关样式 */
.custom-edit-switch {
  text-align: center;
  padding: 15px;
  background-color: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 6px;
  margin: 0 20px 20px 20px;
}

/* 日历容器样式 */
.calendar-container {
  display: flex;
  gap: 20px;
  margin: 0 20px;
  align-items: flex-start;
}

/* 日历区域样式 */
.calendar-section {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 20px;
  background-color: #fafafa;
  flex: 1;
  min-width: 0; /* 防止flex子项溢出 */
}

.calendar-section h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.custom-edit-switch /deep/ .el-switch__label {
  font-weight: 500;
  color: #333;
}

/* 预览组件特殊样式 */
.left-calendar {
  background-color: #f9f9f9;
  border-color: #d3d3d3;
}

.left-calendar h3 {
  color: #666;
}

/* 自定义编辑组件样式 */
.right-calendar {
  background-color: #f0f9ff;
  border-color: #409eff;
}

.right-calendar h3 {
  color: #409eff;
}

/* 只读日历样式 */
.readonly-calendar {
  position: relative;
}

.readonly-calendar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.3);
  pointer-events: none;
  z-index: 1;
}

.readonly-calendar /deep/ .wh_item_date {
  cursor: not-allowed !important;
}

.readonly-calendar /deep/ .wh_item_date:hover {
  background: transparent !important;
}

.readonly-calendar /deep/ .wh_content_item {
  opacity: 0.7;
}

/deep/ .el-form-item__label {
  margin-left: 0;
}
.line {
  border-bottom: 0 none;
  text-align: center;
  margin-right: 13px;
}

/* 响应式样式 */
@media (max-width: 1200px) {
  .calendar-container {
    flex-direction: column;
  }

  .calendar-section {
    width: 100%;
  }
}

/* 确保日历组件在并排布局中的宽度 */
.calendar-container .calendar-section {
  max-width: calc(50% - 10px);
}

@media (max-width: 1200px) {
  .calendar-container .calendar-section {
    max-width: 100%;
  }
}
</style>
