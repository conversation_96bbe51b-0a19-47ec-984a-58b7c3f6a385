<template>
    <el-dialog
        :title="!dataForm.id ? '新增' : '修改'"
        :close-on-click-modal="false"
        :visible.sync="visible"
        :before-close='channelVisible'>
        <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
            <el-form-item label="角色名稱" prop="name">
                <el-input v-model="dataForm.name" placeholder="角色名稱"></el-input>
            </el-form-item>
            <el-form-item label="備註" prop="remark">
                <el-input v-model="dataForm.remark" placeholder="備註"></el-input>
            </el-form-item>
            <el-form-item size="mini" label="權限">
                <el-tree
                    :data="menuList"
                    :props="menuListTreeProps"
                    node-key="id"
                    ref="menuListTree"
                    :default-expand-all="true"
                    :show-checkbox="true"
                    style="max-height: 400px; overflow: auto;"
                    >
                </el-tree>
            </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
            <el-button type="warning" @click="channelVisible()">取消</el-button>
            <el-button type="primary" @click="dataFormSubmit()">確定</el-button>
        </span>
    </el-dialog>
</template>

<script>
    import { treeDataTranslate } from '@/utils'
    export default {
        data () {
            return {
                visible: false,
                menuList: [],
                menuListTreeProps: {
                    label: 'name',
                    children: 'childs'
                },
                dataForm: {
                    id: 0,
                    name: '',
                    remark: ''
                },
                dataRule: {
                    name: [
                        { required: true, message: '角色名稱不能為空', trigger: 'blur' }
                    ]
                },
                tempKey: -666666, // 临时key, 用于解决tree半选中狀態項不能传给後台接口问题. # 待优化
                addNext:true,
            }
        },
        methods: {
            channelVisible(){
                this.visible = false
                this.$refs['dataForm'].resetFields();
            },
            init (id) {
                this.visible = true
                this.addNext = true
                // 先查詢menus列表
                //然後在查詢角色信息
                this.menuList = [];
                this.dataForm = {};
                this.$http.get('/sys/menus/list').then( res => {
                    if(res.success){
                        this.menuList = res.data;
                        this.$refs.menuListTree.setCheckedKeys([]);
                        this.queryRole(id);
                    }
                })
                // this.$http.get('/sys/menus').then( res => {
                //     if(res.success){
                //         this.menuList = res.data;
                //         this.$refs.menuListTree.setCheckedKeys([]);
                //         this.queryRole(id);
                //     }
                // })
            },
            queryRole(id){
                if(!id){
                    return;
                }
                this.$http.get('/sys/roles/'+id).then( res => {
                    if(res.success){
                        this.dataForm = res.data.data;
                        this.$refs.menuListTree.setCheckedKeys(this.filterLeaf(res.data.data.sysMenuList));
                    }
                })
            },
            // 過濾，只剩下末端的子節點
            filterLeaf(array){
                let tempArray = [];
                for(let i=0;i<array.length;i++){
                    if(this.isLast(array[i])){
                        tempArray.push(array[i])
                    }
                }
                return tempArray;
            },
            // 判斷是否為末端的子節點
            isLast(id, list){
                let flag = false;
                list = list ? list : this.menuList;
                list.forEach( item => {
                    if(item.id == id && item.childs.length==0){
                        flag = true;
                    }else{
                        if(this.isLast(id, item.childs)){
                            flag = true;
                        }
                    }
                })
                return flag;
            },
            // 表單提交
            dataFormSubmit () {
                this.$refs['dataForm'].validate((valid) => {
                    if (valid) {
                        if(this.addNext){
                            this.addNext=false
                            let ajaxData = {
                                id: this.dataForm.id || null,
                                name: this.dataForm.name,
                                nameEn: '',
                                remark: this.dataForm.remark,
                                sysMenuList: [].concat(this.$refs.menuListTree.getCheckedKeys(), this.$refs.menuListTree.getHalfCheckedKeys())
                            };
                            console.log(ajaxData.sysMenuList)
                            if(this.dataForm.id){
                                this.$http.post('/sys/roles', ajaxData).then( res => {
                                    if(res.success){
                                        
                                        if (res.data.code ==200) {
                                            this.$toast({tips: '修改成功'});
                                            this.visible = false;
                                            this.$emit('update');
                                            location.reload();
                                        }else{
                                            this.$toast({tips: res.data.message});
                                        }
                                        
                                        
                                    }else{
                                        
                                    }
                                    this.addNext = true
                                })
                            }else{
                                // delete ajaxData.id;
                                this.$http.put('sys/roles', ajaxData).then( res => {
                                    if(res.success){
                                        if (res.data.code ==200) {
                                            this.$toast({tips: '添加成功'});
                                            this.visible = false;
                                            this.$emit('update');
                                            location.reload();
                                        }else{
                                            this.$toast({tips: res.data.message});
                                        }
                                    }else{
                                        
                                    }
                                    this.addNext = true
                                })
                            }
                        }
                    }
                })
            }
        }
    }
</script>
<style lang="scss" scoped>
/deep/ *{
    font-size: 14px;
}
/deep/ .el-form-item__label{
    margin-left: 0;
}
/deep/ .el-tabs button span{
    color: #333 !important;
}
</style>