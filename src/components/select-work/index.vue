<template>
<div> 
    <el-dialog :visible.sync="show" width="1000px" title="Work" :close-on-click-modal="false">
        <div style="width: 560px;margin: auto;margin-bottom: 20px">
            <el-input v-model="searchInfo.title" @keyup.enter.native="onSubmit()" placeholder="Title" style="width: 220px;"></el-input>
            <el-input v-model.trim="searchInfo.workId" @keyup.enter.native="onSubmit()" placeholder="Work No" style="width: 128px;"></el-input>
            <el-input v-model.trim="searchInfo.soc" @keyup.enter.native="onSubmit()" placeholder="Soc" style="width: 60px;"></el-input>
            <el-button type="primary" @click="onSubmit()">查詢</el-button>
            <span class="clear-search" @click="clearSearch()">清除搜索</span>
        </div>
        <el-table  :data="tableData" stripe :empty-text="emptyText" :row-class-name="handleRowClass" @row-dblclick="getCurrentWorkInfo"  v-loading="loading">
            <el-table-column property="title" label="Title">
                <template slot-scope="scope">
                    {{scope.row.title||scope.row.title_en}}
                </template>
            </el-table-column>
            <el-table-column property="work_id" label="WorkNo" width="150px"></el-table-column>
            <el-table-column property="work_society_code" label="Soc" width="60px"></el-table-column>
            <el-table-column property="genre_code" label="Genre" width="90px"></el-table-column>
            <el-table-column
                label="operation"
                width="100">
                <template slot-scope="scope">
                    <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                        <i class="el-icon-check" @click="checkedWork(scope.$index,scope.row)"></i>
                    </span>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :total="total" @current-change="onSubmit" :current-page="currentPage">
        </el-pagination>
    </el-dialog>
</div>
</template>
<script>
export default {
    data(){
        return{
            loading:false,
            show:false,
            searchInfo: {},
            tableData: [],
            total: 0,
            currentPage: 1,
            emptyText: '暫無數據'
        }
    },
    props: {
        search: {
            type: Object,
            default: () => {
                return {
                    title: '',
                    soc: '',
                    workId: ''
                }

            }
        }
    },
    watch: {
        'searchInfo.workId': function(){
            if(this.searchInfo.workId){
                this.searchInfo.workId = (this.searchInfo.workId + '').replace(/[^0-9]/g, '');
            }
        },
        'searchInfo.soc': function(){
            if(this.searchInfo.soc){
                this.searchInfo.soc = (this.searchInfo.soc + '').replace(/[^0-9]/g, '');
            }
        }
    },
    mounted(){
        // this.init()
    },
    methods: {
        getCurrentWorkInfo(row){
            // console.log("%%%%%",row)
            let routeName = '';
                if(row.work_type === 'ARR'){
                    routeName = 'works-baseinfoarr';
                }else if(row.work_type === 'ORG'){
                    routeName = 'works-baseinfo';
                }else if(row.work_type === 'ADP'){
                    routeName = 'works-baseinfoadp';
                }else if(row.work_type === 'AV' && row.genre_code != 'TVS'){
                    routeName = 'works-baseinfoav';
                }else if((row.work_type === 'AV' && row.genre_code === 'TVS') || row.work_type == 'TV'){
                    routeName = 'works-baseinfotv';
                }else if(row.work_type == 'ME'){
                    routeName = 'works-medleyinfo'
                }
                // r.push({name: routeName, query: {id: row.work_id, socId: row.work_society_code, title: row.title ? row.title : row.title_en, nameId: row.id+row.work_id}, params: {id: row.work_id}})
               //                         let {href} = this.$router.resolve({name:'login'})
               let {href} = this.$router.resolve({name: routeName, query: {id: row.work_id, socId: row.work_society_code, title: row.title ? row.title : row.title_en, nameId: row.id+row.work_id}, params: {id: row.work_id}})
               window.open(href, '_blank')
            //    this.$router.go(-1)
        },
         handleRowClass(row, index){
             return 'no'

                // if(row.row == this.matchInfo || row.row == this.matchIPInfo || row.row == this.currentIpRow){
                //     return 'current'
                // }else{
                //     return 'no';
                // }
            },
        /**
         * dialog 组件调用方式：
         * 1、父级用v-if在html里加载组件,
         * 2、父级调用子级的init方法，将数据初始化，prop传给data等
         *
         */
        init(){
            this.show = true;
            this.searchInfo = this.$utils.copy(this.search);
            this.onSubmit();
        },
        clearSearch(){
            this.searchInfo = {
                title: '',
                soc: '',
                workId: ''
            };
            this.onSubmit();
        },
        onSubmit(page){
            // this.loading=true
            let params = this.$utils.copy(this.searchInfo);
            params.title = params.title ? params.title.trim() : params.title;
            params.workId = params.workId ? String(params.workId).trim() : params.workId;
            params.soc = params.soc ? String(params.soc).trim() : params.soc;
            params.page = {
                pageNum: page ? page : 1,
                pageSize: 10
            }
            this.emptyText = '數據加載中';
            this.$http.post('/wrk/queryWrkWorkListEs', params).then(res => {
                if (res.success) {
                    // this.loading=false
                    this.tableData = res.data.list;
                    if(! this.tableData || this.tableData.length == 0){
                        this.emptyText = '暫無數據';
                    }
                    this.total = res.data.total;
                    this.currentPage = page ? page : 1;
                }
            })
        },
        checkedWork(index, row){
            this.$emit('checkWork', row);
            this.show = false;
        }
    }

}
</script>
<style lang="scss" scoped>
    /deep/ .el-dialog__title{
        font-weight: bold;
    }
    /deep/ .el-dialog__body{
        text-align: center;
        padding-top: 0;
    }
     /deep/ tr.current>td{
        background-color: #17B3A3 !important;
    }
</style>
