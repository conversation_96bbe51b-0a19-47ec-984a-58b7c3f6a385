<template>
  <div>
    <el-button type="primary" class="btn" @click="add">新增</el-button>
    <el-table :data="tableData" border stripe style="width: 100%" :empty-text="emptyText">
      <el-table-column prop="distNo" label="dist no">
      </el-table-column>
      <el-table-column prop="categoryCode" label="category code">
      </el-table-column>
      <el-table-column prop="ipBaseNo" label="ip base no">
      </el-table-column>
      <el-table-column prop="ipName" label="ip name">
      </el-table-column>
      <!--<el-table-column
                prop="ipRightCode"
                label="ip right code">
            </el-table-column>-->
      <el-table-column prop="isInclude" label="is include">
      </el-table-column>
      <el-table-column label="operation" width="110px">
        <template slot-scope="scope">
          <el-button @click="editRow(scope.row)" type="text" size="small">编辑</el-button>
          <el-button @click="deleteRow(scope.row)" type="text" size="small">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination background layout="prev, pager, next" :total="total" @current-change="handleCurrentChange" :current-page="page_num">
    </el-pagination>
  </div>
</template>

<script>
export default {
  name: 'filterIp',
  props: ['distNo'],
  data() {
    return {
      tableData: [],
      page_num: 1,
      total: 1,
      emptyText: '暫無數據',
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    getList(num) {
      console.log('ip')
      let params = {
        distNo: this.distNo,
        page_num: num ? num : 1
      }
      this.emptyText = '數據加載中';
      this.$http.get('/distributionSpecialCondition/listDistListDistListFilterIpWithPage', { params }).then(res => {
        console.log(res)
        if (res.success) {
          this.tableData = res.data.data.list
          this.total = res.data.data.total
          this.page_num = params.page_num
          if (!this.tableData || this.tableData.length == 0) {
            this.emptyText = '暫無數據';
          }
        }
      })
    },
    handleCurrentChange(val) {
      this.page_num = val
      this.getList(val)
    },
    editRow(row) {
      console.log(row)
      this.$router.push({ name: 'distribute-editip', query: { row: JSON.stringify(row), nameId: row.id, title: row.distNo } })
    },
    add() {
      this.$router.push({ name: 'distribute-addip', params: { distNo: this.distNo } })
    },
    deleteRow(row) {
      this.$alert('確定要刪除嗎', '刪除', {
        confirmButtonText: '确定',
        showCancelButton: true,
        cancelButtonText: '取消',
        callback: action => {
          if (action == 'confirm') {
            this.$http.delete('/distributionSpecialCondition/deleteDistListDistListFilterIp/' + row.id).then(res => {
              if (res.data.code === 200) {
                this.$message({
                  message: '删除成功',
                  type: 'success'
                })
                this.getList(this.page_num)
              } else {
                this.$message({
                  message: '删除失敗',
                  type: 'error'
                })
              }
            })
          }
        }
      });

    }
  }
}
</script>

<style scoped>
.btn {
  margin-bottom: 10px;
}
</style>
