<template>
    <div>
        <top-bar :steps="steps" @save="saveIp"></top-bar>
        <el-collapse class="contentbox" v-model="activeNames">
            <el-collapse-item title="IP Base" class="step-jump" name="1">
                <div class="formcarbox">
                    <div class="frombox">
                        <div class="boxline">
                            <el-form :inline="true" label-width="120px" :model="formDummy" label-position="right" class="demo-form-inline">
                                <div>
                                    <el-form-item label="IP Base No">
                                        <el-input v-model="formDummy.ipBaseNo" placeholder="" readonly></el-input>
                                    </el-form-item>
                                    <el-form-item label="type">
                                        <el-select v-model="formDummy.ipType" @change="changeType" placeholder="">
                                            <el-option label="L" value="L"></el-option>
                                            <el-option label="N" value="N"></el-option>
                                        </el-select>
                                    </el-form-item>
                                    <el-form-item label="sex" v-if="formDummy.ipType == 'N'">
                                        <el-select v-model="formDummy.sex" placeholder="">
                                            <el-option label="男" value="M"></el-option>
                                            <el-option label="女" value="F"></el-option>
                                        </el-select>
                                    </el-form-item>
                                    <el-form-item label="Company" v-else>
                                        <el-select v-model="formDummy.sex" placeholder="">
                                            <el-option label="S" value="S"></el-option>
                                            <el-option label="B" value="B"></el-option>
                                            <el-option label="P" value="P"></el-option>
                                            <el-option label="I" value="I"></el-option>
                                            <el-option label="C" value="C"></el-option>
                                        </el-select>
                                    </el-form-item>
                                </div>
                                <el-form-item label="Place of Birth" class="f-13">
                                    <el-input v-model="formDummy.birthPlace" placeholder="" readonly></el-input>
                                </el-form-item>
                                <el-form-item label="State of Birth">
                                    <el-input v-model="formDummy.birthState" placeholder="" readonly></el-input>
                                </el-form-item>
                                <div>
                                    <el-form-item label="CountryOfBirth">
                                        <el-input v-model="formDummy.birthCountryCode" style="width: 80px;"  placeholder=""  readonly></el-input>
                                        <el-input v-model="formDummy.birthCountryName" style="width: 260px;"  placeholder=""  readonly></el-input>
                                    </el-form-item>
                                    <el-form-item label="Nationality">
                                        <el-input v-model="formDummy.tisN" placeholder=""  readonly style="width: 80px;"></el-input>
                                        <el-input v-model="formDummy.nationality" placeholder=""  readonly style="width: 260px;"></el-input>
                                    </el-form-item>
                                </div>
                                <div>
                                    <el-form-item label="Status">
                                        <el-input v-model="formDummy.status" readonly></el-input>
                                    </el-form-item>
                                    <el-form-item style="margin-left: 40px">
                                        <el-checkbox v-model="dummychecked" @change="dummyhandle" disabled>Dummy IPI</el-checkbox>
                                    </el-form-item>
                                    
                                <!-- </div>
                                <div> -->
                                    <el-form-item label="DateOfBirth">
                                        <date-picker v-model="formDummy.birthDate" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd" style="width: 100px !important;" readonly></date-picker>
                                    </el-form-item>
                                    <el-form-item label="DateOfDeath">
                                        <date-picker v-model="formDummy.deathDate" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd" style="width: 100px !important;" readonly></date-picker>
                                    </el-form-item>
                                </div>
                            </el-form>
                        </div>
                    </div>
                </div>
            </el-collapse-item>
            <el-collapse-item title="IP Right" class="step-jump" name="2">
                <div class="boxline component-class">
                    <el-form :inline="true" :model="formAgreement" class="demo-form-inline">
                        <el-form-item label="Right">
                            <el-select v-model="formAgreement.groupName"  style="width: 250px;" placeholder="" @change="getRightSoc">
                                <el-option label="PERFORMING RIGHT" value="PER"></el-option>
                                <el-option label="MECHANICAL RIGHT" value="MEC"></el-option>
                                <el-option label="SYNCHRONIZATION RIGHT" value="ZYN"></el-option>
                                <el-option label="New MEDIA RIGHT" value="NOD"></el-option>
                                <el-option label="Other RIGHT" value="OR"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-form>
                    <el-table
                        :data="ipRight[formAgreement.groupName]"
                        border
                        stripe
                        style="width: 100%;">
                        <el-table-column
                            prop="societyCode"
                            label="SOC">
                            <template slot-scope="scope">
                                <el-input v-model=scope.row.societyCode  ></el-input>
                                <!-- placeholder="雙擊查詢" @dblclick.native="getSocData(scope.$index,scope.row)" -->
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="rightCode"
                            label="Right">
                            <template slot-scope="scope">
                                <el-select v-model="scope.row.rightCode" placeholder="" >
                                    <el-option :label="item.rightCode" :value="item.rightCode" v-for="(item,index) in rightData" :key="index"></el-option>
                                </el-select>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="roleCode"
                            label="Role">
                            <template slot-scope="scope">
                                <el-select v-model="scope.row.roleCode" placeholder="" >
                                    <el-option :label="item.roleCode" :value="item.roleCode" v-for="(item,index) in roleData" :key="index"></el-option>
                                </el-select>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="validFrom"
                            label="Valid From">
                            <template slot-scope="scope">
                                <date-picker v-model="scope.row.validFrom" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd"></date-picker>

                                <!--<el-input v-model=scope.row.validFrom placeholder=""></el-input>-->
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="validTo"
                            label="Valid To">
                            <template slot-scope="scope">
                                <date-picker v-model="scope.row.validTo" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd"></date-picker>
                                <!--<el-input v-model=scope.row.validTo placeholder=""></el-input>-->
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="membershipShare"
                            label="Share">
                            <template slot-scope="scope">
                                <el-input v-model=scope.row.membershipShare placeholder=""></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="signatureDate"
                            label="Signature Date">
                            <template slot-scope="scope">
                                <el-input
                                    v-model=scope.row.signatureDate
                                    type="text"
                                    placeholder="">
                                </el-input>
                                <!--<el-input v-model=scope.row.signatureDate placeholder=""></el-input>-->
                            </template>
                        </el-table-column>
                        <el-table-column
                            label="operation"
                            width="100">
                            <template slot-scope="scope">
                                <span style="width: 100%;display: inline-block;text-align: center">
                                    <i class="el-icon-delete" @click="deletedata1(scope.$index,ipRight[formAgreement.groupName])"></i>
                                </span>
                            </template>
                        </el-table-column>
                    </el-table>
                    <div class="add-new"><el-button type="primary" @click="adddata1()">新 增</el-button></div>
                </div>
            </el-collapse-item>

            <el-collapse-item title="IP Info" class="step-jump" name="3">
                <!-- <el-tabs type="border-card"> -->
                    <div class="boxline">
                        <el-table
                            :data="tableData2"
                            border
                            stripe
                            style="width: 100%">
                            <el-table-column
                                prop="lastName"
                                label="LastName"
                                width="180">
                                <template slot-scope="scope">
                                    <el-input v-model=scope.row.lastName placeholder=""></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="firstName"
                                label="FirstName"
                                width="180">
                                <template slot-scope="scope">
                                    <el-input v-model=scope.row.firstName placeholder=""></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="chineseLastName"
                                label="ch_lastName">
                                <template slot-scope="scope">
                                    <el-input v-model=scope.row.chineseLastName placeholder=""></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="chineseFirstName"
                                label="ch_firstName">
                                <template slot-scope="scope">
                                    <el-input v-model=scope.row.chineseFirstName placeholder=""></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="nameNo"
                                label="IP Name No">
                                <template slot-scope="scope">
                                    <el-input v-model=scope.row.nameNo placeholder="" readonly></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="nameType"
                                label="nameType">
                                <template slot-scope="scope">
                                    <el-select v-model=scope.row.nameType placeholder="">
                                        <el-option label="PA" value="PA"></el-option>
                                        <el-option label="PP" value="PP"></el-option>
                                        <el-option label="MO" value="MO"></el-option>
                                        <el-option label="DF" value="DF"></el-option>
                                        <el-option label="OR" value="OR"></el-option>
                                        <el-option label="PG" value="PG"></el-option>
                                        <el-option label="HR" value="HR"></el-option>
                                    </el-select>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="operation"
                                width="100">
                                <template slot-scope="scope">
                                    <span style="width: 100%;display: inline-block;text-align: center">
                                        <i class="el-icon-delete" @click="deletedata2(scope.$index,tableData2)"></i>
                                    </span>
                                </template>
                            </el-table-column>
                        </el-table>
                        <div class="add-new">
                            <el-button type="primary" @click="adddata2()">新 增</el-button>
                        </div>
                    </div>
                <!-- </el-tabs> -->
            </el-collapse-item>
        </el-collapse>
        <el-dialog :visible.sync="socTableVisible" width="1000px" :close-on-click-modal="false">
            <div style="width: 500px;margin: auto;margin-bottom: 20px">
                <el-input placeholder="請輸入societyName" v-model="socInput" class="input-with-select">
                    <el-button slot="append" icon="el-icon-search" @click="querySocList()"></el-button>
                </el-input>
            </div>
            <el-table :empty-text="tableresult"   stripe :data="socGridData">
                <el-table-column property="societyName" label="societyName"></el-table-column>
                <el-table-column property="societyCode" label="societyCode"></el-table-column>
                <el-table-column property="bankName" label="bankName"></el-table-column>
                <el-table-column property="bankSwiftCode" label="bankSwiftCode"></el-table-column>
                <el-table-column
                    label="operation"
                    width="100">
                    <template slot-scope="scope">
                        <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                            <i class="el-icon-check" @click="checkedSoc(scope.$index,scope.row)"></i>
                        </span>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                layout="prev, pager, next"
                :total="socTotal"
                @current-change="querySocList">
            </el-pagination>
        </el-dialog>
        <select-country v-if="countryShow" ref="selectCountry" :search="countrySearch" @checkCountry="checkCountry"></select-country>
    </div>
</template>

<script>
    import selectCountry from '@/components/select-country'
    import axios from '../../utils/httpRequest'
    const cityOptions = ['P', 'M', 'CP', 'MA']

    export default {
        name: 'info',
        data () {
            var validateYearInput = (rule, value, callback) => {
                if (!Number.isInteger(value)) {
                    callback(new Error('請輸入數字值'))
                } else {
                    if (value < 1000 || value > 2100) {
                        callback(new Error('不能大於2100或者小於1000！'))
                    } else {
                        callback()
                    }
                }
            }
            var validateMounthInput = (rule, value, callback) => {
                if (!Number.isInteger(value)) {
                    callback(new Error('請輸入數字值'))
                } else {
                    if (value < 1 || value > 12) {
                        callback(new Error('不能大於12或者小於0！'))
                    } else {
                        callback()
                    }
                }
            }
            var validateDayInput = (rule, value, callback) => {
                if (!Number.isInteger(value)) {
                    callback(new Error('請輸入數字值'))
                } else {
                    if (value < 1 || value > 31) {
                        callback(new Error('不能大於31或者小於0！'))
                    } else {
                        callback()
                    }
                }
            }
            return {
                index: 0,
                steps: [
                    {
                        name: 'IP Base'
                    },
                    {
                        name: 'IP Right'
                    },
                    {
                        name: 'IP Info'
                    }
                ],
                activeNames: ['1', '2', '3'],
                disableshow: false,
                socTableVisible: false,
                blurIndex: '',
                socGridData: [],tableresult:' ',
                modules: [],
                socInput: '',
                socTotal: 0,
                nShow: false,
                cities: cityOptions,
                checkedDummy: false,
                dummychecked: true,
                id: '',
                formDummy: {
                    ipBaseNo: '',
                    ipType: '',
                    sex: '',
                    company: '',
                    birthPlace: '',
                    birthState: '',
                    birthCountryName: '',
                    status: '1',
                    dummy: true,
                    id: null,
                    birthYear: '',
                    birthMounth: '',
                    birthDay: '',
                    deathYear: '',
                    deathMounth: '',
                    deathDay: '',
                    tisN: '',
                    nationality: '',

                },
                formAgreement: {
                    groupName: 'PER'
                },
                tableData: [],
                // tableData1: [
                    // { societyCode: '', rightCode: '', roleCode: '', validFrom: '', validTo: '', membershipShare: '', signatureDate: '', ipBaseNo: '' }
                // ],
                ipRight: {},
                tableData2: [
                    // {lastName: '', firstName: '', chineseFirstName: '', chineseLastName: '', nameNo: '', nameType: ''}
                ],
                rightData: [],
                roleData: [],
                countrySearch: {
                    tisN: ''
                },
                countryShow: false,
                countryType: '',
            }
        },
        components: {
            selectCountry
        },
        methods: {
            getCountry(type){
                this.countryType = type;
                this.countrySearch = {
                    tisN: type == 'nationality' ? this.formDummy.tisN : this.formDummy.birthCountryCode
                }
                this.countryShow = true;
                this.$nextTick( () => {
                    this.$refs.selectCountry.init();
                })
            },
            checkCountry(info){
                if(this.countryType == 'nationality'){
                    this.$set(this.formDummy, 'tisN', info.tisN);
                    this.$set(this.formDummy, 'nationality', info.name);
                }else if(this.countryType == 'birth'){
                    this.$set(this.formDummy, 'birthCountryCode', info.tisN);
                    this.$set(this.formDummy, 'birthCountryName', info.name);
                }
            },
            changeType (val, item) {
                this.formDummy.sex = '';
                // if (val === 'N') {
                //     this.nShow = true
                // } else {
                //     this.nShow = false
                // }
            },
            adddata1 () {
                let myArray = { societyCode: '', rightCode: '', roleCode: '', validFrom: '', validTo: '', membershipShare: '', signatureDate: '', ipBaseNo: '' };
                if(!this.ipRight[this.formAgreement.groupName]){
                    this.$set(this.ipRight, this.formAgreement.groupName, []);
                }
                this.ipRight[this.formAgreement.groupName].push(myArray);
            },
            deletedata1 (index, rows) {
                this.$msgbox.confirm(`確定進行[删除]操作?`, '提示', {
                    confirmButtonText: '確定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    rows.splice(index, 1)
                })
            },
            adddata2 () {
                this.$http.get('/ref/sequence', {params:{prefix: '00', seq_length: 11 }}).then( res => {
                    if(res.success){
                        let myArray = [{lastName: '', firstName: '', chineseFirstName: '', chineseLastName: '', nameNo: res.data, nameType: ''}]
                        this.tableData2 = [...this.tableData2, ...myArray]
                    }
                })

            },
            deletedata2 (index, rows) {
                this.$msgbox.confirm(`確定進行[删除]操作?`, '提示', {
                    confirmButtonText: '確定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    rows.splice(index, 1)
                })
            },
            dummyhandle () {
                if (this.checkedDummy) {
                    this.disableshow = true
                } else {
                    this.disableshow = false
                }
            },
            saveIp () {
                if (this.dummychecked) {
                    this.formDummy.dummy = 'Y'
                }
                if (!this.dummychecked) {
                    this.formDummy.dummy = 'N'
                }
                if(!this.formDummy.birthDate){
                    // this.$toast({tips: '請選擇出生日期'});
                    // return;
                }else{
                    this.formDummy.birthYear = new Date(this.formDummy.birthDate).getFullYear();
                    this.formDummy.birthMounth = new Date(this.formDummy.birthDate).getMonth() + 1;
                    this.formDummy.birthDay = new Date(this.formDummy.birthDate).getDate();
                }
                if(this.formDummy.deathDate){
                    this.formDummy.deathYear = new Date(this.formDummy.deathDate).getFullYear();
                    this.formDummy.deathMounth = new Date(this.formDummy.deathDate).getMonth() + 1;
                    this.formDummy.deathDay = new Date(this.formDummy.deathDate).getDate();

                }

                let rights = [];
                for(let key in this.ipRight){
                    rights.push({agreements: this.ipRight[key], groupName: key});
                }
                let params = {
                    'info': this.formDummy,
                    'agreementGroups': rights,
                    'names': this.tableData2
                }

                /**
                 * ipinfo 验证规则
                 *必须有一條
                   NT： 必须有一條PA 的
                    lastname firstname 必填一個
                 *
                 */
                if(this.tableData2.length < 1){
                    this.$toast({tips: '請至少填寫一條Ip Info 信息'});
                    return;
                }else{
                    let ipInfoFlag = false;
                    let flag = true;
                    this.tableData2.forEach( item => {
                        if(item.nameType == 'PA'){
                            ipInfoFlag = true;
                        }
                        if(!item.lastName && !item.firstName && !item.chineseLastName && !item.chineseFirstName){
                            flag = false;
                        }
                    })
                    if(!ipInfoFlag){
                        this.$toast({tips: '請至少有一條Ip Info 信息的Name Type 為 PA'});
                        return;
                    }
                    if(!flag){
                        this.$toast({tips: 'Ip Info信息中，每条信息的LastName、FirstName、chineseFirstName、chineseLastName,至少填寫一項'});
                        return;
                    }
                }


                axios.post('/ip', params).then(res => {
                    if (res.status === 200 && (!res.data.code || res.data.code == 200)) {
                        this.id = res.data
                        this.$toast({tips: '新增成功'});
                        this.$bus.$emit('closeCurrentTab', () => { this.$router.push({name: 'member-ipi', query: {update: true}})});
                        // this.$msgbox.confirm('新增會員成功', '提示', {
                        //     confirmButtonText: '確定',
                        //     // cancelButtonText: '取消',
                        //     showCancelButton: false,
                        //     type: 'warning'
                        // }).then(() => {
                        //     this.$bus.$emit('closeCurrentTab', () => { this.$router.push({name: 'member-ipi', query: {update: true}})});
                        //     // this.$router.push({name: 'member-ipi'})
                        // })
                        if (res.data) {
                            this.formDummy.id = res.data.toString()
                        }
                    }else{
                        this.$toast({tips: res.data.message})
                    }
                })
                // .catch(err => {
                //     console.log(err)
                //     this.$message({
                //         type: 'error',
                //         message: '後台錯誤'
                //     })
                // })
            },
            getRightSoc () {
                let params = this.formAgreement.groupName
                this.tableData1 = []
                axios.get('/ip/right?cash_right=' + params).then(res => {
                    if (res.status === 200) {
                        this.rightData = res.data;
                    }
                })
            },
            getRoleList () {
                axios.get('/ref/role').then(res => {
                    if (res.status === 200) {
                        this.roleData = res.data
                    }
                })
            },
            getSocData (val, item) {
                this.blurIndex = val
                this.querySocList(1);
            },
            querySocList(page){
                let that = this
                this.tableresult = '數據加載中...'
                this.$http.post('/ref/society/getSocietyList', {page: {pageNum: page ? page : 1, pageSize: 10}, data: {societyName: this.socInput}}).then(res => {
                    if (res.status === 200) {
                        this.socTableVisible = true;
                        that.modules = res.data.list;
                        that.socGridData = that.modules;
                        this.socTotal = res.data.total;
                    }
                        this.tableresult = res.data.list.length == 0 ? '暫無數據' : ' '
                })
            },
            search () {
                let that = this
                if (that.socInput === '' || typeof (that.socInput) === 'undefined') {
                    that.socGridData = that.modules
                } else {
                    that.socGridData = []
                    that.modules.map(function (item) {
                        if (item.societyName.toLowerCase().search(that.socInput.toLowerCase()) !== -1) { // 筛选条件
                            that.socGridData.push(item)
                        }
                    })
                }
            },
            checkedSoc (val, item) {
                this.socTableVisible = false
                this.tableData1[this.blurIndex].societyCode = item.societyCode
            },
            getIpBaseNo () {
                let params = {
                    prefix: 161,
                    seq_length: 13
                }
                axios.get('/ref/sequence', {params}).then(res => {
                    if (res.status === 200) {
                        this.formDummy.ipBaseNo = res.data
                    }
                })
            }
        },
        mounted () {
            this.getRightSoc()
            this.getRoleList()
            this.getIpBaseNo()
            this.index = this.$route.query.index
        },
        computed: {
            sidebarFold: {
                get () { return this.$store.state.common.sidebarFold }
            }
        },
        watch: {
            index (newVal, oldVal) {
                this.getIpBaseNo()
            }
        }
    }
</script>

<style lang="scss" scoped>
@import "../../assets/scss/works.scss";
    .frombox{
        margin-top: 20px;
    }
    .memberdetail {
        width: 100%;
    }
    .memberdetail .el-form-item__content {
        width: calc(100% - 150px) !important;
    }
    .el-icon-delete {
        cursor: pointer;
    }
    .savebox{
        width: 100%;
        text-align: center;
        margin: 20px 0;
    }
    .right{
        float: right;
    }
    .f-13 label{
        font-size: 13px !important;
    }
    .el-date-editor.el-input, .el-date-editor.el-input__inner{
        width: auto !important;
    }
    .step-jump{
        position: relative;
    }
    .add-new {
        position: absolute;
        right: 50px;
        top: 8px;
        text-align: center;
        width: auto;
    }
</style>
<style>
.el-checkbox__input.is-disabled+span.el-checkbox__label{
    color: #666 !important;
}
</style>
