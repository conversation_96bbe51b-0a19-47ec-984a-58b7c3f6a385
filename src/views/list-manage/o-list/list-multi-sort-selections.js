export default [
  {
    name: '分配代號 - 昇序(▲)',
    prop: 'distNo',
    value: JSON.stringify({
      orderField: 'distNo',
      direction: 'asc',
    }),
  },
  {
    name: '分配代號 - 降序(▼)',
    prop: 'distNo',
    value: JSON.stringify({
      orderField: 'distNo',
      direction: 'desc',
    }),
  },
  {
    name: '⽔單ID - 昇序(▲)',
    prop: 'id',
    value: JSON.stringify({
      orderField: 'id',
      direction: 'asc',
    }),
  },
  {
    name: '⽔單ID - 降序(▼)',
    prop: 'id',
    value: JSON.stringify({
      orderField: 'id',
      direction: 'desc',
    }),
  },
  {
    name: '匯款日期 - 昇序(▲)',
    prop: 'receiptDate',
    value: JSON.stringify({
      orderField: 'receiptDate',
      direction: 'asc',
    }),
  },
  {
    name: '匯款日期 - 降序(▼)',
    prop: 'receiptDate',
    value: JSON.stringify({
      orderField: 'receiptDate',
      direction: 'desc',
    }),
  },
  {
    name: '協會名稱 - 昇序(▲)',
    prop: 'sourceSocName',
    value: JSON.stringify({
      orderField: 'sourceSocName',
      direction: 'asc',
    }),
  },
  {
    name: '協會名稱 - 降序(▼)',
    prop: 'sourceSocName',
    value: JSON.stringify({
      orderField: 'sourceSocName',
      direction: 'desc',
    }),
  },
  {
    name: '協會代碼 - 昇序(▲)',
    prop: 'sourceSoc',
    value: JSON.stringify({
      orderField: 'sourceSoc',
      direction: 'asc',
    }),
  },
  {
    name: '協會代碼 - 降序(▼)',
    prop: 'sourceSoc',
    value: JSON.stringify({
      orderField: 'sourceSoc',
      direction: 'desc',
    }),
  }
];
