<template>
    <div>
        <el-form :inline="true" :model="searchForm" class="demo-form-inline" @keyup.enter.native="getCcidList(1)">
            <el-form-item label="Company">
                <el-input v-model.trim="searchForm.company" placeholder="company"></el-input>
            </el-form-item>
            <el-form-item label="Product">
                <el-input v-model="searchForm.product" placeholder="product"></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="getCcidList(1)" v-if="isAuth('distribute:reportlist:find')">搜索</el-button>
            </el-form-item>
        </el-form>
        <el-table
            :data="tableData"
            border
            stripe
            style="width: 100%"
            v-loading="loading"
            :empty-text="emptyText">
            <el-table-column
                prop="id"
                label="id">
            </el-table-column>
            <el-table-column
                prop="company"
                label="company">
            </el-table-column>
            <el-table-column
                prop="products"
                label="product">
            </el-table-column>
            <el-table-column
                prop="fileType"
                label="file type">
            </el-table-column>
            <el-table-column
                prop="fileNames"
                label="file name">
            </el-table-column>
            <el-table-column
                prop="startDate"
                label="claim start time"
                min-width="120px">
                <template slot-scope="scope">
                    {{scope.row.startDate?scope.row.startDate.split(' ')[0]:''}}
                </template>
            </el-table-column>
            <el-table-column
                prop="endDate"
                label="claim end time"
                min-width="120px">
                <template slot-scope="scope">
                    {{scope.row.endDate?scope.row.endDate.split(' ')[0]:''}}
                </template>
            </el-table-column>
            <el-table-column
                prop="createTime"
                label="create time"
                min-width="100px">
            </el-table-column>
            <el-table-column
                prop="amendTime"
                label="Finish time"
                min-width="100px">
            </el-table-column>
            <el-table-column
                prop="status"
                label="status">
                <template slot-scope="scope">
                    <span v-if="scope.row.status == 0">等待計算</span>
                    <span v-else-if="scope.row.status == 1">計算中</span>
                    <span v-else-if="scope.row.status == 2">計算完成</span>
                    <span v-else-if="scope.row.status == 3">計算失敗</span>
                    <span v-else-if="scope.row.status == 4">導出中</span>
                    <span v-else-if="scope.row.status == 5">導出完成</span>
                    <span v-else-if="scope.row.status == 7">部分计算</span>
                    <span v-else>導出失敗</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="filePath"
                label="output path"
                min-width="100px">
                <template slot-scope="scope">
                    <span :title="scope.row.filePath">{{scope.row.filePath}}</span>
                </template>
            </el-table-column>
            <el-table-column
                label="失敗原因"
                min-width="100px">
                <template slot-scope="scope">
                    <span class="over-line pointer" @dblclick="showErrorMsg(scope.row.description)">{{scope.row.description}}</span>
                </template>
            </el-table-column>
            <el-table-column
                label="OP"
                fixed="right"
                width='160px'>
                <template slot-scope="scope">
                    <!-- <el-button @click="reCreateReport(scope.row)" type="text" size="small" v-if="scope.row.status != 0 && scope.row.status != 1">重新生成</el-button> -->
                    <el-button @click="reCreateReport(scope.row)" type="text" size="small" v-if="isAuth('distribute:reportlist:partial_run')&&(scope.row.status != 0 && scope.row.status != 1)">partial run</el-button>
                    <el-button @click="delCreateReport(scope.row)" type="text" size="small"  v-if="isAuth('distribute:reportlist:del')">刪除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :current-page="pageNum"
            :total='total' @current-change="handleCurrentChange">
        </el-pagination>
    </div>
</template>

<script>
    export default {
        name: 'reportlist',
        data(){
            return {
                searchForm:{},
                tableData:[],
                total:0,
                pageNum:1,
                loading:false,
                emptyText: '數據加載中',
            }
        },
        mounted () {
            this.init()
            this.getCcidList()
        },
        activated(){
        //   if(this.$route.query.update){
        //       this.init()
        //       this.getCcidList()
        //   }
            this.$nextTick( () => {
                if(this.$route.query.update){
                let query = this.$route.query;
                delete query.update;
                this.init()
                this.getCcidList()
            }
        })
        },
        methods: {
            init(){
                this.searchForm = {
                    setId:'',
                    company:'',
                    product:''
                }
            },
            showErrorMsg(msg){
                this.$alert(msg, '失敗原因', {
                    confirmButtonText: '確定'
                });
            },
            handleCurrentChange(val){
                this.getCcidList(val)
            },
            getCcidList(page){
                let params = this.searchForm
                params.page_num = page?page:1
                this.loading=true
                this.emptyText = '數據加載中';
                this.$http.get('/claim/ccid/header',{params}).then(res => {
                    this.loading=false
                    if(res.status === 200){
                        this.tableData = res.data.list
                        this.total = res.data.total
                        this.pageNum = page?page:1
                        if(! this.tableData || this.tableData.length == 0){
                            this.emptyText = '暫無數據';
                        }
                    }

                })
            },
            reCreateReport(row){
                this.$router.push({name: 'distribute-createreport', query: {data:JSON.stringify(row)}})
            },
            delCreateReport(row){
                console.log(row)
                this.$msgbox.confirm(`確定進行[删除]操作?`, '提示', {
                    confirmButtonText: '確定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$http.delete('/claim/ccid/header/del/' + row.id).then(res => {
                        if (res.success) {
                            this.$toast({ tips: '删除成功' })
                            this.getCcidList(this.pageNum)
                        }
                    })
                }).catch(() => {
                
                })
            }
        }
    }
</script>

<style scoped>

</style>
