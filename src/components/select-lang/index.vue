<template>
<div>
    <el-dialog :visible.sync="show" title="Lang" :close-on-click-modal="false">
        <div style="width: 500px;margin: auto;margin-bottom: 20px">
            <el-input placeholder="請輸入語言編碼" @keyup.enter.native="onSubmit()" v-model="searchInfo.languageCode" class="input-with-select">
                <el-button slot="append" icon="el-icon-search" @click="onSubmit()"></el-button>
            </el-input>
        </div>
        <el-table :empty-text="emptyText" :data="tableData">
            <el-table-column property="languageCode" label="語言編碼"></el-table-column>
            <el-table-column property="description" label="描述" ></el-table-column>
            <el-table-column
                label="operation"
                width="100">
                <template slot-scope="scope">
                    <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                        <i class="el-icon-check" @click="checkedLang(scope.$index,scope.row)"></i>
                    </span>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :total="total" @current-change="onSubmit">
        </el-pagination>
    </el-dialog>
</div>
</template>
<script>
export default {
    data(){
        return{
            emptyText:'暫無數據',
            show:false,
            searchInfo: {},
            tableData: [],
            total: 0
        }
    },
    props: {
        search: {
            type: Object,
            default: () => {
                return {
                    languageCode: ''
                }

            }
        }
    },

    methods: {
        /**
         * dialog 组件调用方式： 
         * 1、父级用v-if在html里加载组件,
         * 2、父级调用子级的init方法，将数据初始化，prop传给data等
         * 
         */
        init(){
            this.show = true;
            this.searchInfo = this.$utils.copy(this.search);
            this.onSubmit();
        },
        onSubmit(page){
            let params = this.searchInfo;
            this.$utils.trim(params);
            params.page = {
                pageNum: page ? page : 1,
                pageSize: 10
            }
            this.emptyText = '數據加載中'
            this.$http.post('/ref/languageByCode', params).then(res => {
                if (res.success) {
                    this.tableData = res.data.list;
                    this.total = res.data.total;
                    if(! this.tableData || this.tableData.length == 0){
                        this.emptyText = '暫無數據';
                    }
                }
            })
        },
        checkedLang(index, row){
            this.$emit('checkLang', row);
            this.show = false;
        }
    }
    
}
</script>