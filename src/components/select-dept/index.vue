<template>
<div>
    <el-dialog :visible.sync="show" width="700px" title="Dept" :close-on-click-modal="false">
        <div style="width: 520px;margin: auto;margin-bottom: 20px">
            <el-input v-model.number="searchInfo.deptId" @keyup.enter.native="onSubmit()" placeholder="Dept Id" style="width: 100px;"></el-input>
            <el-input v-model.trim="searchInfo.dept" @keyup.enter.native="onSubmit()" placeholder="Dept Name" style="width: 180px;"></el-input>
            <el-button type="primary" @click="onSubmit()">查詢</el-button>
            <span class="clear-search" @click="clearSearch()">清除搜索</span>
        </div>
        <el-table :empty-text="emptyText" :data="tableData">
            <el-table-column property="deptId" label="Dept Id" width="100px"></el-table-column>
            <el-table-column property="dept" label="Dept Name"></el-table-column>
            <el-table-column
                label="operation"
                width="100px">
                <template slot-scope="scope">
                    <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                        <i class="el-icon-check" @click="checkedDept(scope.$index,scope.row)"></i>
                    </span>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :total="total" @current-change="onSubmit" :current-page="currentPage">
        </el-pagination>
    </el-dialog>
</div>
</template>
<script>
import qs from 'qs'

export default {
    data(){
        return{
            emptyText:'暫無數據',
            show:false,
            searchInfo: {},
            tableData: [],
            total: 0,
            currentPage: 1
        }
    },
    props: {
        search: {
            type: Object,
            default: () => {
                return {
                    deptId: '',
                    dept: ''
                }

            }
        }
    },

    methods: {
        /**
         * dialog 组件调用方式： 
         * 1、父级用v-if在html里加载组件,
         * 2、父级调用子级的init方法，将数据初始化，prop传给data等
         * 
         */
        init(){
            this.show = true;
            this.searchInfo = this.$utils.copy(this.search);
            this.onSubmit();
        },
        clearSearch(){
            this.searchInfo = {
                deptId: '',
                dept: ''
            };
            this.onSubmit();
        },
        onSubmit(page){
            let params = this.searchInfo;
            params.dept = params.dept ? params.dept.trim() : params.dept;
            params.page = {
                pageNum: page ? page : 1,
                pageSize: 10
            }
            this.emptyText = '數據加載中'
            this.$http.post('/ref/getRefSocietyContactDept', params).then(res => {
                if (res.success) {
                    this.tableData = res.data.list;
                    this.total = res.data.total;
                    this.currentPage = page ? page : 1;
                    if(! this.tableData || this.tableData.length == 0){
                        this.emptyText = '暫無數據';
                    }
                }
            })
        },
        checkedDept(index, row){
            this.$emit('checkDept', row);
            this.show = false;
        }
    }
    
}
</script>