<template>
    <div>
        <el-form :inline="true" :model="searchForm" ref="searchForm" :rules="rules" class="demo-form-inline" @keyup.enter.native="getList(1)">
            <el-form-item label="dist no" prop="distNo">
                <el-input v-model.trim="searchForm.distNo" placeholder="" style="width: 100px;"></el-input>
            </el-form-item>
            <el-form-item label="soc no" prop="societyCode">
                <el-input v-model.number="searchForm.societyCode" placeholder="雙擊查詢" @dblclick.native="getSocName()" @change="changeSoc" style="width: 100px;" readonly></el-input>
            </el-form-item>
            <el-form-item label="name" prop="societyName">
                <el-input v-model.trim="searchForm.societyName" placeholder="" disabled style="width: 150px;"></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="getList(1)" v-if="isAuth('royalties:society:find')">搜索</el-button>
            </el-form-item>
            <el-form-item>
                <el-button type="success" @click="add" v-if="isAuth('royalties:society:add') && addFlag">新增</el-button>
            </el-form-item>
            <el-form-item>
                <span class="clear-search" @click="clearSearch()" v-if="isAuth('royalties:society:find')">清除搜索</span>
            </el-form-item>
        </el-form>
        <el-table
            :data="tableData"
            border
            stripe
            style="width: 100%"
            :empty-text="emptyText">
            <el-table-column
                type="index"
                label="序號"
                width="80px">
            </el-table-column>
            <el-table-column
                prop="distNo"
                label="dist no"
                width="100px">
            </el-table-column>
            <el-table-column
                prop="tranType"
                label="type"
                width="80px">
            </el-table-column>
            <el-table-column
                prop="tranDescpt"
                label="description"
                width="330px">
                <template slot="header" slot-scope="scope">
                    <span title="description">description</span>
                </template>
                <template slot-scope="scope">
                    <span :title="scope.row.tranDescpt">{{scope.row.tranDescpt}}</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="tranAmt"
                label="amount">
            </el-table-column>
            <el-table-column
                prop="nonTaxableIncomeInd"
                label="Non-Roy/Non-Tax.Income">
            </el-table-column>
            <el-table-column
                prop="commissionInd"
                label="Apply Comm">
            </el-table-column>
            <el-table-column
                prop="withheldTaxInd"
                label="Apply Tax">
            </el-table-column>
            <el-table-column
                prop="addAfterDistInd"
                label="After Dist">
            </el-table-column>
            <el-table-column label="operation" width="200px">
              <template slot-scope="scope"> 
                <el-button @click="edit(scope.row)" type="text" size="small" v-if="isAuth('royalties:society:change') && scope.row.payStatus != 'P'">編輯</el-button>
                <el-button @click="del(scope.row)" type="text" size="small"  v-if="isAuth('royalties:society:del') && scope.row.payStatus != 'P'">刪除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"  :current-page.sync='searchForm.page_num'
            :total="total" @current-change="handleCurrentChange">
        </el-pagination>
        <el-dialog :title="dialogTitle" :visible.sync="dialogFormVisible" width="480px" @close="clearForm" :close-on-click-modal="false">
            <el-form :inline="true" :model="formData" :rules="rules2" ref="editForm" label-width="120px" label-position="right" >
                <el-form-item style="dialog-el-form" label="Type" prop="tranType">
                    <el-input v-model="formData.tranType" ref="typeInput" placeholder="請雙擊" :readonly="true" @dblclick.native="getTypeList()"></el-input>
                </el-form-item>
                <el-form-item label="Description">
                    <el-input v-model="formData.tranDescpt" placeholder="請輸入"></el-input>
                </el-form-item>
                <el-form-item label="Amount" prop="tranAmt">
                    <el-input v-model="formData.tranAmt" placeholder="請輸入" onkeyup="this.value=this.value.match(/\d+\.?\d{0,6}/);this.dispatchEvent(new Event('input'))"></el-input>
                </el-form-item>
                <el-form-item label="" class="is-required">
                    <el-checkbox-group v-model="checkList" @change="selectNonTax">
                        <el-checkbox style="margin-left:60px;" 
                        v-for="item in checkGroup" :disabled="item.disabled" :label="item.label" :key="item.label" >{{item.label}}</el-checkbox>
                        <!-- <el-checkbox style="margin-left:60px;" label="Non-Roy/Non-Tax.Income"  key="Non-Roy/Non-Tax.Incomet">Non-Roy/Non-Tax.Income</el-checkbox>
                        <el-checkbox style="margin-left:60px;" label="Apply Comm" key="Apply Comm">Apply Comm</el-checkbox>
                        <el-checkbox style="margin-left:60px;" label="Apply Tax" key="Apply Tax">Apply Tax</el-checkbox>
                        <el-checkbox style="margin-left:60px;" disabled label="After Dist" key="After Dist">After Dist</el-checkbox> -->
                    </el-checkbox-group>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="cancelEdit">取 消</el-button>
                <el-button type="primary" @click="editConfirm">確 定</el-button>
            </span>
        </el-dialog>
        <el-dialog title="Transaction Type" :visible.sync="showTypeDialog" width="50%" :close-on-click-modal="false">
            <el-table :empty-text="emptyText"   :data="typeList" stripe style="width: 100%">
                <el-table-column prop="tranType" label="tranType">
                </el-table-column>
                <el-table-column prop="tranDescpt" label="description" min-width="350">
                </el-table-column>
                <el-table-column label="option" align="center">
                    <template slot-scope="scope">
                        <i class="el-icon-check" @click="chooseType(scope.row)"></i>
                    </template>
                </el-table-column>
            </el-table>
        </el-dialog>
        <society-name ref="society" :societyCode='societyCode' @checkSociet='checkSociet' @flag='flag1=true'></society-name>
    </div>
</template>

<script>
    import AddDialog from './add-member';
    import societyName from './components/societyName';
    export default {
        name: 'member',
        components:{
            AddDialog,
            societyName
        },
        data(){
            return {
                searchForm:{
                    page_num:1,
                    page_size:10,
                    distNo:'',
                    societyCode:'',
                    societyName:''
                },
                total:0,
                tableData:[],
                ipNoPage:1,
                emptyText: '暫無數據',
                dialogFormVisible: false,
                dialogTitle: '新增',
                societyCode:'',
                checkList: [],
                checkGroup: [
                    {label: 'Non-Roy/Non-Tax.Income', disabled: undefined, change: 'selectNonTax',property:'nonTaxableIncomeInd'},
                    {label: 'Apply Comm', disabled: undefined, change: 'selectApply',property:'commissionInd'},
                    {label: 'Apply Tax', disabled: undefined, change: 'selectApply',property:'withheldTaxInd'},
                    {label: 'After Dist', disabled: 'disabled', change: '',property:'addAfterDistInd'},
                ],
                showTypeDialog: false,
                typeList: [],
                formData: {
                    tranAmt: 0
                },
                distInfo: {
                    distNo: '',
                    status: -1
                },
                addFlag: true,
                flag1:true,
                rules: {
                    distNo: [
                    { required: true, message: '请输入distNo', trigger: 'change' }
                    ],
                    societyCode: [
                    { required: true, message: 'societyCode', trigger: 'change' }
                    ]
                },
                rules2: {
                    tranType: [
                    { required: true, message: '请输入类型', trigger: 'blur' }
                    ],
                    tranAmt: [
                    { required: true, message: '请输入金额', trigger: 'blur' }
                    ]
                },
            }
        },
        mounted(){
            console.log('this is mounted');
            console.log(this.$route.query)
            if(this.$route.query.distNo){
                this.searchForm.distNo = this.$route.query.distNo
                this.searchForm.societyCode = this.$route.query.societyCode
                this.searchForm.societyName = this.$route.query.societyName
                this.getList(1);
            }
            this.$nextTick( () => {
                // this.addListener();
            })
        },
        methods:{
            getSocName(){
                if(this.flag1){
                    this.societyCode = ''
                    this.flag1=false
                    this.$refs.society.socDataD()
                }
            },
            changeSoc(data){
                if(data){
                    if(this.flag1){
                    this.societyCode = data
                    this.flag1=false
                    this.$nextTick(()=>{
                        this.$refs.society.socDataC()
                    })
                    }
                }else{
                    this.$set(this.searchForm,'societyName','')
                }
            },
            checkSociet(data){
                this.flag1=true
                this.$set(this.searchForm,'societyCode',data.societyCode)
                this.$set(this.searchForm,'societyName',data.societyName)
            },
            close(){
                this.showExport=false
                this.societyData = {
                    autopayNo: "",
                    autopayDescription: "",
                    societyCode:'',
                    name:'',
                    distNo:''
                }
            },
            clearSearch(){
                this.searchForm = {
                    page_num:1,
                    page_size:10,
                    distNo:'',
                    societyCode:'',
                    societyName:''
                }
                this.getList();
            },
            getList(page){
                this.$refs.searchForm.validate(validate => {
                    if (validate) {
                        this.searchForm.page_num=page?page:this.searchForm.page_num
                        if (this.searchForm.distNo||this.searchForm.societyCode||this.searchForm.societyName) {
                            
                            this.searchForm.page_num=1
                        }
                        let params = this.searchForm
                        this.emptyText = '數據加載中';
                        this.$http.get('/transaction/statement/society',{params}).then(res => {
                            console.log(res)
                            if(res.success){
                                this.tableData = res.data.data.list
                                this.total = res.data.data.total
                                this.searchForm.page_num=res.data.data.pageNum
                                if(! this.tableData || this.tableData.length == 0){
                                    this.emptyText = '暫無數據';
                                } else {
                                    if(this.tableData[0].getPayStatus == 'P'){
                                        this.addFlag = false
                                    } else {
                                        this.addFlag = true
                                    }
                                }
                            }
                        })
                    }
                })
            },
            handleCurrentChange(val){
                this.searchForm.page_num = val
                this.getList()
            },
            getNameByBaseNo(){
                let reg = /^[0-9]*$/;
                let societyCode = this.searchForm.societyCode
                if(!societyCode){
                    return;
                }
                if(!reg.test(societyCode)){
                    this.$toast({tips:'soc no必須為數字'})
                    return
                }
                this.$http.get('/transaction/statement/ref/society/'+societyCode).then(res => {
                    if(res.success){
                        this.searchForm.societyName = res.data.societyName
                    }
                })
            },
            add(){
                // let data = this.searchForm

                if(!this.searchForm.distNo){
                    this.$toast({ tips: 'distNo不能為空！' })
                    return;
                }

                console.log(this.formData)
                let _this = this
                if(this.distInfo.status == -1 || this.distInfo.distNo != this.searchForm.distNo){
                    this.$http.get('/dist/findParamInfoStatus',{params: {distNo: this.searchForm.distNo}}).then(res => {
                        console.log(res)
                        if(res.success){
                            _this.distInfo.distNo = _this.searchForm.distNo
                            _this.distInfo.status = res.data

                            _this.dialogTitle = '新增SOCIETY Transaction Statment'
                            _this.dialogFormVisible = true
                            _this.formData = _this.$utils.copy(_this.searchForm)
                            _this.formData.id = undefined

                            if(_this.distInfo.status == 7){
                                _this.checkList = ['After Dist']
                            } else {
                                _this.checkList = []
                            }
                        } else {
                            _this.distInfo.distNo = _this.searchForm.distNo
                            _this.distInfo.status = -1
                            _this.checkList = []

                            this.$message({
                                message: '分配編號不存在',
                                type: 'warning'
                            });
                        }
                    })
                } else {
                    _this.dialogTitle = '新增SOCIETY Transaction Statment'
                    _this.dialogFormVisible = true
                    _this.formData = _this.$utils.copy(_this.searchForm)
                    _this.formData.id = undefined

                    if(_this.distInfo.status == 7){
                        _this.checkList = ['After Dist']
                    } else {
                        _this.checkList = []
                    }
                }
            },

            edit(row) {
                this.dialogTitle = '编辑SOCIETY Transaction Statment'
                this.dialogFormVisible = true
                this.formData = this.$utils.copy(row)

                let _this = this
                _this.checkGroup.forEach(c => {
                    if(_this.formData[c.property] == 'Y'){
                        _this.checkList.push(c.label)
                    }
                })

                if(_this.checkList.some(item => {
                    return item == _this.checkGroup[1].label || item == _this.checkGroup[2].label
                })){
                    _this.checkGroup[0].disabled = 'disabled'
                } else if(_this.checkList.includes(_this.checkGroup[0].label)){
                    _this.checkGroup[1].disabled = 'disabled'
                    _this.checkGroup[2].disabled = 'disabled'
                }
            },
            editConfirm() {
                this.$refs.editForm.validate(valid => {
                    if (valid) {
                        this.checkList.indexOf('Non-Roy/Non-Tax.Income') > -1 ? this.formData.nonTaxableIncomeInd = 'Y' : this.formData.nonTaxableIncomeInd = 'N'
                        this.checkList.indexOf('Apply Comm') > -1 ? this.formData.commissionInd = 'Y' : this.formData.commissionInd = 'N'
                        this.checkList.indexOf('Apply Tax') > -1 ? this.formData.withheldTaxInd = 'Y' : this.formData.withheldTaxInd = 'N'
                        this.checkList.indexOf('After Dist') > -1 ? this.formData.addAfterDistInd = 'Y' : this.formData.addAfterDistInd = 'N'
                        let _message = ''
                        if(this.formData.commissionInd == 'Y' || this.formData.withheldTaxInd == 'Y'){
                            _message = '請重新執行分配'
                        }
                        this.$http.post('/transaction/statement/society', this.formData).then(res => {
                            if (res.success) {
                                if (res.data.code === 200) {
                                    this.dialogFormVisible = false
                                    this.getList(this.currentpage)
                                    this.$message({
                                        message: '存檔成功' + _message,
                                        type: 'success', duration: 3000,
                                    })

                                } else {
                                    this.$message({
                                    message: res.data.message,
                                    type: 'warning'
                                    })
                                }
                            }
                        })
                    }
                })
            },
            
            
            getTypeList() {
                this.emptyText1 = '數據加載中';
                this.$http.get('/transaction/statement/tran/type/all').then(res => {
                        if (res.success) {
                        this.showTypeDialog = true
                        this.typeList = res.data
                        console.log(res.data)
                        if(! this.typeList || this.typeList.length == 0){
                            this.emptyText1 = '暫無數據';
                        }
                    }
                    
                })
            },
            chooseType(row) {
                this.$set(this.formData, 'tranType', row.tranType);
                this.$set(this.formData, 'tranDescpt', row.tranDescpt);
                this.showTypeDialog = false 
                this.$refs.typeInput.focus()
                this.$refs.typeInput.blur()
            },
            showSocList() {
                this.showSocDialog = true
                this.socCode = this.socCode || this.formData.societyCode
                this.getSocList()
            },
            getSocList(page = 1) {
            let ajaxData = {
                data: {
                    societyCode: this.socCode
                },
                page: {
                pageNum: page,
                pageSize: 10    
                }
            }
            this.emptyText = '數據加載中';
                this.$http.post('/ref/society/getSocietyList', ajaxData).then(res => {
                    console.log(res)
                    if (res.success) {
                    this.socList = res.data.list
                    this.socTotal = res.data.total
                    if(! this.socList || this.socList.length == 0){
                        this.emptyText = '暫無數據';
                    }
                }
                
            })

            },
            chooseSoc(row) {
                this.showSocDialog = false
                this.$set(this.formData, 'societyCode', row.societyCode);
                this.$set(this.formData, 'societyName', row.societyName);
                this.$refs.socInput.focus()
                this.$refs.socInput.blur()
                if (row.societyName) {
                    this.$refs.socNameInput.focus()
                    this.$refs.socNameInput.blur()
                }
            },
            handleSocChange(val) {
                this.getSocList(val)
            },
            closeSoc() {
                this.socCode = ''
            },
            clearForm() {
                this.$refs.addDialog && this.$refs.addDialog.clearValidate()
                this.checkList = []
                this.checkGroup.forEach((ch,index) => {
                    if(index < 3){
                        ch.disabled = undefined
                    }
                })
            },
            cancelEdit () {
                this.dialogFormVisible = false
                this.$refs.editForm.resetFields()
            },
            selectNonTax(checked){
                if(checked.includes(this.checkGroup[0].label)){
                    this.checkGroup[1].disabled = 'disabled'
                    this.checkGroup[2].disabled = 'disabled'
                    this.checkList = checked
                } else if(checked.includes(this.checkGroup[1].label) || checked.includes(this.checkGroup[2].label)){
                    this.checkGroup[0].disabled = 'disabled'
                    this.checkList = checked
                } else {
                    this.checkGroup.forEach(item => {if(item.label != 'After Dist'){item.disabled = undefined}})
                    this.checkList = checked
                }
            },  
            del(row){
                this.$msgbox.confirm("是否確認刪除?", "提示", {
                    confirmButtonText: "確定",
                    cancelButtonText: "取消",
                    type: "warning",
                }).then(() => {
                    this.$http.delete('/transaction/statement/society/' + row.id).then(res => {
                        console.log(res)
                        this.getList()
                        if (res.data.code == 200) {
                            this.$message({
                            message: '刪除成功',
                            type: 'success'
                        });
                        } else {
                            this.$toast({ tips: res.data.message })
                        }
                        })
                    })
                return;
            }
        },
        //   beforeRouteEnter (to, from, next) {
        //     next(vm => {
        //         vm.getList(vm.searchForm.page_num)
        //     })
        // }
    }
</script>

<style scoped>

</style>
