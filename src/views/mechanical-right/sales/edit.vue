<template>
    <div class="p-t-40">
        <top-bar :noShow="false" @save="saveReset"></top-bar>
        <el-collapse v-model="activeNames">
            <!-- baseInfo -->
            <el-collapse-item title="Base Info" class="step-jump" name="1">
                <el-form :inline="true" ref="form" :model="baseInfo" :rules="rules" class="p-t-20" label-width="146px">
                    <div>
                        <el-form-item label="Label ID" prop="labelId">
                           <el-input type="text" v-model="baseInfo.labelId" @change="labelIdChange"></el-input>
                        </el-form-item>
                        <el-form-item label="Label Name" prop="labelName">
                            <el-input type="text" v-model="baseInfo.labelName"></el-input>
                        </el-form-item>
                    </div>
                    <div>
                        <el-form-item label="Producer Code" prop="producerCode">
                            <el-input type="text" v-model="baseInfo.producerCode" placeholder="" readonly></el-input>
                        </el-form-item>
                        <el-form-item label="Producer Name">
                            <el-input type="text" v-model="baseInfo.producerName" placeholder="" readonly></el-input>
                        </el-form-item>
                        <el-form-item label="SC ID" prop="scID">
                            <el-input type="text" v-model="baseInfo.id" disabled></el-input>
                        </el-form-item>
                        <el-form-item label="SC No" prop="scNo">
                            <el-input type="text" v-model="baseInfo.scNo"></el-input>
                        </el-form-item>
                        <el-form-item label="Type" prop="scTypeCode">
                            <el-input type="text" v-model="baseInfo.scTypeCode" placeholder="雙擊查詢" @dblclick.native="getType()" readonly></el-input>
                        </el-form-item>
                        <el-form-item label="Country" prop="countryCode">
                            <el-input type="text" style="width: 80px;" v-model="baseInfo.countryCode" placeholder="雙擊查詢" @dblclick.native="getCountry()" readonly></el-input>
                            <el-input type="text" style="width: 130px;" v-model="baseInfo.countryName" readonly></el-input>
                        </el-form-item>
                        <!-- <el-form-item>
                            <el-input type="text" style="width: 140px;" v-model="baseInfo.countryName" readonly></el-input>
                        </el-form-item> -->
                    </div>
                    <div>
                        <el-form-item label="English Title">
                            <el-input type="text" style="width: 600px;" v-model="baseInfo.scTitleEn"></el-input>
                        </el-form-item>
                        <el-form-item label="Chinese Title">
                            <el-input type="text" style="width: 600px;" v-model="baseInfo.scTitleCh"></el-input>
                        </el-form-item>
                    </div>
                    <div>
                        
                        <el-form-item label="Release Date" prop="releaseDate">
                            <date-picker v-model="baseInfo.releaseDate" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd"></date-picker>
                        </el-form-item>
                        <el-form-item label="Receive Date" prop="receiveDate">
                            <date-picker v-model="baseInfo.receiveDate" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd"></date-picker>
                        </el-form-item>
                    </div>
                    <div>
                        <el-form-item label="Tracks" prop="tracks" required>
                            <el-input type="number" placeholder="" v-model="baseInfo.tracks"></el-input>
                        </el-form-item>
                        <el-form-item label="Duration" prop="durationM">
                            <el-input style="width: 60px" v-model.number="baseInfo.durationM" placeholder=""></el-input>
                        </el-form-item>
                        :
                        <el-form-item style="margin-left: 5px" prop="durationS">
                            <el-input style="width: 60px"  v-model.number="baseInfo.durationS" placeholder=""></el-input>
                        </el-form-item>
                    </div>
                </el-form>
            </el-collapse-item>
            <!-- right -->
            <el-collapse-item title="Work List" class="step-jump" name="2">
                 <el-table :empty-text="emptyText" stripe :data="salesTable" border>
                    <el-table-column label="check" width="80px">
                        <template slot-scope="scope">
                            <div style="text-align: center;">
                                <span class="flag" :class="scope.row.check || 'WHITE'"></span>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="Seq">
                        <template slot-scope="scope">
                            <el-input type="text" v-model="scope.row.seqNo" readonly></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="Title">
                        <template slot-scope="scope">
                            <el-input type="text" placeholder="雙擊查詢" v-model="scope.row.workTitle" @dblclick.native="selectWork(scope.$index)" readonly></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="workId" label="Work No">
                        <template slot-scope="scope">
                            <el-input type="text" placeholder="雙擊查詢" v-model="scope.row.workId" @dblclick.native="selectWork(scope.$index)" readonly></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="workSocietyCode" label="Soc">
                        <template slot-scope="scope">
                            <el-input type="text" placeholder="雙擊查詢" v-model="scope.row.workSocietyCode" @dblclick.native="selectWork(scope.$index)" readonly></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="durationM" label="Duration" width="142px">
                        <template slot-scope="scope">
                            <el-input type="text" placeholder="" v-model="scope.row.durationM" style="width: 60px;" readonly></el-input>
                            :
                            <el-input type="text" placeholder="" v-model="scope.row.durationS" style="width: 60px;" readonly></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="isrc" label="ISRC">
                        <template slot-scope="scope">
                            <el-input type="text" placeholder="" v-model="scope.row.isrc"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="remitCode" label="Remit Code">
                        <template slot-scope="scope">
                            <el-input type="text" v-model="scope.row.remitCode"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="scClaimDate" label="Claim Date">
                        <template slot-scope="scope">
                            <date-picker v-model="scope.row.scClaimDate" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd" :disabled="!scope.row.scClaimFlag"></date-picker>
                        </template>
                    </el-table-column>
                    <el-table-column prop="scClaimFlag" label="Claim">
                        <template slot-scope="scope">
                            <el-checkbox v-model="scope.row.scClaimFlag" @change="claimChange(scope.row.scClaimFlag, scope.$index)"></el-checkbox>
                        </template>
                    </el-table-column>
                    <el-table-column label="opteration">
                        <template slot-scope="scope">
                            <span class="operation">
                                <i class="el-icon-delete" @click="deleteData(scope.row, scope.$index)"></i>
                            </span>
                        </template>
                    </el-table-column>
                </el-table>
                <div class="searchCriteria">
                    <span>Search Criteria</span>
                    <el-select v-model="searchData.type" placeholder="" style="width: 150px;">
                      <el-option
                        v-for="item in searchOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                      </el-option>
                    </el-select>
                    <el-input style="width: 400px;" @keyup.enter.native="search()" v-model="searchData.value" placeholder=""></el-input>
                    <el-button type="primary" @click="search()">搜索</el-button>

                </div>
                <div class="add-new">
                    <el-button type="primary" @click="addData(salesTable)">添加</el-button>
                    <el-button type="success" @click="uploadData()">導入</el-button>
                </div>
            </el-collapse-item>
            <!-- end -->
        </el-collapse>
        <select-type ref="selectType" @checkedType="checkedType"></select-type>
        <select-work v-if="workTableVisible" ref="selectWorkCom" :search="workSearch" @checkWork="checkWork"></select-work>
        <select-country v-if="countryShow" :search="countrySearch" ref="selectCountry" @checkCountry="checkCountry"></select-country>
        <upload ref="upload" v-if="uploadingShow" @update='update'></upload>
    </div>
</template>
<script>
import selectCountry from '@/components/select-country'
import selectWork from '@/components/select-work';
import selectType from './components/selectType'
import upload from './components/uploading'
export default {
    data(){
        return {
            searchOptions: [
                {
                    value: 'workTitle',
                    label: 'Work Title'
                },
                {
                    value: 'workId',
                    label: 'Work No'
                },
                {
                    value: 'isrc',
                    label: 'ISRC'
                },
                {
                    value: 'remitCode',
                    label: 'Remit Code'
                },
            ],
            searchData:{
                type:'workTitle',
                value:''
            },
            uploadingShow:false,
            id: '',
            baseInfo: {
                producerCode: '',
                producerName: '',
                scID: '',
                scNo: '',
                scTypeCode: '',
                countryName: '',
                countryCode: '',
                scTitleEn: '',
                scTitleCh: '',
                labelId: '',
                labelName: '',
                releaseDate: '',
                receiveDate: '',
                tracks: '',
                durationM: '',
                durationS: '',
            },
            activeNames: ['1', '2'],
            salesTable: [],
            emptyText:'暫無數據',
            salesTableA: [],
            rules: {
                tracks: [{required: true, message: '請輸入tracks', trigger: 'blur'}],
                labelId: [{required: true, message: '請輸入labelId', trigger: 'blur'}],
                producerCode: [{required: true, message: '請輸入producerCode', trigger: 'blur'}],
                // labelName: [{required: true, message: '請輸入labelName', trigger: 'blur'}],
                // receiveDate: [{required: true, message: 'receiveDate', trigger: 'blur'}],
                // producerId: [{required: true, message: '請選擇Producer Code', trigger: 'change'}],
                // distNo: [{required: true, message: '請輸入Dist No', trigger: 'blur'}],
                // amount: [{required: true, message: '請輸入Amount', trigger: 'blur'}],
                // exchangeRate: [{required: true, message: '請輸入Exchange Rate', trigger: 'blur'}],
                // currency: [{required: true, message: '請選擇Currency', trigger: 'change'}],
                // batchNo: [{required: true, message: '請輸入Batch No', trigger: 'blur'}],
                scNo: [{required: true, message: '請輸入Sc No', trigger: 'blur'}],
                // typeCode: [{required: true, message: '請輸入Sc Type', trigger: 'blur'}],
                // countryCode: [{required: true, message: '請選擇Country', trigger: 'change'}],
                // releaseDate: [{required: true, message: '請輸入Release Date', trigger: 'blur'}],
                // recevieDate: [{required: true, message: '請輸入Receive Date', trigger: 'blur'}]
            },
            countrySearch: {},
            countryShow: false,

            workTableVisible: false,
            workSearch: {},
            editIndex: 0,

            deleteList: [],
            filter:false
        }
    },
    components: {
        selectCountry,
        selectWork,
        selectType,
        upload
    },
    created(){
        this.init();
    },
    watch:{
        salesTable(v){
            console.log(v)
            if(!this.filter){
                this.salesTableA=v
            }
        }
    },
    computed: {
        // itemListFilter() {
        //     return this.salesTable
        // },
    },
    methods: {
        search(){
            console.log('salesTable')
            console.log(this.salesTable)
            if(this.searchData.value){
                this.salesTable=this.salesTableA
                let valueInput=this.searchData.value || '';
                let filterData= this.salesTable.filter((item)=> {
                    //遍历数组，返回值为true保留并复制到新数组，false则过滤掉
                    let inputValue=new RegExp(`(.*)(${valueInput.split('').join(')(.*)(')})(.*)`, 'i');
                    let itemData=item[this.searchData.type].toString()
                    return itemData.match(inputValue);
                })
                this.salesTable=filterData
                this.filter=true
            }else{
                this.salesTable=this.salesTableA

            }
        },
        update(data){
            data.forEach(item=>{
                item.action = 'ADD'
                this.salesTable.push(item)
            })
            this.$toast({tips: '導入成功'});
        },
        /**
         * 选取作品
         */
        selectWork(index, row) {
            this.editIndex = index;
            this.workTableVisible= true;
            this.workSearch = {
            }
            this.$nextTick( () => {
                this.$refs.selectWorkCom.init();
            })
        },
        checkWork(info){
            console.log('info: ', info);
            this.$set(this.salesTable[this.editIndex], 'workTitle', info.title ? info.title : info.title_en);
            this.$set(this.salesTable[this.editIndex], 'workId', info.work_id);
            this.$set(this.salesTable[this.editIndex], 'workSocietyCode', info.work_society_code);
            this.$set(this.salesTable[this.editIndex], 'durationM', info.duration_m);
            this.$set(this.salesTable[this.editIndex], 'durationS', info.duration_s);
            this.$set(this.salesTable[this.editIndex], 'isrc', info.isrc[0] || '');
            this.$set(this.salesTable[this.editIndex], 'workUniqueKey', info.work_unique_key);
        },
        getType(){
            this.$refs.selectType.init();
        },
        checkedType(data){
            console.log(data)
            this.baseInfo.scTypeCode = data.typeCode
        },
        labelIdChange(){
            if(!this.baseInfo.labelId){
                this.baseInfo.producerCode=''
                this.baseInfo.producerName=''
                this.baseInfo.labelName=''
                return;
            }
            let ajaxData = {
                page_num: 1,
                page_size: 10,
                id: this.baseInfo.labelId
            };
            this.$http.get('/distMrdScLabel', {params: ajaxData}).then( res => {
                if(res.success){
                    if(!res.data.data.list.length){
                        this.$toast({tips: '無此Label ID'});

                    }
                    let label = res.data.data.list.length ? res.data.data.list[0] : {};
                    this.$set(this.baseInfo, 'producerCode', label.producerCode);
                    this.$set(this.baseInfo, 'producerName', label.producerName);
                    this.$set(this.baseInfo, 'labelName', label.labelName);
                }
            })
        },
        claimChange(value, index){
            if(value){
                this.salesTable[index].scClaimDate = this.$utils.DATE(new Date(), 'yyyy-MM-dd')
            }else{
                this.salesTable[index].scClaimDate = ''
            }
        },
        init(){
            this.id = this.$route.query.id;
            if(this.id){
                this.queryInfo();
            }else{
                this.baseInfo = {
                    producerCode: '',
                    producerName: '',
                    scID: '',
                    scNo: '',
                    scTypeCode: '',
                    countryName: '',
                    countryCode: '',
                    scTitleEn: '',
                    scTitleCh: '',
                    labelId: '',
                    labelName: '',
                    releaseDate: '',
                    recevieDate: '',
                    tracks: '',
                    durationM: '',
                    durationS: '',
                }
            }


        },
        queryInfo(){
            let temp = JSON.parse(localStorage.getItem('salesDetail'));
            this.salesTable = this.$utils.copy(temp.distMrdScWorkList || []);
            this.salesTable.forEach( (item, index) => {
                if(item.scClaimFlag == 'Y'){
                    this.salesTable[index].scClaimFlag = true;
                }else{
                    this.salesTable[index].scClaimFlag = false;
                }
                this.salesTable[index].action = 'UPDATE';
            })
            delete temp.distMrdScWorkList;
            this.baseInfo = this.$utils.copy(temp);
            this.$set(this, 'baseInfo', this.baseInfo);
            this.labelIdChange();
        },
        uploadData(){
            this.uploadingShow=true
            this.$nextTick( () => {
                this.$refs.upload.showPop()
            })
        },
        addData(data){
            this.salesTable.push({
                seqNo: '',
                workTitle: '',
                workId: '',
                workSocietyCode: '',
                durationM: '',
                durationS: '',
                isrc: '',
                remitCode: '',
                scClaimDate: '',
                scClaimFlag: false,
                action: 'ADD'
            })
        },
        deleteData(data, index){
            this.$confirm('確認刪除?', '提示', {
                confirmButtonText: '確認',
                cancelButtonText: '取消',
                closeOnClickModal:false,
                type: 'warning'
            }).then( () => {
                let temp = this.$utils.copy(this.salesTable[index]);
                if(temp.action == 'UPDATE'){
                    temp.action = 'DEL';
                    this.deleteList.push(temp);
                }
                
                this.salesTable.splice(index,1)
            })

        },
        saveReset(){
            this.$refs.form.validate( (valid) => {
                if(valid){
                    if(this.baseInfo.scTitleEn || this.baseInfo.scTitleCh){
                        this.saveResetFn();
                    }else{
                        this.$toast({tips: 'English Title與Chinese Title 必填其中之一！'});
                    }
                }
            })
        },
        saveResetFn(){
            let ajaxData = this.$utils.copy(this.baseInfo);

            ajaxData.distMrdScWorkList = this.$utils.copy(this.salesTableA)
            ajaxData.distMrdScWorkList.forEach( (item, index) => {
                if(item.scClaimFlag){
                    ajaxData.distMrdScWorkList[index].scClaimFlag = 'Y';
                }else{
                    ajaxData.distMrdScWorkList[index].scClaimFlag = 'N';
                }
            })
            ajaxData.distMrdScWorkList = ajaxData.distMrdScWorkList.concat(this.deleteList);
            if(ajaxData.distMrdScWorkList.length == 0){
                this.$toast({tips: '請至少錄入一條作品信息'});
                return;
            }
            this.$http.post('/distMrdSc', ajaxData).then( res => {
                if(res.data.code && res.data.code != 200){
                    this.$toast({tips: res.data.message})
                }else{
                    this.$toast({tips: '保存成功'});
                    this.$bus.$emit('closeCurrentTab', () => { this.$router.push({name: 'salesList', query: {update: true}})});
                }
            })
        },

        getCountry(){
            this.countrySearch = {
                countryCode: this.baseInfo.countryCode
            }
            this.countryShow = true;
            this.$nextTick( () => {
                this.$refs.selectCountry.init();
            })
        },
        checkCountry(info){
            this.$set(this.baseInfo, 'countryCode', info.countryCode);
            this.$set(this.baseInfo, 'countryName', info.name);
        },

    }
}
</script>
<style lang="scss" scoped>
    @import '../../../assets/scss/works.scss';
    .searchCriteria{
        position: absolute;
        right: 215px;
        top: 8px;
        text-align: center;
        button{
            padding: 8px 16px;
        }
    }
    .flag{
        margin: 0 auto;
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
    }
    .RED{
        background: red;
    }
    .GREEN{
        background: green;
    }
    .WHITE{
        background: white;
        box-shadow: 0 2px 2px rgba($color: #000000, $alpha: .1);
    }
</style>

