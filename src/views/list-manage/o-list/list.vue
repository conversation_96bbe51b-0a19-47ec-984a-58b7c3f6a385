<template>
  <div class="ipibpox">
    <el-form :inline="true" :model="search" ref="form" class="demo-form-inline"  @keyup.enter.native='searchFn(1)'>
      <el-form-item label="分配代號">
        <el-input v-model="search.distNo" placeholder="distNo" style="width: 100px;"></el-input>
      </el-form-item>
      <el-form-item label="協會代碼">
        <el-input v-model="search.sourceSoc" placeholder="協會代碼" style="width: 100px;"></el-input>
      </el-form-item>
      <el-form-item label="協會名稱">
        <el-input v-model="search.sourceSocName" placeholder="協會名稱" style="width: 100px;"></el-input>
      </el-form-item>
      <el-form-item label="⽔單ID">
        <el-input v-model="search.id" placeholder="⽔單ID" style="width: 100px;"></el-input>
      </el-form-item>
      <el-form-item label="處理人">
        <el-input v-model="search.createUserName" placeholder="處理人" style="width: 120px;"></el-input>
      </el-form-item>
      <el-form-item label="匯款日期">
        <date-picker v-model="search.receiptStartDate" value-format="yyyy-MM-dd" format="yyyyMMdd" placeholder="開始日期" style="width: 130px;">
        </date-picker>
        -
        <date-picker v-model="search.receiptEndDate" value-format="yyyy-MM-dd" format="yyyyMMdd" placeholder="結束日期" style="width: 130px;">
        </date-picker>
      </el-form-item>
      <!--<el-form-item label="isChecked">
                <el-switch
                    v-model="search.isChecked"
                    active-value="1"
                    inactive-value="0"
                    active-color="#13ce66"
                    inactive-color="#e9e9e9">
                </el-switch>
                <el-select v-model="search.isChecked" placeholder="请选择" style="width:100px">
                    <el-option label="全部" value=""></el-option>
                    <el-option label="未处理" :value="0"></el-option>
                    <el-option label="已处理" :value="1"></el-option>
                </el-select>
            </el-form-item>-->

      <el-form-item label="多重排序">
        <el-select v-model="search.orderList" style="width: 400px" multiple placeholder="選擇排序">
          <el-option
            v-for="(item,index) in orderSelections"
            :key="`k_${index||'-1'}`"
            :label="item.name"
            :value="item.value"
            :disabled="!!search.orderList.filter((si) => si.indexOf(item.prop) !== -1).length"
            >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="searchFn(1)" v-if="isAuth('list-manage:o-list:list:find')">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="addNew()" v-if="isAuth('list-manage:o-list:list:add')">添加</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="upload" v-if="isAuth('list-manage:o-list:list:exportin')">上傳清單</el-button>
      </el-form-item>
      <el-form-item>
        <span class="clear-search" @click="clearSearch()" v-if="isAuth('list-manage:o-list:list:find')">清除搜索</span>
      </el-form-item>

    </el-form>
    <el-table :empty-text="tableresult" stripe :data="tableData" border style="width: 100%"
              @row-dblclick="showTableDetail" @row-click="handleRowClick" @sort-change="handleSortChange"
              highlight-current-row ref="listTableRef" class="highlight-row-table">
      <el-table-column prop="distNo" label="分配代號" min-width="90" sortable="custom">
      </el-table-column>
      <el-table-column prop="id" label="水單ID" sortable="custom">
        <template slot-scope="scope">
          <span style="color:red" v-if="scope.row.starMark=='Y'">*</span>
          <span :title="scope.row.id">{{scope.row.id}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="receiptDate" label="匯款日期" min-width="120" sortable="custom">
        <template slot-scope="scope">
          {{formatTime(scope.row.receiptDate)}}
        </template>
      </el-table-column>
      <el-table-column prop="sourceSoc" label="協會代碼" min-width="120" sortable="custom">
      </el-table-column>
      <el-table-column prop="sourceSocName" label="協會名稱" min-width="120" sortable="custom">
      </el-table-column>
      <el-table-column prop="currencyCode" label="幣別" sortable="custom">
      </el-table-column>
      <el-table-column prop="receiptAmount" label="外幣金額" align="right" min-width="100" sortable="custom">
        <template slot-scope="scope">
          <span :title="scope.row.receiptAmount">{{formatThousand(scope.row.receiptAmount, 2)}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="localAmount" label="台幣金額" align="right" min-width="100" sortable="custom">
        <template slot-scope="scope">
          <span :title="scope.row.localAmount">{{formatThousand(scope.row.localAmount, 0)}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="distAmount" label="入賬金額" align="right" min-width="100" sortable="custom">
        <template slot-scope="scope">
          <span :title="scope.row.distAmount">{{formatThousand(scope.row.distAmount, 0)}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="chargeAmount" label="手續費" align="right" min-width="90" sortable="custom">
        <template slot-scope="scope">
          <span :title="scope.row.chargeAmount">{{formatThousand(scope.row.chargeAmount, 2)}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="updateUserName" label="處理人" sortable="custom">
      </el-table-column>
      <!--<el-table-column
                prop="checked"
                label="處理狀态"
                min-width="90">
                <template slot-scope="scope">{{scope.row.checked===0?'未处理':'已处理'}}</template>
            </el-table-column>-->
      <el-table-column prop="remark" label="備註" >
        <template slot-scope="scope">
          <span class="breakword" :title="scope.row.remark">{{scope.row.remark}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="" fixed="right" label="operation" min-width="120">
        <template slot-scope="scope">
          <span style="width: 100%;display: flex;felx-start:row;text-align: center;justify-content:space-between;cursor: pointer">
            <span style="width: 50%;display: inline-block;text-align: center;cursor: pointer">
              <el-button @click="showTableBillto(scope.row.id, scope.row)" type="text" size="small" v-if="isAuth('list-manage:o-list:list:look')">查看檔案</el-button>
            </span>
            <el-dropdown style="display:flex; align-items:center;cursor: pointer;line-height: 16px;">
              <span class="a-blue">
                更多
              </span>
              <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item @click.native="addDetail(scope.row)" type="text" size="small" v-if="isAuth('list-manage:o-list:list:changeDetail')"> 編輯明細</el-dropdown-item>
                  <el-dropdown-item @click.native="configBill(scope.row)" type="text" size="small" v-if="isAuth('list-manage:o-list:list:changeWater')">編輯水單</el-dropdown-item>
                  <el-dropdown-item @click.native="deleteBill(scope.row)" type="text" size="small" v-if="isAuth('list-manage:o-list:list:changeWater')">刪除</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </span>
        </template>
        <!-- <template slot-scope="scope">
          <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
            <el-button @click="showTableDetailto(scope.row.id)" type="text" size="small">查看清單</el-button>
          </span>
          <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
            <el-button @click="addDetail(scope.row)" type="text" size="small"> 編輯明細</el-button>
          </span>
          <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
            <el-button @click="configBill(scope.row)" type="text" size="small">編輯水單</el-button>
          </span>
        </template> -->
      </el-table-column>
    </el-table>
    <el-pagination background layout="prev, pager, next" :current-page="page.pageNum" :total="total" @current-change="handleCurrentChange">
    </el-pagination>
  </div>
</template>

<script>
import { formatThousand } from '@/utils';
import listMultiSortSelections from './list-multi-sort-selections';

export default {
  name: 'sampleDateList',
  data() {
    return {
      tableData: [],tableresult:' ',
      total: 0,
      search: {
        createUserName: '',
        distNo: '',
        receiptEndDate: '',
        isChecked: '',
        receiptStartDate: '',
        sourceSocName: '',
        sourceSoc: '',
        orderList: [], // 多重排序
        id: '',
        orderBy: '',
        ascend: ''
      },
      orderSelections: listMultiSortSelections,
      loading: false,
      dateRange: [],
      page: {
        pageSize: 10,
        pageNum: 1
      },
    }
  },
  computed: {
    formatThousand: {
      get: () => formatThousand
    }
  },
  mounted() {
    this.searchFn(1);
  },
  activated() {
    // if(this.$route.query.update){
    //     this.searchFn(1);
    //     this.$router.push({name: 'oList', query: {update: false}});
    // }

    this.$nextTick(() => {
      if (this.$route.query.update) {
        let query = this.$route.query;
        delete query.update;
        this.searchFn(1);
      }
    })

  },
  methods: {
    upload() {
      this.$router.push({ name: 'oListStep2' })
    },
    clearSearch() {
      this.search = {
        createUserName: '',
        distNo: '',
        isChecked: '',
        receiptEndDate: '',
        receiptStartDate: '',
        sourceSocName: '',
        sourceSoc: '',
        orderList: [],
        id: '',
        orderBy: '',
        ascend: ''
      }
      this.searchFn(1);
    },
    // formatThousand,
    formatTime(time) {
      let str = ''
      if (time) {
        str = time.split(' ')[0]
      }
      return str
    },
    showTableDetail(row) {
      // this.$router.push({ name: 'bill', query: { receiptDetailsId: row.id } })
      this.$router.push({ name: 'oListStep3', query: { id: row.id,sourceSoc: row.sourceSoc, title: row.id, nameId: row.id} })
    },
    showTableBillto(useid, row) {
      // 如果传入了row参数，设置当前高亮行
      if (row) {
        this.$refs.listTableRef.setCurrentRow(row);
      }
      this.$router.push({ name: 'billList', query: { receiptId: useid } })
    },
    addDetail(row) {
      // 设置当前高亮行
      this.$refs.listTableRef.setCurrentRow(row);
      this.$router.push({ name: 'oListStep3', query: { id: row.id, sourceSoc: row.sourceSoc, title: row.id, nameId: row.id } })
    },
    configBill(row) {
      // 设置当前高亮行
      this.$refs.listTableRef.setCurrentRow(row);
      this.$router.push({ name: 'oListStep1', query: { row: JSON.stringify(row), title: 'edit' + row.id, nameId: 'edit' + row.id } })
    },
    handleMultiSortChange(e, a, b) {
      console.log('handleMultiSortChange:', e, a, b)
    },
    searchFn(page) {
      this.page.pageNum = page
      const searchParam = {
        ...this.search,
        orderList: this.search.orderList.map((item) => JSON.parse(item))
      }
      let ajaxData = searchParam;
      ajaxData.page = this.page
      if (page == 1) {
        this.total = 0
      }
      this.tableresult = '數據加載中...'
      this.$http.post('/listOverseasReceipt/getListOverseasReceiptList', ajaxData).then(res => {
        if (res.success) {
          this.tableData = res.data.list
          this.total = res.data.total
        }
            this.tableresult = this.tableData.length == 0 ? '暫無數據' : ' '
      })
    },
    handleCurrentChange(val) {
      this.page.pageNum = val
      this.searchFn(val);
    },
    handleClick(item) {
      this.$router.push({ name: '', query: {} })
    },
    addNew() {
      this.$router.push({ name: 'oListStep1', query: { row: '', title: 'add', nameId: 'add' } })
    },
    handleSortChange(obj ){
      this.search.orderBy = obj.prop
      this.search.ascend = obj.order
      this.search.orderList = []
      this.searchFn(this.page.pageNum)
    },
    // 处理行点击事件，设置当前高亮行
    handleRowClick(row, column, event) {
      // 设置当前高亮行
      this.$refs.listTableRef.setCurrentRow(row);
    },
    deleteBill(row){
      // 设置当前高亮行
      this.$refs.listTableRef.setCurrentRow(row);
      this.$alert('是否確定刪除水單？', '刪除', {
        confirmButtonText: '确定',
        showCancelButton: true,
        callback: action => {
          if (action === 'confirm') {
            this.$http.delete('/listOverseasReceipt/deleteListOverseasReceipt/' + row.id ).then(res => {
              console.log(res)
              if (res.success) {
                this.$toast({ tips: res.data })
                this.searchFn(this.page.pageNum)
              }
            })
          }
        }
      });
    }
  }
}
</script>

<style scoped>
.el-date-editor {
  width: 100px !important;
}
/deep/.el-input--suffix .el-input__inner {
  padding-right: 10px;
}
.breakword {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  width: 100%;
  display: block;
}

/* 行高亮效果样式 - 与billlist.vue保持一致 */
/deep/ .el-table__body tr.current-row > td {
    color: white !important;
    background-color: #17b3a3 !important;
}

/deep/ .el-table__body tr.current-row .el-button--text span {
    color: white !important;
}

/* 确保下拉菜单文字也变白 */
/deep/ .el-table__body tr.current-row .el-dropdown span {
    color: white !important;
}


</style>
