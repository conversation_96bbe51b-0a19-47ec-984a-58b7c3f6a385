<template>
    <div class="baseinfobox">
        <top-bar :steps="steps" :showDeleteContract="!!contractNo" @save="saveContract" @deleteContract="deleteContract">
            <slot v-if="contractNo">
                <div>
                    <label>Agreement No: {{contractNo}}</label>
                </div>
            </slot>
        </top-bar>
        <el-card class="box-card">
            <el-collapse v-model="activeNames">
                <el-collapse-item class="step-jump" title="Info" name="1">
                    <el-form :inline="true" ref="formContract" :model="formContract" :rules="rulesOriginal"  label-position="right" label-width="110px" class="p-t-20" name="1">
                        <div>
                            <el-form-item label="Type">
                                <el-select v-model="formContract.agrType" placeholder="">
                                    <!-- <el-option label="默認選項" value=""></el-option> -->
                                    <el-option label="Writer General" value="OG"></el-option>
                                    <el-option label="Writer Specific" value="OS"></el-option>
                                    <el-option label="Publisher General" value="PG"></el-option>
                                    <el-option label="Publisher Specific" value="PS"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="ID">
                                <el-input v-model="formContract.agrTypeId" @dblclick.native="selectTypeId" @change="checkAssigneeRoleCode" :readonly="!!contractNo"></el-input>
                            </el-form-item>
                            <el-form-item label="Doc No">
                                <el-input v-model="formContract.docNo" placeholder="Doc No"></el-input>
                            </el-form-item>
                        </div>
                        <!-- 以下為assignor 信息 -->
                        <div>
                            <el-form style="padding-top: 0;" :inline="true" ref="assignorInfo" :model="assignorInfo" :rules="rulesOriginal"  label-position="right" label-width="110px" class="p-t-20" name="1">
                                <el-form-item label="Assignor" prop="oipNameNo">
                                    <el-input ref="assignorInput" style="width: 120px;" v-model="assignorInfo.oipNameNo" placeholder="雙擊查詢" @dblclick.native="getIp('info', 1, {ipNameNo: assignorInfo.oipNameNo, name: assignorInfo.oipName})"  readonly></el-input>
                                </el-form-item>
                                <el-form-item>
                                    <el-input style="width: 260px;" v-model="assignorInfo.oipName" placeholder="雙擊查詢" @dblclick.native="getIp('info', 1, {ipNameNo: assignorInfo.oipNameNo, name: assignorInfo.oipName})" readonly></el-input>
                                </el-form-item>
                                <el-form-item label="Role Code" label-width="230px">
                                    <el-input v-model="assignorInfo.oipRole" placeholder="" readonly></el-input>
                                </el-form-item>
                            </el-form>
                            <div class="interest">
                                <el-form-item label="PER" label-width="110px" required>
                                    <el-input v-model="assignorInfo.oipPshareSoc" placeholder="" readonly></el-input>
                                </el-form-item>
                                <el-form-item prop="oipPshare">
                                    <el-input type="number" v-model="formContract.oipPshare" placeholder="" @change="shareChange('p')"></el-input>
                                </el-form-item>

                                <el-form-item label="MR" label-width="40px" required>
                                    <el-input v-model="assignorInfo.oipMshareSoc" placeholder="" readonly></el-input>
                                </el-form-item>
                                <el-form-item prop="oipMshare">
                                    <el-input type="number" v-model="formContract.oipMshare" placeholder="" @change="shareChange('m')"></el-input>
                                </el-form-item>

                                <el-form-item label="SY" label-width="40px" required>
                                    <el-input v-model="assignorInfo.oipSshareSoc" placeholder="" readonly></el-input>
                                </el-form-item>
                                <el-form-item prop="oipSshare">
                                    <el-input type="number" v-model="formContract.oipSshare" placeholder="" @change="shareChange('s')"></el-input>
                                </el-form-item>

                                <el-form-item label="OD" label-width="40px" required>
                                    <el-input v-model="assignorInfo.oipOdshareSoc" placeholder="" readonly></el-input>
                                </el-form-item>
                                <el-form-item prop="oipOdshare"> 
                                    <el-input type="number" v-model="formContract.oipOdshare" placeholder="" @change="shareChange('o')"></el-input>
                                </el-form-item>
                                
                                <el-form-item label="DB" label-width="40px" required>
                                    <el-input v-model="assignorInfo.oipDbshareSoc " placeholder="" readonly></el-input>
                                </el-form-item>
                                <el-form-item prop="oipDbshare">
                                    <el-input type="number" v-model="formContract.oipDbshare" placeholder="" @change="shareChange('d')"></el-input>
                                </el-form-item>
                            </div>
                        </div>
                        <!-- assignor 信息結束 -->
                        <div>
                            <el-form-item label="Agr Date" prop="agrDate">
                                <date-picker   v-model="formContract.agrDate" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd"></date-picker>
                            </el-form-item>
                            <el-form-item label="Start Date" prop="agrSdate">
                                <date-picker   v-model="formContract.agrSdate" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd"></date-picker>
                            </el-form-item>
                            <el-form-item label="End Date" prop="agrEdate">
                                <date-picker   v-model="formContract.agrEdate" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd"></date-picker>
                            </el-form-item>
                        </div>
                        <div>
                            <el-form-item label="Retention Date" prop="covRetentionEdate" label-width="140px">
                                <date-picker v-model="formContract.covRetentionEdate" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd" :disabled="!checked2"></date-picker>
                            </el-form-item>
                            <el-form-item label="Post Date" prop="covPostTermDate">
                                <date-picker v-model="formContract.covPostTermDate" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd"></date-picker>
                            </el-form-item>
                            <!-- <el-form-item label="Post Date" prop="covRetentionPeriod">
                                <date-picker v-model="formContract.covRetentionPeriod" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd"></date-picker>
                            </el-form-item> -->
                        </div>
                        <div style="padding: 0 30px;">
                            <el-form-item>
                                <el-checkbox v-model="checked1">Auto Extension</el-checkbox>
                            </el-form-item>
                            <el-form-item>
                                <el-checkbox v-model="checked2">Expired</el-checkbox>
                            </el-form-item>
                            <div style="float: right">
                                <el-form :inline="true" :model="formCheck">
                                    <el-form-item>
                                        <el-checkbox v-model="checked3">Agreement Recevied</el-checkbox>
                                    </el-form-item>
                                    <el-form-item>
                                        <el-checkbox v-model="checked4">Futher assign</el-checkbox>
                                    </el-form-item>
                                    <el-form-item>
                                        <el-checkbox v-model="checked5">SD</el-checkbox>
                                    </el-form-item>
                                    <el-form-item>
                                            <el-checkbox v-model="checked6">Pre term</el-checkbox>
                                    </el-form-item>
                                </el-form>
                            </div>
                        </div>
                    </el-form>
                </el-collapse-item>
                <el-collapse-item class="step-jump" title="Assignee" name="2">
                    <el-form ref="assigneeForm" :model="tableForm">
                        <el-table
                            :data="tableForm.tableData4"
                            border
                            stripe
                            style="width: 100%">
                            <el-table-column
                                prop="sipNameNo"
                                label="AssigneeNo"
                                width="126px">
                                <template slot-scope="scope">
                                    <el-form-item :prop="`tableData4.${scope.$index}.sipNameNo`" :rules="tableRules.sipNameNo">
                                        <el-input :ref="'assignee'+scope.$index" v-model=scope.row.sipNameNo placeholder="雙擊查詢" @dblclick.native="getIp('assignee', scope.$index, {ipNameNo: scope.row.sipNameNo, name: scope.row.sipChinName})" readonly></el-input>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="sipChineseName"
                                label="AssigneeName">
                                <template slot-scope="scope">
                                    <el-form-item>
                                        <el-input v-model=scope.row.sipChinName placeholder="雙擊查詢" @dblclick.native="getIp('assignee', scope.$index, {ipNameNo: scope.row.sipNameNo, name: scope.row.sipChinName})" readonly></el-input>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="sipRole"
                                label="Role Code"
                                width="100px">
                                <template slot-scope="scope">
                                    <el-form-item>
                                        <el-input v-model=scope.row.sipRole placeholder="Role Code"></el-input>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="sipPshareSoc"
                                label="PER"
                                width="130px">
                                <template slot-scope="scope">
                                    <el-form-item>
                                        <el-input class="soc" v-model=scope.row.sipPshareSoc placeholder="" readonly></el-input>
                                        <el-input class="share" v-model=scope.row.sipPshare placeholder=""></el-input>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="sipPshareSoc"
                                label="MR"
                                width="130px">
                                <template slot-scope="scope">
                                    <el-form-item>
                                        <el-input class="soc" v-model=scope.row.sipMshareSoc placeholder="" readonly></el-input>
                                        <el-input class="share" v-model=scope.row.sipMshare placeholder=""></el-input>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="sipPshareSoc"
                                label="SY"
                                width="130px">
                                <template slot-scope="scope">
                                    <el-form-item>
                                        <el-input class="soc" v-model=scope.row.sipSshareSoc placeholder="" readonly></el-input>
                                        <el-input class="share" v-model=scope.row.sipSshare placeholder="" ></el-input>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="sipPshareSoc"
                                label="OD"
                                width="130px">
                                <template slot-scope="scope">
                                    <el-form-item>
                                        <el-input class="soc" v-model=scope.row.sipOdshareSoc placeholder="" readonly></el-input>
                                        <el-input class="share" v-model=scope.row.sipOdshare placeholder=""></el-input>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="sipPshareSoc"
                                label="DB"
                                width="130px">
                                <template slot-scope="scope">
                                    <el-form-item>
                                        <el-input class="soc" v-model=scope.row.sipDbshareSoc placeholder="" readonly></el-input>
                                        <el-input class="share" v-model=scope.row.sipDbshare placeholder=""></el-input>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="OP"
                                width="50">
                                <template slot-scope="scope">
                                    <el-form-item>
                                        <span style="width: 100%;display: inline-block; text-align: center;">
                                            <i class="el-icon-delete" @click="deletedata4(scope.$index,tableForm.tableData4)"></i>
                                        </span>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-form>
                    <div class="add-new">
                        <el-button type="primary" @click="adddata4()">新 增</el-button>
                    </div>
                 </el-collapse-item>
                <!--Territory tab切換-->
                <el-collapse-item class="step-jump" title="Territory" name="3">
                    <el-form ref="territoryForm" :model="tableForm">
                        <el-table
                            :data="tableForm.tableData1"
                            border
                            stripe
                            style="width: 100%">
                            <el-table-column
                                prop="tisA"
                                label="Code">
                                <template slot-scope="scope">
                                    <el-form-item :prop="`tableData1.${scope.$index}.tisA`" :rules="territoryRules.tisA">
                                        <el-input :ref="'territory' + scope.$index" v-model=scope.row.tisA placeholder="雙擊查詢" @dblclick.native="getTerritroy(scope.$index,scope.row)" @blur="searchTerr(scope.$index, scope.row)"></el-input>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="generDetail"
                                label="Description">
                                <template slot-scope="scope">
                                    <el-form-item>
                                        <el-input v-model=scope.row.generDetail placeholder="" readonly></el-input>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="checkedInclude"
                                label="Include"
                                width="80px">
                                <template slot-scope="scope">
                                    <el-form-item>
                                        <el-checkbox v-model="scope.row.checkedInclude"></el-checkbox>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="OP"
                                width="50">
                                <template slot-scope="scope">
                                    <span style="width: 100%;display: inline-block;text-align: center;">
                                        <i class="el-icon-delete" @click="deletedata1(scope.$index,tableForm.tableData1)"></i>
                                    </span>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-form>
                    <div class="add-new">
                        <el-button type="primary" @click="adddata1()">新 增</el-button>
                    </div>
                </el-collapse-item>
                <!--Source tab切換-->
                <el-collapse-item class="step-jump" title="Source " name="4">
                    <el-form ref="formTable" :model="tableForm">
                        <el-table
                            :data="tableForm.tableData2"
                            border
                            stripe
                            style="width: 100%">
                            <el-table-column
                                prop="sourceDate"
                                label="Date"
                                width="140">
                                <template slot-scope="scope">
                                    <el-form-item>
                                        <date-picker v-model="scope.row.sourceDate" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd" style="width: 120px !important;"></date-picker>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="sourceType"
                                label="Type">
                                <template slot-scope="scope">
                                    <el-form-item :prop="`tableData2.${scope.$index}.sourceType`" :rules="tableRules.sourceType">
                                        <el-input :ref="'type' + scope.$index" v-model=scope.row.sourceType placeholder="雙擊查詢" @dblclick.native="getSourceType(scope.$index, scope.row)"></el-input>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="source"
                                label="Source">
                                <template slot-scope="scope">
                                    <el-form-item :prop="`tableData2.${scope.$index}.source`" :rules="tableRules.source">
                                        <el-input v-model=scope.row.source placeholder="" @change="souceIdChange(scope.$index, scope.row.source)"></el-input>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="sourceContent"
                                label="Name">
                                <template slot-scope="scope">
                                    <el-form-item :prop="`tableData2.${scope.$index}.sourceContent`" :rules="tableRules.sourceContent">
                                        <el-input :ref="'name'+scope.$index" v-model=scope.row.sourceContent placeholder=""></el-input>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="agrNo"
                                label="Source Agr No">
                                <template slot-scope="scope">
                                    <el-form-item>
                                        <el-input v-model=scope.row.agrNo placeholder=""></el-input>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="inputSoc"
                                label="Soc">
                                <template slot-scope="scope">
                                    <el-form-item>
                                        <el-input v-model=scope.row.inputSoc placeholder="" :readonly="opType == 'add'"></el-input>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="OP"
                                width="50">
                                <template slot-scope="scope">
                                    <el-form-item>
                                        <span style="width: 100%;display: inline-block;text-align: center;">
                                            <i class="el-icon-delete" @click="deletedata2(scope.$index,tableForm.tableData2)"></i>
                                        </span>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-form>
                    <div class="add-new">
                        <el-button type="primary" @click="adddata2()">新 增</el-button>
                    </div>
                </el-collapse-item>
                <!--Extend tab切換-->
                <el-collapse-item class="step-jump component-class" title="Extend" name="5">
                    <el-form :inline="true" @submit.native.prevent>
                        <!-- <el-form-item label="Include">
                            <el-select v-model="currExtendType">
                                <el-option label="exclude" value="exclude"></el-option>
                                <el-option label="include" value="include"></el-option>
                            </el-select>
                        </el-form-item> -->
                        <el-form-item label="Work No/Title">
                            <el-input style="width: 200px" v-model="extendFilter" placeholder=""></el-input>
                        </el-form-item>
                    </el-form>
                    <el-tabs v-model="currExtendType">
                        <el-tab-pane label="exclude" name="exclude">
                            <div style="max-height: 300px; overflow: auto;" id="extendBox1">
                                <el-table
                                    id="extendTable1"
                                    ref="extendTable"
                                    :data="tableData3E"
                                    border
                                    stripe
                                    @selection-change="selectionChangeHandle"
                                    style="max-height: 999999px;">
                                    <el-table-column
                                        type="selection">
                                    </el-table-column>
                                    <el-table-column
                                        prop="worksnum"
                                        label="Work No"
                                        width="130px">
                                        <template slot-scope="scope">
                                            <el-input v-model=scope.row.worksnum placeholder="雙擊查詢" @dblclick.native="getWork(scope.$index, scope.row, tableData3E)" readonly></el-input>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        prop="worknumSociety"
                                        label="Soc"
                                        width="60px">
                                        <template slot-scope="scope">
                                            <el-input v-model=scope.row.worknumSociety placeholder="雙擊查詢" @dblclick.native="getWork(scope.$index, scope.row, tableData3E)" readonly></el-input>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        prop="title"
                                        label="Title"
                                        min-width="120">
                                        <template slot-scope="scope">
                                            <el-input v-model="scope.row.title" placeholder="雙擊查詢"  @dblclick.native="getWork(scope.$index, scope.row, tableData3E)" readonly></el-input>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        prop="languageCode"
                                        label="Lang"
                                        width="60px">
                                        <template slot-scope="scope">
                                            <el-input v-model=scope.row.languageCode placeholder="" readonly></el-input>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        prop="generDetail"
                                        label="Genre"
                                        width="70px">
                                        <template slot-scope="scope">
                                            <el-input v-model=scope.row.genreDetail placeholder="" readonly></el-input>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        prop="ipNameNo"
                                        label="Ip Name No"
                                        width="130px">
                                        <template slot-scope="scope">
                                            <el-input v-model=scope.row.ipNameNo placeholder="雙擊查詢" @dblclick.native="getIp('extendE', scope.$index, scope.row)" readonly></el-input>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        prop="name"
                                        label="Ip Name"
                                        width="150px">
                                        <template slot-scope="scope">
                                            <el-input v-model=scope.row.name placeholder="雙擊查詢" @dblclick.native="getIp('extendE', scope.$index, scope.row)" readonly></el-input>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        prop="workWriterRole"
                                        label="RoleCode"
                                        width="100px">
                                        <template slot-scope="scope">
                                            <el-input v-model=scope.row.workWriterRole placeholder="" @change="extendChange(scope.row, 'edit')"></el-input>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        prop="oriValidTo"
                                        label="Org Valid To"
                                        width="136">
                                        <template slot-scope="scope">
                                            <date-picker style="width: 130px !important;" v-model="scope.row.oriValidTo" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd" @change="extendChange(scope.row, scope.$index)"></date-picker>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        prop="covValidTo"
                                        label="Cov Valid To"
                                        width="136">
                                        <template slot-scope="scope">
                                            <date-picker style="width: 130px !important;" v-model="scope.row.covValidTo" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd" @change="extendChange(scope.row, scope.$index)"></date-picker>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        prop="pchecked"
                                        label="P"
                                        width="40">
                                        <template slot-scope="scope">
                                            <el-checkbox v-model="scope.row.pchecked" @change="extendChange(scope.row, 'edit')"></el-checkbox>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        prop="mchecked"
                                        label="M"
                                        width="40">
                                        <template slot-scope="scope">
                                            <el-checkbox v-model="scope.row.mchecked" @change="extendChange(scope.row, 'edit')"></el-checkbox>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        prop="schecked"
                                        label="S"
                                        width="40">
                                        <template slot-scope="scope">
                                            <el-checkbox v-model="scope.row.schecked" @change="extendChange(scope.row, 'edit')"></el-checkbox>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        prop="ochecked"
                                        label="O"
                                        width="40">
                                        <template slot-scope="scope">
                                            <el-checkbox v-model="scope.row.ochecked" @change="extendChange(scope.row, 'edit')"></el-checkbox>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        prop="dchecked"
                                        label="D"
                                        width="40">
                                        <template slot-scope="scope">
                                            <el-checkbox v-model="scope.row.dchecked" @change="extendChange(scope.row, 'edit')"></el-checkbox>
                                        </template>
                                    </el-table-column>
                                    <!-- <el-table-column
                                        prop="includechecked"
                                        label="Include"
                                        width="110">
                                        <template slot-scope="scope">
                                            <el-select v-model="scope.row.includechecked" @change="extendChange(scope.row, 'edit')">
                                                <el-option :value="true" label="Include"></el-option>
                                                <el-option :value="false" label="Exclude"></el-option>
                                            </el-select>
                                        </template>
                                    </el-table-column> -->
                                </el-table>
                                <div class="t-c" style="height: 40px; line-height: 40px;" v-if="this.contractNo && tableData3E.length > 0 && !(!hasMore && this.extendPage==1)">
                                    <span @click="queryExtend(extendPage+1)" :class="hasMore?'pointer':''">{{hasMore ? '查看更多' : '没有更多数据了'}} </span>
                                </div>
                            </div>
                            <!-- <div class="add-new">
                                <el-button type="primary" @click="adddata3E()">新 增</el-button>
                                <el-button type="danger" @click="deletedata3E()">刪 除</el-button>
                            </div> -->
                        </el-tab-pane>
                        <el-tab-pane label="include" name="include">
                            <div style="max-height: 300px; overflow: auto;" id="extendBox">
                                <el-table
                                    empty-text=' '
                                    id="extendTable"
                                    ref="extendTable"
                                    :data="tableData3I"
                                    border
                                    stripe
                                    @selection-change="selectionChangeHandle"
                                    style="max-height: 999999px;">
                                    <el-table-column
                                        type="selection">
                                    </el-table-column>
                                    <el-table-column
                                        prop="worksnum"
                                        label="Work No"
                                        width="130px">
                                        <template slot-scope="scope">
                                            <el-input v-model=scope.row.worksnum placeholder="雙擊查詢" @dblclick.native="getWork(scope.$index, scope.row, tableData3I)" readonly></el-input>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        prop="worknumSociety"
                                        label="Soc"
                                        width="60px">
                                        <template slot-scope="scope">
                                            <el-input v-model=scope.row.worknumSociety placeholder="雙擊查詢" @dblclick.native="getWork(scope.$index, scope.row, tableData3I)" readonly></el-input>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        prop="title"
                                        label="Title"
                                        min-width="120">
                                        <template slot-scope="scope">
                                            <el-input v-model="scope.row.title" placeholder="雙擊查詢"  @dblclick.native="getWork(scope.$index, scope.row, tableData3I)" readonly></el-input>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        prop="languageCode"
                                        label="Lang"
                                        width="60px">
                                        <template slot-scope="scope">
                                            <el-input v-model=scope.row.languageCode placeholder="" readonly></el-input>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        prop="generDetail"
                                        label="Genre"
                                        width="70px">
                                        <template slot-scope="scope">
                                            <el-input v-model=scope.row.genreDetail placeholder="" readonly></el-input>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        prop="ipNameNo"
                                        label="Ip Name No"
                                        width="130px">
                                        <template slot-scope="scope">
                                            <el-input v-model=scope.row.ipNameNo placeholder="雙擊查詢" @dblclick.native="getIp('extendI', scope.$index, scope.row)" readonly></el-input>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        prop="name"
                                        label="Ip Name"
                                        width="150px">
                                        <template slot-scope="scope">
                                            <el-input v-model=scope.row.name placeholder="雙擊查詢" @dblclick.native="getIp('extendI', scope.$index, scope.row)" readonly></el-input>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        prop="workWriterRole"
                                        label="RoleCode"
                                        width="100px">
                                        <template slot-scope="scope">
                                            <el-input v-model=scope.row.workWriterRole placeholder="" @change="extendChange(scope.row, 'edit')"></el-input>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        prop="oriValidTo"
                                        label="Org Valid To"
                                        width="136">
                                        <template slot-scope="scope">
                                            <date-picker style="width: 130px !important;" v-model="scope.row.oriValidTo" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd" @change="extendChange(scope.row, 'edit')"></date-picker>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        prop="covValidTo"
                                        label="Cov Valid To"
                                        width="136">
                                        <template slot-scope="scope">
                                            <date-picker style="width: 130px !important;" v-model="scope.row.covValidTo" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd" @change="extendChange(scope.row, 'edit')"></date-picker>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        prop="pchecked"
                                        label="P"
                                        width="40">
                                        <template slot-scope="scope">
                                            <el-checkbox v-model="scope.row.pchecked" @change="extendChange(scope.row, 'edit')"></el-checkbox>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        prop="mchecked"
                                        label="M"
                                        width="40">
                                        <template slot-scope="scope">
                                            <el-checkbox v-model="scope.row.mchecked" @change="extendChange(scope.row, 'edit')"></el-checkbox>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        prop="schecked"
                                        label="S"
                                        width="40">
                                        <template slot-scope="scope">
                                            <el-checkbox v-model="scope.row.schecked" @change="extendChange(scope.row, 'edit')"></el-checkbox>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        prop="ochecked"
                                        label="O"
                                        width="40">
                                        <template slot-scope="scope">
                                            <el-checkbox v-model="scope.row.ochecked" @change="extendChange(scope.row, 'edit')"></el-checkbox>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        prop="dchecked"
                                        label="D"
                                        width="40">
                                        <template slot-scope="scope">
                                            <el-checkbox v-model="scope.row.dchecked" @change="extendChange(scope.row, 'edit')"></el-checkbox>
                                        </template>
                                    </el-table-column>
                                    <!-- <el-table-column
                                        prop="includechecked"
                                        label="Include"
                                        width="110">
                                        <template slot-scope="scope">
                                            <el-select v-model="scope.row.includechecked" @change="extendChange(scope.row, 'edit')">
                                                <el-option :value="true" label="Include"></el-option>
                                                <el-option :value="false" label="Exclude"></el-option>
                                            </el-select>
                                        </template>
                                    </el-table-column> -->
                                </el-table>
                                <div class="t-c" style="height: 40px; line-height: 40px;" v-if="this.contractNo && tableData3I.length > 0 && !(!hasMore && this.extendPage==1)">
                                    <span @click="queryExtend(extendPage+1)" :class="hasMore?'pointer':''">{{hasMore ? '查看更多' : '没有更多数据了'}} </span>
                                </div>
                            </div>
                            <!-- <div class="add-new">
                                <el-button type="primary" @click="adddata3I()">新 增</el-button>
                                <el-button type="danger" @click="deletedata3I()">刪 除</el-button>
                            </div> -->
                        </el-tab-pane>
                    </el-tabs>
                    <div class="add-new">
                        <el-button type="primary" @click="toAdd()">新 增</el-button>
                        <el-button type="danger" @click="toDel()">刪 除</el-button>
                    </div>
                        
                    
                </el-collapse-item>
                <!--Remark tab切換-->
                <el-collapse-item class="step-jump" title="Remark" name="6">
                    <el-table
                        :data="tableData5"
                        border
                        stripe
                        style="width: 100%">
                        <el-table-column
                            prop="inputSoc"
                            label="Soc"
                            width="180">
                            <template slot-scope="scope">
                                <el-input v-model=scope.row.inputSoc placeholder=""></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="remark"
                            label="Remark">
                            <template slot-scope="scope">
                                <el-input
                                    type="textarea"
                                    :rows="2"
                                    placeholder="請輸入内容"
                                    v-model="scope.row.remark">
                                </el-input>
                            </template>
                        </el-table-column>
                        <el-table-column
                            label="OP"
                            width="50">
                            <template slot-scope="scope">
                                <!-- <span style="width: 35px;display: inline-block">
                                    <i class="el-icon-circle-plus-outline" @click="adddata5()"></i>
                                </span> -->
                                <span style="width: 42px;display: inline-block; text-align: center;">
                                    <i class="el-icon-delete" @click="deletedata5(scope.$index,tableData5)"></i>
                                </span>
                            </template>
                        </el-table-column>
                    </el-table>
                    <div class="add-new">
                        <el-button type="primary" @click="adddata5()">新 增</el-button>
                    </div>
                </el-collapse-item>
            </el-collapse>
        </el-card>
        <el-dialog :visible.sync="showSelectTypeId"   width="1000px" title="List of Agreement Type Id" :close-on-click-modal="false">
            <!-- <div style="width: 500px;margin: auto;margin-bottom: 20px">
                <el-input placeholder="請輸入name" v-model="writerInput" class="input-with-select">
                    <el-button slot="append" icon="el-icon-search" @click="search(null)"></el-button>
                </el-input>
            </div> -->
            <el-table :empty-text="tableresult"   stripe :data="idTypeList">
                <el-table-column property="id" label="Agr_Type_Id"></el-table-column>
                <el-table-column property="agrType" label="Agr_Type"></el-table-column>
                <el-table-column
                    label="OP"
                    width="50">
                    <template slot-scope="scope">
                        <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                            <i class="el-icon-check" @click="checkedTypeId(scope.row.id,scope.row.agrType)"></i>
                        </span>
                    </template>
                </el-table-column>
            </el-table>
        </el-dialog>
        <el-dialog :visible.sync="sourceTypeShow" title="選擇SourceType" :close-on-click-modal="false">
            <div style="width: 500px;margin: auto;margin-bottom: 20px">
                <el-input placeholder="請輸入type" @keyup.enter.native="typeSearch()" v-model="typeInput" class="input-with-select">
                    <el-button slot="append" icon="el-icon-search" @click="typeSearch()"></el-button>
                </el-input>
            </div>
            <el-table :empty-text="tableresult"   stripe :data="typeData">
                <el-table-column property="sourceType" label="type"></el-table-column>
                <el-table-column property="description" label="描述" ></el-table-column>
                <el-table-column
                    label="operation"
                    width="100">
                    <template slot-scope="scope">
                        <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                            <i class="el-icon-check" @click="checkedType(scope.$index,scope.row)"></i>
                        </span>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                background
                layout="prev, pager, next"
                :total=typeTotal @current-change="typeSearch">
            </el-pagination>
        </el-dialog>
        <!-- 选择territory -->
        <el-dialog :visible.sync="territoryShow" title="選擇Territory" :close-on-click-modal="false">
            <div style="width: 500px;margin: auto;margin-bottom: 20px">
                <el-input placeholder="請輸入code" @keyup.enter.native="territorySearch()" v-model="tisA" class="input-with-select">
                    <el-button slot="append" icon="el-icon-search" @click="territorySearch()"></el-button>
                </el-input>
            </div>
            <el-table :empty-text="tableresult"   stripe :data="territoryList">
                <el-table-column property="tisA" label="code"></el-table-column>
                <el-table-column property="description" label="描述" ></el-table-column>
                <el-table-column
                    label="operation"
                    width="100">
                    <template slot-scope="scope">
                        <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                            <i class="el-icon-check" @click="checkedTerritory(scope.$index,scope.row)"></i>
                        </span>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                background
                layout="prev, pager, next"
                :total=territoryTotal @current-change="territorySearch">
            </el-pagination>
        </el-dialog>

        <select-ip v-if="IpTableVisible" ref="selectIpCom" :search="ipSearch" @checkIp="checkIp"></select-ip>
        <select-work v-if="workTableVisible" ref="selectWorkCom" :search="workSearch" @checkWork="checkWork"></select-work>

    </div>
</template>

<script>
    import qs from 'qs'
    import axios from '../../utils/httpRequest';
    import selectWork from '../../components/select-work';
    import selectIp from '../../components/select-ip';

    export default {
        name: 'info',
        data () {
            var validateNameInput = (rule, value, callback) => {
                if (!value) {
                    return callback(new Error('此項必填！'))
                } else {
                    callback()
                }
            }
            var validateTypeInput = (rule, value, callback) => {
                if (!value) {
                    return callback(new Error('此項必填！'))
                } else {
                    callback()
                }
            }
            var validateIdInput = (rule, value, callback) => {
                if (!value) {
                    return callback(new Error('此項必填！'))
                } else {
                    callback()
                }
            }
            return {
                tableresult:' ',
                contractNo: '',
                activeStep: 1,
                toworkno: '',
                steps: [
                    {
                        name: 'Info'
                    }, {
                        name: 'Assignee'
                    }, {
                        name: 'Territory'
                    }, {
                        name: 'Source'
                    },{
                        name: 'Extend'
                    },{
                        name: 'Remark'
                    }
                ],
                rulesOriginal: {
                    agrDate: [
                        { required: true, message: '請輸入Agr Date', trigger: 'blur' }
                    ],
                    agrSdate: [
                        { required: true, message: '請輸入Start Date', trigger: 'blur' }
                    ],
                    agrEdate: [
                        { required: true, message: '請輸入End Date', trigger: 'blur' }
                    ],
                    oipNameNo: [
                        { required: true, message: '請選擇Assignor', trigger: 'blur' }
                    ],
                    docNo: [
                        { required: true, message: '請輸入Doc No', trigger: 'blur' }
                    ],
                    oipPshare: [
                        { required: true, message: '請輸入', trigger: 'blur' }
                    ],
                    oipMshare: [
                        { required: true, message: '請輸入', trigger: 'blur' }
                    ],
                    oipSshare: [
                        { required: true, message: '請輸入', trigger: 'blur' }
                    ],
                    oipOdshare: [
                        { required: true, message: '請輸入', trigger: 'blur' }
                    ],
                    oipDbshare: [
                        { required: true, message: '請輸入', trigger: 'blur' }
                    ],

                },
                tableRules: {
                    sourceType: [{ validator: validateTypeInput, trigger: 'blur' }],
                    source: [{ validator: validateIdInput, trigger: 'blur' }],
                    sourceContent: [{ validator: validateNameInput, trigger: 'blur' }],
                    sipNameNo: [
                        {required: true, message: '請選擇Assignee', trigger: 'blur'}
                    ],
                },
                territoryRules: {
                    tisA: [{required: true, message: '請選擇Code', trigger: 'blur'}]
                },
                pchecked: true,
                checkedpub: true,
                adchecked: true,
                checkedInclude: true,
                checked1: false,
                checked2: false,
                checked3: false,
                checked4: false,
                checked5: false,
                checked6: false,
                checked7: false,
                checked8: false,
                checked9: false,
                restaurants: [],
                activeNames: ['1', '2', '3', '4', '5', '6'],
                id: '',
                state4: '',
                timeout: null,
                formContract: {
                    agrType: 'OG',
                    agrTypeId: '9',
                    docNo: '',
                    oipNameNo: '',
                    oipFirstName: '',
                    oipName: '',
                    oipRole: '',
                    oipPshareSoc: '',
                    oipPshare: '',
                    oipMshareSoc: '',
                    oipMshare: '',
                    oipSshareSoc: '',
                    oipSshare: '',
                    oipOdshareSoc: '',
                    oipOdshare: '',
                    oipDbshareSoc: '',
                    oipDbshare: '',
                    agrDate: '',
                    agrSdate: '',
                    agrEdate: '',
                    autoExtensionInd: true,
                    expired: true,
                    agrReceived: true,
                    furtherAssign: true,
                    preTerm: '',
                    sd: true,
                    id: this.id
                },
                assignorInfo: {
                    oipPshareSoc: '',
                    oipMshareSoc: '',
                    oipSshareSoc: '',
                    oipOdshareSoc: '',
                    oipDbshareSoc: '',
                    oipNameNo: '',
                    oipName: ''
                },
                territoryobj: {},
                fromList: [
                    {formAssignee: {
                        sipNameNo: '',
                        sipChineseFirstName: '',
                        sipChineseName: '',
                        sipRole: '',
                        sipPshareSoc: '',
                        sipPshare: '',
                        sipMshareSoc: '',
                        sipMshare: '',
                        sipSshareSoc: '',
                        sipSshare: '',
                        sipOdshareSoc: '',
                        sipOdshare: '',
                        sipDbshareSoc: '',
                        sipDbshare: ''
                    }}
                ],
                formCheck: {
                    wp: '',
                    inn: '',
                    year: '',
                    territory: '',
                    qt: '',
                    an: '',
                    type: '',
                    id: ''
                },
                // tableData1: [
                // ],
                tableForm: {
                    tableData1: [],
                    tableData2: [],
                    tableData4: []
                },
                //用于extend的筛选
                extendFilter: '',
                tableData3: [
                ],
                tableData3I: [],
                tableData3E: [],
                extendSelected: [],
                // tableData4: [
                // ],
                tableData5: [
                ],
                showSelectTypeId: false,
                idTypeList: [],

                // 查詢ip
                selectIpType: '', //哪里查找會員的  info, assignee, extend
                editIpIndex: 0,
                IpTableVisible: false,
                ipSearch: {},

                // 查詢作品
                editIndex: 0,
                workTableVisible: false,
                workSearch: {},

                opType: 'add', //操作類型，添加合同或編輯合同

                typeIndex: '',
                sourceIndex: '',
                typeTotal: 10,
                typeInput: '',
                sourceTypeShow: false,
                typeData: [],

                querying: false,  //是否正在查询extend
                extendPage: 1,
                hasMore: true,

                extendUpdate: [],

                // 选择territory
                territoryShow: false,
                editTerritoryIndex: 0,
                tisA: '',
                territoryList: [],
                territoryTotal: 0,

                currExtendType: 'exclude',
            }
        },
        watch: {
            "formContract.agrType" : function(newVal, oldVal){
                if(!this.checkType(null, null)){
                    this.formContract.agrType = oldVal;
                }
            },
            extendFilter: function(newVal, oldVal){
                if(newVal !== oldVal){
                    this.queryExtend(1);
                }
            }
        },
        components: {
            selectWork,
            selectIp
        },
        methods: {
            toAdd(){
                console.log(this.currExtendType)
                if(this.currExtendType=='exclude'){
                    this.adddata3E()
                }else{
                    this.adddata3I()
                }
            },
            toDel(){
                if(this.currExtendType=='exclude'){
                    this.deletedata3E()
                }else{
                    this.deletedata3I()
                }
            },
            /**
             * 选取作品
             */
            getWork(index, row, extendData) {
                this.extendData = extendData;
                this.editIndex = index;
                this.workTableVisible= true;
                this.workSearch = {
                    workId: row.worksnum,
                    soc: row.worknumSociety,
                    title: row.title
                }
                this.$nextTick( () => {
                    this.$refs.selectWorkCom.init();
                })
            },
            checkWork(info){
                this.$set(this.extendData[this.editIndex], 'title', info.title ? info.title : info.title_en);
                this.$set(this.extendData[this.editIndex], 'worksnum', info.work_id);
                this.$set(this.extendData[this.editIndex], 'worknumSociety', info.work_society_code);
                this.$set(this.extendData[this.editIndex], 'genreDetail', info.genre_code);
                this.$set(this.extendData[this.editIndex], 'languageCode', info.language_code);
                this.extendChange(this.extendData[this.editIndex], 'edit');
            },
            // checkWork(info){
            //     this.$set(this.tableData3[this.editIndex], 'title', info.title ? info.title : info.title_en);
            //     this.$set(this.tableData3[this.editIndex], 'worksnum', info.work_id);
            //     this.$set(this.tableData3[this.editIndex], 'worknumSociety', info.work_society_code);
            //     this.$set(this.tableData3[this.editIndex], 'genreDetail', info.genre_code);
            //     this.$set(this.tableData3[this.editIndex], 'languageCode', info.language_code);
            //     this.extendChange(this.tableData3[this.editIndex], 'edit');
            // },
             /**
             * 选取IP
             */
            getIp(type, index, row){
                this.selectIpType = type;
                this.editIpIndex = index;
                this.IpTableVisible = true;
                this.ipSearch = {
                    name_no: row.ipNameNo ? row.ipNameNo : '',
                    name: row.name ? row.name : '',
                    ip_no: row.baseNo ? row.baseNo : ''
                }
                this.$nextTick( () => {
                    this.$refs.selectIpCom.init();
                })
            },
            checkIp(info){
                console.log('info')
                console.log(info)
                /*
                    如果PG/PS合约，assignee和Assignor 都只能选择团体会员（ip_type=L）
                    如果是OG/OS 合约，Assignor只能选个人，
                    role带出后都是只读不能更改
                    role 前端固定赋值项
                    1.type_id in (10,6) Assignee 的role 固定为PA
                    2.OS/OG合约，排除（1）后的情况，Assignee如果是公司role为E
                    3.PG/PS合约，排除（1）后的情况，Assignee如果是公司 ，role为SE
                */
                //N 个人  L 团体
                let assigneeRoleCode = '';
                // let assignorRoleCode = '';
                if(this.formContract.agrTypeId == '6' || this.formContract.agrTypeId == '10'){
                    assigneeRoleCode = 'PA';
                }
                if(this.formContract.agrTypeId != '6' && this.formContract.agrTypeId != '10' && info.ip_type == 'L'){
                    if(this.formContract.agrType == 'OG' || this.formContract.agrType == 'OS'){
                        assigneeRoleCode = 'E';
                    }else{
                        assigneeRoleCode = 'SE';
                    }
                    // chinese_name
                }
                if(this.selectIpType == 'extendE'){
                    this.$set(this.tableData3E[this.editIpIndex], 'ipNameNo', info.ip_name_no);
                    this.$set(this.tableData3E[this.editIpIndex], 'ipBaseNo', info.ip_base_no);
                    this.$set(this.tableData3E[this.editIpIndex], 'name', info.chinese_name || info.name);
                    this.$set(this.tableData3E[this.editIpIndex], 'workWriterRole', info.role_code);
                    this.extendChange(this.tableData3E[this.editIpIndex], 'edit');
                }else if(this.selectIpType == 'extendI'){
                    this.$set(this.tableData3I[this.editIpIndex], 'ipNameNo', info.ip_name_no);
                    this.$set(this.tableData3I[this.editIpIndex], 'ipBaseNo', info.ip_base_no);
                    this.$set(this.tableData3I[this.editIpIndex], 'name', info.chinese_name || info.name);
                    this.$set(this.tableData3I[this.editIpIndex], 'workWriterRole', info.role_code);
                    this.extendChange(this.tableData3I[this.editIpIndex], 'edit');
                }else if(this.selectIpType == 'info'){
                    if(!this.checkType(info.ip_type, 'assignor')){
                        console.log('1');
                        return;
                    }
                    console.log('2');
                    this.$set(this.assignorInfo, 'oip_base_no', info.ip_base_no);
                    this.$set(this.assignorInfo, 'oipNameNo', info.ip_name_no);
                    this.$set(this.assignorInfo, 'oipName', info.name + (info.chinese_name ? ('('+info.chinese_name+')') : ''));
                    this.$set(this.assignorInfo, 'oipRole', info.role_code);
                    this.$set(this.assignorInfo, 'oipType', info.ip_type);

                    this.handleRight(info.agreement, 'info');

                    this.$refs['assignorInput'].focus();
                    this.$refs['assignorInput'].blur();

                }else{
                    if(!this.checkType(info.ip_type, 'assignee')){
                        return;
                    }
                    // this.$set(this.tableData4[this.editIpIndex], 'sipBaseNo', info.ip_base_no)
                    this.$set(this.tableForm.tableData4[this.editIpIndex], 'sipNameNo', info.ip_name_no)
                    this.$set(this.tableForm.tableData4[this.editIpIndex], 'sipBaseNo', info.ip_base_no)
                    // this.$set(this.tableForm.tableData4[this.editIpIndex], 'sipChinName', info.chinese_name ? info.chinese_name : info.name)
                    this.$set(this.tableForm.tableData4[this.editIpIndex], 'sipChinName', info.name + ( info.chinese_name ? ('('+info.chinese_name+')') : ''));
                    // this.$set(this.tableForm.tableData4[this.editIpIndex], 'sipRole', info.role_code)
                    this.$set(this.tableForm.tableData4[this.editIpIndex], 'sipRole', assigneeRoleCode ? assigneeRoleCode : info.role_code)
                    this.$set(this.tableForm.tableData4[this.editIpIndex], 'sipRole_b', info.role_code)
                    this.$set(this.tableForm.tableData4[this.editIpIndex], 'sipType', info.ip_type)
                    this.handleRight(info.agreement, 'assignee', this.editIpIndex);
                    this.$refs['assignee'+this.editIpIndex].focus();
                    this.$refs['assignee'+this.editIpIndex].blur();
                 }

            },
            // 当ID改变时，触发assignee 的roleCode的校对
            checkAssigneeRoleCode(){
                let assigneeRoleCode = '';
                
                this.tableForm.tableData4.forEach( (item, index) => {
                    this.tableForm.tableData4[index].sipRole = this.tableForm.tableData4[index].sipRole_b;

                    if(this.formContract.agrTypeId == '6' || this.formContract.agrTypeId == '10'){
                        assigneeRoleCode = 'PA';
                        this.tableForm.tableData4[index].sipRole = assigneeRoleCode;
                    }
                    if(this.formContract.agrTypeId != '6' && this.formContract.agrTypeId != '10' && item.ip_type == 'L'){
                        if(this.formContract.agrType == 'OG' || this.formContract.agrType == 'OS'){
                            assigneeRoleCode = 'E';
                        }else{
                            assigneeRoleCode = 'SE';
                        }
                        this.tableForm.tableData4[index].sipRole = assigneeRoleCode;
                    }
                })
                
            },
            checkType(type, selectType){  //type 为 iptype；；；selectType 为要写入的类型， assignor  或 assignee

                // N 个人   L 公司
                if(type){
                    if((this.formContract.agrType == 'PG' || this.formContract.agrType == 'PS') && type == 'N'){
                        this.$message({
                            dangerouslyUseHTMLString: true,
                            message: '<span style="color:#333;">assignor或assignee不可用為個人(ipType)</span>',
                            offset: 50,
                            type: 'warning'
                        })
                        return false;
                    }
                    if((this.formContract.agrType == 'OG' || this.formContract.agrType == 'OS') && type == 'L' && selectType == 'assignor'){
                        this.$message({
                            dangerouslyUseHTMLString: true,
                            message: '<span style="color:#333;">assignor只可用為個人(ipType)</span>',
                            offset: 50,
                            type: 'warning'
                        })
                        return false;
                    }
                }else{
                    if(this.formContract.agrType == 'PG' || this.formContract.agrType == 'PS'){
                        if(this.assignorInfo.oipType == 'N'){
                            this.$message({
                                dangerouslyUseHTMLString: true,
                                message: '<span style="color:#333;">assignor或assignee不可用為個人(ipType)</span>',
                                offset: 50,
                                type: 'warning'
                            })
                            return false;
                        }else{
                            let flag = true;
                            this.tableForm.tableData4.forEach( item => {
                                if(item.sipType == 'N'){
                                    flag = false;
                                }
                            })
                            if(!flag){
                                this.$message({
                                    dangerouslyUseHTMLString: true,
                                    message: '<span style="color:#333;">assignor或assignee不可用為個人(ipType)</span>',
                                    offset: 50,
                                    type: 'warning'
                                })
                            }
                            return flag;
                        }
                        
                    }
                }
                return true;
            },
            /**
             * source 下的type
             */
            getSourceType (index, item) {
                this.typeIndex = index;
                this.sourceTypeShow = true;
                this.typeInput = item.sourceType;
                this.typeSearch(1);
            },
            typeSearch(val) {
                this.sourceTypeShow = true;
                let data = {
                    page: {
                        pageNum: val ? val :1,
                        pageSize: '10'
                    },
                    sourceType: this.typeInput
                }
                this.tableresult='數據加載中...'
                this.$http.post('/ref/getRefWrkSourceType', data).then(res => {
                    if (res.status === 200) {
                        this.typeData = res.data.list;
                        this.typeTotal = res.data.total;
                        this.tableresult=this.typeData.length==0?'暫無數據':' '
                    }
                })
            },
            checkedType (index, item) {
                this.sourceTypeShow = false;
                this.$set(this.tableForm.tableData2[this.typeIndex], 'sourceType', item.sourceType);
                this.$refs['type'+this.typeIndex].focus();
                this.$refs['type'+this.typeIndex].blur();
            },
            handleRight(agreement, type, index){
                let right = {};
                let rightInfo = {};
                agreement.forEach( item => {
                    if(!right[item.cash_right]){
                        right[item.cash_right] = [];
                    }
                    right[item.cash_right].push(item.society_code)
                })
                for(let key in right){
                    right[key] = Array.from( new Set(right[key]) );
                    rightInfo[key] = right[key].join('/');
                }
                if(type == 'info'){
                    this.$set(this.assignorInfo, 'oipPshareSoc', rightInfo.PER || '099')
                    // this.assignorInfo.oipPshareSoc = rightInfo.PER || '099';
                    this.assignorInfo.oipMshareSoc = rightInfo.MR || '099';
                    this.assignorInfo.oipSshareSoc = rightInfo.SY || '099';
                    this.assignorInfo.oipOdshareSoc = rightInfo.NOD || '099';
                    this.assignorInfo.oipDbshareSoc = rightInfo.DB || '099';
                }else if(type == 'assignee'){
                    this.$set(this.tableForm.tableData4[index], 'sipPshareSoc', rightInfo.PER || '099');
                    // this.tableData4[index].sipPshareSoc = rightInfo.PER || '099';
                    this.tableForm.tableData4[index].sipMshareSoc = rightInfo.MR || '099';
                    this.tableForm.tableData4[index].sipSshareSoc = rightInfo.SY || '099';
                    this.tableForm.tableData4[index].sipOdshareSoc = rightInfo.NOD || '099';
                    this.tableForm.tableData4[index].sipDbshareSoc = rightInfo.DB || '099';
                }
            },
            adddata1 () {
                let myArray = [{ languageCode: '', generDetail: '', checkedInclude: '' }]
                this.tableForm.tableData1 = [...this.tableForm.tableData1, ...myArray]
            },
            deletedata1 (index, rows) {
                this.$msgbox.confirm(`確定進行[删除]操作?`, '提示', {
                    confirmButtonText: '確定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    rows.splice(index, 1)
                })
            },
            adddata2 () {
                let myArray = [{ sourceDate: '', sourceType: '', source: '', sourceContent: '', agrNo: '', inputSoc: '' }]
                if(this.opType == 'add'){
                    myArray[0].inputSoc = '161';
                }
                this.tableForm.tableData2 = [...this.tableForm.tableData2, ...myArray]
            },
            deletedata2 (index, rows) {
                this.$msgbox.confirm(`確定進行[删除]操作?`, '提示', {
                    confirmButtonText: '確定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    rows.splice(index, 1)
                })
            },
            selectionChangeHandle(val) {
                console.log('selection: ', val);
                this.extendSelected = val;
            },
            adddata3E () {
                let timeId = new Date().getTime();
                let myArray = [{ timeId: timeId, worksnum: '', inputSoc: '', title: '', languageCode: '', generDetail: '', ipNameNo: '', name: '', 
                                oriValidTo: '', covValidTo: '', pchecked: true, mchecked: true, schecked: true, ochecked: true, dchecked: true, 
                                // includechecked: (this.formContract.agrType == 'OS' || this.formContract.agrType =='PS') ? true : false }];
                                includechecked: false}];
                this.tableData3E = [...this.tableData3E, ...myArray];
                this.extendChange(myArray[0], 'add');
            },
            adddata3I () {
                let timeId = new Date().getTime();
                let myArray = [{ timeId: timeId, worksnum: '', inputSoc: '', title: '', languageCode: '', generDetail: '', ipNameNo: '', name: '', 
                                oriValidTo: '', covValidTo: '', pchecked: true, mchecked: true, schecked: true, ochecked: true, dchecked: true, 
                                // includechecked: (this.formContract.agrType == 'OS' || this.formContract.agrType =='PS') ? true : false }];
                                includechecked: true}];
                this.tableData3I = [...this.tableData3I, ...myArray];
                this.extendChange(myArray[0], 'add');
            },
            deletedata3E () {
                if(this.extendSelected.length == 0){
                    this.$toast({tips: '請至少勾選一條記錄'});
                    return;
                }
                this.$msgbox.confirm(`確定進行[删除]操作?`, '提示', {
                    confirmButtonText: '確定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {

                    this.extendSelected.forEach( item => {
                        this.extendChange(item, 'delete');
                        let tableData3EIndex = 9999;
                        this.tableData3E.forEach( (table, i) => {
                            if(table.timeId == item.timeId){
                                tableData3EIndex = i;
                            }
                        })
                        if(tableData3EIndex != 9999){
                            this.tableData3E.splice(tableData3EIndex, 1);
                        }
                    })
                })
            },
            deletedata3I () {
                if(this.extendSelected.length == 0){
                    this.$toast({tips: '請至少勾選一條記錄'});
                    return;
                }
                this.$msgbox.confirm(`確定進行[删除]操作?`, '提示', {
                    confirmButtonText: '確定',closeOnClickModal:false,
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {

                    this.extendSelected.forEach( item => {
                        this.extendChange(item, 'delete');
                        let tableData3IIndex = 9999;
                        this.tableData3I.forEach( (table, i) => {
                            if(table.timeId == item.timeId){
                                tableData3IIndex = i;
                            }
                        })
                        if(tableData3IIndex != 9999){
                            this.tableData3I.splice(tableData3IIndex, 1);
                        }
                    })
                })
            },
            adddata4 () {
                let sipPshare = '';
                let sipMshare = '';
                let sipSshare = '';
                let sipOdshare = '';
                let sipDbshare = '';
                if(this.tableForm.tableData4.length == 0 && this.assignorInfo.oipNameNo){
                    if(this.formContract.oipPshare || this.formContract.oipPshare == 0){
                        sipPshare = 100 - parseFloat(this.formContract.oipPshare ? this.formContract.oipPshare : 0);
                    }
                    if(this.formContract.oipMshare || this.formContract.oipMshare == 0){
                        sipMshare = 100 - parseFloat(this.formContract.oipMshare ? this.formContract.oipMshare : 0);
                    }
                    if(this.formContract.oipSshare || this.formContract.oipSshare == 0){
                        sipSshare = 100 - parseFloat(this.formContract.oipSshare ? this.formContract.oipSshare : 0);
                    }
                    if(this.formContract.oipOdshare || this.formContract.oipOdshare == 0){
                        sipOdshare = 100 - parseFloat(this.formContract.oipOdshare ? this.formContract.oipOdshare : 0);
                    }
                    if(this.formContract.oipDbshare || this.formContract.oipDbshare == 0){
                        sipDbshare = 100 - parseFloat(this.formContract.oipDbshare ? this.formContract.oipDbshare : 0);
                    }
                    
                }
                let myArray = [{sipNameNo: '',sipBaseNo: '', sipChineseFirstName: '', sipChineseName: '', sipRole: '', sipPshareSoc: '', sipPshare: sipPshare, sipMshareSoc: '', sipMshare: sipMshare
                                    , sipSshareSoc: '', sipSshare: sipSshare, sipOdshareSoc: '', sipOdshare: sipOdshare, sipDbshareSoc: '', sipDbshare: sipDbshare}]
                
                this.tableForm.tableData4 = [...this.tableForm.tableData4, ...myArray]
            },
            shareChange(type){
                if(this.tableForm.tableData4.length == 1 && this.assignorInfo.oipNameNo){
                    if(type == 'p' && (this.formContract.oipPshare || this.formContract.oipPshare === 0)){
                        this.tableForm.tableData4[0].sipPshare = 100 - parseFloat(this.formContract.oipPshare ? this.formContract.oipPshare : 0);
                    }else if(type == 'm' && (this.formContract.oipMshare || this.formContract.oipMshare === 0)){
                        this.tableForm.tableData4[0].sipMshare = 100 - parseFloat(this.formContract.oipMshare ? this.formContract.oipMshare : 0);
                    }else if(type == 's' && (this.formContract.oipSshare || this.formContract.oipSshare === 0)){
                        this.tableForm.tableData4[0].sipSshare = 100 - parseFloat(this.formContract.oipSshare ? this.formContract.oipSshare : 0);
                    }else if(type == 'o' && (this.formContract.oipOdshare || this.formContract.oipOdshare === 0)){
                        this.tableForm.tableData4[0].sipOdshare = 100 - parseFloat(this.formContract.oipOdshare ? this.formContract.oipOdshare : 0);
                    }else if(type == 'd' && (this.formContract.oipDbshare || this.formContract.oipDbshare === 0)){
                        this.tableForm.tableData4[0].sipDbshare = 100 - parseFloat(this.formContract.oipDbshare ? this.formContract.oipDbshare : 0);
                    }
                }
            },
            deletedata4 (index, rows) {
                this.$msgbox.confirm(`確定進行[删除]操作?`, '提示', {
                    confirmButtonText: '確定',closeOnClickModal:false,
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    rows.splice(index, 1)
                })
            },
            adddata5 () {
                let myArray = [{ inputSoc: '', remark: '' }]
                this.tableData5 = [...this.tableData5, ...myArray]
            },
            deletedata5 (index, rows) {
                this.$msgbox.confirm(`確定進行[删除]操作?`, '提示', {
                    confirmButtonText: '確定',closeOnClickModal:false,
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    rows.splice(index, 1)
                })
            },
            handleSelect (item) {
                console.log(item)
            },
            saveContract(){
                let _this = this;
                this.$refs['assignorInfo'].validate((valid) => {
                    if (valid) {
                        this.$refs['formContract'].validate((valid) => {
                            if (valid) {
                                this.$refs['formTable'].validate((valid) => {
                                    if (valid) {
                                        this.$refs['assigneeForm'].validate( (valid) => {
                                            if(valid){
                                                this.$refs['territoryForm'].validate( (valid) => {
                                                    if(valid){
                                                        _this.saveContractFn();
                                                    }
                                                })
                                            }
                                        })
                                        
                                    }
                                })
                            }
                        })
                    }
                })
            },
            saveContractFn() {
                if(this.checked1 && this.checked2){
                    this.$msgbox.confirm('auto extension和expired不能同時勾選', '提示', {
                        confirmButtonText: '確定',closeOnClickModal:false,
                        type: 'warning'
                    })
                    return;
                }
                this.formContract.autoExtensionInd = this.checked1 ? 'Y' : 'N';
                this.formContract.expired = this.checked2 ? 'Y' : 'N';
                if(!this.formContract.expired){
                    this.formContract.covRetentionEdate = '';
                }

                this.formContract.agrReceived = this.checked3 ? 'Y' : 'N';
                this.formContract.furtherAssign = this.checked4 ? 'Y' : 'N';
                this.formContract.sd = this.checked5 ? 'Y' : 'N';
                this.formContract.preTerm = this.checked6 ? 'Y' : 'N';
                let isInclude = false
                this.tableForm.tableData1.forEach((item, index) => {
                    item.indicator = item.checkedInclude ? '+' : '-';
                    if(item.checkedInclude && !isInclude){ //至少勾选一个include
                        isInclude = true
                    }
                })
                if(!isInclude){
                    this.$toast({
                        tips: 'Territory中，至少勾选一个地区'
                    })
                    return;
                }
                let extendAjax = this.$utils.copy(this.extendUpdate);
                extendAjax.forEach((item, index) => {
                    item.indicator = item.includechecked ? '+' : '-';
                    item.per = item.pchecked ? 'Y' : 'N';
                    item.mec = item.mchecked ? 'Y' : 'N';
                    item.zyn = item.schecked  ? 'Y' : 'N';
                    item.od = item.ochecked  ? 'Y' : 'N';
                    item.db = item.dchecked  ? 'Y' : 'N';
                })
                /*
                    校验extend , 每行数据，ip work 至少填一项
                */
                let extendFlag = true;
                extendAjax.forEach((item, index) => {
                    if(!item.ipBaseNo && !item.worksnum){
                        extendFlag = false;
                    }
                })
                if(!extendFlag){
                    this.$toast({
                        tips: 'Extend中，Ipi或Work至少填寫一項'
                    })
                    return;
                }




               /**
                * end
                */

                if(this.tableForm.tableData4.length == 0){
                    this.$toast({tips: '請至少填寫一行Assignee'});
                    return;
                }
                if(this.tableForm.tableData1.length == 0){
                    this.$toast({tips: '請至少填寫一行Territory'});
                    return;
                }else{
                    // if()
                }
                if(this.tableForm.tableData2.length == 0){
                    this.$toast({tips: '請至少填寫一行Source'});
                    return;
                }
                if(!this.formContract.covPostTermDate){
                    this.formContract.covPostTermDate=null
                }
                let params = {
                    'agreement': this.formContract,
                    'assigneeList': this.tableForm.tableData4,
                    'territory': this.tableForm.tableData1,
                    'source': this.tableForm.tableData2,
                    'remark': this.tableData5,
                    'extend': extendAjax,
                    assignor: this.assignorInfo
                }
                axios.post('/agreement/saveAgreement', params).then(res => {
                    if (res.status === 200) {
                        if(res.data.code != 200){
                            this.$msgbox.confirm(res.data.message, '提示', {showCancelButton: false});
                        }else{
                            this.$toast({tips: (this.opType == 'add' ? '新增' : '編輯' ) + '合同成功'});
                            this.$bus.$emit('closeCurrentTab', () => { this.$router.push({name: 'contractList', query: {update: true}}) });
                        }
                     }
                })
            },
            addFormAssignee () {
            },
            selectTypeId(){
                if(this.contractNo){
                    return;
                }
                this.showSelectTypeId = true;
            },
            checkedTypeId(type, label){
                this.formContract.agrTypeId = type;
                this.showSelectTypeId = false;
                if(type == 6){
                    this.tableForm.tableData4.forEach( (item, index) => {
                        this.tableForm.tableData4[index].sipRole = 'PA';
                    })
                }
            },
            getTerritroy(index, row){
                this.editTerritoryIndex = index;
                this.territorySearch(1);
                this.tisA = row.tisA;
                this.territoryShow = true;
            },
            territorySearch(page){
                let ajaxData = {
                    tisA: this.tisA ? this.tisA : '',
                    page: {
                        pageNum: page ? page : 1,
                        pageSize: 10
                    }
                }
                        this.tableresult='數據加載中...'

                this.$http.post("/ref/getRefTerritoryListPage", ajaxData).then( res => {
                    if(res.success){
                        this.territoryList = res.data.list;
                        this.territoryTotal = res.data.total;
              this.tableresult=this.territoryList.length==0?'暫無數據':' '
                        
                    }
                })
            },
            checkedTerritory(index, info){

                this.$set(this.tableForm.tableData1[this.editTerritoryIndex], 'generDetail', info.description);
                this.$set(this.tableForm.tableData1[this.editTerritoryIndex], 'tisA', info.tisA);
                this.$set(this.tableForm.tableData1[this.editTerritoryIndex], 'tisN', info.tisN);

                // this.tableForm.tableData1[this.editTerritoryIndex].generDetail =  info.description;
                // this.tableForm.tableData1[this.editTerritoryIndex].tisA = info.tisA;
                // this.tableForm.tableData1[this.editTerritoryIndex].tisN = info.tisN;
                this.territoryShow = false;
                this.$refs['territory'+this.editTerritoryIndex].focus();
                this.$refs['territory'+this.editTerritoryIndex].blur();

            },
            searchTerr(index, item) {
                if(!item.tisA){
                    return;
                }
                let myformdata = {
                    tisA: item.tisA
                };
                this.$http.post("/ref/getRefTerritoryList", myformdata).then(res => {
                    if (res.success) {
                        if(res.data.length == 0){
                            this.$toast({tips: '未查詢到Territory，請確認Code輸入正確'})
                        }else{
                            this.tableForm.tableData1[index].generDetail =  res.data[0].description;
                            this.tableForm.tableData1[index].tisA = res.data[0].tisA;
                            this.tableForm.tableData1[index].tisN = res.data[0].tisN;
                        }
                    }
                });
            },
            getData () {
                if(this.$route.query.contractNo == '000'){
                    console.log('000 脏數據');
                    return ;
                }
                let params = {
                    agrNo: this.$route.query.contractNo
                }
                const loading = this.$loading();
                axios.get('/agreement/getAgreementByAgrNo', {params}).then(res => {
                    loading.close();
                    this.tableForm.tableData1 = []
                    if (res.status === 200) {
                        this.territoryobj = res.data.refTerritoryMap
                        // this.formContract = Object.assign(res.data.agreement, res.data.assignor)
                        res.data.agreement = res.data.agreement ? res.data.agreement : {};
                        this.formContract = res.data.agreement;
                        this.id = res.data.agreement.id;


                        this.assignorInfo = res.data.assignor;
                        let chineseName = this.assignorInfo.oipChinName ? ('(' + this.assignorInfo.oipChinName + ')') : '';
                        this.assignorInfo.oipName = this.assignorInfo.oipName + chineseName;
                        this.queryRight(this.assignorInfo.oipNameNo, 'info');

                        this.tableForm.tableData4 = res.data.assigneeList;
                        this.tableForm.tableData4.forEach( (item, index) => {
                            this.queryRight(item.sipNameNo, 'assignee', index);
                            item.sipChinName = item.sipName + ( item.sipChinName ? ('('+item.sipChinName+')') : '');
                        })
                        res.data.territory.forEach(item => {
                            let obj = {}
                            let tis = item.tisA;
                            obj.generDetail = this.territoryobj[tis] ? this.territoryobj[tis].description : '';
                            obj.checkedInclude = item.indicator == '+' ? true : false;
                            this.tableForm.tableData1.push(Object.assign(obj, item))
                        })
                        this.$set(this.tableForm, 'tableData2', res.data.source);
                        this.tableData5 = res.data.remark
                        if (res.data.agreement.autoExtensionInd === 'Y') {
                            this.checked1 = true;
                        }
                        if (res.data.agreement.autoExtensionInd === 'N') {
                            this.checked1 = false;
                        }

                        if (res.data.agreement.expired === 'Y') {
                            this.checked2 = true
                        }
                        if (res.data.agreement.expired === 'N') {
                            this.checked2 = false
                        }

                        if (res.data.agreement.agrReceived === 'Y') {
                            this.checked3 = true
                        }
                        if (res.data.agreement.agrReceived === 'N') {
                            this.checked3 = false
                        }

                        if (res.data.agreement.furtherAssign === 'Y') {
                            this.checked4 = true
                        }
                        if (res.data.agreement.furtherAssign === 'N') {
                            this.checked4 = false
                        }

                        if (res.data.agreement.sd === 'Y') {
                            this.checked5 = true
                        }
                        if (res.data.agreement.sd === 'N') {
                            this.checked5 = false
                        }

                        if (res.data.agreement.preTerm === 'Y') {
                            this.checked6 = true
                        }
                        if (res.data.agreement.preTerm === 'N') {
                            this.checked6 = false
                        }

                    }
                    this.$forceUpdate();
                    //单独查询extend 默认一次100条吧
                    this.queryExtend(1);
                }).catch(()=>{
                    loading.close();
                })
            },
            getIdType() {
                    this.tableresult='數據加載中...'
                axios.get("/agreement/getAgreementType").then(res => {
                    if (res.status === 200) {
                        this.idTypeList = res.data;
                        this.tableresult=this.idTypeList.length==0?'暫無數據':' '

                    }
                });
            },
            queryRight(ipNameNo, type, index){
                let params = { name_no: ipNameNo };
                params.page_num = 1;
                params.page_size = 10;
                let formData = qs.stringify(params)
                let config = {headers: { 'content-type': 'application/x-www-form-urlencoded' }}
                this.$http.post('/ip/name/es', formData, config).then(res => {
                    if (res.success) {
                        if(res.data.list.length > 0){
                            this.handleRight(res.data.list[0].agreement, type, index);
                        }
                    }
                })

            },
            souceIdChange(index, value){
                value = value+'';
                // value = value.replace(/\D/g, '')
                this.tableForm.tableData2[index].source = value;
                if(value && value.length <= 3){
                    // 查询协会
                    this.$http.get('/ref/society/getSocietyBySocietyCode', {params: {societyCode: value}}).then( res => {
                        if(res.success && res.data.code == 200){
                            if(res.data.data.refSociety){
                                this.tableForm.tableData2[index].sourceContent = res.data.data.refSociety.societyName
                                this.$refs['name'+index].focus();
                                this.$refs['name'+index].blur();
                            }else{
                                this.$toast({tips: '未查詢到該協會'});
                                this.tableForm.tableData2[index].sourceContent = '';
                                this.$refs['name'+index].focus();
                                this.$refs['name'+index].blur();
                            }
                        }
                    })
                }else if(value && value.length > 3){
                    // 查询ip
                    let formData = qs.stringify({name_no: value, page_num: 1, page_size: 10});
                    let config = {headers: { 'content-type': 'application/x-www-form-urlencoded' }}
                    this.$http.post('/ip/name/es', formData, config).then(res => {
                        if (res.status === 200) {
                            if(res.data.list.length > 0){
                                this.tableForm.tableData2[index].sourceContent = res.data.list[0].chinese_name ? res.data.list[0].chinese_name : res.data.list[0].name;
                                this.$refs['name'+index].focus();
                                this.$refs['name'+index].blur();
                            }else{
                                this.$toast({tips: '未查詢到該會員'});
                                this.tableForm.tableData2[index].sourceContent = '';
                                this.$refs['name'+index].focus();
                                this.$refs['name'+index].blur();
                            }
                        }
                    })
                }

            },
            queryExtend(page){
                console.log(page)
                if(this.querying){
                    return;
                }
                let ajaxData = {
                    agrNo: this.assignorInfo.contentAgrNo,
                    keyWord: this.extendFilter,
                    page_num: page ? page : 1,
                    page_size: 100
                }
                this.extendPage = ajaxData.page_num;
                this.querying = true;
                this.$http.post('/agreement/getAgrAgreementExtendWithPage?' + this.$utils.parseParams(ajaxData), {}, {loading: true}).then( res => {
                    this.querying = false;
                    if(res.success){
                        let tempTable = res.data.data ? res.data.data.list : [];
                        tempTable.length && tempTable.forEach( item => {
                            item.includechecked = item.indicator == '+' ? true : false;
                            item.pchecked = item.per == 'Y' ? true : false;
                            item.mchecked = item.mec == 'Y' ? true : false;
                            item.schecked = item.zyn == 'Y' ? true : false;
                            item.ochecked = item.od == 'Y' ? true : false;
                            item.dchecked = item.db == 'Y' ? true : false;
                            item.timeId = new Date().getTime() + item.id;
                            item.title = item.workTitle ? item.workTitle : '';
                            item.name = item.ipChineseName ? item.ipChineseName : item.ipName
                            // item.name = item.ipName + (item.ipChineseName ? ('('+item.ipChineseName+')') : '');

                        })
                        if(ajaxData.page_num != 1){
                            this.tableData3 = this.tableData3.concat(this.$utils.copy(tempTable));
                        }else{
                            this.tableData3 = this.$utils.copy(tempTable);
                        }
                        /*
                            tableData3 根据includechecked 拆出来 tableData3E  tableData3I
                        */
                        this.tableData3E = [];
                        this.tableData3I = [];
                        this.tableData3.forEach( item => {
                        // tempTable.length && tempTable.forEach( item => {
                            if(item.includechecked){
                                this.tableData3I.push(this.$utils.copy(item));
                            }else{
                                this.tableData3E.push(this.$utils.copy(item));
                            }
                        })
                        console.log('this.tableData3E')
                        console.log(this.tableData3E)
                        console.log('this.tableData3I')
                        console.log(this.tableData3I)
                        this.hasMore = res.data.data.hasNextPage;
                        
                        this.$nextTick(()=>{
                            if(this.extendPage!=1){
                                let box = document.querySelector("#extendBox");
                                let table = document.querySelector("#extendTable");
                                let b = table.offsetHeight;
                                setTimeout(()=>{
                                    box.scrollTop=b-(b/this.extendPage)
                                },30)
                            }
                        })
                    }
                }).catch( () => {
                    this.querying = false;
                })
            },
            // this.extendUpdate
            extendChange(row, type){
                console.log(row.action, ' : ', type);
                //操作类型 add edit delete
                let has = false;
                if(row.action){
                    if(row.action == 'add'){
                        if(type == 'delete'){
                            console.log('1');
                            row.action = 'delete';
                        }else if(type == 'edit'){
                            console.log('2');

                            row.action = 'add';
                        }
                    }else if(row.action == 'edit'){
                        if(type == 'delete'){
                            console.log('3');

                            row.aciton = 'delete';
                        }
                    }
                }else{
                            console.log('4');

                    row.action = type;
                }
                this.extendUpdate.forEach( (item, index) => {
                    if(item.timeId == row.timeId){
                        has = true;
                        // item = this.$utils.copy(row);
                        this.extendUpdate[index] = this.$utils.copy(row);

                    }
                })
                if(!has){
                    this.extendUpdate.push(this.$utils.copy(row));
                }
            },
            addListener(){

                let box = document.querySelector("#extendBox");
                let table = document.querySelector("#extendTable");
                box.addEventListener('scroll', () =>{ 
                    let height = box.clientHeight;
                    let a = box.scrollTop;
                    let b = table.offsetHeight;
                    a = Math.ceil(a);
                    if((a + height) >= b){
                        if(this.hasMore){
                            // this.extendPage ++;
                            // this.queryExtend(this.extendPage);
                        }
                        
                    }
                })
            },
            deleteContract(){
                this.$prompt('確定是否刪除，確認請輸入“yes”', '提示', {
                    confirmButtonText: '确定',closeOnClickModal:false,
                    cancelButtonText: '取消',
                }).then(({ value }) => {
                    if(value && value.toUpperCase() === 'YES'){
                        this.deleteContractFn();
                    }else{
                        this.$msgbox.confirm('刪除請確認輸入“yes”, 否則請點擊取消', '提示', {showCancelButton: false});
                        // this.$toast({tips: '刪除請確認輸入“yes”, 否則請點擊取消'})
                    }
                })
            },
            deleteContractFn(){
                // console.log('agrNo: ', this.contractNo);
                // return;
                this.$http.post('/agreement/deleteAgreement',{agrNo: this.contractNo}).then( res => {
                    if(res.success){
                        if(res.data.code == 200){
                            this.$msgbox.confirm('刪除成功', '提示', {showCancelButton: false});
                            this.$bus.$emit('closeCurrentTab', () => {
                                this.$router.push({name: 'contractList', query: {update: true}})
                            });
                        }else{
                            this.$msgbox.confirm(res.data.message, '提示', {showCancelButton: false});
                        }
                    }
                    
                })
            }
        },
        mounted () {
            this.contractNo = this.$route.query.contractNo;
            if(this.$route.query.contractNo){
                this.getData();
                this.opType = 'edit';
            }else{
                this.opType = 'add';
            }

            this.getIdType();
            this.$nextTick( () => {
                // this.addListener();
            })
            
        },
        activated(){
            if(this.contractNo && (this.contractNo != this.$route.query.contractNo)){
                this.contractNo = this.$route.query.contractNo;
                this.getData();
            }
        }
    }
</script>

<style>
    .el-card {
        margin-bottom: 10px;
    }
    .el-icon-circle-plus-outline {
        cursor: pointer;
    }
    .el-icon-delete {
        cursor: pointer;
    }
</style>
<style lang="scss" scoped>
    @import "../../assets/scss/works.scss";

    // /deep/ .interest label{
    //     width: auto !important;
    //     margin-left: 6px !important;
    // }
    /deep/ .interest .el-input{
        width: 50px !important;
    }
    // /deep/ .interest .el-form-item:first-child label{
    //     margin-left: 40px !important;
    // }

    /deep/ .interest input{
        padding: 0 10px !important;
    }
    .soc{
        width: 66px;
        float: left;
        margin-right: 4px;
    }
    .share{
        width: 40px;
        float: left;
    }
    /deep/ .el-tabs__nav-scroll{
        padding-left: 10px;
    }
    .pointer{
        cursor: pointer;
    }
</style>
