<template>
    <!-- 原文件 處理後 拆分成的多個小文件 -->
    <div class="ipibpox">
        <!--<el-form :inline="true" :model="formData" ref="form" class="demo-form-inline">
            <el-form-item prop="composer">
                <el-input v-model="formData.composer" placeholder="composer"></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="searchFn()">查詢</el-button>
            </el-form-item>
            <el-form-item>
                <span class="clear-search" @click="clearSearch()">清除搜索</span>
            </el-form-item>
        </el-form>-->
        <el-table :empty-text="tableresult" stripe :data="tableData" border style="width: 100%" v-loading="loading">
            <el-table-column
                prop="id"
                width="60"
                label="FID">
            </el-table-column>
            <el-table-column
                prop="fileName"
                width="200"
                label="File Name">
                <template slot-scope="scope">
                    <span :title="scope.row.fileName">{{scope.row.fileName}}</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="filePath"
                width="200"
                label="File Path">
                <template slot-scope="scope">
                    <span :title="scope.row.filePath">{{scope.row.filePath}}</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="productName"
                label="product name">
            </el-table-column>
            <el-table-column
                prop="currency"
                label="Currency">
            </el-table-column>
            <el-table-column
                prop="listFileTotal"
                label="number_of_lines">
                <template slot="header" slot-scope="scope">
                    <span title="number_of_lines">number_of_lines</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="matched"
                label="matched">
                <template slot="header" slot-scope="scope">
                    <span title="matched">matched</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="unmatched"
                label="unmatched">
                <template slot="header" slot-scope="scope">
                    <span title="unmatched">unmatched</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="createTime"
                width="190"
                label="createTime">
                <template slot-scope="scope">
                    {{scope.row.createTime?scope.row.createTime:''}}
                </template>
            </el-table-column>
            <el-table-column
                prop="clickNumber"
                label="使用次數">
            </el-table-column>
            <!--<el-table-column
                prop="fileStatus"
                label="File Status">
                <template slot-scope="scope">
                    <span v-if="scope.row.fileStatus === 0">待解析</span>
                    <span v-else-if="scope.row.fileStatus === 1">解析完成</span>
                    <span v-else-if="scope.row.fileStatus === 2">解析出錯</span>
                </template>
            </el-table-column>-->
            <el-table-column
                fixed="right"
                label="operation"
                width="100">
                <template slot-scope="scope">
                    <el-button @click="viewFile(scope.row)" type="text" size="small" style="cursor:pointer;">查看明細</el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :total="total" @current-change="searchFn" :current-page="currentPage">
        </el-pagination>
    </div>
</template>

<script>
    export default {
        name: 'claimFileList',
        data () {
            return {
                tableData: [],
                tableresult:' ',
                total: 0,
                currentPage: 1,
                formData: {
                    queueId:'',
                    page_num:1,
                    page_size:10
                },
                loading: false
            }
        },
        activated(){
            this.init();
        },
        methods: {
            clearSearch(){
                this.init();
            },
            init(){
                let id = this.$route.query.queueId;
                this.formData.queueId = id
                this.searchFn();
            },
            handleSelect(item) {
                console.log(item);
            },
            searchFn (page) {
                this.loading = true;
                this.formData.page_num = page ? page : 1;
                      this.tableresult = '數據加載中...'
                this.$http.get('/list/dsp/file/listListDspFileBaseByQueueId',{params:this.formData}).then(res => {
                    if(res.success){
                        console.log(res.data.data.list)
                        this.tableData = res.data.data.list;
                        this.total = res.data.data.total;
                        this.currentPage = page ? page : 1;
                    }
                          this.tableresult = this.tableData.length == 0 ? '暫無數據' : ' '
                    this.loading = false;
                })
            },
            handleCurrentChange (val) {
                this.formData.page_num = val
                this.searchFn();
            },
            viewFile(row){
                this.$router.push({name: 'claimFileDetail',query:{baseId:row.id}})
            }
        }
    }
</script>

<style>
</style>
