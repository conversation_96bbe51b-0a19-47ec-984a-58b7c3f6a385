<template>
    <div style="width:860px">
        <el-form label-position="right" label-width="140px" ref="form" :rules="rules" :model="createReportData" class="demo-form-inline">
            <el-form-item label="Company" prop="company" style="width:660px">
                <el-input v-model="createReportData.company" readonly></el-input>
            </el-form-item>
            <el-form-item label="claim date" required>
                <el-form-item prop="startDate" style="width:185px;display: inline-block">
                    <date-picker v-model="createReportData.startDate" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd"></date-picker>
                </el-form-item>
                -
                <el-form-item prop="endDate" style="width:185px;display: inline-block">
                    <date-picker v-model="createReportData.endDate" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd"></date-picker>
                </el-form-item>

            </el-form-item>
            <el-form-item label="type" required>
                <el-radio-group v-model="createReportData.fileType" @change="fileTypeSelect">
                    <el-radio label="CCID13" v-if="createReportData.company != 'META'">CCID13</el-radio>
                    <el-radio label="CCID14">CCID14</el-radio>
                    <el-radio label="CCR" v-if="createReportData.company != 'META'">CCR</el-radio>
                    <el-radio label="preClaim" v-if="createReportData.company == 'youtube'">pre claim</el-radio>
                    <el-radio label="fbcsv" v-if="createReportData.company == 'META'">FB.csv(preClaim)</el-radio>
                    <el-radio label="CCID14(DataExchange)" v-if="createReportData.company == 'META'">CCID14(DataExchange)</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="product">
                <el-select v-model="products" :disabled="productsDisabled" multiple placeholder="請選擇" class="m-select">
                    <template v-for="item in productList">
                        <!-- v-if="item.name != 'preClaim'" -->
                        <el-option
                            :key="item.value"
                            :label="item.label"
                            :value="item">
                        </el-option>
                    </template>

                </el-select>
                <!-- <el-radio v-model="createReportData.isPart" label="1">备选项</el-radio>
                <el-radio v-model="createReportData.isFile" label="2">备选项</el-radio> -->
                <el-checkbox @change="changeRepor('isPart')" :true-label='1' :false-label='0' v-model="createReportData.isPart">根據product拆分輸出</el-checkbox>
                <el-checkbox @change="changeRepor('isFile')" :true-label='1' :false-label='0' v-model="createReportData.isFile">依照檔案拆分輸出</el-checkbox>
                 <el-checkbox @change="changeRepor('isRight')" :true-label='1' :false-label='0' v-model="createReportData.isRight">依照PR/MEC拆分</el-checkbox>
            </el-form-item>
            <el-form-item label="按月输入">
                <el-radio-group v-model="createReportData.isMonth">
                    <el-radio :label="0">否</el-radio>
                    <el-radio :label="1">是</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="計算委任協會作品">
                <el-radio-group v-model="createReportData.isAppoint">
                    <el-radio :label="0">否</el-radio>
                    <el-radio :label="1">是</el-radio>
                </el-radio-group>
            </el-form-item>
            <!-- <el-form-item label="output path" style="width:660px">
                <el-input v-model="createReportData.filePath" placeholder="輸入地址路徑"></el-input>
            </el-form-item> -->
            <el-form-item>
                <el-button type="primary" @click="genReport">生成</el-button>
                <el-button type="primary" @click="batchReport">batch Partial Run & gen Report</el-button>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
    export default {
        name: 'createreport',
        data(){
            return {
                createReportData:{
                    fileType:'CCID13',
                    isMonth:0
                },
                productList: [],
                productsDisabled:false,
                products:[],
                dateRange:[],
                rules:{
                    company:[
                        { required: true, message: '请输入company', trigger: 'blur' },
                    ],
                    startDate:[
                        { required: true, message: '请输入startDate', trigger: 'blur' },
                    ],
                    endDate:[
                        { required: true, message: '请输入endDate', trigger: 'blur' },
                    ]
                }
            }
        },
        mounted(){
            let params = JSON.parse(this.$route.query.data)
            console.log(params)
            this.createReportData = params
            if(!params.fileQueueIds){
                this.getProducts()
            }
            if(params.products == 'preClaim'){
                // this.products = ['preClaim']
                this.productsDisabled = true
            }
            if(params.products){
                let products = params.products.split(';')
                let ids = params.claimMinimaInfoIds.split(';')
                products.map((product,index) => {
                    this.products.push({
                        name:product,
                        value:Number(ids[index])
                    })
                    if(params.products == 'preClaim' && params.fileQueueIds){
                        this.productList.push({
                            label:product,
                            name:product,
                            value:Number(ids[index])
                        })
                    }
                })
            }
        },
        methods: {
            changeRepor(data){
                if(this.createReportData.isPart && data=='isPart'){
                    this.$set(this.createReportData,'isFile',false)
                }
                if(this.createReportData.isFile && data=='isFile'){
                    this.$set(this.createReportData,'isPart',false)
                }
            },
            getProducts(){
                let params = {
                    setId :this.createReportData.claimSetId
                }
                this.$http.get('/claim/minima/product',{params}).then(res => {
                    if(res.status === 200){
                        res.data.map(item => {
                            item && this.productList.push({
                                label:item.productFullName+'('+item.productShortName+')',
                                value:item.id,
                                name:item.productShortName
                            })
                        })
                    }
                })
            },
            genReport(){
                let params = JSON.parse(this.$route.query.data)
                this.createReportData.status = params.status
                this.createReport()
            },
            batchReport(){
                this.createReportData.status = 7
                this.createReport()
            },
            createReport(){
                let claimMinimaInfoIds = []
                let products = []
                this.products.map(item => {
                    claimMinimaInfoIds.push(item.value)
                    if(item.name){
                        products.push(item.name)
                    }else{
                        products.push(item)
                    }
                })
                claimMinimaInfoIds = claimMinimaInfoIds.join(',')
                products = products.join(';')
                this.createReportData.claimMinimaInfoIds = claimMinimaInfoIds
                this.createReportData.products = products
                this.createReportData.isPart = this.createReportData.isPart?1:0
                this.createReportData.isFile = this.createReportData.isFile?1:0
                this.createReportData.isRight = this.createReportData.isRight?1:0
                if(this.createReportData.endDate<this.createReportData.startDate){
                    this.$toast({tips:'End Date不能小於Start Date'})
                    return
                }
                if(!this.createReportData.fileType){
                    this.$toast({tips:'type不能為空'})
                    return
                }
                this.$refs.form.validate(validate => {
                    if(validate){
                        this.$http.post('/claim/ccid/header/if',this.createReportData).then(res => {
                            console.log(res)
                            if (res.status==200) {
                                if(!res.data){
                                this.createFn()
                                }else{
                                    this.$alert('報告已經生成，是否重新生成', '溫馨提示', {
                                        confirmButtonText: '確定',
                                        showCancelButton: true,
                                        callback: action => {
                                            if(action === 'confirm'){
                                                this.createFn()
                                            }
                                        }
                                    });
                                }
                            }else{
                                this.$alert('報告生成失敗')
                            }

                        })
                    }
                })

            },
            createFn(){
                return this.$http.post('/claim/ccid/header',this.createReportData).then((res) => {
                    if(res.data.code == 200){
                        this.$message({
                            type:'success',
                            message:'保存成功',
                            duration:1500,
                            onClose:() => {
                                this.$bus.$emit('closeCurrentTab', () => {
                                    this.$router.push({name: 'distribute-report', query: {update: true}});
                                })
                            }
                        })
                    }else{
                        this.$message(res.data.message)
                    }
                })
            },
            fileTypeSelect(val){
                let products = this.products
                if(val == 'preClaim'){
                    this.products = ['preClaim']
                    this.productsDisabled = true
                }else{
                    if(this.createReportData.fileQueueIds){
                        this.productList=[]
                    }
                    this.products = []
                    this.productsDisabled = false
                }
            }
        }
    }
</script>
<style lang="scss">
.m-select{
    .el-select__tags{
        overflow: hidden;
    }
}
</style>
<style scoped lang="scss">
    /deep/ .el-form-item__label {
        margin-left: 0 !important;
    }

</style>
