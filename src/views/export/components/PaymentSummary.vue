<template>
  <div>
    <el-dialog
      title="支票報表-總表"
      :visible.sync="showExport"
      width="800px"
      :show-close="true"
      :close-on-click-modal="false"
      :before-close='close'
    >
      <el-form :inline="true" class="demo-form-inline" @keyup.enter.native="requData()">
        <el-form-item label="Autopay No" label-width="150px" required>
          <el-input
            v-model="memberData.autopayNo"
            placeholder="雙擊查詢"
            @dblclick.native="getAutopay()"
            @change="changeAuto"
            style="width: 150px"
          ></el-input>
        </el-form-item>
        <el-form-item label="Autopay Description" label-width="170px">
          <el-input
            v-model="memberData.autopayDescription"
            placeholder=""
            style="width: 150px"
            readonly
          ></el-input>
        </el-form-item>
        <el-form-item label="PA Name No" label-width="150px">
          <el-input
            v-model="memberData.paNameNo"
            placeholder="雙擊查詢"
            @dblclick.native="getPaName()"
            @change="changePa"
            style="width: 150px"

          ></el-input>
        </el-form-item>
        <el-form-item label="PA Name" label-width="170px">
          <el-input
            v-model="memberData.name"
            placeholder=""
            readonly
            style="width: 150px"
          ></el-input>
        </el-form-item>
        <el-form-item label="IP BASE NO" label-width="150px">
          <el-input
            v-model="memberData.ipBaseNo"
            placeholder=""
            style="width: 150px"
          ></el-input>
        </el-form-item>
        <el-form-item label="Dist No" label-width="170px">
          <el-input
            v-model="memberData.distNo"
            placeholder=""
            style="width: 150px"
            readonly
          ></el-input>
        </el-form-item>
      </el-form>

      <div slot="footer">
        <el-button type="primary" @click="requData()">導出</el-button>
      </div>
    </el-dialog>
    <autopay-no ref="autopay" :autopayNo='autopayNo' @checkAutopay='checkAutopay' @flag='flag1=true'></autopay-no>
    <pa-name ref="paName" :paNameNo='paNameNo' @checkPaName='checkPaName' @flag='flag2=true'></pa-name>
  </div>
</template>
<script>
import autopayNo from './autopayNo';
import paName from './paName';
export default {
  name: "",
  data() {
    return {
      showExport: false,
      exportType: "member",
      memberData: {
        autopayNo: "",
        autopayDescription: "",
        paNameNo:'',
        name:'',
        distNo:'',
        ipBaseNo:''
      },
      autopayNo:'',
      paNameNo:'',
      flag1:true,
      flag2:true,
    };
  },
  components: { autopayNo,paName },
  methods: {
    checkPaName(data){
      this.flag2=true
      this.$set(this.memberData,'paNameNo',data.ip_name_no)
      this.$set(this.memberData,'name',data.name)
    },
    changePa(data){
      if(data){
        if(this.flag2){
          this.paNameNo = data
          this.flag2=false
          this.$nextTick(()=>{
            this.$refs.paName.paNameDataC()
          })
        }
      }else{
        this.$set(this.memberData,'name','')
      }
    },
    close(){
      this.showExport=false
      this.memberData = {
        autopayNo: "",
        autopayDescription: "",
        paNameNo:'',
        name:'',
        distNo:'',
        ipBaseNo:''
      }
    },
    exportList() {
      this.showExport = true;
    },
    getPaName(){
      if(this.flag2){
        this.paNameNo = ''
        this.flag2=false
        this.$refs.paName.paNameDataD()
      }
    },
    checkAutopay(data){
      this.flag1=true
      console.log(data)
      this.$set(this.memberData,'autopayNo',data.autopayNo)
      this.$set(this.memberData,'autopayDescription',data.autopayDescription)
      this.$set(this.memberData,'distNo',data.distNoDetails)
    },
    exportList() {
      this.showExport = true;
    },
    changeAuto(data){
      if(data){
        if(this.flag1){
          this.autopayNo = data
          this.flag1=false
          this.$nextTick(()=>{
            this.$refs.autopay.autopayDataC()
          })
        }
      }else{
        this.$set(this.memberData,'autopayDescription','')
      }
      
    },
    getAutopay() {
      if(this.flag1){
        this.flag1=false
        this.autopayNo = ''
        this.$refs.autopay.autopayDataD()
      }
    },
    requData() {
      let params = this.$utils.copy(this.memberData);
      if(!params.autopayNo){
        this.$toast({ tips: "Autopay No不能為空" });
        return
      }
      this.$http.get(`/dist/autoPay/Report/reportDistAutoPay880`, {params, responseType: "blob"})
        .then((res) => {
          // this.showExport = false;
          let tempBlob = new Blob([res.data], { type: 'application/json' })
          let reader = new FileReader()
          reader.onload = e => {
            let res1 = e.target.result
            // 此处对fileReader读出的结果进行JSON解析
            // 可能会出现错误，需要进行捕获
            try {
              let json = JSON.parse(res1)
              this.$toast({ tips: json.message })
              // console.log('========',json)
              //正常json数据格式
            } catch (err) {
              // 该异常为无法将字符串转为json
              // 说明返回的数据是一个流文件
              // 不需要处理该异常，只需要捕获即刻
              this.close()
              this.$utils.downloadByBlobPDF(
              res.data,
              res.headers["content-disposition"]
                ? res.headers["content-disposition"].split(";")[1]
                : "=支票報表-總表導出"
              );
            }
          }
          // 将blob对象以文本的方式读出，读出完成后将会执行 onload 方法
          reader.readAsText(tempBlob)
        });
    },
  },
};
</script>
<style scoped>
/deep/ .el-dialog__body {
  padding-top: 0;
}
/deep/ .el-radio + .el-radio {
  margin-left: 0;
}
</style>