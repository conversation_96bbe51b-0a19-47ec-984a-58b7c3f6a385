<template>
    <div>
        <el-form :inline="true" :model="searchForm" class="demo-form-inline" @keyup.enter.native="querySearch(1)">
            <el-form-item
                prop="adjDistNo"
                label="Dist No"
                class="is-required"
            >
                <el-input v-model="searchForm.adjDistNo" placeholder=""  style="width: 100px"></el-input>
            </el-form-item>
            <el-form-item label="old dist No">
                <el-input v-model.trim="searchForm.oldDistNo" placeholder=""  style="width: 100px"></el-input>
            </el-form-item>
            <el-form-item label="work No">
                <el-input v-model.trim="searchForm.workId" placeholder=""  style="width: 100px"></el-input>
            </el-form-item>
            <el-form-item label="ip name">
                <el-input v-model="searchForm.ipName" :title="searchForm.ipName" placeholder="雙擊選擇" style="width: 150px" @dblclick.native="getIp()" readonly></el-input>
            </el-form-item>
            <el-form-item label="ip name no">
                <el-input v-model="searchForm.ipNameNo"  placeholder="雙擊選擇" style="width: 140px" @dblclick.native="getIp()" readonly></el-input>
            </el-form-item>
            <el-form-item label="ip base No">
                <el-input v-model="searchForm.ipBaseNo" placeholder="雙擊選擇" style="width: 140px" @dblclick.native="getIp()" readonly></el-input>
            </el-form-item>
            <el-form-item label="status">
                <el-switch
                    v-model="searchForm.distFlag"
                    active-value="Y"
                    inactive-value="N"
                    active-color="#13ce66"
                    inactive-color="#e9e9e9">
                </el-switch>
            </el-form-item>
            <el-form-item>
                <el-button @click="showPull=true" type="primary" v-if="isAuth('distribute:sd:list:pull')">拉取</el-button>
                <el-button @click="querySearch(1)" type="primary" v-if="isAuth('distribute:sd:list:find')">搜索</el-button>
            </el-form-item>
            <el-form-item>
                <span class="clear-search" @click="clearSearch()" v-if="isAuth('distribute:sd:list:find')">清除搜索</span>
            </el-form-item>
        </el-form>
        <el-table
            :data="tableData"
            border
            stripe
            v-loading="loading"
            :empty-text="emptyText"
            style="width: 100%">
            <el-table-column
                type="index"
                label="序號">
            </el-table-column>
            <el-table-column
                prop="adjDistNo"
                label="dist no">
            </el-table-column>
            <el-table-column
                prop="oldDistNo"
                label="old dist no">
            </el-table-column>
            <el-table-column
                prop="adjType"
                label="dist type">
            </el-table-column>
            <el-table-column
                prop="adjMethod"
                label="data from">
                <template slot-scope="scope">
                    {{scope.row.adjMethod === 'A'?'自動':'手動'}}
                </template>
            </el-table-column>
            <el-table-column
                prop="distFlag"
                label="狀態">
                <template slot-scope="scope">
                    {{scope.row.distFlag === 'Y'?'已處理':'未處理'}}
                </template>
            </el-table-column>
            <el-table-column
                prop="categoryCode"
                label="category code">
            </el-table-column>
            <el-table-column
                prop="workId"
                label="work No">
                <template slot-scope="scope">
                    <span :title="scope.row.workId">{{scope.row.workId}}</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="workSocietyCode"
                label="work soc">
            </el-table-column>
            <el-table-column
                prop="title"
                label="title">
            </el-table-column>
            <el-table-column
                prop="distAmount"
                label="待分配金額">
            </el-table-column>
            <el-table-column
                label="operation">
                <template slot-scope="scope">
                    <div class="option">
                        <el-button @click="edit(scope.row)" type="text" size="small">處理</el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :total="total" @current-change="handleCurrentChange" :current-page="currentPage">
        </el-pagination>
        <select-ip v-if="IpTableVisible" ref="selectIpCom" :search="ipSearch" @checkIp="checkIp"></select-ip>
        <el-dialog
          title="拉取"
          :visible.sync="showPull"
          width="40%"
          :show-close="true"
          :close-on-click-modal="false"
        >
            <el-form :inline="true" :model="pullForm" class="demo-form-inline" @keyup.enter.native="pullData(1)">
                <el-form-item
                    prop="adjDistNo"
                    label="Dist No"
                    class="is-required"
                >
                    <el-input v-model="pullForm.adjDistNo" placeholder="" ></el-input>
                </el-form-item>
                <el-form-item label="old dist No">
                    <el-input v-model="pullForm.adjOldDistNo" placeholder="" ></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer">
              <el-button type="primary" @click="pullData(1)">拉取</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
    import selectIp from '@/components/select-ip';
    export default {
        name: 'distribute-sd',
        data(){
            return {
                showPull:false,
                pullForm:{
                    adjDistNo:'',
                    adjOldDistNo:'',
                },
                searchForm:{
                    adjDistNo:'',
                    oldDistNo:'',
                    adjType:'SD',
                    adjMethod:'',
                    distFlag:'',
                    workId:'',
                    ipName:'',
                    ipNameNo:'',
                    ipBaseNo:'',
                },
                tableData:[],
                total:0,
                isPull:false,
                loading:false,
                IpTableVisible:false,
                ipSearch: {},
                emptyText: '暫無數據',
                currentPage:1,
            }
        },
        components: {
            selectIp
        },
        methods:{
            clearSearch(){
                this.searchForm = {
                    adjDistNo:'',
                    oldDistNo:'',
                    adjType:'SD',
                    adjMethod:'',
                    distFlag:'',
                    workId:'',
                    ipName:'',
                    ipNameNo:'',
                    ipBaseNo:'',
                }
                this.tableData = []
                this.total = 0
            },
            getIp(){
                this.IpTableVisible = true;
                this.ipSearch = {
                    name_no: '',
                    name: '',
                    soc: ''
                }
                this.$nextTick( () => {
                    this.$refs.selectIpCom.init();
                })
            },
            checkIp(info){
                this.$set(this.searchForm, 'ipNameNo', info.ip_name_no);
                this.$set(this.searchForm, 'ipName', info.name);
                this.$set(this.searchForm, 'ipBaseNo', info.ip_base_no);
                this.IpTableVisible = false;
            },
            handleCurrentChange(page){
                if(this.isPull){
                    this.pullData(page)
                }else{
                    this.querySearch(page)
                }
            },
            querySearch(pageNum){
                if(!this.checkDistNo()){
                    return false
                }
                this.loading = true
                this.isPull = false
                let params = this.searchForm
                params.page_num = pageNum?pageNum:1
                this.emptyText = '數據加載中';
                this.$http.get('/dist/adj/listDistAdjSdsrHeaderWithPage',{params}).then(res => {
                    console.log(res)
                    if(res.success){
                        if(res.data.data){
                            this.tableData = res.data.data.list;
                            this.total = res.data.data.total;
                            this.currentPage = params.page_num;
                        }else{
                            this.tableData=[]
                            this.total=1
                            this.currentPage=1
                        }
                        if(! this.tableData || this.tableData.length == 0){
                            this.emptyText = '暫無數據';
                        }
                    }
                    this.loading = false
                }).catch(res => {
                    this.loading = false
                })
            },
            pullData(pageNum){
                if(!this.pullForm.adjDistNo){
                    this.$message({
                        message:'distNo不能為空',
                        type:'warning'
                    })
                    return
                }
                this.loading = true
                this.isPull = true
                let params = this.pullForm
                params.page_num = pageNum?pageNum:1
                params.page_size = 10
                this.$http.get('/dist/adj/sd/pullDistAdjSdsrHeaderFromDistAdjSdAccount',{params}).then(res => {
                    console.log(res.data.code)
                    if(res.success){
                        if(res.data.code==200){
                            this.showPull=false
                            this.tableData = res.data.data.list
                            this.total = res.data.data.total
                            this.currentPage = params.page_num 
                            this.pullForm = {
                                adjDistNo:'',
                                adjOldDistNo:'',
                            }
                        }else{
                            this.$toast({ tips: res.data.message });
                            this.tableData = []
                            this.total = 0
                            this.currentPage = 1
                        }
                    }
                    this.loading = false
                }).catch(res => {
                    this.loading = false
                })
            },
            checkDistNo(){
                let no = this.searchForm.adjDistNo
                if(no){
                    return true;
                }else{
                    this.$message({
                        message:'distNo不能為空',
                        type:'warning'
                    })
                    return  false
                }
            },
            edit(row){
                let data = {
                    rightType:row.rightType,
                    workUniqueKey:row.workUniqueKey,
                    id:row.id,
                    distNo:row.adjDistNo
                }
                this.$router.push({name:'distribute-sd-detail',query:{data:JSON.stringify(data)}})
            }
        }
    }
</script>

<style scoped>
    /deep/ .el-form-item__label{
        margin-left: 0;
    }
</style>
