<template>
  <div>
    <div class="add-box">
      <h3 class="title">編輯內容</h3>
      <el-form :model="searchForm" label-width="120px" ref="searchForm" class="demo-form-inline searchForm">
        <el-form-item prop="adjDistNo" label="Dist No" :rules="[
                  { required: true, message: '請輸入Dist No', trigger: 'blur' }
                ]">
          <el-input v-model="searchForm.adjDistNo" placeholder="新的分配編號"></el-input>
        </el-form-item>
        <el-form-item label="old dist No">
          <el-autocomplete v-model="distNo" :fetch-suggestions="getCategoryCode" placeholder="舊的分配編號" @select="handleSelectOldDist" :trigger-on-focus="false">
            <template slot-scope="scope">
              {{scope.item}}
            </template>
          </el-autocomplete>
        </el-form-item>
        <el-form-item label="categoryCode">
          <el-input v-model="searchForm.categoryCode" placeholder="" readonly></el-input>
        </el-form-item>
        <el-form-item label="work No">
          <el-input v-model="searchForm.workId" placeholder="請輸入" @blur="checkWorkId('old')"></el-input>
        </el-form-item>
        <el-form-item label="work soc">
          <el-input v-model="searchForm.workSocietyCode" placeholder="" readonly></el-input>
        </el-form-item>
        <el-form-item label="title">
          <el-input v-model="searchForm.title" placeholder="" readonly></el-input>
        </el-form-item>
        <el-form-item label="dist amount">
          <el-input v-model="distAmount" placeholder="" readonly></el-input>
        </el-form-item>
        <el-form-item label="adj work No">
          <el-input v-model="searchForm.adjWorkId" placeholder="請輸入" @blur="checkWorkId('adj')"></el-input>
        </el-form-item>
        <el-form-item label="adj work soc">
          <el-input v-model="searchForm.adjWorkSocietyCode" placeholder="" readonly></el-input>
        </el-form-item>
        <el-form-item label="adj title">
          <el-input v-model="searchForm.adjTitle" placeholder="" readonly></el-input>
        </el-form-item>
        <el-form-item>
          <el-button @click="addIpShare" type="primary">新增</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="table-box">
      <h3 class="title">內容列表</h3>
      <el-table :data="tableData" border stripe v-loading="loading" style="width: 100%">
        <el-table-column prop="rightType" label="Right">
        </el-table-column>
        <el-table-column prop="groupIndicator" label="Gp">
        </el-table-column>
        <el-table-column prop="ipName" label="IP Name">
        </el-table-column>
        <el-table-column prop="ipNameNo" label="IP Name No">
        </el-table-column>
        <el-table-column prop="workIpRole" label="role">
        </el-table-column>
        <el-table-column prop="workIpSociety" label="soc">
        </el-table-column>
        <el-table-column prop="ipShare" label="Old IP Share">
          <template slot-scope="scope">
            <el-input v-model="scope.row.ipShare"></el-input>
          </template>
        </el-table-column>
        <el-table-column prop="adjIpShare" label="Now IP Share">
          <template slot-scope="scope">
            <el-input v-model="scope.row.adjIpShare"></el-input>
          </template>
        </el-table-column>
        <el-table-column prop="distAmount" label="dist roy">
          <template slot-scope="scope">
            <el-input v-model="scope.row.distAmount"></el-input>
          </template>
        </el-table-column>
        <el-table-column prop="adjAmount" label="Adj amount">
          <template slot-scope="scope">
            <el-input v-model="scope.row.adjAmount"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="operation" width="120px">
          <template slot-scope="scope">
            <div class="option">
              <el-button @click="deleteItem(scope.$index)" type="text" size="small">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="save-box">
        <el-button @click="save" type="primary">保存</el-button>
      </div>
    </div>
    <el-dialog title="IpShare新增" :visible.sync="showLog" width="60%" :close-on-click-modal="false">
      <el-table :data="ipshareList" border stripe style="width: 100%">
        <el-table-column prop="rightType" label="Right">
        </el-table-column>
        <el-table-column prop="groupIndicator" label="Gp">
        </el-table-column>
        <el-table-column prop="name" label="name">
        </el-table-column>
        <el-table-column prop="chineseName" label="chineseName">
        </el-table-column>
        <el-table-column prop="ipNameNo" label="IP Name No">
        </el-table-column>
        <el-table-column prop="workIpRole" label="role">
        </el-table-column>
        <el-table-column prop="workSocietyCode" label="soc">
        </el-table-column>
        <el-table-column prop="ipShare" label="Old IP Share">
        </el-table-column>
        <el-table-column label="operation">
          <template slot-scope="scope">
            <i class="el-icon-check" @click="add(scope.row)" style="cursor:pointer;"></i>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <el-dialog :visible.sync="showWorkList" width="1000px" :close-on-click-modal="false">
      <el-table :empty-text="tableresult" stripe :data="tableData1">
        <el-table-column property="title" label="title">
          <template slot-scope="scope">
            {{scope.row.title||scope.row.title_en}}
          </template>
        </el-table-column>
        <el-table-column property="work_id" label="workId"></el-table-column>
        <el-table-column property="work_society_code" label="workSocietyCode"></el-table-column>
        <el-table-column label="operation" width="100">
          <template slot-scope="scope">
            <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
              <i class="el-icon-check" @click="checkedWork(scope.row)"></i>
            </span>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background layout="prev, pager, next" :total="workTotal" @current-change="switchWorkPage">
      </el-pagination>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'ipshare-sr',
  data() {
    return {
      searchForm: {},
      tableData: [],
      tableData1: [],
      loading: false,
      showLog: false,
      showWorkList: false,
      workTotal: 0,
      ipshareList: [],
      distNo: '',
      workType: '',
      tableresult: ' '
    }
  },
  mounted() {

  },
  watch:{
    distNo:function(val){
      if(!val){
        this.searchForm.categoryCode = ''
      }
    },
    'searchForm.workId':function(val){
      if(!val){
        this.$set(this.searchForm,'workSocietyCode','')
        this.$set(this.searchForm,'title','')
      }
    },
    'searchForm.adjWorkId':function(val){
      if(!val){
        this.$set(this.searchForm,'adjWorkSocietyCode','')
        this.$set(this.searchForm,'adjTitle','')
      }
    }
  },
  computed: {
    distAmount() {
      let count = 0
      this.tableData.map(item => {
        count += Number(item.adjAmount ? item.adjAmount : 0)
      })
      return count
    }
  },
  methods: {
    getUniqueKey() {
      let categoryCode = String(this.searchForm.adjWorkSocietyCode)
      let workId = String(this.searchForm.adjWorkId)
      let str = categoryCode
      if (categoryCode.length < 3) {
        for (let i = 0; i < (3 - categoryCode.length); i++) {
          str = '0' + str
        }
      }
      return str + '-' + workId
    },
    getWorkInfo(page) {
      let data = {
        page: { pageNum: page, pageSize: 10 },
        workId: this.searchForm.workId
      };
      if (this.workType !== 'old') {
        data.workId = this.searchForm.adjWorkId
      }
      this.tableresult = '數據加載中...'
      this.$http.post('/wrk/queryWrkWorkListEs', data).then(res => {
        this.tableData1 = res.data.list
        this.workTotal = res.data.total
        this.showWorkList = true
        if(!this.tableData1.length){
          if(this.workType == 'old'){
            this.$set(this.searchForm,'workSocietyCode','')
            this.$set(this.searchForm,'title','')
          }else{
            this.$set(this.searchForm,'adjWorkSocietyCode','')
            this.$set(this.searchForm,'adjTitle','')
          }
        }
        this.tableresult = '暫無數據'
      })
    },
    switchWorkPage(page) {
      this.getWorkInfo((page))
    },
    checkedWork(row) {
      if (this.workType === 'old') {
        this.searchForm.workSocietyCode = row.work_society_code
        this.searchForm.title = row.title || row.title_en
        this.searchForm.titleId = row.id
        this.searchForm.workId = row.work_id
        this.searchForm.workUniqueKey = this.getUniqueKey(row.work_id)
      } else {
        this.searchForm.adjWorkSocietyCode = row.work_society_code
        this.searchForm.adjTitle = row.title || row.title_en
        this.searchForm.adjTitleId = row.id
        this.searchForm.adjWorkId = row.work_id
        this.searchForm.adjWorkUniqueKey = this.getUniqueKey(row.work_id)
      }
      this.showWorkList = false
    },
    checkWorkId(type) {
      if (!this.searchForm.oldDistNo) {
        this.$message({
          message: '請先填寫dist no',
          type: 'warning'
        })
        return
      }
      if (!this.searchForm.categoryCode) {
        this.$message({
          message: '請先填寫categoryCode',
          type: 'warning'
        })
        return
      }
      this.workType = type;
      let params = {};
      if (type === 'old') {
        params = {
          oldDistNo: this.distNo,
          workId: this.searchForm.workId
        }
      } else {
        params = {
          oldDistNo: this.distNo,
          workId: this.searchForm.adjWorkId
        }
      }
      if (params.workId) {
        this.$http.get('/dist/adj/sr/checkWorkFromDistCalcWorkIpShare', { params }).then(res => {
          if (res.success) {
            if (!res.data.data) {
              this.getWorkInfo(1)
            } else {
              this.$message({
                message: '此作品無效',
                type: 'warning'
              })
            }
          }
        })
      }
    },
    getCategoryCode(query, cb) {
      if (!query) {
        return
      }
      this.searchForm.oldDistNo = query
      this.$http.get('/dist/adj/getCategoryCodeList', { params: { distNo: query } }).then(res => {
        if (res.success) {
          cb(res.data.data)
        }
      })
    },
    handleSelectOldDist(val) {
      this.distNo = this.searchForm.oldDistNo
      this.searchForm.categoryCode = val
    },
    addIpShare() {
      this.$refs.searchForm.validate(validate => {
        if (validate) {
          this.showLog = true
          let workUniqueKey = this.getUniqueKey()
          if (!workUniqueKey) {
            return false
          }
          let params = {
            rightType: 'PER',
            workUniqueKey: workUniqueKey,
            adjDistNo: this.searchForm.adjDistNo
          }
          this.$http.get('/dist/adj/getIpshareList', { params }).then(res => {
            if (res.success) {
              let list = res.data.data
              if (list) {
                list.map(item => {
                  item.workIpSociety = item.ipSocietyCode
                  item.adjHeaderId = this.config.id
                  item.distNo = this.config.distNo
                  item.adjIpShare = ''
                })
                this.ipshareList = list
              }
            }
          })
        }
      })

    },
    add(row) {
      let canPush = true
      if (this.tableData.length) {
        this.tableData.map(item => {
          if (item.ipNameNo === row.ipNameNo && item.rightType === row.rightType) {
            canPush = false
            this.$message({
              message: '已經添加過了',
              type: 'warning'
            })
          }

        })
      }
      if (canPush) {
        this.tableData.push(row)
        this.showLog = false
      }
    },
    deleteItem(index) {
      this.$alert('確定删除吗', 'ipshare删除', {
        confirmButtonText: '確定',
        showCancelButton: true,
        callback: action => {
          if (action === 'confirm') {
            this.tableData.splice(index, 1);
            this.$message({
              message: '删除成功',
              type: 'success'
            })
          }
        }
      });

    },
    save() {
      let canSave = true
      this.tableData.length && this.tableData.map(item => {
        if (item.adjIpShare === '') {
          this.$message({
            message: 'new ip share不能為空',
            type: 'warning'
          })
          canSave = false
        }
        if (item.adjIpShare === '') {
          this.$message({
            message: 'old ip share不能為空',
            type: 'warning'
          })
          canSave = false
        }
      })
      if (canSave) {
        this.searchForm.oldDistNo = this.distNo
        let params = {
          distAdjSdsrHeader: this.searchForm,
          distAdjSdsrDetailsList: this.tableData
        }
        this.$http.post('/dist/adj/sr/saveSRData', params).then(res => {
          if (res.success) {
            if (res.data.code === 1 || res.data.code === 200) {
              this.$message({
                message: '保存成功',
                type: 'success'
              })
            } else {
              this.$message({
                message: res.data.message,
                type: 'warning'
              })
            }
          }
        })
      }
    }
  }
}
</script>

<style scoped lang="scss">
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .left {
    span {
      margin-right: 50px;
    }
  }
}
.save-box {
  padding: 20px 0;
  text-align: right;
}
.add-box {
  width: 30%;
  float: left;
  background: #eee;
  padding: 10px;
}
.table-box {
  width: 65%;
  float: right;
}
/deep/ .el-form-item__label {
  margin-left: 0;
}
</style>
