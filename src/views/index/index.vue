<template>
    <div class="index">
        <el-row :gutter="20">
            <el-col :offset="6" :span="12">
                <el-row>
                    <el-col>
                        <el-card shadow="hover" class="mgb20">
                            <h1>MoneySystem</h1>
                            <!-- <h1>歡迎使用詞曲版權分配管理系統</h1> -->
                            <div class="user-info">
                                <img src="static/img/img.jpg" class="user-avator" alt="">
                                <div class="user-info-cont">
                                    <div class="user-info-name">{{name}}</div>
                                    <div>{{role}}</div>
                                </div>
                            </div>
                            <div class="user-info-list">上次登錄時間：<span>2018-01-01</span></div>
                             
                        </el-card>
                    </el-col>
                </el-row>
                
            </el-col>
        </el-row>
    </div>
</template>

<script>
    export default {
        name: 'dashboard',
        data() {
            return {
                name: localStorage.getItem('ms_username'),
            }
        },
        computed: {
            role() {
                //return this.name === 'admin' ? '超级管理員' : '普通用户';
                return '管理員';
            }
        }
    }

</script>


<style scoped>
    h1{
        text-align: center;
        margin: 14px 0 24px 0;
    }
    .el-row {
        margin-bottom: 20px;
    }

    .user-info {
        display: flex;
        align-items: center;
        padding-bottom: 20px;
        border-bottom: 2px solid #ccc;
        margin-bottom: 20px;
    }

    .user-avator {
        width: 120px;
        height: 120px;
        border-radius: 50%;
    }

    .user-info-cont {
        padding-left: 50px;
        flex: 1;
        font-size: 14px;
        color: #999;
    }

    .user-info-cont div:first-child {
        font-size: 30px;
        color: #222;
    }

    .user-info-list {
        font-size: 14px;
        color: #999;
        line-height: 25px;
    }

    .user-info-list span {
        margin-left: 70px;
    }

    .mgb20 {
        margin-bottom: 20px;
    }
</style>
