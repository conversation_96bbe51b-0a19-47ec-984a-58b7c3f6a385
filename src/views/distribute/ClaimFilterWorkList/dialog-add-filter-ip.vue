<template>
  <div class="dialog-add-filter-work">
    <el-form
      label-position="right"
      ref="form"
      label-width="140px"
      :inline="true"
      :rules="updataRules"
      :model="createReportData"
      class="demo-form-inline"
      @keyup.enter.native="createReport">
      <el-form-item label="workId" prop="workId">
        <el-input v-model="createReportData.workId" placeholder="workId" :readonly="update"></el-input>
      </el-form-item>
      <el-form-item label="workSoc" prop="workSoc">
        <el-input v-model="createReportData.workSoc" placeholder="workSoc" :readonly="update"></el-input>
      </el-form-item>
      <el-form-item label="IP BASE NO" prop="ipBaseNo">
        <el-input v-model="createReportData.ipBaseNo" placeholder="ipBaseNo" :readonly="update"></el-input>
      </el-form-item>
      <el-form-item label="IP Name" prop="name">
        <el-input v-model="createReportData.name" placeholder="name"></el-input>
      </el-form-item>
      <el-form-item label="RightType">
<!--        <el-checkbox v-model="createReportData.per" >PER</el-checkbox>-->
        <el-checkbox v-model="createReportData.mec" :disabled="update || create">MEC</el-checkbox>
      </el-form-item>
      <el-form-item label="extInfo">
        <el-input type="textarea" v-model="createReportData.extInfo" placeholder="extInfo"></el-input>
      </el-form-item>
    </el-form>
    <div style="width:100%;display:flex;align-item:center">
      <el-button type="primary" style="margin: 0 auto" @click="createReport">保存</el-button>
    </div>
  </div>
</template>
<script>
// 添加作品弹窗内容部分

export default {
  name: 'dialog-add-filter-work',
  props: ['data'],
  data() {
    return {
      updataRules: {
        workSoc: [{ required: true, message: "", trigger: "blur" }],
        workId: [{ required: true, message: "", trigger: "blur" }],
        ipBaseNo: [{ required: true, message: "", trigger: "blur" }],
      },
      createReportData: {
        amendTime: '',
        createTime: '',
        extInfo: '',
        id: '',
        workId: '',
        workSoc: '',
        workUniqueKey: '',
        per: false,
        mec: true,
        company: '',
        ipBaseNo: '',
        name: ''
      },
      update: false,
      create: false
    }
  },
    mounted(){
      console.log(this.createReportData)
    },
  methods: {
    createReport() {
      console.log('createReport:createReportData:', this.createReportData)
      if(!this.createReportData.per && !this.createReportData.mec){
          this.$toast({ tips: 'Right Type請至少選擇一個!' })
          return
      }
      this.$refs.form.validate(validate => {
        if (validate) {
          this.$http.post('/claimFilterIp/addClaimFilterOtherIp', this.createReportData).then(res => {
            console.log(res)
            if (res.data.code == 200) {
              this.showReport = false
              this.$toast({ tips: this.update ? '修改成功!' : '新增成功!' })
              this.close()
              this.callback()
            } else {
              this.$toast({ tips: res.data.message })
            }
            // if (!res.data) {
            //   return this.$http.post('/claim/ccid/header', this.createReportData)
            // } else {
            //   this.$alert('報告已經生成，是否重新生成', '溫馨提示', {
            //     confirmButtonText: '確定',
            //     showCancelButton: true,
            //     callback: action => {
            //       console.log(action)
            //       if (action === 'confirm') {
            //         return this.$http.post('/claim/ccid/header', this.createReportData)
            //       }
            //     }
            //   })
            // }
          }).then((res) => {
            console.log(res)

          })
        }
      })
    }
  }
}

</script>
