
<template>
    <div class="work-transfer">
        <el-dialog :visible.sync="showt" :width="'1140px'" center :title="'Work Transfer: '+ transferInfo.baseInfo.workId" :close-on-click-modal="false">
            <div>
                <label>Right:</label>
                <el-select v-model="right" placeholder="Type">
                    <el-option label="PERFORMING RIGHT" value="PER"></el-option>
                    <el-option label="MECHANICAL RIGHT" value="MEC"></el-option>
                    <el-option label="SYNCHRONIZATION RIGHT" value="ZYN"></el-option>
                    <el-option label="OD RIGHT" value="NOD"></el-option>
                    <el-option label="DB RIGHT" value="NDB"></el-option>
                </el-select>
            </div>
            <el-row>
                <div class="title-logo"></div><h3>Transfer From</h3>
                <el-col :span="24" class="from">
                    <el-row>
                        <el-col :span="6">
                            <label style="display: inlne-block;">Title: </label>
                            <span :title="transferInfo.baseInfo.title" class="el-sl" style="width: 180px;display: inline-block;height: 20px;">{{transferInfo.baseInfo.title}}</span>
                        </el-col>
                        <el-col :span="6">
                            <label>Work No: </label>
                            <span>{{transferInfo.baseInfo.workId}}</span>
                        </el-col>
                        <el-col :span="6">
                            <label>Soc: </label>
                            <span>{{transferInfo.baseInfo.workSocietyCode}}</span>
                        </el-col>
                        <el-col :span="6">
                            <label>workType: </label>
                            <span>{{transferInfo.baseInfo.workType}}</span>
                        </el-col>
                        <el-col :span="6">
                            <label>ISWC: </label>
                            <span>{{transferInfo.baseInfo.iSWC}}</span>
                        </el-col>
                        <el-col :span="6">
                            <label>Genre: </label>
                            <span>{{transferInfo.baseInfo.genre}}</span>
                        </el-col>
                        <el-col :span="6">
                            <label>P.Lang: </label>
                            <span>{{transferInfo.baseInfo.performLanguage}}</span>
                        </el-col>
                        <!-- <el-col :span="6">
                            <label>T.L.: </label>
                            <span>{{transferInfo.baseInfo.titleLanguage}}</span>
                        </el-col>                         -->
                    </el-row>
                    <el-row>
                        <el-table :empty-text="tableresult"   stripe :data="transferInfo.info ?  transferInfo.info[right] : []" border :cell-class-name="cellClass">
                            <el-table-column prop="name" label="Name" min-width="360"></el-table-column>
                            <el-table-column prop="chineseName" label="Chinese Name" width="170"></el-table-column>
                            <el-table-column prop="workIpRole" label="Role" width="120"></el-table-column>
                            <el-table-column prop="ipNameNo" label="IP Name No" width="180">
                            </el-table-column>
                            <el-table-column prop="ipSocietyCode" label="Soc" width="100" class="select">
                            </el-table-column>
                            <el-table-column prop="ipShare" label="IP Share" width="140">
                            </el-table-column>
                        </el-table>
                    </el-row>
                </el-col>
            </el-row>
            <!-- <div class="line"></div> -->
            <el-row>
                <div class="title-logo"></div>
                <h3>Transfer To</h3>
                <el-col :span="24" class="from">
                    <el-row>
                        <el-col :span="6">
                            <label>Title: </label>
                            <el-input type="text" v-model="search.title" placeholder="雙擊查詢" readonly @dblclick.native="getWork()"></el-input>
                        </el-col>
                        <el-col :span="6">
                            <label style="width: 80px;">Work No: </label>
                            <el-input type="text" v-model="search.workId" placeholder="雙擊查詢" readonly @dblclick.native="getWork()"></el-input>
                        </el-col>
                        <el-col :span="6">
                            <label style="width: 80px;">Soc: </label>
                            <el-input type="text" v-model="search.workSocietyCode" placeholder="雙擊查詢" readonly @dblclick.native="getWork()"></el-input>
                        </el-col>
                        <el-col :span="6">
                            <label style="width: 90px;">workType: </label>
                            <el-input type="text" v-model="search.workType" readonly></el-input>
                        </el-col>
                    </el-row>
                    <el-row class="second">
                        <el-col :span="6">
                            <label style="width: 84px;">ISWC: </label>
                            <el-input type="text" v-model="search.iswc" readonly></el-input>
                        </el-col>
                        <el-col :span="6">
                            <label style="width: 80px;">Genre: </label>
                            <el-input type="text" v-model="search.genre" readonly></el-input>
                        </el-col>
                        <el-col :span="6">
                            <label style="width: 80px;">P.Lang: </label>
                            <el-input type="text" v-model="search.performLanguage" readonly></el-input>
                        </el-col>
                        <!-- <el-col :span="6">
                            <label style="width: 90px;">T.L.: </label>
                            <el-input type="text" v-model="search.titleLanguage" readonly></el-input>
                        </el-col>   -->
                        <!-- <el-col :span="3">
                            <el-button type="primary" @click="searchFn(1)">查詢</el-button>
                        </el-col>                       -->
                    </el-row>
                    <el-row>
                        <el-table :empty-text="tableresult"   stripe :data="toResult[right]" border :cell-class-name="cellClass">
                            <el-table-column prop="name" label="Name" min-width="360"></el-table-column>
                            <el-table-column prop="chinese_name" label="Chinese Name" width="170"></el-table-column>
                            <el-table-column prop="workIpRole" label="Role" width="120"></el-table-column>
                            <el-table-column prop="ipNameNo" label="IP Name No" width="180">
                            </el-table-column>
                            <el-table-column prop="ipSocietyCode" label="Soc" width="100" class="select">
                            </el-table-column>
                            <el-table-column prop="ipShare" label="IP Share" width="140">
                            </el-table-column>
                        </el-table>
                    </el-row>
                </el-col>
            </el-row>
            <span slot="footer" class="dialog-footer">
                <el-button @click="showt = false">取 消</el-button>
                <el-button type="primary" @click="confirm()">確 定</el-button>
            </span>
        </el-dialog>
        <select-work v-if="workTableVisible" ref="selectWorkCom" :search="workSearch" @checkWork="checkWork"></select-work>
        <!-- <work-result :show="showResult" :result-data="resultData" @selectWork="selectWork"></work-result> -->
    </div>
</template>
<script>
import qs from 'qs'
import selectWork from '../../../components/select-work'
export default {
    props: {
        transferInfo: {
            type: Object,
            default: {
                baseInfo: {}
            }
        },
        showTransfer: {
            type: Boolean,
            default: false
        }
    },
    data(){
        return {
            tableresult:' ',
            // ipiInfo: JSON.parse(JSON.stringify(this.ipInfo)),
            showt: this.showTransfer,
            search: {
                id: '', //选取的作品的title id
                title: '',
                workId: '',
                workSocietyCode: '',
                workType: '',
                iswc: '',
                genre: '',
                performLanguage: '',
                titleLanguage: ''
            },
            tableData: [],
            resultData: [],
            toResult: [],
            showResult: false,
            right: 'PER',
            // 查詢作品
            editIndex: 0,
            workTableVisible: false,
            workSearch: {},
        }
    },
    components: {
        selectWork
    },
    watch: {
        showt(){
            this.$emit('change', {'name': 'showTransfer', 'value': this.showt});
        },
        showTransfer(){
            this.showt = this.showTransfer
        }
    },
    methods: {
        cellClass({row, column, rowIndex, columnIndex}){
            if(columnIndex == 4){
                return 'select';
            }
        },
        updateShow(obj){
            this[obj.name] = obj.value
        },
        // searchFn(num){
        //     this.loading = true;
        //     this.search.page = {
        //         pageNum: num ? num : 1,
        //         pageSize: 10
        //     }
        //     this.$http.post('/wrk/queryWrkWorkListEs', this.search).then(res => {
        //         if (res.status === 200) {
        //             this.resultData = res.data.list
        //             this.total = res.data.total
        //         //    distFlag 1後台返回1代表可匹配    後台返回0代表不可匹配
        //             this.showResult = true;
        //             // this.resultData = res.data.list
        //             // this.total = parseInt(res.data.total)
        //             this.loading = false
        //         }
        //     })
        // },
        selectWork(info){
            this.showResult = false;
            let params = {
                workId: info.work_id,
                workSocietyCode: info.work_society_code
            }
            this.$http.get('/wrk/getWrkWorkById', {params}).then(res => {
                if (res.status === 200) {
                    this.toResult = res.data.wwisList[0]
                    // this.search = res.data.wrkWork;

                    this.search.title = res.data.wrkWork.title;
                    this.search.id = res.data.wrkWork.id;
                    this.search.workId = res.data.wrkWork.workId;
                    this.search.workSocietyCode = res.data.wrkWork.workSocietyCode;
                    this.search.workType = res.data.wrkWork.workType;
                    this.search.iswc = res.data.wrkWork.iswc;
                    this.search.genre = res.data.wrkWork.genre;
                    this.search.performLanguage = res.data.wrkWork.performLanguage;
                    this.search.titleLanguage = res.data.wrkWork.titleLanguage
                }
            })
        },
        /**
         * 选取作品
         */
        getWork(index, row) {
            this.editIndex = index;
            this.workTableVisible= true;
            this.workSearch = {
                workId: this.search.workId,
                soc: this.search.workSocietyCode,
                title: this.search.title
            }
            this.$nextTick( () => {
                this.$refs.selectWorkCom.init();
            })
        },
        checkWork(info){
            console.log('info: ', info);
            this.selectWork(info);
            // this.$set(this.search, 'title', info.title ? info.title : info.title_en);
            // this.$set(this.search, 'workId', info.work_id);
            // this.$set(this.search, 'workSocietyCode', info.work_society_code);
            // this.$set(this.search, 'workType', info.work_type);
            // this.$set(this.search, 'iswc', info.iswc);
            // this.$set(this.search, 'genre', info.genre_code);
            // this.$set(this.search, 'performLanguage', info.language_code);
            // this.$set(this.search, 'titleLanguage', info.language_code);
        },
        confirm(){
            if(!this.search.id){
                this.$toast({tips: '請選擇Transfer To作品'})
                return;
            }
            let ajaxData = {
                destTitleId: this.search.id,
                workDestNo: this.search.workId,
                workDestNoSociety: this.search.workSocietyCode,
                sourceTitleId: this.transferInfo.baseInfo.id,
                workSouceNo: this.transferInfo.baseInfo.workId,
                workSourceNoSociety: this.transferInfo.baseInfo.workSocietyCode,
            }
            this.$http.post('/wrk/transfer/saveWrkWorkTransfer', ajaxData).then(res => {
                if(res.status == 200){
                    this.$toast({tips: '提交成功'})
                    this.showt = false;
                }
            })
        }
    }
}
</script>
<style lang="scss" scoped>
    /deep/ .el-dialog{
        padding-bottom: 20px;
        margin-top: 3vh !important;
    }
    /deep/ .from{
        .el-input{
            width: 60%;
            margin-left: 8px;
        }
        label{
            display: inline-block;
            width: 84px;
            text-align: right;
            padding-right: 4px;
        }

        .second label{
            width: 60px;
        }
        .select{
            padding: 0 !important;
            .cell{
                height: 100%;
                padding: 0 !important;
                .el-input{
                    margin-left: 0;
                    width: 100% !important;
                    input{
                        border: 0;
                    }
                }
            }
        }
    }
    /deep/ .to .el-input{
        width: 54%;
        margin-left: 2px;
        input{
            padding-left: 6px;
        }
    }
    /deep/ .cell{
        padding: 0 6px !important;
        line-height: 18px;
    }
    .el-row{
        margin-bottom: 10px;
    }
    .title-logo{
        float: left;
        width: 6px;
        height: 24px;
        background: #17B3A3;
        margin-top: 16px;
    }
    h3{
        margin-left: 14px;
    }
    /deep/ .el-dialog__body{
        padding: 14px 25px;
    }
    /deep/ .el-dialog__header{
        font-weight: bold;
        text-align: left;
        // border-bottom: 1px solid #eee;
        height: 50px;
    }

    /deep/ .el-table__header tr th{
        background: #eee !important;
    }

</style>
