<template>
    <div id="app">
        <transition name="fade">
            <router-view v-if="isRouterAlive"></router-view>
        </transition>
        <!-- 使用 transition + keep-alive 合理嵌套 -->
<!--        <keep-alive>-->
<!--            &lt;!&ndash; 缓存符合条件的组件 &ndash;&gt;-->
<!--            <router-view v-if="$route.meta.keepAlive && isRouterAlive" />-->
<!--        </keep-alive>-->

        <!-- 不缓存的组件正常渲染 -->
<!--        <router-view v-if="!$route.meta.keepAlive && isRouterAlive" />-->
    </div>
</template>

<script>
  export default {
    name: 'App',
    provide () {
      return {
        reload: this.reload
      }
    },
    data () {
      return {
        isRouterAlive: true
      }
    },
    methods: {
      reload () {
        this.isRouterAlive = false
        this.$nextTick(function () {
          this.isRouterAlive = true
        })
      }
    }

  }
</script>
<style lang="scss">
  body {
    background-color: #f0f0f0;
  }
  .el-collapse-item__header {
    /* color: #909399!important; */
    color: #444 !important;
    font-size: 18px!important;
    font-weight: 600!important;
  }
  /* .el-checkbox{
    width: 185px;
  } */
  .el-form-item__label{
      color: #000 !important;
      margin-left: 20px;
  }
  /* .el-input--suffix{
    width: 185px!important;
  } */
  .saveright{
    width: calc(100% - 208px);
    position: fixed;
    text-align: right;
    z-index: 999;
    top: 87px;
    left: 210px;
    height: 50px;
  }
  .saveright2{
 width: 50%;
    position: fixed;
    text-align: right;
    z-index: 999;
    /* top: 87px; */
    /* left: 210px; */
    height: 50px;
  }
  .saveSmallright{
    width: calc(100% - 62px)!important;
    position: fixed;
    text-align: right;
    z-index: 999;
    top: 87px;
    left: 64px;
    height: 50px;
  }
  .contentbox{
    margin-top: 40px;
  }
  .saveright .el-button{
    margin-top: -30px!important;
  }
  .right{
    float: right;
  }
  .spanright{
    width: 46%;
    display: inline-block;
    text-align: center;
  }
  /* .boxline{
    border-bottom: 2px dotted #ebeef5;
  } */
  .mytitle{
    color: #909399;
    font-size: 14px;
    font-weight: 600;
  }
  .el-collapse-item__header{
    color: #909399;
    font-size: 18px;
    font-weight: 600;
  }
  .el-table__row td:nth-last-child(1) .el-button--text span{
      color: #409EFF;
  }
  .el-table__row td:nth-last-child(1) .el-button--text span:hover{
      color: #409EFF;
      text-decoration: underline;
  }

  .site-wrapper {
    .flexable-table {
      .el-table__header-wrapper {
        max-width: 100%;
        overflow-x: hidden;
        overflow-y: scroll;
      }
      .el-table__body-wrapper {
        min-height: 100px;
        max-height: 300px;
        overflow-y: scroll;
      }
    }
  }

  /* el-tabs */
  .el-tabs {
    .el-tabs__nav {
      display: flex;
    }
    &.el-tabs--card {
      .el-tabs__header {
        border-bottom-color: #17b3a3;
        .el-tabs__item {
          color: #666;
          &:active {
            // border: none;
          }
          &.is-active {
            color: #222;
            background-color: #72c0b8;
            border-bottom-color: transparent;
          }
        }
      }
    }
  }

  /* table 选中绿色高亮 */
  .el-table {
    /* 在 el-table 的 class 中添加 highlight-row-table，来使鼠标选择行高亮 */
    &.highlight-row-table {
      .el-table__body-wrapper {
        .el-table__body {
          tr.el-table__row {
            &.current-row {
              td {
                background: #17b3a3;
                color: #fff;
              }
            }
            td {
              transition: color 180ms linear;
            }
          }
        }
      }
    }
  }


  .input-text-right {
    .el-input__inner {
      text-align: right
    }
  }
</style>
