<template>
  <div>
    <el-tabs v-model="activeName">
      <el-tab-pane label="授權配置" name="first">
        <div class="step1">
          <el-form ref="itemdataForm" :rules="rulesdate" :inline="true" :model="itemData" class="demo-form-inline">
            <el-form-item label="Company" class="is-required"  prop="company">
              <el-input v-model.trim="itemData.company" placeholder></el-input>
            </el-form-item>
            <el-form-item label="Desc">
              <el-input v-model.trim="itemData.remark" placeholder></el-input>
            </el-form-item>
            <el-form-item label="status">
              <el-switch v-model="itemData.status" active-color="#13ce66" inactive-color="#e9e9e9" :active-value="1" :inactive-value="2"></el-switch>
            </el-form-item>
            <el-table :empty-text="tableresult" :data="tableData" border stripe style="width: 100%">
              <el-table-column prop="name" label="類別"></el-table-column>
              <el-table-column prop="date" label="授權期限">
                <template slot-scope="scope">
                  <!-- <div id="cover" style="width:155px;height:35px;position: absolute;z-index:100"></div> -->
                  <span v-if="scope.row.name=='重製'" class="requrestyle">
                    <el-form-item prop="mechanicalStartTime" label=" " class="is-required">

                      <el-input v-model="itemData.mechanicalStartTime" value-format="yyyy-MM-dd" format="yyyy-MM-dd" style="width: 200px" placeholder="startDate" maxlength="8"></el-input>
                    </el-form-item>
                  </span>
                  <span v-if="scope.row.name!='重製'" class="requrestyle">
                    <el-form-item prop="publicTransmissionStartTime" label=" "  class="is-required">

                      <el-input v-model="itemData.publicTransmissionStartTime" value-format="yyyy-MM-dd" format="yyyy-MM-dd" style="width: 200px" placeholder="startDate" maxlength="8"></el-input>
                    </el-form-item>
                  </span>
                  -
                  <span v-if="scope.row.name=='重製'" class="requrestyle">
                    <el-form-item prop="mechanicalEndTime" label=" "  class="is-required">
                      <el-input v-model="itemData.mechanicalEndTime" value-format="yyyy-MM-dd" format="yyyy-MM-dd" style="width: 200px" placeholder="endDate" maxlength="8"></el-input>
                    </el-form-item>
                  </span>
                  <span v-if="scope.row.name!='重製'" class="requrestyle">
                    <el-form-item prop="publicTransmissionEndTime" label=" "  class="is-required">
                      <el-input v-model="itemData.publicTransmissionEndTime" value-format="yyyy-MM-dd" format="yyyy-MM-dd" style="width: 200px" placeholder="endDate" maxlength="8"></el-input>
                    </el-form-item>
                  </span>
                  <!-- mechanicalEndTime==publicTransmissionEndTime -->
                  <!-- <div id="cover" style="width:155px;height:35px;position: absolute;z-index:100"></div> -->
                  <!-- <el-input v-model="scope.row.date[1]" value-format="yyyy-MM-dd" format="yyyy-MM-dd" style="width: 200px" placeholder="endDate"></el-input> -->
                  <!-- <date-picker v-model="scope.row.date[1]" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd" style="width: 200px" placeholder="endDate" maxlength="8"></date-picker> -->
                </template>
              </el-table-column>
            </el-table>
          </el-form>
          <el-button type="primary" @click="saveStep1">save</el-button>
        </div>
      </el-tab-pane>
      <el-tab-pane label="minima設置" name="second" :disabled="disabled" v-if="company.trim()=='youtube' || company.trim()=='google'">
        <div class="step1">
          <div class="clear title">
            <p>SVOD</p>
          </div>
          <el-form ref="step1Form" :inline="true" :model="SVOD" class="demo-form-inline" label-width="180px">
            <el-form-item label="GE PSM">
              <el-input v-model="SVOD.gePSM" placeholder @input="clearNoNum('SVOD','gePSM')"></el-input>
            </el-form-item>
            <el-form-item label="MUSIC PSM">
              <el-input v-model="SVOD.musicPSM" placeholder @input="clearNoNum('SVOD','musicPSM')"></el-input>
            </el-form-item>
            <el-form-item label="GEMusic PSM">
              <el-input v-model="SVOD.gemusicPSM" placeholder @input="clearNoNum('SVOD','gemusicPSM')"></el-input>
            </el-form-item>
            <el-form-item label="exchange">
              <el-input v-model="SVOD.exchange" placeholder @input="clearNoNum('SVOD','exchange')"></el-input>
            </el-form-item>
          </el-form>
          <el-form ref="step1Form" :inline="true" :model="SVOD" class="demo-form-inline" label-width="180px">
            <el-form-item label="GE Rate">
              <el-input v-model="SVOD.geRate" placeholder @input="clearNoNum('SVOD','geRate')"></el-input>
            </el-form-item>
            <el-form-item label="MUSIC Rate">
              <el-input v-model="SVOD.musicRate" placeholder @input="clearNoNum('SVOD','musicRate')"></el-input>
            </el-form-item>
            <el-form-item label="GEMusic Rate">
              <el-input v-model="SVOD.gemusicRate" placeholder @input="clearNoNum('SVOD','gemusicRate')"></el-input>
            </el-form-item>
          </el-form>
          <el-form ref="step1Form" :inline="true" :model="SVOD" class="demo-form-inline" label-width="180px">
            <el-form-item label="AUX GE Rate">
              <el-input v-model="SVOD.mechanicalShare" placeholder @input="clearNoNum('SVOD','mechanicalShare')"></el-input>
            </el-form-item>
            <el-form-item label="AUX music Rate">
              <el-input v-model="SVOD.publicShare" placeholder @input="clearNoNum('SVOD','publicShare')"></el-input>
            </el-form-item>
            <el-form-item label="AUX GEMusic Rate">
              <el-input v-model="SVOD.gemusicPublicShare" placeholder @input="clearNoNum('SVOD','gemusicPublicShare')"></el-input>
            </el-form-item>
          </el-form>
          <el-form ref="step1Form" :inline="true" :model="SVOD" class="demo-form-inline" label-width="180px">
            <el-form-item label="PER Split Share">
              <el-input v-model="SVOD.perSplitShare" placeholder @input="clearNoNum('SVOD','perSplitShare')"></el-input>
            </el-form-item>
            <el-form-item label="Mec Split Share">
              <el-input v-model="SVOD.mecSplitShare" placeholder @input="clearNoNum('SVOD','mecSplitShare')"></el-input>
            </el-form-item>
          </el-form>
          <div class="clear title">
            <p>PSM-based</p>
            <el-button style="float:right" type="primary" @click="addProduct('add')">新增產品</el-button>
          </div>
          <el-table :empty-text="tableresult"   :data="SVOD.pmsBasedList" border stripe style="width: 100%">
            <el-table-column prop="subscriberType" label="SubScriberType"></el-table-column>
            <el-table-column prop="multiplierPER" label="Multiplier(PER)"></el-table-column>
            <el-table-column prop="multiplierMEC" label="Multiplier(MEC)"></el-table-column>
            <el-table-column label="operation">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="addProduct('edit',scope.row,scope.$index)">編輯</el-button>
                <el-button type="text" size="small" @click="deleteProduct(scope.$index)">刪除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="clear title">
            <p>AVOD</p>
          </div>
          <el-form ref="step1Form" :inline="true" :model="AVOD" class="demo-form-inline" label-width="180px">
            <el-form-item label="PER Split Share">
              <el-input v-model="AVOD.perSplitShare" placeholder @input="clearNoNum('AVOD','perSplitShare')"></el-input>
            </el-form-item>
            <el-form-item label="Mec Split Share">
              <el-input v-model="AVOD.mecSplitShare" placeholder @input="clearNoNum('AVOD','mecSplitShare')"></el-input>
            </el-form-item>
          </el-form>
          <el-form ref="step1Form" :inline="true" :model="AVOD" class="demo-form-inline" label-width="180px">
            <el-form-item label="MUSIC PER Share">
              <el-input v-model="AVOD.publicShare" placeholder @input="clearNoNum('AVOD','publicShare')"></el-input>
            </el-form-item>
            <el-form-item label="MUSIC Mec Share">
              <el-input v-model="AVOD.mechanicalShare" placeholder @input="clearNoNum('AVOD','mechanicalShare')"></el-input>
            </el-form-item>
          </el-form>
          <el-form ref="step1Form" :inline="true" :model="AVOD" class="demo-form-inline" label-width="180px">
            <el-form-item label="GEMusic PER Share">
              <el-input v-model="AVOD.gemusicPublicShare" placeholder @input="clearNoNum('AVOD','gemusicPublicShare')"></el-input>
            </el-form-item>
            <el-form-item label="GEMusic MEC Share">
              <el-input v-model="AVOD.gemusicMechanicalShare" placeholder @input="clearNoNum('AVOD','gemusicMechanicalShare')"></el-input>
            </el-form-item>
          </el-form>
          <el-form ref="step1Form" :inline="true" :model="AVOD" class="demo-form-inline" label-width="180px">
            <el-form-item label="GE PER Share">
              <el-input v-model="AVOD.gePublicShare" placeholder @input="clearNoNum('AVOD','gePublicShare')"></el-input>
            </el-form-item>
            <el-form-item label="GE MEC Share">
              <el-input v-model="AVOD.geMechanicalShare" placeholder @input="clearNoNum('AVOD','geMechanicalShare')"></el-input>
            </el-form-item>
          </el-form>
          <div class="clear title">
            <p>PRE CLAIM</p>
          </div>
          <el-form ref="step1Form" :inline="true" :model="preClaim" class="demo-form-inline" label-width="180px">
            <el-form-item label="PER Share">
              <el-input v-model="preClaim.publicShare" placeholder @input="clearNoNum('preClaim','publicShare')"></el-input>
            </el-form-item>
            <el-form-item label="Mec Share">
              <el-input v-model="preClaim.mechanicalShare" placeholder @input="clearNoNum('preClaim','mechanicalShare')"></el-input>
            </el-form-item>
          </el-form>
          <div class="clear title">
            <p>Audio-Only</p>
          </div>
          <el-form ref="step1Form" :inline="true" :model="AUDIO" class="demo-form-inline" label-width="180px">
            <el-form-item label="AUX music Rate">
              <el-input v-model="AUDIO.publicShare" placeholder @input="clearNoNum('AUDIO','publicShare')"></el-input>
            </el-form-item>
            <el-form-item label="MUSIC PSM">
              <el-input v-model="AUDIO.musicPSM" placeholder @input="clearNoNum('AUDIO','musicPSM')"></el-input>
            </el-form-item>
            <el-form-item label="exchange">
              <el-input v-model="AUDIO.exchange" placeholder @input="clearNoNum('AUDIO','exchange')"></el-input>
            </el-form-item>
          </el-form>
          <el-form ref="step1Form" :inline="true" :model="AUDIO" class="demo-form-inline" label-width="180px">
            <el-form-item label="PER Split Share">
              <el-input v-model="AUDIO.perSplitShare" placeholder @input="clearNoNum('AUDIO','perSplitShare')"></el-input>
            </el-form-item>
            <el-form-item label="Mec Split Share">
              <el-input v-model="AUDIO.mecSplitShare" placeholder @input="clearNoNum('AUDIO','mecSplitShare')"></el-input>
            </el-form-item>
          </el-form>
          <el-button type="primary" @click="saveStep2">save</el-button>
        </div>
      </el-tab-pane>
      <el-tab-pane label="minima設置" name="second" :disabled="disabled" v-else>
        <div class="steps">
          <el-form :inline="true" :model="searchFormMini" class="demo-form-inline" @keyup.enter.native="getMiniList">
            <el-form-item label="product name" label-width="130px">
              <el-input v-model.trim="searchFormMini.productName" placeholder="輸入名稱"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="getMiniList()">搜索</el-button>
            </el-form-item>
            <el-form-item>
              <el-button type="success" @click="editMiniItem('add')">新增</el-button>
            </el-form-item>
          </el-form>
          <el-table :empty-text="tableresult" :data="tableData1" key="miniList" border stripe style="width: 100%">
            <el-table-column prop="id" label="id" width="50"></el-table-column>
            <el-table-column prop="productFullName" label="product_full_name" >
              <template slot-scope="scope">
                <span :title="scope.row.productFullName">{{scope.row.productFullName}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="productShortName" label="product_short_name" >
              <template slot-scope="scope">
                <span :title="scope.row.productShortName">{{scope.row.productShortName}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="formulaPublic" label="MINIMAS-公傳"></el-table-column>
            <el-table-column prop="formulaMechanical" label="MINIMAS-重製"></el-table-column>
            <el-table-column prop="formulaPublicMechanical" label="formula-公傳+重製"></el-table-column>
            <el-table-column prop="mechanicalShare" label="重製share">
              <template slot-scope="scope">
                {{scope.row.mechanicalShare}}%
              </template>
            </el-table-column>
            <el-table-column prop="publicShare" label="公傳share">
              <template slot-scope="scope">
                {{scope.row.publicShare}}%
              </template>
            </el-table-column>
            <el-table-column prop="publicShare" label="share總和">
              <template slot-scope="scope">
                {{scope.row.mechanicalShare+scope.row.publicShare}}%
              </template>
            </el-table-column>
            <el-table-column label="operation">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="editMiniItem('edit',scope.row)">編輯
                </el-button>
                <el-button type="text" size="small" @click="deleteMini(scope.row)">刪除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination background layout="prev, pager, next" :total="miniTotal" @current-change="handlePage"></el-pagination>
        </div>
      </el-tab-pane>
      <el-tab-pane label="重製委託會員設置" name="third" :disabled="disabled">
        <div>
          <el-form :inline="true" :model="searchFormMember" class="demo-form-inline" @keyup.enter.native="getMemberList">
            <el-form-item label="publisher代號">
              <el-input v-model.trim="searchFormMember.publisher" placeholder="publisher" style="width:140px"></el-input>
            </el-form-item>
            <el-form-item label="ipBaseNo">
              <el-input v-model.trim="searchFormMember.ipBaseNo" placeholder="ipBaseNo" style="width:120px"></el-input>
            </el-form-item>
            <el-form-item label="paNameNo">
              <el-input v-model.trim="searchFormMember.nameNo" placeholder="nameNo" style="width:120px"></el-input>
            </el-form-item>
            <el-form-item label="socNo">
              <el-input v-model.trim="searchFormMember.socNo" placeholder="socNo" style="width:70px"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="getMemberList">搜索</el-button>
            </el-form-item>
            <el-form-item>
              <el-button type="success" @click="addMember('新增')">新增</el-button>
            </el-form-item>
            <el-form-item>
              <span class="clear-search" @click="clearStep3()">清除搜索</span>
            </el-form-item>
          </el-form>
          <el-table :empty-text="tableresult" :data="tableData2" border stripe key="memberList" style="width: 100%">
            <el-table-column prop="publisher" label="PUBLISHER代號"></el-table-column>
            <el-table-column prop="paName" label="name"></el-table-column>
            <el-table-column prop="paNameNo" label="PA NAME NO"></el-table-column>
            <el-table-column prop="ipBaseNo" label="IP BASE NO"></el-table-column>
            <el-table-column prop="socNo" label="SOC NO"></el-table-column>
            <el-table-column label="operation">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="addMember('編輯',scope.row)">編輯</el-button>
                <el-button type="text" size="small" @click="deleteMember(scope.row)">刪除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination background layout="prev, pager, next" :total="agreementTotal" @current-change="handlePage1"></el-pagination>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!--minimas編輯弹框-->
    <el-dialog :title="productTitle" :visible.sync="productShow" width="40%" :show-close="false" @close="procloseDialog" :close-on-click-modal="false">
      <el-form 
        label-position="right" 
        label-width="190px" 
        :model="productTableItem" 
        :rules="productRules" 
        class="demo-form-inline" 
        ref="productForm"
        @keyup.enter.native="saveProduct('productForm')">
        <el-form-item label="SubScriberType:" prop="subscriberType">
          <el-input v-model="productTableItem.subscriberType" placeholder="請輸入"></el-input>
        </el-form-item>
        <el-form-item label="Multiplier(PER):" prop="multiplierPER">
          <el-input v-model="productTableItem.multiplierPER" placeholder="請輸入" @input="clearNoNum('productTableItem','multiplierPER')"></el-input>
        </el-form-item>
        <el-form-item label="Multiplier(MEC):" prop="multiplierMEC">
          <el-input v-model="productTableItem.multiplierMEC" @keydown.native="$inputNumber" placeholder="請輸入" @input="clearNoNum('productTableItem','multiplierMEC')"></el-input>
        </el-form-item>
      </el-form>
      <div style="text-align: center">
        <el-button @click="()=>{this.productShow = false}">取消</el-button>
        <el-button type="primary" @click="saveProduct('productForm')">確定</el-button>
      </div>
    </el-dialog>
    <el-dialog :title="miniTitle" :visible.sync="miniShow" width="45%" :show-close="false" @close="closeDialog" :close-on-click-modal="false">
      <el-form 
        label-position="right" 
       
        :model="miniTableItem" 
        :rules="minimaRules" 
        class="demo-form-inline" 
        ref="miniForm"
        :inline="true"
        @keyup.enter.native="saveMini('miniForm')">
        <el-form-item label="product_full_name:" prop="productFullName">
          <el-input v-model="miniTableItem.productFullName" placeholder="請輸入"></el-input>
        </el-form-item>
        <el-form-item label="product_short_name:" prop="productShortName">
          <el-input v-model="miniTableItem.productShortName" placeholder="請輸入"></el-input>
        </el-form-item>
      </el-form>
      <el-form :inline="true" :model="miniTableItem" class="demo-form-inline">
        <div style="margin-bottom:10px;font-weight: bold;">MINIMAS</div>
        <el-form-item label="PERF:" prop="formulaPublic">
          <el-input v-model="miniTableItem.formulaPublic" @keydown.native="$inputNumber" placeholder="請輸入"></el-input>
        </el-form-item>
        <el-form-item label="MEC:" prop="formulaMechanical">
          <el-input v-model="miniTableItem.formulaMechanical" @keydown.native="$inputNumber" placeholder="請輸入"></el-input>
        </el-form-item>

        <el-form-item label="formulaType:" prop="formulaType">
          <el-radio v-model="miniTableItem.formulaType" :label="1">Pre-Subscriber</el-radio>
          <el-radio v-model="miniTableItem.formulaType" :label="2">Per-Play</el-radio>
        </el-form-item>
      </el-form>
      <el-form :inline="true" :model="miniTableItem" class="demo-form-inline">
        <div style="margin-bottom:10px;font-weight: bold;">Share</div>
        <el-form-item label="PERF:" prop="publicShare">
          <el-input :disabled="disabled1" v-model="miniTableItem.publicShare" @keydown.native="$inputNumber" @input="updateView($event)" @focus="aaa()" @blur="checkNumSize($event,'publicShare')" placeholder="請輸入0~100之間的數字"></el-input>
        </el-form-item>
        <el-form-item label="MEC:" prop="mechanicalShare">
          <el-input :disabled="disabled1" v-model="miniTableItem.mechanicalShare" @keydown.native="$inputNumber" @input="updateView($event)" @focus="aaa()" @blur="checkNumSize($event,'mechanicalShare')" placeholder="請輸入0~100之間的數字"></el-input>
        </el-form-item>
      </el-form>
      <el-form :inline="true" :model="miniTableItem" class="demo-form-inline">
        <el-form-item label="Total Share:" prop="totalShare">
          <el-input :disabled="disabled2" v-model="miniTableItem.totalShare" @keydown.native="$inputNumber" @input="updateView($event)" @focus="aaa2()" @blur="aaa3()" placeholder="請輸入"></el-input>
        </el-form-item>
      </el-form>
      <el-form :inline="true" :model="miniTableItem" class="demo-form-inline">
        <el-form-item label="PERF Split Share:" prop="perSplitShare">
          <el-input :disabled="disabled2" v-model="miniTableItem.perSplitShare" @keydown.native="$inputNumber" @input="updateView($event)" @focus="aaa2()" @blur="aaa3()" placeholder="請輸入"></el-input>
        </el-form-item>
        <el-form-item label="MEC Split Share:" prop="mecSplitShare">
          <el-input :disabled="disabled2" v-model="miniTableItem.mecSplitShare" @keydown.native="$inputNumber" @input="updateView($event)" @focus="aaa2()" @blur="aaa3()" placeholder="請輸入"></el-input>
        </el-form-item>
      </el-form>
      <div style="text-align: center">
        <el-button @click="()=>{this.miniShow = false}">取消</el-button>
        <el-button type="primary" @click="saveMini('miniForm')">確定</el-button>
      </div>
    </el-dialog>

    <!-- 第三步弹框    -->
    <el-dialog :title="memberTitle" :visible.sync="memberShow" class="addResetMember" :show-close="false" :close-on-click-modal="false">
      <el-tabs v-model="memberActiveName">
        <el-tab-pane label="会员" name="first">
          <el-form ref="memberAddMember" label-position="right" label-width="150px" :model="memberTableItem" class="demo-form-inline" :rules="rules" @keyup.enter.native="saveMemberDialog">
            <el-form-item label="PUBLISHER代號:" prop="publisher">
              <el-input v-model="memberTableItem.publisher" placeholder="請輸入"></el-input>
            </el-form-item>
            <el-form-item label="name:" prop="paName">
              <el-autocomplete v-model="memberTableItem.paName" placeholder="請輸入" :fetch-suggestions="getItemData" style="width:100%" @select="setItemData" :trigger-on-focus="false">
                <template slot-scope="scope">
                  <p style="margin:5px 0;">name：{{scope.item.name}} | {{scope.item.chineseName}}</p>
                  <p style="margin:5px 0;">PA NAME NO：{{scope.item.nameNo}}</p>
                  <p style="margin:5px 0;">IP BASE NO：{{scope.item.ipBaseNo}}</p>
                </template>
              </el-autocomplete>
            </el-form-item>
            <el-form-item label="PA NAME NO:">
              <el-input v-model="memberTableItem.paNameNo" placeholder="請輸入"></el-input>
            </el-form-item>
            <el-form-item label="IP BASE NO:">
              <el-input v-model="memberTableItem.ipBaseNo" placeholder="請輸入"></el-input>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="协会" name="second">
          <el-form ref="memberAddSoc" label-position="right" label-width="150px" :model="memberTableItem" class="demo-form-inline" :rules="rules" @keyup.enter.native="saveMemberDialog">
            <el-form-item label="PUBLISHER代號:" prop="publisher">
              <el-input v-model="memberTableItem.publisher" placeholder="請輸入"></el-input>
            </el-form-item>
            <el-form-item label="SOC NO:" prop="socNo">
              <el-input type="number" v-model="memberTableItem.socNo" placeholder="請輸入"></el-input>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>

      <div style="text-align: center">
        <el-button @click="cancelMenberDialog">取消</el-button>
        <el-button type="primary" @click="saveMemberDialog">確定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import inputSelect from '../../components/input-select'
import { isEmail, isMobile, isIdentityId } from '@/utils/validate'

export default {
  name: 'claim',
  data() {
    var checkIdentitytionId = (rule, value, callback) => {
      console.log('=====', rule, value)
      var errorMsg = isIdentityId(value);
      if (errorMsg) {
        callback(new Error(errorMsg));
      } else {
        callback()
      }
    }
    
    return {
      disabled1:false,
      disabled2:false,
      tableresult:'暫無數據',
      activeName: 'first',
      steps: 1,
      disabled: true,
      itemData: {
        company: ''
      },
      formClaim: {},
      miniTotal: 0,
      miniTitle: '新增',
      miniShow: false,
      productShow: false,
      productTitle: '',
      miniTableItem: {"formulaType":1},
      productTableItem: {},
      minimaRules: {
        productFullName: [{ required: true, trigger: 'blur', pattern: /^((?!^[\s　]|[ ]$).)/gi, message: '名稱不能為空且首尾不能有空格' }],
        productShortName: [{ required: true, trigger: 'blur', pattern: /^((?!^[\s　]|[ ]$).)/gi, message: '名稱不能為空且首尾不能有空格' }],
        formulaPublic: [{ required: true, trigger: 'blur' }],
        formulaMechanical: [{ required: true, trigger: 'blur' }],
        mechanicalShare: [{ required: true, trigger: 'blur' }],
        publicShare: [{ required: true, trigger: 'blur' }]
      },

      rulesdate: {
        mechanicalStartTime: [{ validator: checkIdentitytionId, trigger: "blur" }, { required: true, message: '請輸入初始時間', trigger: 'blur' }],
        publicTransmissionStartTime: [{ validator: checkIdentitytionId, trigger: "blur" }, { required: true, message: '請輸入初始時間', trigger: 'blur' }],
        mechanicalEndTime: [{ validator: checkIdentitytionId, trigger: "blur" }, { required: true, message: '請輸入結束時間', trigger: 'blur' }],
        publicTransmissionEndTime: [{ validator: checkIdentitytionId, trigger: "blur" }, { required: true, message: '請輸入結束時間', trigger: 'blur' }],
        company: [{ required: true, message: '請輸入Company', trigger: 'blur' }],
      },
      productRules: {
        subscriberType: [{ required: true, trigger: 'blur' }],
        multiplierPER: [{ required: true, trigger: 'blur' }],
        multiplierMEC: [{ required: true, trigger: 'blur' }],
      },
      searchFormMini: {
        productName: '',
        page_num: 1
      },
      searchFormMember: {
        page_num: 1,
        ipBaseNo: '',
        nameNo: '',
        publisher: '',
        socNo: ''
      },
      agreementTotal: 0,
      memberTitle: '新增',
      memberShow: false,
      memberTableItem: {
        paNameNo: '',
        ipBaseNo: ''
      },
      memberActiveName: 'first',
      workGridData: [],
      tableData: [
        {
          date: [],
          name: '重製'
        },
        {
          date: [],
          name: '公傳'
        }
      ],
      tableData1: [],
      tableData2: [],
      ipDisabled: false,
      socDisabled: false,
      rules: {
        company: [{ required: true, message: '请输入company', trigger: 'blur' }],
        publisher: [{ required: true, message: '请输入', trigger: 'blur' }],
        paName: [{ required: true, message: '请输入', trigger: 'change' }],
        socNo: [{ required: true, message: '请输入', trigger: 'blur' }],
      },
      company: '',
      SVOD: {},
      AVOD: {},
      preClaim: {},
      AUDIO: {},
      editIndex: 0,
    }
  },
  watch:{
    'memberTableItem.paName': function(){
      if(!this.memberTableItem.paName){
        this.$set(this.memberTableItem, 'paNameNo', '')
        this.$set(this.memberTableItem, 'ipBaseNo', '')
      }
    }
  },
  methods: {
    clearNoNum(tit1, tit2) {
      let value = this[tit1][tit2]
      //先把非数字的都替换掉，除了数字和.
      value = value.replace(/[^\d.]/g, "");
      //保证只有出现一个.而没有多个.
      value = value.replace(/\.{2,}/g, ".");
      //必须保证第一个为数字而不是.
      value = value.replace(/^\./g, "");
      //保证.只出现一次，而不能出现两次以上
      value = value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
      //只能输入两个小数
      value = value.replace(/^(\-)*(\d+)\.(\d\d\d\d).*$/, '$1$2.$3');
      this.$set(this[tit1], tit2, value)

    },
    addProduct(title, data = {}, index) {
      this.productTitle = title
      this.productShow = true
      this.productTableItem = this.$utils.copy(data)
      this.editIndex = index
    },
    deleteProduct(index) {
      this.$msgbox.confirm(`確定進行[刪除]操作?`, '提示', {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.SVOD.pmsBasedList.splice(index, 1)
      }).catch(() => {
      })
    },
    clearStep3() {
      this.searchFormMember = {
        page_num: 1,
        ipBaseNo: '',
        nameNo: '',
        publisher: '',
        socNo: ''
      }
      this.getMemberList()
    },
    handlePage() {
      let pageNum = [...arguments][0]
      this.searchFormMini.page_num = pageNum
      this.getMiniList()
    },
    handlePage1() {
      let pageNum = [...arguments][0]
      this.searchFormMember.page_num = pageNum
      this.getMemberList()
    },
    saveStep2() {
      console.log(this.SVOD)
      console.log(this.AVOD)
      console.log(this.preClaim)
      console.log(this.AUDIO)
      let params = [this.SVOD, this.AVOD, this.preClaim,this.AUDIO]
      this.$http.post('/claim/editMinima', params).then(res => {
        console.log(res)
        if (res.success) {
          if (res.data.code) {
            this.$message.error(res.data.message)
            return
          }
          res.data.forEach(item => {
            this[item.productFullName] = item
          })
          this.$message.success('保存成功')
        } else {
          this.$message.error('保存失敗')
        }
      })
    },
    saveStep1() {
      this.$refs.itemdataForm.validate(validate => {
        if (validate) {
          // this.itemData.mechanicalStartTime = this.tableData[0].date[0]
          // this.itemData.mechanicalEndTime = this.tableData[0].date[1]
          // this.itemData.publicTransmissionStartTime = this.tableData[1].date[0]
          // this.itemData.publicTransmissionEndTime = this.tableData[1].date[1]
          let jsonData = this.itemData
          jsonData.mechanicalStartTime = this.$utils.transdatetype(this.itemData.mechanicalStartTime)
          jsonData.mechanicalEndTime = this.$utils.transdatetype(this.itemData.mechanicalEndTime)
          jsonData.publicTransmissionStartTime = this.$utils.transdatetype(this.itemData.publicTransmissionStartTime)
          jsonData.publicTransmissionEndTime = this.$utils.transdatetype(this.itemData.publicTransmissionEndTime)

          // for (const key in jsonData) {
          //   if (jsonData.hasOwnProperty(key)) {
          //     const element = jsonData[key]
          //     console.log('hasownproperyuty',element);
          //     if (!element) {
          //       let name = key
          //       switch (key) {
          //         case 'company':
          //           name = 'company';
          //           this.$toast({ tips: '請輸入' + name })
          //           break;
          //         case 'mechanicalStartTime':
          //           name = '重製開始時間';
          //           this.$toast({ tips: '請輸入' + name })
          //           break;
          //         case 'mechanicalEndTime':
          //           name = '重製結束時間';
          //           this.$toast({ tips: '請輸入' + name })
          //           break;
          //         case 'publicTransmissionStartTime':
          //           name = '公傳開始時間';
          //           this.$toast({ tips: '請輸入' + name })
          //           break;
          //         case 'publicTransmissionEndTime':
          //           name = '公傳結束時間';
          //           this.$toast({ tips: '請輸入' + name })
          //           break;
          //       }
          //       return false
          //     }
          //   }
          // }
          // this.$refs.step1Form.validate(validate => {
          //     if(validate){
          if (jsonData.mechanicalStartTime > jsonData.mechanicalEndTime || jsonData.publicTransmissionStartTime > jsonData.publicTransmissionEndTime) {
            this.$toast({ tips: '開始時間不能大於結束時間,請重新填寫!' })
            return
          }
          this.$http.post('/claim/set', jsonData).then(res => {
            if (res.success && res.data.code === 200) {
              this.itemData = res.data.data
              let item=res.data.data
              this.itemData.mechanicalStartTime = item.mechanicalStartTime.replace(/-/g, "")
              this.itemData.mechanicalEndTime = item.mechanicalEndTime.replace(/-/g, "")
              this.itemData.publicTransmissionStartTime = item.publicTransmissionStartTime.replace(/-/g, "")
              this.itemData.publicTransmissionEndTime = item.publicTransmissionEndTime.replace(/-/g, "")
              console.log('this.itemData', this.itemData)
              this.company = this.itemData.company
              this.disabled = false
              this.$message.success('保存成功')
            } else {
              this.$message.error('保存失敗')
            }
          })
        }
      })
    },
    aaa(){
      this.disabled2 = true
    },
    aaa2(){
      this.disabled1 = true
    },
    aaa3(){
      this.miniTableItem.mechanicalShare = ((this.miniTableItem.totalShare*1)*(this.miniTableItem.mecSplitShare*1)/100).toFixed(2)
      this.miniTableItem.publicShare  = ((this.miniTableItem.totalShare*1)*(this.miniTableItem.perSplitShare*1)/100).toFixed(2)
      this.disabled1 = false
    },
    updateView(e) {
      this.$forceUpdate()
    },
    checkNumSize(e, key) {
      let val = e.target.value
      if (val < 0) {
        val = 0
      }
      if (val > 100) {
        val = 100
      }
      this.$set(this.miniTableItem, key, val)
      this.miniTableItem.totalShare = this.miniTableItem.mechanicalShare*1 +  this.miniTableItem.publicShare*1
      this.miniTableItem.mecSplitShare = ((this.miniTableItem.mechanicalShare*1) / (this.miniTableItem.totalShare*1)*100).toFixed(2)
      this.miniTableItem.perSplitShare = ((this.miniTableItem.publicShare*1) / (this.miniTableItem.totalShare*1)*100).toFixed(2)
      this.disabled2 = false
    },
    saveMini(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          let obj = this.miniTableItem
          obj.claimSetId = this.itemData.id
          this.$http.post('/claim/minima', this.miniTableItem).then(res => {
            if (res.data.id) {
              this.getMiniList()
              this.miniShow = false
            } else {
              this.$toast({ tips: res.data.message })
            }
          })
        } else {
          return false
        }
      })
    },
    saveProduct(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          let obj = this.productTableItem
          let next = false
          this.SVOD.pmsBasedList = this.SVOD.pmsBasedList?this.SVOD.pmsBasedList:[]
          console.log(this.SVOD.pmsBasedList)
          this.SVOD.pmsBasedList.forEach((item, index) => {
            // this.productTitle=='edit'
            if (item.subscriberType == obj.subscriberType) {
              if (this.productTitle == 'add') {
                next = true
              } else if (index != this.editIndex) {
                next = true
              }
            }
          })
          if (next) {
            this.$toast({ tips: 'SubScriberType重複' })
            return
          }
          if (this.productTitle == 'add') {
            this.SVOD.pmsBasedList.push(obj)
          } else {
            this.$set(this.SVOD.pmsBasedList, this.editIndex, obj)
          }
          this.productShow = false
          this.$refs.productForm.clearValidate()

        } else {
          return false
        }
      })
    },
    deleteMini(row) {
      this.$alert('確定要刪除嗎？', '刪除', {
        confirmButtonText: '确定',
        showCancelButton: true,
        cancelButtonText: '取消',
        callback: action => {
          if (action == 'confirm') {
            let params = {
              id: row.id
            }
            this.$http.delete('/claim/minima', { params }).then(res => {
              if (res.status === 200) {
                this.getMiniList()
              }
            })
          }
        }
      });

    },
    deleteMember(row) {
      let params = {
        id: row.id
      }
      this.$alert('確定要刪除嗎？', '刪除會員信息', {
        showCancelButton: true,
        callback: action => {
          if (action === 'confirm') {
            this.$http.delete('/claim/sign_member', { params }).then(res => {
              if (res.status === 200) {
                this.getMemberList()
              }
            })
          }
        }
      })
    },
    cancelMiniDialog() {
      this.miniShow = false
    },
    getMiniList() {
      let params = this.searchFormMini
      params.setId = this.itemData.id
      console.log('&&&&&',params)
      if (params.productName) {
        params.page_num=1
      }
      this.tableresult = '數據加載中'
      this.$http.get('/claim/minima', { params }).then(res => {
        if (res.status === 200) {
          this.tableData1 = res.data.list
          this.miniTotal = res.data.total
          if (this.company.trim() == 'youtube' || this.company.trim() == 'google') {
            this.tableData1.forEach(item => {
              console.log('getminilist******', item)
              if(item.productFullName === 'Audio-Only'){
                this.AUDIO = item
              } else {
                this[item.productFullName] = item
              }
            })
          }
        }
        this.tableresult = '暫無數據'
      })
    },
    editMiniItem(type, item = {}) {
      this.miniShow = true
      this.miniTableItem = JSON.parse(JSON.stringify(item))
      // if (this.miniTableItem.totalShare) {
      //   this.miniTableItem.mecSplitShare = ((this.miniTableItem.mechanicalShare*1) / (this.miniTableItem.totalShare*1)).toFixed(2)
      //   this.miniTableItem.perSplitShare = ((this.miniTableItem.publicShare*1) / (this.miniTableItem.totalShare*1)).toFixed(2)
      // }else{
      //   this.miniTableItem.mecSplitShare = ''
      //   this.miniTableItem.perSplitShare = ''
      // }
      
      if (type === 'add') {
        this.miniTitle = '新增'
      } else {
        this.miniTitle = '編輯'
      }

    },
    addMember(type, item = {}) {
      this.memberShow = true
      this.memberTitle = type
      if (item.socNo) {
        this.memberActiveName = 'second'
      } else {
        this.memberActiveName = 'first'
      }
      this.memberTableItem = JSON.parse(JSON.stringify(item))
      this.$nextTick(() => {
        this.$refs['memberAddMember'] && this.$refs['memberAddMember'].clearValidate()
        this.$refs['memberAddSoc'] && this.$refs['memberAddSoc'].clearValidate()
      })
    },
    getMemberList() {
      let params = this.searchFormMember
      params.setId = this.itemData.id
      console.log('=====',params)
      if (params.ipBaseNo||params.nameNo||params.publisher||params.socNo) {
        params.page_num=1
      }
      this.tableresult = '數據加載中'
      this.$http.get('/claim/sign_member', { params }).then(res => {
        if (res.status === 200) {
          this.tableData2 = res.data.list
          this.agreementTotal = res.data.total
        }
        this.tableresult = '暫無數據'
      })
    },

    //保存分配SignMember信息
    checkedWriter(item) {
      item.claimSetId = this.itemData.id
      this.$http.post('/claim/sign_member', item).then(res => {
        this.getMemberList()
      })
    },

    //獲取提示建议框數據
    getItemData(query, cb) {
      this.socDisabled = true
      if (this.memberTableItem.paName) {
        this.$http
          .post(
            '/agreement/getIpBaseNoByName?name=' +
            this.memberTableItem.paName
          )
          .then(res => {
            cb(res.data)
          })
      }
    },
    //設置提示建议框數據
    setItemData(item) {
      this.$set(this.memberTableItem, 'paName', item.name)
      this.$set(this.memberTableItem, 'paNameNo', item.nameNo)
      this.$set(this.memberTableItem, 'ipBaseNo', item.ipBaseNo)
    },
    cancelMenberDialog() {
      this.memberShow = false
    },
    saveMemberDialog() {
      let params = this.$utils.copy(this.memberTableItem)
      params.claimSetId = this.itemData.id
      let ref = this.memberActiveName == 'first' ? 'memberAddMember' : 'memberAddSoc'
      if (ref === 'memberAddMember') {
        params.socNo = ''
      } else {
        params.paName = ''
        params.paNameNo = ''
        params.ipBaseNo = ''
      }
      this.$refs[ref].validate(validate => {
        if (validate) {
          this.$http.post('/claim/sign_member', params).then(res => {
            if (res.data.id) {
              this.memberShow = false
              this.getMemberList()
            } else {
              this.$toast({ tips: res.data.message })
            }
          })
        }
      })

    },
    init() {
      let item = JSON.parse(this.$route.query.item)
      this.itemData = item
      this.itemData.mechanicalStartTime = item.mechanicalStartTime.replace(/-/g, "")
      this.itemData.mechanicalEndTime = item.mechanicalEndTime.replace(/-/g, "")
      this.itemData.publicTransmissionStartTime = item.publicTransmissionStartTime.replace(/-/g, "")
      this.itemData.publicTransmissionEndTime = item.publicTransmissionEndTime.replace(/-/g, "")

      this.tableData = [
        {
          date: [
            this.itemData.mechanicalStartTime,
            this.itemData.mechanicalEndTime
          ],
          name: '重製'
        },
        {
          date: [
            this.itemData.publicTransmissionStartTime,
            this.itemData.publicTransmissionEndTime
          ],
          name: '公傳'
        }
      ]
      if (item.id) {
        this.disabled = false
        this.getMiniList()
        this.getMemberList()
      } else {
        this.disabled = true
      }
    },
    closeDialog() {
      // 清空校驗信息
      this.$refs.miniForm.clearValidate()
    },
    procloseDialog() {
      this.$refs.productForm.clearValidate()
    }
  },
  mounted() {
    this.company = this.$route.query.title
  },
  activated() {
    let item = JSON.parse(this.$route.query.item)
    console.log('this.$route.query')
    console.log(item)
    if (item.id) {
      this.init()
    }
    this.company = this.$route.query.title
  }
}
</script>

<style scoped>
.progress {
  margin-bottom: 50px;
}

/deep/ .el-table {
  margin-bottom: 50px;
}

/deep/ .el-form-item__label {
  margin-left: 0 !important;
  padding: 0 10px 0 0;
}
.title p {
  float: left;
  color: #333;
  font-weight: bold;
  font-size: 18px;
}
.requrestyle /deep/ .el-form-item__label {
    width: 0px !important;
}
>>> .el-input__inner{
  padding: 0 10px;
}
>>> .el-form--inline .el-form-item{
  margin-right:8px
}
.addResetMember >>> .el-dialog{
  min-width: 500px;
}
</style>
