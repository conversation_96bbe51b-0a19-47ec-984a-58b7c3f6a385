<template>
    <div>
        <el-steps :active="2">
            <el-step title="清單處理"></el-step>
            <el-step title="配置分配代號"></el-step>
            <el-step title="分配配置"></el-step>
            <el-step title="分配提交"></el-step>
        </el-steps>
        <div class="clear" style="margin-top: 50px">
            <el-form :inline="true" class="demo-form-inline" @keyup.enter.native="next()">
                <el-form-item class="f-l" label="本次分配編號:">
                    <el-input v-model="distNo"></el-input>
                </el-form-item>
                <div class="f-l" style="margin-left: 10px;line-height: 34px;">
                    <a href="javascript: void(0)" @click="createNo()">創建新的分配編號</a>
                </div>
            </el-form>
        </div>
        <div style="margin-top: 20px;padding-left: 120px">
            <el-button type="primary" @click="next()">下一步</el-button>
        </div>
        <el-dialog title="創建分配編號" :visible.sync="show" width="400px" :close-on-click-modal="false">
            <el-form :inline="true">
                <el-form-item label="分配編號">
                    <el-input type="text" v-model="add.distNo"></el-input>
                </el-form-item>
                <el-form-item label="分配描述">
                    <el-input type="text" v-model="add.distDescribe"></el-input>
                </el-form-item>
                <el-form-item class="t-c" style="width: 100%;">
                    <el-button type="primary" @click="saveNo">確認</el-button>
                    <el-button @click="cancel">取消</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
    </div>
</template>

<script>
    export default {
        name: 'createdistribution',
        data () {
            return {
                distNo: '',
                add: {
                    distNo: '',
                    distDescribe: ''
                },
                show: false,
            }
        },
        methods: {
            createNo(){
                this.add.distNo = this.distNo;
                this.add.distDescribe = '';
                this.show = true;
            },
            saveNo(){
                this.$http.post('/dist/param/number?distNo=' + this.add.distNo + '&distDescribe=' + encodeURIComponent(this.add.distDescribe)).then( res => {
                    if(res.success){
                        if(res.data.code){
                            this.$toast({tips: res.data.message})
                        }else{
                            this.$toast({tips: '創建成功'});
                            this.show = false;
                            this.distNo = this.add.distNo;
                        }

                    }
                })
            },
            cancel(){
                this.add.distNo = '';
                this.add.distDescribe = '';
                this.show = false;
            },
            next () {
                if(this.distNo){
                    this.$http.get('/dist/param/info/distNo?distNo=' + this.distNo).then( res => {
                        if(res.success){
                            if(res.data.code && res.data.code == '20006'){
                                // distNo 不存在
                                this.$toast({tips: '此分配編號不存在,請確認分配編號或創建新的分配編號！'})
                            }else{
                                this.$bus.$emit('closeCurrentTab', () => { this.$router.push({ name: 'distribute-info', query: {distNo: this.distNo} })});

                            }
                        }
                    })
                }else{
                    this.$toast({tips: '請填寫分配編號或創建新的分配編號！'})
                }
            }
        },
        mounted () {
        }
    }
</script>
