<template>
    <div>
        <el-drawer
            :visible.sync="detailShow"
            :wrapperClosable='false'
            direction="rtl"
            size="800"
        >
            <el-form :inline="true" class="demo-form-inline" @keyup.enter.native="searchFn(1)">
                <el-form-item>
                    <el-input
                      type="text"
                      v-model="searchData.itemText"
                      placeholder="名稱"
                      style="width: 160px"
                    ></el-input>
                </el-form-item>
                <el-form-item prop="">
                    <el-select
                        v-model="searchData.status"
                        placeholder="狀態"
                        style="width: 120px"
                    >
                        <el-option label="啟用" :value="1"></el-option>
                        <el-option label="禁用" :value="0"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="searchFn(1)">查詢</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="addItem()">新增</el-button>
                </el-form-item>
                <el-form-item>
                    <span class="clear-search" @click="clearSearch()">清除搜索</span>
                </el-form-item>
            </el-form>
            <el-table
                :data="tableData"
                border
                stripe
                style="width: 100%"
                v-loading="loading"
                :empty-text="emptyText"
            >
                <el-table-column prop="itemText" label="名稱"></el-table-column>
                <el-table-column prop="itemValue" label="數據值"></el-table-column>
                <el-table-column
                    label="operation"
                    width="200"
                >
                    <template slot-scope="scope">
                        <div class="option">
                            <el-button @click="itemEdit(scope.row)" type="text" size="small">編輯</el-button>
                            <span style="color: rgba(0,0,0,.1);">|</span>
                            <el-button @click="itemDelete(scope.row, scope.$index)" type="text" size="small">刪除</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
              background
              layout="prev, pager, next"
              :total="total"
              @current-change="searchFn"
              :current-page="currentPage"
            >
            </el-pagination>
        </el-drawer>
        <el-dialog :title="isEdit?'編輯':'新增'" :visible="addDetail" width="550px" :show-close="false" :close-on-click-modal="false">
            <el-form label-position="right" label-width="100px" ref="editForm" :model="EditData" class="demo-form-inline" :rules="rules">
                <el-form-item label="名稱" prop="itemText">
                    <el-input style="width:300px" v-model="EditData.itemText" placeholder="請輸入名稱"></el-input>
                </el-form-item>
                <el-form-item label="數據值" prop="itemValue">
                    <el-input style="width:300px" v-model="EditData.itemValue" placeholder="請輸入數據值"></el-input>
                </el-form-item>
                <el-form-item label="描述">
                    <el-input style="width:300px" v-model="EditData.description" placeholder="描述"></el-input>
                </el-form-item>
                <el-form-item label="排序值">
                    <el-input style="width:100px" v-model="EditData.sortOrder" placeholder="排序值"></el-input>
                    <span>值越小越靠前</span>
                </el-form-item>
                <el-form-item label="是否啟用">
                    <el-switch
                      v-model="EditData.status"
                      :active-value="1"
                      :inactive-value="0"
                    >
                    </el-switch>
                </el-form-item>
            </el-form>
            <div style="text-align: right;margin-top: 50px;">
                <el-button @click="cancelDialog">关闭</el-button>
                <el-button type="primary" @click="addFormItem">確定</el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
export default {
    name:'',
    prop:{
        
    },
    watch:{
        
    },
    data(){
        return{
            detailShow:false,
            addDetail:false,
            searchData:{
                itemText:'',
                status:'',
            },
            tableData:[],
            loading:false,
            currentPage:1,
            total:1,
            dictId:'',
            isEdit:false,
            rules:{
                itemText:[{ required: true, message: '請輸入名稱', trigger: 'blur' }],
                itemValue:[{ required: true, message: '請輸入數據值', trigger: 'blur' }],
            },
            EditData:{
                itemText:'',
                itemValue:'',
                description:'',
                sortOrder:'',
                status:1
            },
            emptyText: '暫無數據',
        }
    },
    methods:{
        addFormItem(){
            this.$refs.editForm.validate(validate => {
                if(validate){
                    let params = this.EditData
                    params.dictId = this.dictId
                    params.status = params.status?params.status:0
                    this.$http.post('/sysDict/item/saveOrUpdateDictItem',params).then(res => {
                        if(res.success){
                            if(res.data.code === 200){
                                this.cancelDialog()
                                this.searchFn(this.currentPage)
                            }else{
                                this.$toast({tips: res.data.message})
                            }
                        }
                    })
                }
            })
        },
        cancelDialog(){
            this.$refs.editForm.resetFields()
            this.addDetail=false
        },
        itemShow(id){
            this.dictId = id
            this.searchFn()
            this.detailShow = true
        },
        searchFn(num){
            this.loading=true
            let params = this.searchData
            params.dictId = this.dictId
            params.pageNum = num?num:1
            params.pageSize = 10
            console.log(params)
            this.emptyText = '數據加載中';
            this.$http
            .get("/sysDict/item/getList", { params })
            .then((res) => {
                this.loading=false
                if (res.success) {
                    if(res.data.data){
                        this.tableData = res.data.data.list;
                        this.total = res.data.data.total;
                        this.currentPage = num ? num : 1;
                    }else{
                        this.tableData=[]
                        this.total=1
                        this.currentPage=1
                    }
                    if(! this.tableData || this.tableData.length == 0){
                        this.emptyText = '暫無數據';
                    }
                    if(!this.tableData.length && num>1){
                        this.searchFn(num-1)
                    }
                }
            });
        },
        clearSearch(){
            this.searchData={
                itemText:'',
                status:'',
            }
            this.searchFn()
        },
        addItem(){
            this.EditData={}
            this.isEdit = false
            this.addDetail=true
        },
        itemEdit(data){
            this.EditData=this.$utils.copy(data)
            this.isEdit = true
            this.addDetail=true
        },
        itemDelete(data){
            this.$msgbox.confirm(`確定進行[刪除]操作?`, '提示', {
                confirmButtonText: '確定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.deleteFn(data.id)
            }).catch( () => {
            })
        },
        deleteFn(id){
            this.loading=true
            this.$http
            .delete(`/sysDict/item/deleteDictItem/${id}`)
            .then((res) => {
                this.loading=false
                if (res.success) {
                    this.searchFn(this.currentPage)
                }
            });
        },
    }
}
</script>
<style scoped>
/deep/ .el-drawer__header{
    margin-bottom: 0;
}
/deep/ .el-drawer__body{
    padding: 20px;
}
</style>