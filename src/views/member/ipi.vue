<template>
    <div class="ipibpox">
        <el-form :inline="true" :model="formInline" ref="form" class="search-form" :class="showHigh ? 'high' : ''" @keyup.enter.native="onSubmit">
            <div v-if="!showHigh">
                <el-form-item prop="name">
                    <el-input v-model="formInline.name" placeholder="Name" style="width: 160px"></el-input>
                </el-form-item>
                <el-form-item prop="ip_no">
                    <el-input v-model="formInline.ip_no" placeholder="IP Base No" style="width: 160px;"></el-input>
                </el-form-item>
                <el-form-item prop="name_no">
                    <el-input v-model="formInline.name_no" placeholder="IP Name No" style="width: 160px;"></el-input>
                </el-form-item>
                <el-form-item prop="ip_type">
                    <el-select v-model="formInline.ip_type" placeholder="IP Type" style="width: 120px;">
                        <el-option label="會員類型" value=""></el-option>
                        <el-option label="L" value="L"></el-option>
                        <el-option label="N" value="N"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item prop="role_code">
                    <el-select v-model="formInline.role_code" placeholder="role_code" style="width: 120px;">
                        <el-option label="會員角色" value=""></el-option>
                        <el-option v-for=" (value, key) in roleCodeList" :key="key" :label="value.roleCode" :value="value.roleCode"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item prop="soc">
                    <el-input v-model="formInline.soc" placeholder="Soc" style="width: 90px;"></el-input>
                </el-form-item>
                <el-form-item prop="status">
                    <el-select v-model="formInline.status" placeholder="status" style="width: 120px;">
                        <el-option label="All Status" value=""></el-option>
                        <el-option
                            v-for="item in options"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>
                <!-- <el-form-item v-if="!showHigh">
                    <span class="clear-search" @click="openHigh()" style="color: #409EFF;">高级搜索</span>
                </el-form-item>
            </div>
            <div style="text-align: center; position: absolute;bottom: 0;width: 100%;background: #fff;"> -->
                <el-form-item>
                    <el-button type="primary" @click="onSubmit" v-if="isAuth('member:ipi:find')">搜索</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="success" @click="dummyAdd" v-if="isAuth('member:ipi:add')">新增</el-button>
                </el-form-item>
                <el-form-item style="">
                    <el-button type="success" @click="exportList" v-if="isAuth('member:ipi:exportout')">導出</el-button>
                </el-form-item>
                <el-form-item>
                    <span class="clear-search" @click="clearSearch()" v-if="isAuth('member:ipi:find')">清除搜索</span>
                </el-form-item>
                <el-form-item v-if="!showHigh">
                    <span class="clear-search" @click="openHigh()" style="color: #409EFF;" v-if="isAuth('member:ipi:find')">高级搜索</span>
                </el-form-item>
            </div>
        </el-form>
        <el-table
            :data="tableData"
            border
            stripe
            style="width: 100%" 
            class="table-fixed"
            v-loading="loading"
            :empty-text="emptyText">
            <el-table-column
                prop="name"
                label="Name"
                min-width="180">
            </el-table-column>
            <el-table-column
                prop="chinese_name"
                label="Chinese Name"
                width="180">
            </el-table-column>
            <el-table-column
                prop="ip_base_no"
                label="IP Base no"
                width="160">
            </el-table-column>
            <el-table-column
                prop="ip_name_no"
                label="IP Name no"
                width="130">
            </el-table-column>
            <el-table-column
                prop="ip_type"
                label="IP Type"
                width="90px">
            </el-table-column>
            <el-table-column
                prop="name_type"
                label="NameType"
                width="110px">
            </el-table-column>
            <el-table-column
                prop="status"
                label="status"
                width="80">
                <template slot-scope="scope">
                    {{!(scope.row.name_type === 'HR' || scope.row.name_type === 'PG') ? scope.row.status : ''}}
                </template>
            </el-table-column>
            <el-table-column
                prop="society_code"
                label="soc"
                width="60px"
                >
                <template slot-scope="scope">
                    <span v-if="scope.row.society_code.toString().length == 3">
                        {{scope.row.society_code}}
                    </span>
                    <span v-if="scope.row.society_code.toString().length == 2">
                        {{'0'+scope.row.society_code}}
                    </span>
                    <span v-if="scope.row.society_code.toString().length == 1">
                        {{'00'+scope.row.society_code}}
                    </span>
                </template>
            </el-table-column>
            <el-table-column
                label="operation"
                width="180"
                fixed="right">
                <template slot-scope="scope">
                    <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                        <span class="a-blue" @click="handleClick(scope.row,'look')" v-if="!isAuth('member:ipi:change')">查看</span>
                        <span class="a-blue" @click="handleClick(scope.row)" v-if="isAuth('member:ipi:change')">編輯</span>
                        <span class="a-blue" @click="note(scope.row)" v-if="isAuth('member:ipi:note')&&(!(scope.row.name_type === 'HR' || scope.row.name_type === 'PG'))">Note</span>
                        <el-dropdown  @command="goWorkList" v-if="isAuth('member:ipi:work_list')||isAuth('member:ipi:income')">
                            <span class="a-blue">
                                更多
                            </span>
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item :command ="scope.row" v-if="isAuth('member:ipi:work_list')">work list</el-dropdown-item>
                                <el-dropdown-item v-if="isAuth('member:ipi:income')">income</el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                    </span>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :total='total' 
            @current-change="handleCurrentChange"
            :current-page="currentPage">
        </el-pagination>

        <el-dialog
            :visible.sync="lookDialogVisible"
            width="1100px"
             :close-on-click-modal="false"
            center>
            <div>
                <el-form :inline="true" :model="formDialog" ref="formInline" class="demo-form-inline">
                    <el-form-item prop="name" label="name">
                        <el-input v-model="formDialog.name" readonly></el-input>
                    </el-form-item>
                    <el-form-item prop="ip_base_no" label="IP Base No">
                        <el-input v-model="formDialog.ip_base_no" readonly></el-input>
                    </el-form-item>
                    <el-form-item prop="ip_name_no" label="IP Name No">
                        <el-input v-model="formDialog.ip_name_no" readonly></el-input>
                    </el-form-item>
                </el-form>
                <el-table
                    :data="tableDialogData"
                    border
                    stripe
                    style="width: 100%" 
                    v-loading="loading"
                    :empty-text="emptyText1"
                    >
                    <el-table-column
                        prop="name"
                        label="Name"
                        width="180">
                    </el-table-column>
                    <el-table-column
                        prop="chineseName"
                        label="Chinese Name"
                        width="180">
                    </el-table-column>
                    <el-table-column
                        prop="nameType"
                        label="nameType"
                        width="180">
                    </el-table-column>
                    <el-table-column
                        prop="ipBaseNo"
                        label="IP Base no">
                    </el-table-column>
                    <el-table-column
                        prop="nameNo"
                        label="IP Name no">
                    </el-table-column>
                    <el-table-column
                        fixed="right"
                        label="operation"
                        width="100">
                        <template slot-scope="scope">
                            <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                                <!-- <el-button @click="confirmDialog(scope.row)" type="text" size="small">確認</el-button> -->
                                <el-button @click="confirmDialog(scope.row)" type="text" size="small">查看</el-button>
                            </span>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="lookDialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="lookDialogVisible = false">確 定</el-button>
            </span>
        </el-dialog>
        <export-dlg ref="exportCom" v-if="exportShow"></export-dlg>
        <note v-if="noteShow" ref="noteCom" :noteInfo="noteInfo"></note>
    </div>
</template>

<script>
    import exportDlg from './dialog/export'
    import axios from '../../utils/httpRequest'
    import qs from 'qs'
    import note from './dialog/note';
    const cityOptions = ['P', 'M', 'CP', 'MA']
    export default {
        name: 'ipi',
        data () {
            return {
                status: [],
                options: [
                    {
                        value: '1',
                        label: '1'
                    }, {
                        value: '2',
                        label: '2'
                    }, {
                        value: '3',
                        label: '3'
                    }, {
                        value: '4',
                        label: '4'
                    }],
                lookDialogVisible: false,
                dummyDialogVisible: false,
                checkedCities1: ['P', 'M'],
                cities: cityOptions,
                myindex: 0,
                tabShow: false,
                loading: false,
                checked: false,
                total: 1,
                currentPage: 1,
                formInline: {
                    name: '',
                    status: '',
                    ip_no: '',
                    name_no: '',
                    ip_type: '',
                    role_code: '',
                    soc: '',
                    page_num: 1,
                    page_size: '10'
                },
                highSearch: {
                    bankName: '',
                    name: '',
                    person: '',
                    mail_name: '',
                    idCardNo: '',
                    memberNo: '',
                    ciNo: '',
                    terminate: false,
                    recipientBrNoOrHkId: '',
                    telephoneOrMobileOrFaxNo: '',
                    terminateReason: '',
                    email: '',
                    districtStreetSectionLaneAlleyNo: '',
                    pageNum: 1,
                    pageSize: '10'

                },
                formDialog: {
                    name: '',
                    ip_no: '',
                    name_type: ''
                },
                formDummy: {
                    user: '',
                    region: '',
                    type: ''
                },
                obj: {},
                tableDialogData: [],
                tableData: [
                    // {name: '1', chinese: '', ipBaseNo: '', nameNo: '', ipType: '', societyCode: ''},
                    // {name: '2', chinese: '', ipBaseNo: '', nameNo: '', ipType: '', societyCode: ''}
                ],
                roleCodeList: [],

                noteShow:false,
                noteInfo: {},

                exportShow: false,

                showHigh: false,
                emptyText: '數據加載中',
                emptyText1: '數據加載中',
            }
        },
        components: {
            exportDlg,
            note
        },
        methods: {
            goWorkList(row){
                console.log('row: ', row);
                this.$router.push({name: 'works-list', query: { ipBaseNo: row.ip_base_no}})
            },
            clearSearch(){
                // this.$refs.form.resetFields();
                this.formInline= {
                    name: '',
                    status: '',
                    ip_no: '',
                    name_no: '',
                    ip_type: '',
                    role_code: '',
                    soc: '',
                    page_num: 1,
                    page_size: '10'
                }
                this.onSubmit();
            },
            onSubmit(){
                if(this.showHigh){
                    this.highSearchFn();
                }else{
                    this.onSubmitFn();
                }
            },
            onSubmitFn() {
                // this.loading = true
                // this.formInline.status = this.status.toString()
                this.formInline.page_num = 1;
                this.$utils.trim(this.formInline);
                let formData = qs.stringify(this.formInline);
                let config = {headers: { 'content-type': 'application/x-www-form-urlencoded' }}
                // console.log('-------------',this.formInline,'+++',formData)
                this.emptyText = '數據加載中';
                axios.post('/ip/name/es', formData, config).then(res => {
                    if (res.success) {
                        if (!res.data.code && res.data.code != 200) {
                            this.tableData = res.data.list
                            this.total = parseInt(res.data.total)
                        }else{
                            this.tableData=[]
                            this.total=1
                            this.$toast({ tips: res.data.message });
                        }
                        this.currentPage = 1;
                        if(! this.tableData || this.tableData.length == 0){
                            this.emptyText = '暫無數據';
                        }
                    }
                })
            },
            highSearchFn(page){
                this.highSearch.pageNum = page ? page : 1;
                this.$utils.trim(this.highSearch);
                this.emptyText = '數據加載中';
                axios.post('/member/highSearch', this.highSearch).then(res => {
                    if (res.status === 200) {
                        this.tableData = res.data.list
                        if(! this.tableData || this.tableData.length == 0){
                            this.emptyText = '暫無數據';
                        }
                        this.total = parseInt(res.data.total)
                        this.currentPage = this.highSearch.pageNum;
                    }
                })
            },
            dummyAdd () {
                this.myindex += 1
                this.$router.push({ name: 'member-dummyinfo', query: {index: this.myindex} })
            },
            handleClick (item,type) {
                this.obj = item
                let ipnameno = item.ip_name_no
                console.log('&&&&&&',item.name_type);
                if (item.name_type === 'HR' || item.name_type === 'PG') {
                    this.lookDialogVisible = true
                    this.formDialog = item
                    this.emptyText1 = '數據加載中';
                    axios.get('/ip/name/' + ipnameno + '/members').then(res => {
                        this.tableDialogData = res.data
                        if(! this.tableDialogData || this.tableDialogData.length == 0){
                            this.emptyText1 = '暫無數據';
                        }
                    })
                }
                if (item.name_type !== 'HR' && item.name_type !== 'PG') {
                    if (item.society_code.toString().indexOf('161') === -1) {
                        this.tabShow = false
                    } else {
                        this.tabShow = true
                    } 
                    this.$router.push({ path: '/member-info2', name: 'member-info2',
                        query: {id: item.id, nameId:item.ip_base_no + item.ip_name_no, title: item.chinese_name ? item.chinese_name : item.name, tabShow: this.tabShow, ipBaseNo: item.ip_base_no, ipId: item.id,type:type}
                    })
                }
            },
            confirmDialog (item) {
                this.lookDialogVisible = false
                if (item.society_code && item.society_code.toString().indexOf('161') === -1) {
                    this.tabShow = false
                } else {
                    this.tabShow = true
                }

                console.log(':', item);
                this.$router.push({ path: '/member-info', name: 'member-info',
                    query: {id: item.id, nameId:item.ipBaseNo + item.nameNo, title: item.chineseName ? item.chineseName : item.name, tabShow: this.tabShow, ipBaseNo: item.ipBaseNo, ipId: item.id},
                    // params: {tabShow: this.tabShow, ipBaseNo: item.ip_base_no, ipId: item.id}
                 })
            },
            handleCurrentChange (val) {
                if(this.showHigh){
                    this.highSearchFn(val);
                }else{
                    this.formInline.page_num = val
                    this.$utils.trim(this.formInline);
                    let formData = qs.stringify(this.formInline)
                    let config = {headers: { 'content-type': 'application/x-www-form-urlencoded' }}
                    this.emptyText = '數據加載中';
                    axios.post('/ip/name/es', formData, config).then(res => {
                        if (res.success) {
                            if (!res.data.code && res.data.code != 200) {
                                this.tableData = res.data.list
                                this.total = parseInt(res.data.total)
                            }else{
                                this.tableData=[]
                                this.total=1
                                this.$toast({ tips: res.data.message });
                            }
                            this.currentPage = 1;
                            if(! this.tableData || this.tableData.length == 0){
                                this.emptyText = '暫無數據';
                            }
                        }
                    })
                }
            },
            note(row){
                this.noteInfo = row;
                this.noteShow = true;
                this.$nextTick( () => {
                    this.$refs.noteCom.init();
                })
            },
            exportList(){
                this.exportShow = true;
                this.$nextTick( () => {
                    this.$refs.exportCom.init();
                })
            },
            openHigh(){
                // this.showHigh = true;
                this.$router.push({name: 'member-ipi-height'})
            },
            closeHigh(){
                this.showHigh = false;
            }
        },
        mounted () {
            this.$http.get('/ref/role').then( res => {
                if(res.success){
                    this.roleCodeList = res.data;
                }
            })
            this.$utils.trim(this.formInline);
            let formData = qs.stringify(this.formInline);

            let config = {headers: { 'content-type': 'application/x-www-form-urlencoded' }}
            // this.loading = true

            this.emptyText = '數據加載中';
            axios.post('/ip/name/es', formData, config).then(res => {
                // this.loading=false;
                if (res.status === 200) {
                    this.tableData = res.data.list
                    if(! this.tableData || this.tableData.length == 0){
                        this.emptyText = '暫無數據';
                    }
                    this.total = parseInt(res.data.total)
                    this.currentPage = 1;
                    // this.loading = false
                }
            })
        },
        activated () {
            // this.getButtonMenu()
            console.log(new Date().getTime());
            setTimeout( () => {
            console.log(new Date().getTime());

                if(this.$route.query.update){
                    this.$utils.trim(this.formInline);
                    let formData = qs.stringify(this.formInline);
                    let config = {headers: { 'content-type': 'application/x-www-form-urlencoded' }}
                    this.emptyText = '數據加載中';
                    axios.post('/ip/name/es', formData, config).then(res => {
                        if (res.status === 200) {
                            this.tableData = res.data.list;
                            if(! this.tableData || this.tableData.length == 0){
                                this.emptyText = '暫無數據';
                            }
                            this.total = parseInt(res.data.total);
                            // this.loading = false;
                        }
                    })
                }
            }, 500)
        }
    }
</script>
<style lang="scss" scoped>
    .search-form{
        position: relative;
        /deep/ input{
            padding: 0 6px;
        }

    }
    .search-form.high{
        height: 234px;
        // height: auto !important;
    }
    .el-form--inline .el-form-item{
        margin-right: 2px;
    }
    /deep/ .el-dialog__header{
        text-align: left;
        font-weight: bold;
    }
    .search-form{
        margin: 0 auto;
        text-align: left;
    }
    .ter{
       /deep/ .el-form-item__label{
            margin-left: 0;
        }
    }
</style>

<style>
    .el-card{
        margin-bottom: 20px;
    }
    .memberdetail{
        width: 100%;
    }
    .memberdetail .el-form-item__content{
        width: calc(100% - 150px )!important;
    }
</style>
