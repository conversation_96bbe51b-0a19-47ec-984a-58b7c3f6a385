<!-- 樣本列表-->
<template>
    <div class="ipibpox">
        <el-form :inline="true" :model="search" ref="form" class="demo-form-inline" @keyup.enter.native="searchFn(1)">
            <el-form-item prop="Work No" label="Work No">
                <el-input v-model="search.worknum" placeholder="" style="width: 180px;"></el-input>
            </el-form-item>
            <el-form-item prop="Work Name" label="Work Name">
                <el-input v-model="search.workName" placeholder="" style="width: 180px;"></el-input>
            </el-form-item>
            <el-form-item label="Status">
                <el-select v-model="search.status" placeholder="所有狀態" style="width: 120px;">
                    <el-option label="所有狀態" value=""></el-option>
                    <el-option v-for=" (value, key) in config.status" :key="key" :value="key" :label="value"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="searchFn()">搜索</el-button>
            </el-form-item>
            <el-form-item>
                <span class="clear-search" @click="clearSearch()">清除搜索</span>
            </el-form-item>
        </el-form>
        <el-table 
            :empty-text="emptyText" 
            stripe 
            :data="tableData" 
            border 
            style="width: 100%" 
            v-loading="loading"
            >
            <el-table-column
                prop="id"
                label="ID"
                width="40">
            </el-table-column>
            <el-table-column
                prop="worknum"
                label="Work No"
                width="120">
            </el-table-column>
            <el-table-column
                prop="worknumSociety"
                label="Work Soc"
                width="100">
            </el-table-column>
            <el-table-column
                prop="workName"
                label="Work Name"
                min-width="120">
            </el-table-column>
            <el-table-column
                prop="workArtist"
                label="Artist">
                <template slot-scope="scope">
                    <span :title="scope.row.workArtist">{{scope.row.workArtist | addSpace}}</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="workComposer"
                label="Composer"
                min-width="110">
                <template slot-scope="scope">
                    <span :title="scope.row.workComposer">{{scope.row.workComposer | addSpace}}</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="workIsrc"
                label="Isrc">
                <template slot-scope="scope">
                    <span :title="scope.row.workIsrc">{{scope.row.workIsrc}}</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="workIswc"
                label="Iswc">
                <template slot-scope="scope">
                    <span :title="scope.row.workIswc">{{scope.row.workIswc}}</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="url"
                label="Sample Url"
                min-width="110">
                <template slot-scope="scope">
                    <span :title="scope.row.url">{{scope.row.url}}</span>
                </template>
            </el-table-column>
            <!-- <el-table-column
                prop="storePath"
                label="Sample Path"
                min-width="130">
            </el-table-column> -->
            <el-table-column
                prop="status"
                label="Status"
                min-width="90">
                <template slot-scope="scope">
                    {{scope.row.status | Status}}
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :total="total" @current-change="handleCurrentChange" :current-page="currentPage">
        </el-pagination>
    </div>
</template>

<script>
    export default {
        name: 'sampleDateList',
        data () {
            return {
                tableData: [],
                total: 0,
                currentPage: 1,
                search: {
                    worknum: '',
                    workName: '',
                    status: ''
                },
                loading: false,
                config: {
                    status: {
                        0: '待下載',
                        1: '下載成功',
                        2: '下載失敗',
                        3: '提取成功'
                    }
                },
                emptyText: '暫無數據',
            }
        },
        created(){
            this.init();
        },
        filters: {
            Status: function(status){
                let config = {
                    0: '待下載',
                    1: '下載成功',
                    2: '下載失敗',
                    3: '提取成功'
                }
                return config[status];
            }
        },
        methods: {
            clearSearch(){
                this.search= {
                    worknum: '',
                    workName: '',
                    status: ''
                }
                this.searchFn(1);
            },
            init(){
                this.searchFn(1);
            },
            searchFn (page) {
                this.loading = true;
                let ajaxData = this.$utils.copy(this.search);
                ajaxData.page_num = page ? page : 1;
                ajaxData.page_size = 10;
                this.emptyText = '數據加載中';
                this.$http.get('/sampleMatch', {params: ajaxData}).then(res => {
                    if (res.success) {
                        console.log(res)
                        if (!res.data.code && res.data.code != 200) {
                            this.tableData = res.data.list;
                            this.total = res.data.total;
                            this.currentPage = ajaxData.page_num;
                        }else{
                            this.tableData=[]
                            this.total=1
                            this.currentPage=1
                            this.$toast({ tips: res.data.message });
                        }
                        
                        if(! this.tableData || this.tableData.length == 0){
                            this.emptyText = '暫無數據';
                        }
                    }
                    this.loading = false;
                    
                })
            },
            handleCurrentChange (val) {
                this.searchFn(val);
            }
        }
    }
</script>

<style lang="scss" scoped>
    /deep/ .cell{
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        word-break: break-all;
    }
</style>
