<template>
    <div>
        <el-form :inline="true" class="demo-form-inline">
            <el-form-item>
                <el-button type="primary" @click="addFn()" v-if="isAuth('mechanical-right:category:list:add')">添加</el-button>
            </el-form-item>
        </el-form>
        <el-table
            :data="tableData"
            border
            stripe
            style="width: 100%"
            v-loading="loading"
            :empty-text="emptyText">
            <!-- <el-table-column
                prop="id"
                label="ID"
                width="180px">
            </el-table-column> -->
            <el-table-column
                prop="typeCode"
                label="Type Code">
            </el-table-column>
            <el-table-column
                prop="typeName"
                label="type name">
            </el-table-column>
            <el-table-column
                prop="createTime"
                label="Create Time">
            </el-table-column>
            <el-table-column
                label="operation"
                width="260px">
                <template slot-scope="scope">
                    <el-button type="text" size="small" @click="editFn(scope.row)" v-if="isAuth('mechanical-right:category:list:change')">編輯</el-button>
                    <el-button type="text" size="small" @click="deleteFn(scope.row, scope.$index)" v-if="isAuth('mechanical-right:category:list:del')">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :total="total" @current-change="searchFn" :current-page="currentPage">
        </el-pagination>
        <!-- 編輯、添加 -->
        <el-dialog :title="(opType == 'add' ? '添加' : '編輯')" :visible.sync="show" :close-on-click-modal="false">
            <el-form ref="editForm" :inline="true" :model="edit" label-width="120px" @keyup.enter.native="confirmEdit">
                <el-form-item label="Type Code" required>
                    <el-input type="text" v-model="edit.typeCode" :readonly="opType != 'add'" maxlength="4"></el-input>
                </el-form-item>
                <el-form-item label="Type Name" required>
                    <el-input type="text" v-model="edit.typeName"></el-input>
                </el-form-item>
                <div class="t-c">
                    <el-form-item>
                        <el-button type="primary" @click="confirmEdit">確定</el-button>
                    </el-form-item>
                </div>
            </el-form>
        </el-dialog>
    </div>
</template>

<script>
    export default {
        name: 'list',
        data () {
            return {
                tableData: [],
                total: 1,
                currentPage: 1,

                // dialog
                show: false,
                opType: 'add',
                edit: {
                    typeCode: '',
                    typeName: ''
                },
                loading: false,
                emptyText: '數據加載中',
            }
        },
        mounted () {
            this.searchFn();
        },
        methods: {
            searchFn(page) {
                let ajaxData = {};
                ajaxData.page_num = page ? page : 1;
                ajaxData.page_size = 10;
                this.loading = true;
                this.emptyText = '數據加載中';
                this.$http.get('/dist/mrd/listDistMrdScTypeWithPage', {params: ajaxData}).then( res => {
                    this.loading = false;
                    if(res.success){
                        this.tableData = res.data.data.list;
                        this.total = res.data.data.total;
                        this.currentPage = ajaxData.page_num;
                        if(! this.tableData || this.tableData.length == 0){
                            this.emptyText = '暫無數據';
                        }
                    }
                })
            },
            addFn(){
                this.opType = 'add';
                this.show = true;
                this.edit = {
                    typeCode: '',
                    typeName: ''
                };
            },
            editFn (row) {
                this.opType = 'edit';
                this.edit = row;
                this.show = true;
            },
            confirmEdit(){
                if(!this.edit.typeCode){
                    this.$toast({tips: 'Type Code不能為空'})
                    return
                }else if(!this.edit.typeName){
                    this.$toast({tips: 'Type Name不能為空'})
                    return
                }
                let ajaxData = this.edit;

                this.$http.post('/dist/mrd/saveDistMrdScType', ajaxData).then(res => {
                    if(res.success){
                        if(res.data.code && res.data.code != 200){
                            this.$toast({tips: res.data.message});
                        }else{
                            this.$toast({tips: (this.opType == 'add'? '添加' : '編輯') +'成功'});
                            this.show = false;
                            this.searchFn(1);
                        }
                    }
                })

            },
            deleteFn (row, index) {
                this.$msgbox.confirm(`確定進行[删除]操作?`, '提示', {
                    confirmButtonText: '確定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$http.delete('/dist/mrd/delDistMrdScType/' + row.id).then( res => {
                        if(res.success){
                            this.$toast({tips: '删除成功'})
                            this.tableData.splice(index, 1);
                            this.searchFn(this.currentPage)
                        }
                    })

                }).catch( () => {
                    
                })
            }
        }
        
    }
</script>
<style lang="scss" scoped>
/deep/ .el-dialog{
    width: 500px;
}
</style>

