<template>
  <div>
    <el-form
      :inline="true"
      :model="searchData"
      class="demo-form-inline search-list"
      @keyup.enter.native="selectWork()"
    >
      <el-form-item>
        <el-input
          type="number"
          v-model.number.trim="searchData.workNo"
          placeholder="Work No"
          style="width: 150px"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-input
          type="number"
          v-model.number.trim="searchData.soc"
          placeholder="Soc"
          style="width: 150px"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-input
          v-model.trim="searchData.titleType"
          placeholder="Title Type"
          style="width: 150px"
        ></el-input>
      </el-form-item>
      <el-form-item prop="">
        <el-input
          v-model.trim="searchData.title"
          placeholder="Title"
          style="width: 150px"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="selectWork()">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <span class="clear-search" @click="clearSearch()">清除搜索</span>
      </el-form-item>
    </el-form>
    <div style="">
      <el-tabs v-model="activeName" @tab-click="tabsChange()">
        <!-- Adapted/Arrange Work -->
        <el-tab-pane
          :label="
            AdaptedNum !== ''
              ? 'Adapted/Arrange Work(' + AdaptedNum + ')'
              : 'Adapted/Arrange Work'
          "
          name="first"
        >
          <el-table
            :data="AdaptedData"
            border
            @row-dblclick="rowClick"
            style="width: 100%"
            :empty-text="emptyText"
          >
            <el-table-column prop="workId" label="work No"> </el-table-column>
            <el-table-column prop="workSocietyCode" label="Soc">
            </el-table-column>
            <el-table-column prop="" label="Title">
              <template slot-scope="scope">
                <span :title="scope.row.title || scope.row.titleEn">{{
                  scope.row.title || scope.row.titleEn
                }}</span>
              </template>
            </el-table-column>
          </el-table>
          <!-- <el-pagination
                        background
                        layout="prev, pager, next"
                        :total='AdaptedTotal' @current-change="handleAdaptedChange">
                    </el-pagination> -->
        </el-tab-pane>

        <!-- AV Work -->
        <el-tab-pane
          :label="avWorkNum !== '' ? 'AV Work(' + avWorkNum + ')' : 'AV Work'"
          name="second"
        >
          <el-table
            :data="avWorkData"
            border
            @row-dblclick="rowClick"
            style="width: 100%"
            :empty-text="emptyText1"
          >
            <el-table-column prop="workId" label="work No"> </el-table-column>
            <el-table-column prop="workSocietyCode" label="Soc">
            </el-table-column>
            <el-table-column prop="" label="Title" width="400px">
              <template slot-scope="scope">
                <span :title="scope.row.title || scope.row.titleEn">{{
                  scope.row.title || scope.row.titleEn
                }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="genreCode" label="Genre"> </el-table-column>
            <el-table-column prop="durationM" label="Duration(M)">
            </el-table-column>
            <el-table-column prop="durationS" label="Duration(S)">
            </el-table-column>
            <el-table-column prop="usageType" label="Usage"> </el-table-column>
          </el-table>
          <!-- <el-pagination
                        background
                        layout="prev, pager, next"
                        :total='avWorkTotal' @current-change="handlereAvWorkChange">
                    </el-pagination> -->
        </el-tab-pane>
      </el-tabs>
    </div>
    <select-work
      v-if="workTableVisible"
      ref="selectWorkCom"
      :search="searchData"
      @checkWork="checkWork"
    ></select-work>
  </div>
</template>
<script>
import selectWork from "./components/selectWork";
import { Loading } from "element-ui";
export default {
  data() {
    return {
      activeName: "first",
      searchData: {
        workNo: "",
        soc: "",
        titleType: "",
        title: "",
      },
      oldsearchData: {},
      AdaptedData: [],
      avWorkData: [],
      AdaptedNum: "",
      avWorkNum: "",
      AdaptedTotal: 1,
      avWorkTotal: 1,
      workTableVisible: false,
      emptyText: "數據加載中",
      emptyText1: "數據加載中",
    };
  },
  components: {
    selectWork,
  },
  created() {
    console.log(this.$route.query);
    this.searchData.workNo = this.$route.query.workNo;
    this.searchData.soc = this.$route.query.soc;
    this.searchData.titleType = this.$route.query.titleType;
    this.searchData.title = this.$route.query.title;
    this.search(1);
  },
  methods: {
    tabsChange() {},
    selectWork() {
      this.workTableVisible = true;
      this.$nextTick(() => {
        this.$refs.selectWorkCom.init();
      });
    },
    checkWork(data) {
      console.log("data");
      console.log(data);
      this.searchData = {
        workNo: data.work_id,
        soc: data.work_society_code,
        titleType: data.title_type,
        title: data.title ? data.title : data.title_en,
      };
      this.search(1);
    },
    search(page) {
      this.loadingB = Loading.service({
        lock: true,
        text: "Loading",
        background: "rgba(0,0,0,0.3)",
      });
      let ajaxData = this.$utils.copy(this.searchData);
      // ajaxData.page_num = page ? page : 1;
      // ajaxData.page_size = 10;
      this.emptyText = "數據加載中";
      this.emptyText1 = "數據加載中";
      this.$http
        .get("/wrk/getAdArOrAvInfo", { params: ajaxData })
        .then((res) => {
          if (res.success) {
            this.loadingB.close();
            if (res.status === 200) {
              console.log(res);
              console.log(res.data.data);
              this.AdaptedData = res.data.data.AA;
              this.avWorkData = res.data.data.AV;
              this.AdaptedNum = this.AdaptedData.length;
              this.avWorkNum = this.avWorkData.length;
              if (!this.AdaptedNum || this.AdaptedNum.length == 0) {
                this.emptyText = "暫無數據";
              }
              if (!this.avWorkNum || this.avWorkNum.length == 0) {
                this.emptyText1 = "暫無數據";
              }
            }
          }
        });
    },
    clearSearch() {
      this.searchData = {
        workNo: "",
        soc: "",
        titleType: "",
        title: "",
      };
      this.AdaptedData = [];
      this.avWorkData = [];
      this.AdaptedNum = "";
      this.avWorkNum = "";
    },
    handleAdaptedChange() {},
    handlereAvWorkChange() {},
    rowClick(item) {
      let routeName = "";
      if (item.workType === "ARR") {
        routeName = "works-baseinfoarr2";
      } else if (item.workType === "ORG") {
        routeName = "works-baseinfo2";
      } else if (item.workType === "ADP") {
        routeName = "works-baseinfoadp2";
      } else if (item.workType === "AV" && item.genre_code != "TVS") {
        routeName = "works-baseinfoav2";
      } else if (
        (item.workType === "AV" && item.genre_code === "TVS") ||
        item.workType == "TV"
      ) {
        routeName = "works-baseinfotv2";
      } else if (item.workType == "ME") {
        routeName = "works-medleyinfo";
      } else {
        this.$message({
          message: 'workType為空，無法跳轉！',
          type: 'warning'
        })
        return 
      }
      this.$router.push({
        name: routeName,
        query: {
          id: item.workId,
          socId: item.workSocietyCode,
          title: item.title ? item.title : item.titleEn,
          nameId: item.workId + item.workSocietyCode,
          type: 'look',
        },
        params: { id: item.work_id },
      });
    },
  },
};
</script>
<style scoped>
</style>