<template>
  <div>
    <el-form :inline="true" :model="search" ref="form" class="demo-form-inline"  @keyup.enter.native='searchFn()'>
      <el-form-item style="width:100px">
        <!-- <el-input v-model="search.sourceWorkCode" placeholder="source work no"></el-input> -->
        <!-- <el-input v-model="fid" placeholder="FID" @blur="getFileInfo"></el-input> -->
        <el-input v-model="search.fileBaseId" placeholder="FID"></el-input>
      </el-form-item>
      <el-form-item style="width:120px">
        <el-input v-model="search.remitSociety" placeholder="remit soc"></el-input>
      </el-form-item>

      <el-form-item style="width:120px">
        <el-input v-model="search.originalTitle" placeholder="title"></el-input>
      </el-form-item>
      <el-form-item style="width:120px">
        <el-select v-model="search.status" placeholder="status">
          <el-option label="全部" value=""></el-option>
          <el-option label="待匹配" value="0"></el-option>
          <el-option label="已匹配" value="1"></el-option>
          <el-option label="不匹配" value="2"></el-option>
          <el-option label="IP匹配完毕" value="3"></el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item>
                <el-input v-model="search.remitSociety" placeholder="remitSociety"></el-input>
            </el-form-item> -->
      <el-form-item>
        <el-input v-model="search.sourceWorkCode" placeholder="SourceWorkNo"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="searchFn()">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="bindWork">指定作品</el-button>
      </el-form-item>
      <el-form-item>
        <span class="clear-search" @click="clearSearch()">清除搜索</span>
      </el-form-item>
    </el-form>
    <div style="margin-top:-10px">DataUniqueKey:
      <!-- <el-input v-model="search.dataUniqueKey" placeholder="DataUniqueKey" style="text-overflow:ellipsis;display:inline-block;overflow: hidden;
text-overflow: -o-ellipsis-lastline;width:330px;white-space:nowrap"></el-input> -->
      {{search.dataUniqueKey}}
    </div>
    <el-dialog title="指定作品" :visible.sync="selectWorkfix.selectWorkShow" :close-on-click-modal="false">
      <div style="width: 100%; margin: auto; margin-bottom: 20px">
        <el-input   @keyup.enter.native='onSubmitdialog()' style="width: 140px" placeholder="Title" v-model="selectWorkfix.title"></el-input>
        <el-input   @keyup.enter.native='onSubmitdialog()' style="width: 140px" placeholder="Work No" v-model="selectWorkfix.workId"></el-input>
        <el-input   @keyup.enter.native='onSubmitdialog()' style="width: 60px" placeholder="Soc" v-model="selectWorkfix.soc"></el-input>
<!--        <el-input   @keyup.enter.native='onSubmitdialog()' style="width: 140px" placeholder="Author" v-model="selectWorkfix.author"></el-input>-->
        <el-input   @keyup.enter.native='onSubmitdialog()' style="width: 200px" placeholder="Author or Composer" v-model="selectWorkfix.authorName"></el-input>
        <el-input   @keyup.enter.native='onSubmitdialog()' style="width: 140px" placeholder="ArtistName" v-model="selectWorkfix.artistName"></el-input>
        <el-button slot="append" icon="el-icon-search" @click="onSubmitdialog()"></el-button>
        <span class="clear-search" @click="clearSearchslect()">清除搜索</span>
      </div>
      <el-table :empty-text="tableresult" :data="tableData" @row-dblclick="getCurrentWorkInfo" @row-click="highlightDialogRow" highlight-current-row ref="dialogTableRef">
        <el-table-column property="title" label="title">
          <template slot-scope="scope">
            <span class="over-line" :title="scope.row.title || scope.row.title_en">{{ scope.row.title || scope.row.title_en }}</span>
          </template>
        </el-table-column>
        <el-table-column property="work_id" label="workId"></el-table-column>
        <el-table-column property="work_society_code" label="workSoc"></el-table-column>
        <el-table-column property="genre_code" label="Genre"></el-table-column>
        <el-table-column property="author" label="author"></el-table-column>
        <el-table-column property="genre_code" label="Composer">
          <template slot-scope="scope">
            <span class="over-line" :title="scope.row.composer && scope.row.composer.join('、')">{{ scope.row.composer && scope.row.composer.join("、") }}</span>
          </template>
        </el-table-column>
        <el-table-column label="operation">
          <template slot-scope="scope">
            <span style="
                width: 100%;
                display: inline-block;
                text-align: center;
                cursor: pointer;
              " title="指定作品">
              <i class="el-icon-check" @click="checkedWorkfix(scope.$index, scope.row)"></i>
            </span>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background layout="prev, pager, next" :total="total" @current-change="onSubmitdialog">
      </el-pagination>
    </el-dialog>
    <!-- <el-dialog :visible.sync="selectWorkfix.selectWorkShow" :close-on-click-modal="false">
      <div style="width: 100%; margin: auto; margin-bottom: 20px">
        <el-input style="width: 140px" placeholder="Title" v-model="selectWorkfix.title"></el-input>
        <el-input style="width: 140px" placeholder="Work No" v-model="selectWorkfix.workId"></el-input>
        <el-input style="width: 60px" placeholder="Soc" v-model="selectWorkfix.soc"></el-input>
        <el-input style="width: 140px" placeholder="Author" v-model="selectWorkfix.author"></el-input>
        <el-input style="width: 140px" placeholder="Composer" v-model="selectWorkfix.composer"></el-input>
        <el-input style="width: 140px" placeholder="ArtistName" v-model="selectWorkfix.performer"></el-input>
        <el-button slot="append" icon="el-icon-search" @click="onSubmitdialog()"></el-button>
      </div>
      <el-table :empty-text="tableresult"   :data="tableData">
        <el-table-column property="title" label="title">
          <template slot-scope="scope">
            <span class="over-line" :title="scope.row.title || scope.row.title_en">{{ scope.row.title || scope.row.title_en }}</span>
          </template>
        </el-table-column>
        <el-table-column property="work_id" label="workId"></el-table-column>
        <el-table-column property="work_society_code" label="workSoc"></el-table-column>
        <el-table-column property="genre_code" label="Genre"></el-table-column>
        <el-table-column property="author" label="author"></el-table-column>
        <el-table-column property="genre_code" label="Composer">
          <template slot-scope="scope">
            <span class="over-line" :title="scope.row.composer && scope.row.composer.join('、')">{{ scope.row.composer && scope.row.composer.join("、") }}</span>
          </template>
        </el-table-column>
        <el-table-column label="operation">
          <template slot-scope="scope">
            <span style="
                width: 100%;
                display: inline-block;
                text-align: center;
                cursor: pointer;
              " title="指定作品">
              <i class="el-icon-check" @click="checkedWorkfix(scope.$index, scope.row)"></i>
            </span>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background layout="prev, pager, next" :total="total" @current-change="onSubmitdialog">
      </el-pagination>
    </el-dialog> -->
    <el-table :empty-text="tableresult" :data="sampleDetailList" @selection-change="handleSelectionChange" @row-click="highlightMainRow" highlight-current-row ref="mainTableRef" border>
      <el-table-column type="selection"></el-table-column>
      <el-table-column prop="fileBaseId" width="60px" label="FID"></el-table-column>
      <el-table-column prop="originalTitle" label="Title"></el-table-column>
      <el-table-column prop="remitDistNo" label="RemitDistNo"></el-table-column>
      <el-table-column prop="remitSociety" label="RemitSoc"></el-table-column>
      <el-table-column prop="sourceWorkCode" label="SourceWorkNo" width="120"></el-table-column>
      <!-- <el-table-column prop="matchWorkSocietyCode" label="WorkSoc"></el-table-column> -->
      <!-- <el-table-column prop="matchWorkTitle" label="WorkTitle"></el-table-column> -->
      <el-table-column prop="authorComposer" label="authorComposer"></el-table-column>
      <!-- <el-table-column prop="authorName" label="Author"></el-table-column> -->
      <el-table-column prop="iswc" label="ISWC"></el-table-column>
      <el-table-column prop="artistName" label="Performer"></el-table-column>
      <el-table-column prop="isrc" label="ISRC"></el-table-column>
      <el-table-column prop="matchWorkId" label="MatchWorkId"></el-table-column>
      <el-table-column prop="matchWorkSocietyCode" label="MatchSoc"></el-table-column>
      <!-- <el-table-column prop="matchWorkId" label=""></el-table-column> -->
      <el-table-column prop="matchWorkTitle" label="MatchTitle"></el-table-column>
      <el-table-column prop="matchScore" label="MatchScore"></el-table-column>

    </el-table>
    <el-pagination background layout="prev, pager, next" :total="totallist" :current-page="search.page_num" @current-change="handleCurrentChange">
    </el-pagination>
    <!-- 指定作品 查找作品 弹框 -->
    <!-- 輸入查詢条件，查詢結果在match work info 列表里展示 -->
    <el-dialog :visible.sync="selectWorkShow" :close-on-click-modal="false">
      <div style="width: 600px;margin: auto;margin-bottom: 20px">
        <el-input   @keyup.enter.native='onSubmit()' style="width: 140px" placeholder="Work No" v-model='params.workId'></el-input>
        <el-input   @keyup.enter.native='onSubmit()' style="width: 60px" placeholder="Soc" v-model='params.soc'></el-input>
        <el-input   @keyup.enter.native='onSubmit()' style="width: 240px" placeholder="Title" v-model='params.title'></el-input>
        <el-button slot="append" icon="el-icon-search" @click="onSubmit()"></el-button>
      </div>
      <el-table :empty-text="tableresult"   :data="tableData">
        <el-table-column property="title" label="title">
          <template slot-scope="scope">
            <span class="over-line" :title="scope.row.title || scope.row.title_en">{{scope.row.title || scope.row.title_en}}</span>
          </template>
        </el-table-column>
        <el-table-column property="work_id" label="workId" width="120"></el-table-column>
        <el-table-column property="work_society_code" label="workSoc" width="90"></el-table-column>
        <el-table-column property="genre_code" label="Genre" width="80"></el-table-column>
        <el-table-column property="genre_code" label="Composer" width="150">
          <template slot-scope="scope">
            <span class="over-line" :title="scope.row.composer && scope.row.composer.join('、')">{{scope.row.composer && scope.row.composer.join('、')}}</span>
          </template>
        </el-table-column>
        <el-table-column label="operation" width="100">
          <template slot-scope="scope">
            <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer" title="指定作品">
              <i class="el-icon-check" @click="checkedWork(scope.row)"></i>
            </span>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background layout="prev, pager, next" :total="total1" @current-change="onSubmit">
      </el-pagination>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "sample-detail",
  data() {
    return {
      selectWorkfix: {
        workId: "",
        soc: "",
        title: "",
        authorName: "",
        composer: "",
        performer: "",
        selectWorkShow: false,
        list: [],
      },
      selectWorkTitleId: "",
      sampleDetailList: [],
      total: 0,
      totallist: 0,
      search: {
        page_num: 1,
        page_size: 10
      },
      selectWork: [],
      selectWorkShow: false,
      tableData: [],tableresult:' ',
      total1: 0,
      params: {
        workId: '',
        soc: '',
        title: ''
      }
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    // 高亮弹窗中的表格行
    highlightDialogRow(row) {
      // 设置当前高亮行
      this.$refs.dialogTableRef.setCurrentRow(row);
    },

    // 高亮主表格行
    highlightMainRow(row) {
      // 设置当前高亮行
      this.$refs.mainTableRef.setCurrentRow(row);
    },

    getCurrentWorkInfo(row) {
      console.log("%%%%%", row)
      let routeName = '';
      if (row.work_type === 'ARR') {
        routeName = 'works-baseinfoarr';
      } else if (row.work_type === 'ORG') {
        routeName = 'works-baseinfo';
      } else if (row.work_type === 'ADP') {
        routeName = 'works-baseinfoadp';
      } else if (row.work_type === 'AV' && row.genre_code != 'TVS') {
        routeName = 'works-baseinfoav';
      } else if ((row.work_type === 'AV' && row.genre_code === 'TVS') || row.work_type == 'TV') {
        routeName = 'works-baseinfotv';
      } else if (row.work_type == 'ME') {
        routeName = 'works-medleyinfo'
      }
      // r.push({name: routeName, query: {id: row.work_id, socId: row.work_society_code, title: row.title ? row.title : row.title_en, nameId: row.id+row.work_id}, params: {id: row.work_id}})
      //                         let {href} = this.$router.resolve({name:'login'})
      let { href } = this.$router.resolve({ name: routeName, query: { id: row.work_id, socId: row.work_society_code, title: row.title ? row.title : row.title_en, nameId: row.id + row.work_id }, params: { id: row.work_id } })
      window.open(href, '_blank')
    },
    clearSearch() {
      this.search = {
        page_num: 1,
        page_size: 10
      }
      this.init()
    },
    clearSearchslect() {
      for (let item in this.selectWorkfix) {
        this.selectWorkfix[item] = ''
      }
      this.onSubmitdialog()
    },
    init() {
      let work = JSON.parse(this.$route.query.work)

      console.log('&&&&&&&&', work)
      this.$set(this.search, 'dataUniqueKey', work.dataUniqueKey)
      // this.$set(this.search,'fileBaseId',work.fileBaseId)
      // this.$set(this.search,'originalTitle',work.originalTitle)
      // this.$set(this.search,'remitSociety',work.remitSociety)
      // this.$set(this.search,'sourceWorkCode',work.sourceWorkCode)
      // this.$set(this.search,'status',work.status.toString())
      console.warn("search", this.search)
      // this.$nextTick()
      this.$nextTick(() => {
        this.searchFn()
      })
    },
    searchFn(page = 1) {
      if (page == 1) {
        this.total = 0
      }
      this.search.page_num = page
      // this.dataUniqueKey=''
            this.tableresult = '數據加載中...'
      this.$http.get('/listmatchoverseas/getUnCheckedListMatchDataOverseasList', { params: this.search }).then(res => {
        console.log(res)
        if (res.success) {
          this.sampleDetailList = res.data.data.list
          this.totallist = res.data.data.total
        }
            this.tableresult = this.sampleDetailList.length == 0 ? '暫無數據' : ' '
      })
    },
    handleCurrentChange(page) {
      this.search.page_num=page
      this.searchFn(page)
    },
    checkedWorkfix(index, row) {
      //   console.warn('----------------',row,index);
      // 设置当前高亮行
      this.$refs.dialogTableRef.setCurrentRow(row);

      this.$msgbox
        .confirm("確定指定此作品?", "提示", {
          confirmButtonText: "確定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(() => {

          let ajaxData = {};
          ajaxData.listMatchDataOverseas = this.$utils.copy(this.selectWork);
          ajaxData.status = 1
          ajaxData.workTitleId = row.id
          this.$http.post('/listmatchoverseas/checkSpecialOverseasList', ajaxData).then(res => {
            // this.selectWorkShow = true
            console.warn("%%%%", res)
            if (res.success) {
              this.$toast({ tips: '審核成功' })
              this.selectWorkfix.selectWorkShow = false
              this.searchFn()
            } else {
              this.$toast({ tips: res.data.message })
            }
          })



          //   this.ignore = false;
          //   this.selectWorkTitleId = row.id;
          //   this.submitFn(1);
          //   this.selectWork.selectWorkShow = false;
          //   let matchComposers = [];
          //   let matchAuthors = [];
          //   row.composer &&
          //     row.composer.map((item) => {
          //       item && matchComposers.push(item);
          //     });
          //   row.author &&
          //     row.author.map((item) => {
          //       item && matchAuthors.push(item);
          //     });
          //   let data = {
          //     matchWorkId: row.work_id,
          //     matchWorkSocietyCode: row.work_society_code,
          //     matchTitle: row.title_en,
          //     matchTitleId: row.id,
          //     matchAuthors: <AUTHORS>
          //     matchComposers: matchComposers.join(";"),
          //     matchIswc: row.iswc,
          //   };
          //   this.matchTable = [data];
          //   this.changeWork(data);
        })
        .catch(() => { });
    },
    //指定作品
    bindWork() {
      if (this.selectWork.length < 1) {
        this.$toast({ tips: '请先选定数据' })
        return
      } else {
        this.tableresult='暫無數據'
        this.selectWorkfix.selectWorkShow = true
        // this.onSubmitdialog()
        //    console.log("======row",row)
        //     let ajaxData = {};
        //         ajaxData.listMatchDataOverseas= this.$utils.copy(this.selectWork);
        //         ajaxData.status=''
        //         ajaxData.workTitleId =''
        //  this.$http.post('/listmatchoverseas/checkSpecialOverseasList',params).then(res => {
        //     this.selectWorkShow = true
        //     if(res.success){
        //         this.$toast({tips:'審核成功'})
        //     }else{
        //         this.$toast({tips:res.data.message})
        //     }
        // })

      }
      /*this.params.workId = this.selectWork[0].matchWorkId
      this.params.soc = this.selectWork[0].matchWorkSocietyCode
      this.params.title = this.selectWork[0].matchWorkTitle*/

    },
    checkedWork(row) {
      this.$msgbox.confirm('確定指定此作品?', '提示', {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.selectWorkShow = false
        this.bindWorkWithList(row.id)

      }).catch(() => {
      });
    },
    bindWorkWithList(id) {
      let params = {
        status: 1,
        workTitleId: id,
        listMatchDataOverseas: this.selectWork
      }
      this.$http.post('/listmatchoverseas/checkSpecialOverseasList', params).then(res => {
        this.selectWorkShow = true
        if (res.success) {
          this.$toast({ tips: '審核成功' })
        } else {
          this.$toast({ tips: res.data.message })
        }
      })
    },
    onSubmitdialog(page = 1) {

      let params = this.selectWorkfix;
      params.page = {
        pageNum: page ? page : 1,
        pageSize: 10,
      };
            this.tableresult = '數據加載中...'
      this.$http.post("/wrk/queryWrkWorkListEs", params).then((res) => {
        if (res.success) {
          this.tableData = res.data.list;
          this.total = res.data.total;
        }
            this.tableresult = this.tableData.length == 0 ? '暫無數據' : ' '
      });
    },
    onSubmit(page = 1) {

      this.params.page = {
        pageNum: page,
        pageSize: 10
      }
      this.$http.post('/wrk/queryWrkWorkListEs', this.params).then(res => {
        if (res.success) {
          this.tableData = res.data.list;
          this.total1 = res.data.total;
        }
      })
    },

    handleSelectionChange(selection) {
      this.selectWork = selection
      // console.log('xuanzhognde++++++++',this.selectWork);
    }
  },
  // beforeRouteEnter(to, from, next) {
  //   next((vm) => {
  //     console.log("*****", vm);
  //     // vm.search.dataUniqueKey = JSON.parse(this.$route.query.work).dataUniqueKey
  //     vm.init()

  //   });
  // },
}
</script>

<style scoped>
/* 表格高亮行样式 */
/deep/ .el-table__body tr.current-row > td {
  background-color: #17b3a3 !important;
  color: #fff !important;
}
</style>
