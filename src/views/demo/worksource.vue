<template>
    <div>
        <div class="boxline">
            <div class="add-new">
                <el-button type="primary" v-if="isAuth('works:base:change')" @click="adddata3()">新 增</el-button>
            </div>
            <el-form ref="formTable" :model="tableForm" style="padding-top:50px">
                <el-table
                    :data="tableForm.tableData3"
                    border
                    stripe
                    style="width: 100%"
                    :header-cell-class-name="must">
                    <el-table-column
                        label="sourceType" class="must"
                        width="150px">
                        <template slot-scope="scope">
                            <el-form-item :prop="`tableData3.${scope.$index}.sourceType`" :rules="tableRules.sourceType">
                                <el-input :ref="'type' + scope.$index" v-model='scope.row.sourceType' @blur='verifyType(scope.row)' @keyup.native="sourceTypeChange(scope.$index,scope.row.sourceType)" placeholder="雙擊查詢" @dblclick.native="getSourceType(scope.$index, scope.row)"></el-input>
                            </el-form-item>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="notifySouceId"
                        label="notify_souce_id"
                        width="180px">
                        <template slot-scope="scope">
                            <el-form-item :prop="`tableData3.${scope.$index}.notifySouceId`" :rules="tableRules.notifySouceId">
                                <el-input v-model=scope.row.notifySouceId placeholder="notify_souce_id" @input="validate" @change="souceIdChange(scope.$index, scope.row.notifySouceId)"></el-input>
                            </el-form-item>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="name"
                        label="Name">
                        <template slot-scope="scope">
                            <el-form-item :prop="`tableData3.${scope.$index}.name`" :rules="tableRules.name">
                                <el-input :ref="'name'+scope.$index" v-model=scope.row.name placeholder="Name" @input="validate"></el-input>
                            </el-form-item>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="notifyDate"
                        label="nd"
                        width="140px">
                        <template slot-scope="scope">
                            <el-form-item :prop="`tableData3.${scope.$index}.notifyDate`" :rules="tableRules.notifyDate">
                                <!-- <el-input v-model=scope.row.notifyDate placeholder="nd"></el-input> -->
                                <!-- <el-input type="text" v-model="scope.row.notifyDate" @input="validate"  v-dateFmt></el-input> -->
                                <el-input v-model="scope.row.notifyDate"  value-format="yyyy-MM-dd" format="yyyyMMdd" @input="validate"></el-input>
                            </el-form-item>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="inputSoc"
                        label="inputSoc"
                        width="140px">
                        <template slot-scope="scope">
                            <el-form-item :prop="`tableData3.${scope.$index}.inputSoc`" :rules="tableRules.inputSoc">
                                <el-input v-model="scope.row.inputSoc" readonly></el-input>
                            </el-form-item>
                        </template>
                    </el-table-column>
                    <el-table-column
                        label="operation"
                        width="100">
                        <template slot-scope="scope">
                            <span class="spanright">
                                <i class="el-icon-delete" v-if="isAuth('works:base:change')" @click="deletedata3(scope.$index)"></i>
                            </span>
                        </template>
                    </el-table-column>
                </el-table>
            </el-form>
        </div>
        <el-dialog :visible.sync="sourceTypeShow" title="選擇SourceType" :close-on-click-modal="false">
            <div style="width: 500px;margin: auto;margin-bottom: 20px">
                <el-input placeholder="請輸入type" @keyup.enter.native="typeSearch()" v-model="typeInput" class="input-with-select">
                    <el-button slot="append" icon="el-icon-search" @click="typeSearch()"></el-button>
                </el-input>
            </div>
            <el-table :empty-text="emptyText" stripe :data="typeData">
                <el-table-column label="type">
                    <template slot-scope="scope">
                        {{scope.row.sourceType.toUpperCase()}}
                    </template>
                </el-table-column>
                <el-table-column property="description" label="描述" ></el-table-column>
                <el-table-column
                    label="operation"
                    width="100">
                    <template slot-scope="scope">
                        <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                            <i class="el-icon-check" v-if="isAuth('works:base:change')" @click="checkedType(scope.$index,scope.row)"></i>
                        </span>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                background
                layout="prev, pager, next"
                :total=typeTotal @current-change="typeSearch">
            </el-pagination>
        </el-dialog>
    </div>
</template>

<script>
    import qs from 'qs'
    export default {
        name: 'worksource',
        props: {
            tableData: {
                type: Array,
                default: () => []
            }
        },
        data () {
            var validateNameInput = (rule, value, callback) => {
                if (!value) {
                    return callback(new Error('此項必填！'))
                } else {
                    callback()
                }
            }
            var validateTypeInput = (rule, value, callback) => {
                if (!value) {
                    return callback(new Error('此項必填！'))
                } else {
                    callback() 
                }
            }
            var validateIdInput = (rule, value, callback) => {
                if (!value) {
                    return callback(new Error('此項必填！'))
                } else {
                    callback()
                }
            }
            var validateDateInput = (rule, value, callback) => {
                if (!value) {
                    return callback(new Error('此項必填！'))
                } else {
                    callback()
                }
            }
            var validateSocInput = (rule, value, callback) => {
                if (!value) {
                    return callback(new Error('此項必填！'))
                } else {
                    callback()
                }
            }
            return {
                tableForm: {tableData3: [
                    // { sourceType: '', notifySouceId: '', source: '', name: '', notifyDate: '', inputSoc: '161' }
                ]},
                tableRules: {
                    sourceType: [{ validator: validateTypeInput, trigger: 'blur' }],
                    notifySouceId: [{ validator: validateIdInput, trigger: 'blur' }],
                    name: [{ validator: validateNameInput, trigger: 'blur' }],
                    notifyDate: [{ validator: validateDateInput, trigger: 'blur' }]
                },
                qingkong:true,
                typeIndex: '',
                sourceIndex: '',
                typeTotal: 10,
                typeInput: '',
                emptyText: '暫無數據',
                sourceTypeShow: false,
                typeData: [],
                validateFlag: false,
                pageInfo: {
                    page: {
                        pageNum: 0,
                        pageSize: 10
                    }
                }
            }
        },
        watch: {
            'tableForm': function(val, oldVal){
                this.validate();
            }
        },
        methods: {
            sourceTypeChange(index,data){
                // data = data.toUpperCase()
                this.$set(this.tableForm.tableData3[index], 'sourceType', data.toUpperCase());
            },
            must(obj){
                if(obj.columnIndex != 5)
                return 'must';
            },
            //   source 的選擇列表 待跟建新確認後，补充
            // getSource (index, item) {
            //     // return;
            //     this.sourceIndex = index
            //     let params = {
            //         source: item.notifySouceId
            //     }
            //     this.$http.get('/wrk/getNameBySource', {params}).then(res => {
            //         if (res.status === 200) {
            //             this.$nextTick(() => {
            //                 this.tableForm.tableData3[index].name = res.data;
            //             })
            //         }
            //     })
            // },
            adddata3 () {
                let myArray = [{ type: '', source: '', name: '', nd: '', inputSoc: '161' }];
                this.tableForm.tableData3 = [...this.tableForm.tableData3, ...myArray];
                this.$nextTick( () => {
                    this.validate();
                })

            },
            deletedata3 (index) {
                this.$msgbox.confirm(`確定進行[删除]操作?`, '提示', {
                    confirmButtonText: '確定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.tableForm.tableData3.splice(index, 1);
                    this.$nextTick( () => {
                        this.validate();
                    })
                })
            },
            getSourceType (index, item) {
                this.typeIndex = index;
                this.sourceTypeShow = true;
                this.typeInput = item.sourceType;
                this.typeSearch(1);
            },
            typeSearch(val) {
                this.sourceTypeShow = true;
                let data = {
                    page: {
                        pageNum: val ? val :1,
                        pageSize: '10'
                    },
                    sourceType: this.typeInput
                }
                this.emptyText = '數據加載中';
                this.$http.post('/ref/getRefWrkSourceType', data).then(res => {
                    if (res.status === 200) {
                        this.typeData = res.data.list;
                        this.typeTotal = res.data.total;
                        if(! this.typeData || this.typeData.length == 0){
                          this.emptyText = '暫無數據';
                        }
                    }else{
                        this.emptyText = '暫無數據';
                    }
                })
            },
            checkedType (index, item) {
                this.sourceTypeShow = false;
                this.$set(this.tableForm.tableData3[this.typeIndex], 'sourceType', item.sourceType.toUpperCase());
                this.$refs['type'+this.typeIndex].focus();
                this.$refs['type'+this.typeIndex].blur();
                this.$nextTick( () => {
                    this.validate();
                })
            },
            validate(){
                this.$refs.formTable.validate( ( valid) => {
                    if(valid){
                        this.validateFlag = true;
                    }else{
                        this.validateFlag = false;
                    }
                })
            },
            souceIdChange(index, value){
                value = value + '';
                // value = value.replace(/\D/g, '')
                this.tableForm.tableData3[index].notifySouceId = value;
                if(value && value.length <= 3){
                    // 查询协会
                    this.$http.get('/ref/society/getSocietyBySocietyCode', {params: {societyCode: value}}).then( res => {
                        if(res.success && res.data.code == 200){
                            if(res.data.data.refSociety){
                                this.tableForm.tableData3[index].name = res.data.data.refSociety.societyName
                                this.$refs['name'+index].focus();
                                this.$refs['name'+index].blur();
                            }else{
                                this.$toast({tips: '未查詢到該協會'});
                                this.tableForm.tableData3[index].name = '';
                                this.$refs['name'+index].focus();
                                this.$refs['name'+index].blur();
                            }
                        }
                        this.validate();
                    })
                }else if(value && value.length > 3){
                    // 查询ip
                    let formData = qs.stringify({name_no: value, page_num: 1, page_size: 10});
                    let config = {headers: { 'content-type': 'application/x-www-form-urlencoded' }}
                    this.$http.post('/ip/name/es', formData, config).then(res => {
                        if (res.status === 200) {
                            if(res.data.list.length > 0){
                                this.tableForm.tableData3[index].name = res.data.list[0].chinese_name ? res.data.list[0].chinese_name : res.data.list[0].name;
                                this.$refs['name'+index].focus();
                                this.$refs['name'+index].blur();
                            }else{
                                this.$toast({tips: '未查詢到該會員'});
                                this.tableForm.tableData3[index].name = '';
                                this.$refs['name'+index].focus();
                                this.$refs['name'+index].blur();
                            }
                        }
                        this.validate();
                    })
                }

            },
            getData(){
                if(this.tableForm.tableData3.length == 0){
                    this.$toast({tips: '請至少填寫一條source'})
                    return [];
                }
                if(this.validateFlag){
                    // console.log(this.tableForm.tableData3);
                    // if(this.tableForm.tableData3.length == 0){
                    //     this.$toast({tips: '請至少填寫一條source'})
                    //     return [];
                    // }else{
                        return this.tableForm.tableData3;
                    // }
                }else{
                    this.$toast({tips: 'Source中有必填項未填寫'})
                    return [];
                }

            },
            verifyType(item){
                let data = {
                    sourceType: item.sourceType
                }
                this.$http.post('/ref/getRefWrkSourceType', data).then(res => {
                    if (res.status === 200) {
                        if(res.data.list.length < 1){
                            
                            this.$alert('暫無此Type！')
                            item.sourceType = ""
                        }
                    }
                })
            }
        },
        watch: {
            tableData: function(){
                this.tableForm.tableData3 = this.$utils.copy(this.tableData);
                this.tableForm.tableData3.forEach(item=>item.sourceType=item.sourceType.toUpperCase())
                this.$nextTick( () => {
                    this.validate();
                })
            }
        },
        mounted () {
            this.tableForm.tableData3 = this.$utils.copy(this.tableData);
            console.log("this.tableForm.tableData3")
            console.log(this.tableForm.tableData3)
            this.tableForm.tableData3.forEach(item=>item.sourceType=item.sourceType.toUpperCase())
        }
    }
</script>

<style lang="scss" scoped>
@import "../../assets/scss/works.scss";
    /deep/ .el-table{
        max-height: 320px !important;
    }
    /deep/ .must .cell:after{
        content: ' *';
        color: red
    }
    /deep/ .el-form-item{
        margin-bottom: 0;
    }
    /deep/ .add-new{
        z-index: 100;
    }
</style>
