<template>
    <div>
        <el-form :inline="true" :model="searchForm" class="demo-form-inline" @keyup.enter.native="getSearchList(1)">
            <el-form-item>
                <el-input v-model="searchForm.sourceName" placeholder="source name"></el-input>
            </el-form-item>
            <el-form-item>
                <el-input v-model="searchForm.createUserName" placeholder="create user"></el-input>
            </el-form-item>
            <el-form-item>
                <date-picker type="date" v-model="searchForm.startTime" value-format="yyyy-MM-dd HH:mm:ss" format="yyyyMMdd" placeholder="開始日期" style="width: 160px;"></date-picker>
                -
                <date-picker type="date" v-model="searchForm.endTime" value-format="yyyy-MM-dd HH:mm:ss" format="yyyyMMdd" placeholder="結束日期" style="width: 160px;"></date-picker>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="getSearchList(1)">搜索</el-button>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="configAdd">新增</el-button>
            </el-form-item>
            <el-form-item>
                <span class="clear-search" @click="clearSearch()">清除搜索</span>
            </el-form-item>
        </el-form>
        <el-table
            :data="tableData"
            border
            stripe
            style="width: 100%"
            v-loading="loading"
            :empty-text="emptyText">
            <el-table-column
                prop="id"
                label="Source Id"
                width="130">
            </el-table-column>
            <el-table-column
                prop="sourceName"
                label="Name"
                width="160">
            </el-table-column>
            <el-table-column
                prop="sourceDesc"
                label="Desc">
            </el-table-column>
            <el-table-column
                prop="distSource"
                label="Dist Source">
            </el-table-column>
            <el-table-column
                prop="createUserName"
                label="Create User"
                width="120">
            </el-table-column>
            <el-table-column
                prop="createTime"
                label="Create Time"
                width="180">
                <template></template>
            </el-table-column>
            <el-table-column
                fixed="right"
                label="operation"
                width="100">
                <template slot-scope="scope">
                    <div class="option">
                        <span>
                        <el-button @click="handleClick(scope.row)" type="text" size="small">編輯</el-button>
                    </span>
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next" :current-page="currentPage"
            :total='total' @current-change="getSearchList">
        </el-pagination>
        <!--編輯弹框-->
        <el-dialog :title="dialogTitle" :visible.sync="isShow" width="40%" :show-close="false" :close-on-click-modal="false">
            <el-form
                ref="editForm"
                label-position="right"
                label-width="120px"
                :model="tableItem"
                class="demo-form-inline"
                 @keyup.enter.native="addFormItem"
            >
                <el-form-item
                    label="Name:"
                    prop="sourceName"
                    :rules="{required: true, message: '姓名不能為空', trigger: 'blur'}"
                >
                    <el-input v-model="tableItem.sourceName" :readonly="isEdit" placeholder="Name" label="Name"></el-input>
                </el-form-item>
                <el-form-item label="Desc:" prop="sourceDesc" :rules="{required: true, message: 'Desc不能為空', trigger: 'blur'}">
                    <el-input v-model="tableItem.sourceDesc" placeholder="Desc"></el-input>
                </el-form-item>
                <el-form-item label="Dist Source:">
                    <el-input v-model="tableItem.distSource" placeholder="Dist"></el-input>
                </el-form-item>
            </el-form>
            <div style="text-align: center">
                <el-button @click="cancelDialog">取消</el-button>
                <el-button type="primary" @click="addFormItem" :disabled="isDisabled">確定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
    export default {
        name: 'list-source',
        data () {
            return {
                loading: false,
                isDisabled: false,
                searchForm: {
                    createUserName: '',
                    sourceName: '',
                    startDay: '',
                    startHour: '',
                    endDay: '',
                    endHour: '',
                    startTime: '',
                    endTime: ''
                },
                tableData: [],
                tableItem: {
                    sourceName: '',
                    sourceDesc: '',
                    distSource: '',
                    id: ''
                },
                tableItemOrigin: {},
                isShow: false,
                dialogTitle: '新增',
                isEdit: false,
                total: 0,
                currentPage:1,
                id: 2,
                emptyText: '數據加載中',

            }
        },

        mounted () {
            this.getSearchList()
        },
        methods: {
            clearSearch () {
                this.searchForm = {
                    createUserName: '',
                    sourceName: '',
                    startDay: '',
                    startHour: '',
                    endDay: '',
                    endHour: '',
                    startTime: '',
                    endTime: ''
                }
                this.getSearchList()
            },
            handleCurrentChange (val) {
                // let pageNum = [...arguments][0]
                this.searchForm.page_num = val
                this.getSearchList()
            },
            getSearchList (num) {
                this.loading = true
                let endDate = this.searchForm.endTime
                if (endDate) {
                  this.searchForm.endTime = endDate.split(' ')[0] + ' 23:59:59'
                }
                let params = {
                    createUserName: this.searchForm.createUserName,
                    sourceName: this.searchForm.sourceName,
                    startTime: this.searchForm.startTime,
                    endTime: this.searchForm.endTime,
                    page_num: num?num:1
    
                }
                this.emptyText = '數據加載中';
                this.$http.get('/list/source', {params}).then(res => {
                    if (res.success) {
                        let list = res.data.data.list
                        this.total = res.data.data.total
                        this.tableData = list
                        if(! this.tableData || this.tableData.length == 0){
                            this.emptyText = '暫無數據';
                        }
                        this.currentPage = params.page_num
                    }
                    this.loading = false
                })
            },
            handleClick (row) {
                this.isShow = true
                this.dialogTitle = '編輯'
                this.isEdit = true
                this.tableItem = row
                this.tableItemOrigin = JSON.parse(JSON.stringify(row))
            },
            configAdd () {
                this.isShow = true
                this.dialogTitle = '新增'
                this.isEdit = false
                this.resetTableItem()
            },
            cancelDialog () {
                this.isShow = false
                this.tableData.map((item, index) => {
                    if (item.id === this.tableItemOrigin.id) {
                        this.tableData.splice(index, 1, this.tableItemOrigin)
                    }
                })
                this.resetTableItem()
                this.$refs['editForm'].clearValidate()
            },
            addFormItem () {
                this.isDisabled = true
                setTimeout(()=>{
                        this.isDisabled = false
                },3000)
                this.$refs['editForm'].validate((valid) => {
                    if (valid) {
                        this.addFormItemFn()
                    }
                })
            },
            addFormItemFn () {
                if (this.isEdit) {
                    this.isShow = false
                    this.$http.post('/list/source', this.tableItem).then(res => {
                        if (res.success) {
                            if (res.data.code == 200) {
                                this.getSearchList()
                                this.isShow = false
                            } else {
                                this.$toast({tips: res.data.message})
                            }
                        }
                    })
                } else {
                    this.$http.put('/list/source', this.tableItem).then(res => {
                        console.log(res)
                        if (res.success) {
                            if (res.data.code === 20003) {
                                this.$message('名稱已經存在了')
                            } else {
                                this.getSearchList()
                                this.isShow = false
                            }

                        }
                    })
                }
            },
            resetTableItem () {
                this.tableItem = {
                    name: '',
                    desc: '',
                    dist: ''
                }
            }
        }
    }
</script>

<style scoped>
    /deep/ .el-table .cell {
        text-align: center;
    }

    /deep/ .el-form-item__label {
        margin-left: 0 !important;
    }
</style>
