<template>
<div>
    <el-dialog class="detail" :visible.sync="show"  center top="10vh" :close-on-click-modal="false" :before-close='cancel'>
        <div class="boxline">
            <span class="mytitle" style="color: #333;font-size: 16px;margin-bottom: 16px;margin-left: 20px;display: inline-block;"> Recipient</span>
            <el-form :inline="true" :model="formRecipient" label-position="right" label-width="220px" class="demo-form-inline" ref="ruleForm" :rules="rules">

                <el-form-item label="Payment">
                    <el-select v-model="formRecipient.paymentMethod" placeholder="" @change="clearBank">
                        <el-option label="" value=""></el-option>
                        <el-option label="Auto-Transfer" value="A"></el-option>
                        <el-option label="Cheque" value="C"></el-option>
                        <el-option label="Telegraphic-Transfer" value="T"></el-option>
                        <el-option label="Draft" value="D"></el-option>
                        <el-option label="Not Privided" value="X"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="Tax Type">
                    <el-select v-model="formRecipient.ipType" placeholder="">
                        <el-option label="" value=""></el-option>
                        <el-option label="Legal Entity" value="le"></el-option>
                        <el-option label="Natural Person（Local）" value="NL"></el-option>
                        <el-option label="Natural Person（Overseas）" value="NO"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="Valid From" prop='validFrom'>
                    <!-- <el-input type="text" v-model="formRecipient.validFrom" v-dateFmt></el-input> -->
                    <date-picker v-model="formRecipient.validFrom" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd" @change="validFromChange"></date-picker>
                </el-form-item>
                <el-form-item label="Valid To">
                    <!-- <el-input type="text" v-model="formRecipient.validTo" v-dateFmt></el-input> -->
                    <date-picker v-model="formRecipient.validTo" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd"></date-picker>
                </el-form-item>
                <el-form-item label="Type">
                    <el-select v-model="formRecipient.recipientType" placeholder="Type">
                        <el-option label="" value=""></el-option>
                        <el-option label="Male Single" value="MS"></el-option>
                        <el-option label="Male Married" value="MM"></el-option>
                        <el-option label="Female Single" value="FS"></el-option>
                        <el-option label="Female Married" value="FM"></el-option>
                        <el-option label="Sole Proprietorship" value="S"></el-option>
                        <el-option label="Partnership" value="P"></el-option>
                        <el-option label="Individual" value="I"></el-option>
                        <el-option label="Corporation" value="C"></el-option>
                        <el-option label="No BR/ID Provided" value="X"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="City">
                    <el-input v-model="formRecipient.transferCity" placeholder=""></el-input>
                </el-form-item>
                <el-form-item label="Country">
                    <el-input v-model="formRecipient.country" placeholder="雙擊查詢" @dblclick.native="getCountry" clearable></el-input>
                    <!-- <el-input v-model="formRecipient.country" placeholder="雙擊查詢" @dblclick.native="getCountry" readonly style="width: 160px;"></el-input> -->
                </el-form-item>
                <el-form-item label="Currency">
                    <el-input v-model="formRecipient.currencyCode" placeholder="雙擊查詢" @dblclick.native="getCurrency" clearable></el-input>
                    <!-- <el-input v-model="formRecipient.currencyName" placeholder="雙擊查詢" @dblclick.native="getCurrency" readonly style="width: 160px;"></el-input> -->
                </el-form-item>
                <br/>
                <div>
                    <el-form-item label="ID No">
                        <el-input v-model="formRecipient.recipientHkId" placeholder=""></el-input>
                    </el-form-item>
                    <el-form-item label="BR No">
                        <el-input v-model="formRecipient.recipientBrNo" placeholder=""></el-input>
                    </el-form-item>
                </div>
                <el-form-item label="Bank No">
                    <el-input v-model="formRecipient.bankNo" placeholder="雙擊查詢" @dblclick.native="getBank()" clearable></el-input>
                </el-form-item>
                <el-form-item label="Bank Name">
                    <el-input v-model="formRecipient.bankName" placeholder="" @dblclick.native="getBank()" clearable></el-input>
                </el-form-item>
                <el-form-item label="Branch No">
                    <el-input v-model="formRecipient.bankBranchNo" placeholder="雙擊查詢" @dblclick.native="getBranch()" clearable></el-input>
                    <!-- <el-select v-model="formRecipient.bankBranchNo" @change="branchChange">
                        <el-option v-for="(item, index) in branchList" :key="index" :value='item.id' :label="item.id + ' ' + item.branchName"></el-option>
                    </el-select> -->
                </el-form-item>
                <el-form-item label="Branch Name">
                    <el-input v-model="formRecipient.bankBranchName" placeholder="" @dblclick.native="getBranch()" clearable></el-input>
                    <!-- <el-select v-model="formRecipient.bankBranchName">
                        <el-option v-for="(item, index) in branchList" :key="index" :value='item.branchName' :label="item.id"></el-option>
                    </el-select> -->
                </el-form-item>
                <el-form-item label="A/C No">
                    <el-input v-model="formRecipient.accountNo" placeholder=""></el-input>
                </el-form-item>
                <el-form-item label="A/C Name">
                    <el-input v-model="formRecipient.accountName" placeholder=""></el-input>
                </el-form-item>
                <el-form-item label="Description">
                    <el-input v-model="formRecipient.paymentDesc" placeholder=""></el-input>
                </el-form-item>
                 <el-form-item style="margin-left: 130px">
                    <el-checkbox v-model="formMemberDetail.specialExchangeRate">Special exchange rate</el-checkbox>
                </el-form-item>
                <el-form-item class="countryName" label="Country Name" v-if="formMemberDetail.specialExchangeRate">
                    <el-input v-model="formMemberDetail.specialExchangeCountryName" placeholder="" readonly @dblclick.native="getSocietyTaxRate()"></el-input>
                </el-form-item>
                <el-form-item label="AddressOfBank">
                    <el-input style="width: 620px" v-model="formRecipient.bankAddress" placeholder=""></el-input>
                </el-form-item>
            </el-form>
            <div class="mytitle second" style="color: #333;font-size: 16px;margin-bottom: 16px;">Bank Account Detail for Telegraphic Transfer</div>
            <el-form :inline="true" :model="mbrMemberBankAccDtl" label-position="right" label-width="220px" class="demo-form-inline">
                <el-form-item label="Account Name">
                    <el-input v-model="mbrMemberBankAccDtl.bankAccountName" placeholder=""></el-input>
                </el-form-item>
                <el-form-item label="Account No">
                    <el-input v-model="mbrMemberBankAccDtl.bankAccountNo" placeholder=""></el-input>
                </el-form-item>
                <el-form-item label="Name of Bank">
                    <el-input v-model="mbrMemberBankAccDtl.bankName" placeholder="" ></el-input>
                </el-form-item>
                <el-form-item label="Address of Bank">
                    <el-input v-model="mbrMemberBankAccDtl.bankAddress1" placeholder="" style="width: 200px"></el-input>
                    <el-input v-model="mbrMemberBankAccDtl.bankAddress2" placeholder="" style="width: 200px"></el-input>
                    <el-input v-model="mbrMemberBankAccDtl.bankAddress3" placeholder="" style="width: 200px"></el-input>
                </el-form-item>
            </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
            <el-button @click="cancel()">取 消</el-button>
                <el-button type="primary" @click="confirm()">確 定</el-button>
        </span>
    </el-dialog>
    <select-country v-if="countryShow" ref="selectCountry" :search="countrySearch" @checkCountry="checkCountry"></select-country>
    <select-currency v-if="currencyShow" ref="selectCurrency" :search="currencySearch" @checkCurrency="checkCurrency"></select-currency>
    <select-bank v-if="bankShow" ref="selectBank" :search="bankSearch" @checkBank="checkBank"></select-bank>
    <select-branch v-if="branchShow" ref="selectBranch" :search="branchSearch" @checkBranch="checkBranch"></select-branch>

</div>
</template>
<script>
    import selectCountry from '@/components/select-country'
    import selectCurrency from '@/components/select-currency'
    import selectBank from '@/components/select-bank'
    import selectBranch from '@/components/select-branch'

export default {
    props: {
        options: {
            type: Object,
            default: {
                index: 0,
                type: 'member'
            }
        },
        bankInfo: {
            type: Array,
            default: () => []
        },
        bankDtlsInfo: {
            type: Array,
            default: () => []
        },
        showEditBank: {
            type: Boolean,
            default: false
        }
    },
    data(){
        return {
            tableData: this.$utils.copy(this.bankInfo),
            dtlsList: this.$utils.copy(this.bankDtlsInfo),
            show: this.showEditBank,

            showEdit: false,
            formRecipient: {
            },
            mbrMemberBankAccDtl: {},
            editIndex: 0,

            countrySearch: {
                tisN: '',
                countryCode: ''
            },
            countryShow: false,
            
            currencySearch: {
                countryCode: '',
                currencyCode: ''
            },
            currencyShow: false,

            bankSearch: {
            },
            bankShow: false,
            bankType: '',
            
            branchSearch: {

            },
            branchShow: false,
            formMemberDetail:{},
            rules: {
                validFrom: { required: true, message: '請輸入Valid From', trigger: 'blur'}
            }
        }
    },
    components: {
        selectCountry,
        selectCurrency,
        selectBank,
        selectBranch
    },
    watch: {
        show(){
            this.$emit('change', {'name': 'showEditBank', 'value': this.show});
        },
        showEditBank(){
            this.show = this.showEditBank
        },
        bankInfo(){
            this.tableData = this.$utils.copy(this.bankInfo);
            // this.tableData.forEach( item => {
            //     item.mbrMemberBankAccDtl = item.mbrMemberBankAccDtl || {};
            // })
        },
        bankDtlsInfo(){
            this.dtlsList = this.$utils.copy(this.bankDtlsInfo);
        },
        'formMemberDetail.specialExchangeRate':function(status){
            this.$emit('changeSpecialExchangeRate', status);
        }
    },
    methods: {
        getSocietyTaxRate(){
            this.$emit('getSocietyTaxRate');
        },
        selectRate(row){
            this.formMemberDetail.specialExchangeCountryName = row.countryName;

        },
        /**/
        init(index,rows,type,data){
            this.formMemberDetail = this.$utils.copy(data);
            this.editBankType = type ? type : 'edit'; 
            this.editIndex = index;
            this.showEdit = true;
            this.formRecipient = this.$utils.copy(rows);
            let flag = false;
            this.dtlsList.forEach( (item, index) => {
                if(item.validFrom == this.formRecipient.validFrom && item.ipBaseNo == this.formRecipient.ipBaseNo){
                    flag = true;
                    this.mbrMemberBankAccDtl = this.dtlsList[index];
                }
            })
            if(!flag){
                this.mbrMemberBankAccDtl = {
                    ipBaseNo: this.formRecipient.ipBaseNo,
                    validFrom: this.formRecipient.validFrom
                };
                this.dtlsList.push(this.mbrMemberBankAccDtl);
            }

            // if( JSON.stringify(this.mbrMemberBankAccDtl) === '{}'){

            // }
            
            // this.$set(this.formRecipient , 'mbrMemberBankAccDtl', this.formRecipient.mbrMemberBankAccDtl ? this.formRecipient.mbrMemberBankAccDtl : {})
            if(type != 'add'){
                if(this.formRecipient.bankNo){
                    let no1 = '';
                    for(let i =0; i<3-(this.formRecipient.bankNo + '').length;i++){
                        no1 += '0';
                    }
                    no1 += this.formRecipient.bankNo;
                    this.formRecipient.bankNo = no1;
                }
                
                if(this.formRecipient.bankBranchNo){
                    let no2 = '';
                    for(let i =0; i<7-(this.formRecipient.bankBranchNo + '').length;i++){
                        no2 += '0';
                    }
                    no2 += this.formRecipient.bankBranchNo;
                    this.formRecipient.bankBranchNo = no2;
                }
                
            }
        },
        getBank(type){
            this.bankType = type;
            this.bankSearch = {
                bankNo: '',
                bankName: ''
            }
            this.bankShow = true;
            this.$nextTick( () => {
                this.$refs.selectBank.init();
            })
        },
        checkBank(info){
            // let no = '';
            // for(let i =0; i<3-(info.id + '').length;i++){
            //     no += '0';
            // }
            // no += info.id;
            this.$set(this.formRecipient, 'bankNo', info.bankNo);
            this.$set(this.formRecipient, 'bankName', info.bankName);
            // this.queryBranch(info.id);
        },
         /**/
        getBranch(){
            this.branchSearch = {
                bankNo: this.formRecipient.bankNo,
                branchNo: '',
                branchName: ''
            }
            this.branchShow = true;
            this.$nextTick( () => {
                this.$refs.selectBranch.init();
            })
        },
        checkBranch(info){
            // let no = '';
            // for(let i =0; i<7-(info.id + '').length;i++){
            //     no += '0';
            // }
            // no += info.id;
            this.$set(this.formRecipient, 'bankBranchNo', info.branchNo);
            this.$set(this.formRecipient, 'bankBranchName', info.branchName);
            // this.queryBranch(info.id);
        },
        
        getCountry(){
            this.countrySearch = {
                countryCode: this.formRecipient.countryCode
            }
            this.countryShow = true;
            this.$nextTick( () => {
                this.$refs.selectCountry.init();
            })
        },
        checkCountry(info){
            this.$set(this.formRecipient, 'country', info.country);
            this.$set(this.formRecipient, 'country', info.name);
        },
        getCurrency(){
            this.currencySearch ={
                currencyCode: this.formRecipient.currencyCode ? this.formRecipient.currencyCode: ''
            }
            this.currencyShow = true;
            this.$nextTick( () => {
                this.$refs.selectCurrency.init();
            })
        },
        checkCurrency(info){
            this.$set(this.formRecipient, 'currencyCode', info.currencyCode);
            this.$set(this.formRecipient, 'currencyName', info.currencyName);
        },
        
        saveBank(){
            this.$emit('updateBank', {index: this.options.index, value: this.tableData, type: this.options.type});
            this.$emit('updateBankCtls', this.dtlsList);
            this.show = false;
        },
        addNew () {
            let obj = {
                bankName: null,
                accountName: null,
                accountNo: null,
                ipBaseNo: this.$route.query.ipBaseNo
            }
            this.tableData.push(obj);
            let length = this.tableData.length;
            this.editBank(length-1, this.tableData[length-1], 'add')
        },
        deleteBank (index, rows) {
            this.$msgbox.confirm(`確定進行[删除]操作?`, '提示', {
                confirmButtonText: '確定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.tableData.splice(index, 1)
            })
        },
        editBank (index, rows, type) {
            this.editBankType = type ? type : 'edit'; 
            this.editIndex = index;
            this.showEdit = true;
            this.formRecipient = this.$utils.copy(rows);
            let flag = false;
            this.dtlsList.forEach( (item, index) => {
                if(item.validFrom == this.formRecipient.validFrom && item.ipBaseNo == this.formRecipient.ipBaseNo){
                    flag = true;
                    this.mbrMemberBankAccDtl = this.dtlsList[index];
                }
            })
            if(!flag){
                this.mbrMemberBankAccDtl = {
                    ipBaseNo: this.formRecipient.ipBaseNo,
                    validFrom: this.formRecipient.validFrom
                };
                this.dtlsList.push(this.mbrMemberBankAccDtl);
            }
            // if( JSON.stringify(this.mbrMemberBankAccDtl) === '{}'){

            // }
            
            // this.$set(this.formRecipient , 'mbrMemberBankAccDtl', this.formRecipient.mbrMemberBankAccDtl ? this.formRecipient.mbrMemberBankAccDtl : {})
            if(type != 'add'){
                if(this.formRecipient.bankNo){
                    let no1 = '';
                    for(let i =0; i<3-(this.formRecipient.bankNo + '').length;i++){
                        no1 += '0';
                    }
                    no1 += this.formRecipient.bankNo;
                    this.formRecipient.bankNo = no1;
                }
                
                if(this.formRecipient.bankBranchNo){
                    let no2 = '';
                    for(let i =0; i<7-(this.formRecipient.bankBranchNo + '').length;i++){
                        no2 += '0';
                    }
                    no2 += this.formRecipient.bankBranchNo;
                    this.formRecipient.bankBranchNo = no2;
                }
                
            }
            
        },
        validFromChange(){
            this.mbrMemberBankAccDtl.validFrom = this.formRecipient.validFrom
        },
        cancel(){            
            if(this.editBankType == 'add'){
                this.tableData.splice(length-1, 1);
                this.dtlsList.splice(length-1, 1);
            }
            this.$emit("cancel")
            this.$refs.ruleForm.resetFields();
        },
        confirm() {
            this.$refs.ruleForm.validate((valid) => {
                if (valid) {
                    this.$set(this.tableData, this.editIndex, this.formRecipient)
                    this.showEdit = false;
                    this.$emit('saveChangeBank', this.formRecipient,this.mbrMemberBankAccDtl,this.options);
                    this.formRecipient = {
                        // mbrMemberBankAccDtl: {}
                    }
                } else {
                    // this.$toast({tips: '請輸入 Valid From'})
                    return false;
                }
            });
            // if(!this.formRecipient.validFrom){
            //     this.$toast({tips: '請輸入 Valid From'})
            //     return;
            // }
            
        },
        clearBank(){
            this.formRecipient.bankNo = ''
            this.formRecipient.bankName = ''
            this.formRecipient.bankBranchNo = ''
            this.formRecipient.bankBranchName = ''
        }
    }

}
</script>
<style lang="scss" scoped>
/deep/ .detail .el-dialog__header{
    padding: 0;
}
/deep/ .el-dialog--center .el-dialog__body{
    padding-bottom: 0px;
}
/deep/ .el-select{
    width: 207px;
}
.second{
    border-top: 1px solid #eee;
    padding-top: 14px;
    padding-left: 20px;

}
</style>
