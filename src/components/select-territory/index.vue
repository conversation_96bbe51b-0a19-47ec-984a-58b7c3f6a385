<template>
<div>
    <el-dialog :visible.sync="show" width="600px" title="Territory" :close-on-click-modal="false">
        <div style="width: 520px;margin: auto;margin-bottom: 20px">
            <el-input v-model="searchInfo.tisN" @keyup.enter.native="onSubmit()" placeholder="tisN" style="width: 160px;"></el-input>
            <el-input v-model.trim="searchInfo.tisA" @keyup.enter.native="onSubmit()" placeholder="tisA" style="width: 140px;"></el-input>
            <el-button type="primary" @click="onSubmit()">查詢</el-button>
            <span class="clear-search" @click="clearSearch()">清除搜索</span>
        </div>
        <el-table :empty-text="emptyText"   :data="tableData">
            <el-table-column property="tisN" label="tisN" width="230px"></el-table-column>
            <el-table-column property="tisA" label="tisA" width="150px"></el-table-column>
            <el-table-column
                label="operation">
                <template slot-scope="scope">
                    <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                        <i class="el-icon-check" @click="checkedTerritory(scope.$index,scope.row)"></i>
                    </span>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :total="total" @current-change="onSubmit" :current-page="currentPage">
        </el-pagination>
    </el-dialog>
</div>
</template>
<script>

export default {
    data(){
        return{
            tableresult:'暫無數據',
            show:false,
            searchInfo: {},
            tableData: [],
            total: 0,
            currentPage: 1 
        }
    },
    props: {
        search: {
            type: Object,
            default: () => {
                return {
                    tisA: '',
                    tisN: '',
                }

            }
        },
    },

    methods: {
        /**
         * dialog 组件调用方式： 
         * 1、父级用v-if在html里加载组件,
         * 2、父级调用子级的init方法，将数据初始化，prop传给data等
         * 
         */
        init(){
            this.show = true;
            this.searchInfo = this.$utils.copy(this.search);
            this.onSubmit();
        },
        clearSearch(){
            this.searchInfo = {
                tisA: '',
                tisN: ''
            };
            this.onSubmit();
        },
        onSubmit(page){
            let params = this.searchInfo;
            params.page = {
                pageNum : page ? page : 1,
                pageSize: 10
            };
            this.emptyText = '數據加載中'
            this.$http.post('/ref/getRefTerritoryListPage', params).then(res => {
                if (res.success) {
                    this.tableData = res.data.list;
                    this.total = res.data.total;
                    this.currentPage = page ? page : 1;
                    if(! this.tableData || this.tableData.length == 0){
                        this.emptyText = '暫無數據';
                    }
                }
            })
        },
        checkedTerritory(index, row){
            this.$emit('checkTerritory', row);
            this.show = false;
        }
    }
    
}
</script>
<style lang="scss" scoped>
    /deep/ .el-dialog__title{
        font-weight: bold;
    }
    /deep/ .el-dialog__body{
        text-align: center;
        padding-top: 0;
    }
</style>