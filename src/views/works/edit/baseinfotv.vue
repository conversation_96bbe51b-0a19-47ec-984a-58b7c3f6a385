<template>
    <div class="tvbox">
        <top-bar :steps="steps" :showTransfer="false" @save="saveWrk" v-if="top_barShow">
            <slot v-if="workId">
                <div class="work-header">
                    <!-- <label><span>Work No:</span> {{formOriginal.workId}}</label> -->
                    <label><span>Soc/Work No:</span> {{formOriginal.workSocietyCode}}/{{formOriginal.workId}}</label>
                    <label class="title" :title="headerInfo.title"><span>Title:</span> {{headerInfo.title}}</label>
                    <label :title="headerInfo.performersAll"><span>Perf:</span> {{headerInfo.performers}}</label>
                    <label :title="headerInfo.authorComposerAll"><span>A&C:</span> {{headerInfo.authorComposer}}</label>
                </div>
            </slot>
        </top-bar>
            <slot v-if="!top_barShow&&workId">
                <div class="work-header">
                    <!-- <label><span>Work No:</span> {{formOriginal.workId}}</label> -->
                    <label><span>Soc/Work No:</span> {{formOriginal.workSocietyCode}}/{{formOriginal.workId}}</label>
                    <label class="title" :title="headerInfo.title"><span>Title:</span> {{headerInfo.title}}</label>
                    <label :title="headerInfo.performersAll"><span>Perf:</span> {{headerInfo.performers}}</label>
                    <label :title="headerInfo.authorComposerAll"><span>A&C:</span> {{headerInfo.authorComposer}}</label>
                </div>
            </slot>
        <el-collapse v-model="activeNames" class="contentbox">
            <el-collapse-item class="step-jump works" title="works" name="1">
                <div class="boxline p-t-10">
                    <el-form :inline="true" :model="formOriginal" :rules="rulesOriginal" ref="formOriginal" label-position="left" class="demo-form-inline">
                        <!-- <div class="f-l"> -->
                            <el-form-item label="societyCode" prop="soc">
                                <el-input v-model="formOriginal.workSocietyCode" placeholder="" style="width: 50px;" :readonly="!!formOriginal.workId"></el-input>
                            </el-form-item>
                            <el-form-item label="Title" prop="title">
                                <el-input    v-model="formOriginal.title" placeholder=""></el-input>
                            </el-form-item>
                            <el-form-item label="Eposide No" prop="episodeNo">
                                <el-input v-model="formOriginal.episodeNo" placeholder=""></el-input>
                            </el-form-item>
                            <el-form-item label="Local" v-if="workId">
                                <el-checkbox v-model="localchecked" disabled></el-checkbox>
                            </el-form-item>
                            <el-form-item label="Genre">
                                <el-input v-model="formOriginal.genre" readonly placeholder="雙擊查詢" @dblclick.native="genreInit" style="width: 80px"></el-input>
                            </el-form-item>
                        <!-- </div> -->
                        <div class="f-l double">
                            <el-form-item label="Duration" prop=""> <!-- prop="durationM" -->
                                <el-input style="width: 60px" v-model.number="formOriginal.durationM" placeholder="" @change="changeDuration('durationM')"></el-input>
                            </el-form-item>
                            :
                            <el-form-item style="margin-left: 5px; margin-right: 15px;" prop=""> <!-- prop="durationS" -->
                                <el-input style="width: 60px" v-model.number="formOriginal.durationS" placeholder="" @change="changeDuration('durationS')"></el-input>
                            </el-form-item>
                        </div>
                        <el-form-item label="PerLanguage">
                            <el-input v-model="formOriginal.performLanguage" readonly placeholder="雙擊查詢" @dblclick.native="languageInit('pre')" style="width: 80px"></el-input>
                        </el-form-item>
                        <el-form-item label="Title Language">
                            <el-input v-model="formOriginal.titleLanguage" readonly placeholder="雙擊查詢" @dblclick.native="languageInit('title')" style="width: 80px"></el-input>
                        </el-form-item>
                        <el-form-item label="Prod No">
                            <el-input v-model="formOriginal.productionNo" placeholder="" style="width: 120px;"></el-input>
                        </el-form-item>
                        <el-form-item label="Prod Year">
                            <el-input v-model="formOriginal.productionYear" placeholder="" style="width: 50px;"></el-input>
                        </el-form-item>
                        <el-form-item label="ProdCompany">
                            <el-input v-model="formOriginal.productCompany" placeholder=""></el-input>
                        </el-form-item>
                        <el-form-item label="Air Date">
                            <!-- <el-input v-model="formOriginal.publishAirDate" placeholder="" v-dateFmt></el-input> -->
                            <date-picker v-model="formOriginal.publishAirDate" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd"></date-picker>
                        </el-form-item>
                        <el-form-item label="ISWC">
                            <!-- <el-input v-model="formOriginal.iSWC" placeholder="" style="width: 140px;"></el-input> -->
                                <el-select v-model="currentIswc" @change="changeIswc">
                                     <el-option
                                        v-for="item in iswcList"
                                        :key="item.id"
                                        :label="item.iswc"
                                        :value="item.id">
                                    </el-option>
                                </el-select>
                        </el-form-item>
                        <el-form-item label="Director">
                            <el-input v-model="formOriginal.director" placeholder=""></el-input>
                        </el-form-item>
                        <el-form-item label="ISAN">
                            <el-input v-model="formOriginal.isan" placeholder="" style="width: 140px;"></el-input>
                        </el-form-item>
                        <!-- <el-form-item label="Title Language">
                            <el-input v-model="formOriginal.titleLanguage" placeholder="雙擊查詢" @dblclick.native="languageInit('title')" style="width: 80px" readonly></el-input>
                        </el-form-item> -->
                    </el-form>
                </div>
            </el-collapse-item>
            <el-collapse-item class="step-jump" title="Performer " name="2">
                <performer ref="performer" :tableData="tableData1"></performer>
            </el-collapse-item>
            <!--Episode-->
            <el-collapse-item class="step-jump" title="Episode" name="3">
                <el-form :inline="true" :model="formShare" class="demo-form-inline" style="padding-top: 10px;">
                    <el-form-item label="Search Text" style="margin-left: -10px;">
                        <el-input v-model="formShare.user" placeholder=""></el-input>
                    </el-form-item>
                    <!-- <el-form-item>
                        <el-checkbox v-model="checked">Distributable</el-checkbox>
                    </el-form-item> -->
                </el-form>
                <div class="boxline">
                    <el-table
                        :data="tableData2"
                        border
                        stripe
                        style="width: 100%">
                        <el-table-column
                            prop="episodeNo"
                            label="Eps No">
                            <template slot-scope="scope">
                                <el-input v-model=scope.row.episodeNo placeholder="雙擊查詢" readonly @dblclick.native="getWork(scope.$index,scope.row)"></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="title"
                            label="Title">
                            <template slot-scope="scope">
                                <el-input v-model=scope.row.title placeholder="雙擊查詢" @dblclick.native="getWork(scope.$index,scope.row)" readonly></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="componentWorkId"
                            label="Works No">
                            <template slot-scope="scope">
                                <el-input type="text" v-model=scope.row.componentWorkId placeholder="雙擊查詢" @dblclick.native="getWork(scope.$index,scope.row)" readonly></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="comWorkSociety"
                            label="Soc">
                            <template slot-scope="scope">
                                <el-input v-model=scope.row.comWorkSociety placeholder="" readonly></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="genre"
                            label="Genre">
                            <template slot-scope="scope">
                                <el-input v-model=scope.row.genre placeholder="雙擊查詢" @dblclick.native="getWork(scope.$index,scope.row)" readonly></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="type"
                            label="Type">
                            <template slot-scope="scope">
                                <el-input v-model=scope.row.type placeholder="" readonly></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="source"
                            label="Source">
                            <template slot-scope="scope">
                                <el-input v-model=scope.row.source readonly></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="notifyDate"
                            label="NotifyDate"
                            width="120">
                            <template slot-scope="scope">
                                <date-picker v-model="scope.row.notifyDate" type="date" style="width: 100px !important; " value-format="yyyy-MM-dd" format="yyyyMMdd" readonly></date-picker>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="airDate"
                            label="AirDate"
                            width="120">
                            <template slot-scope="scope">
                                <date-picker v-model="scope.row.publisherAirDate" type="date" style="width: 100px !important; " value-format="yyyy-MM-dd" format="yyyyMMdd" readonly></date-picker>
                            </template>
                        </el-table-column>

                        <!-- <el-table-column
                            prop="dur"
                            label="Duration">
                            <template slot-scope="scope">
                                <el-input v-model=scope.row.dur placeholder="" readonly></el-input>
                            </template>
                        </el-table-column> -->
                        <!-- <el-table-column
                            prop="usage"
                            label="Usage">
                            <template slot-scope="scope">
                                <el-input v-model=scope.row.usage placeholder=""></el-input>
                            </template>
                        </el-table-column> -->
                        
                        <el-table-column
                            label="operation"
                            width="100">
                            <template slot-scope="scope">
                                <span class="spanright">
                                    <i class="el-icon-delete" @click="deletedata2(scope.$index,tableData2)"></i>
                                </span>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <div class="add-new">
                    <el-button type="primary" @click="adddata2()" v-if="isAuth('works:base:change')">新 增</el-button>
                </div>
            </el-collapse-item>
            <!--Source-->
            <el-collapse-item class="step-jump" title="Source" name="4">
                <work-source ref="getSource" :tableData="tableData3"></work-source>
            </el-collapse-item>
            <!--Other Titles-->
            <el-collapse-item class="step-jump" title="Other Titles" name="5">
                <other-title ref="otherTitle" :tableData="tableData4"></other-title>
            </el-collapse-item>
            <!--Remark-->
            <el-collapse-item class="step-jump" title="Remark" name="6">
                <div class="boxline">
                    <el-table
                        :data="tableData5"
                        border
                        stripe
                        style="width: 100%">
                        <el-table-column
                            prop="inputSoc"
                            label="Soc">
                            <template slot-scope="scope">
                                <el-input v-model=scope.row.inputSoc placeholder="" readonly></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="remark"
                            label="Remark">
                            <template slot-scope="scope">
                                <el-input type="textarea" v-model=scope.row.remark placeholder=""></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column
                            label="operation"
                            width="100">
                            <template slot-scope="scope">
                                <span class="spanright">
                                    <i class="el-icon-delete" @click="deletedata5(scope.$index,tableData5)"></i>
                                </span>
                            </template>
                        </el-table-column>
                    </el-table>
                    <div class="add-new">
                        <el-button type="primary" @click="adddata5()" v-if="isAuth('works:base:change')">新 增</el-button>
                    </div>
                </div>
            </el-collapse-item>
            <!--ISRC-->
            <el-collapse-item class="isrcpo" title="ISRC" name="7">
                <div class="boxline">
                    <div id='scrollBox' style="max-height: 320px; overflow: auto;">
                        <el-table
                            :data="tableData6"
                            border
                            stripe
                            style="width: 100%">
                            <el-table-column
                                prop="isrc"
                                label="ISRC">
                                <template slot-scope="scope">
                                    <el-input v-model=scope.row.isrc placeholder=""></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="inputSoc"
                                label="SOC">
                                <template slot-scope="scope">
                                    <el-input v-model=scope.row.inputSoc placeholder=""></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="operation"
                                width="100">
                                <template slot-scope="scope">
                                    <span class="spanright">
                                        <i class="el-icon-delete" @click="deletedata6(scope.$index,tableData6)"></i>
                                    </span>
                                </template>
                            </el-table-column>
                        </el-table>
                        <div class="t-c" style="height: 40px; line-height: 40px;" v-if="wiLength > 1">
                            <span :class="wiLength != scrollPage?'pointer':''" @click='lookMore()'>{{more}} </span>
                        </div>
                    </div>
                    <div class="add-new">
                        <el-button type="primary" @click="adddata6()" v-if="isAuth('works:base:change')">新 增</el-button>
                    </div>
                </div>
            </el-collapse-item>
            <!--关联作品-->
            <!-- <el-collapse-item class="step-jump component-class" title="Ref Work" name="8" v-show="workId"> -->
            <el-collapse-item class="step-jump component-class" title="Ref Work" name="8">
                <ref-work ref="refWork" :tableData="wrkWorkOtherSocCodeList" :baseInfo="formOriginal"></ref-work>
            </el-collapse-item>
            <!--Album-->
            <el-collapse-item class="step-jump component-class" title="Album" name="9">
                <album ref="album" :tableData="wrkWorkAlbumList" :baseInfo="formOriginal"></album>
            </el-collapse-item>
            <el-collapse-item class="step-jump" title="Sound Carrier" name="10">
                <div class="boxline p-t-10 clear carrier">
                    <p v-for="(item,index) in soundCarrierSet" :key="index">{{item}}</p>
                </div>
            </el-collapse-item>
            <el-collapse-item class="isrcpo" title="ISWC" name="11">
                <el-form :inline="true" :model="iswcdata" label-position="right" label-width="150px" class="demo-form-inline">
                    <div class="boxline">
                        <!-- <div>
                            <el-form-item label="iswc">
                                <el-select v-model="currentIswc" @change="changeIswc">
                                     <el-option
                                        v-for="item in iswcList"
                                        :key="item.id"
                                        :label="item.iswc"
                                        :value="item.id">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </div> -->
                        <div>
                            <el-form-item label="iswc">
                                <el-input v-model="iswcdata.iswc" placeholder="" @change="updateIswc"></el-input>
                            </el-form-item>
                            <el-form-item label="source">
                                <el-input v-model="iswcdata.source" placeholder=""></el-input>
                            </el-form-item>
                            <!-- <el-form-item label="Country">
                                <el-input v-model="iswcdata.country" placeholder=""></el-input>
                            </el-form-item> -->
                            <el-form-item label="Duplicate Status">
                                <el-input v-model="iswcdata.duplicateStatus" ></el-input>
                            </el-form-item>
                            <el-form-item label="Music Arr Code">
                                <el-input v-model="iswcdata.musicArrCode" ></el-input>
                            </el-form-item>
                            <el-form-item label="Lyric Adapt Code">
                                <el-input v-model="iswcdata.lyricAdaptCode" ></el-input>
                            </el-form-item>
                            <el-form-item label="Version Type">
                                <el-input v-model="iswcdata.versionType" placeholder="" ></el-input>
                            </el-form-item>
                            <el-form-item label="Version Iswc">
                                <el-input v-model="iswcdata.versionIswc" placeholder=""></el-input>
                            </el-form-item>
                            <el-form-item label="Version Title">
                                <el-input v-model="iswcdata.versionTitle" placeholder=""></el-input>
                            </el-form-item>
                            <el-form-item label="Version Ip">
                                <el-input v-model="iswcdata.versionIp" placeholder=""></el-input>
                            </el-form-item>
                            <el-form-item label="Version Lang">
                                <el-input v-model="iswcdata.versionLangCode" placeholder=""></el-input>
                            </el-form-item>
                            <el-form-item label="Excerpt Type">
                                <el-input v-model="iswcdata.excerptType" placeholder=""></el-input>
                            </el-form-item>
                            <el-form-item label="Excerpt Iswc">
                                <el-input v-model="iswcdata.excerptIswc" placeholder=""></el-input>
                            </el-form-item>
                            <el-form-item label="Excerpt Title">
                                <el-input v-model="iswcdata.excerptTitle" placeholder=""></el-input>
                            </el-form-item>
                            <el-form-item label="Excerpt Ip">
                                <el-input v-model="iswcdata.excerptIp" placeholder=""></el-input>
                            </el-form-item>
                            <el-form-item label="Excerpt Lang">
                                <el-input v-model="iswcdata.excerptLangCode" placeholder="" style="width: 250px"></el-input>
                            </el-form-item>
                            <el-form-item label="Composite Type">
                                <el-input v-model="iswcdata.compositeType" placeholder=""></el-input>
                            </el-form-item>
                            <el-form-item label="Composite Title">
                                <el-input v-model="iswcdata.compositeTitle" placeholder="" style="width: 250px"></el-input>
                            </el-form-item>

                            <el-form-item label="Composite Iswc">
                                <el-input v-model="iswcdata.compositeIswc" placeholder=""></el-input>
                            </el-form-item>
                            <el-form-item label="Composite Ip">
                                <el-input v-model="iswcdata.compositeIp" placeholder="" style="width: 250px"></el-input>
                            </el-form-item>

                            <el-form-item label="Composite Dur">
                                <el-input v-model="iswcdata.compositeDur" placeholder=""></el-input>
                            </el-form-item>
                            <el-form-item label="Preferred">
                                <el-input v-model="iswcdata.preferred" placeholder="" style="width: 250px"></el-input>
                            </el-form-item>
                        </div>
                    </div>
                </el-form>
            </el-collapse-item>
        </el-collapse>
        <!-- <el-dialog    :visible.sync="genreShow">
            <div style="width: 500px;margin: auto;margin-bottom: 20px">
                <el-input placeholder="請輸入曲風" v-model="workInput" class="input-with-select">
                    <el-button slot="append" icon="el-icon-search" @click="workSearch"></el-button>
                </el-input>
            </div>
            <el-table :empty-text="emptyText"   stripe :data="genreData">
                <el-table-column property="genreDetail" label="曲風詳情" width="150"></el-table-column>
                <el-table-column property="workType" label="作品類型" width="200"></el-table-column>
                <el-table-column property="usageFlag" label="使用標識"></el-table-column>
                <el-table-column property="description" label="描述"></el-table-column>
                <el-table-column
                    label="operation"
                    width="100">
                    <template slot-scope="scope">
                        <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                            <i class="el-icon-check" @click="checkedGenre(scope.$index,scope.row)"></i>
                        </span>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                background
                layout="prev, pager, next"
                :total=genreTotal @current-change="genreHandleCurrentChange">
            </el-pagination>
        </el-dialog> -->

        <el-dialog :visible.sync="languageShow" :before-close='languageClose' :close-on-click-modal="false">
            <div style="width: 500px;margin: auto;margin-bottom: 20px">
                <el-input placeholder="請輸入語言编碼" @keyup.enter.native="languageSearch" v-model="languageCodeInput" class="input-with-select">
                    <el-button slot="append" icon="el-icon-search" @click="languageSearch"></el-button>
                </el-input>
            </div>
            <el-table :empty-text="emptyText" stripe :data="languageData">
                <el-table-column property="languageCode" label="語言编碼"    ></el-table-column>
                <el-table-column property="description" label="描述" ></el-table-column>
                <el-table-column
                    label="operation"
                    width="100">
                    <template slot-scope="scope">
                        <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                            <i class="el-icon-check" @click="checkedLanguage(scope.$index,scope.row)"></i>
                        </span>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                background
                layout="prev, pager, next"
                :total=languageTotal @current-change="languageHandleCurrentChange">
            </el-pagination>
        </el-dialog>
        <el-dialog :visible.sync="ipShareShow" :before-close='ipShareClose' :close-on-click-modal="false">
            <div style="width: 500px;margin: auto;margin-bottom: 20px">
                <el-input placeholder="請輸入RomanName" @keyup.enter.native="ipShareSearch" v-model="ipShareInput" class="input-with-select">
                    <el-button slot="append" icon="el-icon-search" @click="ipShareSearch"></el-button>
                </el-input>
            </div>
            <el-table :empty-text="emptyText" stripe :data="ipShareData">
                <el-table-column property="dummyName" label="Name" ></el-table-column>
                <el-table-column property="ipNameNo" label="ipNameNo" ></el-table-column>
                <el-table-column property="workSocietyCode" label="Soc" ></el-table-column>
                <el-table-column property="ipShare" label="ipShare" ></el-table-column>
                <el-table-column
                    label="operation"
                    width="100">
                    <template slot-scope="scope">
                        <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                            <i class="el-icon-check" @click="checkedIpShare(scope.$index,scope.row)"></i>
                        </span>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                background
                layout="prev, pager, next"
                :total=ipShareTotal @current-change="ipShareHandleCurrentChange">
            </el-pagination>
        </el-dialog>
        <el-dialog :visible.sync="langShow" :before-close='langClose' :close-on-click-modal="false">
            <div style="width: 500px;margin: auto;margin-bottom: 20px">
                <el-input placeholder="請輸入語言编碼" @keyup.enter.native="langSearch" v-model="langCodeInput" class="input-with-select">
                    <el-button slot="append" icon="el-icon-search" @click="langSearch"></el-button>
                </el-input>
            </div>
            <el-table :empty-text="emptyText" stripe :data="langData">
                <el-table-column property="languageCode" label="語言编碼" ></el-table-column>
                <el-table-column property="description" label="描述" ></el-table-column>
                <el-table-column
                    label="operation"
                    width="100">
                    <template slot-scope="scope">
                        <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                            <i class="el-icon-check" @click="checkedLang(scope.$index,scope.row)"></i>
                        </span>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                background
                layout="prev, pager, next"
                :total=languageTotal @current-change="languageHandleCurrentChange">
            </el-pagination>
        </el-dialog>
        <el-dialog :visible.sync="genreTableShow" :before-close='genretableClose' :close-on-click-modal="false" width="750px">
            <div style="width: 500px;margin: auto;margin-bottom: 20px">
                <el-input placeholder="請輸入曲風" @keyup.enter.native="genreSearch" v-model="genretableInput" class="input-with-select">
                    <el-button slot="append" icon="el-icon-search" @click="genreSearch"></el-button>
                </el-input>
            </div>
            <el-table :empty-text="emptyText" stripe :data="genreTableData">
                <el-table-column property="genreDetail" label="曲風詳情" width="120"></el-table-column>
                <el-table-column property="workType" label="作品類型" width="120"></el-table-column>
                <el-table-column property="usageFlag" label="使用標識" width="120"></el-table-column>
                <el-table-column property="description" label="描述"></el-table-column>
                <el-table-column
                    label="operation"
                    width="100">
                    <template slot-scope="scope">
                        <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                            <i class="el-icon-check" @click="checkedTableGenre(scope.$index,scope.row)"></i>
                        </span>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                background
                layout="prev, pager, next"
                :total=genreTotal @current-change="genreHandleCurrentChange">
            </el-pagination>
        </el-dialog>

        <!-- <el-dialog :visible.sync="workByIdShow">
            <div style="width: 500px;margin: auto;margin-bottom: 20px">
                <el-input placeholder="請輸入曲風" v-model="workByIdInput" class="input-with-select">
                    <el-button slot="append" icon="el-icon-search" @click="workByIdSearch"></el-button>
                </el-input>
            </div>
            <el-table :empty-text="emptyText"   stripe :data="workByIdTableData">
                <el-table-column property="inputSoc" label="inputSoc" width="150"></el-table-column>
                <el-table-column property="title" label="title" width="200"></el-table-column>
                <el-table-column property="workId" label="workId"></el-table-column>
                <el-table-column property="genreCode" label="genreCode"></el-table-column>
                <el-table-column
                    label="operation"
                    width="100">
                    <template slot-scope="scope">
                        <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                            <i class="el-icon-check" @click="checkedTableById(scope.$index,scope.row)"></i>
                        </span>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                background
                layout="prev, pager, next"
                :total=genreTotal @current-change="genreHandleCurrentChange">
            </el-pagination>
        </el-dialog> -->
        <select-work v-if="selectWorkShow" ref="selectWorkCom" :search="workSearch" @checkWork="checkWork"></select-work>
    </div>
</template>

<script>
    import axios from '../../../utils/httpRequest'
    import qs from 'qs'
    import otherTitle from '../../demo/othertitle-tv'
    import performer from '../../demo/performer'
    import workSource from '../../demo/worksource'
    import selectWork from '../../../components/select-work';
    import refWork from '../../demo/ref-work'
    import album from '../../demo/album'


    export default {
        name: 'tvinfo',
        data () {
            var checkDurationM = (rule, value, callback) => {
                if (value < 0 || value > 1000) {
                    callback(new Error('不能大於1000min或者小於0min！'))
                } else {
                    callback()
                }
            }
            var checkDurationS = (rule, value, callback) => {
                if (value < 0 || value > 59) {
                    callback(new Error('不能大於59s或者小於0s！'))
                } else {
                    callback()
                }
            }
            var validateNameInput = (rule, value, callback) => {
                if (!value) {
                    return callback(new Error('此項必填！'))
                } else {
                    callback()
                }
            }
            var validateTypeInput = (rule, value, callback) => {
                if (!value) {
                    return callback(new Error('此項必填！'))
                } else {
                    callback()
                }
            }
            var validateIdInput = (rule, value, callback) => {
                if (!value) {
                    return callback(new Error('此項必填！'))
                } else {
                    callback()
                }
            }
            var validateDateInput = (rule, value, callback) => {
                if (!value) {
                    return callback(new Error('此項必填！'))
                } else {
                    callback()
                }
            }
            var validateSocInput = (rule, value, callback) => {
                if (!value) {
                    return callback(new Error('此項必填！'))
                } else {
                    callback()
                }
            }
            return {
                top_barShow:true,
                tableData3: [
                    // { sourceType: '', notifySouceId: '', source: '', name: '', notifyDate: '', inputSoc: '' }
                ],
                tableRules: {
                    sourceType: [{ validator: validateTypeInput, trigger: 'blur' }],
                    notifySouceId: [{ validator: validateIdInput, trigger: 'blur' }],
                    name: [{ validator: validateNameInput, trigger: 'blur' }],
                    notifyDate: [{ validator: validateDateInput, trigger: 'blur' }],
                    inputSoc: [{ validator: validateSocInput, trigger: 'blur' }]
                },
                titleInput: '',
                titleData: [],
                titleTotal: 10,
                activeStep: 1,
                steps: [
                    {
                        name: 'works'
                    }, {
                        name: 'Performer'
                    }, {
                        name: 'Episode'
                    }, {
                        name: 'Source'
                    }, {
                        name: 'Other Titles'
                    },
                    {
                        name: 'Remark'
                    },
                    {
                        name: 'ISRC'
                    },
                    {
                        name: 'Ref Work'
                    },
                    {
                        name: 'Album'
                    },
                    {
                        name: 'Sound Carrier'
                    },
                    {
                        name: 'ISWC'
                    }
                ],
                stepshow: true,
                sourceTypeShow: false,
                typeData: [],
                typeTotal: 10,
                typeInput: '',
                genreShow: false,
                genreShow1: false,
                genreIndex1: '',
                langShow: false,
                workByIdInput: '',
                genreTableShow: false,
                workByIdShow: false,
                genretableInput: '',
                workByIdIndex: '',
                langIndex: '',
                genreIndex: '',
                ipShareIndex: '',
                langCodeInput: '',
                langData: [],
                genreTableData: [],
                workByIdTableData: [],
                languageShow: false,
                artistShow: false,
                titleLanguageShow: true,
                workInput: '',
                romanNameInput: '',
                languageCodeInput: '',
                artistTotal: 1,
                languageTotal: 1,
                ipShareTotal: 10,
                genreTotal: 1,
                pageInfo: {
                    page: {
                        pageNum: 0,
                        pageSize: 10
                    }
                },
                ipShareShow: false,
                ipShareInput: '',
                emptyText: '暫無數據',
                ipShareData: [],
                genreData: [],
                languageData: [],
                artistData: [],
                checked: true,
                sdchecked: true,
                rsdchecked: true,
                checkedperf: true,
                localchecked: true,
                checkedpub: true,
                activeNames: ['1', '2', '3', '4', '5', '6', '7','8','9','10'],
                formOriginal: {
                    id: '',
                    title: '',
                    episodeNo: '',
                    local: '',
                    workSocietyCode: '161',
                    genre: 'TVS',
                    durationM: '',
                    durationS: '',
                    performLanguage: '',
                    productionNo: '',
                    productionYear: '',
                    productCompany: '',
                    publishAirDate: '',
                    iSWC: '',
                    director: '',
                    isan: '',
                    workType: 'AV'
                },
                rulesOriginal: {
                    title: [  
                        { required: true, message: '請輸入作品名稱', trigger: 'blur' }
                    ],
                    durationM: [
                        // { required: true, message: '請輸入分钟', trigger: 'blur' },
                        { validator: checkDurationM, trigger: 'blur' }
                    ],
                    durationS: [
                        // { required: true, message: '請輸入秒', trigger: 'blur' },
                        { validator: checkDurationS, trigger: 'blur' }
                    ]
                },
                formShare: {
                    user: '',
                    region: '',
                    type: ''
                },
                tableData1: [
                    // { id: '', ipNameNo: '', romanName: 'male', chineseName: '', country: '', soc: '', perf: '', pub: '' }
                ],
                tableData2: [
                    // {dummyName: '', ipNameNo: '', workSocietyCode: '', ipShare: '', sdcheck: '', ipType: '', wn: '', genre: ''}
                    // { gp: '', name: '', cn: '', status: '', inn: '', soc: '', ips: '', sd: '' }
                ],
                tableData4: [
                    // { group: '', title: '', languageCode: '', genreCode: '', durationM: '', durationS: '', inputSoc: '' }
                ],
                tableData5: [
                    // { inputSoc: '', remark: '' }
                ],
                tableData6: [
                    // { isrc: '', inputSoc: '' }
                ],
                wrkWorkAlbumList:[],
                soundCarrierSet:[],
                rules: {
                    sourceType: [{ validator: validateTypeInput, trigger: 'blur' }],
                    notifySouceId: [{ validator: validateIdInput, trigger: 'blur' }],
                    name: [{ validator: validateNameInput, trigger: 'blur' }],
                    notifyDate: [{ validator: validateDateInput, trigger: 'blur' }],
                    inputSoc: [{ validator: validateSocInput, trigger: 'blur' }]
                },
                tableData8: [
                    // {date: '', userid: 'ZHOUCHUANXIONG', type: 'male', remark: 'L120881672'}
                ],
                showConvert: false,

                 // 查詢作品
                editIndex: 0,
                selectWorkShow: false,
                workSearch: {},
                // 頭部
                headerInfo:{
                    performers: '',
                    performersAll: '',
                    authorComposer: '',
                    authorComposerAll: '',
                    title: ''
                },
                wrkWorkOtherSocCodeList: [],
                scrollPage:1,
                wiList:[],
                wiLength:1,
                more:'查看更多',
                iswcdata: {
                    iswc: '',
                    source: '',
                    duplicateStatus: '',
                    musicArrCode: '',
                    lyricAdaptCode: '',
                    versionType: '',
                    versionIswc: '',
                    versionTitle: '',
                    versionIp: '',
                    versionLangCode: '',
                    excerptType: '',
                    excerptIswc: '',
                    excerptTitle: '',
                    excerptIp: '',
                    excerptLangCode: '',
                    compositeType: '',
                    compositeTitle: '',
                    compositeIswc: '',
                    compositeIp: '',
                    compositeDur: '',
                    preferred:''
                },
                iswcList: [],
                currentIswc: ''
            }
        },
        methods: {
            changeDuration(data){
                let isNum = /^[0-9]*$/;
                let val = this.formOriginal[data]
                if (isNum.test(val)) {
                    if(data=='durationM' && (val < 0 || val > 1000)){
                        this.$toast({tips: '不能大於1000min或者小於0min！'});
                        this.$set(this.formOriginal, data, '')
                    }else if(data=='durationS' && (val < 0 || val > 59)){
                        this.$toast({tips: '不能大於59s或者小於0s！'});
                        this.$set(this.formOriginal, data, '')
                    }
                }else{
                    this.$toast({tips: 'Duration为纯数字'});
                    this.$set(this.formOriginal, data, '')
                }
            },
            languageClose(){
                this.languageCodeInput=''
                this.pageInfo.languageCode = this.languageCodeInput
                this.languageShow=false
            },
            ipShareClose(){
                this.ipShareInput=''
                this.ipShareShow=false
            },
            langClose(){
                this.langCodeInput=''
                this.pageInfo.languageCode = this.langCodeInput
                this.langShow=false
            },
            genretableClose(){
                this.genretableInput=''
                this.pageInfo.genreDetail = this.genretableInput
                this.genreTableShow=false
            },
            lookMore(){
                if(this.wiLength != this.scrollPage){
                    let box = document.querySelector("#scrollBox");
                    let table = document.querySelector("#scrollTable");
                    let b = table.offsetHeight;
                    this.scrollPage ++;
                    this.tableData6 = this.wiList.slice(1,(this.scrollPage*100))
                    this.$nextTick(()=>{
                        box.scrollTop=(b/this.scrollPage)*(this.scrollPage)
                    })
                    return
                }else{
                    this.more='没有更多数据了'
                }
            },
             /**
             * 选取作品
             */
            getWork(index, row) {
                console.log(row)
                this.editIndex = index;
                this.selectWorkShow= true;
                // console  clg();
                this.workSearch = {
                    title: row.title,
                    // workNo: row.componentWorkId,
                    // workSoc: row.soc,
                    workId: row.componentWorkId,
                    soc: row.comWorkSociety,
                    // workType: row.type,
                    genre: row.genre
                    // workType: 'AV',
                    // genre: 'TVS'
                }
                this.$nextTick( () => {
                    this.$refs.selectWorkCom.init();
                })
            },
            checkWork(info){
               let params = {
                    workId: info.work_id,
                    workSocietyCode: info.work_society_code
                }
                axios.get('/wrk/getWrkWorkById', {params}).then(res => {
                    if (res.status === 200) {
                        let workInfo = res.data.wrkWork;
                        this.$set(this.tableData2[this.editIndex], 'title', workInfo.title ? workInfo.title : workInfo.title_en);
                        this.$set(this.tableData2[this.editIndex], 'componentWorkId', workInfo.workId);
                        this.$set(this.tableData2[this.editIndex], 'comWorkSociety', workInfo.workSocietyCode);
                        this.$set(this.tableData2[this.editIndex], 'genre', workInfo.genre);
                        this.$set(this.tableData2[this.editIndex], 'type', workInfo.workType);
                        this.$set(this.tableData2[this.editIndex], 'episodeNo', workInfo.episodeNo);
                        this.$set(this.tableData2[this.editIndex], 'publisherAirDate', workInfo.publishAirDate);
                        // 这里 找出 source 和 notifyDate 规则：   sourceList里有 161 的优先161的，没有161的，就取第一条的
                        let temp = {};
                        res.data.wwsList.forEach( item => {
                            if(item.inputSoc == '161'){
                                temp = item;
                            }
                        })
                        if(!temp.sourceId){
                            temp = res.data.wwsList[0];
                        }
                        this.$set(this.tableData2[this.editIndex], 'source', temp.notifySouceId);
                        this.$set(this.tableData2[this.editIndex], 'notifyDate', temp.notifyDate);
                    }
                })
            },
            changeRsd (val) {
                this.rschecked = val
                this.update = false
                this.$nextTick(() => {
                    this.update = true
                })

                if (val) {
                    this.tableData2.forEach((item, index) => {
                        item.sdcheck = val
                    })
                }
                if (!val) {
                    this.tableData2.forEach((item, index) => {
                        item.sdcheck = val
                    })
                }
            },
            changeSd (item) {
                this.updateRsd = false
                this.$nextTick(() => {
                    this.updateRsd = true
                })
                // console clg(JSON.stringify(this.tableData2))
                if (JSON.stringify(this.tableData2).includes('false')) {
                    this.rschecked = false
                } else {
                    this.rschecked = true
                }
            },
            // 描点链接跳转
            jump (index) {
                // 用 class="d_jump" 添加锚点
                let jump = document.querySelectorAll('.step-jump')
                let total = jump[index].offsetTop
                this.scrollTopIndex = index
                this.activeStep = index
                // this.scrollTop = jump[index].offsetTop
                let distance = document.documentElement.scrollTop || document.body.scrollTop
                // 平滑滚動，时长500ms，每10ms一跳，共50跳
                let step = total / 50
                if (total > distance) {
                    smoothDown()
                } else {
                    let newTotal = distance - total
                    step = newTotal / 50
                    smoothUp()
                }
                function smoothDown () {
                    if (distance < total) {
                        distance += step
                        document.body.scrollTop = distance
                        document.documentElement.scrollTop = distance
                        setTimeout(smoothDown, 10)
                    } else {
                        document.body.scrollTop = total
                        document.documentElement.scrollTop = total
                    }
                }
                function smoothUp () {
                    if (distance > total) {
                        distance -= step
                        document.body.scrollTop = distance
                        document.documentElement.scrollTop = distance
                        setTimeout(smoothUp, 10)
                    } else {
                        document.body.scrollTop = total
                        document.documentElement.scrollTop = total
                    }
                }
            },

            onScroll () {
                let _article = document.querySelectorAll('.step-jump')
                _article.forEach((item, index) => {
                    if (this.scrollTop >= item.offsetTop) {
                        this.activeStep = index
                    }
                })
            },
            // 描点链接跳转結束
            stepToggle () {
                this.stepshow = !this.stepshow
            },
            // source
            getSourceType (index, item) {
                this.typeIndex = index
                axios.post('/ref/getRefWrkSourceType', this.pageInfo).then(res => {
                    if (res.status === 200) {
                        this.sourceTypeShow = true
                        this.typeData = res.data.list
                        this.typeTotal = res.data.total
                        // console  clg(res.data)
                    }
                })
            },
            addArtist () {
                this.artistShow = false
                this.$router.push({ name: 'addActor' })
            },
            // getSummaries (param) {
            //     const { columns, data } = param
            //     const sums = []
            //     columns.forEach((column, index) => {
            //         if (index === 0) {
            //             sums[index] = '總計'
            //         } else if (index === 5) {
            //             const values = data.map(item => Number(item[column.property]))
            //             if (!values.every(value => isNaN(value))) {
            //                 sums[index] = values.reduce((prev, curr) => {
            //                     const value = Number(curr)
            //                     if (!isNaN(value)) {
            //                         return prev + curr
            //                     } else {
            //                         return prev
            //                     }
            //                 }, 0)
            //             } else {
            //                 sums[index] = 'N/A'
            //             }
            //         } else {
            //             sums[index] = '--'
            //         }
            //     })
            //     return sums
            // },
            adddata1 () {
                let myArray = [{ id: '', ipNameNo: '', romanName: '', chineseName: '', country: '', soc: '', perf: '', pub: '' }]
                this.tableData1 = [...this.tableData1, ...myArray]
            },
            deletedata1 (index, rows) {
                this.$msgbox.confirm(`確定進行[删除]操作?`, '提示', {
                    confirmButtonText: '確定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    rows.splice(index, 1)
                })
            },
            adddata2 () {
                let myArray = [{ episodeNo: '', title: '', componentWorkId: '', 
                                 comWorkSociety: '', genre: '', type: '', source: '', notifyDate: '',
                                 publisherAirDate: '', workId: this.workId, workSocietyCode: this.formOriginal.workSocietyCode,
                                 workUniqueKey: this.formOriginal.workUniqueKey
                                }]
                this.tableData2 = [...this.tableData2, ...myArray]
            },
            deletedata2 (index, rows) {
                this.$msgbox.confirm(`確定進行[删除]操作?`, '提示', {
                    confirmButtonText: '確定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    rows.splice(index, 1)
                })
            },
            adddata4 () {
                let myArray = [{ group: '', title: '', languageCode: '', genreCode: '', durationM: '', durationS: '', inputSoc: '' }]
                this.tableData4 = [...this.tableData4, ...myArray]
            },
            deletedata4 (index, rows) {
                this.$msgbox.confirm(`確定進行[删除]操作?`, '提示', {
                    confirmButtonText: '確定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    rows.splice(index, 1)
                })
            },
            adddata5 () {
                let myArray = [{ inputSoc: '161', remark: '' }]
                this.tableData5 = [...this.tableData5, ...myArray]
            },
            deletedata5 (index, rows) {
                this.$msgbox.confirm(`確定進行[删除]操作?`, '提示', {
                    confirmButtonText: '確定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    rows.splice(index, 1)
                })
            },
            adddata6 () {
                let myArray = [{ isrc: '', inputSoc: '' }]
                this.tableData6 = [...this.tableData6, ...myArray]
            },
            deletedata6 (index, rows) {
                this.$msgbox.confirm(`確定進行[删除]操作?`, '提示', {
                    confirmButtonText: '確定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    rows.splice(index, 1)
                })
            },
            saveWrk () {
                let that = this;
                this.tableData1 = this.$refs.performer.getData();

                var p1 = new Promise(function (resolve, reject) {
                    that.$refs['formOriginal'].validate((valid) => {
                        if (valid) {
                            resolve()
                        }
                    })
                })
                // var p2 = new Promise(function (resolve, reject) {
                //     that.$refs['tableForm'].validate((valid) => {
                //         if (valid) {
                //             resolve()
                //         }
                //     })
                // })
                Promise.all([p1]).then(function () {
                    // if (that.localchecked) {
                    //     that.formOriginal.local = 'L'
                    // }
                    // if (!that.localchecked) {
                    //     that.formOriginal.local = 'F'
                    // }
                    if (that.dischecked) {
                        that.formOriginal.distFlag = 1
                    }
                    if (!that.dischecked) {
                        that.formOriginal.distFlag = 0
                    }
                    that.tableData1.forEach(item => {
                        if (item.pub) {
                            item.oriAritist = 'Y'
                        }
                        if (!item.pub) {
                            item.oriAritist = 'N'
                        }
                        if (item.perf) {
                            item.firstPub = 'Y'
                        }
                        if (!item.perf) {
                            item.firstPub = 'N'
                        }
                    })

                    that.tableDataSource = that.$refs.getSource.getData();
                     if(that.tableDataSource.length == 0){
                        console.log('stop');
                        return;
                    }
                    let wrkWorkOtherSocCodeList = that.$refs.refWork.getData();
                    let wrkWorkAlbumList = that.$refs.album.getData();
                    console.log('/: ', wrkWorkOtherSocCodeList);
                    if(!wrkWorkOtherSocCodeList || !wrkWorkAlbumList){
                        return;
                    }
                    that.tableData4 = that.$refs.otherTitle.getData();
                    let tableData6 = []
                    if(that.wiLength != that.scrollPage){
                        tableData6 = [...that.tableData6,...that.wiList.slice((that.scrollPage*100)+1)]
                    }else{
                        tableData6 = that.tableData6
                    }
                    let params = {
                        'soundCarrierSet': that.soundCarrierSet,
                        'waList': that.tableData1,     // 表演者Performer
                        // 'wamList': [],
                        'wiList': tableData6,
                        'wrkWorkAlbumList': wrkWorkAlbumList,
                        'wrkWork': Object.assign(that.formOriginal, that.formShare),     // works头部基本信息
                        // 'wwamList': [],

                        'wwcList': that.tableData2,
                        wrkWorkOtherSocCodeList: wrkWorkOtherSocCodeList,
                        // 'wwisList': {
                        //     'PER': that.PER,
                        //     'ZYN': that.ZYN,
                        //     'MEC': that.MEC,
                        //     'NOD': that.NOD,
                        //     'NDB': that.NDB
                        // },        // ip share
                        'wwrList': that.tableData5,         // remark
                        'wwsList': that.tableDataSource,
                        'wwtList': that.tableData4        // Other Titles
                        // 'wwtfList': []

                    }
                    let iSWC = params.wrkWork.iSWC.split('')
                    let num = 0
                    iSWC.forEach(item=>{
                        if(item=='.'||item=='-'){
                            num+=1
                        }
                    })
                    if(num == iSWC.length && num){
                        that.$toast({tips: 'ISWC不可全為.-'})
                        return
                    }
                    params.wrkWork.iSWC = params.wrkWork.iSWC.replace(/-/g, '').replace(/\./g, '')

                    params.iswcList = that.iswcList || []
                    // 判断 ipshare 里 ipnameno是否為空

                    let flag = true;
                    for(let key in params.wwisList){
                        if(params.wwisList[key]){
                            for(let i =0;i<params.wwisList[key].length ;i++){
                                if(!params.wwisList[key][i].ipNameNo){
                                    flag = false;
                                }
                            }
                        }
                    }
                    if(!flag){
                        that.$toast({tips: 'IP Share中的Ip Name No必須填寫'})
                        return;
                    }
                    const loading = that.$loading();

                    axios.post('/wrk/saveOrUpdateWrkWork', params).then(res => {
                        loading.close();
                        if (res.status === 200) {
                            // that.$message.success(res.data)
                            that.$toast({tips: res.data})
                            that.$bus.$emit('closeCurrentTab', () => {
                                that.$router.push({name: 'works-list', query: {update: true}});
                            })
                        }
                     }).catch( () => {
                        loading.close();
                    })
                })
            },
            genreInit () {
                this.genreShow = true
                axios.post('/ref/getRefGenreDtlByDetail', this.pageInfo).then(res => {
                    if (res.status === 200) {
                        this.genreData = res.data.list
                        this.genreTotal = res.data.total
                    }
                })
            },
            genreInit1 (index, item) {
                this.genreShow = true
                this.genreShow1 = true
                this.genreIndex1 = index
                axios.post('/ref/getRefGenreDtlByDetail', this.pageInfo).then(res => {
                    if (res.status === 200) {
                        this.genreData = res.data.list
                        this.genreTotal = res.data.total
                    }
                })
            },
            checkedGenre (index, item) {
                if (this.genreShow1) {
                    this.tableData2[this.genreIndex1].genre = item.genreDetail
                } else {
                    this.formOriginal.genre = item.genreDetail
                }
                this.genreShow = false
            },
            // workSearch () {
            //     this.pageInfo.genreDetail = this.workInput
            //     axios.post('/ref/getRefGenreDtlByDetail', this.pageInfo).then(res => {
            //         if (res.status === 200) {
            //             this.genreData = res.data.list
            //             this.genreTotal = res.data.total
            //         }
            //     })
            // },
            languageInit (str) {
                this.languageShow = true
                if (str === 'title') {
                    this.titleLanguageShow = true
                } else {
                    this.titleLanguageShow = false
                }
                this.emptyText = '數據加載中'
                axios.post('/ref/languageByCode', this.pageInfo).then(res => {
                    if (res.status === 200) {
                        this.languageData = res.data.list
                        this.languageTotal = res.data.total
                        if(! this.languageData || this.languageData.length == 0){
                            this.emptyText = '暫無數據';
                        }
                    }
                })
            },
            languageSearch () {
                this.pageInfo.languageCode = this.languageCodeInput
                this.emptyText = '數據加載中'
                axios.post('/ref/languageByCode', this.pageInfo).then(res => {
                    if (res.status === 200) {
                        this.languageData = res.data.list
                        this.languageTotal = res.data.total
                        if(! this.languageData || this.languageData.length == 0){
                            this.emptyText = '暫無數據';
                        }
                    }
                })
            },
            checkedLanguage (index, item) {
                if (this.titleLanguageShow) {
                    this.formOriginal.titleLanguage = item.languageCode
                } else {
                    this.formOriginal.performLanguage = item.languageCode
                }
                this.languageShow = false
            },
            artistInit (index, item) {
                this.artistIndex = index
                this.artistShow = true
                axios.post('/wrk/artist/queryWrkArtistListByParams', this.pageInfo).then(res => {
                    if (res.status === 200) {
                        this.artistData = res.data.list
                        this.artistTotal = res.data.total;
                    }
                })
            },
            artistSearch () {
                this.pageInfo.romanName = this.romanNameInput
                axios.post('/wrk/artist/queryWrkArtistListByParams', this.pageInfo).then(res => {
                    if (res.status === 200) {
                        this.artistData = res.data.list
                        this.artistTotal = res.data.total
                    }
                })
            },
            checkedArtist (index, item) {
                this.tableData1[this.artistIndex].romanName = item.romanName
                this.tableData1[this.artistIndex] = item
                this.artistShow = false
            },
            genreHandleCurrentChange (val) {
                this.pageInfo.page.pageNum = val
                this.pageInfo.genreDetail = this.workInput
                axios.post('/ref/getRefGenreDtlByDetail', this.pageInfo).then(res => {
                    if (res.status === 200) {
                        this.genreData = res.data.list
                        this.genreTotal = res.data.total
                    }
                })
            },
            languageHandleCurrentChange (val) {
                this.pageInfo.page.pageNum = val
                this.pageInfo.languageCode = this.languageCodeInput
                this.emptyText = '數據加載中'
                axios.post('/ref/languageByCode', this.pageInfo).then(res => {
                    if (res.status === 200) {
                        this.languageData = res.data.list
                        this.languageTotal = res.data.total
                        if(! this.languageData || this.languageData.length == 0){
                            this.emptyText = '暫無數據';
                        }
                    }
                })
            },
            artistHandleCurrentChange (val) {
                this.pageInfo.page.pageNum = val
                this.pageInfo.romanName = this.romanNameInput
                axios.post('/wrk/artist/queryWrkArtistList', this.pageInfo).then(res => {
                    if (res.status === 200) {
                        this.artistData = res.data.list
                        this.artistTotal = res.data.total
                    }
                })
            },
            ipShareHandleCurrentChange (val) {
                let data = {
                    name: this.ipShareInput,
                    page_num: val,
                    page_size: '10'
                }
                let formData = qs.stringify(data)
                let config = {headers: { 'content-type': 'application/x-www-form-urlencoded' }}
                this.emptyText = '數據加載中'
                axios.post('/ip/name/es', formData, config).then(res => {
                    if (res.status === 200) {
                        this.ipShareData = res.data.list
                        this.ipShareTotal = res.data.total
                        if(! this.ipShareData || this.ipShareData.length == 0){
                            this.emptyText = '暫無數據';
                        }
                    }
                })
            },
            getIpShareData (index, item) {
                this.ipShareIndex = index
                this.ipShareShow = true
                let params = {
                    'page': {
                        'pageNum': 1,
                        'pageSize': 10
                    },
                    tisN: '',
                    countryCode: ''
                }
                this.emptyText = '數據加載中'
                axios.post('/wrk/ipShare/getWrkWorkIpShareByParams', params).then(res => {
                    if (res.status === 200) {
                        this.ipShareData = res.data.list
                        if(! this.ipShareData || this.ipShareData.length == 0){
                            this.emptyText = '暫無數據';
                        }
                    }
                    
                })
            },
            ipShareSearch () {
                let data = {
                    name: this.ipShareInput,
                    page_num: 1,
                    page_size: '10'
                }
                let formData = qs.stringify(data)
                let config = {headers: { 'content-type': 'application/x-www-form-urlencoded' }}
                this.emptyText = '數據加載中'
                axios.post('/ip/name/es', formData, config).then(res => {
                    if (res.status === 200) {
                        this.ipShareData = res.data.list
                        this.artistTotal = res.data.total
                        if(! this.ipShareData || this.ipShareData.length == 0){
                            this.emptyText = '暫無數據';
                        }
                    }
                    
                })
            },
            checkedIpShare (index, item) {
                this.ipShareShow = false
                this.tableData2[this.ipShareIndex] = item
            },
            getLang (index, item) {
                this.langShow = true
                this.langIndex = index
                this.emptyText = '數據加載中'
                axios.post('/ref/languageByCode', this.pageInfo).then(res => {
                    if (res.status === 200) {
                        this.langData = res.data.list
                        if(! this.langData || this.langData.length == 0){
                            this.emptyText = '暫無數據';
                        }
                    }
                })
            },
            langSearch () {
                this.pageInfo.languageCode = this.langCodeInput
                this.emptyText = '數據加載中'
                axios.post('/ref/languageByCode', this.pageInfo).then(res => {
                    if (res.status === 200) {
                        this.langData = res.data.list
                        if(! this.langData || this.langData.length == 0){
                            this.emptyText = '暫無數據';
                        }
                    }
                })
            },
            checkedLang (index, item) {
                this.tableData4[this.langIndex].languageCode = item.languageCode
                this.langShow = false
            },
            getGenre (index, item) {
                this.genreTableShow = true
                this.genreIndex = index
                axios.post('/ref/getRefGenreDtlByDetail', this.pageInfo).then(res => {
                    if (res.status === 200) {
                        this.genreTableData = res.data.list
                        this.genreTotal = res.data.total
                    }
                })
            },
            genreSearch () {
                this.pageInfo.genreDetail = this.genretableInput
                axios.post('/ref/getRefGenreDtlByDetail', this.pageInfo).then(res => {
                    if (res.status === 200) {
                        this.genreTableData = res.data.list
                        this.genreTotal = res.data.total
                    }
                })
            },
            checkedTableGenre (index, item) {
                this.genreTableShow = false
                this.tableData4[this.genreIndex].genreCode = item.genreDetail
            },
            // getTitleByWorkId (index, item) {
            //     if(!item.wn && item.wn!= 0){
            //         return;
            //     }
            //     this.workByIdIndex = index
            //     let params = {
            //         'page': {
            //             'pageNum': 1,
            //             'pageSize': 10
            //         },
            //         workId: item.wn,
            //         title: ''
            //     }
            //     axios.post('/wrk/title/getTitleByParams', params).then(res => {
            //         if (res.status === 200) {
            //             this.workByIdTableData = res.data.list
            //             this.workByIdShow = true
            //         }
            //     })
            // },
            workByIdSearch () {
                this.workByIdTableData = []
                let params = {
                    'page': {
                        'pageNum': 1,
                        'pageSize': 10
                    },
                    workId: '',
                    title: this.workByIdInput
                }
                axios.post('/wrk/title/getTitleByParams', params).then(res => {
                    if (res.status === 200) {
                        this.workByIdTableData = res.data.list
                    }
                })
            },
            // checkedTableById (index, item) {
            //     this.tableData2[this.workByIdIndex].wn = item.workId
            //     this.tableData2[this.workByIdIndex].title = item.title ? item.title : item.titleEn;
            //     this.tableData2[this.workByIdIndex].soc = item.workSocietyCode;
            //     this.workByIdShow = false
            // },
            changeIswc(val){
                let _this = this
                _this.iswcList.forEach(item => {
                    if(item.id == val){
                        _this.currentIswc = item.iswc
                        _this.iswcdata = item
                        return
                    }
                })
            },
            updateIswc(val){
                this.currentIswc = val
            }
        },
        mounted () {
            this.top_barShow = this.$route.query.type ? false :true
            if(!this.workId){
                return;
            }
                let params = {
                    workId: this.workId,
                    workSocietyCode: this.socId
                }
                const loading = this.$loading();
                axios.get('/wrk/getWrkWorkById', {params}).then(res => {
                    loading.close();
                    if (res.status === 200) {
                        //判断，如果已经transfer,就弹框提示跳转到新的work
                        if(res.data.wrkWork.transferStatus){
                            this.$msgbox.confirm('This work will be transferred to ' + res.data.wrkWork.destWorkNum + '(' + res.data.wrkWork.destWorkNumSoc + ')' + '?', '提示', {
                                confirmButtonText: 'OK',
                                type: 'warning'
                            }).then(() => {
                                this.$bus.$emit('closeCurrentTab', (callback) => {
                                    this.$router.push({name: 'works-baseinfotv', query: {id: res.data.wrkWork.destWorkNum, socId: res.data.wrkWork.destWorkNumSoc, title: res.data.wrkWork.destWorkNum, nameId: res.data.wrkWork.destWorkNum + res.data.wrkWork.destWorkNumSoc}, params: {id: res.data.wrkWork.destWorkNum}})
                                })
                            }).catch( () => {
                                this.$bus.$emit('closeCurrentTab', () => { this.$router.push({name: 'works-list'}) });
                            })
                            return;
                        }
                        this.tableData1 = []
                        // let count = 0;
                        let temp = [];
                        res.data.waList.forEach((item, index) => {
                            let obj = {}
                            obj = item
                            if (item.oriAritist === 'Y') {
                                obj.pub = true
                            }
                            if (item.oriAritist === 'N') {
                                obj.pub = false
                            }
                            if (item.firstPub === 'N') {
                                obj.perf = false
                            }
                            if (item.firstPub === 'Y') {
                                obj.perf = true
                            }
                            this.tableData1.push(obj)
                            // if(count < 3){
                                temp.push(obj.chineseName ? obj.chineseName : obj.artistName);
                                // count++;
                            // }
                        })
                        this.headerInfo.performersAll = temp.join(',');
                        if(temp.length > 3){
                            temp = temp.slice(0, 3);
                        }
                        this.headerInfo.performers = temp.length > 0 ? temp.join(',') : '';
                        this.tableData3 = res.data.wwsList
                        this.tableData4 = res.data.wwtList
                        // this.tableData6 = res.data.wiList
                        this.wiList = res.data.wiList;
                        this.wiLength = Math.ceil(this.wiList.length/100)
                        this.tableData6 = this.wiList.slice(0,100)
                        this.wrkWorkAlbumList = res.data.wrkWorkAlbumList;
                        this.soundCarrierSet = res.data.soundCarrierSet;
                        this.tableData2 = res.data.wwcList ? res.data.wwcList : [];
                        this.wrkWorkOtherSocCodeList = res.data.wrkWorkOtherSocCodeList || [];
                        // this.transferInfo.info = res.data.wwisList[0];
                        this.tableRight = res.data.wwisList[0] ? res.data.wwisList[0] : {};
                        if(res.data.iswcList && res.data.iswcList.length > 0){
                            this.iswcList = res.data.iswcList;
                            this.iswcdata = res.data.iswcList[0]
                            this.currentIswc = res.data.iswcList[0].iswc
                        }
                        let ac = [];
                        let count = 0;
                        for(let key in this.tableRight){
                            if(count < 3){
                                this.tableRight[key].forEach( item => {
                                    if((item.workIpRole == 'C' || item.workIpRole == 'A' || item.workIpRole == 'CA') && count < 3 ){
                                        if( ac.indexOf(item.chineseName ? item.chineseName : item.name) === -1){
                                            ac.push(item.chineseName ? item.chineseName : item.name);
                                            count++;
                                        }

                                    }
                                })
                            }
                        }
                        this.headerInfo.authorComposer = ac.length > 0 ? (ac.length > 3 ? ac.join(',') + '...' : ac.join(',')) : '';
                        this.formOriginal = res.data.wrkWork;

                        //处理iswc
                        this.formOriginal.iSWC = this.$utils.formatIswc(this.formOriginal.iSWC);
                        this.headerInfo.title = res.data.wrkWork.title;

                        if(this.formOriginal.local == 'L'){
                            this.localchecked = true;
                        }else{
                            this.localchecked = false;
                        }                    
                        res.data.wwisList.forEach((item, index) => {
                            if (item.sd === 'Y') {
                                this.tableData2 = res.data.wwisList
                                this.tableData2[index].sdcheck = true
                            }
                            if (item.sd === 'N') {
                                this.tableData2 = res.data.wwisList
                                this.tableData2[index].sdcheck = false
                            }
                        })

                        this.tableData5 = res.data.wwrList;
                        res.data.wwtList.forEach((item, index) => {
                            if (item.subTitleId === 0) {
                                this.tableData4[index - 1] = item
                            }
                            // if (item.subTitleId === 1) {
                            //     this.formOriginal.title = item.title
                            // }
                        })
                    }
                }).catch(()=>{
                    loading.close();
                })
        },
        computed: {
            workId: {
                get () {
                    return this.$route.query.id
                }
            },

            socId: {
                get () {
                    return this.$route.query.socId
                }
            }
        },
        components: {
            otherTitle,
            workSource,
            performer,
            selectWork,
            refWork,
            album
        }
    }
</script>

<style lang="scss" scoped>
    @import "../../../assets/scss/works.scss";
</style>

<style>
.work-header label:first-child{
    max-width: 225px;
}
    .position{
        position: fixed;
        top:150px;
        right: 20px;
        width:170px;
        text-align: left;
        background-color: #FFFFFF;
        z-index: 199999999;
    }
    .el-step {
        cursor: pointer;
    }
    .el-card{
        margin-bottom: 20px;
    }
    .memberdetail{
        width: 100%;
    }
    .memberdetail .el-form-item__content{
        width: calc(100% - 150px )!important;
    }
    .el-icon-circle-plus-outline{
        cursor: pointer;
    }
    .el-icon-delete{
        cursor: pointer;
    }

    
    .works .el-form-item__label{
        width: 130px;
        white-space:nowrap;
        overflow:hidden;
        text-overflow:ellipsis;
    }
    .works .el-form-item__content{
        width: 200px;
    }
    .works .el-form-item .el-input input{
        width: 190px;
    }
    .works .double .el-form-item__content,.double .el-form-item .el-input input{
        width: 85px;
    }
    .carrier{
        padding-bottom: 10px;
    }
    .carrier>p{
        float: left;
        margin: 0 0 0 10px;
        background: #cde69c;
        color: #638421;
        border: 1px solid #a5d24a;
        padding: 0 10px;
    }
    .isrcpo{
        position: relative;
    }
</style>
