<template>
  <!-- 拆分後的文件 用于審核用的 -->
  <div class="ipibpox">
    <el-form :inline="true" :model="search" ref="form" class="demo-form-inline"   @keyup.enter.native='searchFn()'>
      <el-form-item>
        <el-input v-model="search.workId" placeholder="match work id" style="width: 153px;"></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="search.workSocietyCode" placeholder="match work soc" style="width: 160px;"></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="search.uniqueKeyMd5" placeholder="unipueKeyMd5" style="width: 120px;"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="searchFn()" v-if="isAuth('list-manage:claim-list:history:HistoryLog:find')">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <span class="clear-search" @click="clearSearch()" v-if="isAuth('list-manage:claim-list:history:HistoryLog:find')">清除搜索</span>
      </el-form-item>
    </el-form>
    <!-- 列表显示字段：
    ，work id，work soc，data unique key、创建时间，原始数据（json存储），更新后数据（json存储） -->
    <el-table :empty-text="tableresult"   stripe :data="tableData" border class="tabbox" title="" style="width: 100%">
      <el-table-column prop="workId" label="MatchWorkId"></el-table-column>
      <el-table-column prop="workSocietyCode" label="Match soc"></el-table-column>
      <el-table-column prop="dataUniqueKey" title="" label="Data unique key">
          <template slot-scope="scope">
              <el-tooltip class="item" effect="dark" :content="scope.row.dataUniqueKey" placement="top">
                  <div class="oneLine">{{scope.row.dataUniqueKey}}</div>
              </el-tooltip>
          </template>
      </el-table-column>
      <el-table-column prop="createTime" label="創建時間">
      </el-table-column>
      <el-table-column prop="createTime" label="修改時間">
      </el-table-column>
        <el-table-column prop="originalData" label="原始數據">
            <template slot-scope="scope">
                <el-tooltip class="item" effect="dark" placement="top" :disabled="!scope.row.originalData">
                    <div v-html="ToBreak(scope.row.originalData,scope.row.updatedData)" slot="content"></div>
                    <div class="oneLine">{{scope.row.originalData}}</div>
                </el-tooltip>
            </template>
        </el-table-column>
    </el-table>
    <div>

      <el-pagination background layout="prev, pager, next" :total="total" @current-change="searchFn" :current-page="currentPage">
      </el-pagination>
    </div>

  </div>
</template>

<script>
export default {
    name: 'ClaimHistoryLog',
    data() {
        return {
            tableData: [],
            tableresult: '',
            total: 0,
            currentPage: 1,
            search: {
                workId: '',
                workSocietyCode: '',
                uniqueKeyMd5: ''
            }
        }
    },
    mounted() {
        this.searchFn()
    },
    methods: {
        ToBreak(val,val2) {
            return `    <div style="width: 200px;">
      原始數據<br/>
      ${val.replace(/,/g, '<br/>')}    </div>
            `;
        }
        ,
        clearSearch() {
            this.search = {
                workId: '',
                workSocietyCode: '',
                uniqueKeyMd5: ''
            }
        },
        searchFn(page = 1) {
            let ajaxData = { ...this.search }
            ajaxData.listMatchDataBasicMatchHistory = this.$utils.copy(this.search);
            ajaxData.page = {
                pageNum: page ? page : 1,
                pageSize: 10
            }

            this.tableresult = '數據加載中...'
            this.$http.post('/list2match/getListMatchDataBasicMatchHistoryLog', ajaxData).then(res => {
                if (res.success) {
                    console.log("=========================")
                    console.log(res);
                    console.log("=========================")
                    this.tableData = res.data.data.list
                    console.log(this.tableData)
                    this.total = res.data.data.total
                    this.currentPage = page ? page : 1
                }
                this.tableresult = this.tableData.length == 0 ? '暫無數據' : ' '
            })
        },
        isTopNearTop(el) {
            if (!el) return false;
            const rect = el.getBoundingClientRect();
            const topThreshold = 120; // 距离顶部小于这个值就向下弹出
            return rect.top < topThreshold;
        }
    }
}
</script>

<style>
.el-form-item {
  margin-left: 0;
  margin-right: 0;
}
.el-button {
  margin-left: 0;
  margin-right: 0;
}
.oneLine {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
/* 去掉鼠标悬浮效果 */
.el-table tbody tr:hover > td {
		background-color: transparent !important
}
</style>
