<template>
  <!-- 上传的原文件 -->
  <!-- 上传清單 =》 拆分清單 =》 清單明細 -->
  <div class="ipibpox">
    <el-form :inline="true" :model="search" ref="form" class="demo-form-inline"  @keyup.enter.native='querySearch(1)'>
      <el-form-item prop="ID">
        <el-input v-model.trim="search.id" placeholder="ID" style="width: 80px;"></el-input>
      </el-form-item>
      <el-form-item prop="File Name">
        <el-input v-model.trim="search.fileName" placeholder="File Name" style="width: 180px;"></el-input>
      </el-form-item>
      <el-form-item prop="File Path">
        <el-input v-model.trim="search.filePath" placeholder="File Path" style="width: 180px;"></el-input>
      </el-form-item>
      <el-form-item prop="Category">
        <el-autocomplete v-model.trim="search.categoryCode" :fetch-suggestions="querySearchCategory" placeholder="請輸入categoryCode" :trigger-on-focus="false" @select="handleSelectCategory">
          <template slot-scope="scope">
            <p>{{scope.item.categoryCode}}</p>
          </template>
        </el-autocomplete>
      </el-form-item>
      <el-form-item>
        <el-select v-model="search.status" placeholder="所有狀態" style="width: 120px;">
          <el-option v-for=" (item, index) in config.status" :key="index" :value="item.value" :label="item.label"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="search.isNoCategory" placeholder="绑定模板">
          <el-option v-for=" (item, key) in config.hasCategory" :key="key" :value="item.value" :label="item.label"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="search.uploadType" placeholder="音樂類型">
          <el-option v-for=" (item, index) in config.listType" :key="index" :value="item.value" :label="item.label"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="search.fileType" placeholder="File Type">
          <el-option label="全部" value="PS"></el-option>
          <el-option label="一般清单" value="P2"></el-option>
          <el-option label="單場次" value="S"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <date-picker type="date" v-model="search.fileUploadStartDate" placeholder="開始日期" style="width: 160px;" value-format="yyyy-MM-dd HH:mm:ss" format="yyyyMMdd"></date-picker>
        -
        <date-picker type="date" v-model="search.fileUploadEndDate" placeholder="結束日期" value-format="yyyy-MM-dd HH:mm:ss" format="yyyyMMdd" style="width: 160px;"></date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="querySearch(1)" v-if="isAuth('list-manage:normal-list:upload:uploadList:search')">搜索</el-button>
        <el-button type="primary" @click="addFn" v-if="isAuth('list-manage:normal-list:upload:uploadList:addFn')">上傳</el-button>
        <el-button type="primary" :disabled="selectTableList.length == 0" @click="parsingByList" v-if="isAuth('list-manage:upload:uploadList:analysis')">批量重新解析</el-button>
        <el-button type="primary" :disabled="selectTableList.length == 0" @click="bindByList"  v-if="isAuth('list-manage:normal-list:upload:uploadList:binding')">批量绑定</el-button>
        <el-button type="primary" :disabled="selectTableList.length == 0" @click="deleteByList" v-if="isAuth('list-manage:normal-list:upload:uploadList:numDel')">批量删除</el-button>
        <el-button type="primary" :disabled="selectTableList.length == 0" @click="guidangByList" v-if="isAuth('list-manage:normal-list:upload:uploadList:numDel')">批量歸檔</el-button>
        <span class="clear-search" @click="clearSearch()" v-if="isAuth('list-manage:normal-list:upload:uploadList:clearSearch')">清除搜索</span>
      </el-form-item>
    </el-form>
    <p>當前總筆數：{{total}}筆</p>
    <el-table :empty-text="tableresult" stripe :data="tableData" border width='100%' @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55">
      </el-table-column>
      <el-table-column prop="status" label="歸檔" width="60">
        <template slot-scope="scope">
          <span v-if="scope.row.status == 4"><i class="el-icon-check"></i></span>
        </template>
      </el-table-column>
      <el-table-column prop="id" label="ID">
      </el-table-column>
      <el-table-column prop="fileName" min-width="200" label="File Name">
        <!-- <template slot-scope="scope">
          <el-tooltip class="item" effect="dark" :content="scope.row.fileName" placement="top">
            <div class="oneLine">{{scope.row.fileName}}</div>
          </el-tooltip>
        </template> -->
      </el-table-column>
      <el-table-column prop="filePath" min-width="200" label="File Path">
        <template slot-scope="scope">
          <!-- <el-tooltip class="item" effect="dark" :content="scope.row.filePath" placement="top"> -->
          <div class="" @click="copypath(scope.row.filePath)" style="color:#409EFF">{{scope.row.filePath}}</div>
          <!-- </el-tooltip> -->
        </template>

      </el-table-column>
      <el-table-column prop="uploadUserName" label="Upload User">
      </el-table-column>
      <el-table-column prop="createTime" label="Upload Time" width="120">
        <template slot-scope="scope">{{scope.row.createTime | splitDate}}</template>
      </el-table-column>
      <el-table-column prop="amendTime" label="Finished Time" width="120">
        <template slot-scope="scope">
          <span v-if="scope.row.status == 2">{{scope.row.amendTime | splitDate}}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="fileType" label="File Type">
        <template slot-scope="scope">
          <el-button type="text" v-if="scope.row.fileType === 'S'">單場次</el-button>
          <el-button type="text" v-else-if="scope.row.fileType === 'P'">一般清單(旧)</el-button>
          <el-button type="text" v-else-if="scope.row.fileType === 'C'">claim</el-button>
          <el-button type="text" v-else-if="scope.row.fileType === 'P2'">一般清單</el-button>
          <el-button type="text" v-else-if="scope.row.fileType === 'S2'">新單場次</el-button>
          <el-button type="text" v-else>{{scope.row.fileType}}</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="sequence" label="Sequence" width="88">
      </el-table-column>
      <el-table-column prop="categoryCode" label="Category" >
        <template slot-scope="scope">
          <span :title="scope.row.categoryCode">{{scope.row.categoryCode}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="uploadType" label="音樂類型">
      </el-table-column>
      <el-table-column prop="status" label="Status" width="90">
        <template slot-scope="scope">
          <span v-if="scope.row.status == 0">等待處理</span>
          <span v-if="scope.row.status == 1">處理中</span>
          <span v-if="scope.row.status == 2">處理完成</span>
          <span v-if="scope.row.status == 4">歸檔</span>
          <span v-if="scope.row.status == 5">撤銷</span>
          <span v-if="scope.row.status == 3" style="color:#409EFF" @click='faildetail(scope.row.description)'>處理失敗</span>
          <!-- <span v-else-if="scope.row.status == 1">處理中  </span>
                    <span v-else-if="scope.row.status == 2">處理完成</span>
                    <span v-else-if="scope.row.status == 3">處理失敗</span> -->
          <!-- <span v-else>處理失敗</span> -->
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="operation" width="130">
        <!-- <template slot-scope="scope">
          <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
            <el-button type="text" size="small" @click="deleteFile(scope.row)">刪除</el-button>
            <el-button v-if="scope.row.status == 2" @click="viewFile(scope.row)" type="text" size="small">查看文件</el-button>
            <el-button v-if="scope.row.status == 3" type="text" size="small" @click="viewErrorMsg(scope.row.description)">查看錯誤原因</el-button>
            <el-button v-if="scope.row.status == 3" type="text" size="small" @click="reAnalysis(scope.row.id)">重新解析</el-button>
            <el-button v-if="scope.row.isNoCategory > 0 && scope.row.status ==2" type="text" size="small" @click="showCategoryCodeDialog(scope.row)">绑定category</el-button>
          </span>
        </template> -->

        <template slot-scope="scope">
          <span style="width: 100%;display: flex;felx-start:row;text-align: center;justify-content:space-between;cursor: pointer">
            <span style="width: 50%;display: inline-block;text-align: center;cursor: pointer">
                <el-button v-if="scope.row.status == 2 || scope.row.status == 4 || scope.row.status == 5" type="text" size="small" @click.native="viewFile(scope.row)">查看文件</el-button>
                <el-button v-else-if="scope.row.status == 3" type="text" size="small" @click.native="reAnalysis(scope.row.id)">重新解析</el-button>
                <!-- <el-button v-if="scope.row.status == 4" type="text" size="small" @click.native="cancelGuidang(scope.row.id)">撤銷</el-button>        -->
                <el-button v-else @click.native="deleteFile(scope.row)" type="text" size="small">刪除</el-button>
              <!-- <el-button v-if="scope.row.status == 3" type="text" size="small" @click.native="viewErrorMsg(scope.row.description)">查看錯誤原因</el-button> -->
            </span>
            <span v-if="(scope.row.isNoCategory== 0 && scope.row.status == 2 )||scope.row.status == 3">
                <el-button @click.native="deleteFile(scope.row)" type="text" size="small">刪除</el-button>
            </span>
            <span v-if="scope.row.isNoCategory== 0 && scope.row.status == 4">
                <el-button @click.native="cancelGuidang(scope.row.id)" type="text" size="small">撤銷</el-button>
            </span>
            <el-dropdown style="display:flex; align-items:center;cursor: pointer" v-if="scope.row.isNoCategory > 0 && (scope.row.status == 2 || scope.row.status == 4 || scope.row.status == 5)">
              <span class="a-blue">
                更多
              </span>
              <el-dropdown-menu slot="dropdown">
                <!-- <el-dropdown-item v-if="scope.row.status == 2" @click.native="viewFile(scope.row)" type="text" size="small">查看文件</el-dropdown-item> -->
                <el-dropdown-item v-if="scope.row.status == 2" @click.native="deleteFile(scope.row)" type="text" size="small">刪除</el-dropdown-item>
                <!-- <el-dropdown-item v-if="scope.row.status == 3" type="text" size="small" @click.native="viewErrorMsg(scope.row.description)">查看錯誤原因</el-dropdown-item> -->
                <el-dropdown-item v-if="scope.row.status == 3" type="text" size="small" @click.native="reAnalysis(scope.row.id)">重新解析</el-dropdown-item>
                <el-dropdown-item v-if="scope.row.status == 4" type="text" size="small" @click.native="cancelGuidang(scope.row.id)">撤銷</el-dropdown-item>
                <el-dropdown-item v-if="scope.row.isNoCategory > 0 && (scope.row.status ==2 || scope.row.status ==4 || scope.row.status ==5)" type="text" size="small" @click.native="showCategoryCodeDialog(scope.row)">绑定category</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </span>
        </template>


      </el-table-column>
    </el-table>
    <el-pagination background layout=" prev, pager, next" :total="total" :current-page.sync="search.page.pageNum" @current-change="handleCurrentChange">
    </el-pagination>

    <el-dialog :visible.sync="showBindCategoryCodeDialog" title="绑定category" width="60%" @close="initCategoryCodeList" :close-on-click-modal="false">

      <el-form :inline="true" :model="search" ref="form" class="demo-form-inline" style="text-align: center" @keyup.enter.native='getCategoryCodeList()'>
        <el-form-item>
          <el-input v-model="categorySearchForm.categoryCode" placeholder="category code" style="width: 300px;"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getCategoryCodeList()">查询</el-button>
        </el-form-item>
      </el-form>
      <el-table :empty-text="tableresult" :data="categoryCodeList">
        <el-table-column property="categoryCode" label="categoryCode"></el-table-column>
        <el-table-column property="categoryDesc" label="categoryDesc"></el-table-column>
        <el-table-column label="operation" width="100">
          <template slot-scope="scope">
            <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer" title="选择categoryCode">
              <i class="el-icon-check" @click="checkCategory(scope.row.categoryCode)"></i>
            </span>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background layout="prev, pager, next" :total="categoryCodeListTotal" @current-change="handleCategoryChange">
      </el-pagination>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'sampleDateList',
  data() {
    return {
      tableData: [],
      selectTableList: [],
      total: 0,
      bindType: 0, // bindType为1时是批量绑定，为0为单个绑定
      search: {
        page: {
          pageNum: 1,
          pageSize: 10
        },
        id: '',
        filePath: '',
        fileName: '',
        fileType: 'PS',
        categoryCode: '',
        status: '',
        uploadType: null,
        fileUploadStartDate: '',
        fileUploadEndDate: '',
        isNoCategory: null
      },
      dateRange: [],
      config: {
        status: [
          {
            label: '全部狀態',
            value: null
          },
          {
            label: '等待處理',
            value: 0
          },
          {
            label: '處理中',
            value: 1
          },
          {
            label: '處理完成',
            value: 2
          },
          {
            label: '處理失敗',
            value: 3
          },
          {
            label: '歸檔',
            value: 4
          },
          {
            label: '撤銷',
            value: 5
          }
        ],

        listType: [
          {
            value: '',
            label: '全部'
          },
          {
            value: 'PG',
            label: 'PG'
          },
          {
            value: 'CJ',
            label: 'CJ'
          },
          {
            value: 'MS',
            label: 'MS'
          },
          {
            value: 'FW',
            label: 'FW'
          },
          {
            value: '節目',
            label: '節目'
          }

        ],

        hasCategory: [
          {
            value: '',
            label: '全部'
          },
          {
            value: 0,
            label: '已存在'
          },
          {
            value: 1,
            label: '未配置'
          },
          {
            value: 2,
            label: '未绑定'
          }
        ]
      },
      showBindCategoryCodeDialog: false,
      categoryCodeList: [], tableresult: ' ',
      categoryCodeListTotal: 0,
      categorySearchForm: {
        page_num: 1,
        page_size: 10,
        categoryCode: ''
      },
      bindCategoryForm: null,
    }
  },
  mounted() {
    this.querySearch()
  },
  activated() {
    this.$nextTick(() => {
      if (this.$route.query.update) {
        let query = this.$route.query;
        delete query.update;
        this.clearSearch()
      }
    })
  },
  methods: {
    copypath(value) {
      // this.$alert(`<strong>${value}</strong>`, '請複製路徑', {
      //   dangerouslyUseHTMLString: true
      // });
      this.$alert(value, '請複製路徑', {
        confirmButtonText: '确定',
      });
    },
    faildetail(value) {
      // this.$alert(`<strong>${value}</strong>`, '查看錯誤原因', {
      //   dangerouslyUseHTMLString: true
      // });
      this.$alert(value, '查看錯誤原因', {
        confirmButtonText: '确定',
      });
    },
    clearSearch() {
      this.search = {
        page: {
          pageNum: 1,
          pageSize: 10
        },
        id: '',
        filePath: '',
        fileName: '',
        fileType: 'PS',
        categoryCode: null,
        isNoCategory: null,
        status: '',
        uploadType: null,
        fileUploadStartDate: '',
        fileUploadEndDate: ''
      }
      this.total = 0
      this.querySearch(1)
    },
    querySearch(page = 1) {

      for (let item in this.search) {
        if (item != 'page' && item != 'fileType' && this.search[item]) {
          this.search.page.pageNum = 1
        }
      }
      if (page) {

        this.search.page.pageNum = page
      }

      let endDate = this.search.fileUploadEndDate
      if (endDate) {
        this.search.fileUploadEndDate = endDate.split(' ')[0] + ' 23:59:59'
      }
      this.tableresult = '數據加載中...'
      this.$http.post('/list/getList2FileQueueList', this.search).then(res => {
          console.log('resp is:',res)
        if (res.success) {
          this.tableData = res.data.data?res.data.data.list:[]
          this.total =res.data.data? res.data.data.total:0
          if (res.data.code!=200) {
            this.$toast({tips:res.data.message})
          }
        }
        this.tableresult = !this.tableData||this.tableData.length == 0 ? '暫無數據' : ' '
      })
    },
    handleSelectionChange(tableList) {
      this.selectTableList = tableList
    },
    handleSelect(item) {
      console.log(item)
    },

    handleCurrentChange(val) {
      this.querySearch(val)
      // this.currentpage=val
      this.search.page.pageNum = val
    },
    handleClick(item) {
      this.$router.push({ name: '', query: {} })
    },
    addFn() {
      this.$router.push({ name: 'normalListUpload' })
    },
    // 查看清單處理後的
    viewFile(row) {
      this.$router.push({ name: 'normalListFileList', query: { id: row.id, title: row.id, type: row.fileType } })
    },
    viewErrorMsg(description) {
      this.$alert(description, '失敗原因', {
        confirmButtonText: '确定'
      })

    },
    downloadFile(row) {
      this.$http.post('/list2FileQueue/downloadErrorFile?id=' + row.id, {}, { responseType: 'blob' }).then(res => {
        if (res.data) {
          let blob = new Blob([res.data])
          var downloadElement = document.createElement('a');
          var href = window.URL.createObjectURL(blob); //创建下载的链接
          downloadElement.href = href;
          let name = row.fileName.split('.')[0] + '-錯誤報告.xlsx'
          downloadElement.download = name; //下载后文件名
          downloadElement.click(); //点击下载
          document.body.removeChild(downloadElement); //下载完成移除元素
          window.URL.revokeObjectURL(href); //释放掉blob对象
        }
      })
    },
    deleteFile(row) {
      if(row.status == 4){
        this.$toast({ tips: '已歸檔的文件不可刪除！' })
        return;
      }

      // 先获取删除预览数据
      this.getDeletePreview(row.id).then(previewData => {
        this.showDeleteConfirmDialog(previewData, () => {
          // 用户确认后执行实际删除
          this.$http.delete('/list2/deleteListFileQueue/' + row.id).then(res => {
            console.log(res)
            if (res.success && res.data.code == 200) {
              this.$toast({ tips: '刪除成功' })
              this.querySearch(this.search.page.pageNum)
            } else {
              this.$toast({ tips: '刪除失敗' })
            }
          })
        })
      }).catch(error => {
        console.error('获取删除预览失败:', error)
        this.$toast({ tips: '获取删除预览失败，请重试' })
      })
    },
    reAnalysis(id) {
      this.$http.get('/list2FileQueue/reparse/' + id).then(res => {
        console.log(res)
        if (res.data.code == 200) {
          this.querySearch(this.search.page.pageNum)
        }
      })
    },
    showCategoryCodeDialog(row) {
      this.bindCategoryForm = new FormData()
      this.bindCategoryForm.append('id', row.id)
      this.bindType = 0
      if (row.isNoCategory == 1) {
        this.showBindCategoryCodeDialog = true
        this.getCategoryCodeList()
      } else {
        this.checkCategory(row.categoryCode)
      }
    },
    checkCategory(categoryCode) {
      if (this.bindType) {
        this.bindCategoryCodeByList(categoryCode)
      } else {
        if (this.bindCategoryForm.get('categoryCode')) {
          this.bindCategoryForm.set('categoryCode', categoryCode)
        } else {
          this.bindCategoryForm.append('categoryCode', categoryCode)
        }
        this.bindCategoryCode()
      }
    },
    bindCategoryCodeByList(categoryCode) {
      let ids = this.$utils.getIds(this.selectTableList)
      let formdata = new FormData()
      formdata.append('categoryCode', categoryCode)
      formdata.append('ids', ids)
      this.$http.post('/list2/binding/categoryList', formdata).then(res => {
        if (res.data.code == 200) {
          this.showBindCategoryCodeDialog && (this.showBindCategoryCodeDialog = false)
          this.$toast({ tips: res.data.message })
          this.querySearch(this.search.page.pageNum)
        } else {
          this.$toast({ tips: res.data.message })
        }
      })
    },
    bindCategoryCode() {
      this.$http.post('/list2/binding/category', this.bindCategoryForm).then(res => {

        if (res.data.code == 200) {
          this.showBindCategoryCodeDialog && (this.showBindCategoryCodeDialog = false)
          this.querySearch(this.search.page.pageNum)
          this.$toast({ tips: '綁定成功' })
        } else {
          this.$toast({ tips: res.data.message })
        }
      })
    },
    initCategoryCodeList() {
      this.categorySearchForm.page_num = 0
      this.categoryCodeList = []
      this.categoryCodeListTotal = 0
    },
    getCategoryCodeList() {
      this.tableresult = '數據加載中...'
      this.$http.get('/list/categorys/bindWithoutSingle', { params: this.categorySearchForm }).then(res => {
        if (res.success) {
          this.categoryCodeList = res.data.data.list
          this.categoryCodeListTotal = res.data.data.total
        }
        this.tableresult = this.categoryCodeList.length == 0 ? '暫無數據' : ' '
      })
    },
    handleCategoryChange(val) {
      this.categorySearchForm.page_num = val
      this.getCategoryCodeList()
    },
    querySearchCategory(queryString, cb) {
      if (queryString) {
        this.$http.get('/list/categorys/getListCategoryByCode', { params: { categoryCode: queryString } }).then(res => {
          if (res.success) {
            cb(res.data)
          }
        })
      }
    },
    handleSelectCategory(val) {
      this.search.categoryCode = val.categoryCode
    },
    parsingByList() {
      let ids = this.$utils.getIds(this.selectTableList, 'id')
      this.$http.get('/listFileQueue/reparseList', { params: { ids: ids.toString() } }).then(res => {
        console.log(res)
        if (res.data.code == 200) {
          this.$toast({ tips: '解析成功' })
          this.querySearch()
        } else {
          this.$toast({ tips: res.data.message })
        }
      })

    },
    bindByList() {
      this.showBindCategoryCodeDialog = true

      this.bindType = 1
      this.getCategoryCodeList()
    },
    deleteByList() {
      let notAllowedList = this.selectTableList.filter(s=>s.status == 4 )
      if(notAllowedList && notAllowedList.length > 0){
        this.$toast({ tips: '有勾選已歸檔的文件，不能刪除！' })
        return;
      }

      let ids = this.$utils.getIds(this.selectTableList, 'id')

      // 先获取批量删除预览数据
      this.getBatchDeletePreview(ids).then(previewData => {
        this.showDeleteConfirmDialog(previewData, () => {
          // 用户确认后执行批量删除
          this.$http.delete('/list2/deleteListFileQueueList', { params: { id: ids.toString() } }).then(res => {
            console.log(res)
            if (res.data.code == 200) {
              this.$toast({ tips: '刪除成功' })
              this.querySearch()
            } else {
              this.$toast({ tips: res.data.message })
            }
          })
        })
      }).catch(error => {
        console.error('获取批量删除预览失败:', error)
        this.$toast({ tips: '获取删除预览失败，请重试' })
      })
    },
    guidangByList(){
      let notAllowedList = this.selectTableList.filter(s=> !(s.status == 2 || s.status == 5))
      if(notAllowedList && notAllowedList.length > 0){
        this.$toast({ tips: '有勾選已歸檔的文件或者未完成的文件，不能歸檔！' })
        return;
      }

      this.$alert('執行後將不可刪除資料，確認要執行嗎？', '批量歸檔', {
        confirmButtonText: '确定',
        showCancelButton: true,
        cancelButtonText: '取消',
        callback: action => {
          if (action == 'confirm') {
            let ids = this.$utils.getIds(this.selectTableList, 'id')
            this.$http.get('/list/guidangList2FileQueueList', { params: { id: ids.toString() } }).then(res => {
              console.log(res)
              if (res.data.code == 200) {
                this.$toast({ tips: '歸檔成功' })
                this.querySearch()
              } else {
                this.$toast({ tips: res.data.message })
              }
            })
          }
        }
      });
    },
    cancelGuidang(id){
      this.$http.get('/list/cancelGuidang2/' + id).then(res => {
        console.log(res)
        if (res.data.code == 200) {
          this.querySearch(this.search.page.pageNum)
        } else {
          this.$toast({ tips: res.data.message })
        }
      })
    },

    // 获取单个文件删除预览
    getDeletePreview(id) {
      return new Promise((resolve, reject) => {
        this.$http.get(`/list2/deleteListFileQueue/${id}/preview`).then(res => {
            console.log('res:', res)
          if (res.success && res.data.code === 200) {
            resolve(res.data.data)
          } else {
            reject(new Error(res.data.message || '获取删除预览失败'))
          }
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 获取批量删除预览（汇总多个文件的影响数据）
    getBatchDeletePreview(ids) {
      return new Promise((resolve, reject) => {
        // 为每个ID获取预览数据
        const previewPromises = ids.map(id => this.getDeletePreview(id))

        Promise.all(previewPromises).then(previewDataList => {
          // 汇总所有影响数据
          const combinedData = {
            combinedTotalIds: [],
            sampleRuleBaseIds: [],
            basicFileBaseIds: [],
            impactDescription: ''
          }

          let totalCombinedCount = 0
          let totalSampleRuleCount = 0
          let totalBasicFileCount = 0

          previewDataList.forEach(data => {
            if (data.combinedTotalIds) {
              combinedData.combinedTotalIds.push(...data.combinedTotalIds)
              totalCombinedCount += data.combinedTotalIds.length
            }
            if (data.sampleRuleBaseIds) {
              combinedData.sampleRuleBaseIds.push(...data.sampleRuleBaseIds)
              totalSampleRuleCount += data.sampleRuleBaseIds.length
            }
            if (data.basicFileBaseIds) {
              combinedData.basicFileBaseIds.push(...data.basicFileBaseIds)
              totalBasicFileCount += data.basicFileBaseIds.length
            }
          })

          // 去重
          combinedData.combinedTotalIds = [...new Set(combinedData.combinedTotalIds)]
          combinedData.sampleRuleBaseIds = [...new Set(combinedData.sampleRuleBaseIds)]
          combinedData.basicFileBaseIds = [...new Set(combinedData.basicFileBaseIds)]

          // 生成汇总描述
          combinedData.impactDescription = `批量删除这些文件队列将会影响： ${combinedData.basicFileBaseIds.length} 条基础文件记录， ${combinedData.combinedTotalIds.length} 条合并总表记录， ${combinedData.sampleRuleBaseIds.length} 条抽样规则记录`

          resolve(combinedData)
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 显示删除确认弹窗
    showDeleteConfirmDialog(previewData, confirmCallback) {
      const impactInfo = previewData.impactDescription || '无影响数据'

      // 格式化ID列表显示
      const formatIdList = (ids, maxDisplay = 10) => {
        if (!ids || ids.length === 0) return '无'

        const displayIds = ids.slice(0, maxDisplay)
        const idsText = displayIds.join(', ')

        if (ids.length > maxDisplay) {
          return `${idsText}... (共${ids.length}条)`
        }
        return idsText
      }

      const basicFileIds = formatIdList(previewData.basicFileBaseIds)
      const combinedTotalIds = formatIdList(previewData.combinedTotalIds)
      const sampleRuleIds = formatIdList(previewData.sampleRuleBaseIds)

      this.$alert(
        `<div style="text-align: left; max-height: 400px; overflow-y: auto;">
          <p><strong>刪除影響預覽：</strong></p>
          <p style="color: #E6A23C; margin: 10px 0;">${impactInfo}</p>

          <div style="margin: 15px 0;">
            <p style="color: #606266; font-weight: bold; margin-bottom: 5px;">
              • 基礎文件記錄：${previewData.basicFileBaseIds ? previewData.basicFileBaseIds.length : 0} 条
            </p>
            <p style="color: #909399; font-size: 12px; margin-left: 15px; word-break: break-all;">
              ID: ${basicFileIds}
            </p>
          </div>

          <div style="margin: 15px 0;">
            <p style="color: #606266; font-weight: bold; margin-bottom: 5px;">
              • 合併總表記錄：${previewData.combinedTotalIds ? previewData.combinedTotalIds.length : 0} 条
            </p>
            <p style="color: #909399; font-size: 12px; margin-left: 15px; word-break: break-all;">
              ID: ${combinedTotalIds}
            </p>
          </div>

          <div style="margin: 15px 0;">
            <p style="color: #606266; font-weight: bold; margin-bottom: 5px;">
              • 抽樣規則記錄：${previewData.sampleRuleBaseIds ? previewData.sampleRuleBaseIds.length : 0} 条
            </p>
            <p style="color: #909399; font-size: 12px; margin-left: 15px; word-break: break-all;">
              ID: ${sampleRuleIds}
            </p>
          </div>

          <p style="color: #F56C6C; font-weight: bold; margin-top: 20px;">確定要刪除嗎？</p>
        </div>`,
        '刪除確認',
        {
          confirmButtonText: '確定刪除',
          showCancelButton: true,
          cancelButtonText: '取消',
          dangerouslyUseHTMLString: true,
          callback: action => {
            if (action === 'confirm') {
              confirmCallback()
            }
          }
        }
      )
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__title {
  font-weight: 700;
}
.oneLine {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
