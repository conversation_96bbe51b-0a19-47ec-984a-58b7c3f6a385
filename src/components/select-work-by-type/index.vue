<template>
<div>
    <el-dialog :visible.sync="show" width="1000px" title="Work" :close-on-click-modal="false">
        <div style="width: 560px;margin: auto;margin-bottom: 20px">
            <el-input v-model="searchInfo.title" @keyup.enter.native="onSubmit()" placeholder="Title" style="width: 220px;"></el-input>
            <el-input v-model.trim="searchInfo.workId" @keyup.enter.native="onSubmit()" placeholder="Work No" style="width: 128px;"></el-input>
            <el-input v-model.trim="searchInfo.soc" @keyup.enter.native="onSubmit()" placeholder="Soc" style="width: 60px;"></el-input>
            <el-button type="primary" @click="onSubmit()">查詢</el-button>
            <span class="clear-search" @click="clearSearch()">清除搜索</span>
        </div>
        <el-table  :data="tableData" :empty-text="emptyText">
            <el-table-column property="title" label="Title">
                <template slot-scope="scope">
                    {{scope.row.title||scope.row.title_en}}
                </template>
            </el-table-column>
            <el-table-column property="work_id" label="WorkNo"></el-table-column>
            <el-table-column property="work_society_code" label="Soc"></el-table-column>
            <el-table-column property="genre_code" label="Genre"></el-table-column>
            <el-table-column
                label="operation"
                width="100">
                <template slot-scope="scope">
                    <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                        <i class="el-icon-check" @click="checkedWork(scope.$index,scope.row)"></i>
                    </span>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :total="total" @current-change="onSubmit" :current-page="currentPage">
        </el-pagination>
    </el-dialog>
</div>
</template>
<script>
export default {
    data(){
        return{
            show:false,
            searchInfo: {},
            tableData: [],
            total: 0,
            currentPage: 1,
            emptyText: '暫無數據'
        }
    },
    props: {
        search: {
            type: Object,
            default: () => {
                return {
                    // title: '',
                    workSoc: '',
                    workNo: ''
                }

            }
        }
    },
    watch: {
        'searchInfo.workNo': function(){
            if(this.searchInfo.workNo){
                this.searchInfo.workNo = (this.searchInfo.workNo + '').replace(/[^0-9]/g, '');
            }
        },
        'searchInfo.workSoc': function(){
            if(this.searchInfo.workSoc){
                this.searchInfo.workSoc = (this.searchInfo.workSoc + '').replace(/[^0-9]/g, '');
            }
        }
    },

    methods: {
        /**
         * dialog 组件调用方式： 
         * 1、父级用v-if在html里加载组件,
         * 2、父级调用子级的init方法，将数据初始化，prop传给data等
         * 
         */
        init(){
            this.show = true;
            this.searchInfo = this.$utils.copy(this.search);
            this.onSubmit();
        },
        clearSearch(){
            this.searchInfo = {
                // title: '',
                workSoc: '',
                workNo: ''
            };
            this.onSubmit();
        },
        onSubmit(page){
            let params = this.searchInfo;
            // params.title = params.title ? params.title.trim() : params.title;
            params.pageNum = page ? page : 1;
            params.pageSize = 10;
            this.emptyText = '數據加載中';
            this.$http.get('/wrk/getWrkWorkByTypeFromEs', {params: params}).then(res => {
                if (res.success) {
                    this.tableData = res.data.list;
                    if(! this.tableData || this.tableData.length == 0){
                        this.emptyText = '暫無數據';
                    }
                    this.total = res.data.total;
                    this.currentPage = page ? page : 1;
                }
            })
        },
        checkedWork(index, row){
            this.$emit('checkWork', row);
            this.show = false;
        }
    }
    
}
</script>
<style lang="scss" scoped>
    /deep/ .el-dialog__title{
        font-weight: bold;
    }
    /deep/ .el-dialog__body{
        text-align: center;
        padding-top: 0;
    }
</style>