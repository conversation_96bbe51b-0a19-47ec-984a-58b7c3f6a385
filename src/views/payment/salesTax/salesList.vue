<template>
  <div>
    <el-form :inline="true" class="demo-form-inline">
      <el-form-item>
        <el-button
          type="primary"
          @click="addSales"
          v-if="isAuth('payment:salesTax:salesList:add')"
          >新 增</el-button
        >
      </el-form-item>
    </el-form>
    <el-table
      :empty-text="tableresult"
      :data="tableData"
      border
      style="width: 100%"
    >
      <el-table-column prop="id" label="PID"></el-table-column>
      <el-table-column prop="flagCommit" label="status">
        <template slot-scope="scope">
          {{ scope.row.flagCommit == "C" ? "commit" : "process" }}
        </template>
      </el-table-column>
      <el-table-column prop="createUser" label="Create User"></el-table-column>
      <el-table-column prop="createTime" label="Create Time" min-width="100">
        <template slot-scope="scope">
          {{ $utils.DATE(scope.row.createTime, "yyyyMMdd") }}
        </template>
      </el-table-column>
      <el-table-column label="operation" width="220">
        <template slot-scope="scope">
          <span
            class="a-blue"
            @click="view(scope.row)"
            v-if="isAuth('payment:salesTax:salesList:view')"
            >view</span
          >
          <span
            v-if="
              isAuth('payment:salesTax:salesList:commit') &&
              scope.row.flagCommit != 'C'
            "
            class="a-blue"
            >|</span
          >
          <span
            v-if="
              isAuth('payment:salesTax:salesList:commit') &&
              scope.row.flagCommit != 'C'
            "
            class="a-blue"
            @click="commit(scope.row)"
            >commit</span
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      background
      layout="prev, pager, next"
      :total="total"
      @current-change="queryList"
      :current-page="currentPage"
    >
    </el-pagination>
    <el-dialog
      title="新增支付"
      :visible.sync="addSalesVisible"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        :inline="true"
        ref="editForm"
        :model="SalesTaxBase"
        label-width="120px"
        label-position="right"
        class="demo-form-inline"
        :rules="rules"
        @keyup.enter.native="editConfirm"
      >
        <div>
          <!-- <el-form-item label="Create User" prop="createUser">
                        <el-input v-model="SalesTaxBase.createUser" placeholder=""></el-input>
                    </el-form-item> -->
          <el-form-item label="Tax Rate" prop="taxRate">
            <el-input v-model="SalesTaxBase.taxRate" placeholder=""></el-input>
          </el-form-item>
        </div>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancelEdit">取 消</el-button>
        <el-button type="primary" @click="editConfirm">確 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
export default {
  name: "salesTaxList",
  data() {
    return {
      total: 1,
      currentPage: 1,
      tableData: [],
      tableresult: " ",
      addSalesVisible: false,
      SalesTaxBase: {
        taxRate: null,
      },
      rules: {
        createUser: [{ required: true, message: "请输入", trigger: "blur" }],
        taxRate: [{ required: true, message: "请输入", trigger: "blur" }],
      },
    };
  },
  mounted() {
    this.queryList();
  },
  methods: {
    cancelEdit() {
      this.addSalesVisible = false;
    },
    editConfirm() {
      this.SalesTaxBase.taxRate = Number(this.SalesTaxBase.taxRate);
      this.$http
        .post("/dist/autopay/salesTaxBase/add", this.SalesTaxBase)
        .then((res) => {
          console.log(res.data);
          if (res.success) {
            if (res.data.code == 200) {
              this.$toast({ tips: "操作成功" });
              this.addSalesVisible = false;
              this.SalesTaxBase.taxRate = null;
              this.queryList();
            } else {
              this.$toast({ tips: res.data.message });
            }
          }
        });
    },
    addSales() {
      this.$msgbox
        .confirm("確定新增?", "提示", {
          confirmButtonText: "確定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(() => {
          this.$http
            .post("/dist/autopay/salesTaxBase/add", this.SalesTaxBase)
            .then((res) => {
              console.log(res.data);
              if (res.success) {
                if (res.data.code == 200) {
                  this.$toast({ tips: "操作成功" });
                  this.addSalesVisible = false;
                  this.SalesTaxBase.taxRate = null;
                  this.queryList();
                } else {
                  this.$toast({ tips: res.data.message });
                }
              }
            });
        })
        .catch(() => {});
    },
    queryList(num) {
      let ajaxData = {
        pageNum: num ? num : 1,
        pageSize: 10,
      };
      ajaxData = Object.assign(ajaxData, this.formNum);
      const loading = this.$loading();
      this.tableresult = "數據加載中...";
      this.$http
        .get("/dist/autopay/salesTaxBase/list", { params: ajaxData })
        .then((res) => {
          if (res.success) {
            console.log(res.data.data);
            this.tableData = res.data.data.list;
            this.total = res.data.data.total;
            this.currentPage = num;
            // todo-guan  分页
            loading.close();
          }
          this.tableresult = this.tableData.length == 0 ? "暫無數據" : " ";
        });
    },
    view(data) {
      console.log(data);
      this.$router.push({ name: "invoice", query: { id: data.id } });
    },
    commit(data) {
      this.$msgbox
        .confirm("是否確定commit此數據？", "提示", {
          confirmButtonText: "確定",
          cancelButtonText: "取消",
          type: "warning",
          closeOnClickModal: false,
        })
        .then(() => {
          this.$http
            .post(`/dist/autopay/salesTaxBase/commit/${data.id}`)
            .then((res) => {
              if (res.success) {
                if (res.data.code == 200) {
                  this.$toast({ tips: "操作成功" });
                  this.queryList();
                } else {
                  this.$toast({ tips: res.data.message });
                }
              }
            });
        })
        .catch(() => {});
    },
  },
};
</script>
<style scoped>
</style>