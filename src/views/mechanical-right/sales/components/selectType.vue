<template>
<div>
    <el-dialog :visible.sync="show" width="700px" title="Type" :close-on-click-modal="false">
        <el-table
            :data="tableData"
            border
            stripe
            style="width: 100%"
            v-loading="loading">
            <el-table-column
                prop="typeCode"
                label="Type Code">
            </el-table-column>
            <el-table-column
                prop="typeName"
                label="type name">
            </el-table-column>
            <el-table-column
                label="operation"
                width="150px">
                <template slot-scope="scope">
                    <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                        <i class="el-icon-check" @click="checkedType(scope.$index,scope.row)"></i>
                    </span>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :total="total" @current-change="onSubmit" :current-page="currentPage">
        </el-pagination>
    </el-dialog>
</div>
</template>
<script>

export default {
    data(){
        return{
            show:false,
            tableData: [],
            total: 0,
            currentPage: 1 ,
            loading:false
        }
    },
    methods: {
        /**
         * dialog 组件调用方式： 
         * 1、父级用v-if在html里加载组件,
         * 2、父级调用子级的init方法，将数据初始化，prop传给data等
         * 
         */
        init(){
            this.show = true;
            this.onSubmit();
        },
        onSubmit(page){
            let ajaxData = {};
            ajaxData.page_num = page ? page : 1;
            ajaxData.page_size = 10;
            this.loading = true;
            this.$http.get('/dist/mrd/listDistMrdScTypeWithPage', {params: ajaxData}).then( res => {
                this.loading = false;
                if(res.success){
                    this.tableData = res.data.data.list;
                    this.total = res.data.data.total;
                    this.currentPage = ajaxData.page_num;
                }
            })
        },
        checkedType(index, row){
            this.$emit('checkedType', row);
            this.show = false;
        }
    }
    
}
</script>
<style lang="scss" scoped>
    /deep/ .el-dialog__title{
        font-weight: bold;
    }
    /deep/ .el-dialog__body{
        text-align: center;
        padding-top: 0;
    }
</style>