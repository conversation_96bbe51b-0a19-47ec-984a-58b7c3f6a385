<template>
  <div>
    <el-form :inline="true" :model="searchForm" class="demo-form-inline"   @keyup.enter.native='getList()'>
      <el-form-item label="資料夾">
        <el-input v-model="searchForm.folder" placeholder="" style="width: 150px"></el-input>
      </el-form-item>
      <el-form-item label="category code">
        <el-input v-model="searchForm.categoryCode" placeholder="" style="width: 150px"></el-input>
      </el-form-item>
      <el-form-item label="模板類別">
        <el-select v-model="searchForm.type" placeholder="请选择">
          <el-option label="全部" value=""></el-option>
          <el-option label="默認模板" value="0"></el-option>
          <el-option label="自定義模板" value="1"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList()" v-if="isAuth('list-manage:template-config:template-config:find')">搜索</el-button>
        <el-button type="success" @click="edit({},'新增')" v-if="isAuth('list-manage:template-config:template-config:add')">新增</el-button>

      </el-form-item>
      <el-form-item>
        <span class="clear-search" @click="clearSearch()" v-if="isAuth('list-manage:template-config:template-config:find')">清除搜索</span>
      </el-form-item>
    </el-form>
    <el-table :empty-text="tableresult"   :data="tableData" border stripe style="width: 100%">
      <el-table-column prop="folder" label="資料夾">
      </el-table-column>
      <el-table-column prop="categoryCode" label="category code">
      </el-table-column>
      <el-table-column prop="line" width="120" label="表頭所在行">
      </el-table-column>
      <el-table-column prop="uploadType" width="120" label="upload type">
      </el-table-column>
      <el-table-column prop="remark" label="備註">
        <template slot-scope="scope">
          <span :title="scope.row.remark">{{scope.row.remark}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="type" label="模板類別">
        <template slot-scope="scope">
          {{templateType[scope.row.type]}}
        </template>
      </el-table-column>
      <el-table-column prop="type" label="模式類別">
        <template slot-scope="scope">
          {{scope.row.parsingMode == 0?'嚴格模式':'寬鬆模式'}}
        </template>
      </el-table-column>
      <el-table-column prop="processingMethod" label="處理方式">
        <template slot-scope="scope">
          {{scope.row.processingMethod == 0?'自動比對':'合併抽樣後比對'}}
        </template>
      </el-table-column>
      <el-table-column label="operation" align="center" width="260px">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="configRow(scope.row)" v-if="isAuth('list-manage:template-config:template-config:config')">配製解析模板</el-button>
          <el-button type="text" size="small" @click="edit(scope.row,'編輯')" v-if="isAuth('list-manage:template-config:template-config:change')">編輯</el-button>
          <el-button type="text" size="small" @click="deleteRow(scope.row.id)" v-if="isAuth('list-manage:template-config:template-config:del')">刪除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination background layout="prev, pager, next" :total="total" @current-change="handleCurrentChange">
    </el-pagination>
    <el-dialog :title="dialogTitle" :visible.sync="dialogFormVisible" width="40%" @close="clearForm" :close-on-click-modal="false">
      <el-form :model="editForm" :rules="rules" ref="addDialog" label-position="right" label-width="140px">
        <el-form-item label="資料夾" prop="folder">
          <el-input v-model="editForm.folder" placeholder=""></el-input>
        </el-form-item>
        <el-form-item label="category code" prop="categoryCode">
          <el-input v-model="editForm.categoryCode" placeholder=""></el-input>
        </el-form-item>
        <el-form-item label="表頭所在行" prop="line">
          <el-input v-model="editForm.line" placeholder=""></el-input>
        </el-form-item>
        <el-form-item label="備註">
          <el-input v-model="editForm.remark" placeholder=""></el-input>
        </el-form-item>
        <el-form-item label="upload type">
          <el-select v-model="editForm.uploadType" placeholder="请选择" clearable>
            <!--                        <el-option label="NULL" value=""></el-option>-->
            <el-option label="PG" value="PG"></el-option>
            <el-option label="MS" value="MS"></el-option>
            <el-option label="FW" value="FW"></el-option>
            <el-option label="CJ" value="CJ"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="解析模式">
          <el-radio-group v-model="radioType">
            <el-radio :label="1">嚴格模式</el-radio>
            <el-radio :label="2">寬鬆模式</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="導入後處理方式">
          <el-radio-group v-model="editForm.processingMethod">
            <el-radio :label="0">自動比對</el-radio>
            <el-radio :label="1">合併後抽樣比對</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="editConfirm">确 定</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
export default {
  name: 'template-config',
  data() {
    return {
      total: 0,
      searchForm: {
        page: {
          pageNum: 1,
          pageSize: 10
        },
        categoryCode: '',
        type: '',
        folder: ''
      },
      tableData: [],tableresult:' ',
      dialogFormVisible: false,
      dialogTitle: '新增',
      radioType: 1,
      editForm: {
        categoryCode: "",
        folder: "",
        line: 0,
        remark: "",
        parsingMode: 0,
        processingMethod: 0
      },
      templateType: {
        0: '默認模板',
        1: '自定義模板'
      },
      rules: {
        folder: [{ required: true, message: '請輸入', trigger: 'blur' }],
        categoryCode: [{ required: true, message: '請輸入', trigger: 'blur' }],
        line: [{ required: true, message: '請輸入', trigger: 'blur' }],
      },

    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    clearSearch() {
      this.searchForm = {
        page: {
          pageNum: 1,
          pageSize: 10
        },
        categoryCode: '',
        parsingMode: '',
        folder: ''
      }
      this.getList()
    },
    getList(page = 1) {
      if (page == 1) {
        this.total = 0
      }
      this.searchForm.page.pageNum = page
                        this.tableresult = '數據加載中...'
      this.$http.post('/listBasicFileTemplate/searchTemplate', this.searchForm).then(res => {
        if (res.data.code == 200) {
          this.tableData = res.data.data.list
          this.total = res.data.data.total
        }
          this.tableresult = this.tableData.length == 0 ? '暫無數據' : ' '
      })
    },
    editConfirm() {
      let url = ''
      if (this.dialogTitle == '編輯') {
        url = '/listBasicFileTemplate/updateTemplate'
      } else {
        url = '/listBasicFileTemplate/addTempate'
      }
      this.$refs.addDialog.validate(validate => {
        if (validate) {
          console.log(this.editForm)
          this.editForm.parsingMode = this.radioType - 1
          this.$http.post(url, this.editForm).then(res => {
            if (res.data.code == 200) {
              this.dialogFormVisible = false
              this.getList()
            } else {
              this.$toast({ tips: res.data.message })
            }
          })
        }
      })

    },
    edit(row, type) {
      this.dialogTitle = type
      this.dialogFormVisible = true
      if (row.id) {
        this.editForm = this.$utils.copy(row)
        this.radioType = Number(this.editForm.parsingMode) + 1
      } else {
        this.editForm = {
          categoryCode: "",
          folder: "",
          line: 0,
          remark: "",
          processingMethod: 0
        }
      }
    },
    clearForm() {
      this.$refs.addDialog && this.$refs.addDialog.clearValidate()
    },
    deleteRow(id) {
      this.$alert('確定要刪除嗎', '刪除', {
        confirmButtonText: '确定',
        showCancelButton: true,
        cancelButtonText: '取消',
        callback: action => {
          if (action === 'confirm') {
            this.$http.delete('/listBasicFileTemplate/delete/' + id).then(res => {
              if (res.data.code == 200) {
                this.getList()
              } else {
                this.$toast({ tips: res.data.message })
              }
            }).catch(res => {
              this.$toast({ tips: res.data.message })
            })
          }
        }
      });
    },
    configRow(row) {
      this.$router.push({ name: 'templateConfigDetail', query: { nameId: row.id, title: row.folder } })
    },

    handleCurrentChange(page) { 
      this.getList(page)
    }
  }
}
</script>

<style scoped lang="scss">
/deep/ .el-form-item__label {
  margin-left: 0 !important;
}
</style>
