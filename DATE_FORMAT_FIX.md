# 日期格式问题修复说明

## 问题描述

在自定义日期编辑功能中，从预览组件复制日期到编辑组件后，无法正常取消选中复制过来的日期。

## 问题原因

这个问题的根本原因是**日期格式不一致**导致的比较失败：

1. **后端数据格式**: 从API获取的日期可能包含时间部分，如 `"2024-01-15 00:00:00"`
2. **formatTime处理**: 经过`formatTime`方法处理后变成 `"2024-01-15"`
3. **vue-calendar-component格式**: 日历组件传递的日期格式可能不同
4. **字符串比较失败**: 原有的`indexOf`方法无法正确匹配不同格式的相同日期

### 具体问题场景

```javascript
// 原有的问题代码
customChooseDay(date) {
  let index = this.customMarkDate.indexOf(date)  // 这里会失败
  // 因为 date 可能是 "2024-01-15"
  // 而 this.customMarkDate 中的日期可能是其他格式
}
```

## 解决方案

### 1. 添加日期标准化方法

新增`normalizeDate`方法，统一将所有日期格式转换为`YYYY-MM-DD`格式：

```javascript
normalizeDate(date) {
  if (!date) return ''
  
  // 如果已经是 YYYY-MM-DD 格式
  if (typeof date === 'string' && date.match(/^\d{4}-\d{2}-\d{2}$/)) {
    return date
  }
  
  // 如果是 YYYYMMDD 格式
  if (typeof date === 'string' && date.match(/^\d{8}$/)) {
    return `${date.substring(0, 4)}-${date.substring(4, 6)}-${date.substring(6, 8)}`
  }
  
  // 如果是 Date 对象
  if (date instanceof Date) {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  }
  
  // 其他格式尝试转换
  try {
    const dateObj = new Date(date)
    if (!isNaN(dateObj.getTime())) {
      const year = dateObj.getFullYear()
      const month = String(dateObj.getMonth() + 1).padStart(2, '0')
      const day = String(dateObj.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    }
  } catch (e) {
    console.warn('无法解析日期格式:', date)
  }
  
  return String(date)
}
```

### 2. 修改日期选择逻辑

将原有的`indexOf`比较改为使用`findIndex`和标准化比较：

```javascript
// 修复后的代码
customChooseDay(date) {
  // 标准化日期格式进行比较
  const normalizedDate = this.normalizeDate(date)
  const index = this.customMarkDate.findIndex(d => this.normalizeDate(d) === normalizedDate)
  
  if (index === -1) {
    this.customMarkDate.push(normalizedDate);
  } else {
    this.customMarkDate.splice(index, 1);
  }
  this.customMarkDate = [...this.customMarkDate];
  this.$forceUpdate();
}
```

### 3. 修改复制逻辑

确保复制时也进行日期标准化：

```javascript
copyFromPreview() {
  // 标准化所有日期格式后复制
  this.customMarkDate = this.markDate.map(date => this.normalizeDate(date))
  this.customTargetYear = this.targetYear
  this.$message.success(`已从预览复制 ${this.customMarkDate.length} 个日期到自定义编辑`)
  // ...
}
```

### 4. 修改数据获取逻辑

确保从后端获取的数据也经过标准化：

```javascript
getDateList(id) {
  this.$http.get('/list2SampleRule/getListSampleRuleBaseById', { params: { baseId: id } }).then(res => {
    let list = res.data.listSampleRuleMappingList
    if (list.length) {
      this.markDate = []
      list.map(item => {
        // 使用标准化的日期格式
        const formattedDate = this.normalizeDate(this.formatTime(item.sampleDate))
        this.markDate.push(formattedDate)
      })
      // ...
    }
  })
}
```

## 调试信息

为了便于调试，在`customChooseDay`方法中添加了详细的控制台输出：

- 显示原始日期和标准化后的日期
- 显示比较过程
- 显示找到的索引
- 显示操作结果

## 测试验证

修复后，请按以下步骤测试：

1. 打开抽样规则编辑页面
2. 开启自定义编辑开关
3. 点击"从预览复制日期"
4. 在自定义编辑组件中点击已选中的日期
5. 验证日期是否能正常取消选中

## 注意事项

1. 所有日期统一使用`YYYY-MM-DD`格式存储和比较
2. 新增的调试信息可以在生产环境中移除
3. 如果发现其他日期格式，可以在`normalizeDate`方法中添加相应的处理逻辑
