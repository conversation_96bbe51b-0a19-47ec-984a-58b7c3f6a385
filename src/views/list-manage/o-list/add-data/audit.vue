<template>
  <div style="position: relative">
    <div class="main-left" :class="isCheckFile?'slide':''">
      <div class="file-info">
        <el-row>
          <el-col :span="3">
            <el-input v-model="fid" placeholder="FID" @blur="getFileInfo"></el-input>
            <!-- <el-input @keyup.enter.native='getCheckList(1)' v-model="fid" placeholder="FID"></el-input> -->
          </el-col>
          <el-col :span="21">
            <p class="detail">
              <span><b>Remit Soc：</b>{{info.remitSocietyCode == 0?info.remitSocietyCode:(info.remitSocietyCode || '-')}}</span>
              <span><b>File Name：</b>{{info.fileName || '-'}}</span>
              <span><b>Total：</b>{{info.totalAmount || '-'}}</span>
              <span><b>File Total：</b>{{info.fileTotalAmount || '-'}}</span>
            </p>
          </el-col>
        </el-row>
      </div>
      <el-form :model="searchData" :inline="true" @keyup.enter.native='getCheckList(1)'>
        <!-- <el-form-item>
          <el-input v-model="searchData.ListMatchDataOverseas.sourceWorkCode" placeholder="source work no"></el-input>
        </el-form-item> -->
        <el-form-item>
          <el-input v-model="searchData.ListMatchDataOverseas.matchWorkId" placeholder="Match Work No"></el-input>
        </el-form-item>
        <el-form-item>
          <el-input v-model="searchData.ListMatchDataOverseas.matchWorkSociety" placeholder="Match Work Soc"></el-input>
        </el-form-item>
        <el-form-item>
          <el-input v-model="searchData.ListMatchDataOverseas.originalTitle" placeholder="Remit Title"></el-input>
        </el-form-item>
        <el-form-item>
          <el-input v-model="searchData.ListMatchDataOverseas.fileName" placeholder="File Name"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getCheckList(1)" v-if="isAuth('list-manage:o-list:add-data:index:find')">搜索</el-button>
        </el-form-item>
        <el-form-item>
          <span class="clear-search" @click="clearSearch()" v-if="isAuth('list-manage:o-list:add-data:index:find')">清除搜索</span>
        </el-form-item>
        <!-- <el-form-item>
        </el-form-item> -->

      </el-form>
      <div class="raw-match-info">
        <el-table :empty-text="tableresult" :data="rawMatchInfoData" ref="singleTable" height="480" highlight-current-row>
          <el-table-column prop="id" label="ID" class="id">
          </el-table-column>
          <el-table-column label="Source Work No"  prop="sourceWorkCode">
          </el-table-column>
          <el-table-column label="Remit Title" prop="originalTitle">
          </el-table-column>
          <el-table-column label="Iswc" prop="iswc">
          </el-table-column>
          <el-table-column label="Match Work No" prop="matchWorkId">
          </el-table-column>
          <el-table-column label="Match Work Soc" prop="matchWorkSocietyCode">
          </el-table-column>
          <el-table-column label="Match Work Title" prop="matchWorkTitle">
          </el-table-column>
          <el-table-column label="status">
            <template slot-scope="scope">
              <span v-if="scope.row.status == 0">待匹配</span>
              <span v-else-if="scope.row.status == 1">已匹配</span>
              <span v-else-if="scope.row.status == 2">不匹配</span>
              <span v-else-if="scope.row.status == 3">IP匹配完毕</span>
            </template>
          </el-table-column>
          <el-table-column label="operation" fixed="right" width="120">
            <template slot-scope="scope">
              <el-button type="text" v-if="isAuth('list-manage:o-list:add-data:index:audit')&&scope.row.id" @click="initMatchInfo(scope.row,scope.$index)">審核</el-button>
              <el-button type="text" @click="deleteCheckList(scope.row,scope.$index)" v-if="isAuth('list-manage:o-list:add-data:index:del')">刪除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination background layout="prev, pager, next" :total="total" :current-page="searchData.page.pageNum" @current-change="handleCheckListChange">
        </el-pagination>

      </div>
    </div>
    <div class="main-right" :class="isCheckFile?'slide':''">
      <i class="el-icon-close close-btn" @click="closeCheckList"></i>
      <div>
        <h4>match work info</h4>
        <div>
          <el-form :model="workInfoData" :inline="true" @keyup.enter.native='showWorkDialog()'>
            <el-form-item>
              <el-input v-model="workInfoData.title" placeholder="title"></el-input>
            </el-form-item>
            <el-form-item>
              <el-input v-model="workInfoData.workId" placeholder="work no"></el-input>
            </el-form-item>
            <el-form-item>
              <el-input v-model="workInfoData.soc" placeholder="soc"></el-input>
            </el-form-item>

            <el-form-item>
              <el-button @click="showWorkDialog" type="primary">選擇查詢</el-button>
              <span class="clear-search" @click="initWorkInfo">清除搜索</span>
            </el-form-item>
            <el-row style="height: 36px;overflow:hidden;">
              <el-col :span="12">
                <p class="over-line work-info" :title="workInfoData.iswc"><b>iswc：</b>{{workInfoData.iswc}}</p>

              </el-col>
              <el-col :span="12">
                <p class="over-line work-info" :title="workInfoData.performer"><b>performer：</b>{{workInfoData.performer}}</p>
              </el-col>
            </el-row>
          </el-form>
          <el-table :empty-text="tableresult" :data="workInfoTable" height="240">
            <el-table-column prop="groupIndicator" label="gp">
            </el-table-column>

            <el-table-column prop="name" label="name">
            </el-table-column>
            <el-table-column prop="workIpRole" label="role">
            </el-table-column>
            <el-table-column prop="ipNameNo" label="ip name no">
            </el-table-column>
            <el-table-column prop="ipShare" label="ip share">
            </el-table-column>
            <el-table-column prop="sd" label="SD">
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div>
        <!-- @@@@@@@這個是最下面的框框zzzzzzz -->
        <div class="flex">
          <h4>raw ip share</h4>
          <div v-if="this.currentRow">
            <el-button type="primary" @click="addIpShare">新增</el-button>
            <el-button type="primary" @click="saveDetail">save</el-button>
          </div>
        </div>
        <el-table :empty-text="tableresult" :data="ipShareTable" height="250">
          <el-table-column type="index">
          </el-table-column>
          <el-table-column prop="ipName" width="145" label="IP Name">
            <template slot-scope="scope">
              <el-input v-model.trim="scope.row.ipName" :disabled='isdisabled' @blur="testipname(scope.row)"></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="workIpRole" label="Role" width="80">
            <template slot-scope="scope">
              <el-input v-model="scope.row.workIpRole"></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="ipNameNo" label="Ip Name No" width="160">
            <template slot-scope="scope">
              <el-input v-model.trim="scope.row.ipNameNo" @blur="testUser(scope.row)" maxlength="13"></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="shareRatio" label="Share" width="160">
            <template slot-scope="scope">
              <el-input v-model.trim="scope.row.shareRatio"></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="amount" label="Amount" width="100">
            <template slot-scope="scope">
              <el-input v-model="scope.row.amount"></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="match status" width="120">
            <template slot-scope="scope">
              <span v-if="scope.row.status == 0">待匹配</span>
              <span v-if="scope.row.status == 1">已匹配</span>
              <span v-if="scope.row.status == 2">不匹配</span>
            </template>
          </el-table-column>
          <el-table-column prop="matchIpNameNo" label="match ip name no" width="100">
          </el-table-column>
          <el-table-column prop="matchIpName" label="match ip name" width="100">
          </el-table-column>
          <el-table-column prop="description" label="description" width="70px">
            <template slot-scope="scope">
              <span class="over-line">{{scope.row.description}}</span>
            </template>
          </el-table-column>
          <el-table-column label="operation" fixed="right" width="140">
            <template slot-scope="scope">
              <el-button type="text" @click="setCheckStatus(scope.row)">匹配</el-button>
              <el-button type="text" @click="deleteIpShareList(scope.row,scope.$index)">刪除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <el-dialog title="選擇" :visible.sync="exportVisible2" width="1045px" :close-on-click-modal="false">
      <el-table :empty-text="tableresult" stripe @row-click="singleElection" :data="tableDatacheck" height="300" border style="width: 100%">
        <el-table-column prop="groupIndicator" label="gp">
        </el-table-column>

        <el-table-column prop="name" label="name">
        </el-table-column>
        <el-table-column prop="workIpRole" label="role">
        </el-table-column>
        <el-table-column prop="ipNameNo" label="ip name no">
        </el-table-column>
        <el-table-column prop="ipShare" label="ip share">
        </el-table-column>
        <el-table-column prop="sd" label="SD">
        </el-table-column>

        <!-- <el-table-column type="selection" label="选择" width="55"> </el-table-column> -->
        <el-table-column label="" width="65">
          <template slot-scope="scope">
            <el-radio class="radio" v-model="templateSelection" :label="scope.$index">&nbsp;</el-radio>
          </template>
        </el-table-column>
      </el-table>
      <!-- <el-pagination background layout="prev, pager, next" :total="totalcheck" @current-change="handleCurrentChangecheck">
      </el-pagination>
      <div style="width: 520px; display: flex; flex-direction: row-reverse">
        <el-button type="primary" @click="onSubmit()">確定</el-button>
        <el-button style="margin-right: 10px" @click="cancelselect()">取消</el-button>
      </div> -->
    </el-dialog>
    <el-dialog title="匹配設置" :visible.sync="dialogVisible" width="60%">
      <el-tabs v-model="dialogActiveName">
        <el-tab-pane label="匹配" name="first">
          <el-table :empty-text="tableresult" :data="workInfoTable">
            <el-table-column prop="groupIndicator" label="gp" width="45">
            </el-table-column>
            <el-table-column prop="ipNameNo" label="ip name no">
            </el-table-column>
            <el-table-column prop="name" label="name">
            </el-table-column>
            <el-table-column prop="workIpRole" label="role" width="45">
            </el-table-column>
            <el-table-column prop="ipShare" label="ip share" width="85">
            </el-table-column>
            <el-table-column prop="sd" label="SD" width="45">
            </el-table-column>
            <el-table-column label="operation">
              <template slot-scope="scope">
                <el-button type="text" @click="checkMatchIpShare(scope.row)">匹配</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="不匹配" name="second">
          <el-input type="textarea" v-model="description" placeholder="未匹配理由" resize="none" rows="4"></el-input>
          <div class="description">
            <el-button type="primary" @click="saveDescription">保存</el-button>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
    <selectWork ref="selectWork" :search="{workId:workInfoData.workId,title:workInfoData.title,soc:workInfoData.soc}" @checkWork="checkWork"></selectWork>
  </div>
</template>

<script>
import selectWork from '@/components/select-work'
//import axios from '../../../../utils/httpRequest'
import axios from "axios";
export default {
  name: "index",
  data() {
    return {
      testfid: false,
      ischecked: false,
      isdisabled: true,
      fid: '',
      info: {},
      tempparams: {},
      templateSelection: -1,
      exportVisible2: false,
      isCheckFile: false,
      isFirstSwitch: true,
      fileList: [],
      searchData: {
        ListMatchDataOverseas: {
          status: null,
        },
        page: {
          pageNum: 1,
          pageSize: 10
        }
      },
      currentRow: null,
      currentIndex: null,
      rawMatchInfoData: [],
      total: 0,
      totalMatchInfoTable: {}, //記錄已經翻頁的rawMatchInfoData
      workInfoData: {
        workId: '',
        title: '',
        soc: '',
        iswc: ''
      },
      deletearry:[],
      currentWorkInfo: {},
      tableDatacheck: [],
      workInfoTable: [], tableresult: ' ',
      ipShareTable: [],
      dialogVisible: false,
      dialogActiveName: 'first',
      workDialogVisible: false,
      targetIpShareRow: null,
      description: '',
    }
  },
  components: {
    selectWork
  },
  watch: {
    exportVisible2(newName, oldName) {
      if (!newName) {
        this.templateSelection = -1

      }
      else {

      }
    },
  },
  methods: {
    clearSearch() {
      this.searchData = {
        ListMatchDataOverseas: {
          status: null,
          fileBaseId:''
        },
        page: {
          pageNum: 1,
          pageSize: 10
        }
      },
      this.fid=''
      this.info={}
       this.rawMatchInfoData=[]
       this.tableresult='暫無數據'
        // this.getCheckList(1)
      // this.$router.push({ name: 'oAddData'})
    },
    checkFid() {
      if (!this.fid) {
        this.$toast({ tips: '請輸入FID' })
        return false
      } else {
        return true
      }
    },
    getFileInfo() {
      if (this.checkFid()) {
        this.searchData.ListMatchDataOverseas.fileBaseId = this.fid
        this.tableresult = '數據加載中...'
        this.$http.get('/listOverseasFileBase/get/' + this.fid).then(res => {
          console.log(res)
          if (res.data.code == 200) {
            this.testfid = true
            this.info = res.data.data
            this.getCheckList()
          } else {
            this.info = {}
            this.rawMatchInfoData = []
            this.$toast({ tips: res.data.message })
          }
          this.tableresult = this.rawMatchInfoData.length == 0 ? '暫無數據' : ' '
        })
      }

    },
    //獲取待審核列表
    getCheckList(page = 1) {
      if (isNaN(this.fid)) {
        this.$toast({ tips: '請輸入正確的fid' })
        return
      }
       if (this.checkFid()) {
        this.searchData.ListMatchDataOverseas.fileBaseId = this.fid
        this.tableresult = '數據加載中...'
        this.$http.get('/listOverseasFileBase/get/' + this.fid).then(res => {
          console.log(res)
          if (res.data.code == 200) {
            this.testfid = true
            this.info = res.data.data
          } else {
            this.info = {}
            this.rawMatchInfoData = []
            this.$toast({ tips: res.data.message })
            // return
          }
          this.tableresult = this.rawMatchInfoData.length == 0 ? '暫無數據' : ' '
        })
      }
      this.searchData.ListMatchDataOverseas.fileBaseId = this.fid
      if (page == 1 || this.searchData.ListMatchDataOverseas.sourceWorkCode 
      || this.searchData.ListMatchDataOverseas.originalTitle 
      || this.searchData.ListMatchDataOverseas.status) {
        this.searchData.page.pageNum = 1
      }
      if (this.checkFid() && !this.isCheckFile) {
        this.tableresult = '數據加載中...'
         this.$http.post('/listmatchoverseas/getManualListMatchDataOverseasList', this.searchData).then(res => {
          console.log(res)
          if (res.success && res.data.code == 200) {
            let list =  res.data.data.list
            this.rawMatchInfoData = list
            this.total = res.data.data.total
          }
          this.tableresult = this.rawMatchInfoData.length == 0 ? '暫無數據' : ' '
        })
      }

    },
    testipname(params) {

    },
    testUser(params) {
      let usrpar = {
        ipName: '',
        workIpRole: '',
        ipNameNo: '',
        shareRatio: '',
        amount: '',
        status: '',
        matchIpNameNo: '',
        description: '',

      }

      let temparr = this.workInfoTable.filter((item, i, arr) => item.ipNameNo == params.ipNameNo)
      if (temparr.length < 1) {
        // this.$toast({ tips: '請填寫正确的Ip Name No' })
        return
      }
      console.warn("777***", params, ')))', this.ipShareTable, '(((', this.workInfoTable, '=-===', temparr)
      // if (temparr.length > 1&&this.templateSelection==-1) {
      //   this.exportVisible2 = true
      //   this.tableDatacheck = temparr
      //   this.tempparams=params

      // }
      // if (temparr.length > 1&&this.templateSelection>-1) {

      //   this.ipShareTable.forEach((item, index, arr) => {
      //     if (arr[index].ipNameNo == params.ipNameNo.trim()) {
      //       console.warn('------', temparr[this.templateSelection].name)
      //       arr[index].ipName = temparr[this.templateSelection].name
      //       arr[index].shareRatio = temparr[this.templateSelection].ipShare
      //       arr[index].workIpRole = temparr[this.templateSelection].workIpRole
      //     }

      //   })
      //   this.ischecked = true
      //   this.ipShareTable = JSON.parse(JSON.stringify(this.ipShareTable))
      //   console.warn("%%%", this.ipShareTable, '&&&', temparr)

      // }
      // if (temparr.length == 1) {
      this.ipShareTable.forEach((item, index, arr) => {
        if (arr[index].ipNameNo == params.ipNameNo.trim()) {
          console.warn('------', temparr[0].name)
          arr[index].ipName = temparr[0].name
          arr[index].shareRatio = temparr[0].ipShare
          arr[index].workIpRole = temparr[0].workIpRole
        }

      })
      this.ischecked = true
      this.ipShareTable = JSON.parse(JSON.stringify(this.ipShareTable))
      console.warn("%%%", this.ipShareTable, '&&&', temparr)

      // }

    },
    singleElection(row) {

      this.templateSelection = this.tableDatacheck.indexOf(row);
      // console.log('-------row',row);
      console.log("^^^", this.templateSelection, row, this.tempparams);
      this.exportVisible2 = false
      this.testUser(this.tempparams)

    },
    addCheckList() {
      if (this.checkFid()) {
        if (!this.testfid) {
          this.searchData.ListMatchDataOverseas.fileBaseId = this.fid
          this.tableresult = '數據加載中...'
          this.$http.get('/listOverseasFileBase/get/' + this.fid).then(res => {
            console.log(res)
            if (res.data.code == 400) {
              if(res.data.message){
                this.$toast({ tips: res.data.message })
              } else {
                this.$toast({ tips: '此FID無法新增' })
              }
              this.tableresult = ''
              return
            }
            if (res.data.code == 200) {
              this.testfid = true
              this.info = res.data.data
              if (this.searchData.ListMatchDataOverseas.sourceWorkCode 
              || this.searchData.ListMatchDataOverseas.originalTitle 
              || this.searchData.ListMatchDataOverseas.status) {
                this.searchData.page.pageNum = 1
              }
              if (this.checkFid() && !this.isCheckFile) {
                this.$http.post('/listmatchoverseas/getManualListMatchDataOverseasList', this.searchData).then(res => {
                  console.log('--------------------', res)
                  if (res.success) {
                    let list = res.data.data.list
                    this.rawMatchInfoData = list
                    this.rawMatchInfoData.unshift({ status: 0 })
                    this.total = res.data.data.total
                  }
                })
              }
            } else {
              this.info = {}
              this.rawMatchInfoData = []
              this.$toast({ tips: res.data.message })
            }
            if (this.testfid) {
              this.rawMatchInfoData.unshift({ status: 0 })
            }
            this.tableresult = this.rawMatchInfoData.length == 0 ? '暫無數據' : ' '
          })
          return
        }
        if (this.testfid) {
          this.rawMatchInfoData.unshift({ status: 0 })
        }
      }
    },
    deleteCheckList(row, index) {
      console.log('-----row', row);
      if (row.status == 0) {

        this.$alert('確定要刪除嗎？', '刪除', {
          confirmButtonText: '确定',
          showCancelButton: true,
          cancelButtonText: '取消',
          callback: action => {
            if (action == 'confirm') {

              this.rawMatchInfoData.splice(index, 1)
              this.$toast({ tips: '删除成功' })
            }
            // this.tableresult = '數據加載中...'
            this.tableresult = this.rawMatchInfoData.length == 0 ? '暫無數據' : ' '
          }
        });
      }
      if (row.id) {
        this.$alert('確定要刪除嗎？', '刪除', {
          confirmButtonText: '确定',
          showCancelButton: true,
          cancelButtonText: '取消',
          callback: action => {
            if (action == 'confirm') {
              this.$http.delete('/listmatchoverseas/delete', { params: { id: row.id } }).then(res => {
                if (res.success) {
                  this.getCheckList()
                }
                this.$toast({ tips: res.data.message })
              })
            }
          }
        });
      }
    },
    handleCheckListChange(page) {
      // this.totalMatchInfoTable[page] = this.rawMatchInfoData
      this.searchData.page.pageNum = page
      this.getCheckList()
    },
    initMatchInfo(row, index) {
      console.warn('row===index', row, index)
      if (!row.sourceWorkCode) {
        this.$toast({ tips: '請填寫Source Work No' })
        return
      }
      if (!row.originalTitle) {
        this.$toast({ tips: '請填寫Original Title' })
        return
      }
      // if (!row.status) {
      //   this.workInfoTable = []
      //   this.$toast({ tips: '請填寫Original Title' })
      //   return
      // }
      // debugger
      if (!this.isCheckFile) {
        this.isCheckFile = true
        this.currentRow = row
        this.currentIndex = index
        this.workInfoData = {
          workId: row.matchWorkId,
          title: row.matchWorkTitle,
          soc: row.matchWorkSocietyCode
        }
        if (!row.status) {
          this.workInfoTable = []
          // return
        }
        // this.workInfoTable = []
        if (this.workInfoTable.length < 1) {
          // alert('^^^^^^^')
          let params = this.$utils.copy(this.workInfoData);
          params.title = params.title ? params.title.trim() : params.title;
          params.workId = params.workId ? String(params.workId).trim() : params.workId;
          params.soc = params.soc ? String(params.soc).trim() : params.soc;
          params.page = {
            pageNum: 1,
            pageSize: 10
          }
          // this.emptyText = '數據加載中'; 
          let initrow = {}
          this.$http.post('/wrk/queryWrkWorkListEs', params).then(res => {
            if (res.success) {
              initrow = res.data.list[0];
              // if(! this.tableData || this.tableData.length == 0){
              //     this.emptyText = '暫無數據';
              // }
              // this.total = res.data.total;
              // this.currentPage = page ? page : 1;
              //   console.warn('_____----------',res,initrow,this.ipShareTable)
              //     if (this.ipShareTable.length<1) {
              //     return
              // }
              //    alert('hahahah')
              this.getWorkInfo(initrow)
            }
          })
          // this.checkWork()
        }

        if (row.id) {
          // alert('00000')
          this.getcheckDetail(row)
          // if (this.ipShareTable.length<1) {
          //     alert('xxxxxxxx')
          //     this.workInfoTable=[]
          // }
        }

      } else {
        this.$toast({ tips: '请先保存' })
      }
    },
    getWorkInfo(row) {
      console.warn("initrowshow", row)
      this.tableresult = '數據加載中...'
      this.$http.get('/wrk/getWrkWorkById', { params: { workId: row.work_id, workSocietyCode: row.work_society_code } }).then(res => {

        if (res.success) {
          this.workInfoTable = res.data.wwisList[0].PER || []
          console.warn('this.workinfotable', this.workInfoTable)
          if (this.workInfoTable.length > 0) {

            let waList = res.data.waList
            let performer = []
            if (waList.length > 0) {
              // alert('111')
              waList.map(item => {
                performer.push(item.artistName || item.chineseName)
              })
              if (performer.length > 0) {
                this.workInfoData.performer = performer.join(',')
              }

            } else {
              // alert('222')
              this.workInfoData.performer = '--'
              this.workInfoData.iswc = '--'
            }
          }
          console.warn("*****", this.workInfoTable)
        }
        this.tableresult = this.workInfoTable.length == 0 ? '暫無數據' : ' '
      })
    },
    initWorkInfo() {
      this.workInfoData = {}
      this.workInfoTable = []

    },
    showWorkDialog() {
      this.$refs.selectWork.init()


    },
    checkWork(row) {
      console.warn("^^^^^", row)
      this.currentWorkInfo = row
      this.workInfoData = {
        workId: row.work_id,
        title: row.title,
        soc: row.work_society_code,
        iswc: row.iswc,
      }
      this.getWorkInfo(row)
    },
    getcheckDetail(row) {
      this.tableresult = '數據加載中...'
      this.$http.get('/listmatchoverseas/getListMatchDataOverseasMappingListByOverseasId', { params: { overseasId: row.id } }).then(res => {
        if (res.success) {
          this.ipShareTable = res.data.data
          if (this.ipShareTable.length < 1) {
            this.workInfoTable = []
            this.workInfoData = {
              workId: '',
              title: '',
              soc: '',
              iswc: '',
              performer: ''
            }
          }
          // console.warn('%%%%%', this.ipShareTable, this.workInfoTable)
        }
        this.tableresult = this.ipShareTable.length == 0 ? '暫無數據' : ' '
      })
    },
    addIpShare() {
      if (!this.currentRow) {
        return false
      }
      this.ipShareTable.unshift({ overseasId: this.currentRow.id, status: 0 })
    },
    saveDetail() {
      let canSave = true
      let sum = 0
      this.ipShareTable.map(item => {
        if (!item.id) {
          item.overseasId = this.currentRow.id
          item.fileBaseId = this.fid
          item.dataUniqueKey = this.currentRow.dataUniqueKey
        }
        if (!item.ipNameNo || !item.shareRatio) {
          this.$toast({ tips: '請完整填寫Ip Name No和Share' })
          canSave = false
          return
        }
        if (item.shareRatio > 100) {
          this.$toast({ tips: 'Share不能大于100' })
          canSave = false
          return
        }
        if (item.shareRatio < 0) {
          this.$toast({ tips: 'Share不能小于0' })
          canSave = false
          return
        }
        sum += Number(item.shareRatio)
        // alert(sum)
        if (sum > 100) {
          this.$toast({ tips: 'Share总和不能大于100' })
          canSave = false
          return
        }
      })
      
      if (this.deletearry.length>0) {
        this.deletearry.forEach((item,i,array)=>{
              this.$http.delete('/listmatchoverseas/deleteOverseasMapping/' + item).then(res => {
                console.log('resdeleleafter', res.data.code)
                // if (res.success) {
                //   this.getcheckDetail(this.currentRow)
                // }
                // this.$toast({ tips: res.data.message })
              })
        })
      }
      let data = {
        overseas: {
          matchWorkId: this.currentWorkInfo.work_id,
          matchWorkSocietyCode: this.currentWorkInfo.work_society_code,
          matchWorkTitle: this.currentWorkInfo.title,
          matchWorkTitleId: this.currentWorkInfo.sub_title_id,
          id: this.currentRow.id
        },
        mappings: this.ipShareTable
      }
      canSave && this.$http.post('/listmatchoverseas/addManullyOverseasMapping', data).then(res => {
        if (res.data.code == 200) {
          this.isCheckFile = false
          this.getCheckList()
          this.$toast({ tips: '保存成功' })
        } else {
          this.$toast({ tips: res.data.message })
        }
      })
    },
    deleteIpShareList(row, index) {
      console.log('deleterow', row, index);
      // if (row.id) {
      this.$alert('確定要刪除嗎？', '刪除', {
        confirmButtonText: '确定',
        showCancelButton: true,
        cancelButtonText: '取消',
        callback: action => {
          if (action == 'confirm') {
            
            this.ipShareTable.splice(index, 1)
            
            if (row.id) {
              this.deletearry.push(row.id)
            }
            // if (row.id) {

            //   this.$http.delete('/listmatchoverseas/deleteOverseasMapping/' + row.id).then(res => {
            //     console.log('resdeleleafter', res.data.code)
            //     if (res.success) {
            //       this.getcheckDetail(this.currentRow)
            //     }
            //     this.$toast({ tips: res.data.message })
            //   })

            // } else {
            // }
          }
        }
      });
    },
    setCheckStatus(row) {

      if (!row.id) {
        row.overseasId = this.currentRow.id
        row.fileBaseId = this.fid
        row.dataUniqueKey = this.currentRow.dataUniqueKey
      }
      if (!row.ipNameNo || !row.shareRatio) {
        this.$toast({ tips: '請完整填寫Ip Name No和Share' })
        return
      }
      this.dialogVisible = true
      this.targetIpShareRow = row
      console.log('+++', this.targetIpShareRow)
      // this.dialogActiveName = (row.status == 0?'second':'first')
    },
    checkMatchIpShare(row) {
      this.targetIpShareRow.matchIpNameNo = row.ipNameNo
      this.targetIpShareRow.matchIpName = row.name
      this.targetIpShareRow.status = 1
      this.targetIpShareRow.description = ''
      this.dialogVisible = false
      this.targetIpShareRow = null
    },
    saveDescription() {
      this.targetIpShareRow.matchIpNameNo = ''
      this.targetIpShareRow.matchIpName = ''
      this.targetIpShareRow.description = this.description
      this.targetIpShareRow.status = 2
      this.dialogVisible = false
      this.targetIpShareRow = null
    },
    save() {
      let canSave = true
      let params = {}
      params.fileBaseId = this.fid
      params.remitSoc = this.info.remitSocietyCode
      this.rawMatchInfoData.map((item, index) => {
        if (!item.originalTitle) {
          canSave = false
        }
      })
      params.list = this.rawMatchInfoData
      if (canSave) {
        this.$http.post('/listmatchoverseas/addManully', params).then(res => {
          if (res.data.code == 200) {
            this.getCheckList()
          }
          this.$toast({ tips: res.data.message })
        })
      } else {
        this.$toast({ tips: '請填寫Original Title' })
      }
    },
    closeCheckList() {
      this.isCheckFile = false
    },
    uploadChange(file) {
      console.log(file)
      // if(!this.fid){
      //   this.$toast({ tips: '請輸入fid' })
      //   return
      // }
      // if (isNaN(this.fid)) {
      //   this.$toast({ tips: '請輸入正確的fid' })
      //   return
      // }

      // this.$utils.uploadFile({flie:file.raw,fid:this.fid}, '/listfilemapping/addImport', this)

        let formData = new FormData()
        formData.append('file', file.raw||'')
        console.log(formData)
        axios.post('/listfilemapping/addImport', formData).then(res => {
            console.log('^^^^^^^^^^^^^^^', res)

            if (res.data.code == 200) {
               
                if (Number(res.data.data)) {
                    toast({ tips: '上传成功' + res.data.data + '条数据' })
                    // that.getClaimList()
                } else {
                    let MessageBox = res.data.data || res.data.message || res.message
                    toast({ tips: MessageBox })
                }
                
            } else {
                let MessageBox = res.data.data || res.data.message || res.message
                // setTimeout(function() {
                if(MessageBox.indexOf("錯誤信息文件") > 0){
                    this.$alert(MessageBox, '查看錯誤原因', {
                      confirmButtonText: '确定',
                    });
                }else {
                  toast({ tips: MessageBox })
                }
                // }, 0);
                // this.$toast({tips:res.data.message})
            }
        })
    
    }

  }
}
</script>

<style scoped lang="scss">
.file-info {
  margin-bottom: 10px;
  .detail {
    line-height: 36px;
    margin: 0;
    span {
      margin-left: 15px;
    }
  }
}
.description {
  text-align: center;
  padding-top: 20px;
}
.flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.main-left {
  width: 100%;
  transition: width ease 0.3s;
  &.slide {
    width: 38%;
  }
}
.main-right {
  width: 58%;
  height: calc(100% - 45px);
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.2);
  padding: 0 20px;
  box-sizing: border-box;
  transition: all ease 0.3s;
  position: absolute;
  right: -9999px;
  top: 0;
  &.slide {
    right: 0;
  }
  .close-btn {
    position: absolute;
    right: 20px;
    top: 20px;
    cursor: pointer;
  }
}
.work-info {
  width: 100%;
  max-width: 300px;
  margin: 0;
  line-height: 1;
}
</style>
