<template>
<div>
    <el-dialog :visible.sync="show" width="1000px" title="Order" :close-on-click-modal="false">
        <div style="width: 560px;margin: auto;margin-bottom: 20px">
            <el-input v-model.trim="searchInfo.scId" @keyup.enter.native="onSubmit()" placeholder="scId" style="width: 128px;"></el-input>
            <el-input v-model="searchInfo.scNo" @keyup.enter.native="onSubmit()" placeholder="scNo" style="width: 220px;"></el-input>
            <el-button type="primary" @click="onSubmit()">查詢</el-button>
            <span class="clear-search" @click="clearSearch()">清除搜索</span>
        </div>
        <el-table :data="tableData" :empty-text="emptyText">
            <el-table-column property="id" label="scId"></el-table-column>
            <el-table-column property="scNo" label="scNo"></el-table-column>
            <el-table-column property="countryCode" label="country"></el-table-column>
            <el-table-column property="scTypeCode" label="Type"></el-table-column>
            <el-table-column property="scTitleCh" label="scTitleCh"></el-table-column>
            <el-table-column property="scTitleEn" label="scTitleEn"></el-table-column>
            <el-table-column
                label="operation"
                width="100">
                <template slot-scope="scope">
                    <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                        <i class="el-icon-check" @click="checkedWork(scope.$index,scope.row)"></i>
                    </span>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :total="total" @current-change="onSubmit" :current-page="currentPage">
        </el-pagination>
    </el-dialog>
</div>
</template>
<script>
export default {
    data(){
        return{
            show:false,
            searchInfo: {},
            tableData: [],
            total: 0,
            currentPage: 1,
            emptyText: '暫無數據'
        }
    },
    props: {
        search: {
            type: Object,
            default: () => {
                return {
                    scNo: '',
                    scId: '',
                }

            }
        }
    },
    watch: {
        // 'searchInfo.workId': function(){
        //     if(this.searchInfo.workId){
        //         this.searchInfo.workId = (this.searchInfo.workId + '').replace(/[^0-9]/g, '');
        //     }
        // },
        // 'searchInfo.soc': function(){
        //     if(this.searchInfo.soc){
        //         this.searchInfo.soc = (this.searchInfo.soc + '').replace(/[^0-9]/g, '');
        //     }
        // }
    },

    methods: {
        /**
         * dialog 组件调用方式：
         * 1、父级用v-if在html里加载组件,
         * 2、父级调用子级的init方法，将数据初始化，prop传给data等
         *
         */
        init(){
            this.show = true;
            this.searchInfo = this.$utils.copy(this.search);
            this.onSubmit();
        },
        clearSearch(){
            this.searchInfo = {
                scNo: '',
                scId: '',
            };
            this.onSubmit();
        },
        onSubmit(page){
            let ajaxData={
                id: this.searchInfo.scId?this.searchInfo.scId.toString().trim():'',
                scNo: this.searchInfo.scNo?this.searchInfo.scNo.trim():'',
                page_num: page ? page : 1,
                page_size: 10
            }
            this.emptyText = '數據加載中';
            this.$http.get('/distMrdSc', {params: ajaxData}).then( res => {
                if (res.success) {
                    console.log('res')
                    console.log(res.data)
                    this.tableData = res.data.data.list;
                    console.log(this.tableData)
                    if(! this.tableData || this.tableData.length == 0){
                        this.emptyText = '暫無數據';
                    }
                    this.total = res.data.data.total;
                    this.currentPage = page ? page : 1;
                }
            })
        },
        checkedWork(index, row){
            this.$emit('checkWork', row);
            this.show = false;
        }
    }

}
</script>
<style lang="scss" scoped>
    /deep/ .el-dialog__title{
        font-weight: bold;
    }
    /deep/ .el-dialog__body{
        text-align: center;
        padding-top: 0;
    }
</style>
