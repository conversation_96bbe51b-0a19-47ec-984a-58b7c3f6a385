<template>
    <div style="min-width: 1320px;">
        <el-card class="box-card saveright" :class="{'saveSmallright': sidebarFold}">
            <el-button type="primary" @click="save()">保存模板</el-button>
        </el-card>
        <div class="clear export-edit">
            <div class="sql">
                <label style="width:80px;display: inline-block;">模板SQL</label>
                <el-input style="width:800px" type="textarea" rows="5" v-model="sql" @change='changeSql'></el-input>
                <p class="explain">填寫說明：模板sql,${[keyword]}表示的是可替換參數，在實際運行中會替換成可填寫參數</p>
            </div>
        </div>
        <div class="sql parameter">
            <p>生成参数</p>
            <el-form label-position="left" label-width="120px">
                <template v-for="(item,index) in parameterList">
                    <el-form-item :label="item.key" :key="index">
                        <el-input style="width:200px" v-model="item.value"></el-input>
                    </el-form-item>
                </template>
                
            </el-form>
        </div>
        <el-dialog :visible.sync="inputNameShow" width="500px" title="模板" :close-on-click-modal='false'>
            <!-- <el-input v-model="sqlName" placeholder="請輸入模板名稱" style="margin-bottom: 10px;"></el-input>
            <el-input v-model="remark" placeholder="請輸入模板备注"></el-input> -->
            <el-form :inline="true" label-width="80px">
                <el-form-item label="模板名稱">
                    <el-input v-model="sqlName" placeholder="請輸入模板名稱"></el-input>
                </el-form-item>
                <el-form-item label="备注">
                    <el-input v-model="remark" placeholder="最大長度150" maxlength="150"></el-input>
                </el-form-item>
            </el-form>
            <div style="margin-top: 10px;text-align: right;">
                <el-button type="primary" @click="saveFn">確定</el-button>
            </div>
        </el-dialog>
    </div>
    
</template>
<script>
export default {
    data(){
        return{
            inputNameShow: false,
            sqlName: '',
            remark: '',
            sql: '',
            info: {},
            parameterList:[],
            oldParameterList:[],
            parameterMap:{

            }
        }
    },
    watch: {
        
    },
    computed: {
        sidebarFold: {
            get () {
                return this.$store.state.common.sidebarFold
            }
        }
    },
    created(){
        if(this.$route.query.type == 'edit'){
            this.info = JSON.parse(this.$route.query.info);
            console.log(this.info)
            let sqlJson = JSON.parse(this.info.sqlJson)
            console.log(sqlJson)
            this.sql = sqlJson.sql
            for (let key in sqlJson.parameterMap) {
                let obj = {
                   key:key,
                   value:sqlJson.parameterMap[key]
                }
                this.parameterList.push(obj)
            }
            this.sqlName = this.info.sqlName
            this.remark = this.info.remark
        }
    },
    methods: {
        changeSql(value){
            if(value.indexOf('}')!=-1){
                this.oldParameterList = this.$utils.copy(this.parameterList)
                var strs=value.split("}")
                this.newFun(strs)
            }else{
                this.parameterList=[]
            }
            
        },
        newFun(data){
            this.parameterList=[]
            var newstrArr=[]
            data.forEach((item,index)=>{
                if(item){
                    if(item.lastIndexOf('${') != -1){
                        var str=item.substring(item.lastIndexOf('${')+2);
                        let obj = {
                            key:str,
                            value:''
                        } 
                        newstrArr.push(obj)
                    }
                    
                }
            })
            this.oldParameterList.forEach(item=>{
                newstrArr.forEach(item1=>{
                    if(item.key == item1.key){
                        item1.value = item.value
                    }
                })
            })
            this.parameterList = newstrArr
            console.log("新数组",newstrArr)
        },
        save(){
            // if(!this.parameterList.length){
            //     this.$toast({tips: '請輸入符合規則的sql'})
            //     return
            // }
            this.parameterMap={}
            let next = true
            this.parameterList.forEach(item=>{
                if(item.value==''){
                    next = true
                }
                this.parameterMap[item.key] = item.value
            })
            if(next){
                this.inputNameShow = true;

            }else{
                this.$toast({tips: '參數未輸入完成'})
            }
            // parameterMap
            // this.saveFn();
        },
        saveFn(){
            if(!this.sqlName){
                this.$toast({tips: '請輸入模板名稱'})
                return;
            }
            this.inputNameShow = false;
            let ajaxData={
                sqlJson:{
                    sql:this.sql,
                    parameterMap:this.parameterMap
                },
                currentParam:this.parameterMap,
                sqlName: this.sqlName,
                remark: this.remark
            }
            if(this.$route.query.type == 'edit'){
                ajaxData.id = this.info.id;
            }
            this.$http.post('/sys/export/saveOrUpdateComplexSqlTemplate', ajaxData).then( res => {
                if(res.success){
                    if(res.data.code == 200){
                        this.$toast({tips: '保存成功'})
                        this.$bus.$emit('closeCurrentTab', () => { this.$router.push({name: 'exportDiy', query: {update: true}}) });
                    }else{
                        this.$toast({tips: res.data.message})
                    }
                }
            })

        }
    }
    
}
</script>
<style lang="scss" scoped>
.explain{
    padding-left: 80px;
    font-size: 14px;
}
.parameter{
    p{
        font-weight: bold;
    }
    form{
        padding-left: 30px;
    }
}
.saveright{
    /deep/ .el-card__body{
        padding-top: 14px;
    }
    button{
        padding: 6px 10px;
    }
}
.export-edit{
    margin-top: 40px;
    display: flex;
}

.sql{
    margin-bottom: 10px;
    padding: 0 10px;
    label{
        margin-bottom: 4px;
    }
}
</style>
<style>
.el-radio__input.is-disabled+span.el-radio__label{
    color: #333 !important;
}
</style>

<style>
.el-card__body{
    overflow-x: auto;
}
</style>