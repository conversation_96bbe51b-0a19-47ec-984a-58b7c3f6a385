<template>
  <!-- 可支付列表 -->
  <div>
    <!-- <div style="margin-top: 20px"> -->
    <div style="">
      <el-tabs v-model="activeName">
        <!--個人會員Tabs-->
        <el-tab-pane label="member" name="first" v-if="showMember">
          <el-form
            :inline="true"
            :model="memberSearch"
            class="demo-form-inline"
            @keyup.enter.native="queryMemberList"
          >
            <el-form-item label="autopay no">
              <el-input
                v-model="memberSearch.autopayNo"
                placeholder
                style="width: 100px"
              ></el-input>
            </el-form-item>
            <el-form-item label="ip base no">
              <el-input
                v-model="memberSearch.ipBaseNo"
                placeholder
                style="width: 100px"
              ></el-input>
            </el-form-item>
            <el-form-item label="pa name no">
              <el-input
                v-model="memberSearch.paNameNo"
                placeholder
                style="width: 100px"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="queryMemberList"
                >搜 索</el-button
              >
            </el-form-item>
            <el-form-item>
              <span class="clear-search" @click="clearMemberSearch()"
                >清除搜索</span
              >
            </el-form-item>
          </el-form>
          <el-table :data="memberData" border :empty-text="emptyText">
            <el-table-column
              prop="autopayNo"
              label="Autopay no"
              min-width="120px"
            >
            </el-table-column>
            <el-table-column prop="distNo" label="Dist No" width="100px">
            </el-table-column>
            <el-table-column prop="ipBaseNo" label="ip base no" width="120px">
            </el-table-column>
            <el-table-column prop="paNameNo" label="PA Name No" width="150px">
            </el-table-column>
            <el-table-column prop="paName" label="PA name" width="120px">
            </el-table-column>
            <el-table-column prop="ipType" label="Ip type" width="100px">
              <template slot-scope="scope">
                {{
                  scope.row.ipType == "N"
                    ? "个人会员"
                    : scope.row.ipType == "L"
                    ? "团体会员"
                    : ""
                }}
              </template>
            </el-table-column>
            <el-table-column
              prop="retainCode"
              label="retain Code"
              width="120px"
            >
            </el-table-column>
            <el-table-column
              prop="paymentMethod"
              label="Payment Method"
              width="180px"
            >
            </el-table-column>
            <el-table-column
              prop="royaltyAmount"
              label="royalty Amount"
              width="150px"
            >
            </el-table-column>
            <el-table-column prop="deduction" label="deduction" width="120px">
            </el-table-column>
            <el-table-column
              prop="exchangeRate"
              label="Exchange Rate"
              width="150px"
            >
            </el-table-column>
            <el-table-column
              prop="commissionAmount"
              label="Commission"
              width="140px"
            >
            </el-table-column>
            <el-table-column prop="taxableAmount" label="Tax" width="100px">
            </el-table-column>
            <el-table-column
              prop="salesTaxAmount"
              label="Sales Tax"
              width="100px"
            >
            </el-table-column>
            <el-table-column prop="payDate" label="Payment Date" width="150px">
              <template slot-scope="scope">
                <!-- {{scope.row.payDate.split(' ')[0]}} -->
              </template>
            </el-table-column>
            <el-table-column prop="invNo" label="Inv No" width="100px">
            </el-table-column>
            <el-table-column label="operation" width="120">
              <template slot-scope="scope">
                <span class="a-blue" v-if="isEdit == 1" @click="redo(scope.row, 'Member')">redo</span>
                <span class="a-blue" @click="transaction(scope.row,'Member')">Transaction</span>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            background
            layout="prev, pager, next"
            :total="memberTotal"
            @current-change="handleMemberChange"
          >
          </el-pagination>
        </el-tab-pane>

        <!--團體會員Tabs-->
        <el-tab-pane label="society" name="second" v-if="showSociety">
          <el-form
            :inline="true"
            :model="socSearch"
            class="demo-form-inline"
            @keyup.enter.native="querySocietyList"
          >
            <el-form-item label="autopay no">
              <el-input
                v-model="socSearch.autopayNo"
                placeholder
                style="width: 100px"
              ></el-input>
            </el-form-item>
            <el-form-item label="soc no">
              <el-input
                v-model="socSearch.societyCode"
                placeholder
                style="width: 100px"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="querySocietyList"
                >搜 索</el-button
              >
            </el-form-item>
            <el-form-item>
              <span class="clear-search" @click="clearSocSearch()"
                >清除搜索</span
              >
            </el-form-item>
          </el-form>
          <el-table :data="socData" border :empty-text="emptyText1">
            <el-table-column prop="autopayNo" label="Autopay no" width="120px">
            </el-table-column>
            <el-table-column prop="distNo" label="Dist No" width="100px">
            </el-table-column>
            <el-table-column prop="societyCode" label="Soc" width="100px">
            </el-table-column>
            <el-table-column prop="societyName" label="Soc Name" width="130px">
            </el-table-column>
            <!-- <el-table-column
                            prop="ipType"
                            label="Ip type">
                        </el-table-column> -->
            <el-table-column
              prop="retainCode"
              label="retain Code"
              width="130px"
            >
            </el-table-column>
            <el-table-column
              prop="paymentMethod"
              label="Payment Method"
              width="180px"
            >
            </el-table-column>
            <el-table-column
              prop="royaltyAmount"
              label="royalty Amount"
              width="160px"
            >
            </el-table-column>
            <el-table-column prop="deduction" label="deduction" width="120px">
            </el-table-column>
            <el-table-column
              prop="exchangeRate"
              label="Exchange Rate"
              width="150px"
            >
            </el-table-column>
            <el-table-column
              prop="commissionAmount"
              label="Commission"
              width="130px"
            >
            </el-table-column>
            <el-table-column prop="taxableAmount" label="Tax" width="100px">
            </el-table-column>
            <el-table-column prop="payDate" label="Payment Date" width="150px">
              <template slot-scope="scope">
                <!-- {{scope.row.payDate.split(' ')[0]}} -->
              </template>
            </el-table-column>
            <el-table-column label="operation" width="120">
              <template slot-scope="scope">
                <span class="a-blue" v-if="isEdit == 1" @click="redo(scope.row, 'Society')">redo</span>
                <span class="a-blue" @click="transaction(scope.row,'Society')">Transaction</span>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            background
            layout="prev, pager, next"
            :total="socTotal"
            @current-change="handleSocChange"
          >
          </el-pagination>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
export default {
  name: "payDetailInfo",
  data() {
    return {
      autopayNo: "",
      showMember: false,
      showSociety: false,
      memberSearch: {
        autopayNo: "",
        ipBaseNo: "",
        paNameNo: "",
      },
      socSearch: {
        autopayNo: "",
        societyCode: "",
      },
      memberTotal: 0,
      socTotal: 0,
      memberData: [],
      socData: [],
      activeName: "first",
      emptyText: "數據加載中",
      emptyText1: "數據加載中",
      isEdit: 0
    };
  },
  methods: {
    redo(data, title) {
      this.$msgbox
        .confirm("確定redo?", "提示", {
          confirmButtonText: "確定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(() => {
          this.$http
            .post(`/dist/autopay/distAutopayNetPay${title}Redo/${data.id}`)
            .then((res) => {
              if (res.success && res.data.code == 200) {
                if (title == "Member") {
                  this.queryMemberList();
                } else {
                  this.querySocietyList();
                }
              } else {
                this.$toast({ tips: res.data.message });
              }
            });
        })
        .catch(() => {});
    },

    queryMemberList() {
      let params = this.memberSearch;
      // params.autopayNo = this.autopayNo
      this.emptyText = "數據加載中";
      this.$http
        .get("/dist/autopay/lisDistAutopayNetPayMemberForPay", { params })
        .then((res) => {
          if (res.success && res.data.code == 200) {
            this.memberData = res.data.data.list;
            this.memberTotal = res.data.data.total;
            if (!this.memberData || this.memberData.length == 0) {
              this.emptyText = "暫無數據";
            }
          } else {
            this.$toast({ tips: res.data.message });
          }
        });
    },
    querySocietyList() {
      let params = this.socSearch;
      // params.autopayNo = this.autopayNo
      this.emptyText1 = "數據加載中";
      this.$http
        .get("/dist/autopay/listDistAutopayNetPaySocietyForPay", { params })
        .then((res) => {
          if (res.success && res.data.code == 200) {
            this.socData = res.data.data.list;
            if (!this.socData || this.socData.length == 0) {
              this.emptyText1 = "暫無數據";
            }
            this.socTotal = res.data.data.total;
          } else {
            this.$toast({ tips: res.data.message });
          }
        });
    },
    clearMemberSearch() {
      this.memberSearch = {
        autopayNo: "",
        ipBaseNo: "",
        paNameNo: "",
      };
      this.queryMemberList();
    },
    handleMemberChange(val) {
      this.memberSearch.page_num = val;
      this.queryMemberList();
    },
    clearSocSearch() {
      this.socSearch = {
        autopayNo: "",
        societyCode: "",
      };
      this.querySocietyList();
    },
    handleSocChange(val) {
      this.socSearch.page_num = val;
      this.querySocietyList();
    },
    init() {
      let payType = this.$route.query.payType;
      console.log(payType);
      // this.showMember = payType.indexOf('MBR') != -1 ? true : false
      // this.showSociety = payType.indexOf('SOC') != -1 ? true : false
      if (payType.indexOf("MBR") != -1) {
        this.showMember = true;
        this.activeName = "first";
        this.memberSearch.autopayNo = this.autopayNo;
      }
      if (payType.indexOf("SOC") != -1) {
        this.showSociety = true;
        this.activeName = "second";
        this.socSearch.autopayNo = this.autopayNo;
        console.log(this.autopayNo);
        console.log(this.socSearch.autopayNo);
      }
      this.showMember && this.queryMemberList();
      this.showSociety && this.querySocietyList();
    },
    transaction(data,type){
      // let routeName = "royalties-member";
      if(type == "Member"){
        this.$router.push({
          name: "royalties-member",
          query: {
            id: data.distNo + data.paNameNo,
            ipBaseNo: data.ipBaseNo,
            distNo: data.distNo,
            paNameNo: data.paNameNo,
            paName: data.paName,
            nameId: data.distNo + data.paNameNo,
            title: data.paName
          },
          params: { id: data.paNameNo + data.distNo },
        });
      } else if(type == "Society"){
        this.$router.push({
          name: "royalties-society",
          query: {
            distNo: data.distNo,
            societyCode: data.societyCode,
            societyName: data.societyName,
            nameId: data.distNo + data.societyCode,
            id: data.distNo + data.societyCode,
            title: data.societyName
          },
          params: { id: data.societyCode + data.distNo },
        });
      }
      
    },
  },
  watch: {
    "$route.query.autopayNo"() {
      console.log(this.$route.query);
      console.log(this.$route.query.autopayNo);
      this.autopayNo = this.$route.query.autopayNo;
      this.isEdit = this.$route.query.isEdit
      if (this.autopayNo) {
        this.init();
      }
    },
  },
  mounted() {
    this.autopayNo = this.$route.query.autopayNo;
    this.isEdit = this.$route.query.isEdit
    this.init();
  },
};
</script>

<style>
.mydetail {
  width: 100% !important;
  display: block !important;
}

.mydateform .el-input--suffix {
  width: 200px !important;
}

.mydetail .el-form-item__content {
  width: calc(100% - 150px) !important;
}
</style>
