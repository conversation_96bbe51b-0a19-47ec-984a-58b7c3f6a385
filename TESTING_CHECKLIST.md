# 自定义日期更新功能测试清单

## 功能测试步骤

### 1. 基础功能测试

#### 1.1 页面加载测试
- [ ] 打开抽样规则编辑页面
- [ ] 验证A组件（预览）正常显示原有日期
- [ ] 验证自定义编辑开关默认关闭状态

#### 1.2 开关功能测试
- [ ] 点击"开启自定义编辑"开关
- [ ] 验证B组件（编辑）正常显示
- [ ] 验证开关文本变为"关闭自定义编辑"
- [ ] 再次点击开关，验证B组件隐藏

#### 1.3 日期复制功能测试
- [ ] 开启自定义编辑
- [ ] 点击"从预览复制日期"按钮
- [ ] 验证B组件中显示复制的日期
- [ ] 验证复制的日期数量正确
- [ ] 验证成功提示消息显示

#### 1.4 日期编辑功能测试
- [ ] 在B组件中点击未选中的日期
- [ ] 验证日期被正确选中（高亮显示）
- [ ] 点击已选中的日期
- [ ] 验证日期被正确取消选中
- [ ] 验证复制过来的日期可以正常取消选中

#### 1.5 清空功能测试
- [ ] 选择一些自定义日期
- [ ] 点击"清空选择"按钮
- [ ] 验证所有自定义日期被清空
- [ ] 验证成功提示消息显示

### 2. 接口调用测试

#### 2.1 数据验证测试
- [ ] 不选择任何自定义日期，点击"重新生成(自定义日期)"
- [ ] 验证显示"请先选择自定义日期"警告
- [ ] 选择自定义日期后再次点击
- [ ] 验证显示确认对话框

#### 2.2 确认对话框测试
- [ ] 点击"重新生成(自定义日期)"按钮
- [ ] 验证确认对话框正确显示选中日期数量
- [ ] 点击"取消"，验证操作被取消
- [ ] 点击"确定"，验证接口调用开始

#### 2.3 接口调用测试
- [ ] 确认操作后，验证加载状态显示
- [ ] 打开浏览器开发者工具，查看Network标签
- [ ] 验证请求URL为：`/list2SampleRule/updateSampleDates`
- [ ] 验证请求方法为：`POST`
- [ ] 验证请求头包含：`Content-Type: application/json`

#### 2.4 请求数据格式测试
在开发者工具中验证请求数据格式：
```json
{
  "sampleRuleBaseId": 123,
  "customSampleDates": [
    "2024-01-15T00:00:00.000Z",
    "2024-01-20T00:00:00.000Z"
  ]
}
```
- [ ] 验证`sampleRuleBaseId`是数字类型
- [ ] 验证`customSampleDates`是数组
- [ ] 验证日期格式为ISO字符串

### 3. 响应处理测试

#### 3.1 成功响应测试
- [ ] 后端返回成功响应
- [ ] 验证加载状态消失
- [ ] 验证显示"自定义日期更新成功"消息
- [ ] 验证页面数据刷新（A组件显示更新后的日期）

#### 3.2 失败响应测试
- [ ] 模拟后端返回错误响应
- [ ] 验证加载状态消失
- [ ] 验证显示错误消息
- [ ] 验证页面状态保持不变

#### 3.3 网络错误测试
- [ ] 断开网络连接
- [ ] 执行更新操作
- [ ] 验证显示网络错误提示
- [ ] 验证加载状态正确消失

### 4. 控制台调试信息测试

打开浏览器开发者工具Console标签，验证以下信息：

#### 4.1 日期选择调试信息
- [ ] 点击日期时显示原始日期和标准化日期
- [ ] 显示比较过程和结果
- [ ] 显示操作前后的数组状态

#### 4.2 接口调用调试信息
- [ ] 显示"准备提交的自定义日期"
- [ ] 显示"发送到后端的请求数据"
- [ ] 显示"后端响应"信息

### 5. 边界情况测试

#### 5.1 数据边界测试
- [ ] 选择大量日期（如50个以上）
- [ ] 验证性能表现
- [ ] 验证接口调用正常

#### 5.2 日期格式测试
- [ ] 测试不同格式的日期输入
- [ ] 验证日期标准化功能正常
- [ ] 验证日期比较功能正常

#### 5.3 并发操作测试
- [ ] 快速连续点击"重新生成"按钮
- [ ] 验证不会发送重复请求
- [ ] 验证加载状态正确管理

### 6. 用户体验测试

#### 6.1 视觉反馈测试
- [ ] A组件显示为只读状态（灰色背景）
- [ ] B组件显示为可编辑状态（蓝色背景）
- [ ] 选中日期有明显的视觉反馈
- [ ] 加载状态有明显的视觉提示

#### 6.2 交互反馈测试
- [ ] 所有按钮点击有即时反馈
- [ ] 成功/失败操作有明确提示
- [ ] 错误信息描述清晰易懂

## 问题排查指南

### 常见问题及解决方案

#### 1. 复制的日期无法取消选中
- 检查控制台是否有日期格式相关错误
- 验证`normalizeDate`方法是否正常工作
- 检查日期比较逻辑

#### 2. 接口调用失败
- 检查网络连接
- 验证后端接口是否正常运行
- 检查请求数据格式是否正确

#### 3. 页面显示异常
- 检查浏览器控制台是否有JavaScript错误
- 验证Vue组件是否正常加载
- 检查CSS样式是否正确应用

#### 4. 数据不同步
- 检查`handleUpdateSuccess`方法是否正确执行
- 验证`getDateList`方法是否正常调用
- 检查后端数据是否正确更新

## 测试完成标准

- [ ] 所有基础功能测试通过
- [ ] 所有接口调用测试通过
- [ ] 所有响应处理测试通过
- [ ] 所有边界情况测试通过
- [ ] 用户体验良好
- [ ] 无控制台错误信息
- [ ] 性能表现良好
