<template>
    <div>
        <el-form :inline="true" :model="searchForm" class="demo-form-inline" @keyup.enter.native="getData">
            <el-form-item label="dist no">
                <el-input v-model.trim="searchForm.distNo" placeholder=""></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="getData" v-if="isAuth('distribute:condition:find')">搜索</el-button>
            </el-form-item>
        </el-form>
        <el-tabs v-model="activeName" @tab-click="getData">
            <el-tab-pane label="copy list" name="copy">
                <CopyList :distNo="searchForm.distNo" ref="copyList"></CopyList>
            </el-tab-pane>
            <el-tab-pane label="filter work" name="work">
                <FilterWork :distNo="searchForm.distNo" ref="filterWork"></FilterWork>
            </el-tab-pane>
            <el-tab-pane label="filter ip" name="ip">
                <FilterIp :distNo="searchForm.distNo" ref="filterIp"></FilterIp>
            </el-tab-pane>
            <el-tab-pane label="filter soc" name="soc">
                <FilterSoc :distNo="searchForm.distNo" ref="filterSoc"></FilterSoc>
            </el-tab-pane>
        </el-tabs>
    </div>
</template> 

<script>
    import CopyList from './conditionlist/copylist'
    import FilterWork from './conditionlist/filterwork'
    import FilterIp from './conditionlist/filterip'
    import FilterSoc from './conditionlist/filtersoc'
    export default {
        name: 'special-condition',
        data() {
            return {
                activeName: 'copy',
                searchForm:{
                    distNo:'',
                }
            };
        },
        components:{
            CopyList,
            FilterWork,
            FilterIp,
            FilterSoc
        },
        methods: {

            handleSearch(){
                this.getData()
            },
            getData(){  
                if(this.activeName === 'copy'){
                    this.$refs.copyList.getList(1)
                }else if(this.activeName === 'work'){
                    this.$refs.filterWork.getList(1)
                }else if(this.activeName === 'ip'){
                    this.$refs.filterIp.getList(1)
                }else if(this.activeName === 'soc'){
                    this.$refs.filterSoc.getList(1)
                }
            }
        },
          beforeRouteEnter (to, from, next) {
            next(vm => {
                vm.getData()
            })
        }
    }
</script>

<style scoped>
    .btn{
        margin-bottom: 10px;
    }
</style>
