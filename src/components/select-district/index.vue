<template>
<div>
    <el-dialog :visible.sync="show" width="800px" title="District" :modal="modal" :close-on-click-modal="false">
        <!-- <div style="width: 520px;margin: auto;margin-bottom: 20px; display: inline-block;">
            <el-input v-model="searchInfo.districtCode" placeholder="DistrictCode" style="width: 160px;"></el-input>
            <el-input v-model="searchInfo.district" placeholder="District" style="width: 160px;"></el-input>
            <el-button type="primary" @click="onSubmit()">查詢</el-button>
            <span class="clear-search" @click="clearSearch()">清除搜索</span>
        </div> -->
        <el-table :empty-text="emptyText" :data="tableData">
            <el-table-column label="District">
                <template slot-scope="scope">
                    {{scope.row}}
                </template>
            </el-table-column>
            <el-table-column
                label="operation"
                width="120">
                <template slot-scope="scope">
                    <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                        <i class="el-icon-check" @click="checkedDistrict(scope.$index,scope.row)"></i>
                    </span>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :total="total" @current-change="onSubmit" :current-page="currentPage">
        </el-pagination>
    </el-dialog>
</div>
</template>
<script>
export default {
    data(){
        return {
            emptyText:'暫無數據',
            show:false,
            searchInfo: {
                countyCode: ''
            },
            tableData: [],
            total: 0,
            currentPage: 1
        }
    },
    props: {
        search: {
            type: Object,
            default: () => {
                return {
                    countyCode: ''
                }
            }
        },
        modal: {
            type: Boolean,
            default: true
        }
    },

    methods: {
        /**
         * dialog 组件调用方式： 
         * 1、父级用v-if在html里加载组件,
         * 2、父级调用子级的init方法，将数据初始化，prop传给data等
         * 
         */
        init(){
            this.show = true;
            this.searchInfo = this.$utils.copy(this.search);
            this.searchInfo.countyCode = this.searchInfo.countyCode ? this.searchInfo.countyCode : '';
            this.onSubmit();
        },
        clearSearch(){
            this.searchInfo = {
                countyCode: ''
            };
            this.onSubmit();
        },
        onSubmit(page){
            let params = this.searchInfo;
            params.district = params.district ? params.district.trim() : params.district;
            if(!params.district){
                delete params.district;
            }
            this.emptyText = '數據加載中'
            params.page_num = page ? page : 1;
            params.page_size = 10;
            this.$http.get('/member/getDistrict', {params: params}).then(res => {
                this.tableData = res.data.data.list;
                this.total = res.data.data.total;
                this.currentPage = page ? page : 1;
                if(! this.tableData || this.tableData.length == 0){
                    this.emptyText = '暫無數據';
                }
            })
        },
        checkedDistrict(index, row){
            this.$emit('checkDistrict', row);
            this.show = false;
        }
    }
    
}
</script>
<style lang="scss" scoped>
    /deep/ .el-dialog__title{
        font-weight: bold;
    }
    /deep/ .el-dialog__body{
        text-align: center;
        padding-top: 0;
    }
</style>