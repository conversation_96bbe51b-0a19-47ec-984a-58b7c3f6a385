<template>
    <div style="min-width: 1320px;">
        <el-card class="box-card saveright" :class="{'saveSmallright': sidebarFold}">
            <el-button type="primary" @click="next()" v-if="current == 1">下一步</el-button>
            <template v-else>
                <el-button type="primary" @click="forward()">上一步</el-button>
                <el-button type="primary" @click="viewFn()">預覽數據</el-button>
                <el-button type="primary" @click="runSql()">執行SQL</el-button>
                <el-button type="primary" @click="save()">保存模板</el-button>
            </template>
            
        </el-card>
        <div class="clear export-edit">
            <div class="table-list">
                <div class="title">導出數據表</div>
                <div class="item" v-for="(item, index) in tableList" :key="index">
                    <el-radio :label="item.tableName" v-model="checkTable" :disabled="current == 2"></el-radio>
                    <span class="table-comment">{{item.tableComment}}</span>
                </div>
            </div>
            <div class="table-columns">
                <div v-show="current == 1">
                    <div class="list">
                        <div class="title">{{currTable.tableName}}</div>
                        <el-table :empty-text="tableresult"   ref="stepTable" :data="currTable.columnList" style="width: 100%;" @selection-change="handleSelectionChange">
                            <el-table-column
                                type="selection"
                                width="55">
                            </el-table-column>
                            <el-table-column prop="property" label="property"></el-table-column>
                            <el-table-column prop="columnComment" label="Comment"></el-table-column>
                            <el-table-column label="Alias Name">
                                <template slot-scope="scope">
                                    <el-input type="text" v-model="scope.row.columnName"></el-input>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </div>
                <div v-show="current == 2">
                    <div class="sql">
                        <label>SQL:</label>
                        <el-input type="textarea" rows="5" v-model="sql"></el-input>
                    </div>
                    <div class="list step-2" style="width: auto;margin: 10px;">
                        <div class="title">{{tableJson.title}}</div>
                        <div class="item" v-for="(value, i) in tableJson.whereList" :key="i">
                            <span class="checkbox" @click="checkFilter(value.columnName)">
                                <input type="checkbox" :checked="filterCheck.indexOf(value.columnName) >= 0"/> 
                                <span class="w-200 el-sl" :title="value.oName">{{value.oName}}</span>
                                <span class="w-200 el-sl" :title="value.comment">{{value.comment}}</span>
                            </span>
                            <el-select class="w-120" v-model="value.condition">
                                <el-option v-for="(val, key) in filters" :key="key" :label="key" :value="val"></el-option>
                            </el-select>
                            <el-input class="w-120" v-model="value.value" :placeholder="(value.value == 'IN' || value.value == 'NOT_IN') ? '多个条件用英文逗号隔开' : ''"></el-input>
                            <span class="oper" @click="copy(tableJson.whereList, i)">复制</span>
                            <span class="oper" @click="deleteFn(tableJson.whereList, i)">删除</span>
                        </div>
                    </div>
                </div>
                
            </div>
        </div>
        <el-dialog :visible.sync="show" width="1000px" title="預覽前10條數據" :close-on-click-modal='false'>
            <el-table :empty-text="tableresult"   :data="tableData">
                <el-table-column v-for="(item, index) in header" :key="index" :prop="item" :label="item"></el-table-column>
            </el-table>
        </el-dialog>
        <el-dialog :visible.sync="inputNameShow" width="500px" title="模板" :close-on-click-modal='false'>
            <!-- <el-input v-model="templateName" placeholder="請輸入模板名稱" style="margin-bottom: 10px;"></el-input>
            <el-input v-model="remark" placeholder="請輸入模板备注"></el-input> -->
            <el-form :inline="true" label-width="80px">
                <el-form-item label="模板名稱">
                    <el-input v-model="templateName" placeholder="請輸入模板名稱"></el-input>
                </el-form-item>
                <el-form-item label="备注">
                    <el-input v-model="remark" placeholder="最大長度150" maxlength="150"></el-input>
                </el-form-item>
            </el-form>
            <div style="margin-top: 10px;text-align: right;">
                <el-button type="primary" @click="saveFn">確定</el-button>
            </div>
        </el-dialog>
    </div>
    
</template>
<script>
export default {
    data(){
        return{
            inputNameShow: false,
            templateName: '',
            remark: '',
            info: {},
            //
            current: 1,
            tableList: [],


            checkTable: '',
            currTable: {},

            checkColumn: [],

            tableJson: {},

            filterCheck: [],

            filters: {
                '等於': 'EQUAL',
                '不等於': 'NOT_EQUAL',
                '大於': 'GREATER_THAN',
                '小於': 'LESS_THAN',
                '大於等於': 'GREATER_THAN_OR_EQUAL',
                '小於等於': 'LESS_THAN_OR_EQUAL',
                '包含': 'LIKE',
                '不包含': 'NOT_LIKE',
                '是null': 'IS_NULL',
                '不是null': 'IS_NOT_NULL',
                '在列表中': 'IN',
                '不在列表中': 'NOT_IN'
            },
            sql: '',
            tableData: [],tableresult:' ',
            show: false,
            header: [],
            hasInit: false
        }
    },
    watch: {
        checkTable(oldVal, newVal){
            if(this.hasInit){
                this.checkColumn = [];
                this.tableList.forEach( item => {
                    if(item.tableName == this.checkTable){
                        this.currTable = item;
                    }
                })
                this.$nextTick( () => {
                    this.currTable.columnList.forEach( value => {
                        this.$refs.stepTable.toggleRowSelection( value, true);
                    })
                })
            }
        }
    },
    computed: {
        sidebarFold: {
            get () {
                return this.$store.state.common.sidebarFold
            }
        }
    },
    created(){
        if(this.$route.query.type == 'edit'){
            this.info = JSON.parse(this.$route.query.info);
        }
        this.queryInfo();
    },
    methods: {
        editInit(){
            this.tableJson = JSON.parse(this.info.sqlJson);
            console.log(this.tableJson)
            console.log(this.tableList)
            this.tableList.forEach( item => {
                if(item.tableObjectName == this.tableJson[0].table){
                    this.checkTable = item.tableName;
                }
            })
            this.tableList.forEach( item => {
                if(item.tableName == this.checkTable){
                    item.columnList.forEach(item1=>{
                        this.tableJson[0].columnList.forEach(item2=>{
                            if(item2.columnName==item1.property){
                                item1.columnName=item2.name
                            }
                        })
                    })
                    this.currTable = item;
                }
            })

            this.$nextTick( () => {
                this.checkColumn = [];
                this.tableJson[0].columnList.forEach( item => {
                    this.checkColumn.push(item.property);
                    this.currTable.columnList.forEach( value => {
                        if(item.columnName == value.property){
                            this.$refs.stepTable.toggleRowSelection( value, true);
                        }
                    })
                })
                this.templateName = this.info.sqlName;
                this.hasInit = true;
            })
            
        },
        handleSelectionChange(val){
            console.log(val);
            this.checkColumn = [];
            val.forEach( item => {
                this.checkColumn.push(item.property);
            })
        },
        checkFilter(prop){
            let index = this.filterCheck.indexOf(prop);
            if(index >= 0){
                this.filterCheck.splice(index, 1);
            }else{
                this.filterCheck.push(prop);
            }
        },
        queryInfo(){
            this.$http.get('/sys/export/tableDetail').then( res => {
                if(res.success){
                    if(res.data.code == 200){
                        this.tableList = res.data.data;
                        if(this.$route.query.type == 'edit'){
                            this.editInit();
                        }else{
                            this.hasInit = true;
                        }
                    }else{
                        this.$toast({tips: res.data.message})
                    }
                }
            })
        },
        next(){
            if(!this.checkTable){
                this.$toast({tips: '請選擇要導出的數據表'})
                return;
            }
            if(this.checkColumn.length == 0){
                this.$toast({tips: '請至少勾選一個要導出的字段'});
                return;
            }
            let fleg = true
            let fleg1 = true
            //生成json對象了要
            let whereList = this.info.sqlJson?JSON.parse(this.info.sqlJson)[0].whereList:[]
            this.tableJson = {};
            // this.tableList.forEach( (item, index) => {
                let temp = {}
                // if(item.tableName == this.checkTable){
                    temp.title = this.currTable.tableName;
                    temp.table = this.currTable.tableObjectName;
                    temp.columnList = [];
                    temp.whereList = [] ;
                    this.checkColumn.forEach( checkC => {
                        this.currTable.columnList.forEach( column => {
                            if(checkC == column.property){
                                temp.columnList.forEach(item=>{
                                    if(column.columnName == item.name){
                                        fleg = false
                                    }
                                    if(!column.columnName){
                                        fleg1 = false
                                    }
                                })
                                temp.columnList.push({ columnName: column.property, name: column.columnName })
                                temp.whereList.push({ columnName: column.property, condition: 'EQUAL', value: '', oName: column.columnName, comment: column.columnComment})
                            }
                        })
                    })
                    whereList.forEach(item=>{
                        temp.whereList.forEach(item1=>{
                            if(item.columnName == item1.columnName){
                                item1.value = item.value
                                this.filterCheck.push(item.columnName);
                            }
                        })
                    })
                    console.log(temp)
                    this.tableJson = temp;
                // }
            // })
            if(!fleg){
                this.$toast({tips: '重復的Alias Name'})
            }else if(!fleg1){
                this.$toast({tips: '勾選時Alias Name不可為空'})
            }else{
                this.current = 2;
            }
        },
        forward(){
            this.current = 1;
        },
        // viewFn(){

        // },
        viewFn(){
            this.viewAjaxData = [];
            let temp = this.$utils.copy(this.tableJson);
            let whereList = [];
            
            this.filterCheck.forEach( f => {
                temp.whereList.forEach( value => {
                    if( f == value.columnName){
                        whereList.push(this.$utils.copy(value));
                    }
                })
            })
            temp.whereList = whereList;
            this.viewAjaxData.push(temp);
                    this.tableresult = '數據加載中...'
            this.$http.post('/sys/export/data', this.viewAjaxData).then( res => {
                if(res.success){
                    if(res.data.code == 200){
                        this.sql = res.data.data[0].sql;
                        this.tableData = res.data.data[0][this.checkTable] || [];
                        this.getHeader();
                    }else{
                        this.$toast({tips: res.data.message})
                    }
                          this.tableresult = this.tableData.length == 0 ? '暫無數據' : ' '
                }
            })

        },
        runSql(){
            if(!this.sql){
                this.$toast({tips: 'sql 語句不能為空'})
                return;
            }
            this.loading = this.$loading();
            this.$http.get('/sys/export/original/data?sql=' + this.sql).then( res => {
                this.loading.close();
                if(res.success){
                    if(res.data.code == 200){
                        this.tableData = res.data.data || [];
                        this.getHeader();
                    }else{
                        this.$toast({tips: res.data.message})
                    }
                }
            }).catch(()=>{
                this.loading.close();
            })
        },
        getHeader(){
            this.header = [];
            this.tableJson.columnList.forEach( item => {
                this.header.push(item.name);
            })
            this.show = true;
        },
        copy(list, index){
            let temp = this.$utils.copy(list[index]);
            list.splice(index, 0, temp);
        },
        deleteFn(list, index){
            list.splice(index, 1);
        },
        save(){
            if(!this.sql){
                this.$toast({tips: '請先預覽數據'})
                return; 
            }
            this.inputNameShow = true;
            // this.saveFn(this.templateName);
        },
        saveFn(){
            if(!this.templateName){
                this.$toast({tips: '請輸入模板名稱'})
                return;
            }
            this.inputNameShow = false;
            let ajaxData = {
                sqlInfo: this.sql,
                sqlJson: JSON.stringify(this.viewAjaxData),
                sqlName: this.templateName,
                remark: this.remark
            }
            if(this.$route.query.type == 'edit'){
                ajaxData.id = this.info.id;
            }
            this.$http.post('/sys/export/saveOrUpdateSqlTemplate', ajaxData).then( res => {
                if(res.success){
                    if(res.data.code == 200){
                        this.$toast({tips: '保存成功'})
                        this.$bus.$emit('closeCurrentTab', () => { this.$router.push({name: 'exportDiy', query: {update: true}}) });

                    }else{
                        this.$toast({tips: res.data.message})
                    }
                }
            })

        }
    }
    
}
</script>
<style lang="scss" scoped>
.el-radio{
    width: 180px;
}
.table-comment{
    display: inline-block;
    margin-left: 10px;
    width: 120px;
}
.el-card{
    margin-bottom: 20px;
}
.saveright{
    /deep/ .el-card__body{
        padding-top: 14px;
    }
    button{
        padding: 6px 10px;
    }
}
.export-edit{
    margin-top: 40px;
    display: flex;
}
.table-list{
    flex: 0;
    min-height: 750px;
    margin: 0 10px;
    padding: 14px;
    border: 1px solid #ccc;
    border-radius: 3px;
    .title{
        font-weight: bold;
        margin-bottom: 20px;
        text-align: center;
    }
    .item{
        width: 380px;
        margin: 10px 0;
    }
}
.table-columns{
    flex: 1;
    padding: 14px;
    padding-top: 0;
    border-radius: 3px;
    .list{
        width: 650px;
        padding: 14px 4px;
        border: 1px solid #ccc;
        border-radius: 3px;
        .title{
            font-weight: bold;
            margin-bottom: 20px;
            text-align: center;
        }
        .item{
            
            margin: 10px 0;
            cursor: pointer;
        }
    }
}
.w-120{
    display: inline-block;
    margin-right: 4px;
    width: 130px;
}
.w-200{
    display: inline-block;
    margin-right: 4px;
    width: 200px;
}
.step-2{
    
    .checkbox{
        display: inline-block;
        width: 460px;
    }
    .oper{
        color: blue;
        cursor: pointer;
    }
}
.sql{
    margin-bottom: 10px;
    padding: 0 10px;
    label{
        margin-bottom: 4px;
    }
}
</style>
<style>
.el-radio__input.is-disabled+span.el-radio__label{
    color: #333 !important;
}
</style>

<style>
.el-card__body{
    overflow-x: auto;
}
</style>