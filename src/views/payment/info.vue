<template>
    <!-- 可支付列表 -->
    <div>
        <div style="margin-top: 20px">
            <el-tabs v-model="activeName">
                <!--個人會員Tabs-->
                <el-tab-pane label="member" name="first" v-if="showMember">
                    <el-form
                        :inline="true"
                        :model="memberSearch"
                        label-position="left"
                        class="demo-form-inline"
                    >
                        <div>
                            <el-form-item label="NO">
                                <el-input v-model="memberSearch.paNameNo" placeholder></el-input>
                            </el-form-item>
                            <el-form-item label="Name">
                                <el-input v-model="memberSearch.paName" placeholder></el-input>
                            </el-form-item>

                            <el-form-item label="會員類別">
                                <el-select v-model="memberSearch.ipType" placeholder style="width: 100px;">
                                    <el-option label="L" value="L"></el-option>
                                    <el-option label="N" value="N"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item>
                                <el-button type="primary" @click="queryMemberPay">搜 索</el-button>
                            </el-form-item>
                            <el-form-item>
                                <el-button type="primary" @click="">輸出文檔</el-button>
                            </el-form-item>
                            <el-form-item>
                                <span class="clear-search" @click="clearMemberSearch()">清除搜索</span>
                            </el-form-item>
                        </div>
                        <div style="text-align: right;">
                            <el-form-item>
                                <el-button type="success" @click="payMember">支付</el-button>
                            </el-form-item>
                            <el-form-item>
                                <el-button type="success" @click="payMember('all')">支付所有</el-button>
                            </el-form-item>
                        </div>
                    </el-form>
                    <el-table
                        :data="memberData"
                        border
                        stripe
                        style="width: 100%"
                        @selection-change="handleMemberSelectionChange"
                    >
                        <el-table-column type="selection"></el-table-column>
                        <el-table-column prop="id" label="序號"></el-table-column>
                        <el-table-column prop="paName" label="Name"></el-table-column>
                        <el-table-column prop="paNameNo" label="NO"></el-table-column>
                        <el-table-column prop="totalPayAmount" label="總未支付金額"></el-table-column>
                        <el-table-column prop="payAmount" label="可支付金額"></el-table-column>
                        <el-table-column prop="sd" label="SD"></el-table-column>
                        <el-table-column label="operation">
                            <template slot-scope="scope">
                                <span class="a-blue" @click="memberDetail(scope.$index)">詳情</span>
                            </template>
                        </el-table-column>
                    </el-table>
                    <el-pagination
                        background
                        layout="prev, pager, next"
                        :total="memberTotal"
                        @current-change="handleMemberChange"
                    ></el-pagination>
                </el-tab-pane>

                <!--團體會員Tabs-->
                <el-tab-pane label="society" name="second" v-if="showSociety">
                    <el-form :inline="true" :model="socSearch" label-position="left" class="demo-form-inline">
                        <div>
                            <el-form-item label="Code">
                                <el-input v-model="socSearch.societyCode" placeholder></el-input>
                            </el-form-item>
                            <el-form-item label="Name">
                                <el-input v-model="socSearch.societyName" placeholder></el-input>
                            </el-form-item>
                            <el-form-item>
                                <el-button type="primary" @click="querySocietyPay">搜 索</el-button>
                            </el-form-item>
                            <el-form-item>
                                <el-button type="primary" @click="">輸出文檔</el-button>
                            </el-form-item>
                            <el-form-item>
                                <span class="clear-search" @click="clearSocSearch()">清除搜索</span>
                            </el-form-item>
                        </div>
                        <div style="text-align: right;">
                            <el-form-item>
                                <el-button type="success" @click="paySoc">支付</el-button>
                            </el-form-item>
                            <el-form-item>
                                <el-button type="success" @click="paySoc('all')">支付所有</el-button>
                            </el-form-item>
                        </div>
                    </el-form>
                    <el-table
                        :data="socData"
                        border
                        stripe
                        style="width: 100%"
                        @selection-change="handleSocSelectionChange"
                    >
                        <el-table-column type="selection"></el-table-column>
                        <el-table-column prop="id" label="序號"></el-table-column>
                        <el-table-column prop="societyCode" label="Society Code"></el-table-column>
                        <el-table-column prop="societyName" label="Society Name"></el-table-column>
                        <el-table-column prop="totalPayAmount" label="總未支付金額"></el-table-column>
                        <el-table-column prop="payAmount" label="可支付金額"></el-table-column>
                        <el-table-column prop="sd" label="SD"></el-table-column>
                        <el-table-column label="operation">
                            <template slot-scope="scope">
                                <span class="a-blue" @click="socDetail(scope.row)">詳情</span>
                            </template>
                        </el-table-column>
                    </el-table>
                    <el-pagination
                        background
                        layout="prev, pager, next"
                        :total="socTotal"
                        @current-change="handleSocChange"
                    ></el-pagination>
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</template>

<script>
    export default {
        name: 'info',
        data () {
            return {
                autopayNo: '',
                showMember: false,
                showSociety: false,
                activeName: 'first',
                memberSearch: {
                    page_num: 1,
                    page_size: 10
                },
                socSearch: {},
                memberData: [],
                memberTotal: 0,
                socData: [],
                socTotal: 0,
                memberBaseNoList: [],
                socBaseNoList: []
            }
        },
        methods: {
            queryMemberPay () {
                if(!this.showSociety){
                    this.activeName = 'first'
                }
                this.$http
                  .get('/dist/autopay/lisDistAutopayMemberRetainWithPage', {
                      params: this.memberSearch
                  })
                  .then(res => {
                      if (res.success) {
                          this.memberData = res.data.data.list
                          this.memberTotal = res.data.data.total
                      }
                  })
            },
            clearMemberSearch () {
                this.memberSearch = {}
                this.queryMemberPay()
            },
            handleMemberChange (val) {
                this.memberSearch.page_num = val
                this.queryMemberPay()
            },
            handleMemberSelectionChange (val) {
                if (val) {
                    val.map(item => {
                        this.memberBaseNoList.push(item.ipBaseNo)
                    })
                }
            },
            handleSocSelectionChange (val) {
                console.log(val)
                if (val) {
                    val.map(item => {
                        this.socBaseNoList.push(item.societyCode)
                    })
                }
            },
            querySocietyPay () {
                if(!this.showMember){
                    this.activeName = 'second'
                }
                let ajaxData = Object.assign(
                  {autopayNo: this.autopayNo},
                  this.socSearch
                )
                this.$http
                  .get('/dist/autopay/lisDistAutopaySocRetainWithPage', {
                      params: ajaxData
                  })
                  .then(res => {
                      if (res.success) {
                          this.socData = res.data.data.list
                          this.socTotal = res.data.data.total
                      }
                  })
            },
            clearSocSearch () {
                this.socSearch = {}
                this.querySocietyPay()
            },
            handleSocChange (val) {
                this.socSearch.page_num = val
                this.querySocietyPay()
            },
            memberDetail () {
                this.$router.push({name: 'tobePay'})
            },
            socDetail (row) {
                console.log(row)
                this.$router.push({name: 'tobePaySoc',query:{societyCode:row.societyCode}})
            },
            payMember (type = '') {
                let params = {
                    autopayNo: this.autopayNo
                }
                if (type == 'all') {
                    params.ipBaseNos = ''
                    if(!this.memberData.length){
                        this.$toast({tips:'沒有可支付作品'})
                        return;
                    }
                } else {
                    if(!this.memberBaseNoList.length){
                        this.$toast({tips:'請選擇作品'})
                        return;
                    }
                    params.ipBaseNos = this.memberBaseNoList.join(',')
                }

                this.$http.get('/dist/autopay/paymentDistAutopayMemberRetain', {params}).then(res => {
                    if(res.success){
                        if(res.data.code == 200){
                            this.$toast({tips:'支付成功'})
                            this.queryMemberPay()
                        }
                    }
                })
            },
            paySoc (type = '') {
                let params = {
                    autopayNo: this.autopayNo
                }
                if (type == 'all') {
                    params.societyCodes = ''
                    if(!this.socData.length){
                        this.$toast({tips:'沒有可支付作品'})
                        return;
                    }
                } else {
                    if(!this.socBaseNoList.length){
                        this.$toast({tips:'請選擇作品'})
                        return;
                    }
                    params.societyCodes = this.socBaseNoList.join(',')
                }
                this.$http.get('/dist/autopay/paymentDistAutopaySocRetain', {params}).then(res => {
                    if(res.success){
                        if(res.data.code == 200){
                            this.$toast({tips:'支付成功'})
                            this.querySocietyPay()
                        }
                    }
                })
            },
        },
        activated () {
            let payType = this.$route.query.payType
            this.showMember = payType.indexOf('MBR') != -1 ? true : false
            this.showSociety = payType.indexOf('SOC') != -1 ? true : false

            this.autopayNo = this.$route.query.autopayNo
            this.showMember && this.queryMemberPay()
            this.showSociety && this.querySocietyPay()
        }
    }
</script>

<style>
    .mydetail {
        width: 100% !important;
        display: block !important;
    }

    .mydateform .el-input--suffix {
        width: 200px !important;
    }

    .mydetail .el-form-item__content {
        width: calc(100% - 150px) !important;
    }
</style>
