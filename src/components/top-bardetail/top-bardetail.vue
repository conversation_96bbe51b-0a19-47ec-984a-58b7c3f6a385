<template>
    <div> 
        <el-card class="box-card saveright2" :class="{'saveSmallright': sidebarFold}">
            <div class="slot f-l">
                <slot></slot>
            </div>
            <el-button type="danger" @click="deleteContract" v-if="showDeleteContract">刪除</el-button>
            <el-button type="primary" v-if="noShow && stepshow" @click="stepToggle()">隱藏steps</el-button>
            <el-button type="primary" v-if="noShow && !stepshow" @click="stepToggle()">顯示steps</el-button>
            <el-button type="primary" @click="transfer" v-if="showTransfer">transfer</el-button>
            <el-button type="primary" @click="convert" v-if="showConvert">convert</el-button>
            <el-button type="primary" @click="distribution" v-if="showDistribution">distribution</el-button>
            <el-button type="primary" @click="batchCreate" v-if="showBatchCreate">Batch create</el-button>
            <!-- <el-button type="primary" @click="save()" v-if="!showSave">Save</el-button> -->
        </el-card>
        <!--描点双向实现-->
        <el-card class="box-card position" v-if="stepshow">
            <el-steps
                direction="vertical"
                :active="activeStep">
                    <!-- v-for="(item, index) in (showTransfer?steps:steps.slice(0,7))" -->
                <el-step
                    v-for="(item, index) in steps"
                    :key="index"
                    :title="item.name"
                    @click.native="jump(index)">
                </el-step>
            </el-steps>
        </el-card>
        <el-dialog
                title="請選擇新增作品類型"
                :visible.sync="worksDialogVisible"
                width="500px"
                class="workdialog"
                center>
                <div>
                    <el-table
                    :data="typeData"
                    stripe
                    style="width: 100%" class="typeTable">
                    <el-table-column
                        prop="name">
                        <template slot-scope="scope">
                            <span style="cursor: pointer" @click="selectType(scope.$index,scope.row)">{{scope.row.name}}</span>
                        </template>
                    </el-table-column>
                    </el-table>
                </div>
                <span slot="footer" class="dialog-footer">
            </span>
        </el-dialog>
        <distribution-com ref="distributionCom" v-if="distributionDShow"></distribution-com>
    </div>
</template>
<script>
import distributionCom from "@/views/works/dialog/distribution.vue";
export default {
    props: {
        steps: {
            type: Array,
            default: () =>[]
        },
        showTransfer: {
            type: Boolean,
            default: false
        },
        showConvert: {
            type: Boolean,
            default: false
        },
        showDistribution: {
            type: Boolean,
            default: false
        },
        // 是否显示批量创建作品
        showBatchCreate: {
            type: Boolean,
            default: false
        },
        // 是否完全不显示steps， true 为显示
        noShow: {
            type: Boolean,
            default: true
        },
        showSave: {
            type: Boolean,
            default: false
        },
        showDeleteContract: {
            type: Boolean,
            default: false
        }
    },
    components: {
        distributionCom
    },
    data(){
        return {
            stepshow: false,
            activeStep: 0,

            worksDialogVisible: false,
            typeData: [
                {name: 'Original', type: 'ORG'},
                {name: 'Arranged', type: 'ARG'},
                {name: 'Adapted', type: 'ADP'}
            ],

            distributionDShow: false
        }
    },
    computed: {
        sidebarFold: {
            get () {
                return this.$store.state.common.sidebarFold
            }
        }
    },
    methods: {
        // 描点链接跳转
        jump (index) {
            // 用 class="d_jump" 添加锚点
            let jump = document.querySelectorAll('.step-jump')
            let total = jump[index].offsetTop - 42;
            this.scrollTopIndex = index
            this.activeStep = index
            // this.scrollTop = jump[index].offsetTop
            let distance = document.documentElement.scrollTop || document.body.scrollTop
            // 平滑滚动，时长500ms，每10ms一跳，共50跳
            let step = total / 50
            if (total > distance) {
                smoothDown()
            } else {
                let newTotal = distance - total
                step = newTotal / 50
                smoothUp()
            }
            function smoothDown () {
                if (distance < total) {
                    distance += step
                    document.body.scrollTop = distance
                    document.documentElement.scrollTop = distance
                    setTimeout(smoothDown, 10)
                } else {
                    document.body.scrollTop = total
                    document.documentElement.scrollTop = total
                }
            }
            function smoothUp () {
                if (distance > total) {
                    distance -= step
                    document.body.scrollTop = distance
                    document.documentElement.scrollTop = distance
                    setTimeout(smoothUp, 10)
                } else {
                    document.body.scrollTop = total
                    document.documentElement.scrollTop = total
                }
            }
        },
        onScroll () {
            let _article = document.querySelectorAll('.step-jump')
            _article.forEach((item, index) => {
                if (this.scrollTop >= item.offsetTop) {
                    this.activeStep = index
                }
            })
        },
        // 描点链接跳转结束
        stepToggle() {
            console.log(this.steps)
            this.stepshow = !this.stepshow
        },
        save(){
            this.$emit('save');
        },
        transfer(){
            this.$emit('transfer');
        },
        convert(){
            this.$emit('convert');
        },
        distribution(){
            this.distributionDShow = true;
            this.$nextTick( () => {
                this.$refs.distributionCom.init();
            })
        },
        batchCreate(){
            this.worksDialogVisible = true;
        },
        selectType(index, item){
            // let name = '';
            // if (index === 0) {
            //     name = 'works-originalinfo';
            // }
            // if(index == 1){
            //     name = 'works-arrangedinfo';
            // }
            // if(index == 2){
            //     name = 'works-adaptedinfo';
            // }
            this.worksDialogVisible = false;

            this.$router.push({name: 'worksBatchCreate', query: {id: this.$route.query.id, socId: this.$route.query.socId, type: this.typeData[index].type}})
        },
        deleteContract(){
            this.$emit('deleteContract');
        }

    }
}
</script>
<style lang="scss" scoped>
    .position{
        position: fixed;
        top:150px;
        right: 20px;
        // width:170px;
        width:185px;
        text-align: left;
        background-color: #FFFFFF;
        z-index: 999;
    }
    .el-step {
        cursor: pointer;
    }
    .el-card{
        margin-bottom: 20px;
    }
    .saveright2{
        
        /deep/ .el-card__body{
            padding-top: 14px;
        }
        button{
            padding: 6px 10px;
        }
    }
    /deep/ .workdialog .cell{
        text-align: center;
        line-height: 32px;
    }
</style>

