import store from '../store'
console.log(store)
export default {
    mounted(){
        if(!store.state.common.isListen){
            store.commit('common/updateIsListen',true)
            document.addEventListener('mouseover',(e) => {
                // console.log('222',e)
                if (e.view.location.hash=='#/ClaimHistoryLog'||e.view.location.hash=='#/uploadList') {
                    // alert('claimhistroy')
                    return
                }
                
                let el = e.target
                let name = el.className
                //    console.log('htmlcontract',el.outerHTML.includes('el-tooltip'))
                if (el.outerHTML.includes('<span>')||el.outerHTML.includes('el-tooltip')||el.innerHTML.includes('svg')||el.innerHTML.includes('更多')) {
                    // console.log('psanspnanafpjdgois')
                    return
                }
                if(name == 'cell'){
                    let text = el.innerText
                    if(text){
                        let title = el.getAttribute('title')
                        el.setAttribute('title',text)
                    }

                }
            })
        }

    }
}
