<template>
  <div>
    <el-dialog
      title="银行txt"
      :visible.sync="showExport"
      width="500px"
      :show-close="true"
      :close-on-click-modal="false"
      :before-close='close'
    >
      <el-form :inline="true" class="demo-form-inline" ref="form" @keyup.enter.native="requData()">
        <el-form-item label="autopayNo" label-width="170px" >
          <el-input v-model="exportData.autopayNo"
          placeholder="雙擊查詢"
          @dblclick.native="getAutopay()"
          @change="changeAuto"
          style="width: 150px"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button type="primary" @click="requData()">導出</el-button>
      </div>
    </el-dialog>
    <autopay-no ref="autopay" :autopayNo='exportData.autopayNo' @checkAutopay='checkAutopay' @flag='flag1=true'></autopay-no>
  </div>
</template>
<script>
import autopayNo from './autopayNo';
export default {
  name: "",
  data() {
    return {
      showExport: false,
      exportData: {
        autopayNo: ""
      },
      flag1:true
    };
  },
  components: { autopayNo },
  methods: {
    close(){
      this.showExport=false
      this.exportData = {
        autopayNo: ""
      }
    },
    exportList() {
      this.showExport = true;
    },
    requData() {
      let params = this.$utils.copy(this.exportData);
      if(!params.autopayNo){
        this.$toast({ tips: "autopayNo不能為空" });
        return
      }
      this.$http.get("/dist/autoPay/Report/reportTxtReport", {params, responseType: "blob"}) // exportFSRIS100MemberReport  reportTxtReport
        .then((res) => {
          console.log(res.data)
          // this.showExport = false;
          let tempBlob = new Blob([res.data], { type: 'application/json' })
          let reader = new FileReader()
          reader.onload = e => {
            let res1 = e.target.result
            // 此处对fileReader读出的结果进行JSON解析
            // 可能会出现错误，需要进行捕获
            try {
              let json = JSON.parse(res1)
              this.$toast({ tips: json.message })
              // console.log('========',json)
              //正常json数据格式
            } catch (err) {
              // 该异常为无法将字符串转为json
              // 说明返回的数据是一个流文件
              // 不需要处理该异常，只需要捕获即刻
              this.close()
              this.$utils.downloadByBlobPDF(
              res.data,
              res.headers["content-disposition"]
                ? res.headers["content-disposition"].split(";")[1]
                : "=匯款導出"
              );

            }
          }
          // 将blob对象以文本的方式读出，读出完成后将会执行 onload 方法
          reader.readAsText(tempBlob)
          // if (res.data.type != "application/pdf") {
          //   console.log(JSON.parse(res))
          //   this.$toast({ tips: "導出失敗" });
          // } else {
          //   this.$utils.downloadByBlobPDF(
          //     res.data,
          //     res.headers["content-disposition"]
          //       ? res.headers["content-disposition"].split(";")[1]
          //       : "=匯款導出"
          //   );
          // }
        });
    },
    getAutopay(){
      if(this.flag1){
        this.flag1=false
        this.$nextTick(()=>{
          this.$refs.autopay.autopayDataD()
        })
      }
    },
    changeAuto(data){
      if(data){
        if(this.flag1){
          this.flag1=false
          this.$nextTick(()=>{
            this.$refs.autopay.autopayDataC()
          })
        }
      }

    },
    checkAutopay(data){
      this.flag1=true
      this.$set(this.exportData,'autopayNo',data.autopayNo)
    },
  },
};
</script>
<style scoped>
/deep/ .el-dialog__body {
  padding-top: 0;
}
/deep/ .el-radio + .el-radio {
  margin-left: 0;
}
</style>
