<template>
    <div>
        <el-steps :active="3">
            <el-step title="清單處理"></el-step>
            <el-step title="配置分配代號"></el-step>
            <el-step title="分配配置"></el-step>
            <el-step title="分配提交"></el-step>
        </el-steps>
        <div style="margin-top: 20px">
            <el-tabs v-model="activeName" @tab-click="handleClick">
                <!--分配參數配置Tabs-->
                <el-tab-pane label="分配參數配置" name="first">
                    <param-config @StartTime='StartTimeChange' @EndTime='EndTimeChange'></param-config>
                </el-tab-pane>

                <!--配置管理Tabs-->
                <el-tab-pane label="配置分配金額" name="second">
                    <oMoneyConfig v-if="this.distType == 'o'"></oMoneyConfig>
                    <money-config v-else></money-config>

                </el-tab-pane>

                <!--配置分配清單Tabs-->
                <el-tab-pane label="配置分配清單" name="third" v-if="this.distType !== 'o'">
                    <list-config :endTime='endTime' :startTime='startTime' ref="listConfig"></list-config>
                </el-tab-pane> 
            </el-tabs>
        </div>
    </div>
</template> 

<script>
    import paramConfig from './components/paramConfig';
    import moneyConfig from './components/moneyConfig';
    import oMoneyConfig from './components/omoneyConfig';
    import listConfig from './components/listConfig';

    export default {
        name: 'info',
        data () {
            return {
                activeName: 'first',
                distNo: '',
                distType: '',
                startTime:'',
                endTime:'',

            }
        },
        computed:{
            // distType(){
            //     let distNo = this.$route.query.distNo;
            //     let type = distNo.slice(0,1);
            //     type = type.toLowerCase()
            //     return type;
            // }
            
        },
        mounted(){
            this.distType = this.$route.query.distNo.slice(0, 1).toLowerCase();
            this.distNo = this.$route.query.distNo;
        },
        activated(){
            if(this.distNo != this.$route.query.distNo){
                this.distNo = this.$route.query.distNo;
                this.distType = this.$route.query.distNo.slice(0, 1).toLowerCase();
                this.$set(this, 'distType', this.distType)
            }
        },
        methods: {
            StartTimeChange(data){
                console.log(data)
                this.startTime=data
            },
            EndTimeChange(data){
                this.endTime=data
                console.log(data)
            },
            handleClick (tab, event) {
                if (tab.index == '2') {
                    this.$refs.listConfig.querySourceList();
                }
            }
        },
        components: {
            paramConfig,
            moneyConfig,
            listConfig,
            oMoneyConfig
        }
    }
</script>

