<template>
    <el-dialog
        :title="!dataForm.id ? '新增' : '修改'"
        @close="closeLog"
        :close-on-click-modal="false"
        :visible.sync="visible">
        <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
                 label-width="110px">
            <el-form-item label="name" prop="name">
                <el-input v-model="dataForm.name" placeholder="请输入" :readonly="dataForm.id ? true : false"></el-input>
            </el-form-item>
            <!-- <el-form-item label="nameEn" prop="nameEn">
                <el-input v-model="dataForm.nameEn" placeholder="请输入" :readonly="dataForm.id ? true : false"></el-input>
            </el-form-item> -->
            <el-form-item label="bean名稱" prop="beanName">
                <el-input v-model="dataForm.beanName" placeholder="bean名稱, 如: testTask"></el-input>
            </el-form-item>
            <!--<el-form-item label="方法" prop="methodName">
                <el-input v-model="dataForm.methodName" placeholder="请输入方法名"></el-input>
            </el-form-item>-->
            <el-form-item label="cron表达式" prop="cronExpression">
                <el-input v-model="dataForm.cronExpression" placeholder="如: 0 0 12 * * * ?"></el-input>
            </el-form-item>
            <el-form-item label="備註" prop="remark">
                <el-input v-model="dataForm.remark" placeholder="備註"></el-input>
            </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">確定</el-button>
    </span>
    </el-dialog>
</template>

<script>
    export default {
        data () {
            return {
                visible: false,
                dataForm: {
                    id: 0,
                    beanName: '',
                    name:'',
                    nameEn:'',
                    methodName:'',
                    params: '',
                    cronExpression: '',
                    remark: '',
                    status: 0
                },
                dataRule: {
                    beanName: [
                        {required: true, message: 'beanName不能為空', trigger: 'blur'}
                    ],
                    cronExpression: [
                        {required: true, message: 'cron表达式不能為空', trigger: 'blur'}
                    ],
                    name: [
                        {required: true, message: 'name不能為空', trigger: 'blur'}
                    ],
                    nameEn: [
                        {required: true, message: 'nameEn不能為空', trigger: 'blur'}
                    ],
                    methodName: [
                        {required: true, message: '方法名不能為空', trigger: 'blur'}
                    ]
                }
            }
        },
        methods: {
            init (row) {
                console.log(row)
                if(row){
                    this.dataForm = JSON.parse(JSON.stringify(row))
                }else{
                    this.dataForm = {
                        id: '',
                        beanName: '',
                        name:'',
                        nameEn:'',
                        methodName:'',
                        cronExpression: '',
                        remark: '',
                        status: 0
                    }
                }
                this.visible = true
            },
            // 表单提交
            dataFormSubmit () {
                this.$refs['dataForm'].validate((valid) => {
                    if (valid) {
                        if(this.dataForm.id){
                            //修改
                            this.$http.post('/sys/job/edit',this.dataForm).then(res => {
                                if(res.success){
                                    if(res.data.code === 200){
                                        this.initParentList()
                                        this.$emit('hideLog')
                                    }else{
                                        this.$message.error(res.data.message)
                                    }
                                }else{
                                    this.$message.error(res.data.message)
                                }
                            })
                        }else{
                            //新增
                            this.$http.put('/sys/job/add',this.dataForm).then(res => {
                                console.log(res)
                                if(res.success){
                                    if(res.data.code === 200){
                                        this.initParentList()
                                        this.$emit('hideLog')
                                    }else if(res.data.code ===  20005){
                                        this.$message({
                                            message:res.data.message,
                                            type:'warning'
                                        })
                                    }
                                }
                            })
                        }
                    }
                })
            },
            closeLog(){
                this.$refs.dataForm.clearValidate()
            },
            initParentList(){
                this.$emit('refreshDataList')
            }
        }
    }
</script>
<style scoped lang="scss">
    /deep/ .el-form-item__label {
        margin-left: 0 !important;
    }
</style>
