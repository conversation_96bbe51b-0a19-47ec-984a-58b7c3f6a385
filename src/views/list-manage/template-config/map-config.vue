<template>
    <div>
        <el-form :inline="true" :model="searchForm" class="demo-form-inline"  @keyup.enter.native='getList()'>
            <el-form-item label="字段">
                <el-autocomplete
                    v-model="keyWord"
                    :fetch-suggestions="getWordList"
                    placeholder="请输入内容"
                    @select="handleSelect1"
                    style="width:300px"
                >
                    <template slot-scope="scope">
                        <p class="auto-p">字段：{{scope.item.key}}</p>
                        <p class="auto-p">描述：{{scope.item.description}}</p>
                    </template>
                </el-autocomplete>
            </el-form-item>
            <el-form-item label="表頭">
                <el-input v-model="searchForm.data.name" placeholder=""></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="getList">搜索</el-button>
            </el-form-item>
            <el-form-item>
                <el-button type="success" @click="add">新增</el-button>
            </el-form-item>
            <el-form-item>
                <span class="clear-search" @click="clearSearch()">清除搜索</span>
            </el-form-item>
        </el-form>
        <el-table
            :data="tableData"
            border
            stripe
            style="width: 100%"
            v-loading="loading">
            <el-table-column
                prop="id"
                label="id"
                width="180px">
            </el-table-column>
            <el-table-column
                prop="key"
                width="280"
                label="字段">
            </el-table-column>
            <el-table-column
                prop="keyDescription"
                label="字段說明">
            </el-table-column>
            <el-table-column
                prop="name"
                label="表頭">
            </el-table-column>
            <el-table-column
                prop="description"
                label="表頭說明">
            </el-table-column>
            <el-table-column
                prop="createTime"
                width="200px"
                label="創建時間">
            </el-table-column>
            <el-table-column
                label="operation"
                align="center"
                >
                <template slot-scope="scope">
                    <el-button type="text" size="small" @click="deleteRow(scope.row.id)">刪除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :total="total" @current-change="handleCurrentChange">
        </el-pagination>

        <el-dialog title="新增" :visible.sync="dialogFormVisible" width="35%" @close="closeDialog" :close-on-click-modal="false">
            <el-form :model="addForm" :rules="rules" ref="addDialog" label-position="right" label-width="80px">
                <el-form-item label="字段" class="is-required">
                    <el-autocomplete
                        v-model="addFormKey"
                        :fetch-suggestions="getWordList"
                        placeholder="请输入内容"
                        @select="handleSelect2"
                        style="width: 377px"
                    >
                        <template slot-scope="scope">
                            <p class="auto-p">字段：{{scope.item.key}}</p>
                            <p class="auto-p">描述：{{scope.item.description}}</p>
                        </template>
                    </el-autocomplete>
                </el-form-item>
                <el-form-item label="表頭" prop="name">
                    <el-input v-model="addForm.name" placeholder=""></el-input>
                </el-form-item>
                <el-form-item label="表頭說明">
                    <el-input v-model="addForm.description" placeholder=""></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogFormVisible = false">取 消</el-button>
                <el-button type="primary" @click="addConfirm">确 定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
    export default {
        name: 'map-config',
        data(){
            return {
                loading:false,
                total:0,
                keyWord:'',
                searchForm:{
                    data:{
                        key:'',
                        name:''
                    },
                    page:{
                        pageNum:1,
                        pageSize:10
                    }
                },
                tableData:[{id:1}],
                dialogFormVisible:false,
                addForm:{},
                addFormKey:'',
                rules:{
                    name:[{required: true}]
                }
            }
        },
        mounted(){
            this.getList()
        },
        methods:{
            clearSearch(){
                this.searchForm = {
                    data:{
                        key:'',
                        name:''
                    },
                    page:{
                        pageNum:1,
                          pageSize:10
                    }
                }
                this.getList()
            },
            getList(){
                if(!this.searchForm.data.listBasicFileConfigId){
                    this.keyWord = ''
                }
                this.loading = true
                this.$http.post('/listBasicFileConfig/getListBasicFileConfigDetailList',this.searchForm).then(res => {
                    this.loading = false
                    if(res.success){
                        this.tableData = res.data.list
                        this.total = res.data.total
                    }
                })
            },
            add(){
                this.addForm = {}
                this.addFormKey = ''
                this.dialogFormVisible =  true
            },
            addConfirm(){
                if(!this.addForm.listBasicFileConfigId){
                    this.addFormKey = ''
                    this.$toast({tips:'請從下拉列表中選擇指定數據'})
                    return
                }
                this.$refs['addDialog'].validate(valid => {
                    if(valid){
                        this.$http.post('/listBasicFileConfig/addListBasicFileConfigDetailById',this.addForm).then(res => {
                            console.log(res)
                            if(res.success){
                                this.$toast({tips:'新增成功'})
                                this.dialogFormVisible =  false
                                this.getList()
                            }else{
                                this.$toast({tips:'新增失敗'})
                            }
                        })
                    }
                })

            },
            closeDialog(){
                this.$refs['addDialog'].resetFields()
            },
            getWordList(query,cb){
                this.searchForm.data.listBasicFileConfigId = ''
                let ajaxData = {
                    data:{
                        key:query
                    },
                    page:{
                        pageNum:1,
                        pageSize:9999
                    }
                }
                this.$http.post('/listBasicFileConfig/getListBasicFileConfigList',ajaxData).then(res => {
                    if(res.success){
                        let list = res.data.list
                        cb(list)
                    }
                })
            },
            handleSelect1(item){
                this.keyWord = item.key
                this.searchForm.data.listBasicFileConfigId = item.id
            },
            handleSelect2(item){
                this.addFormKey = this.addForm.key = item.key

                this.addForm.listBasicFileConfigId = item.id
            },
            deleteRow(id){
                this.$alert('確定要刪除嗎？', '刪除', {
                    confirmButtonText: '确定',
                    showCancelButton:true,
                    callback: action => {
                        if(action == 'confirm'){
                            this.$http.get('/listBasicFileConfig/deleteListBasicFileConfigDetailById',{params:{id:id}}).then(res => {
                                if(res.success){
                                    this.$toast({tips:'刪除成功'})
                                    this.getList()
                                }
                            })
                        }
                    }
                });
            },
            handleCurrentChange(page){
                this.searchForm.page.pageNum = page
                this.getList()
            }
        }
    }
</script>

<style scoped lang="scss">
    /deep/ .el-form-item__label {
        margin-left: 0 !important;
    }
    .auto-p{
        margin: 10px 0;
        line-height: 1.2;
    }
</style>
