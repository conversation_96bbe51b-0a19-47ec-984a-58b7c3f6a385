/** axios封装
 * 请求拦截、相应拦截、错误统一处理
 */
import axios from 'axios'
import QS from 'qs'
import store from '../store/index'
import {toast} from '../components/toast';
import {Loading} from 'element-ui'
import router from '../router'
let loading = null;
axios.defaults.baseURL = '/api'
/*
// 环境的切换
if (process.env.NODE_ENV === 'development') {
    axios.defaults.baseURL = '/api'
} else if (process.env.NODE_ENV === 'testing') {
    axios.defaults.baseURL = '/api'
} else if (process.env.NODE_ENV === 'production') {
    axios.defaults.baseURL = 'http://180.166.161.210:18075/api'
    // axios.defaults.baseURL = 'http://192.168.88.205:80/api'
}
*/
// 请求超时时间
// axios.defaults.timeout = 120000
// 是否携带cookies
axios.defaults.withCredentials = true
// post请求头
axios.defaults.headers.post['Content-Type'] = 'application/json;charset=UTF-8'
// 请求拦截器
axios.interceptors.request.use(
    config => {
        // 每次发送请求之前判断是否存在token，如果存在，则统一在http请求的header都加上token，不用每次请求都手动添加了
        // 即使本地存在token，也有可能token是过期的，所以在响应拦截器中要对返回状态进行判断
        const token = store.state.token
        token && (config.headers.Authorization = token)

        config.params = {
            _v: Date.parse(new Date())/1000,
            ...config.params
        }
        // console.log(config);
        if(config.method == 'post' && !config.loading){
            loading = Loading.service({
                lock: true,
                text: 'Loading',
                background: 'rgba(0,0,0,0.3)'
            })

        // }else if(config.method == 'get'){
        //     config.params = {
        //         _v: Date.parse(new Date())/1000,
        //         ...config.params
        //     }
        }

        return config
    },
    error => {
        return Promise.error(error)
    })

// 响应拦截器
axios.interceptors.response.use(
    response => {
        loading && loading.close();
        if (response.status === 200) {
            response.success = true;
            if(typeof response.data == 'object'){
                let message = response.data.message
                if(message){
                    message = message.replace(/[！？。!?.]$/,'')
                }
                response.data.message = message
            }
            return Promise.resolve(response);
        } else {


            return Promise.resolve(response);
        }
    },error => {
        loading && loading.close();

        let status = error.response ? error.response.status : '';
        console.log(error.response);
        if(status == 401){
            router.push({name: 'login'});
        }else if(status == 400){
            toast({tips: error.response.message});
        }else if(status == 500){
            toast({tips: '服務器繁忙', time: 2500});
        }else{
            if(error.response.data){
                toast({tips: error.response.data})
            }else{
                toast({tips: '服務器繁忙', time: 2500});
            }
        }

        return Promise.reject(error);
    }
    // 不是200的情况
)
/**
 * get方法，对应get请求
 * @param {String} url [请求的url地址]
 * @param {Object} params [请求时携带的参数]
 */
// export function get (url, params, timeout) {
//     return new Promise((resolve, reject) => {
//         axios.get(url, {
//             params: params,
//         },
//         timeout).then(res => {
//             resolve(res.data)
//         }).catch(err => {
//             reject(err.data)
//         })
//     })
// }
export function get (url, params) {
    return new Promise((resolve, reject) => {
        axios.get(url, {
            params: params,
        }
        ).then(res => {
            resolve(res.data)
        }).catch(err => {
            reject(err.data)
        })
    })
}
/**
 * post方法，对应post请求
 * @param {String} url [请求的url地址]
 * @param {Object} params [请求时携带的参数]
 */
export function post (url, params) {
    return new Promise((resolve, reject) => {
        axios.post(url, QS.stringify(params))
            .then(res => {
                resolve(res.data)
            })
            .catch(err => {
                reject(err.data)
            })
    })
}

export default axios
