<!-- 比對頁面 -->
<template>
    <div class="ipibpox">
        <el-form :inline="true" :model="formType" class="demo-form-inline" label-width="120px" label-position="left">
            <div style="position: relative;padding-left: 10px;padding-bottom: 46px;margin-bottom: 20px;">
                <el-upload
                    ref="fileUpload"
                    class="upload-demo"
                    action="api/sampleMatch/matchResult"
                    :on-change="handleChange"
                    :data="formType"
                    :auto-upload="false"
                    :file-list="fileList"
                    :on-success="onSuccess"
                    :on-error="onError">
                    <div style="position: absolute;bottom: 0;">
                        <el-button slot="trigger" size="small" type="primary" :disabled="isAbled">选取文件</el-button>
                    </div>
                </el-upload>
                <div style="position: absolute;bottom: 0;left: 110px;">
                    <el-button style="margin-left: 10px;" size="small" type="success" @click="submitUpload" :disabled="isAbled">{{isAbled ? '比對中' : '開始比對'}}</el-button>
                    <!-- todo-guan 等建新確定文件格式 01/11 -->
                    <!-- <div class="el-upload__tip">支持擴展名：.xlsx .xls</div> -->
                </div>
            </div>
        </el-form>
        <el-table   stripe :data="tableData" border style="width: 100%" empty-text="無匹配數據">
            <el-table-column
                prop="id"
                label="ID"
                width="80">
            </el-table-column>
            <el-table-column
                prop="worknum"
                label="Work No"
                width="120">
            </el-table-column>
            <el-table-column
                prop="worknumSociety"
                label="Work Soc"
                width="100">
            </el-table-column>
            <el-table-column
                prop="workName"
                label="Work Name"
                min-width="110">
            </el-table-column>
            <el-table-column
                prop="workArtist"
                label="Artist">
                <template slot-scope="scope">
                    <span :title="scope.row.workArtist">{{scope.row.workArtist}}</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="workComposer"
                label="Composer"
                min-width="100">
                <template slot-scope="scope">
                    <span :title="scope.row.workComposer">{{scope.row.workComposer}}</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="workIsrc"
                label="Isrc">
                <template slot-scope="scope">
                    <span :title="scope.row.workIsrc">{{scope.row.workIsrc}}</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="workIswc"
                label="Iswc">
                <template slot-scope="scope">
                    <span :title="scope.row.workIswc">{{scope.row.workIswc}}</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="url"
                label="Sample Url"
                min-width="100">
                <template slot-scope="scope">
                    <span :title="scope.row.url">{{scope.row.url}}</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="matchscore"
                label="Match Score"
                min-width="110">
                <template slot-scope="scope">
                    {{ scope.row.matchscore | ToFix}}
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
    export default {
        name: 'sampleDateList',
        data () {
            return {
                isAbled: false,
                tableData: [],
                total: 0,
                search: {},
                loading: false,
                config: {
                    status: {
                        0: '等待處理',
                        1: '處理中',
                        2: '處理完成',
                        3: '處理失敗'
                    }
                },

                //上传
                formType: {},
                fileList: [],
                hasUpload: false

            }
        },
        filters: {
            Status: function(status){
                let config = {
                    0: '等待處理',
                    1: '處理中',
                    2: '處理完成',
                    3: '處理失敗'
                }
                return config[status]
            },
            ToFix: function(num){
                 return Math.floor(num * 100)/100;
            }
        },
        created(){
        },
        methods: {
            handleChange(file, fileList){
                console.log('change ',file);
                this.fileList = [file];
            },
            submitUpload(){
                if(this.hasUpload){
                    this.hasUpload = false;
                }
                if(this.fileList.length == 0){
                    this.$toast({tips: '請先選擇要比對的文件'})
                    return;
                }
                if(this.fileList.length && !this.hasUpload){
                    this.$refs.fileUpload.submit();
                    // this.loading = this.$loading();
                    this.isAbled = true;
                }
            },
            onSuccess(res) {
                // this.loading.close();
                this.isAbled = false;
                if(res.code && res.code != 200){
                    this.$toast({tips: res.message})
                }else{
                    this.tableData = res;
                }
                setTimeout(() => {
                    this.$refs.fileUpload.clearFiles()
                }, 200)
                
            },
            onError(){
                this.isAbled = false;

                // this.loading.close();
                setTimeout(() => {
                    this.$refs.fileUpload.clearFiles()
                    this.fileList = [];
                }, 200)
                this.$toast({tips: '比對失敗'})
            }
            
        }
    }
</script>

<style lang="scss" scoped>
.upload{
    padding-top: 40px;
}
.el-upload__tip{
    position: relative;
    bottom: -8px;
    display: inline-block;
    font-size: 14px;
}
 /deep/ .cell{
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    word-break: break-all;
}
/deep/ .el-upload-list {
    display: inline-block;
    max-width: 100%;
}
</style>
