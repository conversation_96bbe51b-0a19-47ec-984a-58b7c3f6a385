<template>
  <div class="detail-drawer">
    <div class="drawer-header">
      <h2>清單明細</h2>
      <el-button type="text" class="close-btn" @click="closeDrawer">
        <i class="el-icon-close"></i>
      </el-button>
    </div>
    <!-- 基本信息 -->
    <div class="section-title">基本信息</div>
    <el-form :inline="true" label-width="120px" class="form-section">
      <el-form-item label="水單ID">
        <el-input v-model="rowData.receiptId" readonly></el-input>
      </el-form-item>
      <el-form-item label="协会代碼">
        <el-input v-model="rowData.sourceSocietyCode" readonly></el-input>
      </el-form-item>
      <el-form-item label="协会名稱">
        <el-input v-model="rowData.sourceSocietyName" readonly></el-input>
      </el-form-item>
      <el-form-item label="協會分配序號">
        <el-input v-model="rowData.distOrderNumber" readonly></el-input>
      </el-form-item>
      <el-form-item label="分配代號">
        <el-input v-model="rowData.distNo" readonly></el-input>
      </el-form-item>
      <el-form-item label="通知/寄送日期">
        <el-input v-model="formattedDate" readonly></el-input>
      </el-form-item>
      <el-form-item label="國外分配代號">
        <el-input v-model="rowData.sourceDistNo" readonly></el-input>
      </el-form-item>
      <el-form-item label="國外分配年度">
        <el-input v-model="rowData.sourceDistYear" readonly></el-input>
      </el-form-item>
      <el-form-item label="檢核狀態">
        <el-input v-model="checkStatus" readonly></el-input>
      </el-form-item>
      <el-form-item label="匯款幣別金額">
        <el-input v-model="rowData.receiptCurrencyCode" readonly style="width: 120px;"></el-input>
        <el-input v-model="formattedCurrencyAmount" readonly style="width: 140px;"></el-input>
      </el-form-item>
      <el-form-item label="銀行手續費">
        <el-input v-model="rowData.receiptChargeCurrencyCode" readonly style="width: 120px;"></el-input>
        <el-input v-model="formattedChargeAmount" readonly style="width: 140px;"></el-input>
      </el-form-item>
      <el-form-item label="匯率">
        <el-input v-model="rowData.exchangeRate" readonly></el-input>
      </el-form-item>
      <el-form-item label="水單匯款金額">
        <el-input v-model="rowData.receiptCurrencyCode2" readonly style="width: 120px;"></el-input>
        <el-input v-model="formattedReceiptAmount" readonly style="width: 140px;"></el-input>
      </el-form-item>
      <el-form-item label="水單台幣入賬">
        <el-input v-model="formattedLocalAmount" readonly></el-input>
      </el-form-item>
      <el-form-item label="合計已沖台幣金額">
        <el-input v-model="formattedLocalStrikeAmount" readonly></el-input>
      </el-form-item>
      <el-form-item label="審核備註">
        <el-input type="textarea" v-model="rowData.checkRemark" readonly style="width: 500px;"></el-input>
      </el-form-item>
    </el-form>

    <!-- Summary 部分 -->
    <div class="section-title">Summary</div>
    <div v-if="summaryData.length === 0" class="empty-data-tip">正在加载数据...</div>
    <el-table :data="summaryData" border class="detail-table" empty-text="" v-else>
      <el-table-column label="分配幣別" prop="summaryCurrencyCode"></el-table-column>
      <el-table-column label="分配金額" prop="summayTotalAmount" align="right">
        <template slot-scope="scope">
          <span>{{ formatThousand(scope.row.summayTotalAmount || 0, 2) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="管理費" prop="summaryCommissionAmount" align="right">
        <template slot-scope="scope">
          <span>{{ formatThousand(scope.row.summaryCommissionAmount || 0, 2) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="稅款" prop="summaryTaxAmount" align="right">
        <template slot-scope="scope">
          <span>{{ formatThousand(scope.row.summaryTaxAmount || 0, 2) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="手續費" prop="summaryChargeAmount" align="right">
        <template slot-scope="scope">
          <span>{{ formatThousand(scope.row.summaryChargeAmount || 0, 2) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="匯款金額" prop="summaryReceiptAmount" align="right">
        <template slot-scope="scope">
          <span>{{ formatThousand(scope.row.summaryReceiptAmount || 0, 0) }}</span>
        </template>
      </el-table-column>
    </el-table>

    <!-- Statement 部分 -->
    <div class="section-title">
      <div>
        <h3>Statement</h3>
        <p>文件匯總金額：<span class="red">{{ rowData.totalStatementAmount || '0' }}</span></p>
        <p>文件讀取總金額：<span class="red">{{ rowData.fileStatementAmount || '0' }}</span></p>
      </div>
    </div>
    <div v-if="statementData.length === 0" class="empty-data-tip">正在加载数据...</div>
    <el-table :data="statementData" border class="detail-table" empty-text="" v-else>
      <el-table-column label="分配幣別" prop="currencyCode">
        <template slot-scope="scope">
          <span v-if="scope.$index !== statementData.length - 1">{{ scope.row.currencyCode }}</span>
          <span v-else class="total-row">總計</span>
        </template>
      </el-table-column>
      <el-table-column label="分配金額" prop="receiptAmount" align="right">
        <template slot-scope="scope">
          <span :class="{'total-row': scope.$index === statementData.length - 1}">{{ formatThousand(scope.row.receiptAmount || 0, 2) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="台幣金額" prop="localAmount" align="right">
        <template slot-scope="scope">
          <span :class="{'total-row': scope.$index === statementData.length - 1}">{{ formatThousand(scope.row.localAmount || 0, 0) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="upload總金頹" prop="uploadAmount" align="right">
        <template slot-scope="scope">
          <span :class="{'total-row': scope.$index === statementData.length - 1}">{{ formatThousand(scope.row.uploadAmount || 0, 2) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="manual-upload總金頷" prop="manualUploadAmount" align="right">
        <template slot-scope="scope">
          <span :class="{'total-row': scope.$index === statementData.length - 1}">{{ formatThousand(scope.row.manualUploadAmount || 0, 2) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="statement總金額" prop="statementAmount" align="right">
        <template slot-scope="scope">
          <span :class="{'total-row': scope.$index === statementData.length - 1}">{{ formatThousand(scope.row.statementAmount || 0, 2) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="no statement總金額" prop="noStatementAmount" align="right">
        <template slot-scope="scope">
          <span :class="{'total-row': scope.$index === statementData.length - 1}">{{ formatThousand(scope.row.noStatementAmount || 0, 2) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="percentage(%)" prop="percentage">
        <template slot-scope="scope">
          <span :class="{'total-row': scope.$index === statementData.length - 1}">{{ scope.row.percentage || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="deduction扣除額" prop="deduction" align="right">
        <template slot-scope="scope">
          <span :class="{'total-row': scope.$index === statementData.length - 1}">{{ formatThousand(scope.row.deduction || 0, 2) }}</span>
        </template>
      </el-table-column>
    </el-table>

    <!-- FIE 部分 -->
    <div class="section-title">
      <div>
        <h3>FIE</h3>
        <p>文件內解析金額：<span class="red">{{ rowData.fieTotalAmount || '0' }}</span></p>
      </div>
    </div>
    <div v-if="fieData.length === 0" class="empty-data-tip">正在加载数据...</div>
    <el-table :data="fieData" border class="detail-table" empty-text="" v-else>
      <el-table-column label="分配幣別" prop="currencyCode">
        <template slot-scope="scope">
          <span v-if="scope.$index !== fieData.length - 1">{{ scope.row.currencyCode }}</span>
          <span v-else class="total-row">總計</span>
        </template>
      </el-table-column>
      <el-table-column label="分配金額" prop="receiptAmount" align="right">
        <template slot-scope="scope">
          <span :class="{'total-row': scope.$index === fieData.length - 1}">{{ formatThousand(scope.row.receiptAmount || 0, 2) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="台幣金額" prop="localAmount" align="right">
        <template slot-scope="scope">
          <span :class="{'total-row': scope.$index === fieData.length - 1}">{{ formatThousand(scope.row.localAmount || 0, 0) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="upload總金頹" prop="uploadAmount" align="right">
        <template slot-scope="scope">
          <span :class="{'total-row': scope.$index === fieData.length - 1}">{{ formatThousand(scope.row.uploadAmount || 0, 2) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="manual-upload總金頷" prop="manualUploadAmount" align="right">
        <template slot-scope="scope">
          <span :class="{'total-row': scope.$index === fieData.length - 1}">{{ formatThousand(scope.row.manualUploadAmount || 0, 2) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="statement總金額" prop="statementAmount" align="right">
        <template slot-scope="scope">
          <span :class="{'total-row': scope.$index === fieData.length - 1}">{{ formatThousand(scope.row.statementAmount || 0, 2) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="no statement總金額" prop="noStatementAmount" align="right">
        <template slot-scope="scope">
          <span :class="{'total-row': scope.$index === fieData.length - 1}">{{ formatThousand(scope.row.noStatementAmount || 0, 2) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="分配代號或年度" prop="fieDistYear">
        <template slot-scope="scope">
          <span :class="{'total-row': scope.$index === fieData.length - 1}">{{ scope.row.fieDistYear || '-' }}</span>
        </template>
      </el-table-column>
    </el-table>

    <!-- FIE文件列表 -->
    <!--
     <div class="file-list-section" v-if="fieFileList.length > 0">
      <div class="section-title">
        <span>文件列表</span>
      </div>
      <div class="file-list">
        <p v-for="(item, index) in fieFileList"
           :key="index"
           :class="['download-link', {'downloaded': isFileDownloaded(item)}]"
           @click="download(item)">
          <i :class="['el-icon-document', {'downloaded-icon': isFileDownloaded(item)}]"></i>
          <span>{{item.name}}</span>
          <span v-if="isFileDownloaded(item)" class="downloaded-text">(已下載)</span>
        </p>
      </div>
    </div>
    -->
  </div>
</template>

<script>
import { formatThousand } from '@/utils';

// 使用普通的数字计算替代BigNumber
function safeAdd(a, b) {
  return Number(a) + Number(b);
}

export default {
  name: 'DetailDrawer',
  props: {
    rowData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      summaryData: [],
      statementData: [],
      fieData: [],
      fieFileList: [],
      downloadedFiles: [], // 用于跟踪已下载的文件
      isDataLoaded: false, // 用于标记数据是否已加载
      isRequestSuccessful: false // 用于标记请求是否成功
    }
  },
  computed: {
    formatThousand() {
      return formatThousand;
    },
    formattedDate() {
      if (this.rowData.messageDate) {
        return this.rowData.messageDate.split(' ')[0];
      }
      return '';
    },
    formattedCurrencyAmount() {
      return formatThousand(this.rowData.receiptCurrencyAmount || 0, 2);
    },
    formattedReceiptAmount() {
      return formatThousand(this.rowData.receiptAmount || 0, 2);
    },
    formattedLocalAmount() {
      return formatThousand(this.rowData.localAmount || 0, 0);
    },
    formattedChargeAmount() {
      return formatThousand(this.rowData.receiptChargeAmount || 0, 2);
    },
    formattedLocalStrikeAmount() {
      return formatThousand(this.rowData.localStrikeAmount || 0, 0);
    },
    checkStatus() {
      return this.rowData.checkStatus === 'Y' ? '已檢核' : '未檢核';
    }
  },
  created() {
    // 从 localStorage 中读取已下载文件的记录
    const downloadedFilesStr = localStorage.getItem('downloadedFiles');
    if (downloadedFilesStr) {
      try {
        this.downloadedFiles = JSON.parse(downloadedFilesStr);
      } catch (e) {
        console.error('解析已下载文件记录失败:', e);
        this.downloadedFiles = [];
      }
    }
  },

  mounted() {
    // 检查是否有预加载的数据
    if (this.rowData && this.rowData.detailData) {
      // 直接使用预加载的数据
      this.summaryData = this.rowData.summaryData || [];
      this.statementData = this.rowData.statementData || [];
      this.fieData = this.rowData.fieData || [];
      this.fieFileList = this.rowData.fieFileList || [];

      // 处理Statement数据，确保有且只有一行总计行
      if (this.statementData.length > 0) {
        // 检查是否已经有总计行
        const hasTotal = this.statementData.some(item => item.isTotal === true);
        if (!hasTotal) {
          // 添加总计行
          const emptyTotal = this.getEmptyTotal();
          emptyTotal.isTotal = true; // 标记为总计行
          this.statementData.push(emptyTotal);
          this.calcTotal(this.statementData);
        }
      }

      // 处理FIE数据，确保有且只有一行总计行
      if (this.fieData.length > 0) {
        // 检查是否已经有总计行
        const hasTotal = this.fieData.some(item => item.isTotal === true);
        if (!hasTotal) {
          // 添加总计行
          const emptyTotal = this.getEmptyTotal();
          emptyTotal.isTotal = true; // 标记为总计行
          this.fieData.push(emptyTotal);
          this.calcTotal(this.fieData);
        }
      }

      // 设置数据已加载标记
      this.isDataLoaded = true;
    } else {
      // 重置所有标记，确保每次打开抽屉时都能正确加载数据
      this.isDataLoaded = false;

      // 在mounted中加载数据，但使用延时确保组件已完全渲染
      this.$nextTick(() => {
        if (this.rowData && this.rowData.receiptId) {
          console.log('在mounted的nextTick中加载数据');
          this.fetchDetailData();
        }
      });
    }
  },
  watch: {
  },
  methods: {
    // 关闭抽屉
    closeDrawer() {
      // 获取当前行的展开图标并点击
      const expandEl = this.$el.closest('tr').previousElementSibling.querySelector('.el-table__expand-icon');
      if (expandEl) {
        // 重置所有标记，以便下次打开时可以重新加载数据
        this.isDataLoaded = false;

        console.log('关闭抽屉，重置标记');

        // 添加延时，避免与其他操作冲突
        setTimeout(() => {
          expandEl.click();
        }, 10);
      }
    },

    fetchDetailData() {
      // 如果没有receiptId，则不请求
      if (!this.rowData.receiptId) {
        return;
      }

      // 如果数据已经加载完成，则不再请求
      if (this.isDataLoaded) {
        return;
      }

      // 先清空数据，避免显示旧数据
      this.summaryData = [];
      this.statementData = [];
      this.fieData = [];
      this.fieFileList = [];



      // 创建一个空的总计对象
      const emptyTotal = this.getEmptyTotal();

      console.log("--------------正在发送请求-----------------")
        if (!this.isRequestSuccessful) {
            this.$http.get(
                '/listOverseasReceiptDetails/getListOverseasReceiptDetailsByReceiptId',
                { params: { receiptId: this.rowData.receiptId } }
            ).then(res => {
                //  请求成功
                if (res.success && res.data.code === 200) {
                    isRequestSuccessful: true;
                    const data = res.data.data;
                    console.log('获取到的详情数据:', data);

                    if (data) {
                        // 确保 rowData 包含所需的属性
                        this.$set(this.rowData, 'totalStatementAmount', data.totalStatementAmount || '');
                        this.$set(this.rowData, 'fileStatementAmount', data.fileStatementAmount || '');
                        this.$set(this.rowData, 'fieTotalAmount', data.fieTotalAmount || '');
                        // 确保设置messageDate字段，用于显示通知/寄送日期
                        this.$set(this.rowData, 'messageDate', data.messageDate || '');
                        // 添加缺失的四个字段
                        this.$set(this.rowData, 'exchangeRate', data.exchangeRate || '');
                        this.$set(this.rowData, 'localStrikeAmount', data.localStrikeAmount || 0);
                        this.$set(this.rowData, 'checkRemark', data.checkRemark || '');
                        // 设置receiptCurrencyCode2字段，用于显示水單匯款金額
                        this.$set(this.rowData, 'receiptCurrencyCode2', data.receiptCurrencyCode || '');
                        // 设置receiptCurrencyAmount字段，用于显示匯款幣別金額，与O水单编辑明细保持一致
                        this.$set(this.rowData, 'receiptCurrencyAmount', data.receiptCurrencyAmount || 0);

                        // 处理 summary 数据 - 确保只有一行数据
                        this.summaryData = [data]; // 直接设置为包含单个元素的数组，而不是添加
                        console.log('summaryData:', this.summaryData);

                        // 强制触发视图更新
                        this.$forceUpdate();

                        // 处理 statement 数据 - 与水单编辑明细相同
                        console.log('listOverseasReceiptStatementList:', data.listOverseasReceiptStatementList);
                        try {
                            if (data.listOverseasReceiptStatementList && Array.isArray(data.listOverseasReceiptStatementList) && data.listOverseasReceiptStatementList.length > 0) {
                                // 深拷贝数组，避免引用问题
                                this.statementData = JSON.parse(JSON.stringify(data.listOverseasReceiptStatementList));
                                // 先检查是否已经有总计行
                                const hasTotal = this.statementData.some(item => item.isTotal === true);
                                if (!hasTotal) {
                                    // 添加总计行
                                    const totalRow = {...emptyTotal};
                                    totalRow.isTotal = true; // 标记为总计行
                                    this.statementData.push(totalRow);
                                    // 计算总计
                                    this.calcTotal(this.statementData);
                                }
                                console.log('处理后的statementData:', this.statementData);
                            } else {
                                this.statementData = [emptyTotal];
                                console.log('无statement数据，使用空总计:', this.statementData);
                            }
                        } catch (err) {
                            console.error('处理statement数据时出错:', err);
                            this.statementData = [emptyTotal];
                        }

                        // 处理 FIE 数据 - 与水单编辑明细相同
                        console.log('listOverseasReceiptStatementFieList:', data.listOverseasReceiptStatementFieList);
                        try {
                            if (data.listOverseasReceiptStatementFieList && Array.isArray(data.listOverseasReceiptStatementFieList) && data.listOverseasReceiptStatementFieList.length > 0) {
                                // 深拷贝数组，避免引用问题
                                this.fieData = JSON.parse(JSON.stringify(data.listOverseasReceiptStatementFieList));
                                // 先检查是否已经有总计行
                                const hasTotal = this.fieData.some(item => item.isTotal === true);
                                if (!hasTotal) {
                                    // 添加总计行
                                    const totalRow = {...emptyTotal};
                                    totalRow.isTotal = true; // 标记为总计行
                                    this.fieData.push(totalRow);
                                    // 计算总计
                                    this.calcTotal(this.fieData);
                                }
                                console.log('处理后的fieData:', this.fieData);
                            } else {
                                this.fieData = [emptyTotal];
                                console.log('无fie数据，使用空总计:', this.fieData);
                            }
                        } catch (err) {
                            console.error('处理FIE数据时出错:', err);
                            this.fieData = [emptyTotal];
                        }

                        // 处理文件列表数据
                        this.fieFileList = data.listOverseasReceiptFieFileList || [];
                        console.log('fieFileList:', this.fieFileList);

                        // 强制触发视图更新，确保所有数据都正确显示
                        this.$nextTick(() => {
                            // 设置标记，表示数据已加载完成
                            this.isDataLoaded = true;

                            this.$forceUpdate();
                            console.log('数据加载完成，强制更新视图');
                            console.log('当前isDataLoaded状态:', this.isDataLoaded);
                        });
                    } else {
                        console.error('API返回的data为空');
                    }
                } else {
                    console.error('API返回错误:', res);
                }
            }).catch(err => {
                console.error('获取详情数据失败:', err);
            });
      }
    },

    // 添加与水单编辑明细相同的calcTotal方法
    calcTotal(arr) {
      // 如果数组为空或长度小于2，不需要计算总计
      if (!arr || arr.length < 2) {
        return;
      }

      // 找出非总计行的数据进行计算
      let data = arr.filter(item => !item.isTotal);
      let total = {
        receiptAmount: 0,
        localAmount: 0,
        uploadAmount: 0,
        manualUploadAmount: 0,
        noStatementAmount: 0,
        statementAmount: 0,
        deduction: 0,
        isTotal: true // 标记为总计行
      };

      if (data.length) {
        data.forEach(item => {
          if (!item) return; // 跳过空项

          Object.keys(total).forEach(key => {
            if (key === 'isTotal') return; // 跳过isTotal属性

            try {
              if (key === 'localAmount') {
                // 对于localAmount，使用整数格式
                total[key] = safeAdd(total[key], Number(item[key] || 0)).toFixed(0);
              } else {
                // 对于其他金额，保留6位小数
                total[key] = safeAdd(total[key], Number(item[key] || 0)).toFixed(6);
              }
            } catch (err) {
              console.error(`计算${key}总计时出错:`, err);
              total[key] = 0;
            }
          });
        });
      }

      // 找到总计行的索引
      const totalIndex = arr.findIndex(item => item.isTotal === true);
      if (totalIndex !== -1) {
        // 如果已经有总计行，则替换它
        arr.splice(totalIndex, 1, total);
      } else {
        // 如果没有总计行，则添加到最后
        arr.push(total);
      }
    },

    calculateTotal(dataList) {
      const total = {
        receiptAmount: 0,
        localAmount: 0,
        uploadAmount: 0,
        manualUploadAmount: 0,
        noStatementAmount: 0,
        statementAmount: 0,
        deduction: 0,
      };

      if (dataList && dataList.length > 0) {
        dataList.forEach(item => {
          Object.keys(total).forEach(key => {
            try {
              if (key === 'localAmount') {
                total[key] = safeAdd(total[key], Number(item[key] || 0)).toFixed(0);
              } else {
                total[key] = safeAdd(total[key], Number(item[key] || 0)).toFixed(6);
              }
            } catch (err) {
              console.error(`计算${key}总计时出错:`, err);
              total[key] = 0;
            }
          });
        });
      }

      return total;
    },

    getEmptyTotal() {
      return {
        receiptAmount: 0,
        localAmount: 0,
        uploadAmount: 0,
        manualUploadAmount: 0,
        noStatementAmount: 0,
        statementAmount: 0,
        deduction: 0,
        isTotal: true // 标记为总计行
      };
    },

    // 检查文件是否已下载
    isFileDownloaded(item) {
      if (!item || !item.id) return false;
      return this.downloadedFiles.includes(item.id);
    },

    // 下载文件
    download(item) {
      if (!item || !item.filePath) {
        this.$message.warning('文件路径不存在');
        return;
      }

      this.$http.get('/listOverseasFileBase/download', {
        params: { absoluteFileName: item.filePath },
        responseType: "arraybuffer"
      }).then((res) => {
        if (res.data) {
          let blob = new Blob([res.data], {
            type: res.headers['content-type']
          });
          let objectUrl = URL.createObjectURL(blob);
          let a = document.createElement('a');
          a.href = objectUrl;
          a.download = item.name;
          a.click();

          // 标记文件已下载
          if (item.id && !this.downloadedFiles.includes(item.id)) {
            this.downloadedFiles.push(item.id);
            // 更新 localStorage
            localStorage.setItem('downloadedFiles', JSON.stringify(this.downloadedFiles));
          }
        }
      }).catch(err => {
        console.error('下载文件失败:', err);
        this.$message.error('下载文件失败');
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.detail-drawer {
  padding: 20px;
  background-color: #f9f9f9;
  position: relative;
  width: 1500px; /* 设置固定宽度，确保触发水平滚动 */
  box-sizing: border-box; /* 确保内边距不会增加元素宽度 */
  min-width: 1500px; /* 设置最小宽度，确保内容不会被压缩 */
  height: 600px; /* 设置固定高度，确保触发垂直滚动 */
  max-height: 600px; /* 设置最大高度，确保内容不会超出 */
  overflow-y: auto; /* 允许内容垂直滚动 */
  overflow-x: auto; /* 允许内容水平滚动 */
}

.drawer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #dcdfe6;

  h2 {
    margin: 0;
    font-size: 18px;
    color: #303133;
  }

  .close-btn {
    padding: 5px;
    font-size: 20px;

    &:hover {
      color: #409EFF;
    }
  }
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  margin: 20px 0 10px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #dcdfe6;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 11px;

  > div {
    display: flex;
    align-items: center;
    justify-content: flex-start;

    h3 {
      margin: 0;
      font-size: 16px;
    }

    p {
      margin: 0 0 0 20px;
      font-size: 14px;
      font-weight: normal;
    }
  }
}

.red {
  color: #f56c6c;
}

.form-section {
  margin-bottom: 20px;
  display: flex;
  flex-wrap: wrap; /* 允许表单项换行 */
  min-width: 960px; /* 设置最小宽度，触发水平滚动 */
}

.detail-table {
  width: 98%;
  margin: auto;
  margin-bottom: 20px;
  table-layout: fixed; /* 固定表格布局，防止内容过宽 */
}

/deep/ .el-form-item {
  margin-bottom: 10px;
}

/* 调整表单元素宽度 */
/deep/ .el-form-item__content {
  min-width: 200px; /* 设置最小宽度，确保内容不会被压缩 */
}

/* 调整输入框宽度 */
/deep/ .el-input {
  width: 100%; /* 输入框宽度适应容器 */
  min-width: 180px; /* 设置最小宽度 */
}

/* 调整表格宽度 */
/deep/ .el-table {
  width: 100% !important; /* 表格宽度适应容器 */
  min-width: 1400px !important; /* 设置最小宽度，确保触发水平滚动 */
  table-layout: auto !important; /* 自动表格布局，确保列名完全展示 */
}

/* 调整表格单元格内容 */
/deep/ .el-table .cell {
  white-space: nowrap; /* 防止文本换行，确保列名完全展示 */
  overflow: visible; /* 允许内容超出单元格 */
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 12px; /* 垂直滚动条宽度 */
  height: 12px; /* 水平滚动条高度 */
}

::-webkit-scrollbar-track {
  background: #f1f1f1; /* 滚动条轨道背景色 */
  border-radius: 6px; /* 轨道圆角 */
}

::-webkit-scrollbar-thumb {
  background: #888; /* 滚动条滑块颜色 */
  border-radius: 6px; /* 滑块圆角 */
  border: 2px solid #f1f1f1; /* 滑块边框 */
}

::-webkit-scrollbar-thumb:hover {
  background: #555; /* 鼠标悬停时滑块颜色 */
}

/* 确保滚动条始终显示 */
.detail-drawer::-webkit-scrollbar {
  display: block; /* 始终显示滚动条 */
}

/deep/ .el-table th {
  background-color: #f5f7fa;
}

.total-row {
  font-weight: bold;
  color: #606266;
  text-align: center;
}

/deep/ .el-table__row:last-child {
  background-color: #f8f8f8;
}

/deep/ .input-text-right {
  text-align: right;
}

.file-list-section {
  margin: 20px 0;
  width: 98%;
  margin: auto;
}

.empty-data-tip {
  text-align: center;
  padding: 20px;
  color: #909399;
  background-color: #f5f7fa;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 20px;
}

.file-list {
  padding: 10px;
  background-color: #fff;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.download-link {
  margin: 10px 0;
  cursor: pointer;
  color: #409EFF;
  display: flex;
  align-items: center;

  &:hover {
    text-decoration: underline;
  }

  i {
    margin-right: 5px;
  }

  &.downloaded {
    color: #67C23A; // 使用Element UI的成功色
  }

  .downloaded-icon {
    color: #67C23A;
  }

  .downloaded-text {
    margin-left: 5px;
    font-size: 12px;
    color: #67C23A;
  }
}
</style>
