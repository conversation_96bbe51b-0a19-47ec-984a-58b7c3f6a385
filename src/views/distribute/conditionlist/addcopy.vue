<template>
    <div>
        <div style="width: 80%">
            <el-form label-position="right" label-width="180px" ref="formData" :rules="rules" :model="formData" class="demo-form-inline">
                <el-form-item label="dist no" prop="distNo" class="is-required">
                    <el-input v-model="formData.distNo" placeholder="請輸入"></el-input>
                </el-form-item>
                <el-form-item label="from dist no" prop="fromDistNo" class="is-required">
                    <el-input v-model="formData.fromDistNo" placeholder="請輸入"  @blur="getListSource()"></el-input>
                </el-form-item>
                <el-form-item label="list source">
                    <el-input v-model="formData.fromListSourceCode" :readonly="true" @dblclick.native="getListSource()" placeholder="請輸入from dist no查詢"></el-input>
                </el-form-item>
                <el-form-item label="list source desc">
                    <el-input v-model="formData.fromListSourceDescription" :readonly="true" @dblclick.native="getListSource()" placeholder="請輸入from dist no查詢"></el-input>
                </el-form-item>
                <el-form-item label="list category">
                    <el-input v-model="formData.fromListCategoryCode" :readonly="true" @dblclick.native="getListSource()" placeholder="選擇list source後自動獲取"></el-input>
                </el-form-item>
                <el-form-item label="list category desc">
                    <el-input v-model="formData.categoryDescription" :readonly="true" @dblclick.native="getListSource()" placeholder="選擇list source後自動獲取"></el-input>
                </el-form-item>
                <el-form-item label="to list source"  prop="tolistsource" class="is-required">
                    <!-- <el-autocomplete
                        v-model="formData.toListSourceCode"
                        :fetch-suggestions="getToList"
                        placeholder="請輸入内容"
                        @select="handleSelect"
                        :trigger-on-focus="false"
                    >
                        <template slot-scope="scope">
                            <p style="margin:5px 0;">{{scope.item.sourceName}}</p>
                        </template>
                    </el-autocomplete> -->
                    <el-select 
                        v-model="formData.toListSourceCode" 
                        placeholder="請輸入内容" 
                        filterable 
                        remote 
                        :remote-method="getToList" 
                        @change="handleSelect" 
                        >
                      <el-option v-for="item in SourceOptions" :key="item.sourceDesc" :value="item.sourceName">
                            <p style="margin:5px 0;">{{item.sourceName}}</p>
                      </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="to list categoryCode">
                    <el-input v-model="formData.toListCategoryCode" placeholder="請輸入to list source獲取"></el-input>
                </el-form-item>
                <el-form-item label="perfDate" prop="perfDate" class="is-required">
                    <!-- <el-input type="text" v-model="formData.startPerfDate" style="width:200px" placeholder="start" v-dateFmt></el-input> -->
                    <date-picker v-model="formData.startPerfDate" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd" style="width:200px" placeholder="start"></date-picker>
                    -
                    <date-picker v-model="formData.endPerfDate" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd" style="width:200px" placeholder="end"></date-picker>
                    <!-- <el-input type="text" v-model="formData.endPerfDate" style="width:200px" placeholder="end" v-dateFmt></el-input> -->
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="save('formData')">確定</el-button>
                </el-form-item>
            </el-form>
            <el-dialog title="toCategoryList" :visible.sync="showToCategoryListDialog" width="50%" :close-on-click-modal="false">
                <el-table
                    :data="toCategoryList"
                    stripe
                    style="width: 100%">
                    <el-table-column
                        prop="sourceName"
                        label="to list source"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="categoryCode"
                        label="to list categoryCode"
                    >
                    </el-table-column>
                    <el-table-column
                        label="operation"
                        align="center"
                    >
                        <template slot-scope="scope">
                            <i class="el-icon-check" @click="chooseToListCategory(scope.row)" style="cursor:pointer;"></i>
                        </template>
                    </el-table-column>
                </el-table>
            </el-dialog>
            <!--list source列表-->
            <el-dialog :title="step==0?'選擇source参數':'選擇category参數'" :visible.sync="showListSoc" width="50%" :close-on-click-modal="false">
                <div v-if="step === 0">
<!--                    <h3>選擇source参數</h3>-->
                    <el-table
                        :data="sourceList"
                        stripe
                        style="width: 100%">
                        <el-table-column
                            prop="sourceName"
                            label="list source"
                        >
                        </el-table-column>
                        <el-table-column
                            prop="sourceDesc"
                            label="list source desc"
                        >
                        </el-table-column>
                        <el-table-column
                            label="operation"
                            align="center"
                        >
                            <template slot-scope="scope">
                                <i class="el-icon-check" @click="chooseListSoc(scope.row)" style="cursor:pointer;"></i>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <div v-if="step === 1">
<!--                    <h3>選擇category参數</h3>-->
                    <el-table
                        :data="categoryList"
                        stripe
                        style="width: 100%">
                        <el-table-column
                            prop="categoryCode"
                            label="list category"
                        >
                        </el-table-column>
                        <el-table-column
                            prop="categoryDesc"
                            label="list category des"
                        >
                        </el-table-column>
                        <el-table-column
                            label="operation"
                            align="center"
                        >
                            <template slot-scope="scope">
                                <i class="el-icon-check" @click="chooseListCategory(scope.row)" style="cursor:pointer;"></i>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </el-dialog>
        </div>
    </div>
</template>

<script>
    export default {
        name: 'addcopy',
        data(){
            return {
                formData:{
                    distNo:'',
                    fromDistNo:'',
                    fromListSourceCode:'',
                    fromListSourceDescription:'',
                    fromListCategoryCode:'',
                    categoryDescription:''
                },
                showListSoc:false,
                sourceList:[],
                categoryList:[],
                toCategoryList:[],
                showToCategoryListDialog:false,
                step:0,
                  rules: {
                    distNo: [{ required: true, message: '請輸入distNo', trigger: 'blur' }],
                    fromDistNo: [{ required: true, message: '請輸入from dist no', trigger: 'blur' }],
                    // tolistsource: [{ required: true, message: '請輸入to list source', trigger: 'blur' }],
                    // perfDate: [{ required: true, message: '請輸入perfDate', trigger: 'blur' }],
                },
                isEdit:false,
                SourceOptions:[]
            }
        },
        mounted(){
            this.isEdit = this.$route.name == 'distribute-editcopy'
            if(this.isEdit){
                this.formData = JSON.parse(this.$route.query.row)
            }
        },
        methods:{
            getListSource(){
                this.step = 0
                this.$http.get('/distributionSpecialCondition/listSourceListByDistNo',{params:{distNo:this.formData.fromDistNo}}).then(res => {
                    console.log(res)
                    if(res.success){
                        if(res.data.code === 200){
                            let list = res.data.data
                            if(list.length){
                                this.sourceList = list
                                this.showListSoc = true
                            }else{
                                this.$message({
                                    message: 'from dist no無效，請重新輸入',
                                    type: 'warning'
                                })
                                this.$set(this.formData,'fromDistNo','')
                            }
                        }else{
                            let message=res.data.message=='ListSource不存在'?'from dist no不存在':res.data.message
                            this.$message({
                                message:message ,
                                type: 'warning'
                            })
                            this.$set(this.formData,'fromDistNo','')
                        }
                    }
                })
            },
            getListCategory(id){
                this.$http.get('/distributionSpecialCondition/listCategoryList',{params:{listSourceId:id,categoryCode:''}}).then(res => {
                    console.log(res)
                    if(res.success){
                        if(res.data.code === 200){
                            let list = res.data.data
                            if(list.length){
                                this.step = 1
                                this.categoryList = list
                            }else{
                                this.showListSoc = false
                            }
                        }else{
                            this.$message({
                                message: res.data.message,
                                type: 'warning'
                            })
                        }
                    }
                })
            },
            getToList(query,cb){
                if(query){
                    this.$http.get('/distributionSpecialCondition/listSourceList',{params:{sourceName:query}}).then(res => {
                        if(res.success){
                            if (res.data.code == 200) {
                                this.SourceOptions = res.data.data
                                cb(res.data.data)
                            } else {
                                this.SourceOptions = []
                            }
                        }

                    })
                }

            },
            handleSelect(val){
                console.log(val)
                this.formData.toListSourceCode = val.sourceName
                let params = {
                    listSourceId: ''
                }
                this.SourceOptions.forEach(item=>{
                    if(item.sourceName == val){
                        this.formData.toListSourceCode = item.sourceName
                        params.listSourceId=item.id
                        this.$set(this.formData,'toListCategoryCode','')
                        // this.$set(this.formData,'categoryDescription','')
                        // this.CategoryOptions=[]
                        
                    }

                })
                //獲取to list category
                
                this.$http.get('/distributionSpecialCondition/listCategoryList',{params}).then(res => {
                    if(res.success){
                        console.log(res)
                        if(res.data.code === 200){
                            this.toCategoryList = res.data.data
                            this.showToCategoryListDialog = true
                        }else{
                            this.$message({
                                message:'未獲取到關聯的to list categoryCode,請重新選擇',
                                type:'warning'
                            })
                        }
                    }

                })
            },

            chooseToListCategory(row){
                this.formData.toListCategoryCode = row.categoryCode
                this.showToCategoryListDialog = false
            },
            chooseListSoc(row){
                this.formData.fromListSourceCode = row.sourceName
                this.formData.fromListSourceDescription = row.sourceDesc
                this.getListCategory(row.id)

            },
            chooseListCategory(row){
                this.formData.fromListCategoryCode = row.categoryCode
                this.formData.categoryDescription = row.categoryDesc
                this.showListSoc = false
            },
            save(formName){
                console.log(this.formData)
                this.$refs[formName].validate((valid) => {
                   if (valid) {
                       if(!this.formData.startPerfDate || !this.formData.endPerfDate){
                            this.$toast({tips: 'perfDate不能為空'})
                            return
                       }
                       this.$http.post('/distributionSpecialCondition/saveDistListCopyFilter',this.formData).then(res => {
                           console.log(res)
                           if(res.success){
                               if(res.data.code === 200){
                                   this.$message({
                                       message: this.isEdit?'修改成功':'新增成功',
                                       type:'success',
                                       duration:2000,
                                       onClose:() => {
                                           this.$bus.$emit('closeCurrentTab', () => { this.$router.push({name: 'distribute-condition'})});
                                       }
                                   })
                               }else{
                                   this.$message({
                                       message:res.data.message,
                                       type:'warning'
                                   })
                               }
                           }
                       })
                    //  alert('submit!');
                   } else {
                    //  console.log('error submit!!');
                     return ;
                   }
                 });
            }
        },
        beforeRouteEnter (to, from, next) {
            next(vm => {
                if(to.params.distNo){
                    vm.formData.distNo = to.params.distNo
                }

            })
        }
    }
</script>

<style scoped>
    /deep/ .el-form-item__label {
        margin-left: 0 !important;
    }
    /deep/ .el-input.is-disabled .el-input__inner{
        color: #000;
    }
</style>
