<template>
    <!-- 可支付列表 -->
    <div>
        <!-- <div style="margin-top: 20px"> -->
        <div style="">
            <el-tabs v-model="activeName">
                <!--個人會員Tabs-->
                <el-tab-pane label="member" name="first" v-if="showMember">
                    <el-form :inline="true" :model="memberSearch" class="demo-form-inline" @keyup.enter.native="queryMemberList">
                        <el-form-item label="分配編號:">
                            <el-input v-model="memberSearch.distNo" placeholder style="width: 80px;"></el-input>
                        </el-form-item>
                        <el-form-item label="ip base no">
                            <el-input v-model="memberSearch.ipBaseNo" placeholder style="width: 80px;"></el-input>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="queryMemberList">搜 索</el-button>
                        </el-form-item>
                        <el-form-item>
                            <span class="clear-search" @click="clearMemberSearch()">清除搜索</span>
                        </el-form-item>
                    </el-form>
                    <el-table
                        :data="memberData"
                        border
                        style="width: 100%"
                        :empty-text="emptyText">
                        <el-table-column
                            prop="ipBaseNo"
                            label="ip base no">
                        </el-table-column>
                        <el-table-column
                            prop="paName"
                            label="pa name">
                        </el-table-column>
                        <el-table-column
                            prop="payDate"
                            label="支付日期"
                            width="120px"
                        >
                            <template slot-scope="scope">
                                {{scope.row.payDate.split(' ')[0]}}
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="distNo"
                            label="DistNo">
                        </el-table-column>
                        <el-table-column
                            prop="localTaxableIncome"
                            label="Local Roy">
                        </el-table-column>
                        <el-table-column
                            prop="overseasTaxableIncome"
                            label="Overseas Roy">
                        </el-table-column>
                        <el-table-column
                            prop="payAmount"
                            label="Rayable Amount">
                        </el-table-column>
                        <el-table-column
                            prop="salesTaxAmount"
                            label="Sales Tax">
                        </el-table-column>
                        <el-table-column
                            prop="withheldTax"
                            label="Withheld Tax">
                        </el-table-column>
                        <el-table-column
                            prop="commissionAmount"
                            label="Administration">
                        </el-table-column>
                        <el-table-column
                            prop="nonTaxableIncome"
                            label="Deduction">
                        </el-table-column>
                    </el-table>
                    <el-pagination
                        background
                        layout="prev, pager, next"
                        :total=memberTotal @current-change="handleMemberChange">
                    </el-pagination>
                </el-tab-pane>

                <!--團體會員Tabs-->
                <el-tab-pane label="society" name="second" v-if="showSociety">
                    <el-form :inline="true" :model="socSearch" class="demo-form-inline" @keyup.enter.native="querySocList">
                        <el-form-item label="分配編號:">
                            <el-input v-model="memberSearch.distNo" placeholder style="width: 80px;"></el-input>
                        </el-form-item>
                        <el-form-item label="society code">
                            <el-input v-model="memberSearch.societyCode" placeholder style="width: 80px;"></el-input>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="querySocList">搜 索</el-button>
                        </el-form-item>
                        <el-form-item>
                            <span class="clear-search" @click="clearSocSearch()">清除搜索</span>
                        </el-form-item>
                    </el-form>
                    <el-table
                        :data="socData"
                        border
                        style="width: 100%"
                        :empty-text="emptyText1">
                        <el-table-column
                            prop="distNo"
                            label="DistNo">
                        </el-table-column>
                        <el-table-column
                            prop="societyCode"
                            label="Society Code">
                        </el-table-column>
                        <el-table-column
                            prop="societyName"
                            label="Society Name">
                        </el-table-column>
                        <el-table-column
                            prop="payAmount"
                            label="支付金額">
                        </el-table-column>
                        <el-table-column
                            prop="payDate"
                            label="支付日期"
                            width="120px"
                        >
                            <template slot-scope="scope">
                                {{scope.row.payDate.split(' ')[0]}}
                            </template>
                        </el-table-column>
                    </el-table>
                    <el-pagination
                        background
                        layout="prev, pager, next"
                        :total=socTotal @current-change="handleSocChange">
                    </el-pagination>
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</template>

<script>
    export default {
        name: 'payDetailInfo',
        data () {
            return {
                autopayNo:'',
                showMember:false,
                showSociety:false,
                memberSearch:{},
                socSearch:{},
                memberTotal:0,
                socTotal:0,
                memberData:[],
                socData:[],
                activeName:'first',
                emptyText: '數據加載中',
                emptyText1: '數據加載中',
            }
        },
        methods: {
            queryMemberList () {
                let params = this.memberSearch
                params.autopayNo = this.autopayNo
                this.emptyText = '數據加載中';
                this.$http.get('/dist/autopay/lisDistAutopayNetPayMemberForPay',{params}).then(res => {
                    if(res.success && res.data.code == 200){
                        this.memberData = res.data.data.list
                        this.memberTotal = res.data.data.total
                        if(! this.memberData || this.memberData.length == 0){
                            this.emptyText = '暫無數據';
                        }
                    }
                })
            },
            querySocList() {
                let params = this.socSearch
                params.autopayNo = this.autopayNo
                this.emptyText1 = '數據加載中';
                this.$http.get('/dist/autopay/listDistAutopayNetPaySocietyForPay',{params}).then(res => {
                    if(res.success && res.data.code == 200){
                        this.socData = res.data.data.list
                        this.socTotal = res.data.data.total
                        if(! this.socData || this.socData.length == 0){
                            this.emptyText1 = '暫無數據';
                        }
                    }
                })
            },
            clearMemberSearch () {
                this.memberSearch = {}
                this.queryMemberList()
            },
            handleMemberChange (val) {
                this.memberSearch.page_num = val
                this.queryMemberList()
            },
            clearSocSearch () {
                this.socSearch = {}
                this.querySocList()
            },
            handleSocChange (val) {
                this.socSearch.page_num = val
                this.querySocList()
            },
        },

        mounted () {
            let payType = this.$route.query.payType
            this.showMember = payType.indexOf('MBR') != -1 ? true : false
            this.showSociety = payType.indexOf('SOC') != -1 ? true : false
            this.autopayNo = this.$route.query.autopayNo
            this.showMember && this.queryMemberList()
            this.showSociety && this.querySocList()
        }
    }
</script>

<style>
    .mydetail {
        width: 100% !important;
        display: block !important;
    }

    .mydateform .el-input--suffix {
        width: 200px !important;
    }

    .mydetail .el-form-item__content {
        width: calc(100% - 150px) !important;
    }
</style>
