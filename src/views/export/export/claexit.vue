<template>
  <!-- 拆分後的文件 用于審核用的 -->
  <div class="ipibpox">
    <el-form :inline="true" :model="search" ref="form" :rules="rules" class="demo-form-inline">
      <el-form-item prop="fileBaseId">
        <span class="red">*</span>
        <el-input v-model.trim="search.fileBaseId" placeholder="FID" style="width: 80px;"></el-input>
      </el-form-item>

      <el-form-item prop="Work Title">
        <el-input v-model.trim="search.title" placeholder="title" style="width: 120px;"></el-input>
      </el-form-item>
      <el-form-item prop="titleNonNull" label="非空Title">
        <el-checkbox v-model="search.titleNonNull"></el-checkbox>
      </el-form-item>
      <el-form-item label="quantile" prop="quantileBegin">
        <el-input style="width: 75px" v-model.trim="search.quantileBegin" placeholder="Begin"></el-input>
      </el-form-item>
      <span style="line-height: 2.15;">~</span>
      <el-form-item prop="quantileEnd">
        <el-input style="width: 75px" v-model.trim="search.quantileEnd" placeholder="End"></el-input>
      </el-form-item>

      <el-form-item>
        <el-select v-model="search.matchScoreLevel" placeholder="分數選擇" style="width: 116px;">
          <el-option v-for=" (item,index) in config.score" :key="index" :value="item.value" :label="item.label"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="search.status" placeholder="所有狀態" style="width: 116px;">
          <el-option v-for=" (item,index) in config.status" :key="index" :value="item.value" :label="item.label"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="clickNumber">
        <el-input v-model.trim="search.clickNumber" placeholder="點擊次數" style="width: 120px;"></el-input>
      </el-form-item>

      <!-- <el-form-item label="批次號:" prop="batchIdA">
        
        <el-input style="width: 75px" v-model.number="search.batchIdA" placeholder="Begin"></el-input>
      </el-form-item>
      <span style="line-height: 2.15;">~</span>
      <el-form-item prop="batchIdB">
        <el-input style="width: 75px" v-model.number="search.batchIdB" placeholder="End"></el-input>
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" @click="searchFn()">搜索</el-button>
        <!-- <el-button type="success" @click="batchAudit(1)">批量通過</el-button> -->
        <!-- <el-button type="danger" @click="batchAudit(2)">批量拒絕</el-button> -->
      </el-form-item>
      <!-- <el-form-item>
        <el-upload class="upload-demo" ref="upload" action="" accept=".xls,.xlsx" :auto-upload="false" :on-change="uploadChange" :show-file-list="false">
          <el-button size="small" type="primary" style="background-color: #b240ff;border-color: #b240ff;">審核結果導入</el-button>
        </el-upload>
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" @click="exportFn">導出</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="autoprod">自動生成custom_id</el-button>
      </el-form-item>
      <el-form-item>
        <el-upload class="upload-demo" ref="upload" action="" accept=".xls,.xlsx" :auto-upload="false" :on-change="uploadChange" :show-file-list="false">
          <el-button size="small" type="primary" style="background-color: #b240ff;border-color: #b240ff;">上傳生成custom_id</el-button>
        </el-upload>
      </el-form-item>
      <el-form-item>
        <span class="clear-search" @click="clearSearch()">清除搜索</span>
      </el-form-item>
    </el-form>
    <el-table :empty-text="tableresult"   stripe :data="tableData" border style="width: 100%" @selection-change="handleSelectionChange">
      <!-- <el-table-column type="selection" width="55" :selectable="checkSelectable">

      </el-table-column> -->
      <el-table-column prop="fileBaseId" label="FID" width="60px">
      </el-table-column>
      <el-table-column prop="title" label="Title">
      </el-table-column>
      <el-table-column prop="workArtist" label="Aritists">
        <template slot-scope="scope">
          <span :title="scope.row.workArtist">{{scope.row.workArtist}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="compWriters" label="Author">
        <template slot-scope="scope">
          <span :title="scope.row.compWriters">{{scope.row.compWriters | addSpace}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="composer" label="Composer">
        <template slot-scope="scope">
          <span :title="scope.row.composer">{{scope.row.composer | addSpace}}</span>
        </template>
      </el-table-column>
      <el-table-column label="Duration">
        <template slot-scope="scope">
          <span>{{scope.row.durationM?scope.row.durationM:'00'}}:{{scope.row.durationSStr?scope.row.durationSStr:'00'}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="matchTitle" label="Match Work Titile">
      </el-table-column>
      <el-table-column label="Soc-WorkId">
        <template slot-scope="scope">
          {{(scope.row.matchWorkSocietyCode ? scope.row.matchWorkSocietyCode : '') + (scope.row.matchWorkId ?
                    '-'+scope.row.matchWorkId : '')}}
        </template>
      </el-table-column>
      <el-table-column prop="clickNumber" label="ClickNum">
      </el-table-column>
      <el-table-column prop="compCustomId" label="CompCustomId">
      </el-table-column>
      <el-table-column prop="quantile" label="Quantile">
      </el-table-column>
      <el-table-column prop="matchType" label="Type">
        <template slot-scope="scope">
          <!-- <span :title="scope.row.status">{{scope.row.author | addSpace}}</span> -->
          <span v-if="scope.row.matchType==0">視頻</span>
          <span v-if="scope.row.matchType==1">文字</span>
        </template>
      </el-table-column>
      <el-table-column prop="matchScore" label="matchScore">
      </el-table-column>

      <el-table-column prop="status" label="status">
        <template slot-scope="scope">
          <!-- <span :title="scope.row.status">{{scope.row.author | addSpace}}</span> -->
          <span v-if="scope.row.status==0">待審核</span>
          <span v-if="scope.row.status==1">已匹配</span>
          <span v-if="scope.row.status==2">不匹配</span>
          <span v-if="scope.row.status==3">指定作品</span>
          <span v-if="scope.row.status==3">過濾</span>
          <span v-if="scope.row.status==4" style=" display: block;width: inherit;overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">CompCustomId過濾</span>
        </template>
      </el-table-column>
      <!-- <el-table-column fixed="right" label="OP" width="60px">
        <template slot-scope="scope">
          <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
            <span class="a-blue" @click="audit(scope.row,scope.$index)">審核</span>
          </span>
        </template>
      </el-table-column> -->
    </el-table>
    <div>

      <el-pagination background layout="prev, pager, next" :total="total" @current-change="searchFn" :current-page="currentPage">
      </el-pagination>
    </div>

  </div>
</template>

<script>
import qs from 'qs'
// import axios from '../../utils/httpRequest';    
export default {
  name: 'exportClaexit',
  data() {
    return {
      tableData: [],tableresult:' ',
      total: 0,
      clickindex: 0,
      currentPage: 1,
      rules: {
        fileBaseId: [
          { required: true, message: '請輸入FID', trigger: 'blur' }
        ],
        batchIdA: [{ pattern: /^\+?[1-9]\d*$/, message: '必須>0' }],
        batchIdB: [{ pattern: /^\+?[1-9]\d*$/, message: '必須>0' }]
      },
      search: {
        status: '',
        title: '',
        matchScoreLevel: '',
        batchIdA: '',
        batchIdB: '',
        fileBaseId: '',
        quantileBegin: '',
        quantileEnd: '',
        titleNonNull: false,
        clickNumber: ''
      },
      config: {
        status: [
          {
            value: '',
            label: '全部狀態'
          },
          {
            value: 0,
            label: '待審核'
          },
          {
            value: 1,
            label: '已匹配'
          },
          {
            value: 2,
            label: '不匹配'
          },
          {
            value: 3,
            label: '過濾'
          },
          {
            value: 4,
            label: 'CompCustomId過濾'
          },

        ],
        score: [
          {
            value: '',
            label: '全部評分'
          },
          {
            value: -1,
            label: '10分以下'
          },
          {
            value: 0,
            label: '10-20分'
          },
          {
            value: 1,
            label: '20分以上'
          },
        ],
        uploadType: ['PG', 'FW', 'MS', 'CJ']
      },
      selectList: []
    }
  },
  methods: {
    clearSearch() {
      this.clickindex = 0
      this.search = {
        status: '',
        title: '',
        matchScoreLevel: '',
        fileBaseId: '',
        batchIdA: '',
        batchIdB: '',
        quantileBegin: '',
        quantileEnd: '',
        titleNonNull: false,
        clickNumber: ''
      }
    },
    searchFn(page = 1) {

      this.$refs.form.validate(validate => {
        if (validate) {
          if (page == 1) {
            this.total = 0
          }
          let ajaxData = {};
          ajaxData = this.$utils.copy(this.search);
          ajaxData.page = {
            pageNum: page ? page : 1,
            pageSize: 10
          }
                this.tableresult = '數據加載中...'
          this.$http.post('/claimmatch/getListMatchDataDspList', ajaxData).then(res => {
            if (res.success) {
              this.tableData = res.data.data.list;
              this.total = res.data.data.total;
              this.clickindex = 0
              this.currentPage = page ? page : 1;
            }
                this.tableresult = this.tableData.length == 0 ? '暫無數據' : ' '
          })
        }
      })

    },
    audit(item, params) {
      this.$router.push({ name: 'claimListAudit', query: { id: item.id, type: item.matchType, useparams: params, usetabledata: this.tableData } })
    },
    changeSearchScore(score) {
      this.search.matchScoreLevel = score;
    },
    checkSelectable(row) {
      return !row.status
    },
    handleSelectionChange(list) {
      let array = [];
      list.forEach((item, index) => {
        array.push(item.dataUniqueKey)
      })
      this.selectList = array;
    },
    batchAudit(type) {
      if (!this.selectList.length) {
        this.$toast({ tips: '請至少選擇一項' })
        return;
      }
      this.$http.post('/claimmatch/batchUpdateListMatchDataDspStatusByParams', {
        uniqueKeyMd5List: this.selectList,
        status: type
      }).then(res => {
        if (res.success) {
          if (res.data.code == 200) {
            this.$toast({ tips: '批量審核' + (type == 1 ? '通過' : '拒絕') + '成功' });
            this.searchFn();
          } else {
            this.$toast({ tips: res.data.message })
          }

        }
      })

    },
    uploadChange(file) {
      this.$utils.uploadFile({flie:file.raw}, '/claimmatch/importAutoGenerateCustomId', this)
    },
    autoprod() {
      this.$refs.form.validate(validate => {
        if (validate) {
          this.$msgbox.confirm(`確定生成custom_id操作?`, '提示', {
            confirmButtonText: '確定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            let ajaxData = {};
            ajaxData = this.$utils.copy(this.search);
            ajaxData.page = {
              pageNum: this.currentPage,
              pageSize: 10
            }
            // if (ajaxData.matchScore == 99) {  
            //   delete ajaxData.matchScore;
            // }

            this.$http.post('/claimmatch/autoGenerateCustomId', ajaxData).then(res => {
              console.log('resresrsa', res)
              var Message = res.data.message || res.data.data || '操作失敗!'
              this.clickindex += 1
              // let Message = res.data.message || res.data.data || '操作失敗!'
              if (res.data.code!=200) {
              this.$toast({ tips: Message, clickindex:3 })
              }else{

                this.$toast({ tips: Message})
              }

              // if (res.data.code=='400') {
              //    this.$toast({tips:res.data.message})
              // }else{
              //   this.$toast({tips:'操作失敗!'})
              // }
            })
          })
        }
      })
    },
    exportFn() {
      this.$refs.form.validate(validate => {
        // console.warn('valisdate',validate)
        if (validate) {
          this.$msgbox.confirm(`是否確定導出?`, '提示', {
            confirmButtonText: '確定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            let ajaxData = {};
            ajaxData = this.$utils.copy(this.search);
            if (ajaxData.matchScore == 99) {
              delete ajaxData.matchScore;
            }
            //  claimmatch/importAutoGenerateCustomId
            this.$http.post('/claimmatch/exportCount', ajaxData).then(res => {
              console.log('rescount', res.data.data);
              let exporturl = res.data.data < 10000 ? '/claimmatch/export' : 'claimmatch/exportAsync'
              if (exporturl == '/claimmatch/export') {
                this.$http.post('/claimmatch/export', ajaxData, { responseType: 'blob' }).then(res => {
                  console.log(res)
                  let data = res.data
                  this.$utils.downloadByBlob(data, res.headers["content-disposition"])
                })
              } else {
                this.$http.post(exporturl, ajaxData).then(res => {
                  console.log('exportres222', res)
                  if (res.success && res.data.code == '200') {
                    this.$alert(res.data.data, '提示');
                  } else {
                    this.$toast({ tips: res.data.message })
                  }
                })
              }
            })
          })
        }
      })
    }
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      // console.log('*****out',vm)
      // vm.formData.distNo = to.params.distNo
      vm.searchFn()
    })
  }
}
</script>

<style  scoped>
.el-form-item {
  margin-left: 0;
  margin-right: 0;
}
.el-button {
  margin-left: 0;
  margin-right: 0;
}
</style>
