<template>
<div v-loading="loading">
    <h2>Base Info</h2>
    <ul class="clear">
        <li>
            <span class="label">Type:</span>
            <span class="value">{{formContract.agrType | AgrType}}</span>
        </li>
        <li>
            <span class="label">ID:</span>
            <span class="value">{{formContract.agrTypeId}}</span>
        </li>
        <li>
            <span class="label">Doc No:</span>
            <span class="value">{{formContract.docNo}}</span>
        </li>
        <li>
            <span class="label">Assignor:</span>
            <span class="value">{{formContract.oipNameNo}}&nbsp;&nbsp;&nbsp;{{formContract.oipName}}</span>
        </li>
        <li>
            <span class="label">RoleCode:</span>
            <span class="value">{{formContract.oipRole}}</span>
        </li>
        <li>
            <span class="label">PER:</span>
            <span class="value">{{formContract.oipPshareSoc || '099'}}&nbsp;&nbsp;{{formContract.oipPshare}}</span>
        </li>
        <li>
            <span class="label">MR:</span>
            <span class="value">{{formContract.oipMshareSoc || '099'}}&nbsp;&nbsp;{{formContract.oipMshare}}</span>
        </li>
        <li>
            <span class="label">SY:</span>
            <span class="value">{{formContract.oipSshareSoc || '099'}}&nbsp;&nbsp;{{formContract.oipSshare}}</span>
        </li>
        <li>
            <span class="label">OD:</span>
            <span class="value">{{formContract.oipOdshareSoc || '099'}}&nbsp;&nbsp;{{formContract.oipOdshare}}</span>
        </li>
        <li>
            <span class="label">DB:</span>
            <span class="value">{{formContract.oipDbshareSoc || '099'}}&nbsp;&nbsp;{{formContract.oipDbshare}}</span>
        </li>
        <li>
            <span class="label">Agr Date:</span>
            <span class="value">{{$utils.DATE(formContract.agrDate, 'yyyy-MM-dd')}}</span>
        </li>
        <li>
            <span class="label">Start Date:</span>
            <span class="value">{{$utils.DATE(formContract.agrSdate, 'yyyy-MM-dd')}}</span>
        </li>
        <li>
            <span class="label">End Date:</span>
            <span class="value">{{$utils.DATE(formContract.agrEdate, 'yyyy-MM-dd')}}</span>
        </li>
        <li style="clear:both;"></li>
        <li>
            <el-checkbox v-model="checked1">Auto Extension</el-checkbox>
        </li>
        <li>
            <el-checkbox v-model="checked2">Expired</el-checkbox>
        </li>
        <li>
            <el-checkbox v-model="checked3">Agreement Recevied</el-checkbox>
        </li>
        <li>
            <el-checkbox v-model="checked4">Futher assign</el-checkbox>
        </li>
        <li>
            <el-checkbox v-model="checked5">SD</el-checkbox>
        </li>
        <li>
            <el-checkbox v-model="checked6">Pre term</el-checkbox>
        </li>
    </ul>
    <h2>Assignee</h2>
    <ul class="clear assignee" v-for="(item, index) in assigneeList" :key="index">
        <li>
            <span class="label">Ip Name No:</span>
            <span class="value">{{item.sipNameNo}}</span>
        </li>
        <li>
            <span class="label">Name:</span>
            <!-- <span class="value">{{item.sipChinName ? item.sipChinName : item.sipName}}</span> -->
            <span class="value">{{item.sipName + (item.sipChinName ? ('('+item.sipChinName+')') : '')}}</span>
        </li>
        <li>
            <span class="label">RoleCode:</span>
            <span class="value">{{item.sipRole}}</span>
        </li>
        <li>
            <span class="label">PER:</span>
            <span class="value">{{item.sipPshareSoc || '099'}}</span><span class="value">{{item.sipPshare}}</span>
        </li>
        <li>
            <span class="label">MR:</span>
            <span class="value">{{item.sipMshareSoc || '099'}}</span><span class="value">{{item.sipMshare}}</span>
        </li>
        <li>
            <span class="label">SY:</span>
            <span class="value">{{item.sipSshareSoc || '099'}}</span><span class="value">{{item.sipSshare}}</span>
        </li>
        <li>
            <span class="label">OD:</span>
            <span class="value">{{item.sipOdshareSoc || '099'}}</span><span class="value">{{item.sipOdshare}}</span>
        </li>
        <li>
            <span class="label">DB:</span>
            <span class="value">{{item.sipDbshareSoc || '099'}}</span><span class="value">{{item.sipDbshare}}</span>
        </li>
    </ul>
    <h2>Territory</h2>
    <ul class="clear territory" v-for="(item, index) in territoryList" :key="index">
        <li>
            <span class="value">{{item.indicator}}{{item.languageCode}}</span>
            <span class="value">{{item.generDetail}}</span>
        </li>
    </ul>
</div>
</template>
<script>
    import qs from 'qs'

export default {
    props: {
        contactNo: {
            type: String,
            default: ''
        }
    },
    data(){
        return {
            loading: false,
            formContract: {},
            checked1: false,
            checked2: false,
            checked3: false,
            checked4: false,
            checked5: false,
            checked6: false,
            checked7: false,
            checked8: false,
            checked9: false,
            territoryobj: {},
            assigneeList: [],
            id: '',
            territoryList: []
        }
    },
    filters: {
        AgrType: function(type){
            let config = {
                OS: "Writer General",
                OG: "Writer Specific",
                PG: "Publisher General",
                PS: "Publisher Specific"
            }
            return config[type]
        }
    },
    methods: {
        init(contractNo){
            this.queryInfo(contractNo);
        },
        queryInfo(contractNo){
            this.loading = true;
            this.$http.get('/agreement/getAgreementByAgrNo', {params: {agrNo: contractNo}}).then(res => {
                this.loading = false;
                this.tableData1 = [];
                if (res.success) {
                    this.territoryobj = res.data.refTerritoryMap;
                    this.id = res.data.agreement.id;
                    this.formContract = Object.assign(res.data.agreement, res.data.assignor);
                    this.formContract.oipName = this.formContract.oipName + (this.formContract.oipChinName ? ('('+this.formContract.oipChinName+')') : '');
                    this.queryRight(this.formContract.oipNameNo, 'info');

                    this.assigneeList = res.data.assigneeList;
                    this.assigneeList.forEach( (item, index) => {
                        this.queryRight(item.sipNameNo, 'assignee', index);
                    })
                    this.territoryList = [];
                    res.data.territory.forEach(item => {
                        let obj = {}
                        let tis = item.tisA;
                        obj.languageCode = item.tisA;
                        obj.generDetail = this.territoryobj[tis].description;
                        this.territoryList.push(Object.assign(obj, item));
                    })
                    if (res.data.agreement.autoExtensionInd === 'Y') {
                        this.checked1 = true;
                    }
                    if (res.data.agreement.autoExtensionInd === 'N') {
                        this.checked1 = false;
                    }

                    if (res.data.agreement.expired === 'Y') {
                        this.checked2 = true;
                    }
                    if (res.data.agreement.expired === 'N') {
                        this.checked2 = false
                    }

                    if (res.data.agreement.agrReceived === 'Y') {
                        this.checked3 = true;
                    }
                    if (res.data.agreement.agrReceived === 'N') {
                        this.checked3 = false;
                    }

                    if (res.data.agreement.furtherAssign === 'Y') {
                        this.checked4 = true;
                    }
                    if (res.data.agreement.furtherAssign === 'N') {
                        this.checked4 = false;
                    }
                    if (res.data.agreement.sd === 'Y') {
                        this.checked5 = true;
                    }
                    if (res.data.agreement.sd === 'N') {
                        this.checked5 = false;
                    }

                    if (res.data.agreement.preTerm === 'Y') {
                        this.checked6 = true;
                    }
                    if (res.data.agreement.preTerm === 'N') {
                        this.checked6 = false;
                    }
                }
            })
        },
        queryRight(ipNameNo, type, index){
            let params = { name_no: ipNameNo };
            params.page_num = 1;
            params.page_size = 10;
            let formData = qs.stringify(params)
            let config = {headers: { 'content-type': 'application/x-www-form-urlencoded' }}
            this.$http.post('/ip/name/es', formData, config).then(res => {
                if (res.success) {
                    if(res.data.list.length > 0){
                        this.handleRight(res.data.list[0].agreement, type, index);
                    }
                }
            })

        },
        handleRight(agreement, type, index){
            let right = {};
            let rightInfo = {};
            agreement.forEach( item => {
                if(!right[item.cash_right]){
                    right[item.cash_right] = [];
                }
                right[item.cash_right].push(item.society_code)
            })
            for(let key in right){
                right[key] = Array.from( new Set(right[key]) );
                rightInfo[key] = right[key].join(',');
            }
            console.log('----------------------------------');
            console.log(rightInfo, ':', type);
            if(type == 'info'){
                console.log(this.formContract);
                console.log(rightInfo.PER);
                this.$set(this.formContract, 'oipPshareSoc', rightInfo.PER || '099')
                // this.assignorInfo.oipPshareSoc = rightInfo.PER || '099';
                this.formContract.oipMshareSoc = rightInfo.MR || '099';
                this.formContract.oipSshareSoc = rightInfo.SY || '099';
                this.formContract.oipOdshareSoc = rightInfo.NOD || '099';
                this.formContract.oipDbshareSoc = rightInfo.DB || '099';
            }else if(type == 'assignee'){
                console.log(this.formContract);
                console.log(rightInfo.PER);
                this.$set(this.assigneeList[index], 'sipPshareSoc', rightInfo.PER || '099');
                // this.tableData4[index].sipPshareSoc = rightInfo.PER || '099';
                this.assigneeList[index].sipMshareSoc = rightInfo.MR || '099';
                this.assigneeList[index].sipSshareSoc = rightInfo.SY || '099';
                this.assigneeList[index].sipOdshareSoc = rightInfo.NOD || '099';
                this.assigneeList[index].sipDbshareSoc = rightInfo.DB || '099';
            }
        }
    }
}
</script>
<style lang="scss" scoped>
ul{
    padding: 10px 0;
    padding-left: 20px;
    margin-top: 0;
    margin-bottom: 0;
    background: #f7f4f4;
}
li{
    list-style: none;
    float: left;
    margin-right: 16px;
}
.assignee, .territory{
    padding-bottom: 0;
    border-bottom: 1px solid #ddd;
}
.label{
    // margin-right: 2px;
    color: #333;
}
.value{
    border:  1px solid #ccc;
    color: #777;
    border-radius: 4px;
    padding: 3px 8px;
    background: #fff;
    margin-left: 2px;
}
h2{
    margin: 0;
    padding-left: 10px;
    line-height: 36px;
}
/deep/ .el-drawer__body{
    height: 100%;
    overflow-y: auto;
}

</style>