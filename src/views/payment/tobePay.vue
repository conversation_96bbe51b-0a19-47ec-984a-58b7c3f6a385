<template>
    <!-- 可支付明細 -->
    <div>
        <el-form :inline="true" :model="searchForm" class="demo-form-inline" @keyup.enter.native="getList">
            <el-form-item label="會員類別">
                <el-select v-model="searchForm.ipType" placeholder style="width: 100px;">
                    <el-option label="L" value="L"></el-option>
                    <el-option label="N" value="N"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="Ip Base No">
                <el-input type="text" v-model="searchForm.ipBaseNo"></el-input>
            </el-form-item>
            <el-form-item label="Name">
                <el-input type="text" v-model="searchForm.paName"></el-input>
            </el-form-item>
            <el-form-item label="Dist no">
                <el-input type="text" v-model="searchForm.distNo"></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="getList">搜 索</el-button>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="setInvNo">set inv no</el-button>
            </el-form-item>
            <el-form-item>
                <span class="clear-search" @click="clearSearch()">清除搜索</span>
            </el-form-item>
        </el-form>
        <el-table
            :data="tableData"
            border
            style="width: 100%"
            @selection-change="handleSelectionChange"
            :empty-text="emptyText"
        >
            <el-table-column type="selection"></el-table-column>
            <el-table-column prop="id" label="ID"></el-table-column>
            <el-table-column prop="ipType" label="Ip Type"></el-table-column>
            <el-table-column prop="ipBaseNo" label="Ip Base No"></el-table-column>
            <el-table-column prop="paNameNo" label="Pa Name No"></el-table-column>
            <el-table-column prop="distNo" label="Dist No"></el-table-column>
            <el-table-column prop="salesTaxRate" label="Sales Tax"></el-table-column>
            <el-table-column prop="invNo" label="Inv No"></el-table-column>
            <el-table-column prop="pay" label="Is Pay"></el-table-column>
            <el-table-column prop="paymentMethod" label="Net Payment"></el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :total="total"
            @current-change="handleCurrentChange"
        ></el-pagination>
        <el-dialog :visible.sync="setInvNoShow" width="1100px" center :close-on-click-modal="false">
            <el-form :inline="true" @keyup.enter.native="setInvEvent" :model="setInvForm" :rules="rules" ref="invForm" label-position="left" class="demo-form-inline">
                <div>
                    <el-form-item label="Inv No" prop="ids">
                        <el-input placeholder v-model="setInvForm.invNo"></el-input>
                    </el-form-item>
                    <el-form-item label="Sales Tax" prop="salesTaxRate">
                        <el-input placeholder v-model="setInvForm.salesTaxRate"></el-input>
                    </el-form-item>
                </div>
            </el-form>
            <span slot="footer" class="dialog-footer">
        <el-button @click="setInvNoShow = false">取 消</el-button>
        <el-button type="primary" @click="setInvEvent">確 定</el-button>
      </span>
        </el-dialog>
    </div>
</template>

<script>
    export default {
        name: 'paymentlist',
        data () {
            return {
                searchForm: {page_num: 1},
                tableData: [],
                setInvNoShow: false,
                setInvForm: {
                    ids:'',
                    invNo:'',
                    salesTaxRate:''
                },
                total: 1,
                rules:{
                    invNo:[{required:true}],
                    salesTaxRate:[{required:true}],
                },
                emptyText: '數據加載中',

            }
        },
        methods: {
            getList () {
                this.emptyText = '數據加載中';
                this.$http
                    .get('/dist/autopay/lisDistAutopayNetPayMember', {
                        params: this.searchForm
                    })
                    .then(res => {
                        if (res.success) {
                            this.tableData = res.data.data.list
                            this.total = res.data.data.total
                            if(! this.tableData || this.tableData.length == 0){
                                this.emptyText = '暫無數據';
                            }
                        }
                    })
            },
            clearSearch () {
                this.searchForm = {}
                this.getList()
            },
            handleSelectionChange(val){
                if (val) {
                    console.log(val)
                    let arr = []
                    val.map(item => {
                        arr.push(item.id)
                    })
                    this.setInvForm.ids = arr.join(',')
                }
            },
            setInvNo () {
                this.setInvNoShow = true
            },
            setInvEvent(){
                this.$refs['invForm'].validate((valid) => {
                    if (valid) {
                        this.$http.post('/dist/autopay/distAutopayNetPayMemberSetInvNo',this.setInvForm).then(res => {
                            console.log(res)
                            if(res.success && res.data.code == 200){
                                this.$toast({tips:'修改成功'})
                                this.getList()
                                this.setInvNoShow = false
                            }else{
                                this.$toast({tips:res.data.message})
                            }
                        })
                    }
                });
            },
            handleCurrentChange (page) {
                this.searchForm.page_num = page
                this.getList()
            }
        },
        mounted () {
            this.getList()
        }
    }
</script>

<style scoped>
</style>
