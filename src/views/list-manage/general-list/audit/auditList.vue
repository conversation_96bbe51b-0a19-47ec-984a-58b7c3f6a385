<template>
  <!-- 拆分後的文件 用于審核用的 -->
  <div class="ipibpox">
    <el-form :inline="true" :model="search" ref="form" class="demo-form-inline" :rules="rules"  @keyup.enter.native='searchFn()'>
      <el-form-item prop="fileBaseId" :validate-event="false">
        <span class="red">*</span>
        <el-input v-model.trim="search.fileBaseId" placeholder="FID" style="width: 80px;"></el-input>
      </el-form-item>
      <!-- todo ? 上传時間筛选应该是上传清單列表 -->
      <!--            <el-form-item prop="Upload Time">-->
      <!--                <date-picker v-model="search.uploadTime" type="date" placeholder="Upload Time" style="width: 120px;"></date-picker>-->
      <!--            </el-form-item>-->

      <el-form-item>
        <el-input v-model.trim="search.title" placeholder="標題" style="width: 120px;"></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model.trim="search.uniqueKeyMd5" placeholder="Data unique key" style="width: 120px;"></el-input>
      </el-form-item>

      <!-- <el-form-item>
        <el-button :type="search.matchScore < 0 ? 'primary' : ''" @click="changeSearchScore(-1)">10分以下</el-button>
        <el-button :type="search.matchScore === 0 ? 'primary' : ''" @click="changeSearchScore(0)">10-20分</el-button>
        <el-button :type="search.matchScore > 0 ? 'primary' : ''" @click="changeSearchScore(1)">20分以上</el-button>
      </el-form-item> -->
      <el-form-item>
        <el-select v-model="search.matchScore" placeholder="分數選擇" style="width: 116px;">
          <el-option v-for=" (item,index) in config.score" :key="index" :value="item.value" :label="item.label"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="search.status" placeholder="所有狀態" style="width: 120px;">
          <el-option v-for=" (item, index) in config.status" :key="index" :value="item.value" :label="item.label"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="批次號:" prop="batchIdA">
        <el-input style="width: 90px;border-color:none" v-model.number="search.batchIdA" placeholder="Begin"></el-input>
      </el-form-item>
      <span style="line-height: 2.15;margin-left: -10px;">~</span>
      <el-form-item prop="batchIdB">
        <el-input style="width: 90px;border-color:none" v-model.number="search.batchIdB" placeholder="End"></el-input>
      </el-form-item>
      <!-- todo  -->
      <!-- <el-form-item>
                <el-input v-model="search.amendUser" placeholder="Amend User" style="width: 120px;"></el-input>
            </el-form-item> -->

      <el-form-item>
        <el-button type="primary" @click="searchFn()" v-if="isAuth('list-manage:general-list:audit:auditList:find')">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="success" @click="auditBatch(1)" v-if="isAuth('list-manage:general-list:audit:auditList:pass')">批量通過</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="danger" @click="auditBatch(2)" v-if="isAuth('list-manage:general-list:audit:auditList:refuse')">批量拒絕</el-button>
      </el-form-item>
      <el-form-item>
        <el-upload class="upload-demo" ref="upload" action="" accept=".xls,.xlsx" :auto-upload="false" :on-change="uploadChange" :on-success="uploadSuccess" :show-file-list="false" :file-list="fileList">
          <el-button size="small" type="primary" style="background-color: #b240ff;border-color: #b240ff;" v-if="isAuth('list-manage:general-list:audit:auditList:exportin')">審核結果導入</el-button>
        </el-upload>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="exportFn" v-if="isAuth('list-manage:general-list:audit:auditList:exportout')">審核結果導出</el-button>
      </el-form-item>
      <el-form-item>
        <span class="clear-search" @click="clearSearch()" v-if="isAuth('list-manage:general-list:audit:auditList:find')">清除搜索</span>
      </el-form-item>
    </el-form>
    <el-table :empty-text="emptyText" stripe :data="tableData" border style="width: 100%" @selection-change="selectionChangeHandle">
      <el-table-column type="selection">

      </el-table-column>
      <el-table-column prop="fileBaseId" label="FID" width="80px">
      </el-table-column>
      <!-- <el-table-column
                prop="uploadType"
                label="List Type">
            </el-table-column> -->
      <!-- <el-table-column
                prop="sourceCode"
                label="Source"> -->
      <!-- </el-table-column> -->
      <!-- todo -->
      <!-- <el-table-column
                prop="categoryCode"
                label="Category">
            </el-table-column> -->
      <el-table-column prop="title" label="Title" min-width="280px">
      </el-table-column>
      <el-table-column prop="artists" label="Aritists">
      </el-table-column>
      <el-table-column prop="authors" label="Author" min-width="100">
        <template slot-scope="scope">
          {{scope.row.authors | addSpace}}
        </template>
      </el-table-column>
      <el-table-column prop="composers" label="Composer">
        <template slot-scope="scope">
          {{scope.row.composers | addSpace}}
        </template>
      </el-table-column>
      <el-table-column label="Duration" width="120px">
        <template slot-scope="scope">
          <span v-if="scope.row.durationM || scope.row.durationS">{{scope.row.durationM + ':' + scope.row.durationS}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="matchWorkTitle" label="Match Work Title" min-width="120px">
      </el-table-column>
      <el-table-column label="Soc-WorkId" min-width="120">
        <template slot-scope="scope">
          {{(scope.row.matchWorkSocietyCode ? scope.row.matchWorkSocietyCode : '') + (scope.row.matchWorkId ? '-'+scope.row.matchWorkId : '')}}
        </template>
      </el-table-column>
      <el-table-column prop="clickNumber" label="clickNumber" width="">
      </el-table-column>
      <el-table-column prop="matchScore" label="Score" width="70px">
      </el-table-column>
      <el-table-column prop="uniqueKeyMd5" label="Data unique key">
        <template slot-scope="scope">
          <el-tooltip class="item" effect="dark" :content="scope.row.uniqueKeyMd5" placement="top">
            <div class="oneLine">{{scope.row.uniqueKeyMd5}}</div>
          </el-tooltip>
        </template>
      </el-table-column>

      <el-table-column prop="matchScore" label="Status" width="80px">
        <template slot-scope="scope">
          {{scope.row.status | Status}}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="OP" width="60px">
        <template slot-scope="scope">
          <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
            <span class="a-blue" @click="audit(scope.row,scope.$index)" v-if="isAuth('list-manage:general-list:audit:auditList:audit')">審核</span>
          </span>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination background layout="prev, pager, next" :total="total" @current-change="handleCurrentChange">
    </el-pagination>
  </div>
</template>

<script>
export default {
  name: 'sampleDateList',
  data() {
    return {
      tableData: [],
      searchparam: {},
      total: 0,
      rules: {
        fileBaseId: [
          { required: true, message: '請輸入FID' }
        ],
        batchIdA: [{ pattern: /^\+?[1-9]\d*$/, message: '必須>0' }],
        batchIdB: [{ pattern: /^\+?[1-9]\d*$/, message: '必須>0' }]
      },
      search: {
        id: '',
        matchScore: '',
        batchIdA: '',
        batchIdB: '',
        categoryCode: '',
        uploadTime: '',
        sourceCode: '',
        status: "",
        uploadType: '',
      },
      currentPage: 1,
      config: {
        status: [
          {
            value: '',
            label: '全部狀態'
          },
          {
            value: '0',
            label: '待審核'
          },
          {
            value: '1',
            label: '已匹配'
          },
          {
            value: '2',
            label: '不匹配'
          }
        ],
        score: [{
          value: '',
          label: '全部評分'
        },
        {
          value: -1,
          label: '10分以下'
        },
        {
          value: 0,
          label: '10-20分'
        },
        {
          value: 1,
          label: '20分以上'
        },
        ],
        uploadType: ['PG', 'FW', 'MS', 'CJ']
      },
      dataListSelections: [],
      //导入报告列表
      fileList: [],
      emptyText:'暫無數據',
    }
  },
  filters: {
    Status: function (status) {
      let config = {
        0: '待審核',
        1: '已匹配',
        2: '不匹配'
      }
      return config[status]
    }
  },
  activated() {
    this.$nextTick(() => {
      if (this.$route.query.update) {
        let query = this.$route.query;
        delete query.update;
        this.searchFn(this.currentPage)
      }
    })
  },

  methods: {
    clearSearch() {
      this.search = {
        id: '',
        matchScore: '',
        categoryCode: '',
        batchIdA: '',
        batchIdB: '',
        uploadTime: '',
        sourceCode: '',
        status: '',
        uploadType: ''
      }
    },
    init() {
      this.searchFn(1);
    },
    handleSelect(item) {
      console.log(item);
    },
    searchFn(page = 1) {
      if (isNaN(this.search.fileBaseId)) {
        this.$toast({ tips: '請輸入正確的fid' })
        return
      }
      this.$refs.form.validate(validate => {
        if (validate) {
          let ajaxData = {};
          this.currentPage = page
          this.search.pageNum = page
          this.search.pageSize = 10
          if (page == 1) {
            this.total = 0
          }
          ajaxData = this.$utils.copy(this.search);
          if (ajaxData.matchScore == 99) {
            delete ajaxData.matchScore;
          }
          this.searchparam = ajaxData
          this.emptyText = '數據加載中';
          this.$http.post('/listmatch/getListMatchList', ajaxData).then(res => {
            if (res.data.code == 200) {
              this.tableData = res.data.data?res.data.data.list:[];
              // console.warn("((((((",this.tableData)
              this.total = res.data.data?res.data.data.total:0
            }
            if(! this.tableData || this.tableData.length == 0){
              this.emptyText = '暫無數據';
            }
          }).catch(res => {

          })
        }
      })

    },
    searchFnfirst(page = 1) {

      let ajaxData = {};
      this.currentPage = page
      this.search.pageNum = page
      this.search.pageSize = 10
      if (page == 1) {
        this.total = 0
      }
      ajaxData = this.$utils.copy(this.search);
      if (ajaxData.matchScore == 99) {
        delete ajaxData.matchScore;
      }
      this.searchparam = ajaxData
      this.emptyText = '數據加載中';
      this.$http.post('/listmatch/getListMatchList', ajaxData).then(res => {
        if (res.data.code == 200) {
          this.tableData = res.data.data ? res.data.data.list : [];
          // console.warn("((((((",this.tableData)
          this.total = res.data.data ? res.data.data.tota : 0;
        }
        if(! this.tableData || this.tableData.length == 0){
          this.emptyText = '暫無數據';
        }
      }).catch(res => {

      })
    },
    handleCurrentChange(val) {
      this.searchFn(val);
    },
    audit(item, params) {
      this.$router.push({ name: 'listAudit', query: { id: item.id, listType: item.uploadType, useparams: params, usetabledata: this.tableData, thissearchparam: this.searchparam } })
    },
    uploadChange(file) {
      this.$utils.uploadFile({flie:file.raw,fid:this.search.fileBaseId}, '/listmatch/importAuditResults', this)
    },
    uploadSuccess(response, file, filelist) {
      console.log("{+++++++++", response, file, filelist)
      this.searchFn(this.currentPage)

    },
    exportFn() {
      this.$msgbox.confirm(`確定進行[導出]操作?`, '提示', {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$refs.form.validate(validate => {
          if (validate) {
            let ajaxData = {};
            ajaxData.listMatchDataBasic = this.$utils.copy(this.search);
            if (ajaxData.listMatchDataBasic.matchScore == 99) {
              delete ajaxData.listMatchDataBasic.matchScore;
            }
            this.$http.post('/listmatch/export', ajaxData, { responseType: 'blob' }).then(res => {
              console.warn("{}{{{resresres", res)
              let data = res.data
              this.$utils.downloadByBlob(data, res.headers["content-disposition"])
            })
          }
        })
      })

    },
    changeSearchScore(score) {
      this.search.matchScore = score;
    },
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    auditBatch(status) {
      let url = '/listmatch/checkListMatchDataBasicAll';

      let uniqueKeyMd5List = [];
      this.dataListSelections.forEach(item => {
        uniqueKeyMd5List.push(item.uniqueKeyMd5);
      })
      if (uniqueKeyMd5List.length == 0) {
        this.$toast({ tips: '請至少選擇一條' });
        return;
      }
      let ajaxData = {
        status,
        uniqueKeyMd5List
      }
      this.$http.post(url, ajaxData).then(res => {
        console.log('resrrews', res)
        if (res.data.code && res.data.code != 200) {
          this.$toast({ tips: res.data.message })
        } else {
          this.$toast({ tips: res.data.data });
          this.searchFn();
        }
      })
    },
    importFn() {
      // todo 後端功能待開發 01-14
      console.log();
    }
  },
  // beforeRouteEnter(to, from, next) {
  //   next((vm) => {
  //     vm.searchFnfirst(vm.currentPage)

  //   });
  // },
}
</script>

<style>
.oneLine {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
