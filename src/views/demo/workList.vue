<template>
    <el-drawer
        :visible.sync="show"
        :title="workId"
        direction="rtl"
        size="80%"
    >
        <div v-loading="loading">
            <!-- works区域 -->
            <div>
                <h2>works</h2>
                <ul class="clear fromData">
                    <li>
                        <span class="label">societyCode:</span>
                        <span class="value">{{wrkWork.workSocietyCode}}</span>
                    </li>
                    <li>
                        <span class="label">title:</span>
                        <span class="value">{{wrkWork.title}}</span>
                    </li>
                    <li>
                        <span class="label">Local:</span>
                        <el-checkbox v-model="wrkWork.local" disabled></el-checkbox>
                    </li>
                    <li>
                        <span class="label">Duration:</span>
                        <span class="value">{{wrkWork.durationM}}:{{wrkWork.durationS}}</span>
                    </li>
                    <li>
                        <span class="label">Genre:</span>
                        <span class="value">{{wrkWork.genre}}</span>
                    </li>
                    <li>
                        <span class="label">PerLanguage:</span>
                        <span class="value">{{wrkWork.performLanguage}}</span>
                    </li>
                    <li>
                        <span class="label">Title Language:</span>
                        <span class="value">{{wrkWork.titleLanguage}}</span>
                    </li>
                    <li>
                        <span class="label">PublishDate:</span>
                        <span class="value">{{$utils.DATE(wrkWork.publishAirDate, 'yyyy-MM-dd')}}</span>
                    </li>
                    <li>
                        <span class="label">ISWC:</span>
                        <span class="value">{{wrkWork.iSWC}}</span>
                    </li>
                </ul>
            </div>
            <!-- performer -->
            <div v-if="waList.length">
                <h2>Performer</h2>
                <ul class="clear tablelist">
                    <li v-for="(item, index) in waList" :key="index" @dblclick="getName(item)">
                        <span class="value ellipsis" style="display:inline-block;vertical-align: middle;height:30px;line-height: 20px;text-align: center;width: 80px;">{{item.id}}</span>
                        <span class="value ellipsis" style="display:inline-block;vertical-align: middle;height:30px;line-height: 20px;text-align: center;width: 130px;">{{item.ipNameNo}}</span>
                        <span class="value ellipsis" style="display:inline-block;vertical-align: middle;height:30px;line-height: 20px;text-align: center;width: 160px;">{{item.artistName}}</span>
                        <span class="value ellipsis" style="display:inline-block;vertical-align: middle;height:30px;line-height: 20px;text-align: center;width: 68px;">{{item.chineseName}}</span>
                        <span class="value ellipsis" style="display:inline-block;vertical-align: middle;height:30px;line-height: 20px;text-align: center;width: 48px;">{{item.country}}</span>
                        <span class="value ellipsis" style="display:inline-block;vertical-align: middle;height:30px;line-height: 20px;text-align: center;width: 48px;">{{item.inputSoc}}</span>
                        <el-checkbox v-model='item.perf'>Perf</el-checkbox>
                        <el-checkbox v-model='item.Pub'>Pub</el-checkbox>
                    </li>
                </ul>
            </div>
            <el-dialog title="IPI info"
            :visible.sync="dialogVisible"
            width="40%"
            append-to-body>
                <ul class="clear tablelist">
                    <li v-for="(item, nameIndex) in nameList" :key="nameIndex">
                        <span class="value ellipsis" style="display:inline-block;vertical-align: middle;height:30px;line-height: 20px;text-align: center;width: 130px;">{{item.ipBaseNo}}</span>
                        <span class="value ellipsis" style="display:inline-block;vertical-align: middle;height:30px;line-height: 20px;text-align: center;width: 130px;">{{item.nameNo}}</span>
                        <span class="value ellipsis" style="display:inline-block;vertical-align: middle;height:30px;line-height: 20px;text-align: center;width: 160px;">{{item.name}}</span>
                        <span class="value ellipsis" style="display:inline-block;vertical-align: middle;height:30px;line-height: 20px;text-align: center;width: 160px;">{{item.chineseName}}</span>
                        <span class="value ellipsis" style="display:inline-block;vertical-align: middle;height:30px;line-height: 20px;text-align: center;width: 50px;">{{item.nameType}}</span>
                    </li>
                </ul>
            </el-dialog>
            <!-- IP Share -->
            <div v-if="ipShareData.length">
                <h2>IP Share   <el-checkbox v-model='IP_ShareCheck' style="margin-right:20px">Distributable</el-checkbox><el-checkbox v-model='IP_ShareCheck2'>Right SD</el-checkbox></h2>
                <ul class="clear tablelist">
                    <li v-for="(item, index) in ipShareData" :key="index" @dblclick="getName(item)">
                        <el-tooltip placement="top" :content="item.groupIndicator" :open-delay="tooltipDelay">
                            <span class="value ellipsis" style="display:inline-block;vertical-align: middle;height:30px;line-height: 20px;text-align: center;width: 40px;">{{item.groupIndicator}}</span>
                        </el-tooltip>
                        <el-tooltip placement="top" :content="item.name" :open-delay="tooltipDelay" >
                            <span class="value ellipsis" style="display:inline-block;vertical-align: middle;height:30px;line-height: 20px;text-align: center;width: 300px;">{{item.name}}</span>
                        </el-tooltip>
                        <el-tooltip placement="top" :content="item.chineseName"  :open-delay="tooltipDelay">
                            <span class="value ellipsis" style="display:inline-block;vertical-align: middle;height:30px;line-height: 20px;text-align: center;width: 70px;">{{item.chineseName}}</span>
                        </el-tooltip>
                        <el-tooltip placement="top" :content="item.ipNameNo"  :open-delay="tooltipDelay">
                            <span class="value ellipsis" style="display:inline-block;vertical-align: middle;height:30px;line-height: 20px;text-align: center;width: 122px;">{{item.ipNameNo}}</span>
                        </el-tooltip>
                        <el-tooltip placement="top" :content="item.workIpRole"  :open-delay="tooltipDelay">
                            <span class="value ellipsis" style="display:inline-block;vertical-align: middle;height:30px;line-height: 20px;text-align: center;width: 38px;">{{item.workIpRole}}</span>
                        </el-tooltip>
                        <el-tooltip placement="top" :content="item.ipSocietyCode"  :open-delay="tooltipDelay">
                            <span class="value ellipsis" style="display:inline-block;vertical-align: middle;height:30px;line-height: 20px;text-align: center;width: 48px;">{{item.ipSocietyCode}}</span>
                        </el-tooltip>
                        <el-tooltip placement="top" :content="item.ipShare"  :open-delay="tooltipDelay">
                            <span class="value ellipsis" style="display:inline-block;vertical-align: middle;height:30px;line-height: 20px;text-align: center;width: 55px;">{{item.ipShare}}</span>
                        </el-tooltip>
                        <el-checkbox v-model='item.sdcheck'>Sd</el-checkbox>
                    </li>
                </ul>
            </div>
            <!-- Component -->
            <div v-if="wwcList.length">
                <h2>Component</h2>
                <ul class="clear tablelist">
                    <li v-for="(item, index) in wwcList" :key="index">
                        <span class="value">{{item.title}}</span>
                        <span class="value">{{item.componentWorkId}}</span>
                        <span class="value">{{item.comWorkSociety}}</span>
                        <span class="value">{{item.genre}}</span>
                        <span class="value">{{item.durationM}}</span>
                        <span class="value">{{item.durationS}}</span> ellipsis
                        <span class="value">{{item.usageType}}</span>
                    </li>
                </ul>
            </div>
            <!-- Source -->
            <div v-if="wwsList.length">
                <h2>Source</h2>
                <ul class="clear tablelist">
                    <li v-for="(item, index) in wwsList" :key="index">
                        <el-tooltip placement="top" :content="item.sourceType" :open-delay="tooltipDelay">
                            <span class="value ellipsis" style="display:inline-block;vertical-align: middle;height:30px;line-height: 20px;text-align: center;width: 60px;">{{item.sourceType}}</span>
                        </el-tooltip>
                        <el-tooltip placement="top" :content="item.notifySouceId" :open-delay="tooltipDelay">
                            <span class="value ellipsis" style="display:inline-block;vertical-align: middle;height:30px;line-height: 20px;text-align: center;width: 130px;">{{item.notifySouceId}}</span>
                        </el-tooltip>
                        <el-tooltip placement="top" :content="item.name" :open-delay="tooltipDelay">
                            <span class="value ellipsis" style="display:inline-block;vertical-align: middle;height:30px;line-height: 20px;text-align: center;width: 370px;">{{item.name}}</span>
                        </el-tooltip>
                        <el-tooltip placement="top" :content="$utils.DATE(item.notifyDate, 'yyyy-MM-dd')" :open-delay="tooltipDelay">
                            <span class="value ellipsis" style="display:inline-block;vertical-align: middle;height:30px;line-height: 20px;text-align: center;width: 108px;">{{$utils.DATE(item.notifyDate, 'yyyy-MM-dd')}}</span>
                        </el-tooltip>
                        <el-tooltip placement="top" :content="item.inputSoc" :open-delay="tooltipDelay">
                            <span class="value ellipsis" style="display:inline-block;vertical-align: middle;height:30px;line-height: 20px;text-align: center;width: 48px;">{{item.inputSoc}}</span>
                        </el-tooltip>
                    </li>
                </ul>
            </div>
            <!-- Other Titles -->
            <div v-if="wwtList.length">
                <h2>Other Titles</h2>
                <ul class="clear tablelist">
                    <li v-for="(item, index) in wwtList" :key="index">
                        <el-tooltip placement="top" :content="item.titleType" :open-delay="tooltipDelay">
                            <span class="value ellipsis" style="display:inline-block;vertical-align: middle;height:30px;line-height: 20px;text-align: center;width: 50px;">{{item.titleType}}</span>
                        </el-tooltip>
                        <el-tooltip placement="top" :content="item.title" :open-delay="tooltipDelay">
                            <span class="value ellipsis" style="display:inline-block;vertical-align: middle;height:30px;line-height: 20px;text-align: center;width: 160px;">{{item.title}}</span>
                        </el-tooltip>
                        <el-tooltip placement="top" :content="item.episodeNo" :open-delay="tooltipDelay">
                            <span class="value ellipsis" style="display:inline-block;vertical-align: middle;height:30px;line-height: 20px;text-align: center;width: 36px;">{{item.episodeNo}}</span>
                        </el-tooltip>
                        <el-tooltip placement="top" :content="item.languageCode" :open-delay="tooltipDelay">
                            <span class="value ellipsis" style="display:inline-block;vertical-align: middle;height:30px;line-height: 20px;text-align: center;width: 55px;">{{item.languageCode}}</span>
                        </el-tooltip>
                        <el-tooltip placement="top" :content="item.genreCode" :open-delay="tooltipDelay">
                            <span class="value ellipsis" style="display:inline-block;vertical-align: middle;height:30px;line-height: 20px;text-align: center;width: 55px;">{{item.genreCode}}</span>
                        </el-tooltip>
                        <el-tooltip placement="top" :content="item.inputSoc" :open-delay="tooltipDelay">
                            <span class="value ellipsis" style="display:inline-block;vertical-align: middle;height:30px;line-height: 20px;text-align: center;width: 55px;">{{item.inputSoc}}</span>
                        </el-tooltip>
                    </li>
                </ul>
            </div>
            <!-- Remark -->
            <div v-if="wwrList.length">
                <h2>Remark</h2>
                <ul class="clear smTablelist">
                    <li v-for="(item, index) in wwrList" :key="index">
                        <span class="value">{{item.inputSoc}}</span>
                        <span class="value">{{item.remark}}</span>
                    </li>
                </ul>
            </div>
            <!-- ISRC -->
            <div v-if="wiList.length">
                <h2>ISRC</h2>
                <ul class="clear smTablelist">
                    <li v-for="(item, index) in wiList" :key="index">
                        <span class="value">{{item.isrc}}</span>
                        <span class="value">{{item.inputSoc}}</span>
                    </li>
                </ul>
            </div>
            <!-- Ref Work -->
            <div v-if="wrkWorkOtherSocCodeList.length">
                <h2>Ref Work</h2>
                <ul class="clear tablelist">
                    <li v-for="(item, index) in wrkWorkOtherSocCodeList" :key="index">
                        <span class="value">{{item.sourceType}}</span>
                        <span class="value">{{item.sourceCode}}</span>
                        <span class="value">{{item.sourceName}}</span>
                        <span class="value">{{item.sourceWorkId}}</span>
                        <span class="value">{{$utils.DATE(item.amendTime, 'yyyy-MM-dd')}}</span>
                        <span class="value">{{item.createUserName}}</span>
                    </li>
                </ul>
            </div>
        </div>
    </el-drawer>
</template>
<script>
export default {
    data(){
        return {
            loading: false,
            show:false,
            wrkWork:{},//works区域
            waList:[],//performer  10
            wwcList:[],//Component
            ipShareData:[],//ipshare   wwisList    per10
            wwsList:[],//source
            wwtList:[],//other title
            wwrList:[],//remark
            wiList:[],//isrc
            nameList:[],
            wrkWorkOtherSocCodeList:[],//ref work
            IP_ShareCheck:false,
            IP_ShareCheck2:false,
            dialogVisible: false,
            tooltipDelay: 500
        }
    },
    props: {
        workId: {
            type: String,
            default: ''
        },
        workSocietyCode: {
            type: String,
            default: ''
        },
    },
    methods: {
        init(){
            this.show=true
            this.getData()
        },
        getName(item){
            this.$http.get('ip/name/'+item.ipNameNo).then(res => {
                console.log(res)
                this.dialogVisible=true
                this.nameList = res.data || []
            })
        },
        getData(){
            let params = {
                workId: this.workId,
                workSocietyCode: this.workSocietyCode
            }
            console.log(params)
            this.loading=true
            this.$http.get('/wrk/getWrkWorkById', {params}).then(res => {
                this.loading=false
                if (res.status === 200) {
                    console.log(res)
                    // works
                    this.wrkWork = res.data.wrkWork
                    this.IP_ShareCheck = res.data.wrkWorkRightMap.PER ? res.data.wrkWorkRightMap.PER.distributable == 0?false:true : false
                    this.IP_ShareCheck2 = this.wrkWork.sd == "N"?false:true
                    // this.waList = res.data.waList.slice(0,10)
                    // this.wwcList = res.data.wwcList.slice(0,10)
                    // this.wwsList = res.data.wwsList.slice(0,10)
                    // this.wwtList = res.data.wwtList.slice(0,10)
                    // this.wwrList = res.data.wwrList.slice(0,10)
                    // this.wiList = res.data.wiList.slice(0,10)
                    this.waList = res.data.waList
                    this.wwcList = res.data.wwcList
                    this.wwsList = res.data.wwsList
                    this.wwtList = res.data.wwtList
                    this.wwrList = res.data.wwrList
                    this.wiList = res.data.wiList

                    // ref work
                    this.wrkWorkOtherSocCodeList = res.data.wrkWorkOtherSocCodeList
                    // ipshare
                    let wwisList = res.data.wwisList[0] ? res.data.wwisList[0] : {}
                    if(wwisList.PER && wwisList.PER.length){
                        this.ipShareData = wwisList.PER
                    }else{
                        this.ipShareData = [];
                    }
                }
            })
        },
    }
}
</script>
<style scoped>
ul{
    padding: 10px 20px;
    margin-top: 0;
    margin-bottom: 0;
    background: #f7f4f4;
    overflow-y: auto; /* 添加垂直滚动条 */
    max-height: 300px; /* 可选：设置最大高度 */
}
li{
    list-style: none;
    line-height: 40px;
}
.fromData li{
    margin-right: 16px;
    float: left;
}
.tablelist li{
    width: 100%;
}
.smTablelist li {
    margin-right: 16px;
    float: left;
    border-right: 1px solid rgba(0, 0, 0, .1);
    padding-right: 16px;
}
.smTablelist li:last-of-type{
    border: none;
}

.assignee, .territory{
    padding-bottom: 0;
    border-bottom: 1px solid #ddd;
}
.label{
    /* // margin-right: 2px; */
    color: #333;
}
.value{
    border:  1px solid #ccc;
    color: #777;
    border-radius: 4px;
    padding: 3px 8px;
    background: #fff;
    margin-left: 2px;
}
h2{
    margin: 0;
    padding-left: 10px;
    line-height: 36px;
}
.ellipsis{
    overflow:hidden;
    text-overflow:ellipsis;
    white-space:nowrap;
}
/deep/ .el-drawer__body{
    height: 100%;
    overflow-y: auto;
    padding: 0 20px 0;
}

>>> .el-drawer__header{
    margin-bottom: 0;
    font-weight: bold;
    outline: 0;
    padding-bottom: 20px;
}
>>> .el-drawer__header span{
    outline: 0;
}

>>> .el-checkbox+.el-checkbox{
    margin-left: 0;
}
>>> .el-checkbox{
    margin-right: 0;
}
</style>
