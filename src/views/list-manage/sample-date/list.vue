<template>
  <div class="ipibpox">
    <el-form :inline="true" :model="search" ref="form" class="demo-form-inline"  @keyup.enter.native='searchFn(1)'>
      <el-form-item prop="name">
        <el-input v-model.trim="search.name" placeholder="Name"></el-input>
      </el-form-item>
      <el-form-item prop="categoryCode">
        <el-input v-model.trim="search.categoryCode" placeholder="Category Code"></el-input>
      </el-form-item>
      <el-form-item prop="year">
        <el-input v-model="search.year" placeholder="Year"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="searchFn(1)"  v-if="isAuth('list-manage:sample-date:list:find')">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="addFn" v-if="isAuth('list-manage:sample-date:list:add')">新增</el-button>
      </el-form-item>
      <el-form-item>
        <span class="clear-search" @click="init()" v-if="isAuth('list-manage:sample-date:list:find')">清除搜索</span>
      </el-form-item>
    </el-form>
    <el-table :empty-text="tableresult"   stripe :data="tableData" border style="width: 100%" v-loading="loading">
      <el-table-column prop="name" label="Name">
      </el-table-column>
      <el-table-column prop="categoryCode" label="Category Code" min-width="100px">
      </el-table-column>
      <el-table-column prop="year" label="Year">
      </el-table-column>
      <el-table-column prop="startDate" label="Start Date">
        <template slot-scope="scope">
          {{splitTime(scope.row.startDate)}}
        </template>
      </el-table-column>
      <el-table-column prop="endDate" label="EndDate">
        <template slot-scope="scope">
          {{splitTime(scope.row.endDate)}}
        </template>
      </el-table-column>
      <el-table-column prop="amendTime" label="Amend Time">
        <template slot-scope="scope">
          {{splitTime(scope.row.amendTime)}}
        </template>
      </el-table-column>
      <el-table-column label="Frequency/Days" min-width="100px">
        <template slot-scope="scope">
          {{scope.row.dayExtracted}}/{{scope.row.dayBase}}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="operation" width="100">
        <template slot-scope="scope">
          <span style="width: 100%;display:flex;text-align: center;cursor: pointer">
            <el-button @click="editFn(scope.row)" type="text" size="small" v-if="isAuth('list-manage:sample-date:list:change')">編輯</el-button>
            <el-button @click="deleteData(scope.row)" type="text" size="small" v-if="isAuth('list-manage:sample-date:list:del')">刪除</el-button>
          </span>

        </template>
      </el-table-column>
    </el-table>
    <el-pagination background layout="prev, pager, next" :total="total" :current-page="search.page.pageNum" @current-change="handleCurrentChange">
    </el-pagination>
  </div>
</template>

<script>
export default {
  name: 'sampleDateList',
  data() {
    return {
      tableData: [],tableresult:' ',
      total: 0,
      search: {
        name: '',
        categoryCode: '',
        year: '',
        page: {
          pageNum: 1,
          pageSize: 10
        }
      },
      loading: false,
    }
  },
  mounted() {
    this.searchFn();
  },
  activated() {
    // if(this.$route.query.update){
    //     this.init();
    //     this.$router.push({name: 'sampleDateList', query: {update: false}});
    // }
    this.$nextTick(() => {
      if (this.$route.query.update) {
        let query = this.$route.query;
        delete query.update;
        this.init();
      }
    })
  },

  methods: {
    init() {
      this.search = {
        name: '',
        categoryCode: '',
        year: '',
        page: {
          pageNum: 1,
          pageSize: 10
        }
      }
      this.total = 0
      this.searchFn();
    },
    splitTime(time) {
      return time.split(' ')[0]
    },
    searchFn(page) {
      if (page==1||this.search.name||this.search.categoryCode||this.search.year) {
        this.search.page.pageNum=1
      }
                        this.tableresult = '數據加載中...'
      this.$http.post('/listSampleRule/getListSampleRuleBaseList', this.search).then(res => {
        console.log(res)
        if (res.success) {
          this.tableData = res.data.list
          this.total = res.data.total
        }
          this.tableresult = this.tableData.length == 0 ? '暫無數據' : ' '
      })
    },
    handleCurrentChange(val) {
      this.search.page.pageNum = val
      this.searchFn();
    },
    handleClick(item) {
      this.$router.push({ name: '', query: {} })
    },
    editFn(row) {
      this.$router.push({ name: 'sampleDateEdit', params: { row: row } })
    },
     deleteData(row) {
       console.log('rowrow====',row)
      this.$msgbox.confirm('確定刪除?', '提示', {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
          this.$http.delete('/listSampleRule/delete/' + row.id).then(res => {
            console.log('resres', res)
            if (res.success && res.data.code == 200) {
              this.$message({
                message: '删除成功',
                type: 'success'
              })
              this.searchFn( this.search.page.pageNum)
              // this.$bus.$emit('closeCurrentTab', () => { this.$router.push({ name: 'sampleDateList', query: { update: true } }) });
            } else {
              this.$message({
                message: '删除失敗',
                type: 'error'
              })
            }

          })
      }).catch(() => {
      });
    },
    addFn() {
      this.$router.push({ name: 'sampleDateAdd' })
    }
  }
}
</script>

<style>
</style>
