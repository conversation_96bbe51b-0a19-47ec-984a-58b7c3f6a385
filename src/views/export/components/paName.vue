<template>
  <div>
    <el-dialog
      :visible.sync="paNameShow"
      width="920px"
      title="Pa Name No"
      :close-on-click-modal="false"
      :before-close="close"
    >
      <div style="margin: auto;margin-bottom: 20px; text-align: center;">
        <el-input @keyup.enter.native="paNameDataD()" v-model="search.paNameNo" placeholder="Pa Name No" style="width: 200px;"></el-input>
        <el-input @keyup.enter.native="paNameDataD()" v-model="search.name" placeholder="Name" style="width: 200px;"></el-input>
        <el-button type="primary" @click="paNameDataD()">查詢</el-button>
        <span class="clear-search" @click="clearSearch()">清除搜索</span>
      </div>
      <el-table
        ref="multipleTable"
        :data="tableData"
        stripe
        :empty-text="emptyText"
      >
        <el-table-column property="ip_name_no" label="Pa Name No" width="150px"></el-table-column>
        <el-table-column property="name" label="Name" width="320px"></el-table-column>
        <el-table-column property="chinese_name" label="Chinese Name" width="320px"></el-table-column>
        <el-table-column label="operation" width="100">
          <template slot-scope="scope">
            <span
              style="
                width: 100%;
                display: inline-block;
                text-align: center;
                cursor: pointer;
              "
            >
              <i class="el-icon-check" @click="checked(scope.row)"></i>
            </span>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        layout="prev, pager, next"
        :total="total"
        @current-change="paNameDataD"
        :current-page="currentPage"
      >
      </el-pagination>
    </el-dialog>
  </div>
</template>
<script>
import qs from "qs";
import { Loading } from "element-ui";
export default {
  name: "",
  data() {
    return {
      paNameShow: false,
      tableData: [],
      emptyText: "暫無數據",
      total: 1,
      currentPage: 1,
      loading: null,
      search:{
        paNameNo:'',
        name:''
      }
    };
  },
  props: {
    paNameNo: {
      type: String,
      default: "",
    },
  },
  watch: {
    paNameNo(data) {
      this.paNameNo = data
    },
  },
  methods: {
    clearSearch(){
      this.search={
        paNameNo:'',
        name:''
      }
      this.paNameDataD(1)
    },
    close() {
      this.paNameShow = false;
      this.search={
        paNameNo:'',
        name:''
      }
      this.$emit("flag");
    },
    checked(data) {
      this.$emit("checkPaName", data);
      this.paNameShow = false;
      this.search={
        paNameNo:'',
        name:''
      }
    },
    paNameDataC(page) {
      this.loading = Loading.service({
        lock: true,
        text: "Loading",
        background: "rgba(0,0,0,0.3)",
      });
      let params = {
        name_type: "PA",
        name_no: this.paNameNo,
        page_num: page ? page : 1,
        page_size: 10,
      };
      params = qs.stringify(params);
      let config = {
        headers: { "content-type": "application/x-www-form-urlencoded" },
      };
      this.emptyText = "數據加載中";
      this.$http.post("/ip/name/es", params, config).then((res) => {
        this.loading.close();
        if (res.success) {
          this.tableData = res.data.list;
          if (!this.tableData || this.tableData.length == 0) {
            this.emptyText = "暫無數據";
          }
          this.total = res.data.total;
          this.currentPage = page ? page : 1;
          if (this.tableData.length == 0) {
            this.$emit("checkPaName", {
              ip_name_no: "",
              name: "",
            });
            this.$toast({ tips: "未查詢到Pa Name No" });
            return;
          } else {
            this.$emit("checkPaName", this.tableData[0]);
          }
          
        } else {
          this.$toast({ tips: res.data.message });
          return;
        }
      });
    },
    paNameDataD(page) {
      this.loading = Loading.service({
        lock: true,
        text: "Loading",
        background: "rgba(0,0,0,0.3)",
      });
      let params = {
        name_type: "PA",
        name_no: this.search.paNameNo,
        name: this.search.name,
        page_num: page ? page : 1,
        page_size: 10,
      };
      params = qs.stringify(params);
      let config = {
        headers: { "content-type": "application/x-www-form-urlencoded" },
      };
      this.emptyText = "數據加載中";
      this.$http.post("/ip/name/es", params, config).then((res) => {
        this.loading.close();
        if (res.success) {
          this.tableData = res.data.list;
          if (!this.tableData || this.tableData.length == 0) {
            this.emptyText = "暫無數據";
          }
          this.total = res.data.total;
          this.currentPage = page ? page : 1;
          this.paNameShow = true;
        } else {
          this.$toast({ tips: res.data.message });
          return;
        }
      });
    },
  },
};
</script>
<style scoped>
</style>