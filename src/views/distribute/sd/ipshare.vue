<template>
  <div>
    <div class="header">
      <p class="left">
        <span><b>work No:</b>{{dataInfo.workId}}</span>
        <span><b>title:</b>{{dataInfo.title}}</span>
        <span><b>分配金額:</b>{{dataInfo.distAmount}}</span>
        <span><b>soc:</b>{{dataInfo.workSocietyCode}}</span>
      </p>
      <p class="right">
        <el-button type="primary" @click="addIpShare">新增</el-button>
      </p>
    </div>
    <el-table :data="tableData" border stripe v-loading="loading" style="width: 100%">
      <el-table-column prop="rightType" label="Right">
      </el-table-column>
      <el-table-column prop="groupIndicator" label="Gp">
      </el-table-column>
      <el-table-column prop="ipName" label="IP Name">
      </el-table-column>
      <el-table-column prop="ipNameNo" label="IP Name No">
        <template slot-scope="scope">
          <span :title="scope.row.ipNameNo">{{scope.row.ipNameNo}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="workIpRole" label="role">
      </el-table-column>
      <el-table-column prop="workIpSociety" label="soc">
      </el-table-column>
      <el-table-column prop="ipShare" label="Old IP Share">
        <template slot-scope="scope">
          <el-input v-model="scope.row.ipShare"></el-input>
        </template>
      </el-table-column>
      <el-table-column prop="adjIpShare" label="new IP Share">
        <template slot-scope="scope">
          <el-input v-model="scope.row.adjIpShare"></el-input>
        </template>
      </el-table-column>
      <el-table-column prop="distAmount" label="sd amount">
        <template slot-scope="scope">
          <el-input v-model="scope.row.distAmount"></el-input>
        </template>
      </el-table-column>
      <el-table-column prop="adjAmount" label="Adj amount">
        <template slot-scope="scope">
          <el-input v-model="scope.row.adjAmount"></el-input>
        </template>
      </el-table-column>
      <el-table-column label="operation" width="120px">
        <template slot-scope="scope">
          <div class="option">
            <el-button @click="deleteItem(scope.row)" type="text" size="small">删除</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <div class="save-box">
      <el-button @click="save('Y')" type="primary">保存并標記已處理</el-button>
      <el-button @click="save('N')" type="primary">保存</el-button>
    </div>
    <el-dialog title="IpShare新增" :visible.sync="showLog" width="80%" :close-on-click-modal="false">
      <el-table :data="ipshareList" border stripe style="width: 100%">
        <el-table-column prop="rightType" label="Right">
        </el-table-column>
        <el-table-column prop="groupIndicator" label="Gp">
        </el-table-column>
        <el-table-column prop="name" label="name" width="160">
          <template slot-scope="scope">
            <span :title="scope.row.name">{{scope.row.name}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="chineseName" width="160" label="chineseName">
          <template slot="header" slot-scope="scope">
            <span title="chineseName">chineseName</span>
          </template>
        </el-table-column>
        <el-table-column prop="ipNameNo" label="IP Name No" width="160">
          <template slot-scope="scope">
            <span :title="scope.row.ipNameNo">{{scope.row.ipNameNo}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="workIpRole" label="role">
        </el-table-column>
        <el-table-column prop="workSocietyCode" label="soc">
        </el-table-column>
        <el-table-column prop="ipShare" label="Old IP Share">
        </el-table-column>
        <el-table-column label="operation" width="100px">
          <template slot-scope="scope">
            <i class="el-icon-check" @click="add(scope.row)" style="cursor: pointer"></i>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'ipshare-sd',
  data() {
    return {
      tableData: [],
      loading: false,
      config: {},
      dataInfo: {},
      showLog: false,
      ipshareList: []
    }
  },
  methods: {
    init() {
      this.config = JSON.parse(this.$route.query.data)
      this.getList()
    },
    getList() {
      this.$http.get('/dist/adj/getDistAdjSdsrHeaderDetail/' + this.config.id).then(res => {
        console.log(res)
        if (res.success) {
          this.tableData = res.data.data.distAdjSdsrDetailsList
          this.dataInfo = res.data.data.distAdjSdsrHeader
        }
      })
    },
    addIpShare() {
      this.showLog = true
      console.log(this.config)
      let params = {
        rightType: this.config.rightType,
        workUniqueKey: this.config.workUniqueKey,
        adjDistNo: this.config.distNo
      }
      this.$http.get('/dist/adj/getIpshareList', { params }).then(res => {
        console.log(res)
        if (res.success) {
          let list = res.data.data
          let arr = []
          if (list) {
            list.map(item => {
              item.workIpSociety = item.ipSocietyCode
              item.adjHeaderId = this.config.id
              item.distNo = this.config.distNo
              item.adjIpShare = ''
            })

            this.ipshareList = list
          } else {
            this.$message({
              message: '未搜索到匹配的數據',
              type: 'warning'
            })
          }
        }

      })
    },
    add(row) {
      console.log(row)
      let canPush = true
      console.log(row)
      if (this.tableData.length) {
        this.tableData.map(item => {
          if (item.ipNameNo === row.ipNameNo && item.rightType === row.rightType) {
            canPush = false
            this.$message({
              message: '已經添加過了',
              type: 'warning'
            })
          }

        })
      }
      if (canPush) {
        this.tableData.push(row)
        this.showLog = false
      }
    },
    deleteItem(row) {
      this.$alert('確定删除吗', 'ipshare删除', {
        showCancelButton: true,
        callback: action => {
          if (action === 'confirm') {
            this.$http.delete('/dist/adj/deleteDistAdjSdsrDetailsById/' + row.id).then(res => {
              if (res.data.code === 200) {
                this.$message({
                  message: '删除成功',
                  type: 'success'
                })
                this.getList()
              } else {
                this.$message({
                  message: '删除失敗',
                  type: 'error'
                })
              }
            })
          }
        }
      });
    },
    save(dist) {
      let canSave = true
      this.tableData.length && this.tableData.map(item => {
        console.log(item)
        if (item.adjIpShare == '') {
          this.$message({
            message: 'new ip share不能為空',
            type: 'warning'
          })
          canSave = false
          return;
        }
        if (item.ipShare == '') {
          this.$message({
            message: 'old ip share不能為空',
            type: 'warning'
          })
          canSave = false
          return;
        }
      })
      if (canSave) {
        let params = {
          adjHeaderId: this.config.id,
          dist: dist,
          distAdjSdsrDetailsList: this.tableData
        }
        this.$http.post('/dist/adj/sd/saveDistAdjSdsrDetailsList', params).then(res => {
          if (res.success) {
            let msg = ''
            if (dist == 'Y') {
              msg = '保存並標記成功'
            } else {
              msg = '保存成功'
            }
            this.$message({
              message: msg,
              type: 'success'
            })
          }
        })
      }
    }
  },
  mounted() {
    this.init()
  },
  beforeRouteEnter(from, to, next) {
    next(vm => {
      vm.init()
    })
  }
}
</script>

<style scoped lang="scss">
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .left {
    span {
      margin-right: 50px;
    }
  }
}
.save-box {
  padding: 20px 0;
  text-align: right;
}
</style>
