<template>
  <div class="worksbox">
    <el-form :inline="true" :model="formInline" class="demo-form-inline search-list" @keyup.enter.native="onSubmit(1)">
      <el-form-item prop="">
        <el-input v-model="formInline.title" placeholder="Title" style="width: 200px;"></el-input>
      </el-form-item>
      <el-form-item label="Roman:">
        <el-checkbox v-model="formInline.isRoman" ></el-checkbox>
      </el-form-item>
      <el-form-item>
        <el-input type="number" v-model.trim="formInline.episodeNo" placeholder="Episode" style="width: 96px;"></el-input>
      </el-form-item>
      <el-form-item>
        <el-input type="number" v-model.number.trim="formInline.workId" placeholder="Work No" style="width: 108px;"></el-input>
      </el-form-item>
      <el-form-item>
        <el-input type="number" v-model.number.trim="formInline.soc" placeholder="Soc" style="width: 46px;"></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model.trim="formInline.genre" placeholder="Genre" style="width: 56px;"></el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="formInline.workType" placeholder="Work Type" style="width: 68px;">
          <el-option label="全部" value=""></el-option>
          <el-option label="ORG" value="ORG"></el-option>
          <el-option label="ARR" value="ARR"></el-option>
          <el-option label="ADP" value="ADP"></el-option>
          <el-option label="AV" value="AV"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-input class="dbq" v-model.trim="formInline.ipBaseNo" placeholder="IpBaseNo" @dblclick.native="getIp()" style="width: 116px;"></el-input>
      </el-form-item>
      <el-form-item>
        <el-input class="dbq" v-model.trim="formInline.ipNameNo" placeholder="IpNameNo" @dblclick.native="getIp()" style="width: 116px;"></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model.trim="formInline.iswc" placeholder="Iswc" style="width: 116px;"></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="formInline.artistName" placeholder="Artist Name" style="width: 116px;"></el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="formInline.titleType" placeholder="Title Type" style="width: 100px;">
          <el-option label="Exclude TE" value="Exclude TE"></el-option>
          <el-option label="AT" value="AT"></el-option>
          <el-option label="CT" value="CT"></el-option>
          <el-option label="ET" value="ET"></el-option>
          <el-option label="EX" value="EX"></el-option>
          <el-option label="FT" value="FT"></el-option>
          <el-option label="IT" value="IT"></el-option>
          <el-option label="OT" value="OT"></el-option>
          <el-option label="PT" value="PT"></el-option>
          <el-option label="RT" value="RT"></el-option>
          <el-option label="TE" value="TE"></el-option>
          <el-option label="TT" value="TT"></el-option>
          <el-option label="VT" value="VT"></el-option>
          <el-option label="全部" value=""></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-input v-model="formInline.authorName" placeholder="Name" style="width: 116px;"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit(1)" v-if="isAuth('works:base:find')">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="addWorks" v-if="isAuth('works:base:add')">新增</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="success" @click="exportList" v-if="isAuth('works:base:exportout')">導出</el-button>
      </el-form-item>
      <el-form-item>
        <span class="clear-search" @click="clearSearch()" v-if="isAuth('works:base:find')">清除搜索</span>
      </el-form-item>
    </el-form>
    <el-row class="total">
      <el-col :span="24">
        共：{{totalWorks || 0}}筆，{{distinctTort || 0}}首
      </el-col>
    </el-row>
    <el-table 
      :data="tableData" 
      border 
      stripe 
      @row-click="handleRowClick" 
      @expand-change="handleRowClickByArrow" 
      ref="refTable" 
      class="tableList" 
      style="width: 100%" 
      v-loading="loading"
      :empty-text="emptyText">
      <el-table-column prop="" label="" width="32px">
        <template slot-scope="scope">
          {{ scope.row.transfer == 'Y' ? 'T' : '' }}
        </template>
      </el-table-column>
      <el-table-column type="expand">
        <template slot-scope="props">
          <el-row :gutter="20">
            <el-col :span="14">
              <div v-if="props.row.work_type=='AV'">
                <el-form :model="wrkWork" label-position="right" label-width="130px">
                  <el-col :span="11">
                    <el-form-item label="Prod Year">
                      <el-input v-model="wrkWork.productionYear" readonly></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="11">
                    <el-form-item label="Prod no">
                      <el-input v-model="wrkWork.productionNo" readonly></el-input>
                    </el-form-item>
                  </el-col>
                  <el-form-item label="Prod Company">
                    <el-input v-model="wrkWork.productCompany" readonly></el-input>
                  </el-form-item>
                  <el-form-item label="Director">
                    <el-input v-model="wrkWork.director" readonly></el-input>
                  </el-form-item>
                  <el-form-item label="Remark">
                    <el-input v-model="wrkWork.remark" type="textarea" :rows="4" readonly></el-input>
                  </el-form-item>
                </el-form>
              </div>
              <div v-else>
                <el-select v-model="props.row.right" placeholder="Type" @change="changeRightType" style="margin-bottom: 10px">
                  <el-option label="PERFORMING RIGHT" :value="'PER&'+props.row.work_id"></el-option>
                  <el-option label="MECHANICAL RIGHT" :value="'MEC&'+props.row.work_id"></el-option>
                  <el-option label="SYNCHRONIZATION RIGHT" :value="'ZYN&'+props.row.work_id"></el-option>
                  <el-option label="OD RIGHT" :value="'NOD&'+props.row.work_id"></el-option>
                  <el-option label="DB RIGHT" :value="'NDB&'+props.row.work_id"></el-option>
                </el-select>
                <el-table :empty-text="tableresult"   stripe :data="perList[props.row.work_id]" class="dropTable" height="224" border style="background: #fff">
                  <el-table-column prop="name" label="Name" min-width="250">
                  </el-table-column>
                  <el-table-column prop="chineseName" label="Chinese Name" min-width="150">
                  </el-table-column>
                  <el-table-column prop="workIpRole" label="RoleCode" width="100">
                  </el-table-column>
                  <el-table-column prop="ipSocietyCode" label="Soc" width="60">
                  </el-table-column>
                </el-table>
              </div>
            </el-col>
            <el-col :span="8">

              <el-card class="box-card">
                <div slot="header" class="clearfix">
                  <span>Performer</span>
                </div>
                <div v-if="performer[props.row.work_id] && performer[props.row.work_id].length" class="performer-list">
                  <div v-for="item in performer[props.row.work_id]" :key="item.id" class="card-item">
                    {{ item.chineseName ? item.chineseName.concat('(',item.artistName,')') : item.artistName }}
                  </div>
                </div>
                <div v-else>
                  暂無表演者
                </div>
              </el-card>
            </el-col>
          </el-row>
        </template>
      </el-table-column>
      <el-table-column prop="" label="Title" min-width="200px">
        <template slot-scope="scope">
          <span style="color:red" v-if="scope.row.distributable!=1">*</span>
          <span :title="scope.row.title||scope.row.title_en">{{scope.row.title||scope.row.title_en}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="" label="episode" width="120px">
        <template slot-scope="scope">
          <span v-if="scope.row.episode_no==0">
            -
          </span>
          <span v-else>
            {{scope.row.episode_no}}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="work_id" label="work no" width="150px">
      </el-table-column>
      <el-table-column prop="work_society_code" label="Soc" width="60px">
      </el-table-column>
      <el-table-column prop="work_type" label="work type" width="110px">
      </el-table-column>
      <el-table-column prop="genre_code" label="Genre" width="70px">
      </el-table-column>
      <el-table-column prop="language_code" label="Lang" width="70px">
      </el-table-column>
      <el-table-column prop="is_local" label="local" width="70px">
      </el-table-column>
      <el-table-column fixed="right" label="operation" width="180px">
        <template slot-scope="scope">
          <span style="width: 100%;text-align: center;display: inline-block">
            <span class="a-blue" @click="handleClick(scope.row,'look')" v-if="!isAuth('works:base:change')">查看</span>
            <el-button @click.stop="handleClick(scope.row)" type="text" size="small" v-if="isAuth('works:base:change')">編輯</el-button>
            <el-button @click.stop="relList(scope.row)" type="text" size="small" v-if="isAuth('works:base:relList')">relList</el-button>
            <!-- <span class="a-blue" @click.stop="handleClick(scope.row)">編輯</span> -->
          </span>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination background layout="prev, pager, next" :total=total @current-change="onSubmit" :current-page="currentPage">
    </el-pagination>
    <el-dialog title="請選擇新增作品類型" :visible.sync="worksDialogVisible" width="30%" class="workdialog" :close-on-click-modal="false" center>
      <div>
        <el-table :empty-text="tableresult"   :data="typeData" stripe style="width: 100%" class="typeTable">
          <el-table-column prop="name">
            <template slot-scope="scope">
              <span style="cursor: pointer" @click="selectType(scope.$index,scope.row)">{{scope.row.name}}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <span slot="footer" class="dialog-footer">
      </span>
    </el-dialog>
    <select-ip v-if="IpTableVisible" ref="selectIpCom" :search="ipSearch" @checkIp="checkIp"></select-ip>
  </div>
</template>

<script>
import axios from '../../utils/httpRequest'
import selectIp from '../../components/select-ip';
import { Loading } from 'element-ui'
export default {
  name: 'base',
  data() {
    return {
      loading: false,
      // checked: true,
      total: 1,
      currentPage: 1,
      totalWorks: 0,
      distinctTort: 0, //查詢列表的數據，去重後有多少
      formInline: {
        title: '',
        episodeNo: '',
        workId: '',
        soc: '',
        genre: '',
        workType: '',
        ipBaseNo: '',
        ipNameNo: '',
        dist: '',
        iswc: '',
        titleType: 'Exclude TE',
        authorName:'',
        isRoman:false,
        page: {
          pageNum: 1,
          pageSize: 10
        }
      },
      tableData: [
        // { pefid: '55157122', rpi: '55157122', name: 'ADVENTURER', firstname: '', chinesename: '', ipnn: '', band: '', country: '', ISWC: '' }
      ],
      tableresult:' ',
      targetRowId: [], //记錄已經展開的項
      wrkWork: {},
      perListTotal: {},
      perList: {},
      performer: {},
      isAV: false,
      // formShare: {
      //     rightType: 'PER',
      //     autoCalc: 'manual'
      // },
      worksDialogVisible: false,
      typeData: [
        { name: 'Original', type: 'ORG' },
        { name: 'Arranged', type: 'ARG' },
        { name: 'Adapted', type: 'ADP' },
        { name: 'Medley', type: 'ME' },
        { name: 'Suite', type: 'SU' },
        { name: 'TV', type: 'TV' },
        { name: 'AV', type: 'AV' }
      ],
      isRowClick: false,

      // 查詢ip
      IpTableVisible: false,
      ipSearch: {},

      exportShow: false,
      emptyText: '數據加載中',
    }
  },
  components: {
    selectIp
  },
  methods: {
    clearSearch() {
      this.formInline = {
        title: '',
        episodeNo: '',
        workId: '',
        soc: '',
        genre: '',
        workType: '',
        ipBaseNo: '',
        ipNameNo: '',
        dist: '',
        iswc: '',
        authorName:'',
        isRoman: false,
        page: {
          pageNum: 1,
          pageSize: 10
        }
      }
      // this.onSubmit();
    },
    /**
    * 选取IP
    */
    getIp() {
      this.IpTableVisible = true;
      this.ipSearch = {
        name_no: this.formInline.ipNameNo ? this.formInline.ipNameNo : '',
        ip_no: this.formInline.ipBaseNo ? this.formInline.ipBaseNo : '',
        name: ''
      }
      this.$nextTick(() => {
        this.$refs.selectIpCom.init();
      })
    },
    checkIp(info) {
      this.$set(this.formInline, 'ipNameNo', info.ip_name_no);
      this.$set(this.formInline, 'ipBaseNo', info.ip_base_no);
    },
    addWorks() {
      this.worksDialogVisible = true
    },
    selectType(index, item) {
      this.worksDialogVisible = false
      if (index === 0) {
        this.$router.push({ name: 'works-originalinfo' })
      }
      if (index == 1) {
        this.$router.push({ name: 'works-arrangedinfo', query: { type: this.typeData[index].type } })
      }
      if (index == 2) {
        this.$router.push({ name: 'works-adaptedinfo', query: { type: this.typeData[index].type } })
      }
      if (index === 3) {
        this.$router.push({ name: 'works-medleyinfo', query: { type: this.typeData[index].type } })
      }
      if (index == 4) {
        this.$router.push({ name: 'works-suiteinfo', query: { type: this.typeData[index].type } })
      }
      if (index == 6) {
        this.$router.push({ name: 'works-avinfo', query: { type: this.typeData[index].type } })
      }
      if (index === 5) {
        this.$router.push({ name: 'works-tvinfo' });
      }
    },
    onSubmit(page) {

      let params = this.$utils.copy(this.formInline);
      console.log('1: ', this.formInline);
      console.log('2: ', this.$utils.copy(this.formInline));
      this.$utils.trim(params);

      console.log('iswc: ', params);
      params.page.pageNum = page ? page : 1;
      params.iswc = params.iswc.replace(/-/g, '').replace(/\./g, '')
      // this.loading = true;
      this.emptyText = '數據加載中';
      axios.post('/wrk/queryWrkWorkListEs', params).then(res => {
        console.log('======-=',res.data.list)
        // this.loading = false;
        if (res.status === 200) {
          this.targetRowId=[]
          this.tableData = res.data.list ? res.data.list : [];
          if(! this.tasksData || this.tasksData.length == 0){
            this.emptyText = '暫無數據';
          }
          this.tableData.length && this.tableData.forEach(item => {
            this.$set(item, 'right', 'PER&' + item.work_id)
          })
          this.total = (res.data.total && res.data.total > 10000) ? 10000 : res.data.total;
          if (!res.data.total) {
            this.total = 0;
          }
          console.warn('=======',this.tableData)
          this.totalWorks = res.data.total;
          this.currentPage = params.page.pageNum;
          //distFlag 1後台返回1代表可匹配    後台返回0代表不可匹配
          this.distinctTort = res.data.distinctTort;
        }
            this.tableresult = this.tableData.length == 0 ? '暫無數據' : ' '
      })
    },
    // 切換typeRight
    changeRightType() {
      let val = [...arguments][0].split('&')
      let type = val[0]
      let id = val[1]

      this.perList[id] = this.perListTotal[id][type]
    },
    handleClick(item,type) {
      let routeName = '';
      if (item.work_type === 'ARR') {
        routeName = 'works-baseinfoarr2';
      } else if (item.work_type === 'ORG') {
        routeName = 'works-baseinfo2';
      } else if (item.work_type === 'ADP') {
        routeName = 'works-baseinfoadp2';
      } else if (item.work_type === 'AV' && item.genre_code != 'TVS') {
        routeName = 'works-baseinfoav2';
      } else if ((item.work_type === 'AV' && item.genre_code === 'TVS') || item.work_type == 'TV') {
        routeName = 'works-baseinfotv2';
      } else if (item.work_type == 'ME') {
        routeName = 'works-medleyinfo'
      }
      this.$router.push({ name: routeName, query: { id: item.work_id, socId: item.work_society_code, title: item.title ? item.title : item.title_en, nameId: item.id + item.work_id,type:type }, params: { id: item.work_id } })
    },
    relList(item) {
      console.log(item)
      this.$router.push({ name: 'rel-list', query: { workNo: item.work_id, soc: item.work_society_code, title: item.title ? item.title : item.title_en, titleType: item.title_type, nameId: item.id + item.work_id } })
    },
    handleCurrentChange(val) {
      this.formInline.page.pageNum = val
      let params = this.formInline
      this.loading = true;
      this.tableresult = '數據加載中...'
      axios.post('/wrk/queryWrkWorkListEs', params).then(res => {
        this.loading = false;
        if (res.status === 200) {
          this.tableData = res.data.list;
          this.total = res.data.total > 10000 ? 10000 : res.data.total;
          this.totalWorks = res.data.total;
          this.currentPage = val;
        }
          this.tableresult = this.tableData.length == 0 ? '暫無數據' : ' '
      })
    },
    handleRowClickByArrow() {
      console.log('zhankai')
      let [row] = [...arguments]
      let index = this.targetRowId.indexOf(row.id)
      if (index != -1) { //已展開
        this.targetRowId.splice(index, 1)
      } else { //未展開
        this.targetRowId.push(row.id)
        // this.formShare = {
        //     rightType: 'PER',
        //     autoCalc: 'manual'
        // }
        let workType = row.work_type
        let workId = row.work_id
        let params = {
          // id: row.id,
          workId: workId,
          workSocietyCode: row.work_society_code
        }
        this.loading = true;
        axios.get('/wrk/getWrkWorkById', { params }).then(res => {
          this.loading = false;
          if (res.status === 200) {
            // this.performer = res.data.waList
            this.performer[workId] = res.data.waList ;
            console.log('this.performer',this.performer)
         
            if (workType === 'AV') {
              this.isAV = true
              this.wrkWork = res.data.wrkWork || {};
              let remarks = res.data.wwrList || [];
              let remark = '';
              if (remarks.length) {
                remark = remarks[remarks.length - 1].remark;
              }
              this.wrkWork.remark = remark;
            } else {
              this.isAV = false;
              this.perListTotal[workId] = res.data.wwisList[0];
              for (let key in this.perListTotal[workId]) {
                this.perListTotal[workId][key].forEach((item) => {
                  if (item.sd === 'Y') {
                    item.sdcheck = true
                  }
                  if (item.sd === 'N') {
                    item.sdcheck = false
                  }
                  if (!item.ipNameNo) {
                    item.name = item.dummyNameRoman;
                    item.chineseName = item.dummyName;
                  }
                })
              }
              this.$set(this.perList, workId, this.perListTotal[workId].PER)
              // this.perList[workId] = this.perListTotal[workId].PER;  
            }
          }
        })
      }
    },
    handleRowClick(row) {
      let index = this.targetRowId.indexOf(row.id)
      if (index != -1) { //已展開
        this.$refs.refTable.toggleRowExpansion(row, false)
      } else { //未展開
        this.$refs.refTable.toggleRowExpansion(row, true)
      }
    },
    exportList() {
      console.log('formline: ', this.formInline);
      let ajaxData = this.$utils.copy(this.formInline);
      delete ajaxData.page;
      // for(let key in ajaxData){
      //     ajaxData[key] = encodeURIComponent(ajaxData[key]);
      // }
      if (ajaxData.title) {
        ajaxData.title = encodeURIComponent(ajaxData.title);
      }
      if (ajaxData.artistName) {
        ajaxData.artistName = encodeURIComponent(ajaxData.artistName);
      }

      let str = this.$utils.parseParams(ajaxData);

      let flag = false;
      for (let key in ajaxData) {
        if (ajaxData[key]) {
          flag = true;
        }
      }
      console.log(ajaxData)
      if (flag) {
        // window.open('api/wrk/exportWrkTitleList?' + str);
        this.$http.get('/wrk/exportWrkTitleListCount', { params: ajaxData }).then(res => {
          if (res.success && res.data.code == '200') {
              if(res.data.data<500){
                this.$utils.downloadGet('/wrk/exportWrkTitleList', ajaxData);
              }else{
                this.$http.get('/wrk/exportWrkTitleListAsync', { params: ajaxData }).then(res => {
                  if (res.success && res.data.code == '200') {
                    this.$alert(res.data.data, '提示');
                  }else{
                    this.$toast({tips: res.data.message})
                  }
                })
              }
          }else{
              this.$toast({tips: res.data.message})
          }
          console.log(res)
        })
      } else {
        this.$toast({ tips: '請至少選擇一個篩選項' })
      }
    }
  },
  created() {
    if (this.$route.query.ipBaseNo) {
      this.formInline.ipBaseNo = this.$route.query.ipBaseNo;
    }
    this.onSubmit();
  },
  activated() {
    this.$nextTick(() => {
      if (this.$route.query.ipBaseNo) {
        // this.formInline.ipBaseNo = this.$route.query.ipBaseNo;
        this.formInline = {
          title: '',
          episodeNo: '',
          workId: '',
          soc: '',
          genre: '',
          workType: '',
          ipBaseNo: this.$route.query.ipBaseNo,
          ipNameNo: '',
          dist: '',
          iswc: '',
          titleType: '',
          authorName:'',
          page: {
            pageNum: 1,
            pageSize: 10
          }
        },
          this.onSubmit()
      }
      if (this.$route.query.update) {
        let query = this.$route.query;
        delete query.update;
        this.onSubmit(this.currentPage);
      }

    })
  }
}
</script>

<style lang="scss">
.tableList {
  .el-form-item__label {
    margin-left: 0;
  }
  .performer-list {
    height: 175px;
    line-height: 38px;
    overflow-y: auto;
    /*-ms-overflow-style: none;*/
    /*overflow: -moz-scrollbars-none;*/
    /*&::-webkit-scrollbar { width: 0 !important }*/
  }
  .el-table__row {
    cursor: pointer;
    &.expanded + tr {
      background: #fff;
    }
  }
}
.dropTable {
  .el-table__row {
    background: #fff;
    cursor: default;
  }
}
</style>
<style lang="scss" scoped>
.total {
  margin-bottom: 10px;
  margin-top: -10px;
}
.search-list > .el-form-item {
  margin-right: 6px;
  /deep/ input {
    padding: 0 4px;
  }
}
/deep/ .workdialog .cell {
  text-align: center;
}
/deep/ .cell {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: break-all;
}
</style>
