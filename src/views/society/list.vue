<template>
    <div class="ipibpox">
        <el-form :inline="true" :model="search" ref="form" class="demo-form-inline" @keyup.enter.native="searchFn(1)">
            <el-form-item prop="Society Code">
                <el-input v-model.trim="search.society_code" placeholder="Society Code"></el-input>
            </el-form-item>
            <el-form-item prop="name">
                <el-input v-model.trim="search.societyName" placeholder="Name"></el-input>
            </el-form-item>
            <el-form-item prop="country">
                <el-input v-model.trim="search.country_code" placeholder="Country"></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="searchFn()" v-if="isAuth('society:list:find')">搜索</el-button>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="addFn" v-if="isAuth('society:list:add')">新增</el-button>
            </el-form-item>
            <el-form-item>
                <span class="clear-search" @click="clearSearch()" v-if="isAuth('society:list:find')">清除搜索</span>
            </el-form-item>
        </el-form>
        <el-table :empty-text="emptyText" stripe :data="tableData" border style="width: 100%">
            <el-table-column
                prop="societyCode"
                label="Society Code"
                width="130">
            </el-table-column>
            <el-table-column
                prop="societyName"
                label="Name"
                width="180">
            </el-table-column>
            <el-table-column
                prop="societyFullName"
                label="Full Name">
            </el-table-column>
            <el-table-column
                prop="countryCode"
                label="Country"
                width="120">
            </el-table-column>
            <el-table-column
                prop="societyCurrencyCode"
                label="Currency Code"
                width="150">
            </el-table-column>
            <el-table-column
                prop="societyPaymentMethod"
                label="Payment Method"
                width="180">
                <template slot-scope="scope">{{ scope.row.societyPaymentMethod | PaymentMethod }}</template>
            </el-table-column>
            <el-table-column
                fixed="right"
                label="operation"
                width="100">
                <template slot-scope="scope">
                    <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                    <el-button @click="handleClick(scope.row)" type="text" size="small" v-if="isAuth('society:list:change')">編輯</el-button>
                    </span>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :total="total" @current-change="handleCurrentChange" :current-page="currentPage">
        </el-pagination>
    </div>
</template>

<script>
    export default {
        name: 'societyList',
        data () {
            return {
                tableData: [],
                emptyText:'暫無數據',
                total: 0,
                currentPage: 1,
                search: {
                    society_code: '',
                    societyName: '',
                    country: ''
                },
                show: true
            }
        },
        created(){
            this.init();
        },
        watch:{

        },
        activated(){
            this.$nextTick( () => {
                if(this.$route.query.update){
                    this.init();
                }
            })
            
        },
        filters: {
            PaymentMethod: function(method){
                let config = {
                    c: 'cable transfer',
                    d: 'demand draft',
                    p: 'pending'
                }
                return config[method]
            }
        },
        methods: {
            clearSearch(){
                this.search = {
                    society_code: '',
                    societyName: '',
                    country: ''
                };
                this.searchFn(1);
            },
            init(){
                this.searchFn(1);
            },
            searchFn (page) {
                if(page == 1 || !page){
                    this.show = false;
                    this.$nextTick( () => {
                        this.show = true;
                    })
                }
                // this.loading = true;
                let ajaxData ={};
                ajaxData.data =  this.$utils.copy(this.search);
                ajaxData.page= {
                    pageNum: page ? page : 1,
                    pageSize: 10
                }
                // ajaxData.data = {};
                this.emptyText = '數據加載中';
                this.$http.post('ref/society/getSocietyList', ajaxData).then(res => {
                    if (res.success) {
                        this.tableData = res.data.list;
                        this.total = res.data.total||1;
                        this.currentPage = page ? page : 1;
                        if(! this.tableData || this.tableData.length == 0){
                            this.emptyText = '暫無數據';
                        }
                    }
                    // this.loading = false;
                })
            },
            handleCurrentChange (val) {
                this.searchFn(val);
            },
            handleClick (item) {
                console.log(item);
                this.$router.push({name: 'societyEdit', query: {societyCode: item.societyCode, nameId: item.societyCode, title: item.societyName}})
            },
            addFn(){
                this.$router.push({name: 'societyAdd'})
            }
        }
    }
</script>

<style>
</style>
