<template>
  <div>
    <el-dialog
      :visible.sync="autopayShow"
      width="700px"
      title="Autopay No"
      :close-on-click-modal="false"
      :before-close='close'
    >
      <div style="margin: auto;margin-bottom: 20px; text-align: center;">
        <el-input v-model="search.autopayNo" @keyup.enter.native="autopayDataD()" placeholder="Autopay No" style="width: 200px;"></el-input>
        <el-button type="primary" @click="autopayDataD()">查詢</el-button>
      </div>
      <el-table
        ref="multipleTable"
        :data="tableData"
        stripe
        :empty-text="emptyText"
      >
        <el-table-column
          property="autopayNo"
          label="Autopay No"
        ></el-table-column>
        <el-table-column
          property="autopayDescription"
          label="autopay description"
        ></el-table-column>
        <el-table-column label="operation" width="100">
          <template slot-scope="scope">
            <span
              style="
                width: 100%;
                display: inline-block;
                text-align: center;
                cursor: pointer;
              "
            >
              <i class="el-icon-check" @click="checkedAutopay(scope.row)"></i>
            </span>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        layout="prev, pager, next"
        :total="total"
        @current-change="autopayDataD"
        :current-page="currentPage"
      >
      </el-pagination>
    </el-dialog>
  </div>
</template>
<script>
import { Loading } from "element-ui";
export default {
  name: "",
  data() {
    return {
      autopayShow: false,
      tableData: [],
      emptyText: "暫無數據",
      total: 1,
      currentPage: 1,
      loading:null,
      search:{
        autopayNo:''
      }
    };
  },
  props: {
    autopayNo: {
      type: String,
      default: "",
    },
  },
  watch:{
  },
  methods: {
    close(){
      this.autopayShow = false;
      this.search={
        autopayNo:''
      }
      this.$emit("flag");
    },
    checkedAutopay(data) {
      this.$emit("checkAutopay", data);
      this.autopayShow = false;
      this.search={
        autopayNo:''
      }
    },
    autopayDataC(page) {
      this.loading = Loading.service({
        lock: true,
        text: "Loading",
        background: "rgba(0,0,0,0.3)",
      });
      let params = {
        autopayNo: this.autopayNo,
        pageNum: page ? page : 1,
        pageSize: 10,
      };
      this.emptyText = "數據加載中";
      this.$http
        .get("/dist/autoPay/Report/autoPayNos", { params })
        .then((res) => {
          this.loading.close();
          if (res.success && res.data.code == 200) {
            this.tableData = res.data.data.list;
            if (!this.tableData || this.tableData.length == 0) {
              this.emptyText = "暫無數據";
            }
            this.total = res.data.data.total;
            this.currentPage = page ? page : 1;
            if (this.tableData.length == 0) {
              this.$emit("checkAutopay", {
                autopayNo: "",
                autopayDescription: "",
              });
              this.$toast({ tips: "未查詢到Autopay No" });
              return;
            } else {
              this.$emit("checkAutopay", this.tableData[0]);
            }
            
          } else {
            this.$toast({ tips: res.data.message });
            return;
          }
        });
    },
    autopayDataD(page) {
      this.loading = Loading.service({
        lock: true,
        text: "Loading",
        background: "rgba(0,0,0,0.3)",
      });
      let params = {
        autopayNo: this.search.autopayNo,
        pageNum: page ? page : 1,
        pageSize: 10,
      };
      this.emptyText = "數據加載中";
      this.$http
        .get("/dist/autoPay/Report/autoPayNos", { params })
        .then((res) => {
          this.loading.close();
          if (res.success && res.data.code == 200) {
            this.tableData = res.data.data.list;
            if (!this.tableData || this.tableData.length == 0) {
              this.emptyText = "暫無數據";
            }
            this.total = res.data.data.total;
            this.currentPage = page ? page : 1;
            this.autopayShow = true;
          } else {
            this.$toast({ tips: res.data.message });
            return;
          }
        });
    },
  },
};
</script>
<style scoped>
</style>