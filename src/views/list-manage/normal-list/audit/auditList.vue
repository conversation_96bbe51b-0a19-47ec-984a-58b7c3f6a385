<template>
  <!-- 拆分後的文件 用于審核用的 -->
  <div class="ipibpox">
    <el-form :inline="true" :model="search" ref="form" class="demo-form-inline" :rules="rules"  @keyup.enter.native='searchFn()'>
      <el-form-item prop="fileBaseId" :validate-event="false">
        <span class="red">*</span>
        <el-input v-model.trim="search.fileBaseId" placeholder="RuleId" style="width: 80px;"></el-input>
      </el-form-item>
      <!-- todo ? 上传時間筛选应该是上传清單列表 -->
      <!--            <el-form-item prop="Upload Time">-->
      <!--                <date-picker v-model="search.uploadTime" type="date" placeholder="Upload Time" style="width: 120px;"></date-picker>-->
      <!--            </el-form-item>-->

      <el-form-item>
        <el-input v-model.trim="search.title" placeholder="標題" style="width: 120px;"></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model.trim="search.uniqueKeyMd5" placeholder="Data unique key" style="width: 120px;"></el-input>
      </el-form-item>
      <el-form-item>
          <el-input v-model.trim="search.uploadType" placeholder="Upload Type" style="width: 120px;"></el-input>
      </el-form-item>
      <el-form-item>
          <el-input v-model.trim="search.batchId" placeholder="Batch Id" style="width: 120px;"></el-input>
      </el-form-item>
      <el-form-item>
          <el-input v-model.trim="search.batchSeq" placeholder="Batch Seq" style="width: 120px;"></el-input>
      </el-form-item>
      <el-form-item>
          <el-input v-model.trim="search.year" placeholder="Year" style="width: 120px;"></el-input>
      </el-form-item>
      <el-form-item>
          <el-input v-model.trim="search.categoryCode" placeholder="Category" style="width: 120px;"></el-input>
      </el-form-item>
      <el-form-item>
          <el-input v-model.trim="search.searchText" placeholder="SearchText" style="width: 120px;"></el-input>
      </el-form-item>

      <!-- <el-form-item>
        <el-button :type="search.matchScore < 0 ? 'primary' : ''" @click="changeSearchScore(-1)">10分以下</el-button>
        <el-button :type="search.matchScore === 0 ? 'primary' : ''" @click="changeSearchScore(0)">10-20分</el-button>
        <el-button :type="search.matchScore > 0 ? 'primary' : ''" @click="changeSearchScore(1)">20分以上</el-button>
      </el-form-item> -->
      <el-form-item>
        <el-select v-model="search.matchScore" placeholder="分數選擇" style="width: 116px;">
          <el-option v-for=" (item,index) in config.score" :key="index" :value="item.value" :label="item.label"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="search.status" placeholder="所有狀態" style="width: 120px;">
          <el-option v-for=" (item, index) in config.status" :key="index" :value="item.value" :label="item.label"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="批次號:" prop="batchIdA">
        <el-input style="width: 90px;border-color:none" v-model.number="search.batchIdA" placeholder="Begin"></el-input>
      </el-form-item>
      <span style="line-height: 2.15;margin-left: -10px;">~</span>
      <el-form-item prop="batchIdB">
        <el-input style="width: 90px;border-color:none" v-model.number="search.batchIdB" placeholder="End"></el-input>
      </el-form-item>
      <el-form-item>
          <date-picker type="date" v-model="search.startDate" placeholder="開始日期" style="width: 160px;" value-format="yyyy-MM-dd" format="yyyyMMdd"></date-picker>
          -
          <date-picker type="date" v-model="search.endDate" placeholder="結束日期" value-format="yyyy-MM-dd" format="yyyyMMdd" style="width: 160px;"></date-picker>
      </el-form-item>
      <!-- todo  -->
      <!-- <el-form-item>
                <el-input v-model="search.amendUser" placeholder="Amend User" style="width: 120px;"></el-input>
            </el-form-item> -->

      <el-form-item>
        <el-button type="primary" @click="searchFn()" v-if="isAuth('list-manage:normal-list:audit:auditList:find')">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="success" @click="auditBatch(1)" v-if="isAuth('list-manage:normal-list:audit:auditList:pass')">批量通過</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="danger" @click="auditBatch(2)" v-if="isAuth('list-manage:normal-list:audit:auditList:refuse')">批量拒絕</el-button>
      </el-form-item>
      <el-form-item>
        <el-upload class="upload-demo" ref="upload" action="" accept=".xls,.xlsx" :auto-upload="false" :on-change="uploadChange" :on-success="uploadSuccess" :show-file-list="false" :file-list="fileList">
          <el-button size="small" type="primary" style="background-color: #b240ff;border-color: #b240ff;" v-if="isAuth('list-manage:normal-list:audit:auditList:exportin')">審核結果導入</el-button>
        </el-upload>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="exportFn" v-if="isAuth('list-manage:normal-list:audit:auditList:exportout')">審核結果導出</el-button>
      </el-form-item>
      <el-form-item>
        <span class="clear-search" @click="clearSearch()" v-if="isAuth('list-manage:normal-list:audit:auditList:find')">清除搜索</span>
      </el-form-item>
      <el-form-item>
          <el-button type="primary" @click="showUnrecognizedDataDialog">無法辨識資料處理</el-button>
      </el-form-item>
    </el-form>
    <el-table ref="table" :empty-text="emptyText" stripe :data="tableData" border style="width: 100%" @selection-change="selectionChangeHandle">
      <el-table-column type="selection">

      </el-table-column>
      <el-table-column prop="fileBaseId" label="Rule Id" width="80px">
      </el-table-column>
      <!-- <el-table-column
                prop="uploadType"
                label="List Type">
            </el-table-column> -->
      <!-- <el-table-column
                prop="sourceCode"
                label="Source"> -->
      <!-- </el-table-column> -->
      <!-- todo -->
      <!-- <el-table-column
                prop="categoryCode"
                label="Category">
            </el-table-column> -->
      <el-table-column prop="title" label="Title" min-width="280px">
      </el-table-column>
      <el-table-column prop="artists" label="Aritists">
      </el-table-column>
      <el-table-column prop="authors" label="Author" min-width="100">
        <template slot-scope="scope">
          {{scope.row.authors | addSpace}}
        </template>
      </el-table-column>
      <el-table-column prop="composers" label="Composer">
        <template slot-scope="scope">
          {{scope.row.composers | addSpace}}
        </template>
      </el-table-column>
      <el-table-column label="Duration" width="120px">
        <template slot-scope="scope">
              <span v-if="scope.row.durationM || scope.row.durationS">
                {{ String(scope.row.durationM).padStart(1, '0') + ':' + String(scope.row.durationS).padStart(2, '0') }}
              </span>
        </template>
      </el-table-column>
      <el-table-column prop="matchWorkTitle" label="Match Work Title" min-width="120px">
      </el-table-column>
      <el-table-column prop="performTime" label="Perform Time" format="yyyy-MM-dd" min-width="120px">
          <template slot-scope="scope">
              {{ scope.row.performTime | formatDate }}
          </template>
      </el-table-column>
      <el-table-column label="Soc-WorkId" min-width="120">
        <template slot-scope="scope">
          {{(scope.row.matchWorkSocietyCode ? scope.row.matchWorkSocietyCode : '') + (scope.row.matchWorkId ? '-'+scope.row.matchWorkId : '')}}
        </template>
      </el-table-column>
      <el-table-column prop="clickNumber" label="clickNumber" width="">
      </el-table-column>
      <el-table-column prop="matchScore" label="Score" width="70px">
      </el-table-column>
      <el-table-column prop="uniqueKeyMd5" label="Data unique key">
        <template slot-scope="scope">
          <el-tooltip class="item" effect="dark" :content="scope.row.uniqueKeyMd5" placement="top">
            <div class="oneLine">{{scope.row.uniqueKeyMd5}}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="uploadType" label="Upload Type" width="60px">
      </el-table-column>

      <el-table-column prop="matchScore" label="Status" width="80px">
        <template slot-scope="scope">
          {{scope.row.status | Status}}
        </template>
      </el-table-column>
      <el-table-column prop="autoMatch" label="Auto Match" width="80px">
          <template slot-scope="scope">
            {{ scope.row.autoMatch | AutoMatch }}
          </template>
      </el-table-column>
      <el-table-column fixed="right" label="OP" width="60px">
        <template slot-scope="scope">
          <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
            <span class="a-blue" @click="audit(scope.row,scope.$index)" v-if="isAuth('list-manage:normal-list:audit:auditList:audit')">審核</span>
          </span>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination background layout="prev, pager, next" :total="total" @current-change="handleCurrentChange">
    </el-pagination>

    <!-- 無法辨識資料處理弹窗 -->
    <el-dialog
      title="無法辨識資料處理"
      :visible.sync="unrecognizedDataDialogVisible"
      width="800px"
      :close-on-click-modal="false"
      @closed="handleUnrecognizedDataDialogClosed">
      <el-form :model="unrecognizedDataForm" ref="unrecognizedDataForm" :rules="unrecognizedDataRules" label-width="140px">
        <el-form-item label="FID" prop="fileBaseId">
          <el-input
            v-model="fidInputText"
            placeholder="請輸入FID，多個FID可用逗號、空格、回車或換行分隔"
            style="width: 100%"
            @blur="handleFidInputChange"
            @keyup.enter.native="handleFidInputChange">
            <template slot="append">
              <el-button icon="el-icon-check" @click="handleFidInputChange">確認</el-button>
            </template>
          </el-input>
          <div v-if="selectedFidList.length > 0" class="selected-fids">
            <el-tag
              v-for="fid in selectedFidList"
              :key="fid.id"
              closable
              @close="removeFid(fid)"
              style="margin-right: 5px; margin-top: 5px;">
              {{ fid.id }}
            </el-tag>
          </div>
        </el-form-item>
        <el-form-item label="篩選條件" prop="filterConditions">
          <el-select
            v-model="unrecognizedDataForm.filterConditions"
            multiple
            placeholder="請選擇篩選條件（可選）"
            style="width: 100%"
            @change="handleFilterConditionsChange">
            <el-option key="matchId" label="去重條件：相同MATCHID" value='matchId'></el-option>
            <el-option key="titleArtistCname" label="去重條件：UP TITLE/UP ARTIST/UP C NAME" value='titleArtistCname'></el-option>
            <el-option key="titleArtistCnameDuration" label="去重條件：UP TITLE/UP ARTIST/UP C NAME/UP DUR MIN/UP DUR SEC" value='titleArtistCnameDuration'></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeUnrecognizedDataDialog">取消</el-button>
        <el-button type="primary" @click="exportUnrecognizedData">導出為Excel</el-button>
      </span>
    </el-dialog>


  </div>
</template>

<script>
import { customDownload } from './custom-download.js';
import th from "element-ui/src/locale/lang/th";

export default {
  name: 'sampleDateList',
  data() {
    return {
      tableData: [],
      searchparam: {},
      total: 0,
      // 无法辨识资料处理弹窗相关数据
      unrecognizedDataDialogVisible: false,
      unrecognizedDataForm: {
        fileBaseId: '',
        filterConditions: []
      },
      unrecognizedDataRules: {
        fileBaseId: [
          { required: true, message: '請選擇FID', trigger: 'change' }
        ]
      },

      // 已选择的FID列表
      selectedFidList: [],
      // 用于显示的FID文本
      displayFidText: '',
      // 手动输入的FID文本
      fidInputText: '',
      rules: {
          fileBaseId: [
              // 移除 required: true
              {
                  validator: (rule, value, callback) => {
                      if (value === '' || value === null || value === undefined) {
                          callback(); // 允许为空
                      } else {
                          const ids = value.split(',').map(id => id.trim());
                          const invalid = ids.some(id => id === '' || !/^\d+$/.test(id));

                          if (invalid) {
                              callback(new Error('无效分隔符！'));
                          } else {
                              callback();
                          }
                      }
                  },
                  trigger: 'blur'
              }
          ],
        batchIdA: [{ pattern: /^\+?[1-9]\d*$/, message: '必須>0' }],
        batchIdB: [{ pattern: /^\+?[1-9]\d*$/, message: '必須>0' }]
      },
      search: {
        id: '',
        matchScore: '',
        batchIdA: '',
        batchIdB: '',
        categoryCode: '',
        uploadTime: '',
        sourceCode: '',
        status: "",
        uploadType: '',
        startTime: '',
        endTime: '',
        batchSeq: '',
        batchId: '',
        year: '',
        searchText : ''
      },
      currentPage: 1,
      config: {
        status: [
          {
            value: '',
            label: '全部狀態'
          },
          {
            value: '0',
            label: '待審核'
          },
          {
            value: '1',
            label: '已匹配'
          },
          {
            value: '2',
            label: '不匹配'
          }
        ],
        score: [{
          value: '',
          label: '全部評分'
        },
        {
          value: -1,
          label: '10分以下'
        },
        {
          value: 0,
          label: '10-20分'
        },
        {
          value: 1,
          label: '20分以上'
        },
        ],
        uploadType: ['PG', 'FW', 'MS', 'CJ']
      },
      dataListSelections: [],
      //导入报告列表
      fileList: [],
      emptyText:'暫無數據',
      // 所有选中的行（跨页）
      allSelectedItems: [],
      // 用于去重的 key 集合（如 uniqueKeyMd5）
      selectedRowKeys: new Set()
    }
  },
  filters: {
    Status: function (status) {
      let config = {
        0: '待審核',
        1: '已匹配',
        2: '不匹配'
      }
      return config[status]
    },
    AutoMatch: function (status) {
        let config = {
            0: '',
            1: 'Y',
            2: 'MM'
        }
        return config[status]
    }
    ,
    formatDate: function (value) {
        if (!value) return '';
        const date = new Date(value);
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        return `${year}-${month}-${day}`;
      }
  },
  activated() {
    this.$nextTick(() => {
      if (this.$route.query.update) {
        let query = this.$route.query;
        delete query.update;
        this.searchFn(this.currentPage)
      }
    })
  },

  methods: {
    clearSearch() {
      this.search = {
        id: '',
        matchScore: '',
        categoryCode: '',
        batchIdA: '',
        batchIdB: '',
        uploadTime: '',
        sourceCode: '',
        status: '',
        uploadType: ''
      }
    },
    init() {
      this.searchFn(1);
    },
    handleSelect(item) {
      console.log(item);
    },
    searchFn(page = 1) {
      // 新增验证逻辑：如果 searchText 有值，batchId 必须填写
      if (this.search.searchText && !this.search.batchId) {
          this.$toast({ tips: 'Batch ID 不能为空' });
          return;
      }
      // if (isNaN(this.search.fileBaseId)) {
      //   this.$toast({ tips: '請輸入正確的fid' })
      //   return
      // }
      this.$refs.form.validate(validate => {
        if (validate) {
          let ajaxData = {};
          this.currentPage = page
          this.search.pageNum = page
          this.search.pageSize = 10
          if (page == 1) {
            this.total = 0
          }
          ajaxData = this.$utils.copy(this.search);
          // 处理多个 FID 的情况
          if (ajaxData.fileBaseId) {
              const fidList = ajaxData.fileBaseId
                  .split(',') // 按逗号分割
                  .map(id => id.trim())
                  .filter(id => id !== '');

              if (fidList.length > 0) {
                  // 如果后端支持数组参数，可直接传入
                  // 例如：fileBaseIdList = fidList
                  ajaxData.fileBaseIdList = fidList;
                  delete ajaxData.fileBaseId; // 删除原字段
              }
          }
          if (ajaxData.matchScore == 99) {
            delete ajaxData.matchScore;
          }
          this.searchparam = ajaxData
          this.emptyText = '數據加載中';
          this.$http.post('/list2match/getListMatchList', ajaxData).then(res => {
            if (res.data.code == 200) {
              this.tableData = res.data.data?res.data.data.list:[];
              // console.warn("((((((",this.tableData)
              this.total = res.data.data?res.data.data.total:0
            }
              // 恢复当前页中已选中的行
              this.$nextTick(() => {
                  if (this.$refs.table && this.$refs.table.toggleRowSelection) {
                      this.tableData.forEach(row => {
                          if (this.selectedRowKeys.has(row.uniqueKeyMd5)) {
                              this.$refs.table.toggleRowSelection(row, true);
                          }
                      });
                  }
              });
            if(! this.tableData || this.tableData.length == 0){
              this.emptyText = '暫無數據';
            }
          }).catch(res => {

          })
        }
      })

    },
    searchFnfirst(page = 1) {

      let ajaxData = {};
      this.currentPage = page
      this.search.pageNum = page
      this.search.pageSize = 10
      if (page == 1) {
        this.total = 0
      }
      ajaxData = this.$utils.copy(this.search);
      if (ajaxData.matchScore == 99) {
        delete ajaxData.matchScore;
      }
      this.searchparam = ajaxData
      this.emptyText = '數據加載中';
      this.$http.post('/list2match/getListMatchList', ajaxData).then(res => {
        if (res.data.code == 200) {
          this.tableData = res.data.data ? res.data.data.list : [];
          // console.warn("((((((",this.tableData)
          this.total = res.data.data ? res.data.data.tota : 0;
        }
        if(! this.tableData || this.tableData.length == 0){
          this.emptyText = '暫無數據';
        }
      }).catch(res => {

      })
    },
    handleCurrentChange(val) {
      this.searchFn(val);
    },
    audit(item, params) {
      this.$router.push({ name: 'newListAudit', query: { id: item.id, listType: item.uploadType, useparams: params, usetabledata: this.tableData, thissearchparam: this.searchparam } })
    },
    uploadChange(file) {
      this.$utils.uploadFile({flie:file.raw,fid:this.search.fileBaseId}, '/list2match/importAuditResults', this)
    },
    uploadSuccess(response, file, filelist) {
      console.log("{+++++++++", response, file, filelist)
      this.searchFn(this.currentPage)

    },
    exportFn() {
      this.$msgbox.confirm(`確定進行[導出]操作?`, '提示', {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$refs.form.validate(validate => {
          if (validate) {
            let ajaxData = {};
            ajaxData.listmatchDataBasic = this.$utils.copy(this.search);
            if (ajaxData.listmatchDataBasic.matchScore == 99) {
              delete ajaxData.listmatchDataBasic.matchScore;
            }
            this.$http.post('/list2match/export', ajaxData, { responseType: 'blob' }).then(res => {
              console.warn("{}{{{resresres", res)
              let data = res.data
              this.$utils.downloadByBlob(data, res.headers["content-disposition"])
            })
          }
        })
      })

    },
    changeSearchScore(score) {
      this.search.matchScore = score;
    },
    selectionChangeHandle(val) {
        const currentSelected = val;

        // 添加新选中的项
        currentSelected.forEach(row => {
            const key = row.uniqueKeyMd5;
            if (!this.selectedRowKeys.has(key)) {
                this.allSelectedItems.push(row);
                this.selectedRowKeys.add(key);
            }
        });

        // 移除取消勾选的项
        this.allSelectedItems = this.allSelectedItems.filter(
            item => currentSelected.some(r => r.uniqueKeyMd5 === item.uniqueKeyMd5)
        );

        // 更新 selectedRowKeys
        const currentKeys = new Set(currentSelected.map(row => row.uniqueKeyMd5));
        this.selectedRowKeys = new Set([...this.selectedRowKeys, ...currentKeys]);

    },
    auditBatch(status) {
      let url = '/list2match/checkListMatchDataBasicAll';

      const uniqueKeyMd5List = Array.from(this.selectedRowKeys);

      // let uniqueKeyMd5List = [];
      // this.dataListSelections.forEach(item => {
      //   uniqueKeyMd5List.push(item.uniqueKeyMd5);
      // })
      //   console.log("======================================")
      //   console.log("uniqueKeyMd5List", uniqueKeyMd5List);
      //   console.log("allSelectedItems",this.allSelectedItems)
      //   console.log("selectedRowKeys",this.selectedRowKeys)
      //   console.log("======================================")
      if (uniqueKeyMd5List.length == 0) {
        this.$toast({ tips: '請至少選擇一條' });
        return;
      }
      let fileBaseId = this.search.fileBaseId;
      let ajaxData = {
        status,
        uniqueKeyMd5List,
        fileBaseId
      }
      this.$http.post(url, ajaxData).then(res => {
        console.log('resrrews', res)
        if (res.data.code && res.data.code != 200) {
          this.$toast({ tips: res.data.message })
        } else {
          this.$toast({ tips: res.data.data });
            this.allSelectedItems = []; // 清空已选中项
            this.selectedRowKeys = new Set();
            console.log("批量审核通过！！！！！！！！！！！！！！！！！！！！！！！！");
          this.searchFn();
        }
      })
    },
    importFn() {
      // todo 後端功能待開發 01-14
      console.log();
    },
    // 显示无法辨识资料处理弹窗
    showUnrecognizedDataDialog() {
      // 重置表单
      this.unrecognizedDataForm = {
        fileBaseId: '',
        filterConditions: []
      };

      // 重置已选择的FID列表
      this.selectedFidList = [];
      this.displayFidText = '';
      this.fidInputText = '';

      // 显示弹窗
      this.unrecognizedDataDialogVisible = true;
    },

    // 处理手动输入FID的变化
    handleFidInputChange() {
      if (!this.fidInputText.trim()) {
        return;
      }

      // 将输入的文本按逗号、空格、回车或换行符分割
      const fidIds = this.fidInputText.split(/[,\s\r\n]+/)
        .map(id => id.trim())
        .filter(id => id !== '' && !isNaN(id)); // 过滤空值和非数字

      if (fidIds.length === 0) {
        this.$toast({ tips: '請輸入有效的FID' });
        return;
      }

      // 清空之前的选择
      this.selectedFidList = [];

      // 将输入的FID添加到选择列表
      fidIds.forEach(id => {
        const fidObj = { id: id };
        // 避免重复添加
        if (!this.selectedFidList.some(item => item.id === id)) {
          this.selectedFidList.push(fidObj);
        }
      });

      // 更新fileBaseId字段
      this.updateFileBaseId();

      // 清空输入框
      this.fidInputText = '';
    },



    // 更新fileBaseId字段
    updateFileBaseId() {
      // 将选中的FID ID组成数组
      const fidIds = this.selectedFidList.map(fid => fid.id);

      // 更新表单中的fileBaseId字段
      // 现在后端接受的是数组，但我们仍然将第一个值存入fileBaseId以便于验证
      this.unrecognizedDataForm.fileBaseId = fidIds.length > 0 ? fidIds[0] : '';

      // 更新显示文本
      this.displayFidText = fidIds.length > 0 ? `已選擇 ${fidIds.length} 個FID` : '';
    },

    // 移除已选择的FID
    removeFid(fid) {
      const index = this.selectedFidList.findIndex(item => item.id === fid.id);
      if (index !== -1) {
        this.selectedFidList.splice(index, 1);
        this.updateFileBaseId();
      }
    },



    // 处理无法辨识资料处理弹窗关闭事件
    handleUnrecognizedDataDialogClosed() {
      // 重置选中状态
      this.selectedFidList = [];
      this.displayFidText = '';
      this.fidInputText = '';
      this.unrecognizedDataForm = {
        fileBaseId: '',
        filterConditions: []
      };
    },

    // 处理筛选条件选择变化
    handleFilterConditionsChange(value) {
      // 筛选条件变化时的处理逻辑
      console.log('选择的筛选条件:', value);
    },

    // 关闭无法辨识资料处理弹窗
    closeUnrecognizedDataDialog() {
      this.unrecognizedDataDialogVisible = false;
    },

    // 导出无法识别的数据
    exportUnrecognizedData() {
        console.log("data :"+this.unrecognizedDataForm.filterConditions)
      this.$refs.unrecognizedDataForm.validate(valid => {
        if (valid) {
          // 准备请求参数
          const formData = new FormData();

          // 将所有选中的FID作为数组传递，使用正确的参数名 fileBaseId
          if (this.selectedFidList.length > 0) {
            // 将所有选中的FID ID转换为数字并作为数组传递
            this.selectedFidList.forEach(fid => {
              formData.append('fileBaseId', parseInt(fid.id, 10));
            });
          } else if (this.unrecognizedDataForm.fileBaseId) {
            // 兼容单个FID的情况
            formData.append('fileBaseId', parseInt(this.unrecognizedDataForm.fileBaseId, 10));
          } else {
            // 如果没有选择FID，返回验证错误
            this.$toast({ tips: '請選擇至少一個FID' });
            return;
          }

          // 添加可选的筛选条件
          if (this.unrecognizedDataForm.filterConditions.includes('matchId')) {
            // 删除符合相同MATCHID条件的资料
            formData.append('filterMatchId', '1');
          }

          if (this.unrecognizedDataForm.filterConditions.includes('titleArtistCname')) {
            // 删除符合UP TITLE/UP ARTIST/UP C NAME组合条件
            formData.append('filterTitleArtistCname', '1');
          }

          if (this.unrecognizedDataForm.filterConditions.includes('titleArtistCnameDuration')) {
            // 删除符合UP TITLE/UP ARTIST/UP C NAME/UP DUR MIN/UP DUR SEC组合条件
            formData.append('filterTitleArtistCnameDuration', '1');
          }

          // 添加导出类型参数，当有筛选条件时设置为 'filtered'
          if (this.unrecognizedDataForm.filterConditions.length > 0) {
            formData.append('exportType', 'filtered');
          }

          // 使用 POST 请求并将参数作为表单数据传递
          this.$http.post('/list2match/exportByConditions', formData, {
            responseType: 'blob'
          })
            .then(res => {
              // 使用自定义下载函数处理文件下载
              customDownload(res.data, res.headers['content-disposition'], res.headers['content-type']);

              // 关闭弹窗
              this.unrecognizedDataDialogVisible = false;
              // 重置选中状态
              this.selectedFidList = [];
              this.displayFidText = '';
              this.fidInputText = '';
              this.$toast({ tips: '导出成功' });
            })
            .catch(err => {
              console.error(err);
              this.$toast({ tips: '导出失败' });
            });
        }
      });
    }
  },
  // beforeRouteEnter(to, from, next) {
  //   next((vm) => {
  //     vm.searchFnfirst(vm.currentPage)

  //   });
  // },
}
</script>

<style>
.el-form-item__error {
    white-space: nowrap;
}
.oneLine {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

/* 無法辨識資料處理弹窗样式 */
.el-select-dropdown__item.selected {
  color: #11C26D;
}

.el-dialog__title {
  font-weight: bold;
}

.el-dialog__body {
  padding: 20px 30px;
}

/* FID選擇弹窗样式 */
.search-box {
  margin-bottom: 15px;
}

.pagination-container {
  margin-top: 15px;
}

.el-table .oneLine {
  display: inline-block;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.selected-fids {
  margin-top: 8px;
  min-height: 32px;
}

.el-table__body tr.current-row > td {
    color: white !important;
    background-color: #17b3a3 !important;
}

.el-table__body tr.current-row .el-button--text span {
    color: white !important;
}
</style>
