import Vue from 'vue'
import App from '@/App'
import router from '@/router'                                 // api: https://github.com/vuejs/vue-router
import store from '@/store'                                     // api: https://github.com/vuejs/vuex
import VueCookie from 'vue-cookie'                        // api: https://github.com/alfhen/vue-cookie
import '@/element-ui'                                                 // api: https://github.com/ElemeFE/element
import '@/icons'                                                            // api: http://www.iconfont.cn/
import '@/element-ui-theme'
import '@/assets/scss/index.scss'
import httpRequest from '@/utils/httpRequest' // api: https://github.com/axios/axios
import { isAuth } from '@/utils'
import installDirectives from './app-directives'

import i18n from '@/i18n/i18n'
import toast from '@/components/toast'
import utils from '@/utils/index'
import bus from '@/utils/bus'
import topBar from '@/components/top-bar/index'
import topBardetail from '@/components/top-bardetail/index'
import filter from '@/utils/filters'
import inputNumber from '@/utils/inputNumber';
import datePicker from '@/components/date-picker/index';
import cloneDeep from 'lodash/cloneDeep'
import fnModal from '@/components/fn-modal/index';

Vue.use(VueCookie)
Vue.use(toast)
Vue.use(topBar)
Vue.use(topBardetail)
Vue.use(datePicker)
Vue.config.productionTip = false

import './assets/toast.css'
//混入全局監聽事件
Vue.mixin(require('@/utils/event-mixin.js').default)
// 挂载全局
Vue.prototype.$http = httpRequest // ajax请求方法
Vue.prototype.$utils = utils
Vue.prototype.isAuth = isAuth         // 权限方法
Vue.prototype.$bus = bus
Vue.prototype.$inputNumber = inputNumber
Vue.prototype.$fnModal = fnModal

installDirectives(Vue);

for( let key in filter){
    Vue.filter(key, filter[key])
}

// 保存整站vuex本地储存初始状态
window.SITE_CONFIG['storeState'] = cloneDeep(store.state)

/* eslint-disable no-new */
new Vue({
    el: '#app',
    router,
    store,
    i18n,
    template: '<App/>',
    components: { App }
})
