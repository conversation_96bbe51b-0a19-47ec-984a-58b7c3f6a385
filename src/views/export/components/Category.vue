<template>
  <el-dialog title="單場次導出" :visible.sync="showExport" width="40%" :show-close="true" :close-on-click-modal="false">
    <el-tabs v-model="exportType" type="card">
      <el-tab-pane label="Category Code" name="code">
        <el-button size="mini" type="primary" @click="exportAddFn()">添加</el-button>
        <el-table  :data="exportForm.categories" border stripe style="width: 100%">
          <el-table-column label="Category Code">
            <template slot-scope="scope">
              <el-input v-model="scope.row.categoryCode" placeholder=""></el-input>
            </template>
          </el-table-column>
          <!-- <el-table-column
              label="Perf Date">
              <template slot-scope="scope">
                  <date-picker v-model="scope.row.performTime" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd" style="width: 180px;"></date-picker>
              </template>
          </el-table-column> -->    
          <el-table-column label="Operation" width="220px">
            <template slot-scope="scope">
              <el-button size="mini" type="danger" @click="exportDeleteFn(scope.$index, scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="Category 段" name="range">
        <el-form :inline="false" label-width="180px">
          <el-form-item label="From Category Code">
            <el-input v-model="exportForm.startCategroyCode" style="width: 180px"></el-input>
          </el-form-item>
          <el-form-item label="To Category Code">
            <el-input v-model="exportForm.endCategoryCode" style="width: 180px"></el-input>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="上傳時間" name="date">
        <el-form :inline="false" label-width="120px">
          <el-form-item label="Start Date">
            <date-picker v-model="exportForm.startTime" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd" style="width: 180px"></date-picker>
          </el-form-item>
          <el-form-item label="End Date">
            <date-picker v-model="exportForm.endTime" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd" style="width: 180px"></date-picker>
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>
    <div slot="footer">
      <el-button type="primary" @click="exportFn">導出</el-button>
    </div>
  </el-dialog>
</template>
<script>
export default {
  name: "",
  data() {
    return {
      showExport: false,
      exportType: "code",
      exportForm: {
        categories: [{ categoryCode: "", performTime: "" }],
        startCategroyCode: "",
        endCategoryCode: "",
        startTime: "",
        endTime: "",
      },
    };
  },
  methods: {
    exportList() {
      this.exportForm = {
        categories: [{ categoryCode: "", performTime: "" }],
        startCategroyCode: "",
        endCategoryCode: "",
        startTime: "",
        endTime: "",
      };
      this.showExport = true;
    },
    exportAddFn() {
      this.exportForm.categories.push({ categoryCode: "", performTime: "" });
    },
    exportDeleteFn(index) {
      this.exportForm.categories.splice(index, 1);
    },
    exportFn() {
      let ajaxData = {};
      if (this.exportType == "code") {
        ajaxData.type = 1;
        ajaxData.categories = this.exportForm.categories;
        let flag = true;
        ajaxData.categories.forEach((item) => {
          if (!item.categoryCode) {
            flag = false;
          }
        });
        if (!flag) {
          this.$toast({ tips: "請輸入Category Code" });
          return;
        }
      } else if (this.exportType == "range") {
        ajaxData.type = 2;
        ajaxData.startCategroyCode = this.exportForm.startCategroyCode;
        ajaxData.endCategoryCode = this.exportForm.endCategoryCode;
        if (!ajaxData.startCategroyCode || !ajaxData.endCategoryCode) {
          this.$toast({
            tips: "請輸入From Category Code 和 To Category Code",
          });
          return;
        }
      } else if (this.exportType == "date") {
        if (!this.exportForm.startTime || !this.exportForm.endTime) {
          this.$toast({ tips: "請輸入Start Date 和 End Date" });
          return;
        }
        if (this.exportForm.endTime < this.exportForm.startTime) {
          this.$toast({ tips: "End Date不能小於Start Date" });
          return;
        }
        ajaxData.type = 3;
        ajaxData.startTime = this.exportForm.startTime + ' 00:00:00';
        ajaxData.endTime = this.exportForm.endTime + ' 23:59:59';
      }
      console.log(ajaxData.categories);
      // if(ajaxData.type != 1||ajaxData.categories.length>1){
      this.$http.post("/export/pdf/singleSession", ajaxData).then((res) => {
          console.log(res);
          if (res.success && res.data.code == 200) {
            this.$msgbox.confirm(`${res.data.message}`, '导出成功', {
              confirmButtonText: '確定',
              type: 'warning',
              closeOnClickModal: false,
              showCancelButton: false
            }).then(() => {
              this.showExport = false;
            }).catch(() => {

            })
            // this.$toast({tips: '導出成功！'})

          } else {
            this.$toast({ tips: res.data.message })
          }
        });
      // }else{
      //     this.$http
      //         .post("/export/pdf/singleSession", ajaxData, { responseType: "blob" })
      //         .then((res) => {
      //           console.log(res);
      //           if (res.data.type != "application/pdf") {
      //             this.$toast({ tips: "导出失败" });
      //           } else {
      //             this.$utils.downloadByBlob(res.data, "单场次导出报表", ".pdf");
      //             this.showExport = false;
      //           }
      //           // if(res.success && res.data.code == 200){
      //           //     this.$utils.downloadByBlob(res.data,'单场次导出报表','.pdf')
      //           //     // this.$toast({tips: '導出成功！'})
      //           //     this.showExport = false;
      //           // }else{
      //           //     this.$toast({tips: res.data.message})
      //           // }
      //     });
      // }

    },
  },
};
</script>
<style scoped>
</style>