<template>
  <!-- 拆分後的文件 用于審核用的 -->
  <div class="ipibpox">
    <el-form :inline="true" :model="search.ListMatchDataOverseas" ref="form" :rules="rules" class="demo-form-inline"  @keyup.enter.native='searchFn(1)'>
      <!-- <el-form-item prop="fileBaseId">
                <span class="red">*</span>
                <el-input v-model="search.ListMatchDataOverseas.fileBaseId" placeholder="FID" style="width: 80px;"></el-input>
            </el-form-item> -->
      <!--            <el-form-item prop="數據来源協會">
                <el-input v-model="search.ListMatchDataOverseas.sourceWorkCode" placeholder="數據来源協會"></el-input>
                 "quantileBegin": 0,
               "quantileEnd": 0,
            </el-form-item>-->
      <el-form-item>
        <el-input v-model="search.ListMatchDataOverseas.batch" placeholder="batch" style="width: 120px;"></el-input>
      </el-form-item>
            <el-form-item>
        <el-input v-model="search.ListMatchDataOverseas.distNo" placeholder="分配代號" style="width: 120px;"></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="search.ListMatchDataOverseas.remitSociety" placeholder="Remit Soc" @keydown.native="$inputNumber" style="width: 120px;"></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="search.ListMatchDataOverseas.originalTitle" placeholder="Remit Work Title" style="width: 160px;"></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="search.ListMatchDataOverseas.authorComposer" placeholder="Remit author_composer" style="width: 220px;"></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="search.ListMatchDataOverseas.matchWorkTitle" placeholder="Match Work Title" style="width: 165px;"></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="search.ListMatchDataOverseas.matchWorkId" placeholder="Match Work id" style="width: 150px;"></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="search.ListMatchDataOverseas.matchWorkSocietyCode" placeholder="Match Work Soc" style="width: 160px;"></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="search.ListMatchDataOverseas.dataUniqueKey" placeholder="Data unique key" style="width: 160px;"></el-input>
      </el-form-item>
      <!-- <el-form-item>
        <el-button :type="search.ListMatchDataOverseas.matchScore < 0 ? 'primary' : ''" @click="changeSearchScore(-1)">10分以下</el-button>
        <el-button :type="search.ListMatchDataOverseas.matchScore === 0 ? 'primary' : ''" @click="changeSearchScore(0)">10-20分</el-button>
        <el-button :type="search.ListMatchDataOverseas.matchScore > 0 ? 'primary' : ''" @click="changeSearchScore(1)">20分以上</el-button>
      </el-form-item> -->
      <el-form-item>
        <el-select v-model="search.ListMatchDataOverseas.status" placeholder="請選擇" style="width: 120px;" @change="gaibian">
          <el-option v-for=" (item, index) in config.status" :key="index" :value="item.value" :label="item.label"></el-option>
        </el-select>
      </el-form-item>
      <!-- todo  -->

      <el-form-item>
        <el-button type="primary" @click="searchFn(1)" v-if="isAuth('list-manage:o-audit:auditList:find')">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <el-upload class="upload-demo" ref="upload" action="" accept=".xls,.xlsx" :auto-upload="false" :on-change="uploadChange" :show-file-list="false" :file-list="fileList">
          <el-button size="small" type="primary" style="background-color: #b240ff;border-color: #b240ff;" v-if="isAuth('list-manage:o-audit:auditList:exportin')">審核結果導入</el-button>
        </el-upload>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="exportFn" v-if="isAuth('list-manage:o-audit:auditList:exportout')">審核結果導出</el-button>
      </el-form-item>
      <el-form-item>
        <span class="clear-search" @click="clearSearch()" v-if="isAuth('list-manage:o-audit:auditList:find')">清除搜索</span>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="showUnrecognizedDataDialog" v-if="isAuth('list-manage:o-audit:auditList:find')">無法辨識資料處理</el-button>
      </el-form-item>

    </el-form>

    <!-- 無法辨識資料處理弹窗 -->
    <el-dialog
      title="無法辨識資料處理"
      :visible.sync="unrecognizedDataDialogVisible"
      width="600px"
      :close-on-click-modal="false"
      @closed="handleUnrecognizedDataDialogClosed">
      <el-form :model="unrecognizedDataForm" ref="unrecognizedDataForm" :rules="unrecognizedDataRules" label-width="140px">
        <el-form-item label="FID" prop="file_base_id">
          <el-input
            v-model="displayFidText"
            placeholder="請點擊搜索選擇FID"
            style="width: 100%"
            readonly
            @dblclick="showFidSelectDialog">
            <template slot="append">
              <el-button icon="el-icon-search" @click="showFidSelectDialog"></el-button>
            </template>
          </el-input>
          <div v-if="selectedFidList.length > 0" class="selected-fids">
            <el-tag
              v-for="fid in selectedFidList"
              :key="fid.id"
              closable
              @close="removeFid(fid)"
              style="margin-right: 5px; margin-top: 5px;">
              {{ fid.id }}
            </el-tag>
          </div>
        </el-form-item>
        <el-form-item label="去重條件" prop="filterConditions">
          <el-select
            v-model="unrecognizedDataForm.filterConditions"
            multiple
            placeholder="請選擇去重條件（可選）"
            style="width: 100%"
            @change="handleFilterConditionsChange">
            <el-option key="data_unique_key" label="data_unique_key" value="data_unique_key"></el-option>
            <el-option key="original_title_author_composer" label="original_title + author_composer" value="original_title_author_composer"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeUnrecognizedDataDialog">取消</el-button>
        <el-button type="primary" @click="exportUnrecognizedData">導出為Excel</el-button>
      </span>
    </el-dialog>

    <!-- FID選擇弹窗 -->
    <el-dialog
      title="選擇FID"
      :visible.sync="fidSelectDialogVisible"
      width="80%"
      :close-on-click-modal="false">
      <div class="search-box">
        <el-form :inline="true" @keyup.enter.native="searchFids()">
          <el-form-item label="FID">
            <el-input v-model="fidSearchForm.id" placeholder="請輸入FID" style="width: 150px"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="searchFids">查詢</el-button>
          </el-form-item>
          <el-form-item>
            <el-button type="success" @click="confirmSelectFids">確定</el-button>
          </el-form-item>
        </el-form>
      </div>

      <el-table
        ref="fidTable"
        :data="fidTableData"
        border
        stripe
        style="width: 100%"
        v-loading="fidTableLoading"
        @selection-change="handleFidSelectionChange"
        @row-dblclick="selectRowFid"
        row-key="id">
        <el-table-column
          type="selection"
          width="55">
        </el-table-column>
        <el-table-column prop="receiptId" label="水单ID" width="100"></el-table-column>
        <el-table-column prop="id" label="FID" width="80"></el-table-column>
        <el-table-column prop="fileType" label="Format" width="80"></el-table-column>
        <el-table-column prop="remitSocietyName" label="協會名" width="120"></el-table-column>
        <el-table-column prop="remitSocietyCode" label="協會編號" width="120"></el-table-column>
        <el-table-column prop="distNo" label="分配代號" width="120"></el-table-column>
        <el-table-column prop="fileName" label="文件名稱" min-width="200">
          <template slot-scope="scope">
            <el-tooltip :content="scope.row.fileName" placement="top" effect="light">
              <span class="oneLine">{{ scope.row.fileName }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container" style="margin-top: 15px; text-align: center;">
        <el-pagination
          background
          layout="prev, pager, next"
          :total="fidTableTotal"
          :current-page.sync="fidSearchForm.page.pageNum"
          @current-change="handleFidPageChange">
        </el-pagination>
      </div>
    </el-dialog>

    <el-table v-table-flex="{topFix: 150}" height="180" :empty-text="tableresult" stripe
              :data="tableData" border style="width: 100%"
              @selection-change="handleSelectionChange"
              @current-change="handleCurrentRowChange"
              highlight-current-row
              ref="auditTable"
    >
      <el-table-column type="selection" width="55" :selectable="checkSelectable">

      </el-table-column>
      <!-- <el-table-column
                prop="fileBaseId"
                label="FID"
            >
            </el-table-column>
            <el-table-column
                prop="remitSociety"
                label="數據來源協會"
                min-width="120">
            </el-table-column> -->
      <!--            <el-table-column-->
      <!--                prop="sourceWorkCode"-->
      <!--                label="來源作品編碼"-->
      <!--                min-width="120">-->
      <!--            </el-table-column>-->
      <!--            <el-table-column-->
      <!--                prop="iswc"-->
      <!--                label="iswc/isrc"-->
      <!--                min-width="100">-->
      <!--            </el-table-column>-->
      <!--            <el-table-column-->
      <!--                prop="isrc"-->
      <!--                label="isrc">-->
      <!--            </el-table-column>-->
      <el-table-column prop="originalTitle" label="Remit work title" min-width="130">
      </el-table-column>
      <el-table-column prop="authorComposer" label="Author/Composer" min-width="100">
        <!-- <template slot-scope="scope"> -->
          <!-- {{scope.row.authorComposer | addSpace}} -->
          <!-- <span :title="scope.row.authorName + (scope.row.authorName && scope.row.composerName ? '/' : '') + scope.row.composerName">{{scope.row.authorName + (scope.row.authorName && scope.row.composerName ? '/' : '') + scope.row.composerName}}</span> -->
        <!-- </template> -->
      </el-table-column>
      <!-- <el-table-column
                prop="matchWorkId"
                prop="matchWorkSocietyCode"
                label="WorkNo"
                label="MatchSoc-WorkNo"
                min-width="160">
            </el-table-column> -->
      <el-table-column prop="matchWorkTitle" label="Match WorkTitle" min-width="160">
        <template slot-scope="scope">
          <span :title="scope.row.matchWorkTitle">{{scope.row.matchWorkTitle}}</span>
        </template>
      </el-table-column>
      <el-table-column label="Match Work Id/Soc" min-width="160">
        <template slot-scope="scope">
          {{(scope.row.matchWorkSocietyCode ? scope.row.matchWorkSocietyCode : '') + (scope.row.matchWorkId ?
                    '-'+scope.row.matchWorkId : '')}}
        </template>
      </el-table-column>
         <el-table-column prop="dataUniqueKey" label="Data unique key">
          <template slot-scope="scope">
          <el-tooltip class="item" effect="dark" :content="scope.row.dataUniqueKey" placement="top">
            <div class="oneLine">{{scope.row.dataUniqueKey}}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="審核狀態" min-width="90">
        <template slot-scope="scope">
          <el-button v-if="scope.row.status === 1 || scope.row.status === 3" type="text">已審核</el-button>
          <el-button v-if="scope.row.status === 0" type="text">待審核</el-button>
          <el-button v-if="scope.row.status === 2" type="text">不匹配</el-button>
          <!-- <el-button v-if="scope.row.status === 3" type="text">ip已審核</el-button> -->
        </template>
      </el-table-column>
      <el-table-column prop="matchType" label="Match Type" min-width="90">
        <!-- <template slot-scope="scope">
          <el-button v-if="scope.row.status === 1" type="text">已審核</el-button>
          <el-button v-if="scope.row.status === 0" type="text">待審核</el-button>
          <el-button v-if="scope.row.status === 2" type="text">不匹配</el-button>
        </template> -->
      </el-table-column>
      <el-table-column fixed="right" label="operation" width="100">
        <template slot-scope="scope">
          <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
            <el-button @click="audit(scope.row,scope.$index)" type="text" size="small" v-if="isAuth('list-manage:o-audit:auditList:audit')">審核</el-button>
          </span>
        </template>
      </el-table-column>
    </el-table>
    <div>

      <el-pagination background layout="sizes, prev, pager, next" :total="total" :current-page="currentPage" @current-change="handleCurrentChange"
      :page-sizes="[10, 20, 50, 100]" :page-size="20" @size-change="handleSizeChange">
      </el-pagination>
    </div>
  </div>
</template>

<script>
import { customDownload } from './custom-download.js';

export default {
  name: 'oAuditList',
  data() {
    return {
      tableData: [],tableresult:' ',
      searchparam: {},
      currentPage: 1,
      fileList: [],
      total: 0,
      // 无法辨识资料处理弹窗相关数据
      unrecognizedDataDialogVisible: false,
      unrecognizedDataForm: {
        file_base_id: '',
        filterConditions: []
      },
      unrecognizedDataRules: {
        file_base_id: [
          { required: true, message: '請選擇FID', trigger: 'change' }
        ]
      },
      // FID选择弹窗相关数据
      fidSelectDialogVisible: false,
      fidSearchForm: {
        id: '',
        page: {
          pageNum: 1,
          pageSize: 10
        }
      },
      fidTableData: [],
      fidTableLoading: false,
      fidTableTotal: 0,
      selectedFids: [],
      // 已选择的FID列表
      selectedFidList: [],
      // 用于显示的FID文本
      displayFidText: '',
      rules: {
        fileBaseId: [
          { required: true, message: '請輸入FID', trigger: 'blur' }
        ]
      },
      search: {
        ListMatchDataOverseas: {
          batch: '',
          distNo: '',
          remitSociety: '',
          originalTitle: '',
          authorComposer: '',
          matchWorkTitle: '',
          matchWorkId: '',
          matchWorkSocietyCode: '',
          dataUniqueKey: '',
          status: ''
        },
        page: {
          pageNum: 1,
          pageSize: 20
        }


      },
      config: {
        /*status: {
            0: '待審核',
            1: '已匹配',
            2: '不匹配'
        },*/
        status: [
          {
            value: '',
            label: '全部'
          },
          {
            value: '0',
            label: '待審核'
          },
          {
            value: '1',
            label: '已審核'
          }
        ],
        score: [{
          value: '',
          label: '全部評分'

        },
        {
          value: -1,
          label: '10分以下'
        },
        {
          value: 0,
          label: '10-20分'
        },
        {
          value: 1,
          label: '20分以上'
        }],
        uploadType: ['PG', 'FW', 'MS', 'CJ']
      },
      selectList: [],
    }
  },
  activated() {
    if (this.$route.query.update) {
      let query = this.$route.query;
      delete query.update;
      this.searchFn();
    }
  },
  mounted() {
    // 搜索后选择最后一页，修改搜索条件但不点搜索，切到其他TAB再切回来，会出现BUG
    // 因此不能这样写  2025-01-25
    // this.searchFn()
  },
  methods: {
    clearSearch() {
      this.search.ListMatchDataOverseas =  {
          batch: '',
          distNo: '',
          remitSociety: '',
          originalTitle: '',
          authorComposer: '',
          matchWorkTitle: '',
          matchWorkId: '',
          matchWorkSocietyCode: '',
          dataUniqueKey: '',
          status: ''
        }
        // page: {
        //   pageNum: 1,
        //   pageSize: 10
        // }

      this.searchFn()
    },
    gaibian(){
      this.search.page.pageNum =1
    },
    searchFn(page) {
      console.log(page)
      if (page == 1) {
        this.total = 0
      }
        this.search.page.pageNum = page||1

      // if (this.search.ListMatchDataOverseas.originalTitle||this.search.ListMatchDataOverseas.matchScore||this.search.ListMatchDataOverseas.status) {
      //   this.search.page.pageNum =1

      // }
      this.searchparam=this.search
            this.tableresult = '數據加載中...'
      this.$http.post('/listmatchoverseas/getListMatchDataOverseasList', this.search).then(res => {
        if (res.success) {
          this.tableData = res.data.data.list;
          this.total = res.data.data.total
        }
          this.tableresult = this.tableData.length == 0 ? '暫無數據' : ' '
      })
      //     }
      // })

    },
    handleCurrentChange(val) {
      console.log(val)
      this.currentPage = val
      this.searchFn(val);

    },
    handleSizeChange(val) {
        console.log(val);
        this.search.page.pageSize = val
        this.searchFn(this.currentPage);
    },
    audit(item, params) {
      console.warn('seleitem', item, 'index', params);
      // 设置当前行为选中状态
      this.$refs.auditTable.setCurrentRow(item);
      this.tableData
      this.$router.push({
        name: 'oAuditDetail',
        params: {
          title: `O清單審核明細:${item.originalTitle}`,
        },
        query: {
          id: item.id,
          listType: item.uploadType,
          useparams: params,
          usetabledata: JSON.stringify(this.tableData),
          thissearchparam:JSON.stringify(this.searchparam)}
        })
    },
    changeSearchScore(score) {
      this.search.ListMatchDataOverseas.matchScore = score;
    },
    checkSelectable(row) {
      return !row.status
    },
    handleSelectionChange(list) {
      let array = [];
      list.forEach((item, index) => {
        array.push(item.dataUniqueKey)
      })
      this.selectList = array;
    },
    // 处理当前行变化事件
    handleCurrentRowChange(currentRow, oldCurrentRow) {
      // 当前行变化时的处理逻辑（如果需要的话）
      console.log('当前选中行:', currentRow);
    },
    uploadChange(file) {
      console.log(file)
      this.$utils.uploadFile({flie:file.raw}, '/listmatchoverseas/importAuditResults', this)
    },
    exportFn() {
      this.$msgbox.confirm(`確定進行[導出]操作?`, '提示', {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let ajaxData = {};
        ajaxData.ListMatchDataOverseas = this.$utils.copy(this.search.ListMatchDataOverseas);
        if (ajaxData.ListMatchDataOverseas.matchScore == 99) {
          delete ajaxData.ListMatchDataOverseas.matchScore;
        }
        this.$http.post('/listmatchoverseas/export', ajaxData, { responseType: 'blob' }).then(res => {
          console.log(res)
          let data = res.data
          this.$utils.downloadByBlob(data, res.headers["content-disposition"])
        })
      })
    },
    batchAudit(type) {
      if (!this.selectList.length) {
        this.$toast({ tips: '請至少選擇一項' })
        return;
      }
      this.$http.post('/listmatchoverseas/checkListMatchDataOverseasAll', { uniqueKeyMd5List: this.selectList, status: type }).then(res => {
        console.log(res)
        if (res.success) {
          if (!res.data.code || res.data.code == 200) {
            this.$toast({ tips: '批量審核' + (type == 1 ? '通過' : '拒絕') + '成功' });
            this.searchFn();
          } else {
            this.$toast({ tips: res.data.message })
          }
        }
      })

    },
    // 显示无法辨识资料处理弹窗
    showUnrecognizedDataDialog() {
      // 重置表单
      this.unrecognizedDataForm = {
        file_base_id: '',
        filterConditions: []
      };

      // 重置已选择的FID列表
      this.selectedFidList = [];
      this.displayFidText = '';

      // 显示弹窗
      this.unrecognizedDataDialogVisible = true;
    },

    // 显示FID选择弹窗
    showFidSelectDialog() {
      this.fidSelectDialogVisible = true;
      this.fidSearchForm = {
        id: '',
        page: {
          pageNum: 1,
          pageSize: 10
        }
      };
      // 不重置selectedFids，保持之前的选择状态
      this.searchFids();
    },

    // 查询FID列表
    searchFids() {
      this.fidTableLoading = true;
      this.$http.post('/listOverseasFileBase/getListOverseasFileBaseList', this.fidSearchForm)
        .then(res => {
          this.fidTableLoading = false;
          if (res.success) {
            this.fidTableData = res.data.list || [];
            this.fidTableTotal = res.data.total || 0;

            // 数据加载完成后，设置之前选中的行的选中状态
            this.$nextTick(() => {
              if (this.$refs.fidTable && this.selectedFidList.length > 0) {
                // 对于当前表格中的每一行，如果它的ID在selectedFidList中存在，则选中它
                this.fidTableData.forEach(row => {
                  if (this.selectedFidList.some(selectedFid => selectedFid.id === row.id)) {
                    this.$refs.fidTable.toggleRowSelection(row, true);
                  }
                });
              }
            });
          } else {
            this.$toast({ tips: '查詢FID列表失敗' });
            this.fidTableData = [];
            this.fidTableTotal = 0;
          }
        })
        .catch(err => {
          this.fidTableLoading = false;
          console.error(err);
          this.$toast({ tips: '查詢FID列表失敗' });
          this.fidTableData = [];
          this.fidTableTotal = 0;
        });
    },

    // 切換页码
    handleFidPageChange(page) {
      this.fidSearchForm.page.pageNum = page;
      this.searchFids();
    },

    // 表格选择变化
    handleFidSelectionChange(selection) {
      // 更新当前选中的行
      this.selectedFids = selection;
    },

    // 确认选择FID
    confirmSelectFids() {
      if (this.selectedFids.length === 0) {
        this.$toast({ tips: '請至少選擇一個FID' });
        return;
      }

      // 將选择的FID添加到已选择列表中
      this.selectedFids.forEach(fid => {
        // 避免重复添加
        if (!this.selectedFidList.some(item => item.id === fid.id)) {
          this.selectedFidList.push(fid);
        }
      });

      // 更新file_base_id字段，将所有选中的FID ID组成数组
      this.updateFileBaseId();

      // 关闭弹窗
      this.fidSelectDialogVisible = false;
    },

    // 更新file_base_id字段
    updateFileBaseId() {
      // 将选中的FID ID组成数组
      const fidIds = this.selectedFidList.map(fid => fid.id);

      // 更新表单中的file_base_id字段
      // 现在后端接受的是数组，但我们仍然将第一个值存入file_base_id以便于验证
      this.unrecognizedDataForm.file_base_id = fidIds.length > 0 ? fidIds[0] : '';

      // 更新显示文本
      this.displayFidText = fidIds.length > 0 ? `已選擇 ${fidIds.length} 個FID` : '';
    },

    // 移除已选择的FID
    removeFid(fid) {
      const index = this.selectedFidList.findIndex(item => item.id === fid.id);
      if (index !== -1) {
        this.selectedFidList.splice(index, 1);
        this.updateFileBaseId();
      }
    },

    // 双击行选择FID
    selectRowFid(row) {
      // 添加到已选择列表中
      if (!this.selectedFidList.some(item => item.id === row.id)) {
        this.selectedFidList.push(row);
      }

      // 更新file_base_id字段
      this.updateFileBaseId();

      // 关闭弹窗
      this.fidSelectDialogVisible = false;
    },

    // 处理无法辨识资料处理弹窗关闭事件
    handleUnrecognizedDataDialogClosed() {
      // 重置选中状态
      this.selectedFidList = [];
      this.selectedFids = [];
      this.displayFidText = '';
      this.unrecognizedDataForm = {
        file_base_id: '',
        filterConditions: []
      };
    },

    // 处理去重条件选择变化
    handleFilterConditionsChange(value) {
      // 如果选中了 original_title_author_composer，自动选中 data_unique_key
      if (value.includes('original_title_author_composer') && !value.includes('data_unique_key')) {
        this.unrecognizedDataForm.filterConditions = ['data_unique_key', 'original_title_author_composer'];
      }
    },

    // 关闭无法辨识资料处理弹窗
    closeUnrecognizedDataDialog() {
      this.unrecognizedDataDialogVisible = false;
    },

    // 导出无法识别的数据
    exportUnrecognizedData() {
      this.$refs.unrecognizedDataForm.validate(valid => {
        if (valid) {
          // 准备请求参数
          const formData = new FormData();

          // 将所有选中的FID作为数组传递，使用正确的参数名 file_base_id
          if (this.selectedFidList.length > 0) {
            // 将所有选中的FID ID转换为数字并作为数组传递
            this.selectedFidList.forEach(fid => {
              formData.append('file_base_id', parseInt(fid.id, 10));
            });
          } else if (this.unrecognizedDataForm.file_base_id) {
            // 兼容单个FID的情况
            formData.append('file_base_id', parseInt(this.unrecognizedDataForm.file_base_id, 10));
          } else {
            // 如果没有选择FID，返回验证错误
            this.$toast({ tips: '請選擇至少一個FID' });
            return;
          }

          // 添加可选的过滤条件，确保使用非空数组
          if (this.unrecognizedDataForm.filterConditions.includes('data_unique_key')) {
            // 使用一个非空字符串，确保参数被识别为有去重条件
            formData.append('data_unique_key', '1');
          }

          if (this.unrecognizedDataForm.filterConditions.includes('original_title_author_composer')) {
            // 当选择了组合条件时，同时设置 original_title 和 author_composer 参数
            formData.append('original_title', '1');
            formData.append('author_composer', '1');
          }

          // 添加导出类型参数，当有去重条件时设置为 'dedup'
          if (this.unrecognizedDataForm.filterConditions.length > 0) {
            formData.append('export_type', 'dedup');
          }

          // 使用 POST 请求并将参数作为表单数据传递
          this.$http.post('/listmatchoverseas/exportByConditions', formData, {
            responseType: 'blob'
          })
            .then(res => {
              // 使用自定义下载函数处理文件下载
              customDownload(res.data, res.headers['content-disposition'], res.headers['content-type']);

              // 关闭弹窗
              this.unrecognizedDataDialogVisible = false;
              // 重置选中状态
              this.selectedFidList = [];
              this.selectedFids = [];
              this.displayFidText = '';
              this.$toast({ tips: '导出成功' });
            })
            .catch(err => {
              console.error(err);
              this.$toast({ tips: '导出失败' });
            });
        }
      });
    }
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      console.log('*****out',vm)
      // vm.formData.distNo = to.params.distNo
      vm.searchFn(vm.currentPage)
    })
  }
}
</script>

<style>
.oneLine {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

/* 無法辨識資料處理弹窗样式 */
.el-select-dropdown__item.selected {
  color: #11C26D;
}

.el-dialog__title {
  font-weight: bold;
}

.el-dialog__body {
  padding: 20px 30px;
}

/* FID選擇弹窗样式 */
.search-box {
  margin-bottom: 15px;
}

.pagination-container {
  margin-top: 15px;
}

.el-table .oneLine {
  display: inline-block;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.selected-fids {
  margin-top: 8px;
  min-height: 32px;
}

.el-table__body tr.current-row > td {
    color: white !important;
    background-color: #17b3a3 !important;
}

.el-table__body tr.current-row .el-button--text span {
    color: white !important;
}

</style>
