<template>
  <div>
    <el-dialog title="Currency" :visible.sync="show" width="800px" :close-on-click-modal="false">
      <div style="width: 520px;margin: auto auto 20px auto;display: inline-block;">
        <el-input v-model.trim="searchInfo.currencyCode" @keyup.enter.native="onSubmit()" placeholder="currencyCode" style="width: 160px;"></el-input>
        <el-button type="primary" @click="onSubmit()">查詢</el-button>
        <span class="clear-search" @click="clearSearch()">清除搜索</span>
      </div>
      <el-table :empty-text="emptyText" :data="tableData">
        <el-table-column prop="id" label="id" width="180">
        </el-table-column>
        <el-table-column prop="currencyCode" label="currencyCode" width="180">
        </el-table-column>
        <el-table-column prop="currencyName" label="currencyName">
        </el-table-column>
        <el-table-column label="operation" width="120">
          <template slot-scope="scope">
            <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
              <i class="el-icon-check" @click="checkedCurrency(scope.$index,scope.row)"></i>
            </span>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background layout="prev, pager, next" :total="total" @current-change="onSubmit">
      </el-pagination>
    </el-dialog>
  </div>
</template>
<script>
export default {
  data() {
    return {
      emptyText: '暫無數據',
      show: false,
      searchInfo: {},
      tableData: [],
      total: 0
    }
  },
  props: {
    search: {
      type: Object,
      default: () => {
        return {
          currencyCode: '',
        }

      }
    }
  },

  methods: {
    /**
     * dialog 组件调用方式：
     * 1、父级用v-if在html里加载组件,
     * 2、父级调用子级的init方法，将数据初始化，prop传给data等
     *
     */
    init() {
      this.show = true;
      this.searchInfo = this.$utils.copy(this.search);
      this.onSubmit();
    },
    clearSearch() {
      this.searchInfo = {
        currencyCode: ''
      };
      this.onSubmit();
    },
    onSubmit(page) {
      let params = this.searchInfo;
      params.currencyCode = params.currencyCode ? params.currencyCode.trim() : params.currencyCode;
      params.page_num = page ? page : 1;
      this.emptyText = '數據加載中'
      params.page_size = 10;
      this.$http.get('/ref/getRefCurrency', { params: params }).then(res => {
        if (res.success && res.data.code === 200) {
          this.tableData = [];
          res.data.data.list.map(item => {
            this.tableData.push(item);
          })
          if(! this.tableData || this.tableData.length == 0){
            this.emptyText = '暫無數據';
          }
          this.total = res.data.data.total;
        }
      })
    },
    checkedCurrency(index, row) {
      this.$emit('checkCurrency', row);
      this.show = false;
    }
  }

}
</script>
<style lang="scss" scoped>
/deep/ .el-dialog__title {
  font-weight: bold;
}
/deep/ .el-dialog__body {
  text-align: center;
  padding-top: 0;
}
</style>
