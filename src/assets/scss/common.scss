#app, body, html{
    width:100%;
    height:100%;
    /* overflow: hidden; */
}
p{
    word-break: break-word;
}
.line{
    border-bottom: 1px dashed #aaa;
}

.p-t-10{
    padding-top: 10px;
}
.b{
    font-weight: bold;
}
.el-sl{
    overflow: hidden;
    text-overflow:ellipsis;
    white-space: nowrap;
}
.relative{
    position: relative;
}
.el-tabs{
    *{
        font-size: 16px;
    }
    .el-tabs__nav-scroll *{
        font-size: 14px;
    }
}
.el-table__header .cell{
    color: #333;
}
.el-button--text span{
    color: #333;
}
.f-l{
    float: left;
}
.clear:after{
    content: '.';
    visibility: hidden;
    height: 0;
    clear: both;
    display: block;
}
.p-t-40{
    padding-top: 40px;
}
.p-t-20{
    padding-top: 20px;
}
.operation{
    display: inline-block;
    width: 100%;
    text-align: center;
    cursor: pointer;
    i{
        margin-right: 10px;
    }
}
.pointer{
    cursor: pointer;
}
.dashed-line{
    border: 1px dashed #ccc;
    margin: 10px 0 20px 0;
}
.t-c{
    text-align: center;
}
.t-r{
    text-align: right;
}

/*
去除type为number的input的上下小箭头
*/
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
}
input[type="number"]{
    -moz-appearance: textfield;
}

.clear-search{
    font-size: 14px;
    line-height: 20px;
    vertical-align: bottom;
    text-decoration: underline;
    cursor: pointer;
    color: #666;
}
.a-blue{
    color: #409EFF;
    cursor: pointer;
}
.a-blue:hover{
    color: #409EFF;
    text-decoration: underline;
}

.a-red{
    color: #FF3333;
    cursor: pointer;
}
.a-red:hover{
    color: #CC0000;
    text-decoration: underline;
}
.el-table .cell{
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}
.el-table tr td:nth-last-child(1) .cell{
    text-overflow: inherit;
    white-space: inherit;
    overflow: inherit;
}
.ellipsis{
    white-space: nowrap;
    text-overflow:ellipsis;
    overflow:hidden;
}

/*
    作品頭部的作品信息
*/
.work-header{
    label{
        display: inline-block;
        margin-right: 6px;

        white-space: nowrap;
        text-overflow:ellipsis;
        overflow:hidden;
        max-width: 180px;
        color: #999;
        font-size: 14px;
    }
    span{
        color: #333;
    }
    .title{
        max-width: 200px;
    }

}
.detail-header{
    background:greenyellow;
    label{
        display: inline-block;
        margin-right: 6px;

        white-space: nowrap;
        text-overflow:ellipsis;
        overflow:hidden;
        max-width: 180px;
        color: #999;
        font-size: 14px;
    }
    span{
        color: #333;
    }
    .title{
        max-width: 200px;
    }

}

/*
element ui :
el-table fixed:right 时，高度不够的bug
*/
.el-table__fixed-right{
    height: 100% !important;
}
.el-table__fixed{
    height: 100% !important;
}
.el-table::before{
    z-index: inherit;
}
/*
必填 *
*/
.required .el-form-item__label:before{
    content: '*';
    color: red;
    margin-right: 2px;
}

/*
    readonly的样式设置
*/
.el-input input[readonly]{
    background: #f5f7fa;
    cursor: default;
}
.el-select .el-input input[readonly]{
    background: #fff;
    cursor: pointer;
}


/*
双击查询的border-color
*/
// .dbquery{
//     /deep/ input{
//         border-color: #1989fa !important;
//     }
// }
.el-input{
    input[placeholder="雙擊查詢"]{
        border-color: #1989fa !important;
    }
}
.el-input.dbq{
    input{
        border-color: #1989fa !important;
    }
}


/*
时间输入框
*/
.el-date-editor{
    width: 185px !important;
    input{
        padding-left: 8px !important;
    }
}

// 哪个混蛋写的代码。。。我还以为组件坏了！
// .el-picker-panel{
//     display: none !important;
// }

.el-date-editor{
    .el-input__prefix, .el-input__suffix{
        display: none;
    }
}

.el-input.is-disabled .el-input__inner, .el-checkbox__input.is-disabled+span.el-checkbox__label, .el-select .el-input.is-disabled .el-input__inner{
    cursor: auto;
}
.el-input.is-disabled .el-input__icon{
    cursor: auto;
}
.over-line{
    display: inline-block;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.pointer{
    cursor: pointer;
}
