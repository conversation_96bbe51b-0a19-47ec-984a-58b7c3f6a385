<template>
<div>
    <div class="convert">
        <div class="item clear" style="margin-bottom: 10px;">
            <div class="chinese f-l t-c">Chinese Words</div>
            <div class="pinyin f-l t-c" style="margin-left: 60px;">Roman Words</div>
        </div>
        <div class="item" v-for="(item, index) in mappingList" :key="index">
            <el-input class="chinese" type="text" v-model="mappingList[index].chineseName" @blur="convertFn(index)"></el-input>
            <!-- <el-button type="primary" icon="el-icon-right" size="mini" @click="convertFn(index)"></el-button> -->
            <el-button type="primary"  @click="convertFn(index)" size="mini"><i class="el-icon-right" size="mini"></i></el-button>
            <el-input class="pinyin" type="text" v-model="mappingList[index].pinyin"></el-input>
        </div>
    </div>
</div>
</template>
<script>
export default {
    data(){
        return{
            mappingList: []
        }
    },
    created(){
        for(let i = 0; i<15; i++){
            this.mappingList.push({chineseName: '', pinyin: ''})
        }
    },
    methods: {
        convertFn(index){
            if(!this.mappingList[index].chineseName){
                return;
            }
            this.$http.get('/ip/name/getPinYinFromChineseName?chineseName=' + this.mappingList[index].chineseName).then(res => {
                if(res.success){
                    this.mappingList[index].pinyin = res.data.data.join(' , ');
                }
            })
        }
    }
    
}
</script>
<style lang="scss" scoped>
    .convert{
        width: 820px;
        border: 1px solid #ccc;
        border-radius: 4px;
        padding: 18px;
    }
    .item{
        margin: 10px;
    }
    .chinese{
        width: 200px;
    }
    .pinyin{
        width: 500px;
    }
</style>