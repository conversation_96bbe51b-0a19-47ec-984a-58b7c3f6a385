<template>
    <nav class="site-navbar" :class="'site-navbar--' + navbarLayoutType">
        <div class="site-navbar__header">
            <h1 class="site-navbar__brand" @click="$router.push({ name: 'home' })">
                <a class="site-navbar__brand-lg" href="javascript:;">
                    <img class="mylogo" src="../assets/img/home/<USER>">
                </a>
                <a class="site-navbar__brand-mini" href="javascript:;">
                    <img class="mylogo" src="../assets/img/home/<USER>">
                </a>
            </h1>
        </div>
        <div class="site-navbar__body clearfix">
            <el-menu
                class="site-navbar__menu"
                mode="horizontal">
                <el-menu-item class="site-navbar__switch" index="0" @click="sidebarFold = !sidebarFold">
                    <icon-svg name="zhedie"></icon-svg>
                </el-menu-item>
            </el-menu>
            <el-menu
                class="site-navbar__menu"
                mode="horizontal">
                <el-menu-item class="site-navbar__switch" index="3" >
                     <span class="black">MoneySystem</span>
                     <!-- <span class="black">詞曲版權分配管理系統</span> -->
                </el-menu-item>
            </el-menu>
            <el-menu
                class="site-navbar__menu"
                mode="horizontal">
                <el-menu-item class="site-navbar__switch" index="4" >
                <!-- <span class="red">公告：</span>
                    <span>2019年10月1日0:00-4:00各部门做好停电准备</span>-->
                </el-menu-item>
            </el-menu>

            <el-menu class="site-navbar__menu site-navbar__menu--right" mode="horizontal">
                <el-menu-item index="2">
                    <langSelect></langSelect>
                </el-menu-item>
                <el-menu-item class="site-navbar__avatar" index="3">
                    <el-dropdown :show-timeout="0" placement="bottom">
                        <span class="el-dropdown-link">
                            <!-- <img src="~@/assets/img/avatar.png" :alt="userName">admin -->
                            <img src="~@/assets/img/avatar.png" :alt="userName">{{ userName }}
                        </span>
                        <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item @click.native="updatePasswordHandle()">修改密碼</el-dropdown-item>
                            <el-dropdown-item @click.native="logoutHandle()">退出</el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown>
                </el-menu-item>
            </el-menu>
        </div>
        <!-- 弹窗, 修改密碼 -->
        <update-password v-if="updatePassowrdVisible" ref="updatePassowrd"></update-password>
    </nav>
</template>

<script>
    import axios from '../utils/httpRequest'
    import UpdatePassword from './main-navbar-update-password'
    import langSelect from '../components/lang/langSelect'
    // import { clearLoginInfo } from '@/utils'
    export default {
        data () {
            return {
                updatePassowrdVisible: false
            }
        },
        components: {
            UpdatePassword,
            langSelect
        },
        mounted(){
            var _this = this;
            if(window.innerWidth < 1368){
                _this.sidebarFold = true
            }else{
                _this.sidebarFold = false
            }
            window.onresize = function(){
                if(window.innerWidth < 1368){
                    _this.sidebarFold = true
                }else{
                    _this.sidebarFold = false
                }
            }
        },
        computed: {
            navbarLayoutType: {
                get () { return this.$store.state.common.navbarLayoutType }
            },
            sidebarFold: {
                get () { return this.$store.state.common.sidebarFold },
                set (val) { this.$store.commit('common/updateSidebarFold', val) }
            },
            // mainTabs: {
            //     get () { return this.$store.state.common.mainTabs },
            //     set (val) { this.$store.commit('common/updateMainTabs', val) }
            // },
            userName: {
                get () { return this.$store.state.user.name }
            }
        },
        methods: {
            // 修改密碼
            updatePasswordHandle () {
                this.updatePassowrdVisible = true
                this.$nextTick(() => {
                    this.$refs.updatePassowrd.init()
                })
            },
            // 退出
            logoutHandle () {
                this.$msgbox.confirm(`確定進行[退出]操作?`, '提示', {
                    confirmButtonText: '確定',closeOnClickModal:false,
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$http.post('/logout').then(res => {
                        if (res.status === 200) {
                            this.$store.commit('common/updateMainTabs', []);
                            this.$router.replace({ name: 'login' })
                        }
                    })
                }).catch(() => {})
            }
        }
    }
</script>
<style>
    .red{
        color: #db1818;
    }
    .black{
        color: #333333;
    }
    .mylogo{
        margin-left: -5px;
        margin-top: -3px;
    }
    .site-navbar{
        /*background-color:#FFFFFF!important;*/
        background-color:#fafafa!important;
    }
</style>
<style lang="scss" scoped>
    .site-navbar__brand-mini logo{
        width: 100%;
    }
</style>
