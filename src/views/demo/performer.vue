<template>
    <div>
        <div class="add-new">
            <el-button type="primary" v-if="isAuth('works:base:change')" @click="addData()">新 增</el-button>
        </div>
        <el-table
            :data="performerList"
            border
            stripe
            style="width: 100%">
            <el-table-column
                prop="id"
                label="Perf ID">
                <template slot-scope="scope">
                    <el-input v-model=scope.row.id readonly placeholder="雙擊查詢" @dblclick.native="artistInit(scope.$index,scope.row)"></el-input>
                </template>
            </el-table-column>
            <el-table-column
                prop="ipNameNo"
                label="Ip Name No">
                <template slot-scope="scope">
                    <el-input v-model=scope.row.ipNameNo readonly placeholder="雙擊查詢" @dblclick.native="artistInit(scope.$index,scope.row)"></el-input>
                </template>
            </el-table-column>
            <el-table-column
                prop="artistName"
                label="ArtistName">
                <template slot-scope="scope">
                    <!-- <input type="text" editable="false"/> -->
                    <el-input type="text" v-model=scope.row.artistName readonly placeholder="雙擊查詢" @dblclick.native="artistInit(scope.$index,scope.row)"></el-input>
                </template>
            </el-table-column>
            <el-table-column
                prop="chineseName"
                label="Chinese Name">
                <template slot-scope="scope">
                    <el-input v-model=scope.row.chineseName placeholder=""></el-input>
                </template>
            </el-table-column>
            <el-table-column
                prop="country"
                label="Country">
                <template slot-scope="scope">
                    <el-input v-model=scope.row.country placeholder=""></el-input>
                </template>
            </el-table-column>
            <el-table-column
                prop="inputSoc"
                label="Soc">
                <template slot-scope="scope">
                    <el-input v-model=scope.row.inputSoc placeholder=""></el-input>
                </template>
            </el-table-column>
            <el-table-column
                prop="perf"
                label="Perf"
                width="80px">
                <template slot-scope="scope">
                    <el-checkbox v-model=scope.row.perf>Perf</el-checkbox>
                </template>
            </el-table-column>
            <el-table-column
                prop="pub"
                label="Pub"
                width="80px">
                <template slot-scope="scope">
                    <el-checkbox v-model=scope.row.pub>Pub</el-checkbox>
                </template>
            </el-table-column>
            <el-table-column
                label="operation"
                width="100">
                <template slot-scope="scope">
                    <span class="spanright">
                        <i class="el-icon-delete" v-if="isAuth('works:base:change')" @click="deleteData(scope.$index,tableData1)"></i>
                    </span>
                </template>
            </el-table-column>
        </el-table>
        <!-- 表演者  用于performer -->
        <el-dialog :visible.sync="artistShow" title="查詢表演者" width="1000px" :close-on-click-modal="false">
            <div style="width: 650px;margin: auto;margin-bottom: 20px">
                <el-input placeholder="ID" @keyup.enter.native="artistSearch()" style="width: 100px" v-model.trim="selectArtist.id" class="input-with-select"></el-input>
                <el-input placeholder="Name" @keyup.enter.native="artistSearch()" v-model="selectArtist.name" style="width: 120px;"></el-input>
                <el-input placeholder="Ip Name No" @keyup.enter.native="artistSearch()" style="width: 140px" v-model.trim="selectArtist.ipNameNo" class="input-with-select"></el-input>
                <el-button type="primary" @click="artistSearch(1)">搜索</el-button>
                <el-button type="success" @click="addArtist">添加表演者</el-button>
                <span class="clear-search" @click="clearSearch()">清除搜索</span>
            </div>
            <el-table :empty-text="tableresult"   stripe :data="artistData">
                <el-table-column property="id" label="ID" width="140"></el-table-column>
                <el-table-column property="ipNameNo" label="ipNameNo"></el-table-column>
                <el-table-column property="firstName" label="firstName" ></el-table-column>
                <el-table-column property="lastName" label="lastName"></el-table-column>
                <el-table-column property="chineseName" label="chineseName" ></el-table-column>
                <el-table-column
                    label="operation"
                    width="100">
                    <template slot-scope="scope">
                        <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                            <i class="el-icon-check" @click="checkedArtist(scope.$index,scope.row)"></i>
                        </span>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                v-if="artistShow"
                background
                layout="prev, pager, next"
                :total=artistTotal @current-change="artistSearch">
            </el-pagination>
        </el-dialog>
    </div>
</template>
<script>
export default {
    props: {
        tableData: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            tableresult:' ',
            performerList: [],
            editIndex: 0,
            artistShow: false,
            artistTotal: 1,
            artistData: [],
            selectArtist: {
                id: '',
                name: '',
                ipNameNo: ''
            }

        }
    },
    methods: {
        addData() {
            let myArray = [{ id: '', ipNameNo: '', romanName: '', chineseName: '', country: '', soc: '', perf: '', pub: '' }]
            this.performerList = [...this.performerList, ...myArray]
        },
        deleteData(index) {
            this.$msgbox.confirm(`確定進行[删除]操作?`, '提示', {
                confirmButtonText: '確定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.performerList.splice(index, 1);
            })
        },
        clearSearch(){
            this.selectArtist = {};
            this.artistSearch();
        },
        artistInit (index, item) {
            this.artistIndex = index;
            this.artistShow = true;
            this.selectArtist = {
                id: item.id,
                name: item.artistName,
                ipNameNo: item.ipNameNo
            }
            this.artistSearch(1);
        },
        addArtist () {
            this.artistShow = false;
            this.$router.push({ name: 'addActor' });
        },
        artistSearch (page) {
            let ajaxData = {
                id: this.selectArtist.id,
                romanName: this.selectArtist.name ? this.selectArtist.name.trim() : '',
                ipNameNo: this.selectArtist.ipNameNo,
                page: {
                    pageNum: page ? page : 1,
                    pageSize: 10
                }
            }
            this.tableresult = '數據加載中...'
            this.$http.post('/wrk/artist/queryWrkArtistListByParams', ajaxData).then(res => {
                if (res.status === 200) {
                    this.artistData = res.data.list;
                    if(! this.artistData || this.artistData.length == 0){
                        this.tableresult = '暫無數據';
                    }
                    this.artistTotal = res.data.total||1;
                }
                    this.tableresult = this.artistData.length == 0 ? '暫無數據' : ' '
            })
        },
        checkedArtist (index, item) {
            this.$set(this.performerList[this.artistIndex], 'id', item.id);
            this.$set(this.performerList[this.artistIndex], 'ipNameNo', item.ipNameNo);
            this.$set(this.performerList[this.artistIndex], 'artistName', item.lastName + ' ' + item.firstName);
            this.$set(this.performerList[this.artistIndex], 'chineseName', item.chineseName);
            this.$set(this.performerList[this.artistIndex], 'country', item.country);
            this.$set(this.performerList[this.artistIndex], 'inputSoc', item.inputSoc);
            this.$set(this.performerList[this.artistIndex], 'refArtistIdMd5', item.refArtistIdMd5);
            this.$set(this.performerList[this.artistIndex], 'uniqueKeyMd5', item.uniqueKeyMd5);
            this.artistShow = false;
        },
        getData() {
            return this.$utils.copy(this.performerList);
        }
    },
    watch: {
        tableData: function(newVal, oldVal){
            this.performerList = this.$utils.copy(this.tableData);
        }
    },
    mounted(){
        this.performerList = this.$utils.copy(this.tableData);
    }

}
</script>
<style lang="scss" scoped>
@import "../../assets/scss/works.scss";
/deep/ .el-table{
    max-height: 320px !important;
}
.el-dialog{
    width: 1000px;
}
</style>
