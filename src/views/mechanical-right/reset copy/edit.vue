<template>
    <div class="p-t-40">
        <top-bar :noShow="false" @save="saveReset"></top-bar>
        <el-collapse v-model="activeNames">
            <!-- baseInfo -->
            <el-collapse-item title="Base Info" class="step-jump" name="1">
                <el-form :inline="true" ref="form" :model="baseInfo" :rules="rules" class="p-t-20" label-width="146px">
                    <div>
                        <el-form-item label="Producer Code" prop="producerId">
                            <el-input type="text" v-model="baseInfo.producerId" placeholder="雙擊查詢" @dblclick.native="getProduct" readonly></el-input>
                        </el-form-item>
                        <el-form-item label="Producer Name">
                            <el-input type="text" v-model="baseInfo.producerName" placeholder="雙擊查詢" @dblclick.native="getProduct" readonly></el-input>
                        </el-form-item>
                        <el-form-item label="Dist No" prop="distNo">
                            <el-input type="text" v-model="baseInfo.distNo"></el-input>
                        </el-form-item>
                    </div>
                    <div>
                        <el-form-item label="Amount" prop="amount">
                           <el-input type="text" v-model="baseInfo.amount"></el-input>
                        </el-form-item>
                        <el-form-item label="Exchange Rate" prop="exchangeRate">
                            <el-input type="text" v-model="baseInfo.exchangeRate"></el-input>
                        </el-form-item>
                        <el-form-item label="Local Equivalent">
                           <el-input type="text" v-model="baseInfo.localEquivlent" readonly></el-input>
                        </el-form-item>
                        <el-form-item label="Currency" prop="currency">
                            <el-input type="text" style="width: 80px;" v-model="baseInfo.currency" placeholder="雙擊查詢" readonly @dblclick.native="selectCurrency"></el-input>
                        </el-form-item>
                        <el-form-item>
                            <el-input type="text" style="width: 140px;" v-model="baseInfo.currencyName" readonly></el-input>
                        </el-form-item>
                        
                    </div>
                    <div>
                        <el-form-item label="Batch No" prop="batchNo">
                            <el-input type="text" placeholder="1-999的數值" v-model="baseInfo.batchNo" maxlength="3"></el-input>
                        </el-form-item>
                        <el-form-item label="Remark">
                            <el-input type="textarea" style="width: 600px;" v-model="baseInfo.remark"></el-input>
                        </el-form-item>
                    </div>
                    <div style="margin-top: 20px;">
                        <el-form-item label="Sc No" prop="scNo">
                           <el-input type="text" v-model="baseInfo.scNo"></el-input>
                        </el-form-item>
                        <el-form-item label="Sc Type" prop="typeCode">
                           <el-input type="text" v-model="baseInfo.typeCode"></el-input>
                        </el-form-item>
                        <el-form-item label="Country" prop="countryCode">
                            <el-input type="text" style="width: 80px;" v-model="baseInfo.countryCode" placeholder="雙擊查詢" @dblclick.native="getCountry()" readonly></el-input>
                        </el-form-item>
                        <el-form-item>
                            <el-input type="text" style="width: 140px;" v-model="baseInfo.countryName" readonly></el-input>
                        </el-form-item>
                    </div>
                    <div>
                        <el-form-item label="English Title">
                            <el-input type="text" style="width: 600px;" v-model="baseInfo.titleEn"></el-input>
                        </el-form-item>
                        <el-form-item label="Chinese Title">
                            <el-input type="text" style="width: 600px;" v-model="baseInfo.titleCh"></el-input>
                        </el-form-item>
                    </div>
                    <div>
                        <el-form-item label="Release Date" prop="releaseDate">
                            <!-- <el-input v-dateFmt v-model="baseInfo.releaseDate"></el-input> -->
                            <date-picker v-model="baseInfo.releaseDate" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd"></date-picker>
                        </el-form-item>
                        <el-form-item label="Receive Date" prop="recevieDate">
                            <!-- <el-input v-dateFmt v-model="baseInfo.recevieDate"></el-input> -->
                            <date-picker v-model="baseInfo.recevieDate" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd"></date-picker>
                        </el-form-item>
                    </div>
                    <!-- <div>
                        <el-form-item label="Remark">
                            <el-input type="textarea" style="width: 600px;"></el-input>
                        </el-form-item>
                    </div> -->
                </el-form>
            </el-collapse-item>
            <!-- right -->
            <el-collapse-item title="" class="step-jump" name="2">
                 <el-table :empty-text="tableresult"   stripe :data="salesTable" border>
                    <el-table-column label="From Period">
                        <template slot-scope="scope">
                            {{scope.row.fromPeriod && scope.row.fromPeriod.split(' ')[0]}}
                        </template>
                    </el-table-column>
                    <el-table-column label="To Period">
                        <template slot-scope="scope">
                            {{scope.row.toPeriod && scope.row.toPeriod.split(' ')[0]}}
                        </template>
                    </el-table-column>
                    <el-table-column prop="id" label="SC ID">
                    </el-table-column>
                    <el-table-column prop="batchSeqNo" label="Seq">
                    </el-table-column>
                    <el-table-column prop="tracks" label="Tracks">
                    </el-table-column>
                    <el-table-column prop="unitPrice" label="Unit Price">
                    </el-table-column>
                    <el-table-column prop="unitSold" label="Unit Sold">
                    </el-table-column>
                    <el-table-column prop="typeCode" label="Type">
                    </el-table-column>
                    <el-table-column prop="totalWorkRoy" label="Sum Sc Work Total" width="170px">
                        <!-- <template slot-scope="scope">
                            {{computedSum(scope.row.unitPrice, scope.row.tracks)}}
                        </template> -->
                    </el-table-column>
                    <el-table-column label="opteration">
                        <template slot-scope="scope">
                            <span class="operation">
                                <i class="el-icon-edit" @click="editData(scope.row, scope.$index)"></i>
                                <i class="el-icon-delete" @click="deleteData(scope.row, scope.$index)"></i>
                            </span>
                        </template>
                    </el-table-column>
                </el-table>
                <div class="add-new"><el-button type="primary" @click="addData(salesTable)">新 增</el-button></div>
            </el-collapse-item>
            <!-- end -->
        </el-collapse>
        <el-dialog
        title="幣別列表"
        :visible.sync="dialogVisible"
        width="50%"
         :close-on-click-modal="false"
        >
        <div class="search" style="margin-bottom: 20px;">
            <el-input placeholder="请输入currencyCode" v-model="currencyCode" class="input-with-select">
                <el-button slot="append" @click="getCurrencyList" icon="el-icon-search"></el-button>
            </el-input>
        </div>
        <el-table
            :data="currencyList"
            border
            height="500"
            v-loading="currencyLoading"
            style="width: 100%">
            <el-table-column
                prop="id"
                label="id"
                width="80">
            </el-table-column>
            <el-table-column
                prop="currencyCode"
                label="currencyCode"
                width="180">
            </el-table-column>
            <el-table-column
                prop="currencyName"
                label="currencyName"
                min-width="180">
            </el-table-column>
            <el-table-column
                label="operation">
                <template slot-scope="scope">
                    <i class="el-icon-check pointer" @click="chooseCurrency(scope.row)"></i>
                </template>
            </el-table-column>
        </el-table>
    </el-dialog>

        <select-product v-if="productTableVisible" ref="selectProduct" :search="productSearch" @checkProduct="checkProduct"></select-product>
        <select-country v-if="countryShow" :search="countrySearch" ref="selectCountry" @checkCountry="checkCountry"></select-country>
    </div>
</template>
<script>
import selectProduct from '../../../components/select-product/index'
import selectCountry from '@/components/select-country'
export default {
    data(){
        return {
            id: '',
            baseInfo: {},
            activeNames: ['1', '2'],
            salesTable: [],tableresult:' ',
            rules: {
                producerId: [{required: true, message: '請選擇Producer Code', trigger: 'change'}],
                distNo: [{required: true, message: '請輸入Dist No', trigger: 'blur'}],
                amount: [{required: true, message: '請輸入Amount', trigger: 'blur'}],
                exchangeRate: [{required: true, message: '請輸入Exchange Rate', trigger: 'blur'}],
                currency: [{required: true, message: '請選擇Currency', trigger: 'change'}],
                batchNo: [{required: true, message: '請輸入Batch No', trigger: 'blur'}],
                scNo: [{required: true, message: '請輸入Sc No', trigger: 'blur'}],
                typeCode: [{required: true, message: '請輸入Sc Type', trigger: 'blur'}],
                countryCode: [{required: true, message: '請選擇Country', trigger: 'change'}],
                releaseDate: [{required: true, message: '請輸入Release Date', trigger: 'blur'}],
                recevieDate: [{required: true, message: '請輸入Receive Date', trigger: 'blur'}]
            },
            countrySearch: {},
            countryShow: false,
            
            productTableVisible: false,
            productSearch: {},

            dialogVisible: false,
            currencyCode:'',
            currencyList: [],
            currencyLoading: false
        }
    },
    components: {
        selectProduct,
        selectCountry
    },
    created(){
        this.init();
        this.$bus.$on('updateReset', () => {
            this.updateSales();
        })
    },
    beforeDestroy(){
        this.$bus.$off('updateReset')
    },
    watch:{
        'baseInfo.amount': function(newVal, oldVal){
            this.computedLocal();
        },
        'baseInfo.exchangeRate': function(){
            this.computedLocal();
        }
    },
    methods: {
        computedLocal(){
            let amount = this.baseInfo.amount ? this.baseInfo.amount : 0;
            let exchangeRate = this.baseInfo.exchangeRate ? this.baseInfo.exchangeRate : 0;
            let local = new BigNumber(amount).multipliedBy(new BigNumber(exchangeRate)).toFixed(6);
            // return local;
            this.baseInfo.localEquivlent = local;
        },
        init(){
            this.id = this.$route.query.id;
            if(this.id){
                this.queryInfo();
            }else{
                this.baseInfo = {
                    producerId: '',
                    producerName: '',
                    currency: '',
                    currencyName: '',
                    countryCode: '',
                    countryName: ''
                }
            }


        },
        queryInfo(){
                              this.tableresult = '數據加載中...'
            this.$http.get('/dist/mrd/listDistMrdSalesWithPage/' + this.id).then( res => {
                if(res.success){
                    this.baseInfo = res.data.data.distMrdSales;
                    this.salesTable = res.data.data.distMrdSalesDetailsList;
                }
                    this.tableresult = this.salesTable.length == 0 ? '暫無數據' : ' '
            })
        },
        /**
         * order 页编辑或修改，返回此页面，表格数据要更新，但只更新表格数据
         */
        updateSales(){
                              this.tableresult = '數據加載中...'
            this.$http.get('/dist/mrd/listDistMrdSalesWithPage/' + this.id).then( res => {
                if(res.success){
                    // this.baseInfo = res.data.data.distMrdSales;
                    this.salesTable = res.data.data.distMrdSalesDetailsList;
                }
                    this.tableresult = this.salesTable.length == 0 ? '暫無數據' : ' '
            })
        },
        addData(data){
            if(!this.id){
                this.$toast({tips: '請先保存基本信息，再操作新增'})
                return;
            }
            // 添加销售订單，跳新頁面添加
            this.$router.push({name: 'orderAdd', query: {salesId: this.id, }})
        },
        deleteData(index, data){
            this.$msgbox.confirm('確認刪除?', '提示', {
                confirmButtonText: '確認',
                cancelButtonText: '取消',
                type: 'warning'
            }).then( () => {
                data.splice(index,1)
            })

        },
        editData(row, index){
            this.$router.push({name: 'orderEdit', query: {salesId: this.id, id: row.id, nameId: row.id, title: row.salesId}});
        },
        saveReset(){
            this.$refs.form.validate( (valid) => {
                if(valid){
                    this.saveResetFn();
                }
            })
        },
        saveResetFn(){
            let ajaxData = this.$utils.copy(this.baseInfo);
            this.$http.post('/dist/mrd/saveDistMrdSales', ajaxData).then( res => {
                if(res.data.code && res.data.code != 200){
                    this.$toast({tips: res.data.message})
                }else{
                    this.$toast({tips: '保存成功'});
                    this.$bus.$emit('closeCurrentTab', () => { this.$router.push({name: 'resetList', query: {update: true}})});
                }
            })
        },

        getCountry(){
            this.countrySearch = {
                tisN: this.baseInfo.countryCode
            }
            this.countryShow = true;
            this.$nextTick( () => {
                this.$refs.selectCountry.init();
            })
        },
        checkCountry(info){
            this.$set(this.baseInfo, 'countryCode', info.countryCode);
            this.$set(this.baseInfo, 'countryName', info.name);
        },

        /**
         *
         * 选取product
         */
        getProduct(){
            this.productTableVisible = true;
            this.$nextTick( () => {
                this.$refs.selectProduct.init();
            })

        },
        checkProduct(info){
            this.baseInfo.producerId = info.id;
            this.baseInfo.producerName = info.producerName;
        },

        /**
         * currency
         */
        // selectCurrency(){
        //     this.$http.get('/ref/getRefCurrency', {params: {currencyCode: this.baseInfo.currency}}).then( res => {
        //         if(res.success){
        //             if(!res.data.data || res.data.data.length < 1){
        //                 this.$toast({tips: '請填寫正確的Currency Code'})
        //             }else{
        //                 console.log(res.data.data[0].currencyName);
        //                 this.baseInfo.currencyName = res.data.data[0].currencyName;
        //             }
        //         }
        //     })
        // }
        selectCurrency(){
            this.dialogVisible = true
            this.getCurrencyList()
        },
        getCurrencyList(){
            this.currencyLoading = true
            this.$http.get('/ref/getRefCurrency?currencyCode='+this.currencyCode).then(res => {
                console.log(res)
                if(res.success && res.data.code === 200){
                    this.currencyList = []
                    res.data.data.map(item => {
                        this.currencyList.push(item)
                    })
                }
                this.currencyLoading = false
            })
        },
        chooseCurrency(row){
            this.$set(this.baseInfo, "currency", row.currencyCode)
            this.$set(this.baseInfo, "currencyName", row.currencyName)

            this.dialogVisible = false
        },
    }
}
</script>
<style lang="scss" scoped>
    @import '../../../assets/scss/works.scss';
</style>

