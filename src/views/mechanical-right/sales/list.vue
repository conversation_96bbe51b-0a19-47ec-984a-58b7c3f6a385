<template>
    <div>
        <el-form :inline="true" class="demo-form-inline" @keyup.enter.native="searchFn(1)">
            <el-form-item prop="">
                <el-input v-model="formInline.producerCode" placeholder="Producer Code" style="width: 200px;"></el-input>
            </el-form-item>
            <el-form-item prop="">
                <el-input v-model="formInline.id" placeholder="SC ID" style="width: 200px;"></el-input>
            </el-form-item>
            <el-form-item prop="">
                <el-input v-model="formInline.scNo" placeholder="SC No" style="width: 200px;"></el-input>
            </el-form-item>
            <el-form-item prop="">
                <el-input v-model="formInline.scTitle" placeholder="SC Title" style="width: 200px;"></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="searchFn(1)" v-if="isAuth('mechanical-right:sales:list:find')">查询</el-button>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="addFn()" v-if="isAuth('mechanical-right:sales:list:add')">添加</el-button>
            </el-form-item>
            <el-form-item>
                <span class="clear-search" @click="clearSearch()" v-if="isAuth('mechanical-right:sales:list:find')">清除搜索</span>
            </el-form-item>
        </el-form>
        <el-table
            :data="tableData"
            border
            stripe
            style="width: 100%"
            v-loading="loading"
            :empty-text="emptyText">
            <!-- <el-table-column
                prop="distNo"
                label="Dist No"
                min-width="100px">
            </el-table-column> -->
            <el-table-column
                prop="producerCode"
                label="Producer Code"
                min-width="140">
            </el-table-column>
            <el-table-column
                prop="producerName"
                label="Producer Name"
                min-width="140">
            </el-table-column>
            <el-table-column
                prop="id"
                label="SC ID"
                min-width="100">
            </el-table-column>
            <el-table-column
                prop="scNo"
                label="SC No"
                min-width="100">
            </el-table-column>
            <el-table-column
                prop="scTypeCode"
                label="Type"
                min-width="100">
            </el-table-column>
            <el-table-column
                prop="scTitleCh"
                label="SC Title"
                min-width="100">
            </el-table-column>
            <el-table-column
                prop="countryCode"
                label="Country"
                min-width="100">
            </el-table-column>
            
            <el-table-column
                label="OP"
                width="60px"
                fixed="right">
                <template slot-scope="scope">
                    <el-button type="text" size="small" @click="editFn(scope.row)" v-if="isAuth('mechanical-right:sales:list:change')">編輯</el-button>
                    <!-- <el-button type="text" size="small" @click="deleteFn(scope.row)">删除</el-button> -->
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :total="total" @current-change="searchFn" :current-page="currentPage">
        </el-pagination>
    </div>
</template>

<script>
    export default {
        name: 'list',
        data () {
            return {
                formInline: {
                    producerCode: '',
                    id: '',
                    scNo: '',
                    scTitle: '',
                    producerName: '',
                    page_num: 1,
                    page_size: 10
                },
                tableData: [],
                total: 0,
                currentPage: 1,
                loading: false,
                emptyText: '數據加載中',
            }
        },
        mounted () {
            this.searchFn();
        },
        activated(){
            // if(this.$route.query.update){
            //     this.searchFn();
            // }
            this.$nextTick( () => {
                if(this.$route.query.update){
                    let query = this.$route.query;
                    delete query.update;
                    this.searchFn();
                }
            })
        },
        methods: {
            clearSearch(){
                this.formInline = {
                    producerCode: '',
                    id: '',
                    scNo: '',
                    scTitle: '',
                    producerName: '',
                    page_num: 1,
                    page_size: 10
                }
                this.searchFn(1);
            },
            searchFn(page) {
                let ajaxData = this.$utils.copy(this.formInline);
                ajaxData.page_num = page ? page : 1;
                ajaxData.page_size = 10;
                this.loading = true;
                this.emptyText = '數據加載中';
                this.$http.get('/distMrdSc', {params: ajaxData}).then( res => {
                    this.loading = false;
                    if(res.success){
                        if(res.data.data){
                            this.tableData = res.data.data.list;
                            this.total = res.data.data.total;
                            this.currentPage = ajaxData.page_num;
                        }else{
                            this.tableData=[]
                            this.total=1
                            this.currentPage=1
                        }
                        if(! this.tableData || this.tableData.length == 0){
                            this.emptyText = '暫無數據';
                        }
                    }
                })
            },
            addFn(){
                this.$router.push({name: 'salesAdd'});
            },
            editFn (row) {
                console.log(row)
                localStorage.setItem('salesDetail', JSON.stringify(row))
                this.$router.push({name: 'salesEdit', query: {id: row.id, nameId: row.id, title: row.scNo}});
            },
            deleteFn () {
                this.$msgbox.confirm(`確定進行[删除]操作?`, '提示', {
                    confirmButtonText: '確定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {

                }).catch( () => {
                    
                })
            }
        }
        
    }
</script>
<style lang="scss" scoped>
/deep/ .el-dialog{
    width: 500px;
}
</style>

