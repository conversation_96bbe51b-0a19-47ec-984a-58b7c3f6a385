<template>
  <div>
    <el-dialog
      :visible.sync="socShow"
      width="700px"
      title="Society Code"
      :close-on-click-modal="false"
      :before-close="close"
    >
      <div style="margin: auto;margin-bottom: 20px; text-align: center;">
        <el-input @keyup.enter.native="socDataD()" v-model="search.societyCode" placeholder="Society Code" style="width: 200px;"></el-input>
        <el-input @keyup.enter.native="socDataD()" v-model="search.societyName" placeholder="Society Name" style="width: 200px;"></el-input>
        <el-button type="primary" @click="socDataD()">查詢</el-button>
        <span class="clear-search" @click="clearSearch()">清除搜索</span>
      </div>
      <el-table
        ref="multipleTable"
        :data="tableData"
        stripe
        :empty-text="emptyText"
      >
        <el-table-column
          property="societyCode"
          label="Society Code"
        >
        </el-table-column>
        <el-table-column property="societyName" label="Society Name"></el-table-column>
        <el-table-column label="operation" width="100">
          <template slot-scope="scope">
            <span
              style="
                width: 100%;
                display: inline-block;
                text-align: center;
                cursor: pointer;
              "
            >
              <i class="el-icon-check" @click="checked(scope.row)"></i>
            </span>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        layout="prev, pager, next"
        :total="total"
        @current-change="socDataD"
        :current-page="currentPage"
      >
      </el-pagination>
    </el-dialog>
  </div>
</template>
<script>
import qs from "qs";
import { Loading } from "element-ui";
export default {
  name: "",
  data() {
    return {
      socShow: false,
      tableData: [],
      emptyText: "暫無數據",
      total: 1,
      currentPage: 1,
      loading: null,
      search:{
        societyCode: '',
        societyName:''
      }
    };
  },
  props: {
    societyCode: {
      type: String,
      default: "",
    },
  },
  watch: {
    societyCode(data) {
      this.societyCode = data;
    },
  },
  methods: {
    clearSearch(){
      this.search={
        societyCode:'',
        societyName:''
      }
      this.socDataD(1)
    },
    close() {
      this.socShow = false;
      this.search={
        societyCode:'',
        societyName:''
      }
      this.$emit("flag");
    },
    checked(data) {
      this.$emit("checkSociet", data);
      this.socShow = false;
      this.search={
        societyCode:'',
        societyName:''
      }
    },
    socDataC(page) {
      this.loading = Loading.service({
        lock: true,
        text: "Loading",
        background: "rgba(0,0,0,0.3)",
      });
      let ajaxData = {
        data: {
          societyCode: this.societyCode,
        },
        page: {
          pageNum: page ? page : 1,
          pageSize: 10,
        },
      };
      this.emptyText = "數據加載中";
      this.$http.post("/ref/society/getSocietyList", ajaxData).then((res) => {
        console.log(res);
        if (res.success) {
          this.tableData = res.data.list;
          if (!this.tableData || this.tableData.length == 0) {
            this.emptyText = "暫無數據";
          }
          this.total = res.data.total;
          this.currentPage = page ? page : 1;
          if (this.tableData.length == 0) {
            this.$emit("checkSociet", {
              societyCode: "",
              name: "",
            });
            this.$toast({ tips: "未查詢到society code" });
            return;
          } else {
            this.$emit("checkSociet", this.tableData[0]);
          }
        }
      });
    },
    socDataD(page) {
      this.loading = Loading.service({
        lock: true,
        text: "Loading",
        background: "rgba(0,0,0,0.3)",
      });
      let ajaxData = {
        data: {
          societyCode:this.search.societyCode,
          societyName:this.search.societyName
        },
        page: {
          pageNum: page ? page : 1,
          pageSize: 10,
        },
      };
      this.emptyText = "數據加載中";
      this.$http.post("/ref/society/getSocietyList", ajaxData).then((res) => {
        console.log(res);
        if (res.success) {
          this.tableData = res.data.list;
          if (!this.tableData || this.tableData.length == 0) {
            this.emptyText = "暫無數據";
          }
          this.tableData.forEach(item=>{
            item.societyCode = item.societyCode.toString().length==1?'00'+item.societyCode:item.societyCode.toString().length==2?'0'+item.societyCode:item.societyCode
          })
          this.total = res.data.total;
          this.currentPage = page ? page : 1;
          this.socShow = true;
        } else {
          this.$toast({ tips: res.data.message });
          return;
        }
      });
    },
  },
};
</script>
<style scoped>
</style>