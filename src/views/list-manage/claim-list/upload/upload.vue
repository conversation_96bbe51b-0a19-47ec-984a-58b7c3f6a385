<template>
    <div class="upload">
        <top-bar :noShow="false" @save="fileUploadSubmit"></top-bar>
        <el-tabs v-model="isFile" type="border-card">
            <el-tab-pane label="文件導入" name="Y">
                <el-form :inline="true" ref="fileForm" :model="formType" class="demo-form-inline" label-width="140px"
                         label-position="right">
                    <div style="padding-left: 160px;margin-bottom: 20px">
                        <el-checkbox v-model="matchMark1">執行DSP匹配程序</el-checkbox>
                    </div>
                    <div>
                        <el-form-item label="Category Code" class="is-required">
                            <el-autocomplete
                                v-model="formType.categoryCode"
                                :fetch-suggestions="querySearchCategory"
                                placeholder="請輸入内容"
                                :trigger-on-focus="false"
                                @select="handleSelectCategory"
                                style="width:397px"
                            >
                                <template slot-scope="scope">
                                    <p>{{scope.item.categoryCode}}</p>
                                </template>
                            </el-autocomplete>
                        </el-form-item>
                    </div>
                    <div>
                        <el-form-item label="claimSetInfoId" class="is-required">
                            <el-input style="width:397px" v-model="formType.claimSetInfoId" type="number" readonly @dblclick.native="getMinimaList('',1)"
                                      placeholder="雙擊選擇"></el-input>
                        </el-form-item>
                    </div>
                    <div>
                        <el-form-item label="date">
                            <el-form-item prop="startTime" style="width:185px;display: inline-block">
                                <date-picker v-model="formType.startTime" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd"></date-picker>
                            </el-form-item>
                            -
                            <el-form-item prop="endTime" style="width:185px;display: inline-block">
                                <date-picker v-model="formType.endTime" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd"></date-picker>
                            </el-form-item>
                        </el-form-item>
                        <p class="tip">youtube需要填写起始时间</p>
                    </div>
                    <div style="padding-left: 16px;width:557px;text-align: right">
                        <el-form-item>
                            <el-upload
                                ref="fileUpload"
                                class="upload-demo"
                                drag
                                action="/list/uploadFile"
                                name="files"
                                :limit="1"
                                accept=".tsv,.csv,.xml,.txt"
                                :data="formType"
                                :auto-upload="false"
                                :on-change="fileChange"
                            >
                                <i class="el-icon-upload"></i>
                                <div class="el-upload__text">
                                    將文件(tsv,csv,xml,txt)拖到此處，或
                                    <em>點擊上传</em>
                                    <br>
                                    大於1G請從目錄導入
                                </div>
                                 <div class="el-upload__tip" slot="tip">支持单个文件上传</div>
                            </el-upload>
                        </el-form-item>
                    </div>
                </el-form>
            </el-tab-pane>
            <el-tab-pane label="從目錄中導入" name="N">
                <el-form :inline="true" :model="formType" class="demo-form-inline" label-width="140px"
                         label-position="left">
                    <div style="padding-left: 160px;margin-bottom: 20px">
                        <el-checkbox v-model="matchMark2">執行DSP匹配程序</el-checkbox>
                    </div>
                    <div>
                        <el-form-item label="Category Code" class="is-required">
                            <el-autocomplete
                                v-model="formType1.categoryCode"
                                :fetch-suggestions="querySearchCategory"
                                placeholder="請輸入内容"
                                :trigger-on-focus="false"
                                @select="handleSelectCategory"
                            >
                                <template slot-scope="scope">
                                    <p>{{scope.item.categoryCode}}</p>
                                </template>
                            </el-autocomplete>
                        </el-form-item>
                    </div>
                    <div>
                        <el-form-item label="claimSetInfoId" class="is-required">
                            <el-input v-model="formType1.claimSetInfoId" type="number" @dblclick.native="getMinimaList('',1)"
                                      placeholder="雙擊選擇"></el-input>
                        </el-form-item>
                    </div>
                    <div>
                        <el-form-item label="file path" class="is-required">
                            <el-input v-model="formType1.filePath"></el-input>
                        </el-form-item>
                    </div>
                </el-form>
            </el-tab-pane>
        </el-tabs>
        <el-dialog title="claimSetInfoId列表" :visible.sync="showMinima" width="80%" @close="closeMinima" :close-on-click-modal="false">
            <div style="margin-bottom: 20px">
                <el-form :inline="true" :model="minimaData" ref="form" class="demo-form-inline"  @keyup.enter.native='getMinimaList("",1)'>
                    <el-form-item prop="compnyName">
                        <el-input v-model="minimaData.company" placeholder="companyName"></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="getMinimaList('',1)">搜索</el-button>
                    </el-form-item>
                    <el-form-item>
                        <span class="clear-search" @click="clearSearch()">清除搜索</span>
                    </el-form-item>
                </el-form>
            </div>
            <el-table :empty-text="tableresult"   stripe :data="minimaTableData">
                <el-table-column property="id" label="id" width="50"></el-table-column>
                <el-table-column property="company" label="company" width="200"></el-table-column>
                <el-table-column property="mechanicalStartTime" label="mechanicalStartTime">
                   
                    
                </el-table-column>
                <el-table-column property="mechanicalEndTime" label="mechanicalEndTime">
                   
                    
                </el-table-column>
                <el-table-column property="publicTransmissionEndTime" label="publicTransmissionEndTime">
                   
                    
                </el-table-column>
                <el-table-column property="publicTransmissionStartTime" label="publicTransmissionStartTime">
                   
                    
                </el-table-column>
                <el-table-column
                    label="operation"
                    width="100">
                    <template slot-scope="scope">
                    <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                      <i class="el-icon-check" @click="chooseMinima(scope.row)"></i>
                    </span>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                background
                layout="prev, pager, next" :current-page="minimaData.page_num"
                :total='minimaTotal' @current-change="handleClick">
            </el-pagination>
        </el-dialog>
    </div>
</template>

<script>
    export default {
        data () {
            return {
                formType: {
                    categoryCode: '',
                    claimSetInfoId: '',
                    fileType:'C',
                    isFile:'Y',
                    filePath:''
                },
                formType1: {
                    categoryCode: '',
                    claimSetInfoId: '',
                    filePath:''
                },
                minimaData: {
                    company: '',
                    page_size:10,
                    page_num:1
                },
                minimaTableData: [],tableresult:' ',
                minimaTotal: 0,
                isFile: 'Y',
                matchMark1: true,
                matchMark2: true,
                showMinima: false,
                fileList: []
            }
        },
        mounted(){
            document.addEventListener('drop',function (e) {
                e.preventDefault()
            })
            document.addEventListener('dragover',function (e) {
                e.preventDefault()
            })
        },
        methods: {
            getMinimaList (e,page) {
                !this.showMinima && (this.showMinima = true)
                this.minimaData.page_num = page
                this.minimaData.status='1'
                // let params=this.minimaData
                      this.tableresult = '數據加載中...'
                this.$http.get('/claim', {params:this.minimaData}).then(res => {
                    if (res.success) {
                        this.minimaTableData = res.data.list
                        this.minimaTotal = res.data.total
                    }
                          this.tableresult = this.minimaTableData.length == 0 ? '暫無數據' : ' '
                })
               //   this.$http.get('/claim/minima', { params }).then(res => {
               //     下面这个是之前的接口--------
               //     this.$http.post('/claim/getClaimMinmaInfoList', this.minimaData).then(res => {
               //         console.warn('(((999999',res)
               //         res.success
               //       if (res.status === 200) {
               //           this.minimaTableData = res.data.list
               //                 this.minimaTotal = res.data.total
               //       this.miniTotal = res.data.total
               //       if (this.company.trim() == 'youtube' || this.company.trim() == 'google') {
               //         this.minimaTableData.forEach(item => {
               //           console.log('getminilist******', item)
               //           this[item.productFullName] = item
               //         })
               //       }
               //     }
               //   })
            },
            clearSearch(){
                this.minimaData = {
                    company: '',
                    page_size:10,
                    page_num:1
                }
                this.getMinimaList('',1)
            },
            chooseMinima (row) {
                this.showMinima = false
                this.minimaData = {
                    compnyName: '',
                    productFullName: '',
                    productShortName: '',
                    page_size:10,
                    page_num:1
                }
                if(this.isFile == 'Y'){
                    this.formType.claimSetInfoId = row.id
                }else{
                    this.formType1.claimSetInfoId = row.id
                }

            },
            handleClick (val) {
                this.getMinimaList('',val)
            },
            closeMinima(){
                this.minimaTotal = 0
            },
            fileChange (file, fileList) {
                console.log('ggg')
                console.log(file)
                if(file.size<1024*1024*1024){
                    let lastIndexOfDot = file.name.lastIndexOf('.')
                    let type = file.name.slice(lastIndexOfDot+1,file.name.length).toLowerCase()
                    let typeList = ['tsv','xml','csv','txt']
                    if(!typeList.includes(type)){
                        this.$toast({tips:'請上傳tsv,xml,csv,txt格式的文件'})
                        this.$refs['fileUpload'].clearFiles()
                        return
                    }
                    this.fileList = []
                    fileList.map(item => {
                        this.fileList.push(item.raw)
                    })
                }else{
                    this.$toast({tips:'大於1G請從目錄導入'})
                }
            },
            fileRemove(file,fileList){
                console.log(fileList)
            },
            querySearchCategory (queryString, cb) {
                if (queryString) {
                    this.$http.get('/list/categorys/getListCategoryByCode', {params: {categoryCode: queryString}}).then(res => {
                        console.log(res)
                        if (res.success) {
                            cb(res.data)
                        }
                    })
                }
            },
            handleSelectCategory (val) {
                if(this.isFile === 'Y'){
                    this.formType.categoryCode = val.categoryCode
                }else{
                    this.formType1.categoryCode = val.categoryCode
                }
            },
            fileUploadSubmit () {
                if(this.isFile === 'Y'){
                    this.saveFile()
                }else{
                    this.saveDirectory()
                }

            },


            saveFile(){
                let formData = new FormData()
                let validateList = ['categoryCode','claimSetInfoId']
                console.log(this.formType)
                for (let key in this.formType) {
                    if(validateList.includes(key)){
                        if(!this.formType[key]){
                            this.$message.error(`${key}不能為空`)
                            return false
                        }
                    }
                    formData.append(key, this.formType[key])
                }

                formData.append('matchMark', this.matchMark1?'Y':'N')
                console.log(this.formData)
                if(!this.fileList.length){
                    this.$message.error(`文件不能為空，請上傳清單文件`)
                    return
                }
                console.log(formData)
                this.fileList.map(item => {
                    formData.append('files', item)
                })
                const loading = this.$loading({
                    lock: true,
                    text: 'uploading',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)'
                });

                this.$http.post('/list/uploadFile', formData).then(res => {
                    loading.close()
                    if(res.success){
                        if(res.data.code == 200){
                            this.$message({
                                type:'success',
                                message:'上傳成功',
                                duration:1500,
                                onClose:() => {
                                    this.$bus.$emit('closeCurrentTab', () => {
                                        this.$router.push({name: 'claimUploadList'});
                                    })
                                }
                            })
                        }else{
                            this.$message.error(res.data.message)
                        }
                    }
                })
                // sd
            },
            saveDirectory(){
                this.formType1.matchMark = this.matchMark2?'Y':'N'
                this.$http.post('/claimFile/uploadCatalogFile',this.formType1).then(res => {
                    if(res.data.code == 200){
                        this.$message({
                            type:'success',
                            message:'上傳成功',
                            duration:1500,
                            onClose:() => {
                                this.$bus.$emit('closeCurrentTab', () => {
                                    this.$router.push({name: 'claimUploadList'});
                                })
                            }
                        })
                    }else{
                        this.$message.error(res.data.message)
                    }
                })
            }
        }
    }
</script>
<style lang="scss" scoped>
    .upload {
        padding-top: 40px;
    }

    .el-upload__tip {
        font-size: 14px;
        height: 20px;
        line-height: 20px;
        text-align: right;
        margin-top: 0;
    }

    /deep/ .el-upload-list__item-name {
        text-align: left;
    }

    /deep/ .el-tabs__content {
        padding-top: 40px;
    }
    .tip{
        margin: -15px 0 20px;
        font-size: 12px;
        color: #ccc;
        text-align: right;
        width: 557px;
    ;
    }
    .el-upload-dragger .el-upload__text{
        line-height: 25px;
    }
</style>
