<script>
import { Input } from 'element-ui';
import { formatThousand } from '@/utils';

// 千分位格式化函数 // AI写的脑残代码，不好用
// function formatThousand(value) {
//   if (!value) return '';
//   const [integerPart, decimalPart] = value.toString().split('.');
//   const formattedIntegerPart = integerPart
//     .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
//     .replace(/,/, ''); // 处理开头的逗号
//   return decimalPart
//     ? `${formattedIntegerPart}.${decimalPart}`
//     : formattedIntegerPart;
// }


// 高阶组件
const ThousandSeparatorInput = {
  // functional: true, // 声明为函数式组件
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    ...Input.props // 继承Input组件的props
  },
  data() {
    return {
      rawValue: null,
      inputValue: null
    }
  },
  watch: {
    value(nv, ov) {
      const nvStr = String(nv || '');
      if (this.rawValue !== nvStr) {
        this.onValueReceived(nvStr);
      }
    }
  },
  created() {
    this.onValueReceived(this.$props.value);
  },
  render: function (h) {
    const _this = this;
    const Input = Input;
    const props = {
      ...this.$props,
      value: _this.inputValue, // 格式化显示的值
      onInput: (val) => {
        // 处理输入，移除千分位分隔符，并触发原始的input事件
        // console.log(event)
        // console.log('--:', val);
        this.rawValue = val.replace(/,/g, '');
        // console.log('==:', rawValue);
        const formatedValue = formatThousand(this.rawValue);
        // console.log('**:', formatedValue);
        _this.inputValue = formatedValue;
        _this.$emit('input', this.rawValue);
      }
    };
    
    // 移除与格式化相关的props，避免传递给Input组件
    // delete props.formatThousand;

    return h(Input, {
      props,
      on: {
        ...this.$listeners,
        input: props.onInput // 覆盖input事件
      }
    });
  },
  methods: {
    onValueReceived(value) {
      this.rawValue = String(value || '').replace(/,/g, '');
      this.inputValue = formatThousand(this.rawValue);
    }
  }
};

export default ThousandSeparatorInput;
</script>
