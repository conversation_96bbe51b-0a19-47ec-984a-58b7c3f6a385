<template>
     <div class="actorbox">
          <el-form :inline="true" :model="formInline" class="demo-form-inline" @keyup.enter.native="onSubmit(1)">
               <div class="f-l">
                    <el-form-item>
                         <el-input v-model="formInline.orcalArtistId" placeholder="Performer ID" style="width: 120px"></el-input>
                    </el-form-item>
                    <el-form-item>
                         <el-input v-model="formInline.refArtistId" placeholder="Ref Artist ID" style="width: 120px"></el-input>
                    </el-form-item>
                    <el-form-item>
                         <el-input v-model="formInline.romanName" placeholder="Name" style="width: 120px"></el-input>
                    </el-form-item>
                    <el-form-item>
                         <el-input v-model="formInline.ipNameNo" placeholder="Ip Name No" style="width: 120px"></el-input>
                    </el-form-item>
                    <el-form-item>
                         <el-input v-model="formInline.ipBaseNo" placeholder="Ip Base No" style="width: 120px"></el-input>
                    </el-form-item>
                    <el-form-item>
                         <el-input v-model="formInline.country" placeholder="Country" style="width: 120px"></el-input>
                    </el-form-item>
                    <!-- <el-form-item>
                         <el-checkbox v-model="checked" style="width: 70px;">Band</el-checkbox>
                    </el-form-item> -->
                    <el-form-item>
                         <el-select v-model="formInline.isBand" placeholder="isBand" style="width: 120px;">
                              <el-option label="全部" value=""></el-option>
                              <el-option label="Band" :value="'Y'"></el-option>
                              <el-option label="notBand" :value="'N'"></el-option>
                         </el-select>
                    </el-form-item>
               </div>
               <div class="f-l">
                    <el-form-item>
                         <el-button type="primary" @click="onSubmit(1)">搜索</el-button>
                    </el-form-item>
                    <el-form-item>
                         <el-button type="primary" @click="actorAdd">新增</el-button>
                    </el-form-item>
                    <el-form-item>
                         <span class="clear-search" @click="clearSearch()">清除搜索</span>
                    </el-form-item>
               </div>
          </el-form>
          <el-table
               :data="tableData"
               border
               stripe
               style="width: 100%"
               :empty-text="emptyText">
               <el-table-column
                    prop="orcalArtistId"
                    label="Performer ID"
                    width="130px">
               </el-table-column>
               <el-table-column
                    prop="refArtistId"
                    label="Ref Artist ID"
                    width="130">
               </el-table-column>
               <!-- <el-table-column
                    prop="lastName"
                    label="LastName">
               </el-table-column>
               <el-table-column
                    prop="firstName"
                    label="FirstName"
                    min-width="100">
               </el-table-column> -->
               <el-table-column
                    prop="romanName"
                    label="Name"
                    min-width="200">
                    <template slot-scope="scope">
                         {{scope.row.artistName}}
                    </template>
               </el-table-column>
               <el-table-column
                    prop="chineseName"
                    label="ChineseName"
                    min-width="120">
               </el-table-column>
               <el-table-column
                    prop="ipNameNo"
                    label="IP Name no"
                    min-width="100">
               </el-table-column>
               <el-table-column
                    prop="isBand"
                    label="Band"
                    width="70">
               </el-table-column>
               <el-table-column
                    prop="country"
                    label="Country"
                    width="100">
               </el-table-column>
               <el-table-column
                    prop="bandName"
                    label="Band Name"
                    width="120">
               </el-table-column>
               <el-table-column
                    fixed="right"
                    label="OP"
                    width="180">
                    <template slot-scope="scope">
                         <span style="width: 100%;text-align: center;display: inline-block">
                              <span class="a-blue" @click="handleClick(scope.row)">編輯</span>
                              <span class="a-blue" @click="handleTransfer(scope.row)">transfer</span>
                         </span>
                        
                    </template>
               </el-table-column>
          </el-table>
          <el-pagination
               background
               :current-page='currentPage'
               layout="prev, pager, next"
               :total='total' @current-change="handleCurrentChange">
          </el-pagination>
     </div>
</template>

<script>
     // import axios from '../../utils/httpRequest'
     export default {
          name: 'actor',
          data () {
               return {
                    checked: true,
                    total: 1,
                    currentPage:1,
                    formInline: {
                         orcalArtistId: '',
                         refArtistId: '',
                         romanName: '',
                         ipBaseNo: '',
                         ipNameNo: '',
                         isBand: '',
                         country: '',
                         page: {
                              pageNum: 1,
                              pageSize: 10
                         }

                    },
                    tableData: [
                         // { pefid: '55157122', rpi: '55157122', name: 'ADVENTURER', firstname: '', chinesename: '', ipnn: '', band: '', country: '', bandname: '' }
                    ],
                    emptyText: '數據加載中',
               }
          },
          methods: {
               clearSearch() {
                    this.formInline = {
                         orcalArtistId: '',
                         refArtistId: '',
                         romanName: '',
                         ipBaseNo: '',
                         ipNameNo: '',
                         isBand: '',
                         country: '',
                         page: {
                              pageNum: 1,
                              pageSize: 10
                         }
                    };
                    this.onSubmit();
               },
               onSubmit (num) {
                    // if (this.checked) {
                    //      this.formInline.isBand = 'Y'
                    // }
                    // if (!this.checked) {
                    //      this.formInline.isBand = 'N'
                    // }
                    let params = this.formInline
                    params.page.pageNum = num ? num:1
                    this.emptyText = '數據加載中';
                    this.$http.post('/wrk/artist/queryWrkArtistListByParams', params).then(res => {
                         if (res.status === 200) {
                              this.tableData = res.data.list
                              if(! this.tableData || this.tableData.length == 0){
                                   this.emptyText = '暫無數據';
                              }
                              this.total = res.data.total||1
                              this.currentPage = params.page.pageNum
                              console.log(this.currentPage)
                         }
                    })
               },
               handleCurrentChange (val) {
                    this.formInline.page.pageNum = val
                    let params = this.formInline
                    this.emptyText = '數據加載中';
                    this.$http.post('/wrk/artist/queryWrkArtistListByParams', params).then(res => {
                         if (res.status === 200) {
                              this.tableData = res.data.list
                              if(! this.tableData || this.tableData.length == 0){
                                   this.emptyText = '暫無數據';
                              }
                              this.total = res.data.total
                              this.currentPage = this.formInline.page.pageNum
                         }
                    })
               },
               actorAdd () {
                    this.$router.push({ name: 'addActor' })
               },
               handleClick (item) {
                    console.log(item)
                    // this.$router.push({ name: 'editActor', query: {id: item.id} })
                    this.$router.push({name: 'editActor', query: {id: item.id, nameId: item.id, title: item.id}});
               },
               handleTransfer (item) {
                    console.log(item)
                    // this.$router.push({ name: 'editActor', query: {id: item.id} })
                    this.$router.push({name: 'transferActor', query: {id: item.id, nameId: item.id, title: item.id}});
               }
          },
          mounted () {
               // this.onSubmit()
          },
          activated(){
               this.onSubmit()
          }
     }
</script>

<style>

</style>
