<template>
    <div>
        <el-drawer
            :visible.sync="show"
            :wrapperClosable='false'
            direction="rtl"
            size="800"
            title="分行列表"
        >
            <el-form :inline="true" class="demo-form-inline" @keyup.enter.native="searchFn(1)">
                <el-form-item>
                <el-input v-model="formInline.branchNo" placeholder="branchNo" style="width: 120px;"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-input v-model="formInline.branchName" placeholder="branchName" style="width: 120px;"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="searchFn(1)">查詢</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="addItem()">新增</el-button>
                </el-form-item>
                <el-form-item>
                    <span class="clear-search" @click="clearSearch()">清除搜索</span>
                </el-form-item>
            </el-form>
            <el-table
                :data="branchTableData"
                border
                stripe
                style="min-width: 600px" 
                v-loading="branchLoading"
                :empty-text="emptyText">
                <el-table-column
                    prop="branchNo"
                    label="Branch No"
                    width="140">
                </el-table-column>
                <el-table-column
                    prop="branchName"
                    label="Branch Name"
                    class-name="branchName"
                >
                </el-table-column>
                <el-table-column
                    fixed="right"
                    label="operation"
                    width="130">
                    <template slot-scope="scope">
                        <div class="option">
                            <el-button @click.native.stop="branchEdit(scope.row)" type="text" size="small">編輯</el-button>
                            <el-button @click.native.stop="branchDelete(scope.row, scope.$index)" type="text" size="small">刪除</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                background
                layout="prev, pager, next"
                :total='total' @current-change="searchFn" :current-page="branchPage">
            </el-pagination>
        </el-drawer>
        <el-dialog :title="isBranchEdit?'編輯':'新增'" :visible="branchShow" width="500px" :show-close="false" :close-on-click-modal="false">
            <el-form label-position="right" label-width="140px" ref="branchForm" :model="addData" class="demo-form-inline" :rules="rules" @keyup.enter.native="addBranchItem">
                <el-form-item label="Bank No" prop="bankNo">
                    <el-input v-model="addData.bankNo" placeholder="Bank No" readonly=""></el-input>
                </el-form-item>
                <el-form-item label="Branch No" prop="branchNo">
                    <el-input v-model="addData.branchNo" placeholder="Branch No"></el-input>
                </el-form-item>
                <el-form-item label="Branch Name" prop="branchName">
                    <el-input v-model="addData.branchName" placeholder="Branch Name"></el-input>
                </el-form-item>
            </el-form>
            <div style="text-align: center;margin-top: 50px;">
                <el-button @click="branchCancelDialog">取消</el-button>
                <el-button type="primary" @click="addBranchItem">確定</el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
export default {
    name:'',
    prop:{

    },
    watch:{

    },
    data(){
        return{
            bankNo:'',
            show:false,
            branchShow:false,
            formInline:{
                branchName:'',
                branchNo:'',
                bankNo:''
            },
            addData:{
                branchName:'',
                branchNo:'',
                bankNo:''
            },
            branchTableData:[],
            branchLoading:false,
            branchPage:1,
            total:1,
            isBranchEdit:false,
            rules:{
                branchName:[{ required: true, message: '请输入Branch Name', trigger: 'blur' }],
                branchNo:[{ required: true, message: '请输入Branch No', trigger: 'blur' }],
                bankNo:[{ required: true, message: '请输入Bank No', trigger: 'blur' }],
            },
        
            emptyText: '暫無數據',
        }
    },
    methods:{
        getBranch(bankNo){
            this.bankNo = bankNo
            this.formInline.bankNo = bankNo
            this.show=true
            this.$nextTick(()=>{
                this.searchFn(1)
            })
        },
        searchFn(page){
            let params = this.$utils.copy(this.formInline);
            console.log(this.bankNo)
            console.log(this.formInline)
            params.page_num = page ? page : 1;
            params.page_size = 10;
            this.branchLoading = true;
            this.emptyText = '數據加載中';
            this.$http.get('/ref/bank/branch/page',{params}).then(res => {
                this.branchLoading = false;
                if(res.success){
                    if (res.data.code == 200) {
                        this.branchTableData = res.data.data.list || [];
                        this.total = res.data.data.total
                        this.branchPage =  params.page_num;
                    }else{
                        this.branchTableData=[]
                        this.total=1
                        this.branchPage=1
                        this.$toast({ tips: res.data.message });
                    }
                    if(! this.branchTableData || this.branchTableData.length == 0){
                        this.emptyText = '暫無數據';
                    }
                }
            })
        },
        clearSearch(){
            this.formInline={
                branchName:'',
                branchNo:'',
                bankNo:this.bankNo
            }
            this.searchFn()
        },
        addItem(){
            this.branchShow = true
            this.isBranchEdit = false
            this.addData = {
                branchName:'',
                branchNo:'',
                bankNo:this.bankNo
            }
            if(this.$refs.bankForm){
                this.$refs.branchForm.resetFields()
            }
        },
        branchEdit(row){
            this.branchShow = true;
            this.isBranchEdit = true;
            this.addData = {
                id:row.id,
                branchName:row.branchName,
                branchNo:row.branchNo,
                bankNo:this.bankNo
            }
        },
        branchDelete(row, index){
            this.$msgbox.confirm('確定刪除?', '提示', {
                confirmButtonText: '確定',
                cancelButtonText: '取消',
                type: 'warning',
                closeOnClickModal: false
            }).then(() => {
                this.branchDeleteFn(row, index);
            }).catch(() => {        
            });
        },
        branchDeleteFn(row, index){
            this.$http.delete('/ref/bank/branch/'+ row.id).then( res => {
                if(res.success){
                    // this.tableData.splice(index-1, 1);
                    this.searchFn(this.branchPage)
                    this.$toast({tips: '刪除成功'})
                }    
            })
        },
        branchCancelDialog(){
            this.branchShow = false 
            this.$refs.branchForm.resetFields()
        },
        addBranchItem(){
            this.$refs.branchForm.validate(validate => {
                if(validate){
                    this.$http.post('/ref/bank/branch/edit',this.addData).then(res => {
                        if(res.success){
                            this.$message({
                                message:res.data.message,
                                type:res.data.code === 200?'success':'warning'
                            })
                            if(res.data.code === 200){
                                this.branchCancelDialog()
                                this.searchFn()
                            }
                        }
                    })
                }
            })
        },
    }
}
</script>
<style scoped>
>>> .el-drawer__header{
    margin-bottom: 0;
    font-weight: bold;
    outline: 0;
}
>>> .el-drawer__header span{
    outline: 0;
}
>>> .el-drawer__body{
    padding: 20px;
}
/* >>> .el-table .branchName .cell{
    text-align: left;
} */
</style>