<template>
    <div>
        <el-form :inline="true" class="search-form" @keyup.enter.native="queryList(1)">
            <el-form-item>
              <el-input
                type="text"
                v-model="searchData.distNo"
                placeholder="Dist No"
                style="width: 160px"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-input
                type="text"
                v-model="searchData.ipBaseNo"
                placeholder="Ip Base No"
                style="width: 160px"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-input
                type="text"
                v-model="searchData.paNameNo"
                placeholder="PA Name No"
                style="width: 160px"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-input
                type="text"
                v-model="searchData.name"
                placeholder="Name"
                style="width: 250px"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="queryList(1)">搜索</el-button>
            </el-form-item>
            <el-form-item>
              <span class="clear-search" @click="clearSearch()">清除搜索</span>
            </el-form-item>
        </el-form>
        <el-table
            :data="tableData"
            @row-dblclick='changeData'
            border
            stripe
            style="width: 100%" 
            class="table-fixed"
            v-loading="loading"
            :empty-text="emptyText">
            <el-table-column
                prop="ipBaseNo"
                label="Ip Base No"
                width="200">
            </el-table-column>
            <el-table-column
                prop="paNameNo"
                label="PA Name No"
                width="200">
                <template slot-scope="scope">
                    <span class="el-sl" :title="scope.row.paNameNo">{{scope.row.paNameNo}}</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="paName"
                label="Name">
            </el-table-column>
            <el-table-column
                prop="payAmount"
                label="Net Payment"
                width="220">
                <template slot-scope="scope">
                    <span class="el-sl">{{scope.row.payAmount}}</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="invNo"
                label="Inv No"
                width="220">
                <template slot-scope="scope">
                    <el-input type="text" v-model="scope.row.invNo" placeholder="Inv No" @dblclick="changeData(scope.row)"></el-input>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :total='total '
            @current-change="queryList"
            :current-page="currentPage">
        </el-pagination>
        <el-dialog title="發票明細" :visible.sync="detailsVisible" width="700px" :close-on-click-modal="false">
            <el-table
              :data="detailData"
              border
              stripe
              style="width: 100%" 
              class="table-fixed"
              v-loading="loading"
              :empty-text="emptyText1">
              <el-table-column
                  prop="distNo"
                  label="Dist No"
                  min-width="180">
              </el-table-column>
              <el-table-column
                  prop="invNo"
                  label="Inv No"
                  width="150">
                  <template slot-scope="scope">
                      <el-input type="text" v-model="scope.row.invNo" placeholder="Inv No"></el-input>
                  </template>
              </el-table-column>
              <el-table-column
                  prop="payAmount"
                  label="Net Payment"
                  width="180">
                  <template slot-scope="scope">
                      <span class="el-sl">{{scope.row.payAmount}}</span>
                  </template>
              </el-table-column>
              <el-table-column
                  prop="pay"
                  label="Apply"
                  width="100">
                  <template slot-scope="scope">
                      <!-- <span class="el-sl">{{scope.row.pay}}</span> -->
                      <el-checkbox :value="scope.row.pay" true-label="Y" false-label="N" :disabled='scope.row.pay=="Y"' @change="editConfirm(scope.row)"></el-checkbox>
                  </template>
              </el-table-column>
            </el-table>
            <el-pagination
              background
              layout="prev, pager, next"
              :total='total1 '
              @current-change="getDetailList"
              :current-page="currentPage1">
            </el-pagination>
            <!-- <span slot="footer" class="dialog-footer">
              <el-button @click="cancelEdit">取 消</el-button>
              <el-button type="primary" @click="editConfirm">確 定</el-button>
            </span> -->
        </el-dialog>
    </div>
</template>
<script>
export default {
    name:'invoice',
    data(){
        return{
            detailsVisible:false,
            searchData:{
                distNo:'',
                ipBaseNo:'',
                name:'',
                paNameNo:'',
                id:'',
            },
            pid:this.$route.query.id,
            tableData:[],
            detailData:[],
            total: 0,
            total1: 0,
            currentPage: 1,
            currentPage1: 1,
            loading: false,
            checkData:{},
            emptyText: '暫無數據',
            emptyText1: '暫無數據',
        }
    },
    mounted (){
      this.queryList()
    },
    watch:{
      detailsVisible(){
        if(!this.detailsVisible)this.queryList(this.currentPage)
      }
    },
    activated(){
      //   if(this.$route.query.update){
      //       this.init()
      //       this.getCcidList()
      //   }
      this.$nextTick( () => {
          if(this.$route.query.id){
            console.log(this.$route.query.id)
            console.log('this.$route.query.id')
            this.clearSearch()
        }
      })
    },
    methods:{
      incNoDB(){

      },
      changeData(row, column, event){
        console.log(row)
        this.checkData = row
        this.detailsVisible=true
        this.getDetailList(1)
      },
      getDetailList(page){
        console.log(this.checkData)
        let ajaxData = {
          ipBaseNo:this.checkData.ipBaseNo,
          pageNum: page?page:1,
          pageSize: 10
        }
        const loading = this.$loading()
        this.emptyText1 = '數據加載中';
        this.$http
          .get('/dist/autopay/salesTaxBase/detailList', {params: ajaxData})
          .then(res => {
              if (res.success) {
                  console.log(res.data.data)
                  this.detailData = res.data.data.list
                  if(! this.detailData || this.detailData.length == 0){
                    this.emptyText1 = '暫無數據';
                  }
                  this.detailData.forEach(item=>{
                    if(!item.invNo){
                      item.invNo = this.checkData.invNo
                    }
                  })
                  
                  this.total1 = res.data.data.total;
                  this.currentPage1 = num;
              }
              loading.close()
          }).catch((e) => {
              loading.close()

        })
      },
      cancelEdit(){
        this.detailsVisible=false
      },
      editConfirm(data){
        console.log(data)
        // if(!data.pay || data.pay=='N'){
            data.pay='Y'
            data.pid=this.pid
            this.$http
              .post(`/dist/autopay/salesTaxBase/detaiSave/${this.pid}`,data)
              .then(res => {
                  if (res.success) {
                    if(res.data.code==200){
                        this.$toast({tips: '操作成功'})
                      }else{
                        data.pay='N'
                        this.$toast({tips: res.data.message})
                      }
                  }
              })
        // }
      },
      clearSearch(){
          this.searchData={
              distNo:'',
              ipBaseNo:'',
              name:'',
              paNameNo:'',
              id:this.$route.query.id,
          }
          this.queryList()
      },
      queryList(num){
          let ajaxData = {
              page_num: num ? num : 1,
              page_size: 10
          }
          ajaxData = Object.assign(ajaxData, this.searchData)
          const loading = this.$loading()
          this.emptyText = '數據加載中';
          this.$http
            .get('/dist/autopay/salesTaxBase/viewList', {params: ajaxData})
            .then(res => {
                if (res.success) {
                    console.log(res.data.data)
                    this.tableData = res.data.data.list
                    if(! this.tableData || this.tableData.length == 0){
                      this.emptyText = '暫無數據';
                    }
                    this.total = res.data.data.total;
                    this.currentPage = num;
                }
                loading.close()
            }).catch((e) => {
                loading.close()
        })
      }
    }
}
</script>
<style scoped>

</style>