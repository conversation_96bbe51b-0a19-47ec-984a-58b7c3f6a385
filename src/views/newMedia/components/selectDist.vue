<template>
<div>
    <el-dialog :visible.sync="show" width="1000px" title="選擇分配編號" :close-on-click-modal="false">
        <div style="width: 800px;margin: auto;margin-bottom: 20px">
            <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
                <el-form-item label="分配編號">
                    <el-input style="width:150px" v-model="searchInfo.distNo" placeholder=""></el-input>
                </el-form-item>
                <!-- <el-form-item label="分配類別">
                    <el-select style="width:150px" v-model="searchInfo.distType" placeholder="Type">
                        <el-option label="所有" value=""></el-option>
                        <el-option label="P" value="P"></el-option>
                        <el-option label="M" value="M"></el-option>
                        <el-option label="I" value="I"></el-option>
                        <el-option label="O" value="O"></el-option>
                    </el-select>
                </el-form-item> -->
                <el-form-item>
                    <el-button type="primary" @click="onSubmit()">搜索</el-button>
                </el-form-item>
                <el-form-item>
                    <span class="clear-search" @click="clearSearch()">清除搜索</span>
                </el-form-item>
            </el-form>
        </div>
        <el-table  :data="tableData" stripe :empty-text="emptyText">
            <el-table-column prop="distNo" label="分配編號" > </el-table-column>
            <el-table-column prop="description" label="Description"></el-table-column>
            <el-table-column
                label="operation"
                width="100">
                <template slot-scope="scope">
                    <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                        <i class="el-icon-check" @click="checkedWork(scope.$index,scope.row)"></i>
                    </span>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :total="total" @current-change="onSubmit" :current-page="currentPage">
        </el-pagination>
    </el-dialog>
</div>
</template>
<script>
export default {
    data(){
        return{
            show:false,
            searchInfo: {
                distNo:'',
            },
            tableData: [],
            total: 0,
            currentPage: 1,
            emptyText: '數據加載中'
        }
    },
    props: {
        search: {
            type: Object,
            default: () => {
                return {
                    distNo:'',
                }

            }
        }
    },
    watch: {

    },

    methods: {

        /**
         * dialog 组件调用方式：
         * 1、父级用v-if在html里加载组件,
         * 2、父级调用子级的init方法，将数据初始化，prop传给data等
         *
         */
        init(){
            this.show = true;
            this.searchInfo = this.$utils.copy(this.search);
            this.onSubmit();
        },
        clearSearch(){
            this.searchInfo = {
                distNo:'',
                distType:'',
            };
            this.onSubmit();
        },
        onSubmit(page){
            let ajaxData = this.$utils.copy(this.searchInfo);
            ajaxData.distType='M'
            ajaxData.page_num = page ? page : 1;
            ajaxData.page_size = 10;
            this.loading = true;
            this.emptyText = '數據加載中';
            this.$http.get('/dist/media/listDistParamInfoVoPage', { params: ajaxData }).then(res => {
            // this.$http.get('/dist/param/info', { params: ajaxData }).then(res => {
              this.loading = false;
              if (res.success) {
                console.log(res.data)
                this.tableData = res.data.data.list;
                this.total = res.data.data.total;
                this.currentPage = ajaxData.page_num;
                if(!this.total){
                    this.emptyText = '暫無數據';
                }
              }
            })
        },
        checkedWork(index, row){
            this.$emit('checkDist', row);
            this.show = false;
        }
    }

}
</script>
<style lang="scss" scoped>
    /deep/ .el-dialog__title{
        font-weight: bold;
    }
    /deep/ .el-dialog__body{
        text-align: center;
        padding-top: 0;
    }
     /deep/ tr.current>td{
        background-color: #17B3A3 !important;
    }
</style>
