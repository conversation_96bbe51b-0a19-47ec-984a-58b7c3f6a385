<template>
    <div>
        <el-form :inline="true" class="demo-form-inline">
            <el-form-item>
                <el-button type="primary" @click="addFn()" v-if="isAuth('mechanical-right:product:list:add')">添加</el-button>
            </el-form-item>
        </el-form>
        <el-table
            :data="tableData"
            border
            stripe
            style="width: 100%"
            v-loading="loading"
            :empty-text="emptyText">
            <el-table-column
                prop="id"
                label="產品编碼"
                width="100px">
                <template slot-scope="scope">
                    <span :title="scope.row.producerCode">{{scope.row.producerCode}}</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="producerName"
                label="產品名稱">
                <template slot-scope="scope">
                    <span :title="scope.row.producerName">{{scope.row.producerName}}</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="companyName"
                label="公司名稱">
                <template slot-scope="scope">
                    <span :title="scope.row.companyName">{{scope.row.companyName}}</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="producerAddress"
                label="地址">
                <template slot-scope="scope">
                    <span :title="scope.row.producerAddress">{{scope.row.producerAddress}}</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="telNo"
                label="Tel">
                <template slot-scope="scope">
                    <span :title="scope.row.telNo">{{scope.row.telNo}}</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="email"
                label="E-mail">
                <template slot-scope="scope">
                    <span :title="scope.row.email">{{scope.row.email}}</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="attention"
                label="Attention"
                min-width="100">
                <template slot-scope="scope">
                    <span :title="scope.row.attention">{{scope.row.attention}}</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="label"
                label="Label">
                <template slot-scope="scope">
                    <span :title="scope.row.label">{{scope.row.label}}</span>
                </template>
            </el-table-column>
            <el-table-column
                label="OP"
                width="80px">
                <template slot-scope="scope">
                    <el-button type="text" size="small" @click="editFn(scope.row)" v-if="isAuth('mechanical-right:product:list:change')">編輯</el-button>
                    <!-- <el-button type="text" size="small" @click="deleteFn(scope.row)">删除</el-button> -->
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :total="total" @current-change="searchFn" :current-page="currentPage">
        </el-pagination>
        <!-- 編輯、添加 -->
        <el-dialog :title="(opType == 'add' ? '添加' : '編輯')" :visible.sync="show" :close-on-click-modal="false">
            <el-form ref="editForm" :inline="true" :model="edit" label-width="160px" @keyup.enter.native="confirmEdit">
                <!-- <el-form-item label="產品编碼">
                    <el-input type="text" v-model="edit.versionName"></el-input>
                </el-form-item> -->
                <el-form-item label="產品名稱" required>
                    <el-input type="text" v-model="edit.producerName" maxlength="60"></el-input>
                </el-form-item>
                <el-form-item label="公司名稱" required>
                    <el-input type="text" v-model="edit.companyName"></el-input>
                </el-form-item>
                <el-form-item label="地址">
                    <el-input type="text" v-model="edit.producerAddress"></el-input>
                </el-form-item>
                <el-form-item label="Tel">
                    <el-input type="text" v-model="edit.telNo"></el-input>
                </el-form-item>
                <el-form-item label="E-mail">
                    <el-input type="text" v-model="edit.email"></el-input>
                </el-form-item>
                <el-form-item label="Attention">
                    <el-input type="text" v-model="edit.attention"></el-input>
                </el-form-item>
                <el-form-item label="Label">
                    <el-input type="text" v-model="edit.label"></el-input>
                </el-form-item>
                <div class="t-c">
                    <el-form-item>
                        <el-button type="primary" @click="confirmEdit">確定</el-button>
                    </el-form-item>
                </div>
            </el-form>
        </el-dialog>
    </div>
</template>

<script>
    export default {
        name: 'list',
        data () {
            return {
                tableData: [],
                total: 0,
                currentPage: 1,

                // dialog
                show: false,
                opType: 'add',
                edit: {
                    producerName: '',
                    companyName: '',
                    producerAddress: '',
                    telNo: '',
                    email: '',
                    attention: '',
                    label: ''
                },
                loading: false,
                emptyText: '數據加載中',
            }
        },
        mounted () {
            this.searchFn();
        },
        methods: {
            searchFn(page) {
                let ajaxData = {};
                ajaxData.page_num = page ? page : 1;
                ajaxData.page_size = 10;
                this.loading = true;
                this.emptyText = '數據加載中';
                this.$http.get('/dist/mrd/listDistDistMrdProducerWithPage', {params: ajaxData}).then( res => {
                    this.loading = false;
                    if(res.success){
                        this.tableData = res.data.data.list;
                        this.total = res.data.data.total;
                        this.currentPage = ajaxData.page_num;
                        if(! this.tableData || this.tableData.length == 0){
                            this.emptyText = '暫無數據';
                        }
                    }
                })
            },
            addFn(){
                this.opType = 'add';
                this.show = true;
                this.edit = {
                    producerName: '',
                    companyName: '',
                    producerAddress: '',
                    telNo: '',
                    email: '',
                    attention: '',
                    label: ''
                };
            },
            editFn (row) {
                this.opType = 'edit';
                this.edit = this.$utils.copy(row);
                this.show = true;
            },
            confirmEdit(){
                if(!this.edit.producerName){
                    this.$toast({tips: '產品名稱不能為空'})
                    return
                }else if(!this.edit.companyName){
                    this.$toast({tips: '公司名稱不能為空'})
                    return
                }
                let ajaxData = this.edit;
                this.$http.post('/dist/mrd/saveDistMrdProducer', ajaxData).then(res => {
                    if(res.success){
                        if(res.data.code && res.data.code != 200){
                            this.$toast({tips: res.data.message});
                        }else{
                            this.$toast({tips: (this.opType == 'add'? '添加' : '編輯') +'成功'});
                            this.show = false;
                            this.searchFn(this.currentPage);
                        }
                    }
                })
                

            },
            deleteFn () {
                this.$msgbox.confirm(`確定進行[删除]操作?`, '提示', {
                    confirmButtonText: '確定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$http.delete('' + row.id).then( res => {
                        if(res.success){
                            this.$toast({tips: '删除成功'})
                            this.tableData.splice(index, 1);
                        }
                    })
                }).catch( () => {
                    
                })
            }
        }
        
    }
</script>
<style lang="scss" scoped>
/deep/ .el-dialog{
    width: 500px;
}
/deep/ .el-dialog__body{
    padding-top: 14px;
}
</style>

