<template>
  <div class="ipibpox">
    <el-form :inline="true" :model="search" ref="form" class="demo-form-inline"  @keyup.enter.native='searchFn(1)'>
      <el-form-item label="分配代號">
        <el-input v-model="search.distNo" placeholder="distNo" style="width: 100px;"></el-input>
      </el-form-item>
      <el-form-item label="⽔單ID">
        <el-input v-model="search.receiptId" placeholder="receiptId" style="width: 100px;"></el-input>
      </el-form-item>
      <el-form-item label="協會名稱" >
          <el-input v-model.number="search.societyName" placeholder="雙擊查詢" @dblclick.native="getSocName()" @change="changeSoc" style="width: 100px;" readonly></el-input>
      </el-form-item>
      <el-form-item label="多重排序">
        <el-select v-model="search.orderList" style="width: 400px" multiple placeholder="選擇排序">
          <el-option
            v-for="(item,index) in orderSelections"
            :key="`k_${index||'-1'}`"
            :label="item.name"
            :value="item.value"
            :disabled="!!search.orderList.filter((si) => si.indexOf(item.prop) !== -1).length"
            >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="searchFn(1)" v-if="isAuth('list-manage:o-list:list:find')">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <span class="clear-search" @click="clearSearch()" v-if="isAuth('list-manage:o-list:list:find')">清除搜索</span>
      </el-form-item>

    </el-form>
    <el-table ref="dataTable" :empty-text="tableresult" stripe :data="tableData" border style="width: 100%"
              @row-dblclick="handleRowDblClick" @sort-change="handleSortChange" row-key="receiptId"
              highlight-current-row @expand-change="handleExpandChange">
      <el-table-column type="expand">
        <template slot-scope="scope">
          <div class="detail-drawer-container"
               v-loading="detailLoading && currentLoadingRow === scope.row.receiptId"
               element-loading-text="正在加载详情数据..."
               element-loading-spinner="el-icon-loading"
               element-loading-background="rgba(255, 255, 255, 0.8)">
            <detail-drawer :row-data="scope.row"></detail-drawer>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="distNo" label="分配代號" min-width="90" sortable="custom">
      </el-table-column>
      <el-table-column prop="receiptId" label="水單ID" sortable="custom">
        <template slot-scope="scope">
          <span style="color:red" v-if="scope.row.starMark=='Y'">*</span>
          <span :title="scope.row.receiptId">{{scope.row.receiptId}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="receiptDate" label="匯款日期" min-width="120" sortable="custom">
        <template slot-scope="scope">
          {{formatTime(scope.row.receiptDate)}}
        </template>
      </el-table-column>
      <el-table-column prop="sourceSocietyCode" label="協會代碼" min-width="120" sortable="custom">
      </el-table-column>
      <el-table-column prop="sourceSocietyName" label="協會名稱" min-width="120" sortable="custom">
      </el-table-column>
      <el-table-column prop="distOrderNumber" label="協會分配序號" min-width="120" sortable="custom">
      </el-table-column>
      <el-table-column prop="sourceDistNo" label="國外分配代號" min-width="120" sortable="custom">
      </el-table-column>
      <el-table-column prop="receiptCurrencyCode" label="外幣幣別" sortable="custom">
      </el-table-column>
      <el-table-column prop="receiptAmount" label="外幣金額" min-width="90" align="right" sortable="custom">
        <template slot-scope="scope">
          <span :title="scope.row.receiptAmount">{{formatThousand(scope.row.receiptAmount?scope.row.receiptAmount:0, 2)}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="localAmount" label="台幣金額" min-width="90" align="right" sortable="custom">
        <template slot-scope="scope">
          <span :title="scope.row.localAmount">{{formatThousand(scope.row.localAmount?scope.row.localAmount:0, 0)}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="localAmount" label="入帳金額" min-width="90" align="right" sortable="custom">
        <template slot-scope="scope">
          <span :title="scope.row.localAmount">{{formatThousand(scope.row.localAmount?scope.row.localAmount:0, 0)}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="receiptChargeAmount" label="手續費" align="right" sortable="custom">
        <template slot-scope="scope">
          <span :title="scope.row.receiptChargeAmount">{{formatThousand(scope.row.receiptChargeAmount?scope.row.receiptChargeAmount:0, 2)}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="id" fixed="right" label="operation" min-width="120">
        <template slot-scope="scope">
          <span style="width: 100%;display: flex;felx-start:row;text-align: center;justify-content:space-between;cursor: pointer">
            <!-- <span style="width: 50%;display: inline-block;text-align: center;cursor: pointer">

            </span> -->
            <el-button @click="addDetail(scope.row)" type="text" size="small" v-if="isAuth('list-manage:o-list:list:changeDetail')">編輯</el-button>
            <el-button @click="showTableBillto(scope.row)" type="text" size="small" v-if="isAuth('list-manage:o-list:list:look')">查看檔案</el-button>
          </span>
        </template>
        <!-- <template slot-scope="scope">
          <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
            <el-button @click="showTableDetailto(scope.row.id)" type="text" size="small">查看清單</el-button>
          </span>
          <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
            <el-button @click="addDetail(scope.row)" type="text" size="small"> 編輯明細</el-button>
          </span>
          <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
            <el-button @click="configBill(scope.row)" type="text" size="small">編輯水單</el-button>
          </span>
        </template> -->
      </el-table-column>
    </el-table>
    <el-pagination background layout="prev, pager, next" :current-page="page.pageNum" :total="total" @current-change="handleCurrentChange">
    </el-pagination>
    <society-name ref="society" :societyCode='societyCode' @checkSociet='checkSociet' @flag='flag1=true'></society-name>
  </div>
</template>

<script>

import { formatThousand } from '@/utils';
import societyName from '../../export/components/societyName';
import DetailDrawer from './components/detail-drawer';
export default {
  name: 'sampleDateList',
  components:{
    societyName,
    DetailDrawer
  },
  data() {
    return {
        windowWidth: window.innerWidth,
        windowHeight: window.innerHeight,
        drawerWidth:0,
        drawerHeight:0,
      tableData: [],
      tableresult:' ',
      total: 0,
      search: {
        distNo: '',
        receiptId: '',
        societyCode: '',
        societyName: '',
        orderList: [], // 多重排序
        orderBy: '',
        ascend: ''
      },
      loading: false,
      flag1:true,
      dateRange: [],
      societyCode: '',
      societyName: '',
      page: {
        pageSize: 10,
        pageNum: 1
      },
      // 新增：用于存储预加载的详情数据
      preloadedDetailData: {},
      // 新增：当前正在加载详情的行
      currentLoadingRow: null,
      // 新增：详情数据加载状态
      detailLoading: false,
      orderSelections: [
        {
          name: '分配代號 - 昇序(▲)',
          prop: 'distNo',
          value: JSON.stringify({
            orderField: 'distNo',
            direction: 'asc',
          }),
        },
        {
          name: '分配代號 - 降序(▼)',
          prop: 'distNo',
          value: JSON.stringify({
            orderField: 'distNo',
            direction: 'desc',
          }),
        },
        {
          name: '⽔單ID - 昇序(▲)',
          prop: 'receiptId',
          value: JSON.stringify({
            orderField: 'receiptId',
            direction: 'asc',
          }),
        },
        {
          name: '⽔單ID - 降序(▼)',
          prop: 'receiptId',
          value: JSON.stringify({
            orderField: 'receiptId',
            direction: 'desc',
          }),
        },
        {
          name: '匯款日期 - 昇序(▲)',
          prop: 'receiptDate',
          value: JSON.stringify({
            orderField: 'receiptDate',
            direction: 'asc',
          }),
        },
        {
          name: '匯款日期 - 降序(▼)',
          prop: 'receiptDate',
          value: JSON.stringify({
            orderField: 'receiptDate',
            direction: 'desc',
          }),
        },
        {
          name: '協會名稱 - 昇序(▲)',
          prop: 'sourceSocietyName',
          value: JSON.stringify({
            orderField: 'sourceSocietyName',
            direction: 'asc',
          }),
        },
        {
          name: '協會名稱 - 降序(▼)',
          prop: 'sourceSocietyName',
          value: JSON.stringify({
            orderField: 'sourceSocietyName',
            direction: 'desc',
          }),
        },
        {
          name: '協會分配序號 - 昇序(▲)',
          prop: 'distOrderNumber',
          value: JSON.stringify({
            orderField: 'distOrderNumber',
            direction: 'asc',
          }),
        },
        {
          name: '協會分配序號 - 降序(▼)',
          prop: 'distOrderNumber',
          value: JSON.stringify({
            orderField: 'distOrderNumber',
            direction: 'desc',
          }),
        }
      ]
    }
  },
    watch: {
        windowWidth(newVal) {
            console.log(`窗口宽度变为: ${newVal}`);
            // 可以在这里根据新的宽度调整UI
        },
        windowHeight(newVal) {
            console.log(`窗口高度变为: ${newVal}`);
            // 可以在这里根据新的高度调整UI
        }
    },
  computed: {
    formatThousand: {
      get: () => formatThousand
    }
  },
  mounted() {
      console.log('页面加载完成时的窗口大小:', window.innerWidth, window.innerHeight);
      window.addEventListener('resize', this.handleResize);
      this.searchFn(1);
  },
  activated() {
    // if(this.$route.query.update){
    //     this.searchFn(1);
    //     this.$router.push({name: 'oList', query: {update: false}});
    // }

    this.$nextTick(() => {
      if (this.$route.query.update) {
        let query = this.$route.query;
        delete query.update;
        this.searchFn(1);
      }
    })

  },
    beforeDestroy() {
        window.removeEventListener('resize', this.handleResize);
    },
  methods: {
      handleExpandChange(row, expandedRows) {
          this.$nextTick(()=>{
              this.$refs.dataTable.setCurrentRow(row);
          })
      },
      handleResize() {
          this.windowWidth = window.innerWidth;
          this.windowHeight = window.innerHeight;
          // 抽屉宽度为窗口的 80%，最大不超过 1000px
          this.drawerWidth = window.innerWidth * 0.6;
          // 抽屉高度为窗口的 60%
          this.drawerHeight = window.innerHeight * 0.4;
      },
    upload() {
      this.$router.push({ name: 'oListStep2' })
    },
    clearSearch() {
      this.search = {
        distNo: '',
        receiptId: '',
        societyCode: '',
        societyName: '',
        orderBy: '',
        orderList: [],
        ascend: ''
      }
      this.searchFn(1);
    },
    formatTime(time) {
      let str = '',result = ''
      if (time) {
       return time.split(' ')[0]
      }
      return result
    },
    getSocName(){
      if(this.flag1){
          this.societyCode = ''
          this.flag1=false
          this.$refs.society.socDataD()
      }
    },
    changeSoc(data){
      if(data){
          if(this.flag1){
          this.societyCode = data
          this.flag1=false
          this.$nextTick(()=>{
              this.$refs.society.socDataC()
          })
          }
      }else{
        this.$set(this.search,'societyName','')
      }
    },
    checkSociet(data){
      this.flag1=true
      this.$set(this.search,'societyCode',data.societyCode)
      this.$set(this.search,'societyName',data.societyName)
    },
    showTableDetail(row) {
      // this.$router.push({ name: 'bill', query: { receiptDetailsId: row.id } })
      this.$router.push({ name: 'oListStep3', query: { id: row.receiptId, sourceSoc: row.sourceSocietyCode, title: row.receiptId, nameId: row.receiptId } })
    },
    showTableBillto(row) {
      this.$router.push({ name: 'billList', query: { receiptId: row.receiptId } })
    },

    // 处理行双击事件
    handleRowDblClick(row, column, event) {
      // 如果点击的是操作列，则不处理
      if (column.label === 'operation') {
        return;
      }

      // 打印行数据，便于调试
      console.log('双击行数据:', row);

      // 直接展开或收起抽屉，不再判断数据是否加载
      this.toggleDrawer(row);
    },

    // 新增方法：切换抽屉的展开/收起状态
    toggleDrawer(row) {
      try {
        console.log('尝试切换抽屉状态:', row.receiptId);

        // 先尝试使用表格组件的API
        if (this.$refs.dataTable) {
          console.log('尝试使用表格组件的API展开/收起行');
          const rowIndex = this.tableData.findIndex(item => item.receiptId === row.receiptId);
          if (rowIndex !== -1) {
            console.log('找到行索引:', rowIndex);
            // 尝试使用表格的toggleRowExpansion方法
            this.$refs.dataTable.toggleRowExpansion(this.tableData[rowIndex]);
            return;
          }
        }

        // 如果不能使用表格组件的API，则尝试手动操作DOM
        // 先找到对应的行
        const rowEl = this.findRowElement(row.receiptId);
        if (!rowEl) {
          console.error('找不到对应的行元素:', row.receiptId);

          // 如果找不到行元素，先加载数据
          this.loadDataAndExpandLater(row);
          return;
        }

        // 找到展开图标
        const expandEl = rowEl.querySelector('.el-table__expand-icon');
        if (!expandEl) {
          console.error('找不到展开图标:', row.receiptId);

          // 如果找不到展开图标，先加载数据
          this.loadDataAndExpandLater(row);
          return;
        }

        // 检查是否已经展开
        const isExpanded = expandEl.classList.contains('el-table__expand-icon--expanded');
        console.log('当前抽屉状态:', isExpanded ? '已展开' : '未展开');

        // 如果已经展开，则收起；如果未展开，则展开
        if (!isExpanded) {
          // 如果数据已经加载，直接展开
          if (this.preloadedDetailData[row.receiptId]) {
            console.log('数据已加载，直接展开抽屉');
            expandEl.click();
          } else {
            // 如果数据未加载，先加载数据
            this.loadDataAndExpandLater(row);
          }
        } else {
          // 如果已经展开，则收起
          console.log('抽屉已展开，点击收起');
          expandEl.click();
        }
      } catch (error) {
        console.error('切换抽屉时发生错误:', error);
      }
    },

    // 新增方法：加载数据并在之后展开抽屉
    loadDataAndExpandLater(row) {
      console.log('数据未加载，先加载数据:', row.receiptId);
      this.currentLoadingRow = row.receiptId;
      this.detailLoading = true;

      // 加载数据
      this.$http.get(
        '/listOverseasReceiptDetails/getListOverseasReceiptDetailsByReceiptId',
        { params: { receiptId: row.receiptId } }
      ).then(res => {
        console.log('详情数据加载完成:', res);

        if (res.success && res.data.code === 200) {
          // 将详情数据存储到预加载数据对象中
          this.$set(this.preloadedDetailData, row.receiptId, res.data.data);

          // 将详情数据合并到行数据中
          const statementData = res.data.data.listOverseasReceiptStatementList || [];
          const fieData = res.data.data.listOverseasReceiptStatementFieList || [];

          Object.assign(row, {
            detailData: res.data.data,
            summaryData: [res.data.data],
            statementData: statementData,
            fieData: fieData,
            fieFileList: res.data.data.listOverseasReceiptFieFileList || []
          });

          // 数据加载完成
          this.detailLoading = false;

          // 尝试使用表格组件的API展开行
          if (this.$refs.dataTable) {
            console.log('尝试使用表格组件的API展开行');
            const rowIndex = this.tableData.findIndex(item => item.receiptId === row.receiptId);
            if (rowIndex !== -1) {
              console.log('找到行索引:', rowIndex);
              // 尝试使用表格的toggleRowExpansion方法
              setTimeout(() => {
                this.$refs.dataTable.toggleRowExpansion(this.tableData[rowIndex], true);
              }, 100);
              return;
            }
          }

          // 如果不能使用表格组件的API，则尝试手动操作DOM
          setTimeout(() => {
            const updatedRowEl = this.findRowElement(row.receiptId);
            if (updatedRowEl) {
              const updatedExpandEl = updatedRowEl.querySelector('.el-table__expand-icon');
              if (updatedExpandEl && !updatedExpandEl.classList.contains('el-table__expand-icon--expanded')) {
                console.log('数据加载完成，点击展开抽屉');
                updatedExpandEl.click();
              } else {
                console.log('找不到展开图标或已展开');

                // 最后的尝试：点击所有行的展开图标
                const allRows = this.$el.querySelectorAll('.el-table__body tr');
                for (let i = 0; i < allRows.length; i++) {
                  const expandIcon = allRows[i].querySelector('.el-table__expand-icon');
                  if (expandIcon && !expandIcon.classList.contains('el-table__expand-icon--expanded')) {
                    console.log('尝试点击第', i, '行的展开图标');
                    expandIcon.click();
                    break; // 只点击第一个找到的展开图标
                  }
                }
              }
            } else {
              console.log('找不到行元素');
            }
          }, 100);
        } else {
          console.error('加载详情数据失败:', res);
          this.detailLoading = false;
          this.currentLoadingRow = null;
        }
      }).catch(err => {
        console.error('加载详情数据异常:', err);
        this.detailLoading = false;
        this.currentLoadingRow = null;
      });
    },

    // 新增方法：查找行元素
    findRowElement(receiptId) {
      try {
        // 先尝试使用data-row-key属性查找
        let rowEl = this.$el.querySelector(`tr[data-row-key="${receiptId}"]`);
        if (rowEl) {
          return rowEl;
        }

        // 如果找不到，尝试遍历所有行
        const allRows = this.$el.querySelectorAll('.el-table__body tr');
        for (let i = 0; i < allRows.length; i++) {
          const currentRow = allRows[i];
          const rowKey = currentRow.getAttribute('data-row-key');
          if (rowKey === receiptId.toString()) {
            return currentRow;
          }
        }

        return null;
      } catch (error) {
        console.error('查找行元素时发生错误:', error);
        return null;
      }
    },
    addDetail(row) {
      this.$router.push({ name: 'oListStep3', query: { id: row.receiptId, sourceSoc: row.sourceSocietyCode, title: row.receiptId, nameId: row.receiptId } })
    },
    configBill(row) {
      this.$router.push({ name: 'oListStep1', query: { row: JSON.stringify(row), title: 'edit' + row.id, nameId: 'edit' + row.id } })
    },
    searchFn(page) {
      this.page.pageNum = page
      const searchParam = {
        ...this.search,
        orderList: this.search.orderList.map((item) => JSON.parse(item))
      }
      let ajaxData = searchParam;
      ajaxData.page = this.page
      if (page == 1) {
        this.total = 0
      }

      this.tableresult = '數據加載中...'
      this.$http.post('listOverseasReceiptDetails/getListOverseasReceiptDetailsList', ajaxData).then(res => {
        if (res.success) {
          this.tableData = res.data.list
          this.total = res.data.total
        }
          this.tableresult = this.tableData.length == 0 ? '暫無數據' : ' '
      })
    },
    handleCurrentChange(val) {
      this.page.pageNum = val
      this.searchFn(val);
    },
    handleClick(item) {
      this.$router.push({ name: '', query: {} })
    },
    addNew() {
      this.$router.push({ name: 'oListStep1', query: { row: '', title: 'add', nameId: 'add' } })
    },
    handleSortChange(obj ){
      this.search.orderBy = obj.prop
      this.search.ascend = obj.order
      this.search.orderList = []
      this.searchFn(this.page.pageNum)
    },

    // 处理行点击事件（已不再直接绑定到行点击事件，改为双击展开抽屉）
    // 保留此方法以防其他地方可能会引用
    handleRowClick(row, column, event) {
      // 打印行数据，便于调试
      console.log('点击行数据:', row);

      // 如果点击的是操作列或已经点击了展开图标，则不处理
      if (column.label === 'operation' || event.target.classList.contains('el-table__expand-icon')) {
        return;
      }

      // 如果已经有正在加载的行，则取消之前的加载
      if (this.currentLoadingRow && this.currentLoadingRow !== row.receiptId) {
        this.detailLoading = false;
        this.currentLoadingRow = null;
      }

      // 如果该行的数据已经加载完成，直接展开抽屉
      if (this.preloadedDetailData[row.receiptId]) {
        // 切换展开状态
        console.log('数据已加载，直接展开抽屉');

        // 增加延时确保DOM已更新
        setTimeout(() => {
          // 尝试多种方式查找展开图标
          let expandEl = event.currentTarget.querySelector('.el-table__expand-icon');

          // 如果找不到，尝试通过索引查找
          if (!expandEl) {
            expandEl = this.$el.querySelector(`tr[data-row-key="${row.receiptId}"] .el-table__expand-icon`);
          }

          // 如果还是找不到，尝试遍历所有行
          if (!expandEl) {
            const rows = this.$el.querySelectorAll('.el-table__body tr');
            for (let i = 0; i < rows.length; i++) {
              const rowEl = rows[i];
              if (rowEl.getAttribute('data-row-key') === row.receiptId.toString()) {
                expandEl = rowEl.querySelector('.el-table__expand-icon');
                break;
              }
            }
          }

          // 如果找到展开图标，点击它
          if (expandEl && !expandEl.classList.contains('el-table__expand-icon--expanded')) {
            console.log('找到展开图标，点击展开');
            expandEl.click();
          } else {
            console.log('未找到展开图标或已展开', expandEl);
          }
        }, 100); // 添加100ms延时

        return;
      }

      // 使用toggleDrawer方法展开抽屉
      this.toggleDrawer(row);
    },

    // loadDetailData方法已集成到toggleDrawer方法中
  }
}
</script>

<style scoped>

/deep/ .el-table__body tr.current-row > td {
    background-color: #17b3a3 !important;
}
.el-date-editor {
  width: 100px !important;
}
/deep/.el-input--suffix .el-input__inner {
  padding-right: 10px;
}
.breakword {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  width: 100%;
  display: block;
}

/* 新增样式 */
.detail-drawer-container {
  position: relative;
  min-height: 200px;
  max-width: 100%;
  overflow: auto; /* 允许内容溢出，以显示抽屉组件的滚动条 */
  z-index: 1; /* 确保抽屉位于图层的最下层 */
  margin-right: 120px; /* 右侧留出120px的空间，不覆盖操作栏 */
  width: calc(100vw - 350px); /* 宽度设置为总宽度减去操作栏的宽度 */
  box-sizing: border-box; /* 确保内边距不会增加元素宽度 */
}

/deep/ .el-loading-mask {
  z-index: 1000;
}

/* 表格展开行的样式 */
/deep/ .el-table__expanded-cell {
  padding: 0 !important; /* 移除内边距，给抽屉容器更多空间 */
  position: relative; /* 设置定位上下文 */
  overflow: visible !important; /* 允许内容超出单元格 */
}

/* 添加滚动条样式 */
::-webkit-scrollbar {
  width: 12px; /* 垂直滚动条宽度 */
  height: 12px; /* 水平滚动条高度 */
}

::-webkit-scrollbar-track {
  background: #f1f1f1; /* 滚动条轨道背景色 */
  border-radius: 6px; /* 轨道圆角 */
}

::-webkit-scrollbar-thumb {
  background: #888; /* 滚动条滑块颜色 */
  border-radius: 6px; /* 滑块圆角 */
  border: 2px solid #f1f1f1; /* 滑块边框 */
}

::-webkit-scrollbar-thumb:hover {
  background: #555; /* 鼠标悬停时滑块颜色 */
}

/* 添加底部滚动条 */
/deep/ .el-table__body-wrapper {
  overflow-x: auto !important; /* 确保水平滚动条显示 */
}

/* 确保抽屉内容不会被截断 */
/deep/ .detail-table {
  width: 98% !important;
  margin: 10px auto !important;
}

/deep/ .el-table__body tr.current-row > td {
    color: white !important;
    background-color: #17b3a3 !important;
}

/deep/ .el-table__body tr.current-row .el-button--text span {
    color: white !important;
}
</style>
