<template>
  <div style="width:900px">
    <el-form label-position="right" label-width="140px" :model="formData" ref="editForm" :rules="rules" class="demo-form-inline">
      <el-form-item label="Dist No" prop="distNo">
        <el-input v-model="formData.distNo" placeholder="請輸入"></el-input>
      </el-form-item>
      <el-form-item label="ip base no" prop="ipBaseNo">
        <!-- <el-input v-model="formData.ipBaseNo" placeholder="雙擊查詢" :readonly="true" @dblclick.native="getIp()" @blur="getNameByBaseNo()"></el-input> -->
        <el-input v-model="formData.ipBaseNo" placeholder="雙擊查詢" :readonly="true" @dblclick.native="getIp()"></el-input>
      </el-form-item>
      <el-form-item label="pa name no" prop="paNameNo">
        <el-input v-model="formData.paNameNo" placeholder="雙擊查詢" :readonly="true" @dblclick.native="getIp()"></el-input>
      </el-form-item>
      <el-form-item label="name" prop="paName">
        <el-input v-model="formData.paName" placeholder="雙擊查詢" :readonly="true" @dblclick.native="getIp()"></el-input>
      </el-form-item>
      <el-form-item label="Type" prop="tranType">
        <el-input v-model="formData.tranType" ref="typeInput" placeholder="請雙擊" :readonly="true" @dblclick.native="getTypeList()"></el-input>
      </el-form-item>
      <el-form-item label="Description">
        <el-input v-model="formData.tranDescpt" placeholder="請輸入"></el-input>
      </el-form-item>
      <el-form-item label="Amount" prop="tranAmt">
        <el-input v-model="formData.tranAmt" placeholder="請輸入" @keydown.native="$inputNumber"></el-input>
      </el-form-item>
      <el-form-item label="" class="is-required">
        <el-checkbox-group v-model="checkList" :min="1">
          <el-checkbox v-for="item in checkGroup" :label="item" :key="item">{{item}}</el-checkbox>
        </el-checkbox-group>

      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="save">Save</el-button>
      </el-form-item>
    </el-form>

    <el-dialog title="選擇type" :visible.sync="showTypeDialog" width="50%" :close-on-click-modal="false">
      <el-table :empty-text="emptyText"   :data="typeList" stripe style="width: 100%">
        <el-table-column prop="tranType" label="tranType">
        </el-table-column>
        <el-table-column prop="tranDescpt" label="description" min-width="350">
        </el-table-column>
        <el-table-column label="option" align="center">
          <template slot-scope="scope">
            <i class="el-icon-check" @click="chooseType(scope.row)"></i>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <select-ip v-if="true" ref="selectIpCom" :search="ipSearch" @checkIp="checkIp"></select-ip>
  </div>
</template>

<script>
import axios from '../../utils/httpRequest'
import qs from 'qs'

import selectIp from '../../components/select-ip';

export default {
  name: 'add',
  components: {
    selectIp, axios, qs
  },
  data() {
    return {
      formData: {
        tranAmt: 0
      },
      IpTableVisible: false,
      ipSearch: {},
      checkList: ['Deduction'],
      checkGroup: ['Deduction', 'Apply Comm', 'Apply Tax', 'After Dist'],
      type: '',
      showTypeDialog: false,
      typeList: [],
      rules: {
        distNo: [
          { required: true, message: '请输入distNo', trigger: 'blur' }
        ],
        ipBaseNo: [
          { required: true, message: '請雙擊', trigger: 'change' }
        ],
        paNameNo: [
          { required: true, message: '請雙擊', trigger: 'change' }
        ],
        paName: [
          { required: true, message: '請雙擊', trigger: 'change' }
        ],
        tranType: [
          { required: true, message: '请雙擊', trigger: 'blur' }
        ],
        amount: [
          { required: true, message: '請輸入整數或小數', trigger: 'blur' }
        ]
      },
      emptyText:'暫無數據',
    }
  },
  // beforeMount () {
  //     this.formData = JSON.parse(JSON.stringify(this.$store.state.common.royalityAdd.member))
  // },
  methods: {
    getIp() {
      this.IpTableVisible = true;
      this.ipSearch = {
        // name_no: this.formData.ipBaseNo,
        name_no: this.formData.paNameNo,
        name: this.formData.paName,
        ip_no: this.formData.ipBaseNo,
      }
      this.$nextTick(() => {
        this.$refs.selectIpCom.init();
      })
    },
    checkIp(info) {
      console.warn('***', info)
      //  alert('&&&&')
      this.$set(this.formData, 'ipBaseNo', info.ip_base_no);
      this.$set(this.formData, 'paNameNo', info.ip_name_no);
      this.$set(this.formData, 'paName', info.name);
      this.formData.ipBaseNo = info.ip_base_no
      this.formData.paNameNo = info.ip_name_no
      this.formData.paName = info.name
      //  console.log('=======',this.$refs.editForm,'++++++++',this.$refs.editForm.validate)
      this.$refs.editForm.validateField('ipBaseNo', emailError => {
        if (!emailError) {
          // alert('邮箱验证通过!');
        } else {
          // console.log('邮箱验证失败');
          return false;
        }
      })

      // this.getNameByBaseNo()

    },
    save() {
      this.checkList.indexOf('Deduction') > -1 ? this.formData.nonTaxableIncomeInd = 'Y' : this.formData.nonTaxableIncomeInd = 'N'
      this.checkList.indexOf('Apply Comm') > -1 ? this.formData.commissionInd = 'Y' : this.formData.commissionInd = 'N'
      this.checkList.indexOf('Apply Tax') > -1 ? this.formData.withheldTaxInd = 'Y' : this.formData.withheldTaxInd = 'N'
      this.checkList.indexOf('After Dist') > -1 ? this.formData.addAfterDistInd = 'Y' : this.formData.addAfterDistInd = 'N'
      console.log(this.formData)
      this.$refs.editForm.validate(validate => {
        if (validate) {
          this.$http.post('/transaction/statement/member', this.formData).then(res => {
            console.log(res)
            if (res.success) {
              if (res.data.code === 200) {
                this.$message({
                  message: '新增成功',
                  type: 'success',
                  type: 'success', duration: 500,
                  onClose: () => {
                    this.$bus.$emit('closeCurrentTab', () => { this.$router.push({ name: 'royalties-member' }) });
                  }
                })
              } else {
                this.$message({
                  message: res.data.message,
                  type: 'warning'
                })
              }
            }
          })
        }
      })
    },
    getTypeList() {
      this.emptyText = '數據加載中';
      this.$http.get('/transaction/statement/tran/type/all').then(res => {
        if (res.success) {
          this.showTypeDialog = true
          this.typeList = res.data
          console.log(res.data)
          if(! this.typeList || this.typeList.length == 0){
              this.emptyText = '暫無數據';
          }
        }
      })
    },
    getNameByBaseNo() {
      // alert('+++++++')
      if (this.formData.ipBaseNo) {
        this.$http.post('/ip/name/es?ip_no=' + this.formData.ipBaseNo + '&page_num=1&page_size=15').then(res => {
          console.log('++++++++++++++', res)
          // if(res.success){
          let list = res.data.list
          if (list.length) {
            list.map(item => {
              if (item.name_type === 'PA') {
                console.warn('======', item)
                this.formData.paName = item.name
                this.formData.paNameNo = item.ip_name_no
                this.formData.ipBaseNo = item.ip_base_no
              }
            })
          } else {
            this.$message({
              message: 'IP_BASE_NO無效',
              type: 'warning'
            });
          }
          // }
        })
      }

    },
    chooseType(row) {
      this.$set(this.formData, 'tranType', row.tranType)
      this.$set(this.formData, 'tranDescpt', row.tranDescpt)
      this.showTypeDialog = false
      this.$refs.typeInput.focus()
      this.$refs.typeInput.blur()
    }
  },
  beforeRouteLeave(to, from, next) {
    this.$store.commit('common/updateRoyalityAdd', { type: 'member', data: this.formData })
    next()
  }
}
</script>

<style scoped>
/deep/ .el-form-item__label {
  margin-left: 0 !important;
}
</style>
