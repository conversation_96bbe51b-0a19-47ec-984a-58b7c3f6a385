<template>
<div>
    <el-dialog :visible.sync="show" width="1100px" title="Ipi" :close-on-click-modal="false">
        <div style="width: 920px;margin: auto;margin-bottom: 20px">
            <el-input @keyup.enter.native="onSubmit()" v-model="searchInfo.name" placeholder="Name" v-if="showAdd"  style="width: 140px;"></el-input>
            <el-input @keyup.enter.native="onSubmit()" v-model="searchInfo.name" placeholder="Name" v-if="!showAdd"  style="width:320px;"></el-input>
            <el-input @keyup.enter.native="onSubmit()" v-model.trim="searchInfo.ip_no" placeholder="IP Base No" style="width: 140px;"></el-input>
            <el-input @keyup.enter.native="onSubmit()" v-model.trim="searchInfo.name_no" placeholder="IP Name No" style="width: 140px;"></el-input>
            <el-input @keyup.enter.native="onSubmit()" v-model.number.trim="searchInfo.soc" v-if="showAdd" placeholder="soc" style="width: 60px;"></el-input>
            <el-button type="primary" @click="onSubmit()">查詢</el-button>
            <el-button type="primary" @click="addIp()" v-if="showAdd">新增dummyIp</el-button>
            <span class="clear-search" @click="clearSearch()">清除搜索</span>
        </div>
        <el-table :empty-text="emptyText" :data="tableData">
            <el-table-column property="name" label="Name" width="250px"></el-table-column>
            <el-table-column property="chinese_name" label="Chinese Name" width="150px"></el-table-column>
            <el-table-column property="ip_base_no" label="ipBaseNo" width="150px"></el-table-column>
            <el-table-column property="ip_name_no" label="ipNameNo" width="150px"></el-table-column>
            <el-table-column property="society_code" label="Soc" width="60px"></el-table-column>
            <el-table-column property="role_code" label="roleCode" width="90px"></el-table-column>
            <el-table-column property="ip_type" label="IT" width="50px"></el-table-column>
            <el-table-column property="name_type" label="NT" width="50px"></el-table-column>
            <el-table-column
                label="operation">
                <template slot-scope="scope">
                    <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                        <i class="el-icon-check" @click="checkedIpShare(scope.$index,scope.row)"></i>
                    </span>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :total="total" @current-change="onSubmit" :current-page="currentPage">
        </el-pagination>
    </el-dialog>
</div>
</template>
<script>
import qs from 'qs'

export default {
    data(){
        return{
            emptyText:'暫無數據',
            show:false,
            searchInfo: {},
            tableData: [],
            total: 0,
            currentPage: 1 
        }
    },
    props: {
        search: {
            type: Object,
            default: () => {
                return {
                    name_no: '',
                    name: '',
                    soc: '',
                    ip_no: '', //ipBaseNo
                    ip_type: ''
                }

            }
        },
        showAdd: {
            type: Boolean,
            dafault: false
        }
    },
    mounted(){
            this.searchInfo = {
                name_no: '',
                name: '',
                soc: '',
                ip_no: '', //ipBaseNo
                ip_type: ''
            };
            // this.onSubmit();
    },

    methods: {
        /**
         * dialog 组件调用方式： 
         * 1、父级用v-if在html里加载组件,
         * 2、父级调用子级的init方法，将数据初始化，prop传给data等
         * 
         */
        init(){
            this.show = true;
            this.searchInfo = JSON.parse(JSON.stringify(this.search));
            // this.searchInfo = {
            //     name_no: '',
            //     name: '',
            //     soc: '',
            //     ip_no: '', //ipBaseNo
            //     ip_type: ''
            // };
            console.log('ininit',this.searchInfo,'-------',this.search)
            this.onSubmit();
        },
        clearSearch(){
            this.searchInfo = {
                name_no: '',
                name: '',
                soc: '',
                ip_no: '', //ipBaseNo
                ip_type: ''
            };
            this.onSubmit();
        },
        onSubmit(page){
            // this.searchInfo.name_no = this.searchInfo.name_no.replace(/\D/g, '');
            let params = this.searchInfo;
            params.page_num = page ? page : 1;
            params.page_size = 10;
            let formData = qs.stringify(params)
            let config = {headers: { 'content-type': 'application/x-www-form-urlencoded' }}
            // const loading = this.$loading();
            this.emptyText = '數據加載中'
            this.$http.post('/ip/name/es', formData, config).then(res => {
                // loading.close();
                if (res.success) {
                    this.tableData = res.data.list;
                    this.total = res.data.total;
                    this.currentPage = page ? page : 1;
                    if(! this.tableData || this.tableData.length == 0){
                        this.emptyText = '暫無數據';
                    }
                }
            })
        },
        checkedIpShare(index, row){
            console.log('xz',row)
            console.log(index)
            console.log(row)
            this.$emit('checkIp', row);
            this.show = false;
        },
        addIp(){
            this.$router.push({name: 'member-dummyinfo'});
            this.show = false;
        }
    }
    
}
</script>
<style lang="scss" scoped>
    /deep/ .el-dialog__title{
        font-weight: bold;
    }
    /deep/ .el-dialog__body{
        text-align: center;
        padding-top: 0;
    }
</style>