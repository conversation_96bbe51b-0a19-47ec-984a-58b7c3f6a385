<template>
  <div>
    <el-form :inline="true" :model="formData" ref="form" class="demo-form-inline" @keyup.enter.native='getList()'>
      <el-form-item label="水單ID">
        <el-input v-model.trim="formData.receiptId" placeholder="" style="width: 100px;"></el-input>
      </el-form-item>
      <el-form-item label="FID">
        <el-input v-model.trim="formData.id" placeholder="" style="width: 100px;"></el-input>
      </el-form-item>

      <el-form-item label="文件名稱">
        <el-input v-model="formData.fileName" @input="trimFileName" placeholder="" style="width: 100px;"></el-input>
      </el-form-item>
      <el-form-item label="分配代號">
        <el-input v-model="formData.distNo" placeholder="" style="width: 100px;"></el-input>
      </el-form-item>
      <el-form-item label="協會代碼">
        <el-input v-model="formData.remitSocietyCode" placeholder="" style="width: 100px;"></el-input>
      </el-form-item>
      <el-form-item label="協會名稱">
        <el-input v-model="formData.remitSocietyName" placeholder="" style="width: 100px;"></el-input>
      </el-form-item>
      <el-form-item label="狀態">
        <el-select v-model="formData.status" placeholder="请选择" style="width:191px">
          <el-option label="全部" value=""></el-option>
          <el-option label="待處理" :value="0"></el-option>
          <el-option label="處理成功" :value="1"></el-option>
          <el-option label="處理失敗" :value="2"></el-option>
          <el-option label="處理中" :value="3"></el-option>
          <el-option label="未指定水單" :value="4"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList()" v-if="isAuth('list-manage:o-list:billlist:find')">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="add" v-if="isAuth('list-manage:o-list:billlist:add')">新增⼿動單</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="danger" class="f-l" @click="batchesDel" v-if="isAuth('list-manage:o-list:billlist:del')">批量删除</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="upload" v-if="isAuth('list-manage:o-list:billlist:exportout')">導出報告</el-button>
      </el-form-item>
      <el-form-item>
        <span class="clear-search" @click="clearSearch()" v-if="isAuth('list-manage:o-list:billlist:find')">清除搜索</span>
      </el-form-item>

    </el-form>
    <el-table :empty-text="tableresult" stripe :data="tableData" border style="width: 100%" v-loading="loading"
              @selection-change="handleSelectionChange" @sort-change="handleSortChange" highlight-current-row ref="billTableRef">
      <el-table-column type="selection" >
      </el-table-column>
      <el-table-column prop="receiptId" label="水单ID" width="93"  sortable="custom">
      </el-table-column>
      <el-table-column prop="id" label="FID" width="75" sortable="custom" >
      </el-table-column>
      <el-table-column prop="fileType" label="Format" width="80" >
      </el-table-column>
      <el-table-column prop="remitSocietyName" label="協會名" width="120" sortable="custom">
      </el-table-column>
      <el-table-column prop="remitSocietyCode" label="協會編號" width="120" sortable="custom">
      </el-table-column>
      <el-table-column prop="distNo" label="分配代號" width="120" sortable="custom">
      </el-table-column>
      <el-table-column prop="fileName" label="文件名稱" sortable="custom" width="120">
        <template slot-scope="scope">
          <span :title="scope.row.fileName">{{scope.row.fileName}}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="fileDetailsNumber" label="文件記錄數" width="130" sortable="custom">
      </el-table-column> -->
      <el-table-column label="一般金額" prop="nomalAmount" sortable="custom" width="155" align="right">
        <!-- <template slot-scope="scope">{{scope.row.nomalAmount?scope.row.nomalAmount:0}}</template> -->
        <el-table-column :label="`${listCountResult.nomalAmount ? formatThousand(listCountResult.nomalAmount, 2) :  '--'}`"
                         prop="nomalAmount" width="155" align="right">
          <template slot-scope="scope">
            <span :title="scope.row.nomalAmount">{{formatThousand(scope.row.nomalAmount?scope.row.nomalAmount:0, 2)}}</span>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column prop="adjAmount" label="調整金額" sortable="custom" width="140" align="right">
        <!-- <template slot-scope="scope">{{scope.row.adjAmount?scope.row.adjAmount:0}}</template> -->
        <el-table-column :label="`${listCountResult.adjAmount ? formatThousand(listCountResult.adjAmount, 2) :  '--'}`" prop="adjAmount" width="140" align="right">
          <template slot-scope="scope">
            <span :title="scope.row.adjAmount">{{formatThousand(scope.row.adjAmount?scope.row.adjAmount:0, 2)}}</span>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column prop="fieAmount" label="FIE金額" sortable="custom" width="145" align="right">
        <!-- <template slot-scope="scope">{{scope.row.fieAmount?scope.row.fieAmount:0}}</template> -->
        <el-table-column :label="`${listCountResult.fieAmount ? formatThousand(listCountResult.fieAmount, 2) :  '--'}`" prop="fieAmount" width="145" align="right">
          <template slot-scope="scope">
            <span :title="scope.row.fieAmount">{{formatThousand(scope.row.fieAmount?scope.row.fieAmount:0, 2)}}</span>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column prop="totalAmount" label="匯總金額" sortable="custom" width="150" align="right">
        <!-- <template slot-scope="scope">{{scope.row.totalAmount?scope.row.totalAmount:0}}</template> -->
        <el-table-column :label="`${listCountResult.totalAmount ? formatThousand(listCountResult.totalAmount, 2) :  '--'}`" prop="totalAmount" width="150" align="right">
          <template slot-scope="scope">
            <span :title="scope.row.totalAmount">{{formatThousand(scope.row.totalAmount?scope.row.totalAmount:0, 2)}}</span>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column prop="fileTotalAmount" label="讀取總金額" width="150" align="right" sortable="custom">
        <!-- <template slot-scope="scope">{{scope.row.fileTotalAmount?scope.row.fileTotalAmount:0}}</template> -->
        <el-table-column :label="`${listCountResult.fileTotalAmount ? formatThousand(listCountResult.fileTotalAmount, 2) :  '--'}`" prop="fileTotalAmount" width="150" align="right">
          <template slot-scope="scope">
            <span :title="scope.row.fileTotalAmount">{{formatThousand(scope.row.fileTotalAmount?scope.row.fileTotalAmount:0, 2)}}</span>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="文件記錄數(work)" width="300" header-align="center">
        <el-table-column prop="fileWorkNumber" label="總數" width="100" sortable="custom"></el-table-column>
        <el-table-column prop="normalAdjWorkNumber" label="一般/調整" width="100" sortable="custom"></el-table-column>
        <el-table-column prop="fieWorkNumber" label="FIE" width="100" sortable="custom"></el-table-column>
      </el-table-column>
      <el-table-column label="文件記錄數(ip)" width="300" header-align="center">
        <el-table-column prop="fileDetailsNumber" label="總數" width="100" sortable="custom"></el-table-column>
        <el-table-column prop="normalAdjIpNumber" label="一般/調整" width="100" sortable="custom"></el-table-column>
        <el-table-column prop="fieIpNumber" label="FIE" width="100" sortable="custom"></el-table-column>
      </el-table-column>
      <el-table-column label="作品匹配" width="360" header-align="center">
        <el-table-column prop="matchedWork" label="matched" width="120" sortable="custom">
        </el-table-column>
        <el-table-column prop="unmatchedWork" label="unmatched" width="120" sortable="custom">
        </el-table-column>
        <el-table-column prop="workMatchRate" label="Match Rate(%)" width="120">
        </el-table-column>
      </el-table-column>
      <el-table-column label="IP匹配" width="360" header-align="center">
        <el-table-column prop="matched" label="matched" width="120" sortable="custom">
        </el-table-column>
        <el-table-column prop="unmatched" label="unmatched" width="120" sortable="custom">
        </el-table-column>
        <el-table-column prop="ipMatchRate" label="Match Rate(%)" width="120">
        </el-table-column>
      </el-table-column>
      <el-table-column prop="createTime" width="130" label="createTime" sortable="custom">
        <template slot-scope="scope">{{scope.row.createTime | splitDate}}</template>
      </el-table-column>
      <el-table-column fixed="right" prop="status" label="狀態" width="120" align="center" sortable="custom">
        <template slot-scope="scope">
          <span v-if="scope.row.status === 0">待處理</span>
          <span v-else-if="scope.row.status === 1">處理成功</span>
          <span style="cursor:pointer;color:#f65f5f" v-else-if="scope.row.status === 2" @click="transferMessage(scope.row.message, scope.row)">處理失敗</span>
          <span v-else-if="scope.row.status === 3">處理中</span>
          <span v-else-if="scope.row.status === 5"></span>
          <span v-else>未匹配上水單</span>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="operation" width="120">
        <template slot-scope="scope">
          <span style="width: 100%;display:flex;justify-content:space-between;text-align: center;cursor: pointer">
            <el-button v-if="isAuth('list-manage:o-list:billlist:assign')&&scope.row.status === 4" @click="showListDialog(scope.row)" type="text" size="small">指定水單</el-button>
            <el-button v-if="isAuth('list-manage:o-list:billlist:update')" @click="update(scope.row)" type="text" size="small">編輯</el-button>
            <el-button @click="handleDelete(scope.row)" type="text" size="small" v-if="isAuth('list-manage:o-list:billlist:del')">刪除</el-button>
            <!--                        <el-button @click="showListDialog(scope.row)" type="text" size="small">指定水單</el-button>-->
          </span>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination background layout="prev, pager, next" :current-page.sync="currentPage1" :total="total" @current-change="handleCurrentChange">
    </el-pagination>
    <!-- <div class="count-row" style="text-align: right; margin-top: 12px;">
      {{ listCountResult }}
    </div> -->
    <el-dialog title="指定水單" :visible.sync="showOList" width="60%" @close="closeOList">
      <div class="search">
        <el-form :model="searchForm" :inline="true" @keyup.enter.native='getBillList()'>
          <el-form-item label="分配代號">
            <el-input v-model="searchForm.distNo"></el-input>
          </el-form-item>
          <el-form-item label="協會名稱">
            <el-input v-model="searchForm.sourceSocName"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="getBillList()">搜索</el-button>
          </el-form-item>
          <el-form-item>
            <span class="clear-search" @click="clearSearchlist()">清除搜索</span>
          </el-form-item>
        </el-form>
      </div>
      <el-table :empty-text="tableresult" :data="tableData1" v-loading="dialogLoading" border height="487" style="width: 100%">
        <el-table-column prop="distNo" label="分配代號"></el-table-column>
        <el-table-column prop="id" label="水單ID"></el-table-column>
        <el-table-column prop="sourceSocName" label="協會名稱"></el-table-column>
        <el-table-column label="operation">
          <template slot-scope="scope">
            <i class="el-icon-check pointer" @click="checkBill(scope.row.id)"></i>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background layout="prev, pager, next" :total="total1" :current-page="currentPage2" @current-change="handleChange"></el-pagination>
    </el-dialog>

    <el-dialog :title="dialogTitle" :visible.sync="showAdd" width="40%" :close-on-click-modal="false" :before-close='cancelEditAdd'>
      <el-form :model="addForm" ref="addForm" class="demo-form-inline" :inline="true" :rules="rules" label-width="110px">
          <el-form-item label="⽔單ID" prop="receiptId">
            <el-input v-model="addForm.receiptId"></el-input>
          </el-form-item>
          <el-form-item label="Format" prop="fileType">
            <el-input v-model="addForm.fileType" readonly></el-input>
          </el-form-item>
          <el-form-item label="⽂件名稱"  prop="fileName">
            <el-input v-model="addForm.fileName"></el-input>
          </el-form-item>
          <el-form-item label="⼀般⾦額"  prop="nomalAmount" >
            <el-input v-model="addForm.nomalAmount"></el-input>
          </el-form-item>
          <el-form-item label="調整⾦額" prop="adjAmount" >
            <el-input v-model="addForm.adjAmount"></el-input>
          </el-form-item>
          <el-form-item label="FIE⾦額" prop="fieAmount" >
            <el-input v-model="addForm.fieAmount"></el-input>
          </el-form-item>
          <el-form-item label="匯總⾦額" prop="totalAmount" >
            <el-input v-model="addForm.totalAmount"></el-input>
          </el-form-item>
          <el-form-item label="讀取總⾦額" prop="fileTotalAmount" >
            <el-input v-model="addForm.fileTotalAmount"></el-input>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="cancelEditAdd">取 消</el-button>
          <el-button type="primary" @click="editConfirmAdd">確 定</el-button>
        </span>
      </el-dialog>
      <el-dialog title="編輯" :visible.sync="showUpdate" width="40%" :close-on-click-modal="false" :before-close='cancelEditUpdate'>
      <el-form :model="editForm" ref="editForm" class="demo-form-inline" :inline="true" :rules="updateRules" label-width="110px">
          <el-form-item label="匯總⾦額"  prop="totalAmount">
            <el-input v-model="editForm.totalAmount"></el-input>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="cancelEditUpdate">取 消</el-button>
          <el-button type="primary" @click="editConfirmUpdate">確 定</el-button>
        </span>
        </el-dialog>
      <!-- 自定义错误信息弹窗 -->
      <el-dialog v-model:visible="showErrorMsgDialog" title="錯誤信息"
                 :visible.sync="showErrorMsgDialog" width="60%"
                 :custom-class="'custom-error-dialog'" center>
          <div
              style="max-height: 400px; overflow-y: auto;
              padding: 10px; white-space: pre-line">
              {{ errorMsgContent }}
          </div>
          <span slot="footer">
            <el-button @click="showErrorMsgDialog = false">确 定</el-button>
          </span>
      </el-dialog>
  </div>
</template>

<script>
import { formatThousand } from '@/utils';
export default {
  name: 'billList',
  data() {
    return {
      //错误信息的变量
      errorMsgContent: '',
      showErrorMsgDialog: false,
      currentPage1: 1,
      currentPage2: 1,
      formData: {
        page: {
          pageNum: 1,
          pageSize: 10
        },
        id: '',
        fileName: '',
        receiptId: this.$route.query.receiptId || null,
        status: '',
        remitSocietyCode: '',
        remitSocietyName: '',
        distNo: '',
        orderBy: '',
        ascend: ''
      },

      tableData: [],
      tableresult: ' ',
      listCountResult: {},

      total: 1,
      loading: false,
      currentFile: null,
      showOList: false,
      total1: 0,
      tableData1: [],
      dialogLoading: true,
      searchForm: {
        distNo: '',
        sourceSocName: '',
        page: {
          pageSize: 10,
          pageNum: 1
        }
      },
      addForm: {
        receiptId: '',
        fileType: 'HC',
        fileName: '',
        nomalAmount: '',
        adjAmount: '',
        fieAmount: '',
        totalAmount: '',
        fileTotalAmount: ''
      },
      dialogTitle: '新增⼿動單',
      editForm: {
        totalAmount: '',
      },
      showAdd: false,
      showUpdate: false,
      batchesDelIds: '',
      rules:{
          receiptId:[
              { required: true, message: '请输入', trigger: 'blur' },
          ],
          fileName:[
              { required: true, message: '请输入', trigger: 'blur' },
          ],
          nomalAmount:[
              { required: true, message: '请输入', trigger: 'blur' },
          ]
      },
      updateRules:{
          nomalAmount:[
              { required: true, message: '请输入', trigger: 'blur' },
          ]
      }
    }
  },
    computed: {
    formatThousand: {
      get: () => formatThousand
    }
  },
  watch: {
      'formData.receiptId'(newVal, oldVal) {
          if (newVal !== oldVal) {
              // 当 formData.receiptId 发生变化时执行操作
              console.log('receiptId changed from', oldVal, 'to', newVal);
              //this.getList(); // 或其他逻辑
          }
      }

    //  'formData.receiptDetailsId': {
    //    handler(newName, oldName) {
    //      console.log('obj.a changed',newName,oldName);
    //    },
    //    immediate: true,
    //    deep: true
    //  }

      // '$route'(to,from){
      //   if(to.name === 'bill' && to.query.receiptId != this.formData.receiptId){
      //       this.formData.receiptId = to.query.receiptId
      //       this.getList()
      //   }
      // }
   } ,
  created() {
    this.formData.receiptId = this.$route.query.receiptId || ''
    if (this.$route.query.receiptId) {
      this.getList()
    }
  },
  // activated () {
  //     // if(this.$route.query.update){
  //     //     this.getList()
  //     // }
  //     this.$nextTick( () => {
  //         if(this.$route.query.update){
  //             let query = this.$route.query;
  //             delete query.update;
  //             this.formData.receiptDetailsId = query.receiptDetailsId
  //             this.getList()
  //         }
  //     })
  // },
  mounted() {
    // this.formData.receiptDetailsId = this.$route.query.receiptDetailsId || ''
    // this.getList();
  },
  methods: {
    batchesDel() {
      console.log(this.batchesDelIds)
      if (!this.batchesDelIds) {
        this.$toast({ tips: '請至少選擇一項' })
        return;
      }
      this.$alert('是否確定刪除文件？', '刪除', {
        confirmButtonText: '确定',
        showCancelButton: true,
        callback: action => {
          if (action === 'confirm') {
            this.$http.delete('/listOverseasFileBase/deleteFileBatch', { params: { ids: this.batchesDelIds } }).then(res => {
              console.log(res)
              if (res.success) {
                this.$toast({ tips: res.data })
                this.getList()
              }
            })
          }
        }
      });
    },
    cancelEditAdd () {
      this.showAdd = false
      this.addForm = {
        receiptId: '',
        fileType: 'HC',
        fileName: '',
        nomalAmount: '',
        adjAmount: '',
        fieAmount: '',
        totalAmount: '',
        fileTotalAmount: ''
      }
    },
    cancelEditUpdate () {
      this.showUpdate = false
      this.editForm = {
        totalAmount: ''
      }
    },
    add () {
        this.showAdd = true

        // this.$refs.addForm.clearValidate()
    },
    update (row) {
      // 设置当前高亮行
      this.$refs.billTableRef.setCurrentRow(row);

      if(row.fileType == 'HC'){
        this.dialogTitle = '編輯⼿動單'
        this.showAdd = true
        this.addForm = this.$utils.copy(row)
      } else {
        this.showUpdate = true
        this.editForm = this.$utils.copy(row)
      }

    },
    editConfirmAdd () {
      this.$refs.addForm.validate(validate => {
        if(validate){
            let params = this.$utils.copy(this.addForm)
            const loading = this.$loading()
            this.$http
              .post('/listOverseasFileBase/updateListOverseasFileBase', params)
              .then(res => {
                  loading.close()
                  if (res.success) {
                      if (res.data.code === 200) {
                        // let message = res.data ? res.data : '保存成功'
                          this.$toast({ tips: res.data.message })
                          this.$refs.addForm.resetFields()
                          this.showAdd = false
                          this.$refs.addForm.resetFields()
                          this.getList()
                      } else {
                          this.$toast({tips: res.data.message})
                          // this.showAdd = false  // 如果提交失败，不关闭窗口
                      }
                  }
              })
          }
      })
    },
    editConfirmUpdate () {
      this.$refs.editForm.validate(validate => {
        if(validate){
            let params = this.$utils.copy(this.editForm)
            const loading = this.$loading()
            this.$http
              .post('/listOverseasFileBase/updateListOverseasFileBase', params)
              .then(res => {
                  loading.close()
                  if (res.success) {
                      if (res.status === 200) {
                        // let message = res.data ? res.data : '保存成功'
                          this.$toast({ tips: res.data.message })
                          this.$refs.addForm.resetFields()
                          this.showUpdate = false
                          this.getList()
                      } else {
                          this.$toast({tips: res.data.message})
                          // this.showUpdate = false   // 如果提交失败，不关闭窗口
                      }

                      this.$refs.editForm.resetFields()
                  }
              })
          }
      })
    },
    handleSelectionChange(val) {
      let ids = ''
      val.forEach(item => {
        console.log(item)
        if (ids) {
          ids = `${ids},${item.id}`
        } else {
          ids = `${item.id}`
        }
      })
      this.batchesDelIds = ids
    },
    handleSortChange(obj ){
      this.formData.orderBy = obj.prop
      this.formData.ascend = obj.order
      this.getList()
    },
    upload() {
      //   this.$router.push({ name: 'oListStep2' })  listOverseasFileBase/reportListOverseasFileBaseList
      this.$msgbox
        .confirm(`確定進行[導出]操作?`, "提示", {
          confirmButtonText: "確定", closeOnClickModal: false,
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(() => {
          this.$refs.form.validate((validate) => {
            if (validate) {
              // let ajaxData = {};
              let ajaxData = this.$utils.copy(this.formData);
              //   if (ajaxData.listMatchDataBasic.matchScore == 99) {
              //     delete ajaxData.listMatchDataBasic.matchScore;
              //   }
              this.$http
                .post("/listOverseasFileBase/reportListOverseasFileBaseList", ajaxData, { responseType: "blob" })
                .then((res) => {
                  console.warn(
                    "[resresres",
                    res.headers["content-disposition"]
                  );
                  let data = res.data;
                  this.$utils.downloadByBlob(
                    data,
                    res.headers["content-disposition"]
                  );
                });
            }
          });
        });
    },
    clearSearch() {
      this.formData = {
        page: {
          pageNum: 1,
          pageSize: 10
        },
        receiptId: '',
        status: ''
      }
      this.total = 0
      this.getList()
    },
    transferMessage(message, row) {
      this.errorMsgContent = message
      this.showErrorMsgDialog  = true

    },

    handleCurrentChange(val) {
      this.currentPage1 = val
      this.getList(val)
    },
    handleDelete(row) {
      this.$alert('是否確定刪除文件？', '刪除', {
        confirmButtonText: '确定',
        showCancelButton: true,
        callback: action => {
          if (action === 'confirm') {
            this.$http.delete('/listOverseasFileBase/deleteFile', { params: { id: row.id } }).then(res => {
              console.log(res)
              if (res.success) {
                this.$toast({ tips: res.data })
                this.getList(this.currentPage1)
              }
            })
          }
        }
      });

    },
    getList(page = 1) {
      if (page == 1) {
        this.total1 = 0
      }
      this.formData.page.pageNum = page
      this.currentPage1 = page
      this.tableresult = '數據加載中...'
      this.$http.post('/listOverseasFileBase/getListOverseasFileBaseList', this.formData).then(res => {
        console.log(res)
        if (res.success) {
          this.tableData = res.data.list
          this.total = res.data.total
        }
        this.tableresult = this.tableData.length == 0 ? '暫無數據' : ' '

        this.getListCount();
      })
    },
    getListCount(page = 1) {
      this.listCountResult = {};
      console.log("530: getListCount::formData:",this.formData);
      this.$http.post('/listOverseasFileBase/getListOverseasFileBaseListAmount', this.formData).then(res => {
        console.log('532: getListCount::response:', res)
        if (res.success) {
          const { data = {} } = res;
          this.listCountResult = data; //`｜　 一般金額 = ${formatThousand(data.nomalAmount)} 　|　 調整金額 = ${formatThousand(data.adjAmount)} 　|　 FIE金額 = ${formatThousand(data.fieAmount)} 　|　 匯總金額 = ${formatThousand(data.totalAmount)} 　|　 讀取總金額 = ${formatThousand(data.fileTotalAmount)} 　｜`;
        } else {
          this.listCountResult = {};
        }

      })
      .catch((err) => {
        console.error('getListCount:catch:error:', err)
        this.listCountResult = {};
      })
    },
    handleChange(page) {
      this.getBillList(page)
      this.currentPage2 = page
    },
    trimFileName(val) {
      this.formData.fileName = val.trim()
    },
    showListDialog(row) {
      this.showOList = true
      this.currentFile = row
      this.getBillList()
    },
    closeOList() {
      this.searchForm = {
        distNo: '',
        sourceSocName: '',
        page: {
          pageSize: 10,
          pageNum: 1
        }
      }
      this.showOList = false
    },
    clearSearchlist() {
      this.searchForm = {
        distNo: '',
        sourceSocName: '',
        page: {
          pageSize: 10,
          pageNum: 1
        }
      }
      this.getBillList()
    },
    getBillList(page = 1) {
      if (page == 1) {
        this.total1 = 0
      }
      this.searchForm.page.pageNum = page
      this.currentPage2 = page
      this.dialogLoading = true;
      this.tableresult = '數據加載中...'
      if (this.searchForm.distNo||this.searchForm.sourceSocName) {
        this.searchForm.page.pageNum=1
      }
      this.$http.post('/listOverseasReceipt/getListOverseasReceiptList', this.searchForm).then(res => {
        console.log(res)
        if (res.success) {
          this.tableData1 = res.data.list
          this.total1 = res.data.total
        }
        this.tableresult = this.tableData1.length == 0 ? '暫無數據' : ' '
        this.dialogLoading = false;
      })
    },
    checkBill(id) {
      let data = JSON.parse(JSON.stringify(this.currentFile))
      data.receiptId = id
      this.$http.post('/listOverseasFileBase/bindReceipt', data).then(res => {
        if (res.success) {
          this.$toast({ tips: '綁定成功' })
          this.closeOList()
          this.getList(this.currentPage1)
        }
      })
    }
  },
  // beforeRouteEnter(from, to, next) {
  //   //             console.log(this, 'beforeRouteEnter'); // undefined
  //   // console.log(to, '组件独享守卫beforeRouteEnter第一个参数');
  //   // console.log(from, '组件独享守卫beforeRouteEnter第二个参数');
  //   // console.log(next, '组件独享守卫beforeRouteEnter第三个参数');
  //   // next(vm => {
  //   //   //因为当钩子执行前，组件实例还没被创建
  //   //   // vm 就是当前组件的实例相当于上面的 this，所以在 next 方法里你就可以把 vm 当 this 来用了。
  //   //   console.log(vm);//当前组件的实例
  //   // });
  //   let paramid = from.query.receiptId
  //   next(vm => {
  //     vm.formData.receiptId = paramid
  //     vm.getList()
  //   })
  // },

}
</script>

<style scoped>
/deep/ .custom-error-dialog {
    width: auto !important;
    min-width: 300px !important;
    max-width: 500px !important;
    min-height: 200px !important;
    max-height: 500px !important;
    overflow-y: auto;
    white-space: pre-line;
}


.upload {
  padding-bottom: 20px;
}

/deep/ .el-table__body tr.current-row > td {
    color: white !important;
    background-color: #17b3a3 !important;
}

/deep/ .el-table__body tr.current-row .el-button--text span {
    color: white !important;
}
</style>
