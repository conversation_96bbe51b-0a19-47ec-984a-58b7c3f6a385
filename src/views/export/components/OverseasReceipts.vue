<template>
  <div>
    <el-dialog
      title="海外收據"
      :visible.sync="showExport"
      width="800px"
      :show-close="true"
      :close-on-click-modal="false"
      :before-close='close'
    >
      <el-form :inline="true" class="demo-form-inline" @keyup.enter.native="requData()">
        <el-form-item label="Autopay No" label-width="150px" required>
          <el-input
            v-model="societyData.autopayNo"
            placeholder="雙擊查詢"
            @dblclick.native="getAutopay()"
            @change="changeAuto"
            style="width: 150px"
          ></el-input>
        </el-form-item>
        <el-form-item label="Autopay Description" label-width="170px">
          <el-input
            v-model="societyData.autopayDescription"
            placeholder=""
            style="width: 150px"
            readonly
          ></el-input>
        </el-form-item>
        <el-form-item label="Society Code" label-width="150px">
          <el-input
            v-model="societyData.societyCode"
            placeholder="雙擊查詢"
            @dblclick.native="getSocName()"
            @change="changeSoc"
            style="width: 150px"
          ></el-input>
        </el-form-item>
        <el-form-item label="Society Name" label-width="170px">
          <el-input
            v-model="societyData.name"
            placeholder=""
            readonly
            style="width: 150px"
          ></el-input>
        </el-form-item>
        <el-form-item label="Dist No" label-width="150px">
          <el-input
            v-model="societyData.distNo"
            placeholder=""
            style="width: 150px"
          ></el-input>
        </el-form-item>
        <el-form-item label="Autopay Date" label-width="150px">
          <el-date-picker
            v-model="societyData.autopayDate"
            type="date"
            placeholder="請選擇日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            style="width: 150px"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="Bank Info" label-width="150px">
          <el-checkbox v-model="societyData.bankInfo">包含銀行信息</el-checkbox>
        </el-form-item>
        <el-form-item label="按Society分檔" label-width="150px">
          <el-checkbox v-model="societyData.groupBySociety">按Society分檔</el-checkbox>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button type="primary" @click="requData()">導出</el-button>
      </div>
    </el-dialog>
    <autopay-no ref="autopay" :autopayNo='autopayNo' @checkAutopay='checkAutopay' @flag='flag1=true'></autopay-no>
    <society-name ref="society" :societyCode='societyCode' @checkSociet='checkSociet' @flag='flag3=true'></society-name>
  </div>
</template>
<script>
import autopayNo from './autopayNo';
import societyName from './societyName';
export default {
  name: "",
  data() {
    return {
      showExport: false,
      exportType: "member",
      societyData: {
        autopayNo: "",
        autopayDescription: "",
        societyCode:'',
        name:'',
        distNo:'',
        autopayDate: '',
        bankInfo: false,
        groupBySociety: false
      },
      autopayNo:'',
      societyCode:'',
      flag1:true,
      flag3:true,
    };
  },
  components: { autopayNo,societyName },
  methods: {
    getSocName(){
      if(this.flag3){
        this.societyCode = ''
        this.flag3=false
        this.$refs.society.socDataD()
      }
    },
    changeSoc(data){
      if(data){
        if(this.flag3){
          this.societyCode = data
          this.flag3=false
          this.$nextTick(()=>{
            this.$refs.society.socDataC()
          })
        }
      }else{
        this.$set(this.societyData,'name','')
      }
    },
    checkSociet(data){
      this.flag3=true
      this.$set(this.societyData,'societyCode',data.societyCode)
      this.$set(this.societyData,'name',data.societyName)
    },
    close(){
      this.showExport=false
      this.societyData = {
        autopayNo: "",
        autopayDescription: "",
        societyCode:'',
        name:'',
        distNo:'',
        autopayDate: '',
        bankInfo: false,
        groupBySociety: false
      }
    },
    exportList() {
      this.showExport = true;
    },
    checkAutopay(data){
      this.flag1=true
      this.$set(this.societyData,'autopayNo',data.autopayNo)
      this.$set(this.societyData,'autopayDescription',data.autopayDescription)
    },
    exportList() {
      this.showExport = true;
    },
    changeAuto(data){
      if(data){
        if(this.flag1){
          this.autopayNo = data
          this.flag1=false
          this.$nextTick(()=>{
            this.$refs.autopay.autopayDataC()
          })
        }
      }else{
        this.$set(this.societyData,'autopayDescription','')
      }

    },
    getAutopay() {
      if(this.flag1){
        this.flag1=false
        this.autopayNo = ''
        this.$refs.autopay.autopayDataD()
      }
    },
    requData() {
      let params = this.$utils.copy(this.societyData);
      if(!params.autopayNo){
        this.$toast({ tips: "Autopay No不能為空" });
        return
      }
      this.$http.get(`/dist/autoPay/Report/reportDistAutoPay780`, {params, responseType: "blob"})
        .then((res) => {
          // this.showExport = false;
          let tempBlob = new Blob([res.data], { type: 'application/json' })
          let reader = new FileReader()
          reader.onload = e => {
            let res1 = e.target.result
            // 此处对fileReader读出的结果进行JSON解析
            // 可能会出现错误，需要进行捕获
            try {
              let json = JSON.parse(res1)
              this.$toast({ tips: json.message })
              // console.log('========',json)
              //正常json数据格式
            } catch (err) {
              // 该异常为无法将字符串转为json
              // 说明返回的数据是一个流文件
              // 不需要处理该异常，只需要捕获即刻
              this.close()
              this.$utils.downloadByBlobPDF(
              res.data,
              res.headers["content-disposition"]
                ? res.headers["content-disposition"].split(";")[1]
                : "=海外收據導出"
              );
            }
          }
          // 将blob对象以文本的方式读出，读出完成后将会执行 onload 方法
          reader.readAsText(tempBlob)
        });
    },
  },
};
</script>
<style scoped>
/deep/ .el-dialog__body {
  padding-top: 0;
}
/deep/ .el-radio + .el-radio {
  margin-left: 0;
}
</style>
