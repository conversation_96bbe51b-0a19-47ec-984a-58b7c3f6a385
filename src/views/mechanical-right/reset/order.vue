<template>
    <div class="p-t-40">
        <top-bar :noShow="false" @save="saveOrder"></top-bar>
        <el-collapse v-model="activeNames">
            <!-- 订單信息 -->
            <el-collapse-item title="Order Info" class="step-jump" name="1">
                <el-form :inline="true" class="p-t-20" label-width="146px">
                    <div class="orderInfo clearfix">
                        <p style="margin-right:50px;">Dist No：{{salesInfo.distNo}}</p>
                        <p>Producer ID：{{salesInfo.producerId}}</p>
                        <p>Producer Company：{{salesInfo.producerName}}</p>
                        <p>Batch No：{{salesInfo.batchNo}}</p>
                        <!-- <el-form-item label="Dist No">
                            <el-input type="text" v-model="salesInfo.distNo" readonly></el-input>
                        </el-form-item>
                        <el-form-item label="Producer ID">
                            <el-input type="text" v-model="salesInfo.producerId" readonly></el-input>
                        </el-form-item>

                        <el-form-item label="Producer Company" label-width="180px">
                            <el-input type="text" v-model="salesInfo.producerName" readonly></el-input>
                        </el-form-item>
                        <el-form-item label="Batch No">
                            <el-input type="text" v-model="salesInfo.batchNo" readonly></el-input>
                        </el-form-item> -->
                    </div>
                    <!-- <div>
                        <el-form-item label="Batch No">
                           <el-input type="text" v-model="salesInfo.batchNo" readonly></el-input>
                        </el-form-item>
                        <el-form-item label="Total Amount">
                            <el-input type="text" v-model="salesInfo.amount" readonly></el-input>
                        </el-form-item>
                        <el-form-item label="Exchange Rate">
                            <el-input type="text" v-model="salesInfo.exchangeRate" readonly></el-input>
                        </el-form-item>
                    </div>
                    <div>
                        <el-form-item label="Currency">
                            <el-input type="text" v-model="salesInfo.currencyName" readonly></el-input>
                        </el-form-item>
                        <el-form-item label="Local Amount">
                            <el-input type="text" v-model="salesInfo.localEquivlent" readonly></el-input>
                        </el-form-item>
                    </div>
                    <div>
                        <el-form-item label="Sc ID">
                            <el-input type="text" v-model="salesInfo.id" readonly></el-input>
                        </el-form-item>
                        <el-form-item label="Sc No">
                           <el-input type="text" v-model="salesInfo.scNo" readonly></el-input>
                        </el-form-item>
                    </div> -->
                </el-form>
            <!-- </el-collapse-item>
            <el-collapse-item title="Base Info" class="step-jump" name="2"> -->
                <el-form :inline="true" class="p-t-20" label-width="146px">
                    <div>
                        <el-form-item label="From Period" required>
                            <!-- <el-input type="text" v-model="baseInfo.fromPeriod"></el-input> -->
                            <!-- <el-input  v-model="baseInfo.fromPeriod" v-dateFmt></el-input> -->
                            <date-picker v-model="baseInfo.fromPeriod" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd"></date-picker>
                        </el-form-item>
                        <el-form-item label="To Period" required>
                            <!-- <el-input type="text" v-model="baseInfo.toPeriod"></el-input> -->
                            <!-- <el-input v-dateFmt v-model="baseInfo.toPeriod"></el-input> -->
                            <date-picker v-model="baseInfo.toPeriod" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd"></date-picker>
                        </el-form-item>
                    </div>
                    <el-form-item label="Sc ID">
                        <el-input type="text" v-model="baseInfo.scId" readonly></el-input>
                    </el-form-item>
                    <el-form-item label="Sc No">
                        <!-- <el-input type="text" v-model="baseInfo.scNo" @change="scIdChange"></el-input> -->
                        <el-input type="text" v-model="baseInfo.scNo" placeholder="雙擊查詢" readonly @dblclick.native="selectSCdata"></el-input>
                    </el-form-item>
                    <el-form-item label="Country">
                        <el-input type="text" v-model="baseInfo.countryCode" readonly></el-input>
                    </el-form-item>
                    <el-form-item label="Type">
                        <el-input type="text" v-model="baseInfo.type" readonly></el-input>
                    </el-form-item>
                    <div>
                        <el-form-item label="English Title">
                            <el-input type="text" style="width: 500px;" v-model="baseInfo.titleEn" readonly></el-input><span class="selectSCdata" @click="selectSCdata">L</span>
                        </el-form-item>
                        <el-form-item label="Chinese Title">
                            <el-input type="text" style="width: 500px;" v-model="baseInfo.titleCh" readonly></el-input><span class="selectSCdata" @click="selectSCdata">L</span>
                        </el-form-item>
                    </div>
                    <div>
                        <el-form-item label="Unit Price" required>
                           <el-input type="text" v-model="baseInfo.unitPrice"></el-input>
                        </el-form-item>
                         <el-form-item label="Unit Sold" required>
                           <el-input type="text" v-model="baseInfo.unitSold"></el-input>
                        </el-form-item>
                    </div>
                    <div>
                        <el-form-item label="Roy Rate" required>
                            <el-input type="text" v-model="baseInfo.royRate"></el-input>
                        </el-form-item>
                        <el-form-item label="Tracks" required>
                            <el-input type="text" v-model="baseInfo.tracks"></el-input>
                        </el-form-item>
                    </div>
                    
                    <!-- <div>
                        <el-form-item label="Sum Sc Work Total" label-width="200px">
                            <el-input type="text" v-model="totalAmount" readonly></el-input>
                        </el-form-item>
                    </div> -->
                     <div>
                        <el-form-item label="Sc Roy">
                            <el-input type="text" v-model="totalAmount" readonly></el-input>
                        </el-form-item>
                    </div>
                </el-form>
            </el-collapse-item>
            <el-collapse-item class="step-jump" name="3">
                 <el-table :empty-text="tableresult"   stripe :data="workData" border @row-click="changeWork" :row-class-name="tableRowClassName">
                    <!-- <el-table-column label="Seq">
                        <template slot-scope="scope">
                            <el-input type="text" v-model="scope.row.distMrdSalesWork.workSeqNo"></el-input>
                        </template>
                    </el-table-column> -->
                    <el-table-column label="Work Title">
                        <template slot-scope="scope">
                            <el-input type="text" v-model="scope.row.distMrdSalesWork.workTitle" @dblclick.native="getWork(scope.$index, scope.row)" readonly></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="Work ID">
                        <template slot-scope="scope">
                            <el-input type="text" v-model="scope.row.distMrdSalesWork.workId" @dblclick.native="getWork(scope.$index, scope.row)" readonly></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="Work Soc">
                        <template slot-scope="scope">
                            <el-input type="text" v-model="scope.row.distMrdSalesWork.workSocietyCode" readonly></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="Genre">
                        <template slot-scope="scope">
                            <el-input type="text" v-model="scope.row.distMrdSalesWork.genreCode" readonly></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="Share">
                        <template slot-scope="scope">
                            <el-input type="text" v-model="scope.row.distMrdSalesWork.ipShare"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="Work Roy">
                        <template slot-scope="scope">
                            <el-input type="text" v-model="scope.row.distMrdSalesWork.grossWorkRoy"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="M">
                        <template slot-scope="scope">
                            <el-checkbox :value="scope.row.distMrdSalesWork.minRoyFlag" true-label="Y" false-label="N" @change="changeFlag(scope.row)"></el-checkbox>
                            <!-- <el-checkbox :value="scope.row.distMrdSalesWorkIpList.length == 0" readonly></el-checkbox> -->
                            <!-- <el-button @click="editIpShare(scope.row, scope.$index)">編輯</el-button> -->
                        </template>
                    </el-table-column>
                    <!-- <el-table-column label="opteration">
                        <template slot-scope="scope">
                            <span class="operation">
                                <i class="el-icon-delete" @click="deleteData(scope.$index, workData)"></i>
                            </span>
                        </template>
                    </el-table-column> -->
                </el-table>
                <!-- <div class="add-new"><el-button type="primary" @click="addData(workData)">新 增</el-button></div> -->
                <div class="add-new"><el-button type="primary" @click="pullData(workData)">拉 取</el-button></div>
            </el-collapse-item>
            <!-- end -->
        </el-collapse>
        <!-- 編輯 IP Share -->
        <!-- <el-dialog :visible.sync="ipShareShow" :close-on-click-modal="false"> -->
            <!-- <el-collapse-item title="IP Share" class="step-jump" name="5"> -->
            <div style="position: relative;" v-if="ipShareShow">
                <p class="IPShare">IP Share</p>
                <div class="add-new" style="top:0"><el-button type="primary" @click="addIpShare(ipShareData)">新 增</el-button></div>
                <el-form ref="editForm" :inline="true" label-width="160px">
                    <el-table :empty-text="tableresult"   stripe :data="ipShareData" border>
                        <el-table-column label="Name No">
                            <template slot-scope="scope">
                                <el-input type="text" v-model="scope.row.ipNameNo" @dblclick.native="getIp(scope.$index, scope.row)" readonly></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column label="Name">
                            <template slot-scope="scope">
                                <el-input type="text" v-model="scope.row.name" @dblclick.native="getIp(scope.$index, scope.row)" readonly></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column label="Chinese Name" width="132px">
                            <template slot-scope="scope">
                                <el-input type="text" v-model="scope.row.chinese_name" readonly></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column label="Work Ip Role">
                            <template slot-scope="scope">
                                <el-input type="text" v-model="scope.row.workIpRole"></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column label="Share">
                            <template slot-scope="scope">
                                <el-input type="text" v-model="scope.row.distShare"></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column label="Work Roy">
                            <template slot-scope="scope">
                                <el-input type="text" v-model="scope.row.grossWorkRoy"></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column label="opteration" width="110px">
                            <template slot-scope="scope">
                                <span class="operation">
                                    <i class="el-icon-delete" @click="deleteIpShare(scope.$index, ipShareData)"></i>
                                </span>
                            </template>
                        </el-table-column>
                    </el-table>
                    <!-- <div class="t-c" style="margin-top: 20px;">
                        <el-form-item>
                            <el-button type="primary" @click="addIpShare(ipShareData)">新 增</el-button>
                            <el-button type="primary" @click="saveIpShare()">確定</el-button>
                            <el-button type="" @click="cancelIpShare()">取消</el-button>
                        </el-form-item>
                    </div> -->
                </el-form>
            </div>
        <!-- </el-dialog> -->
            <!-- </el-collapse-item> -->
        <select-ip v-if="IpTableVisible" ref="selectIpCom" :search="ipSearch" @checkIp="checkIp"></select-ip>

        <select-work v-if="workTableVisible" ref="selectWorkCom" :search="workSearch" @checkWork="checkWork"></select-work>
        <select-sc v-if="scTableVisible" ref="selectOrder" :search="scSearch" @checkWork="scCheckWork"></select-sc>
    </div>
</template>
<script>
import selectWork from '../../../components/select-work/index'
import selectSc from '../../../components/select-SC/index'
import selectIp from '../../../components/select-ip/index'

export default {
    data(){
        return {
            salesInfo: {},
            baseInfo: {},
            activeNames: ['1', '2', '3'],
            rightData: [{}],
            contactData: [],
            workData: [],tableresult:' ',
            show: false,

            // 查詢作品
            editIndex: 0,
            workTableVisible: false,
            workSearch: {},

            // 查詢ip
            editIpIndex: 0,
            IpTableVisible: false,
            ipSearch: {},

            editIpShareIndex: 0,
            editIpShareWorkId: '',

            // 作品ipshare手動添加
            ipShareShow: false,
            ipShareData: [],


            scTableVisible:false,
            scSearch:{}
        }
    },
    computed: {
        totalAmount: function(){
           let total = 0;
           this.workData.forEach( item => {
               let amount = item.distMrdSalesWork.grossWorkRoy ? item.distMrdSalesWork.grossWorkRoy : 0;
               total = new BigNumber(total).plus( new BigNumber(amount)).toFixed(6);
           })
           return total;
        }
    },
    components: {
        selectWork,
        selectIp,
        selectSc
    },
    created(){
        this.init();
    },
    methods: {
        tableRowClassName ({row, rowIndex}) {
            //把每一行的索引放进row
            row.index = rowIndex;
        },
        changeWork(row, column, event){
            if(this.ipShareData.length){
                this.saveIpShare()
            }
            console.log('row: ', row);
            if(row.distMrdSalesWork.minRoyFlag==='Y'){
                this.editIpShare(row,row.index)
            }else{
                this.ipShareShow = false;

            }
                  
        },
        changeFlag(data){
            console.log(data)
            if(!data.distMrdSalesWork.minRoyFlag || data.distMrdSalesWork.minRoyFlag=='N'){
                data.distMrdSalesWork.minRoyFlag='Y'
                this.editIpShare(data,data.index)
            }else{
                data.distMrdSalesWork.minRoyFlag='N'
                let show = true
                this.workData.forEach((item,index)=>{
                    if(show){
                        if(item.distMrdSalesWork.minRoyFlag=='Y'){
                            this.editIpShare(item,index)
                            show=false
                        }
                    }
                })
                if(show){
                    this.ipShareData= []
                    this.ipShareShow=false
                }
            }
        },
        scIdChange(data){
            this.getScData(data,'no')
        },
        getScData(data,status){
            if(data){
                let ajaxData={}
                if(status=='id'){
                    ajaxData.id = this.baseInfo.scId
                }else if(status=='no'){
                    ajaxData.scNo = this.baseInfo.scNo.trim()
                }
                ajaxData.page_num = 1;
                ajaxData.page_size = 10;
                this.$http.get('/distMrdSc', {params: ajaxData}).then( res => {
                    if(res.success){
                        let temp = res.data.data.list[0] || {};
                        if(status=='id'){
                            this.$set( this.baseInfo, 'scNo', temp.scNo);
                        }
                        console.log('distMrdScWorkList')
                        console.log(temp.distMrdScWorkList)
                        // this.$set( this.baseInfo, 'scNo', temp.scNo);
                        this.$set( this.baseInfo, 'scId', temp.id);
                        this.$set( this.baseInfo, 'countryCode', temp.countryCode);
                        this.$set( this.baseInfo, 'type', temp.scTypeCode);
                        this.$set( this.baseInfo, 'titleEn', temp.scTitleEn);
                        this.$set( this.baseInfo, 'titleCh', temp.scTitleCh);
                    }
                })
            }else{
                this.$set( this.baseInfo, 'scNo', '');
                this.$set( this.baseInfo, 'countryCode', '');
                this.$set( this.baseInfo, 'type', '');
                this.$set( this.baseInfo, 'titleEn', '');
                this.$set( this.baseInfo, 'titleCh', '');
            }
        },
        init(){
            this.queryInfo();
            this.querySalesInfo();
            // this.getScData(this.$route.query.scId,'id')
        },
        queryInfo(){
            if(!this.$route.query.id){
                this.baseInfo = {
                    
                };
                this.workData = [];
                return;
            }
                              this.tableresult = '數據加載中...'
            this.$http.get('/dist/mrd/listDistMrdSalesDetails/' + this.$route.query.id).then( res => {
                if(res.success){
                    // this.$utils.copy(res.data.data.distMrdSalesDetails)
                    this.baseInfo = this.$utils.copy(res.data.data.distMrdSalesDetails);
                    this.workData = this.$utils.copy(res.data.data.distMrdSalesWorkVoList);
                    this.baseInfo.titleEn=this.baseInfo.scTitleEn
                    this.baseInfo.titleCh=this.baseInfo.scTitleCh
                    this.baseInfo.type=this.baseInfo.typeCode
                    console.log('this.workData')
                    console.log(this.workData)
                    console.log(this.baseInfo)
                    let show = true
                    this.workData.forEach((item,index)=>{
                        if(show){
                            if(item.distMrdSalesWork.minRoyFlag=='Y'){
                                this.editIpShare(item,index)
                                show=false
                            }
                        }
                    })
                }
            this.tableresult = this.workData.length == 0 ? '暫無數據' : ' '
            })
        },
        querySalesInfo(){
            this.$http.get('/dist/mrd/listDistMrdSalesWithPage/' + this.$route.query.salesId).then( res => {
                if(res.success){
                    this.salesInfo = res.data.data.distMrdSales;
                    if(!this.$route.query.id){
                        this.baseInfo = {
                            producerId: this.salesInfo.producerId,
                            distNo: this.salesInfo.distNo,
                            batchNo: this.salesInfo.batchNo

                        }
                    }
                }
            })
        },
        // 拉取
        pullData(data){
            console.log(data)
            //  + this.baseInfo.scId
            if(!this.baseInfo.scId){
                this.$toast({tips: '请先选择Sc No'})
                return 
            }
            this.$http.get('/distMrdScWork/pullDistMrdScWorkList', {params: {scId : this.baseInfo.scId}}).then( res => {
                if(res.success){
                    res.data.data.forEach(item=>{
                        // console.log(item)
                        if(data.length){
                            data.forEach(item1=>{
                                if(item.workId===item1.distMrdSalesWork.workId&&item.workSocietyCode===item1.distMrdSalesWork.workSocietyCode){
                                    return
                                }else{
                                    this.workDataPushData(item)
                                }
                            })
                        }else{
                            this.workDataPushData(item)
                        }
                    })
                    
                }
            })
        },
        workDataPushData(item){
            item.workSeqNo = item.seqNo
            item.id=''
            item.distNo = this.salesInfo.distNo
            item.producerId= this.baseInfo.producerId
            item.salesDetailId= this.baseInfo.id
            // item.minRoyFlag=item.scClaimFlag
            this.workData.push({
                distMrdSalesWork: item,
                distMrdSalesWorkIpList: []
            })
        },
        addData(data){
            data.push({
                distMrdSalesWork: {
                    distNo: this.salesInfo.distNo,
                    producerId: this.baseInfo.producerId,
                    salesDetailId: this.baseInfo.id,
                    batchNo: this.baseInfo.batchNo,
                    batchSeqNo: this.baseInfo.batchSeqNo,
                    salesId: this.baseInfo.salesId,

                    workTitle: '',
                    workId: '',
                    workSocietyCode: '',
                    genreCode: ''

                },
                distMrdSalesWorkIpList: []})
                
        },
        /**
         * 选取作品
         */
        getWork(index, row) {
            return
            this.editIndex = index;
            this.workTableVisible= true;
            this.workSearch = {
                workId: row.distMrdSalesWork.workId,
                soc: row.distMrdSalesWork.workSocietyCode,
                title: row.distMrdSalesWork.workTitle
            }
            this.$nextTick( () => {
                this.$refs.selectWorkCom.init();
            })
        },
        checkWork(info){
            // this.workData[this.editIndex].distMrdSalesWork.workTitle = info.title ? info.title : info.title_en;
            // this.workData[this.editIndex].distMrdSalesWork.workId = info.work_id;
            // this.workData[this.editIndex].distMrdSalesWork.workSocietyCode = info.work_society_code;
            // this.workData[this.editIndex].distMrdSalesWork.genreCode = info.genre_code;

            this.$set(this.workData[this.editIndex].distMrdSalesWork, 'workTitle', info.title ? info.title : info.title_en);
            this.$set(this.workData[this.editIndex].distMrdSalesWork, 'workId', info.work_id);
            this.$set(this.workData[this.editIndex].distMrdSalesWork, 'workSocietyCode', info.work_society_code);
            this.$set(this.workData[this.editIndex].distMrdSalesWork, 'genreCode', info.genre_code);
        },
        // order info 选择
        selectSCdata(){
            this.scTableVisible= true;
            this.scSearch={
                scNo: this.baseInfo.scNo,
                scId: this.baseInfo.scId,
            }
            this.$nextTick( () => {
                this.$refs.selectOrder.init();
            })
        },
        scCheckWork(info){
            this.$set( this.baseInfo, 'scNo', info.scNo);
            this.$set( this.baseInfo, 'scId', info.id);
            this.$set( this.baseInfo, 'countryCode', info.countryCode);
            this.$set( this.baseInfo, 'type', info.scTypeCode);
            this.$set( this.baseInfo, 'titleEn', info.scTitleEn);
            this.$set( this.baseInfo, 'titleCh', info.scTitleCh);
        },
        /**
         *
         * 选取IP
         */
        getIp(index, row){
            this.editIpIndex = index;
            this.IpTableVisible = true;
            this.ipSearch = {
                name_no: row.ipNameNo,
                name: row.name
            }
            this.$nextTick( () => {
                this.$refs.selectIpCom.init();
            })
        },
        checkIp(info){
            console.log('info')
            console.log(info)
            // this.ipShareData[this.editIpIndex].ipNameNo = info.ip_name_no;
            // this.ipShareData[this.editIpIndex].name = info.name;
            // this.ipShareData[this.editIpIndex].chineseName = info.chinese_name;
            this.$set(this.ipShareData[this.editIpIndex], 'ipNameNo', info.ip_name_no);
            this.$set(this.ipShareData[this.editIpIndex], 'name', info.name);
            this.$set(this.ipShareData[this.editIpIndex], 'chinese_name', info.chinese_name);
        },
        /**
         * IPShare 操作
         */
        editIpShare(row, index){
            this.editIpShareIndex = index;
            this.editIpShareWorkId = row.distMrdSalesWork.id;
            this.ipShareShow = true;
            this.ipShareData = this.$utils.copy(row.distMrdSalesWorkIpList);
            console.log('0');
            console.log(row);
            console.log(this.ipShareData.ipNameMap);
            this.ipShareData.forEach( item => {
                if(item.ipNameMap){
                    item.name = item.ipNameMap.name;
                    item.chinese_name = item.ipNameMap.chinese_name;
                }
            })

        },
        addIpShare(data){
            data.push({
                ipNameNo: '',
                name: '',
                chineseName: '',
                distNo: this.salesInfo.distNo,
                salesWorkId: this.editIpShareWorkId
            })
        },
        deleteIpShare(index, data){
            this.$msgbox.confirm('確認刪除?', '提示', {
                confirmButtonText: '確認',
                cancelButtonText: '取消',
                type: 'warning'
            }).then( () => {
                data.splice(index,1)
            })
        },
        saveIpShare(){
            this.$set(this.workData[this.editIpShareIndex], 'distMrdSalesWorkIpList', this.$utils.copy(this.ipShareData));
            this.ipShareShow = false;
        },
        cancelIpShare(){
            this.ipShareShow = false;
        },
        deleteData(index, data){
            console.log(data)
            this.$msgbox.confirm('確認刪除?', '提示', {
                confirmButtonText: '確認',
                cancelButtonText: '取消',
                type: 'warning'
            }).then( () => {

                if(!data[index].distMrdSalesWork.id){
                    data.splice(index,1);
                    return;
                }

                this.$http.delete('/dist/mrd/delDistMrdSalesWork/' + data[index].distMrdSalesWork.id).then( res => {
                    if(res.success){
                        this.$toast({tips: '删除成功'})
                        data.splice(index,1);
                    }
                })
            })

        },
        saveVerify(){
            let next = false
            if(!this.baseInfo.fromPeriod){
                next=true
                this.$toast({tips: '開始時間不能為空'})
                return next
            }
            if(!this.baseInfo.toPeriod){
                next=true
                this.$toast({tips: '結束時間不能為空'})
                return next
            }
            if(!this.baseInfo.unitPrice){
                next=true
                this.$toast({tips: 'UnitPrice不能為空'})
                return next
            }
            if(!this.baseInfo.unitSold){
                next=true
                this.$toast({tips: 'UnitSold不能為空'})
                return next
            }
            if(!this.baseInfo.royRate){
                next=true
                this.$toast({tips: ' RoyRate不能為空'})
                return next
            }
            if(new Date(this.baseInfo.fromPeriod).getTime()>new Date(this.baseInfo.toPeriod).getTime()){
                next=true
                this.$toast({tips: '開始時間不能大於結束時間'})
                return next
            }
            if(!this.baseInfo.tracks){
                next=true
                this.$toast({tips: 'Tracks不可為空'})
                return next
            }
            let num = this.baseInfo.tracks.toString().trim()
            var reg=new RegExp("^[0-9]*$");
            if(!reg.test(num)){
                next=true
                this.$toast({tips: 'Tracks为纯数字'})
                return next
            }
            return next
        },
        saveOrder(){
            console.log('this.workData')
            console.log(this.workData)
            if(this.saveVerify()){
                return
            }
            if(this.ipShareData.length){
                this.saveIpShare()
            }
            let ajaxData = {
                distMrdSalesDetails: this.baseInfo,
                distMrdSalesWorkVoList: this.workData
            }
            ajaxData.distMrdSalesDetails.salesId = this.$route.query.salesId;
            console.log('sales: ', this.$route.query.salesId);
            this.$http.post('/dist/mrd/saveDistMrdSalesDetails', ajaxData).then( res => {
                if(!res.success || res.data.code != 200){
                    this.$toast({tips: res.data.message})
                }else{
                    this.$toast({tips: '保存成功'});
                    this.$bus.$emit('updateReset');
                    this.$bus.$emit('closeCurrentTab');
                }
            })
        }
    }
}
</script>
<style lang="scss" scoped>
    @import '../../../assets/scss/works.scss';
    .orderInfo{
        padding-left: 100px;
    }
    .orderInfo p{
        float: left;
        margin-right: 30px;
    }
    .selectSCdata{
        border: 1px solid #dcdfe6;
        margin-left: 10px;
        padding: 5px;
        color: #aaaaaa;
        border-radius: 3px;
        text-shadow: 0 1px 1px #aaa;
        cursor: pointer;
        // display: inline-block;
    }
    .IPShare{
        color: #444 ;
        font-size: 18px;
        font-weight: 600;
        // margin-top: 10px;
        // border-top: 1px solid #ebeef5;
        // border-bottom: 1px solid #ebeef5;
        // padding: 5px 0 10px;
        
    }
</style>

