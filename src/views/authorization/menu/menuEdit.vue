<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
      <el-form-item label="类型" prop="type">
        <el-radio-group v-model="dataForm.type">
          <el-radio v-for="(type, index) in dataForm.typeList" :label="index" :key="index">{{ type }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item :label="dataForm.typeList[dataForm.type] + '名称'" prop="name">
        <el-input v-model="dataForm.name" :placeholder="dataForm.typeList[dataForm.type] + '名称'"></el-input>
      </el-form-item>
      <el-form-item label="上级菜单" prop="parentName">
        <el-popover
          ref="menuListPopover"
          placement="bottom-start"
          trigger="click">
          <el-tree
            :data="menuList"
            :props="menuListTreeProps"
            node-key="id"
            ref="menuListTree"
            @current-change="menuListTreeCurrentChangeHandle"
            :default-expand-all="true"
            :highlight-current="true"
            :expand-on-click-node="false"
            style="max-height: 200px; overflow: auto;"
            >
          </el-tree>
        </el-popover>
        <el-input v-model="dataForm.parentName" v-popover:menuListPopover :readonly="true" placeholder="点击选择上级菜单" class="menu-list__input"></el-input>
      </el-form-item>
      <el-form-item v-if="dataForm.type === 1" label="菜单路由" prop="url">
        <el-input v-model="dataForm.url" placeholder="菜单路由"></el-input>
      </el-form-item>
        <el-form-item v-if="dataForm.type === 1" label="path" prop="path">
        <el-input v-model="dataForm.path" placeholder="path"></el-input>
      </el-form-item>
      <el-form-item v-if="dataForm.type !== 0" label="授权标识" prop="perms">
        <el-input v-model="dataForm.perms" placeholder="多个用逗号分隔, 如: user:list,user:create"></el-input>
      </el-form-item>
      <el-form-item v-if="dataForm.type !== 2" label="排序号" prop="orderNum">
        <el-input-number v-model="dataForm.orderNum" controls-position="right" :min="0" label="排序号"></el-input-number>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import { treeDataTranslate } from '@/utils'
  import Icon from '@/icons'
  export default {
    data () {
      var validateUrl = (rule, value, callback) => {
        if (this.dataForm.type === 1 && !/\S/.test(value)) {
          callback(new Error('菜单URL不能为空'))
        } else {
          callback()
        }
      }
      return {
        visible: false,
        dataForm: {
          id: 0,
          type: 1,
          typeList: ['目录', '菜单', '按钮'],
          name: '',
          parentId: 0,
          parentName: '',
          url: '',
          perms: '',
          orderNum: 0,
          icon: '',
          iconList: [],
          path:''
        },
        dataRule: {
          name: [
            { required: true, message: '菜单名称不能为空', trigger: 'blur' }
          ],
          parentName: [
            { required: true, message: '上级菜单不能为空', trigger: 'change' }
          ],
          url: [
            { validator: validateUrl, trigger: 'blur' }
          ],
          path:[{ required: true, message: 'path不能为空', trigger: 'blur' }],
        },
        menuList: [],
        menuListTreeProps: {
          label: 'name',
          children: 'children'
        }
      }
    },
    created () {
      this.iconList = Icon.getNameList()
    },
    methods: {
      init (id) {
        this.dataForm.id = id || 0
        this.$http.get("/sys/menus/select").then(({data}) => {
            console.log(data)
          this.menuList = this.arrtree(data.data,'-1')
          console.log(this.menuList)
        }).then(() => {
          this.visible = true
          this.$nextTick(() => {
            this.$refs['dataForm'].resetFields()
          })
        }).then(() => {
          if (!this.dataForm.id) {
            // 新增
            // this.menuListTreeSetCurrentNode()
          } else {
            console.log(this.dataForm.id)
            // 修改
            this.$http.get("/sys/menus/info/" + this.dataForm.id).then(({data}) => {
              console.log(data)
              this.dataForm.id = data.data.id
              this.dataForm.type = data.data.type
              this.dataForm.name = data.data.name
              this.dataForm.parentId = data.data.parentId
              this.dataForm.url = data.data.url
              this.dataForm.perms = data.data.perms
              this.dataForm.orderNum = data.data.orderNum
              this.dataForm.path = data.data.path
              this.menuListTreeSetCurrentNode()
            })
          }
        })
      },
      //处理数组变树结构
      arrtree(data,parentId){
        var itemArr=[];
        for(var i=0;i<data.length;i++){ 
          var node=data[i];
          //data.splice(i, 1)
          if(node.parentId==parentId ){ 
            var newNode={
              id:node.id,
              type:node.type,
              name:node.name,
              parentId:node.parentId,
              url:node.url,
              nameEn:node.nameEn,
              orderNum:node.orderNum,
              perms:node.perms,
              children:this.arrtree(data,node.id)
            };
            itemArr.push(newNode);              
          }
        }
        return itemArr;
      },
      // 菜单树选中
      menuListTreeCurrentChangeHandle (data, node) {
          console.log(data)
        this.dataForm.parentId = data.id
        this.dataForm.parentName = data.name
      },
      // 菜单树设置当前选中节点
      menuListTreeSetCurrentNode () {
        this.$refs.menuListTree.setCurrentKey(this.dataForm.parentId)
        this.dataForm.parentName = (this.$refs.menuListTree.getCurrentNode() || {})['name']
      },
      // 图标选中
      iconActiveHandle (iconName) {
        this.dataForm.icon = iconName
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            var saveOrUpdata = !this.dataForm.id ? 'save' : 'update'
            console.log(this.dataForm)
            this.$http({
              url: '/sys/menus/'+ saveOrUpdata,
              method: 'post',
              data: {
                'id': this.dataForm.id || undefined,
                'type': this.dataForm.type,
                'name': this.dataForm.name,
                'parentId': this.dataForm.parentId,
                'url': this.dataForm.url,
                'perms': this.dataForm.perms,
                'orderNum': this.dataForm.orderNum,
                'path': this.dataForm.path
              }
            }).then(({data}) => {
                console.log(data)
              if (data && data.code === 200) {
                this.visible = false
                this.$emit('refreshDataList')
                this.$toast({ tips: "操作成功" });
              } else {
                this.$toast({ tips: "操作失败" });
              }
            })
          }
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
    /deep/ *{
        font-size: 14px;
    }
    /deep/ .el-form-item__label{
        margin-left: 0;
    }
    /deep/ .el-tabs button span{
        color: #333 !important;
    }
    /deep/ .el-radio+.el-radio{
        line-height: 25px;
        margin-left: 0;
    }
</style>
