<template>
    <div class="mod-home">
        <div class="index">
            <el-row :gutter="20">
                <el-col :offset="6" :span="12">
                    <el-row>
                        <el-col>
                            <el-card shadow="hover" class="mgb20">
                                <!-- <h1>歡迎使用詞曲版權分配管理系統</h1> -->
                                <h1>歡迎使用MoneySystem</h1>
                                <div class="user-info">
                                    <!-- <img src="static/img/img.jpg" class="user-avator" alt=""> -->
                                    <!-- <div class="user-info-cont">
                                        <div class="user-info-name">{{name}}</div>
                                        <div>{{role ? role : '管理員'}}</div>
                                    </div> -->
                                </div>
                                <!-- <div class="user-info-list">上次登錄時間：<span>2020-01-01</span></div> -->
                            </el-card>
                        </el-col>
                    </el-row>
                </el-col>
            </el-row>
        </div>
    </div>
</template>

<script>
    export default {
        inject:['reload'],  
        data () {
            return {
                name: '',
                role: '',
            }
        },
        created(){
            if (location.href.indexOf("#reloaded") == -1) {
                location.href = location.href + "#reloaded";
                location.reload();
            }
        },
        methods: {}
    }
</script>

<style lang="scss" scoped>
 h1{
        text-align: center;
        margin: 14px 0 24px 0;
    }
    .el-row {
        margin-bottom: 20px;
    }

    .user-info {
        display: flex;
        align-items: center;
        padding-bottom: 20px;
        border-bottom: 2px solid #ccc;
        margin-bottom: 20px;
    }

    .user-avator {
        width: 120px;
        height: 120px;
        border-radius: 50%;
    }

    .user-info-cont {
        padding-left: 50px;
        flex: 1;
        font-size: 14px;
        color: #999;
    }

    .user-info-cont div:first-child {
        font-size: 30px;
        color: #222;
    }

    .user-info-list {
        font-size: 14px;
        color: #999;
        line-height: 25px;
    }

    .user-info-list span {
        margin-left: 10px;
    }

    .mgb20 {
        margin-bottom: 20px;
    }
</style>

<style>
    .mod-home {
        line-height: 1.5;
    }
    .usedimg{
        width: 100%;
        padding-bottom: 20px;
        border-bottom: 1px solid #d3d4d6;
    }
    .usedimg img{
        vertical-align: middle;
    }
    .imgitem{
        width: 200px;
        float: left;
        height: 120px;
        text-align: center;
        margin-top: 30px;
    }
    .tablebox .has-gutter{
        display: none!important;
    }
    .tabletitlebox{
        height: 40px;
        line-height: 40px;
        background: #d2d7db;
        opacity: 0.5;
        color: #666;
    }
    .el-card {
        margin-bottom: 20px;
    }
    .tabletitlebox1{
        height: 40px;
        line-height: 40px;
        background: #d2d7db;
        opacity: 0.5;
        color: #666;
    }
    .more{
        width: 100px;
        height: 40px;
        line-height: 40px;
        display: inline-block;
        float: right;
        text-align: center;
    }
</style>

