/**
 * Created by WebStorm.
 * User: nirongxu
 * Date: 2018/12/8
 * Description: 文件描述
 */
import enLocale from 'element-ui/lib/locale/lang/en'

const en = {
    menuName: {
        home: 'home',
        // 会员
        member: 'IPI/Member',
        addMember: 'Create Dummy Ipi',
        memberList: 'IPI Searching',
        ipTransfer: 'Ip Transfer List',
        // 作品
        work: 'Work',
        workList: 'Work Searching',
        workTransfer: 'Work Transfer List',
        // 作品去重
        duplicate: 'Duplicate Removal',
        cwrUpload: 'CWR Upload List',
        avrUpload: 'AVR Upload List',
        duplicateAuditList: 'Audit List',
        // 合約
        agreement: 'Agreement',
        agreementList: 'Search Agreement',
        // 清單管理
        templateConfig: 'Template Config',
        templateConfigList:'Template Config List',
        mapConfigList:'Map Config',
        classification:'Classification',
        usageList: 'Usage List',
        mergeTitle:'Special Sample',
        mergeList:'Merge List',
        listNumber: 'List Number',
        sampleDate: 'Sample Date',
        usageUploadList: 'Upload Usage List',
        usageListMatch: 'Usage List match',
        oUsage: 'Overseas Usage',
        oUsageList: 'Usage List',
        oUsageFileList:'List Detail',
        oAuditIp:'IP Audit',
        oUsageMatch: 'Usage List match',
        oAddData: 'Add Data',
        // claim
        claim: 'Claim',
        claimParameter: 'Claim Parameter',
        claimReport: 'Claim Report',
        claimUsage: 'Claim Usage',
        claimUpload: 'Upload Usage',
        claimList: 'Usage List',
        claimMatchList: 'Usage List Match',
        // 分配管理
        distribution: 'Distribution',
        distributionList: 'Distribution List',
        createDistribution: 'Create Distribution',
        royalties: 'royalties transaction',
        royaltiesMember: 'member',
        royaltiesSociety: 'society',
        specialCondition: 'special condition',
        // adjustment
        adjustment: 'adjustment',
        sdAdjustment: 'SD Adjustment',
        srAdjustment: 'SR Adjustment',
        // newMedia
        newMedia: 'Digital Mechanical Right',
        publishers: 'publishers',
        newMediaDistNo: 'New Media Dist No',
        mediaVersion: 'Media Version',
        // mechanical right
        mechanicalRight: 'Mechanical Right',
        producerType: 'Producer Type',
        producer: 'producer',
        mecSalesMaintenance: 'Mec Sales Maintenance',
        // 协会
        society: 'Society',
        societyList: 'Society List',
        // 支付
        payment: 'Payment',
        paymentList: 'Payment List',
        incomeDetail: 'Income Detail',
        // 系统管理
        system: 'System Manager',
        role: 'User Role Manager',
        user: 'User Manager',
        menu: 'Menu Manager',
        // Maintenance
        maintenance: 'Maintenance',
        category: 'List Category',
        source: 'List Source',
        performer: 'Performer',
        convert: 'Words Convert',  //中文轉拼音
        // 指纹比对
        sampleDNA: 'Sample DNA',
        sampleList: 'Sample List',
        DNASearch: 'DNA Search',
        export: 'Export',
        exportDiy: 'Export Diy',
        exportReport: 'Export Report',
        bank: 'Bank',
        branch: 'Branch',
        iswc: 'iswc',
        management:'Task Management',
        allTasks:'All the tasks',
        myTasks:'MyTasks',
    },
    routeNmae: {
        home: 'home',
        article: 'article',
        publishArticle: 'publishArticle',
        publishArticleEditor: 'publishArticleEditor',
        icon: 'icon',
        builtInIcon: 'builtInIcon',
        shuttleBox: 'shuttleBox',
        demoShuttle: 'demoShuttle',
        permissions: 'permissions',
        pageControl: 'pageControl',
        btnControl: 'btnControl',
        table: 'table',
        multiDataTable: 'multiDataTable',
        filterTable: 'filterTable',
        dragSort: 'dragSort',
        upload: 'upload',
        fileUpload: 'fileUpload',
        editor: 'editor',
        markdown: 'markdown',
        wangeditor: 'wangeditor',
        multiDirectory: 'multiDirectory',
        'menu2-1': 'menu2-1',
        'menu2-2': 'menu2-2',
        'menu2-3': 'menu2-3',
        'menu3-1': 'menu3-1',
        'menu3-2': 'menu3-2',
        'menu3-3': 'menu3-3',
        'menu4-1': 'menu4-1',
        'menu4-2': 'menu4-2',
        'menu5-1': 'menu5-1',
        systemSettings: 'systemSettings',
        navMenu: 'navMenu'
    },
    rightMenu: {
        close: 'close',
        closeOther: 'closeOther',
        closeAll: 'closeAll'
    },
    role: {
        superAdmin: 'superAdmin',
        admin: 'admin',
        ordinary: 'ordinary'
    },
    userDropdownMenu: {
        basicInfor: 'basicInfor',
        changePassword: 'changePassword',
        logout: 'logout'
    },
    ...enLocale   //  合并element-ui内置翻译
}
export default en
