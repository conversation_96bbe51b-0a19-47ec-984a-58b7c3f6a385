<template>
    <div>
        <div class="boxline">
            <div class="add-new">
                <el-button type="primary" v-if="isAuth('works:base:change')" @click="adddata3()">新 增</el-button>
            </div>
            <el-form ref="formTable" :model="tableForm" style="padding-top:50px"> 
                <el-table
                    :data="tableForm.tableData3"
                    border
                    stripe
                    style="width: 100%"
                    :header-cell-class-name="must">
                    <el-table-column
                        label="sourceType" class="must"
                        width="150px">
                        <template slot-scope="scope">
                            <el-form-item :prop="`tableData3.${scope.$index}.sourceType`" :rules="tableRules.sourceType">
                                <el-input :ref="'type' + scope.$index" v-model='scope.row.sourceType' placeholder="雙擊查詢" @keyup.native="sourceTypeChange(scope.$index,scope.row.sourceType)" @dblclick.native="getSourceType(scope.$index, scope.row)" @change="valueChange(scope.row, 'update')"></el-input>
                            </el-form-item>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="sourceCode"
                        label="Source Code"
                        width="180px">
                        <template slot-scope="scope">
                            <el-form-item :prop="`tableData3.${scope.$index}.sourceCode`" :rules="tableRules.sourceCode">
                                <el-input v-model='scope.row.sourceCode' placeholder="sourceCode" @input="validate" @change="sourceCodeChange(scope.$index, scope.row.sourceCode)"></el-input>
                            </el-form-item>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="sourceName"
                        label="Source Name">
                        <template slot-scope="scope">
                            <el-form-item :prop="`tableData3.${scope.$index}.sourceName`" :rules="tableRules.sourceName">
                                <el-input :ref="'name'+scope.$index" v-model='scope.row.sourceName' placeholder="sourceName" @input="validate" @change="valueChange(scope.row, 'update')"></el-input>
                            </el-form-item>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="sourceWorkId"
                        label="Source Work No"
                        width="240px">
                        <template slot-scope="scope">
                            <el-form-item :prop="`tableData3.${scope.$index}.sourceWorkId`" :rules="tableRules.sourceWorkId">
                                <el-input v-model='scope.row.sourceWorkId' @change="valueChange(scope.row, 'update')"></el-input>
                            </el-form-item>
                        </template>
                    </el-table-column>
                    <el-table-column
                        label="Amend Time"
                        width="190px">
                        <template slot-scope="scope">
                            <el-form-item>
                                <el-input v-model='scope.row.amendTime' readonly></el-input>
                            </el-form-item>
                        </template>
                    </el-table-column>
                    <el-table-column
                        label="Create User Name"
                        width="200px">
                        <template slot-scope="scope">
                            <el-form-item>
                                <el-input v-model='scope.row.createUserName' readonly=""></el-input>
                            </el-form-item>
                        </template>
                    </el-table-column>
                    <el-table-column
                        label="operation"
                        width="200px">
                        <template slot-scope="scope">
                            <span class="spanright">
                                <i class="el-icon-delete" v-if="isAuth('works:base:change')" @click="deletedata3(scope.$index)"></i>
                            </span>
                        </template>
                    </el-table-column>
                </el-table>
            </el-form>
        </div>
        <el-dialog :visible.sync="sourceTypeShow" title="選擇SourceType" :close-on-click-modal="false">
            <div style="width: 500px;margin: auto;margin-bottom: 20px">
                <el-input placeholder="請輸入type" @keyup.enter.native="typeSearch()" v-model="typeInput" class="input-with-select">
                    <el-button slot="append" icon="el-icon-search" @click="typeSearch()"></el-button>
                </el-input>
            </div>
            <el-table :empty-text="tableresult"   stripe :data="typeData">
                <el-table-column property="sourceType" label="type"></el-table-column>
                <el-table-column property="description" label="描述" ></el-table-column>
                <el-table-column
                    label="operation"
                    width="100">
                    <template slot-scope="scope">
                        <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                            <i class="el-icon-check" v-if="isAuth('works:base:change')" @click="checkedType(scope.$index,scope.row)"></i>
                        </span>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                background
                layout="prev, pager, next"
                :total=typeTotal @current-change="typeSearch">
            </el-pagination>
        </el-dialog>
    </div>
</template>

<script>
    import qs from 'qs'
    export default {
        name: 'refWork',
        props: {
            tableData: {
                type: Array,
                default: () => []
            },
            baseInfo: {
                type: Object,
                default: () => {
                    return {}
                }
            }
        },
        data () {
            var validateNameInput = (rule, value, callback) => {
                if (!value) {
                    return callback(new Error('此項必填！'))
                } else {
                    callback()
                }
            }
            var validateTypeInput = (rule, value, callback) => {
                if (!value) {
                    return callback(new Error('此項必填！'))
                } else {
                    callback()
                }
            }
            var validateIdInput = (rule, value, callback) => {
                if (!value) {
                    return callback(new Error('此項必填！'))
                } else {
                    callback()
                }
            }
            var validateDateInput = (rule, value, callback) => {
                if (!value) {
                    return callback(new Error('此項必填！'))
                } else {
                    callback()
                }
            }
            var validateSocInput = (rule, value, callback) => {
                if (!value) {
                    return callback(new Error('此項必填！'))
                } else {
                    callback()
                }
            }
            return {
                tableForm: {tableData3: [
                    // { sourceType: '', notifySouceId: '', source: '', name: '', notifyDate: '', inputSoc: '161' }
                ]},
                tableRules: {
                    sourceType: [{ validator: validateTypeInput, trigger: 'blur' }],
                    sourceCode: [{ validator: validateIdInput, trigger: 'blur' }],
                    sourceName: [{ validator: validateNameInput, trigger: 'blur' }],
                    sourceWorkId: [{ validator: validateDateInput, trigger: 'blur' }]
                },
                typeIndex: '',
                sourceIndex: '',
                tableresult:' ',
                typeTotal: 10,
                typeInput: '',
                sourceTypeShow: false,
                typeData: [],
                validateFlag: true,
                pageInfo: {
                    page: {
                        pageNum: 0,
                        pageSize: 10
                    }
                },
                tableUpdate: []
            }
        },
        // watch: {
        //     'tableForm': function(val, oldVal){
        //         this.validate();
        //     }
        // },
        methods: {
            sourceTypeChange(index,data){
                // data = data.toUpperCase()
                this.$set(this.tableForm.tableData3[index], 'sourceType', data.toUpperCase());
            },
            must(obj){
                if(obj.columnIndex <= 3)
                return 'must';
            },
            adddata3 () {
                let timeId = new Date().getTime();
                let myArray = [{ sourceType: '', sourceCode: '', sourceName: '', sourceWorkId: '',
                    workId: this.baseInfo.workId,
                    workSocietyCode: this.baseInfo.workSocietyCode,
                    workUniqueKey: this.baseInfo.workUniqueKey,
                    action: 'add',
                    timeId: timeId
                }];
                this.tableForm.tableData3 = [...this.tableForm.tableData3, ...myArray];
                // this.$nextTick( () => {
                //     this.validate();
                // })

            },
            deletedata3 (index) {
                this.$msgbox.confirm(`確定進行[删除]操作?`, '提示', {
                    confirmButtonText: '確定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    // this.tableForm.tableData3.splice(index, 1);
                    this.valueChange(this.tableForm.tableData3[index], 'delete');
                    // this.$nextTick( () => {
                    //     this.validate();
                    // })

                    
                })
            },
            getSourceType (index, item) {
                this.typeIndex = index;
                this.sourceTypeShow = true;
                this.typeInput = item.sourceType;
                this.typeSearch(1);
            },
            typeSearch(val) {
                this.sourceTypeShow = true;
                let data = {
                    page: {
                        pageNum: val ? val :1,
                        pageSize: '10'
                    },
                    sourceType: this.typeInput
                }
                                this.tableresult = '數據加載中...'
                this.$http.post('/ref/getRefWrkSourceType', data).then(res => {
                    if (res.status === 200) {
                        this.typeData = res.data.list;
                        this.tableresult = this.typeData.length == 0 ? '暫無數據' : ' '
                        this.typeTotal = res.data.total;
                    }
                })
            },
            checkedType (index, item) {
                this.sourceTypeShow = false;
                item.sourceType = item.sourceType.toUpperCase();
                this.$set(this.tableForm.tableData3[this.typeIndex], 'sourceType', item.sourceType);
                this.$refs['type'+this.typeIndex].focus();
                this.$refs['type'+this.typeIndex].blur();
                this.valueChange(this.tableForm.tableData3[this.typeIndex] ,'update');
                // this.$nextTick( () => {
                //     this.validate();
                // })
            },
            validate(){
                this.$refs.formTable.validate( ( valid) => {
                    if(valid){
                        this.validateFlag = true;
                    }else{
                        this.validateFlag = false;
                    }
                })
            },
            sourceCodeChange(index, value){
                value = value + '';
                // value = value.replace(/\D/g, '')
                this.tableForm.tableData3[index].sourceCode = value;
                if(value && value.length <= 3){
                    // 查询协会
                    this.$http.get('/ref/society/getSocietyBySocietyCode', {params: {societyCode: value}}).then( res => {
                        if(res.success && res.data.code == 200){
                            if(res.data.data.refSociety){
                                this.tableForm.tableData3[index].sourceName = res.data.data.refSociety.societyName
                                this.$refs['name'+index].focus();
                                this.$refs['name'+index].blur();
                            }else{
                                this.$toast({tips: '未查詢到該協會'});
                                this.tableForm.tableData3[index].sourceName = '';
                                this.$refs['name'+index].focus();
                                this.$refs['name'+index].blur();
                            }
                            this.valueChange(this.tableForm.tableData3[index], 'update');
                        }
                        // this.validate();
                    })
                }else if(value && value.length > 3){
                    // 查询ip
                    let formData = qs.stringify({name_no: value, page_num: 1, page_size: 10});
                    let config = {headers: { 'content-type': 'application/x-www-form-urlencoded' }}
                    this.$http.post('/ip/name/es', formData, config).then(res => {
                        if (res.status === 200) {
                            if(res.data.list.length > 0){
                                this.tableForm.tableData3[index].sourceName = res.data.list[0].chinese_name ? res.data.list[0].chinese_name : res.data.list[0].name;
                                this.$refs['name'+index].focus();
                                this.$refs['name'+index].blur();
                            }else{
                                this.$toast({tips: '未查詢到該會員'});
                                this.tableForm.tableData3[index].sourceName = '';
                                this.$refs['name'+index].focus();
                                this.$refs['name'+index].blur();
                            }
                            this.valueChange(this.tableForm.tableData3[index], 'update');
                        }
                        // this.validate();
                    })
                }

            },
            valueChange(row, type){
                if(row.action){
                    if(row.action == 'add'){
                        if(type == 'delete'){
                            row.action = 'delete';
                        }else if(type == 'update'){
                            row.action = 'add';
                        }
                    }else if(row.action == 'update'){
                        if(type == 'delete'){
                            row.aciton = 'delete';
                        }
                    }
                }else{
                    row.action = type;
                }
                

                let has = false;

                this.tableUpdate.forEach( (item, index) => {
                    if(item.timeId == row.timeId){
                        has = true;
                        this.tableUpdate[index] = this.$utils.copy(row);
                    }
                })
                if(!has){
                // if(!has && type != 'delete'){
                    this.tableUpdate.push(this.$utils.copy(row));
                }
                console.log("this.tableUpdate")
                console.log(this.tableUpdate)
                if(type == 'delete'){
                    let deleteIndex = 9999;
                    this.tableForm.tableData3.forEach( (item, index) => {
                        if(item.timeId == row.timeId){
                            deleteIndex = index;
                        }
                    })
                    this.tableForm.tableData3.splice(deleteIndex, 1);
                }
                this.$nextTick( () => {
                    this.validate();
                })
            },
            getData(){
                this.validate();
                if(this.validateFlag){
                    // return this.tableForm.tableData3;
                    console.log('this.tableUpdate')
                    console.log(this.tableUpdate)
                    console.log(this.tableData)
                    let next = true
                    if(this.tableUpdate.length){
                        this.tableUpdate.forEach((item,index)=>{
                            if(!item.id && item.action == "delete"){
                                this.tableUpdate.splice(index, 1);
                                return
                            }
                            this.tableData.forEach(item1=>{
                                if(item.action == "add"){
                                    if(item.sourceWorkId==item1.sourceWorkId&&item.sourceType==item1.sourceType&&item.sourceCode==item1.sourceCode&&item.sourceName==item1.sourceName){
                                        next = false
                                    }
                                }
                            })
                            this.tableUpdate.forEach((item2,index2)=>{
                                if(item2.action != "delete"&&item.action != "delete"&&index!=index2){
                                    if(item.sourceWorkId==item2.sourceWorkId&&item.sourceType==item2.sourceType&&item.sourceCode==item2.sourceCode&&item.sourceName==item2.sourceName){
                                        next = false
                                    }
                                }
                            })
                        })
                    }
                    if(next){
                        return this.tableUpdate;
                    }else{
                        this.$toast({tips: 'Ref Work數據重復'})
                        return false;
                    }
                }else{
                    this.$toast({tips: 'Ref Work中有必填項未填寫'})
                    return false;
                }
            }
        },
        watch: {
            tableData: function(){
                this.tableForm.tableData3 = this.$utils.copy(this.tableData);
                this.tableForm.tableData3.forEach( (item, index) => {
                    this.tableForm.tableData3[index].timeId = new Date().getTime() + item.id;
                })
                this.$nextTick( () => {
                    this.validate();
                })
            }
        },
        mounted () {
            this.tableForm.tableData3 = this.$utils.copy(this.tableData);
            this.tableForm.tableData3.forEach( (item, index) => {
                this.tableForm.tableData3[index].timeId = new Date().getTime() + item.id;
            })

        }
    }
</script>

<style lang="scss" scoped>
@import "../../assets/scss/works.scss";
    /deep/ .el-table{
        max-height: 320px !important;
    }
    /deep/ .must .cell:after{
        content: ' *';
        color: red
    }
    /deep/ .add-new{
        z-index: 100;
    }
    // /deep/ .el-form-item{
    //     margin-bottom: 0;
    // }
</style>
