<template>
  <!-- 拆分後的文件 用于審核用的 -->
  <div class="ipibpox">
    <el-form :inline="true" :model="search" ref="form" class="demo-form-inline" @keyup.enter.native='searchFn()'>
      <el-form-item prop="Work Title">
        <el-input v-model="search.matchWorkId" placeholder="match work id" style="width: 142px;"></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="search.matchWorkSocietyCode" placeholder="match work soc" style="width: 153px;"></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="search.uniqueKeyMd5" placeholder="uniqueKeyMd5" style="width: 160px;"></el-input>
      </el-form-item>
      <!-- <el-form-item label="quantile" prop="quantileBegin">
                                <el-input style="width: 75px"  v-model="search.quantileBegin" placeholder="Begin"></el-input>
                            </el-form-item>
                            <span style="line-height: 2.15;">~</span>
                            <el-form-item  prop="quantileEnd">
                                <el-input style="width: 75px"  v-model="search.quantileEnd" placeholder="End"></el-input>
            </el-form-item> -->
      <!-- <el-form-item>
                <el-select v-model="search.status" placeholder="所有狀態" style="width: 116px;">
                    <el-option v-for=" (item,index) in config.status" :key="index" :value="item.value"
                               :label="item.label"></el-option>
                </el-select>
            </el-form-item> -->
      <el-form-item>
        <el-button type="primary" @click="searchFn()" v-if="isAuth('list-manage:claim-list:history:historyList:find')">搜索</el-button>
        <el-button :disabled='true' type="success" @click="batchAudit(1)" v-if="isAuth('list-manage:claim-list:history:historyList:add')">新增</el-button>
        <el-button type="success" @click="changeNum()" >批量修改</el-button>
      </el-form-item>
      <el-form-item>
        <span class="clear-search" @click="clearSearch()" v-if="isAuth('list-manage:claim-list:history:historyList:find')">清除搜索</span>
      </el-form-item>
    </el-form>
    <el-dialog :visible.sync="selectWorkShow" title="修改" :close-on-click-modal='removemodal' @close='closedialog'>
      <!-- <div style="width: 600px;margin: auto;margin-bottom: 20px">
            <el-input style="width: 140px" placeholder="Work No" v-model='selectWork.workId'></el-input>
            <el-input style="width: 60px" placeholder="Soc" v-model='selectWork.soc'></el-input>
            <el-input style="width: 240px" placeholder="Title" v-model='selectWork.title'></el-input>
            <el-button slot="append" icon="el-icon-search" @click="querySelectWorkList()"></el-button>

        </div> -->
      <el-form ref="editeform" :model="baseInfo" :inline="true" :rules="updataRules" class="p-t-20" label-width="146px" @keyup.enter.native='uploadFile'>
        <el-form-item label="title">
          <el-input type="text" v-model="baseInfo.titleCh"></el-input>
        </el-form-item>
        <el-form-item label="work artist">
          <el-input type="text" v-model="baseInfo.artists"></el-input>
        </el-form-item>
        <el-form-item label="author">
          <el-input type="text" v-model="baseInfo.authors"></el-input>
        </el-form-item>
        <el-form-item label="composer">
          <el-input type="text" v-model="baseInfo.composers"></el-input>
        </el-form-item>
        <el-form-item label="iswc">
          <el-input type="text" v-model="baseInfo.iswc"></el-input>
        </el-form-item>
        <el-form-item label="isrc">
          <el-input type="text" v-model="baseInfo.isrc"></el-input>
        </el-form-item>
        <el-form-item label="match work id " prop="workId">
          <el-input type="text" v-model="baseInfo.workId"></el-input>
        </el-form-item>
        <el-form-item label="match work soc " prop="workSocietyCode">
          <el-input type="text" v-model="baseInfo.workSocietyCode"></el-input>
        </el-form-item>
        <!-- <el-form-item label="匯款日期" prop="receiptDate">
                <date-picker        
                    v-model="baseInfo.receiptDate"
                    type="date"
                    placeholder="輸入日期"
                    format="yyyyMMdd"
                    value-format="yyyy-MM-dd"
                ></date-picker>
            </el-form-item> -->
        <!-- <el-form-item label="匯率" prop="exchangeRate">
                <el-input type="text" @keydown.native="$inputNumber" v-model="baseInfo.exchangeRate"></el-input>
            </el-form-item> -->
        <!-- <el-form-item label="外幣金額" prop="receiptAmount">
                <el-input type="text" @keydown.native="$inputNumber" v-model="baseInfo.receiptAmount"></el-input>
            </el-form-item> -->
        <div style="text-align: center;">
          <el-form-item>
            <!-- <el-button type="primary" @click="uploadFile" :loading="btnLoading">新增</el-button> -->
            <el-button type="primary" @click="uploadFile">保存</el-button>
          </el-form-item>
        </div>
      </el-form>
    </el-dialog>
    <el-table :empty-text="tableresult"   stripe :data="tableData" border style="width: 100%" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" :selectable="checkSelectable">
      </el-table-column>
      <el-table-column prop="titleCh" label="Title">
      </el-table-column>
      <el-table-column prop="artists" label="WorkArtist">
        <template slot-scope="scope">
          <span :title="scope.row.artists">{{scope.row.artists}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="workId" label="MatchWorkId">
      </el-table-column>
      <el-table-column prop="workSocietyCode" label="MatchWorkSoc">
      </el-table-column>
       <!-- <el-table-column prop="uniqueKeyMd5" label="Data unique key">
          <template slot-scope="scope">
          <el-tooltip class="item" effect="dark" :content="scope.row.uniqueKeyMd5" placement="top">
            <div class="oneLine">{{scope.row.uniqueKeyMd5}}</div>
          </el-tooltip>
        </template>
      </el-table-column> -->
           <el-table-column prop="uniqueKeyMd5" title="" label="Data unique key">
          <template slot-scope="scope">
          <el-tooltip class="item" effect="dark" :content="scope.row.uniqueKeyMd5" placement="top">
            <!-- <div v-html="ToBreak(scope.row.originalData,scope.row.updatedData)" slot="content"></div> -->
            <div class="oneLine">{{scope.row.uniqueKeyMd5}}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="authors" label="Author">
        <template slot-scope="scope">
          <span :title="scope.row.authors">{{scope.row.authors}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="composers" label="Composer">
        <template slot-scope="scope">
          <span :title="scope.row.composers">{{scope.row.composers}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="iswc" label="iswc">
      </el-table-column>
      <el-table-column prop="isrc" label="isrc">
      </el-table-column>
      <!-- <el-table-column
                label="Duration"
                >
                <template slot-scope="scope">
                    <span>{{scope.row.durationM?scope.row.durationM:'00'}}:{{scope.row.durationSStr?scope.row.durationSStr:'00'}}</span>
                </template>
            </el-table-column> -->

      <el-table-column prop="matchScore" label="MatchScore">
      </el-table-column>
      <el-table-column fixed="right" label="Operation" width="120px">
        <template slot-scope="scope">
          <div style="width: 100%;display:flex;    justify-content: space-around;text-align: center;cursor: pointer">
            <span class="a-blue" @click="audit(scope.row)" v-if="isAuth('list-manage:general-list:history:historyList:change')">修改</span>
            <span class="a-blue" @click="removeitem(scope.row)" v-if="isAuth('list-manage:general-list:history:historyList:del')">刪除</span>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <div>

      <el-pagination background layout="prev, pager, next" :total="total" @current-change="searchFn" :current-page="currentPage">
      </el-pagination>
    </div>
    <el-dialog :visible.sync="selectWorkShow2" title="批量修改"   :close-on-click-modal='removemodal'>
      <el-form ref="changeNumForm" :model="changeNumForm" class="p-t-20" label-width="150px">
          <el-form-item label="match work id：" >
            <el-input type="text" v-model="changeNumForm.matchWorkId" style="width:450px"></el-input>
          </el-form-item>
          <el-form-item label="matchworksoc：">
            <el-input type="text" v-model="changeNumForm.matchWorkSoc" style="width:450px"></el-input>
          </el-form-item>
          <el-form-item label="scope:">
            <el-select v-model="changeNumForm.scope" placeholder="请选择scope">
              <el-option label="current" value="current"></el-option>
              <el-option label="history" value="history"></el-option>
            </el-select>
          </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer" style="text-align: center;">
        <el-button @click="selectWorkShow2 = false">取 消</el-button>
        <el-button type="primary" @click="selectWorkShow2 = false">保存</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
import qs from 'qs'
// import axios from '../../utils/httpRequest';    
export default {
  name: 'generalHistory',
  data() {
    return {
      selectWorkShow: false,
      selectWorkShow2:false,
      removemodal: false,
      changeNumForm:{},
      selectWork: {

      },
      updataRules: {
        //   這個是修改的提示  看看有沒有問題+++++++++
        resourceId: [{ required: true, message: "", trigger: "blur" }],
        matchWorkId: [{ required: true, message: "請輸入", }],
        matchWorkSocietyCode: [{ required: true, message: "請輸入", trigger: "blur" }],
        categoryCode: [{ required: true, message: "請輸入", trigger: "blur" }],
      },
      tableData: [],tableresult:' ',
      total: 0,
      currentPage: 1,
      // rules:{
      //     fileBaseId: [
      //         { required: true, message: '請輸入FID', trigger: 'blur' }
      //     ],
      // },
      baseInfo: {
        amendTime: '',
        authors: '',
        company: '',
        composers: '',
        createTime: '',
        uniqueKeyMd5: '',
        id: '',
        isrc: '',
        iswc: '',
        album: '',
        artists: '',
        matchScore: '',
        matchTitleId: '',
        matchWorkType: '',
        publisher: '',
        titleCh: '',
        titleEn: ''
      },
      rules: {
        receiptDate: [{ required: true, message: '请输入匯款日期', trigger: 'blur' }],
        receiptAmount: [{ required: true, message: '请输入外幣金額', trigger: 'blur' }],
        chargeAmount: [{ required: true, message: '请输入手續費', trigger: 'blur' }],
        sourceSoc: [{ required: true, message: '请选择', trigger: 'change' }],
        sourceSocName: [{ required: true, message: '请选择', trigger: 'change' }],
        sourceCountryCode: [{ required: true, message: '请选择', trigger: 'change' }],
        sourceCountryName: [{ required: true, message: '请选择', trigger: 'change' }],
        currencyCode: [{ required: true, message: '请选择', trigger: 'change' }],
        exchangeRate: [{ required: true, message: '请输入匯率', trigger: 'blur' }],

      },
      search: {
        workId: '',
        workSocietyCode: '',
        uniqueKeyMd5: ''
      },
      config: {
        status: [
          {
            value: '',
            label: '全部狀態'
          },
          {
            value: 0,
            label: '待審核'
          },
          {
            value: 1,
            label: '已匹配'
          },
          {
            value: 2,
            label: '不匹配'
          },

        ],
        uploadType: ['PG', 'FW', 'MS', 'CJ']
      },
      selectList: []
    }
  },
  mounted() {
    this.searchFn()
  },
  methods: {
    changeNum(){
      if(this.selectList.length>0){
        this.selectWorkShow2 = true
      }else{
        this.$message.error(`请选择数据`)
      }
    },
    uploadFile() {
      console.log("新增点击事件")
      // if(this.file){
      //     let formData = new FormData()
      //     formData.append('files', this.file)
      //     this.$http
      //         .post('/listOverseasFileBase/uploadFile', formData)
      //         .then(res => {
      //             if (res.success) {
      //                 let type = 'warning'
      //                 let msg = res.data
      //                 if (res.data.indexOf('overseas-') !== -1) {
      //                     this.baseInfo.attachmentPath = res.data.substring(9)
      //                     this.save()
      //                 }else{
      //                     this.$message({
      //                         message: msg,
      //                         type: type
      //                     })
      //                 }

      //             }
      //         })
      // }else{
      //     this.save()
      // }
      // claimmatch/updateListMatchDataDspListMatchHistory
      //  for (let key in this.summaryData[0]) {
      //     this.baseInfo[key] = this.summaryData[0][key]
      // }

      // let ajaxData = {}
      // ajaxData.lord = this.baseInfo
      // ajaxData.lorsList = this.statementData.slice(0,this.statementData.length - 1).concat(this.fieData.slice(0,this.fieData.length - 1))
      this.$http.post(
        '/listmatch/updateListMatchDataBasicListMatchHistory',
        this.baseInfo
      )
        .then(res => {
          //   console.log('*****&&&&&&&&',res)
          if (res.data.code == 200) {
            this.$message({
              message: '保存成功',
              type: 'success'
            });
            //   this.baseInfo.distOrderNumber = res.data.data.distOrderNumber
            //    this.searchFn(this.currentPage)
            this.selectWorkShow = false
            this.searchFn(this.currentPage)
          } else {
            this.$toast({ tips: res.data.message })
          }
        })
    },
    closedialog(item) { 
      //    console.warn('item----------',item)
      // this.searchFn(this.currentPage)
    },
    removeitem(baseInfo) {
      console.warn('***baseinforow', baseInfo)
      if (baseInfo.id) {
        this.$alert('確定要刪除嗎？', '刪除', {
          confirmButtonText: '确定',
          showCancelButton: true,
          cancelButtonText: '取消',
          callback: action => {
            if (action == 'confirm') {
              this.$http.delete('/listmatch/deleteListMatchDataBasicListMatchHistory/' + baseInfo.id).then(res => {
                // console.warn('*****====',res)
                if (res.success) {
                  this.searchFn(this.currentPage)
                  this.$toast({ tips: '刪除成功' })
                } else {
                  this.$toast({ tips: res.data.message })
                }
              })
            }
          }
        });
      } else {
        // this.rawMatchInfoData.splice(index,1)
      }
      //  this.$http.delete('/claimmatch/updateListMatchDataDspListMatchHistory/'+baseInfo.id).then(res => {
      //           console.log('*****&deletedelte',res)
      //           if (res.data.code == 200) {
      //               this.$message({
      //                   message: '保存成功',
      //                   type: 'success'
      //               });
      //             //   this.baseInfo.distOrderNumber = res.data.data.distOrderNumber
      //            this.selectWorkShow=false
      //           }else{
      //               this.$toast({tips:res.data.message})
      //           }
      //       })
    },
    clearSearch() {
      this.search = {
        matchWorkId: '',
        matchWorkSocietyCode: '',
        uniqueKeyMd5: '',
        resourceId: '',
      }
    },
    searchFn(page = 1) {
      // claimmatch/getListMatchDataDspListMatchHistory   進入頁面獲取列表
      this.$refs.form.validate(validate => {
        if (validate) {
          if (page == 1) {
            this.total = 0
          }
          let ajaxData = {};
          ajaxData.listMatchDataBasicMatchHistory = this.$utils.copy(this.search);
          ajaxData.page = {
            pageNum: page ? page : 1,
            pageSize: 10
          }
                this.tableresult = '數據加載中...'
          this.$http.post('/listmatch/getListMatchDataBasicMatchHistory', ajaxData).then(res => {
            if (res.success) {
              this.tableData = res.data.data.list;
              this.total = res.data.data.total;
              this.currentPage = page ? page : 1;
            }
                this.tableresult = this.tableData.length == 0 ? '暫無數據' : ' '
          })
        }
      })

    },
    audit(item) {
      // this.$router.push({name: 'claimListAudit', query: {id: item.id, type: item.matchType}})
      // JSON.parse(JSON.stringify(item))
      this.baseInfo = JSON.parse(JSON.stringify(item))
      console.log("item-----", this.baseInfo, item)
      this.selectWorkShow = true
      //  this.$refs.editeform.validate(validate => {
      //    console.warn('------------',validate)
      //  })
        //  还是表单校验的问题  要仔细查看原因!!
    },
    changeSearchScore(score) {
      this.search.matchScoreLevel = score;
    },
    checkSelectable(row) {
      return !row.status
    },
    handleSelectionChange(list) {
      let array = [];
      list.forEach((item, index) => {
        array.push(item.uniqueKeyMd5)
      })
      this.selectList = array;
    },
    batchAudit(type) {
      this.selectWorkShow = true
      // if (!this.selectList.length) {
      //     this.$toast({tips: '請至少選擇一項'})
      //     return;
      // }
      // this.$http.post('/claimmatch/batchUpdateListMatchDataDspStatusByParams', {
      //     uniqueKeyMd5List: this.selectList,
      //     status: type
      // }).then(res => {
      //     if (res.success) {
      //         if (res.data.code == 200) {
      //             this.$toast({tips: '批量審核' + (type == 1 ? '通過' : '拒絕') + '成功'});
      //             this.searchFn();
      //         } else {
      //             this.$toast({tips: res.data.message})
      //         }

      //     }
      // })

    },
    uploadChange(file) {
      this.$utils.uploadFile({flie:file.raw}, '/claimmatch/importAuditResults', this)
    },
    autoprod() {
      this.$msgbox.confirm(`確定生成custom_id操作?`, '提示', {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$refs.form.validate(validate => {
          if (validate) {
            let ajaxData = {};
            ajaxData.listMatchDataDsp = this.$utils.copy(this.search);
            if (ajaxData.listMatchDataDsp.matchScore == 99) {
              delete ajaxData.listMatchDataDsp.matchScore;
            }

            this.$http.post('/claimmatch/autoGenerateCustomId', ajaxData, { responseType: 'blob' }).then(res => {
              console.log(res)
              let data = res.data
              this.$utils.downloadByBlob(data)
            })
          }
        })
      })
    },
    exportFn() {
      this.$msgbox.confirm(`確定進行[導出]操作?`, '提示', {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$refs.form.validate(validate => {
          if (validate) {
            let ajaxData = {};
            ajaxData = this.$utils.copy(this.search);
            if (ajaxData.matchScore == 99) {
              delete ajaxData.matchScore;
            }

            this.$http.post('/claimmatch/export', ajaxData, { responseType: 'blob' }).then(res => {
              console.log(res)
              let data = res.data
              this.$utils.downloadByBlob(data)
            })
          }
        })
      })
    }
  }
}
</script>

<style  scoped>
.el-form-item {
  margin-left: 0;
  margin-right: 0;
}
.el-button {
  margin-left: 0;
  margin-right: 0;
}
.oneLine {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
