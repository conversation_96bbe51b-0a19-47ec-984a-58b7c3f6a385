<template>
    <div>
        <div class="boxline">
            <div class="add-new">
                <el-button type="primary" @click="adddata7()">新 增</el-button>
            </div>
            <el-form ref="formTable" :model="tableForm" style="padding-top:50px">
                <el-table
                    :data="tableForm.tableData7"
                    border
                    stripe
                    style="width: 100%"
                    :header-cell-class-name="must"
                    >
                    <el-table-column
                        prop="albumNo"
                        label="Album No">
                        <template slot-scope="scope">
                            <el-form-item>
                                <el-input v-model=scope.row.albumNo placeholder="" @change="valueChange(scope.row, 'UPDATE')"></el-input>
                            </el-form-item>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="albumName"
                        label="Album Name"
                        >
                        <template slot-scope="scope">
                            <el-form-item :prop="`tableData7.${scope.$index}.albumName`" :rules="tableRules.albumName">
                                <el-input v-model=scope.row.albumName @change="valueChange(scope.row, 'UPDATE')"></el-input>
                            </el-form-item>
                        </template>
                    </el-table-column>
                    <el-table-column
                        label="Album Pub Date"
                        >
                        <template slot-scope="scope">
                            <el-form-item>
                                <date-picker v-model="scope.row.albumPubDate" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd" @change="valueChange(scope.row, 'UPDATE')"></date-picker>
                            </el-form-item>
                        </template>
                    </el-table-column>
                    <el-table-column
                        label="Create Time"
                        >
                        <template slot-scope="scope">
                            <el-form-item>
                                <el-input v-model=scope.row.createTime readonly></el-input>
                            </el-form-item>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="createBy"
                        label="Create User"
                        >
                        <template slot-scope="scope">
                            <el-form-item>
                                <el-input v-model=scope.row.createBy readonly></el-input>
                            </el-form-item>
                        </template>
                    </el-table-column>
                    <el-table-column
                        label="operation"
                        width="200px">
                        <template slot-scope="scope">
                            <span class="spanright">
                                <i class="el-icon-delete" @click="deletedata3(scope.$index)"></i>
                            </span>
                        </template>
                    </el-table-column>
                </el-table>
            </el-form>
        </div>

    </div>
</template>

<script>
    import qs from 'qs'
    export default {
        name: 'refWork',
        props: {
            tableData: {
                type: Array,
                default: () => []
            },
            baseInfo: {
                type: Object,
                default: () => {
                    return {}
                }
            }
        },
        data () {
            var validateTypeInput = (rule, value, callback) => {
                if (!value) {
                    return callback(new Error('此項必填！'))
                } else {
                    callback()
                }
            }
            return {
                tableForm: {tableData7: [
                    // { sourceType: '', notifySouceId: '', source: '', name: '', notifyDate: '', inputSoc: '161' }
                ]},
                tableRules: {
                    albumName: [{ validator: validateTypeInput, trigger: 'blur' }],
                },
                validateFlag: true,
                tableUpdate: []
            }
        },
        // watch: {
        //     'tableForm': function(val, oldVal){
        //         this.validate();
        //     }
        // },
        methods: {
            must(obj){
                if(obj.columnIndex == 1)
                return 'must';
            },
            adddata7 () {
                let timeId = new Date().getTime();
                let myArray = [{ albumNo: '', albumName: '', albumPubDate: '', createTime: '',createBy:'',
                    workId: this.baseInfo.workId,
                    workSocietyCode: this.baseInfo.workSocietyCode,
                    workUniqueKey: this.baseInfo.workUniqueKey,
                    action: 'ADD',
                    timeId: timeId
                }];
                this.tableForm.tableData7 = [...this.tableForm.tableData7, ...myArray];
                // this.$nextTick( () => {
                //     this.validate();
                // })
            },
            deletedata3 (index) {
                this.$msgbox.confirm(`確定進行[删除]操作?`, '提示', {
                    confirmButtonText: '確定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    // this.tableForm.tableData7.splice(index, 1);
                    this.valueChange(this.tableForm.tableData7[index], 'DEL');
                    // this.$nextTick( () => {
                    //     this.validate();
                    // })

                    
                })
            },
            validate(){
                this.$refs.formTable.validate( ( valid) => {
                    if(valid){
                        this.validateFlag = true;
                    }else{
                        this.validateFlag = false;
                    }
                })
            },
            valueChange(row, type){
                if(row.action){
                    if(row.action == 'ADD'){
                        if(type == 'DEL'){
                            row.action = 'DEL';
                        }else if(type == 'UPDATE'){
                            row.action = 'ADD';
                        }
                    }else if(row.action == 'UPDATE'){
                        if(type == 'DEL'){
                            row.aciton = 'DEL';
                        }
                    }
                }else{
                    row.action = type;
                }
                let has = false;
                this.tableUpdate.forEach( (item, index) => {
                    if(item.timeId == row.timeId){
                        has = true;
                        this.tableUpdate[index] = this.$utils.copy(row);
                    }
                })
                if(!has){
                // if(!has && type != 'delete'){
                    this.tableUpdate.push(this.$utils.copy(row));
                }
                console.log("this.tableUpdate1")
                console.log(this.tableUpdate)
                if(type == 'DEL'){
                    let deleteIndex = 9999;
                    this.tableForm.tableData7.forEach( (item, index) => {
                        if(item.timeId == row.timeId){
                            deleteIndex = index;
                        }
                    })
                    this.tableForm.tableData7.splice(deleteIndex, 1);
                }
                this.$nextTick( () => {
                    this.validate();
                })
            },
            getData(){
                this.validate();
                if(this.validateFlag){
                    // return this.tableForm.tableData3;
                    console.log('this.tableUpdate2')
                    console.log(this.tableUpdate)
                    console.log(this.tableData)
                    let next = true
                    if(this.tableUpdate.length){
                        this.tableUpdate.forEach((item,index)=>{
                            if(!item.id && item.action == "DEL"){
                                this.tableUpdate.splice(index, 1);
                                return
                            }
                            this.tableData.forEach(item1=>{
                                if(item.action == "ADD"){
                                    if(item.albumNo==item1.albumNo&&item.albumName==item1.albumName&&item.albumPubDate==item1.albumPubDate){
                                        next = false
                                    }
                                }
                            })
                            this.tableUpdate.forEach((item2,index2)=>{
                                if(item2.action != "DEL"&&item.action != "DEL"&&index!=index2){
                                    if(item.albumNo==item2.albumNo&&item.albumName==item2.albumName&&item.albumPubDate==item2.albumPubDate){
                                        next = false
                                    }
                                }
                            })
                        })
                    }
                    if(next){
                        return this.tableUpdate;
                    }else{
                        this.$toast({tips: 'Album數據重復'})
                        return false;
                    }
                }else{
                    this.$toast({tips: 'Album中有必填項未填寫'})
                    return false;
                }
            }
        },
        watch: {
            tableData: function(){
                this.tableForm.tableData7 = this.$utils.copy(this.tableData);
                console.log('11111111')
                console.log(this.tableData)
                this.tableForm.tableData7.forEach( (item, index) => {
                    this.tableForm.tableData7[index].timeId = new Date().getTime() + item.id;
                })
                this.$nextTick( () => {
                    this.validate();
                })
            }
        },
        mounted () {
            this.tableForm.tableData7 = this.$utils.copy(this.tableData);
            this.tableForm.tableData7.forEach( (item, index) => {
                this.tableForm.tableData7[index].timeId = new Date().getTime() + item.id;
            })
        }
    }
</script>

<style lang="scss" scoped>
@import "../../assets/scss/works.scss";
    /deep/ .el-table{
        max-height: 320px !important;
    }
    /deep/ .must .cell:after{
        content: ' *';
        color: red
    }
    /deep/ .add-new{
        z-index: 100;
    }
    // /deep/ .el-form-item{
    //     margin-bottom: 0;
    // }
</style>
