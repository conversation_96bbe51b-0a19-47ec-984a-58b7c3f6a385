<template>
  <!-- 拆分後的文件 用于審核用的 -->
  <div class="ipibpox">
    <el-form :inline="true" :model="search" ref="form"  class="demo-form-inline"  @keyup.enter.native='searchFn()'>
      <el-form-item prop="fileBaseId">
        <span class="red">*</span>
        <el-input v-model.trim="search.fileBaseId" placeholder="FID" style="width: 80px;"></el-input>
      </el-form-item>

      <el-form-item prop="Work Title">
        <el-input v-model="search.title" placeholder="title" @blur="search.title=$event.target.value.trim()" style="width: 120px;"></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model.trim="search.dataUniqueKey" placeholder="Data unique key" style="width: 120px;"></el-input>
      </el-form-item>
      <el-form-item prop="titleNonNull" label="非空Title">
        <el-checkbox v-model="search.titleNonNull"></el-checkbox>
      </el-form-item>
      <el-form-item label="quantile" prop="quantileBegin">
        <el-input style="width: 75px" v-model.trim="search.quantileBegin" placeholder="Begin"></el-input>
      </el-form-item>
      <span style="line-height: 2.15;">~</span>
      <el-form-item prop="quantileEnd">
        <el-input style="width: 75px" v-model.trim="search.quantileEnd" placeholder="End"></el-input>
      </el-form-item>
      <!-- <el-form-item>
        <el-button :type="(search.matchScoreLevel < 0 && search.matchScoreLevel !== '')  ? 'primary' : ''" @click="changeSearchScore(-1)">10分以下
        </el-button>
        <el-button :type="(search.matchScoreLevel == 0 && search.matchScoreLevel !== '') ? 'primary' : ''" @click="changeSearchScore(0)">10-20分
        </el-button>
        <el-button :type="(search.matchScoreLevel > 0 && search.matchScoreLevel !== '') ? 'primary' : ''" @click="changeSearchScore(1)">20分以上
        </el-button>
      </el-form-item> -->
      <el-form-item>
        <el-select v-model="search.matchScoreLevel" placeholder="分數選擇" style="width: 116px;">
          <el-option v-for=" (item,index) in config.score" :key="index" :value="item.value" :label="item.label"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="search.status" placeholder="所有狀態" style="width: 116px;">
          <el-option v-for=" (item,index) in config.status" :key="index" :value="item.value" :label="item.label"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="批次號:" prop="batchIdA">
        <el-input style="width:90px;" v-model="search.batchIdA" placeholder="Begin"></el-input>
      </el-form-item>
      <span style="line-height: 2.15;">~</span>
      <el-form-item prop="batchIdB">
        <el-input style="width:90px;" v-model="search.batchIdB" placeholder="End"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="searchFn()" v-if="isAuth('list-manage:claim-list:audit:auditList:find')">搜索</el-button>
        <el-button type="success" @click="batchAudit(1)" v-if="isAuth('list-manage:claim-list:audit:auditList:pass')">批量通過</el-button>
        <el-button type="danger" @click="batchAudit(2)" v-if="isAuth('list-manage:claim-list:audit:auditList:refuse')">批量拒絕</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="exportInFn" style="background-color: #b240ff;border-color: #b240ff;" v-if="isAuth('list-manage:claim-list:audit:auditList:exportin')">審核結果導入</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="exportFn" v-if="isAuth('list-manage:claim-list:audit:auditList:exportout')">審核結果導出</el-button>
      </el-form-item>

      <el-form-item>
        <span class="clear-search" @click="clearSearch()" v-if="isAuth('list-manage:claim-list:audit:auditList:find')">清除搜索</span>
      </el-form-item>
    </el-form>
    <el-table :empty-text='emptyText' stripe :data="tableData" border style="width: 100%" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" :selectable="checkSelectable">
      </el-table-column>
      <el-table-column prop="fileBaseId" label="FID" width="60px">
      </el-table-column>
      <el-table-column prop="title" label="Title">
      </el-table-column>

      <el-table-column prop="workArtist" label="Aritists">
        <template slot-scope="scope">
          <span :title="scope.row.workArtist">{{scope.row.workArtist}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="compWriters" label="Author">
        <template slot-scope="scope">
          <span :title="scope.row.compWriters">{{scope.row.compWriters | addSpace}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="composer" label="Composer">
        <template slot-scope="scope">
          <span :title="scope.row.composer">{{scope.row.composer | addSpace}}</span>
        </template>
      </el-table-column>
      <el-table-column label="Duration">
        <template slot-scope="scope">
          <span>{{scope.row.durationM?scope.row.durationM:'00'}}:{{scope.row.durationSStr?scope.row.durationSStr:'00'}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="matchTitle" label="Match Work Titile">
      </el-table-column>
      <el-table-column label="Soc-WorkId">
        <template slot-scope="scope">
          {{(scope.row.matchWorkSocietyCode ? scope.row.matchWorkSocietyCode : '') + (scope.row.matchWorkId ?
                    '-'+scope.row.matchWorkId : '')}}
        </template>
      </el-table-column>
      <el-table-column prop="clickNumber" label="ClickNum">
      </el-table-column>
      <el-table-column prop="compCustomId" label="CompCustomId">
      </el-table-column>
      <el-table-column prop="quantile" label="Quantile">
      </el-table-column>
      <el-table-column prop="matchType" label="Type">
        <template slot-scope="scope">
          <!-- <span :title="scope.row.status">{{scope.row.author | addSpace}}</span> -->
          <span v-if="scope.row.matchType==0">視頻</span>
          <span v-if="scope.row.matchType==1">文字</span>
        </template>
      </el-table-column>
      <el-table-column prop="matchScore" label="matchScore">
      </el-table-column>
      <el-table-column prop="dataUniqueKey" label="Data unique key">
        <template slot-scope="scope">
          <el-tooltip class="item" effect="dark" :content="scope.row.dataUniqueKey" placement="top">
            <div class="oneLine">{{scope.row.dataUniqueKey}}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="status">
        <template slot-scope="scope">
          <!-- <span :title="scope.row.status">{{scope.row.author | addSpace}}</span> -->
          <span v-if="scope.row.status==0">待審核</span>
          <span v-if="scope.row.status==1">已匹配</span>
          <span v-if="scope.row.status==2">不匹配</span>
          <span v-if="scope.row.status==3">過濾</span>
          <span v-if="scope.row.status==4">CompCustomId過濾</span>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="OP" width="60px">
        <template slot-scope="scope">
          <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
            <span class="a-blue" @click="audit(scope.row,scope.$index)" v-if="isAuth('list-manage:claim-list:audit:auditList:audit')">審核</span>
          </span>
        </template>
      </el-table-column>
    </el-table>
    <div>

      <el-pagination background layout="prev, pager, next" :total="total" @current-change="searchFn" :current-page="currentPage">
      </el-pagination>
    </div>

    <el-dialog
      :visible.sync="dialogVisible"
    >
      <el-form label-position="right" label-width="140px" ref="form2" >
        <el-form-item label="claimSetInfoId">
          <el-input v-model="claimSetInfoId"  placeholder="雙擊選擇" readonly @dblclick.native="getMinimaList('',1)"></el-input>
        </el-form-item>
        <el-form-item label="year">
          <el-input oninput="value=value.replace(/[^\d]/g,'')" maxlength='4' v-model="year" ></el-input>
        </el-form-item>
        <el-form-item>
          <el-upload class="upload-demo" ref="upload" action="" accept=".xls,.xlsx" :auto-upload="false" :on-change="uploadChange" :show-file-list="false">
            <el-button type="primary" >上传</el-button>
          </el-upload>
        </el-form-item>
        
      </el-form>
      
    </el-dialog>

    <el-dialog title="claimSetInfoId列表" :visible.sync="showMinima" width="80%" @close="minimaTotal=0" :close-on-click-modal="false">
        <div style="margin-bottom: 20px">
            <el-form :inline="true" :model="minimaData" ref="form" class="demo-form-inline"  @keyup.enter.native='getMinimaList("",1)'>
                <el-form-item prop="compnyName">
                    <el-input v-model="minimaData.company" placeholder="companyName"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="getMinimaList('',1)">搜索</el-button>
                </el-form-item>
                <el-form-item>
                    <span class="clear-search" @click="clearSearch()">清除搜索</span>
                </el-form-item>
            </el-form>
        </div>
        <el-table :empty-text="tableresult"   stripe :data="minimaTableData">
            <el-table-column property="id" label="id" width="50"></el-table-column>
            <el-table-column property="company" label="company" width="200"></el-table-column>
            <el-table-column property="mechanicalStartTime" label="mechanicalStartTime">
                
                
            </el-table-column>
            <el-table-column property="mechanicalEndTime" label="mechanicalEndTime">
                
                
            </el-table-column>
            <el-table-column property="publicTransmissionEndTime" label="publicTransmissionEndTime">
                
                
            </el-table-column>
            <el-table-column property="publicTransmissionStartTime" label="publicTransmissionStartTime">
                
                
            </el-table-column>
            <el-table-column
                label="operation"
                width="100">
                <template slot-scope="scope">
                <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                  <i class="el-icon-check" @click="chooseMinima(scope.row)"></i>
                </span>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next" :current-page="minimaData.page_num"
            :total='minimaTotal' @current-change="handleClick">
        </el-pagination>
    </el-dialog>
  </div>
  
</template>

<script>
import qs from 'qs'
// import axios from '../../utils/httpRequest';    
export default {
  name: 'sampleDateList',
  data() {
    return {
      emptyText:'暫無數據',
      tableData: [],
      searchparam: {},
      total: 0,
      currentPage: 1,
      rules: {
        fileBaseId: [
          { required: true, message: '請輸入FID', trigger: 'blur' }
        ],
        batchIdA: [{ pattern: /^\+?[1-9]\d*$/, message: '必須>0' }],
        batchIdB: [{ pattern: /^\+?[1-9]\d*$/, message: '必須>0' }]
      },
      search: {
        status: '',
        title: '',
        matchScoreLevel: '',
        batchIdA: '',
        batchIdB: '',
        fileBaseId: '',
        quantileBegin: '',
        quantileEnd: '',
        titleNonNull: false
      },
      config: {
        status: [
          {
            value: '',
            label: '全部狀態'
          },
          {
            value: 0,
            label: '待審核'
          },
          {
            value: 1,
            label: '已匹配'
          },
          {
            value: 2,
            label: '不匹配'
          },
          {
            value: 3,
            label: '過濾'
          },
          {
            value: 4,
            label: 'CompCustomId過濾'
          },

        ],
        score: [{
          value: '',
          label: '全部評分'

        },
        {
          value: -1,
          label: '10分以下'
        },
        {
          value: 0,
          label: '10-20分'
        },
        {
          value: 1,
          label: '20分以上'
        },
        ],
        uploadType: ['PG', 'FW', 'MS', 'CJ']
      },
      selectList: [],
      dialogVisible:false,
      claimSetInfoId:'',
      year:'',
      showMinima: false,
      minimaTotal: 0,
      minimaData: {
          company: '',
          page_size:10,
          page_num:1
      },
      tableresult:' ',
      minimaTableData: [],
    }
  },
  methods: {
    getMinimaList (e,page) {
      !this.showMinima && (this.showMinima = true)
      this.minimaData.page_num = page
      this.minimaData.status='1'
      // let params=this.minimaData
            this.tableresult = '數據加載中...'
      this.$http.get('/claim', {params:this.minimaData}).then(res => {
          if (res.success) {
              this.minimaTableData = res.data.list
              this.minimaTotal = res.data.total
          }
                this.tableresult = this.minimaTableData.length == 0 ? '暫無數據' : ' '
      })
    },
    handleClick (val) {
        this.getMinimaList('',val)
    },
    chooseMinima (row) {
        this.showMinima = false
        this.minimaData = {
            compnyName: '',
            productFullName: '',
            productShortName: '',
            page_size:10,
            page_num:1
        }
        if(this.isFile == 'Y'){
            this.claimSetInfoId = row.id
        }else{
            this.claimSetInfoId = row.id
        }

    },
    clearSearch() {
      this.search = {
        status: '',
        title: '',
        matchScoreLevel: '',
        fileBaseId: '',
        batchIdA: '',
        batchIdB: '',
        quantileBegin: '',
        quantileEnd: '',
        titleNonNull: false
      }
    },
    // mounted(){
    //    this.$refs.form.clearValidate()
    // },
    searchFn(page = 1) {
      this.$refs.form.validate(validate => {
        if (validate) {
          this.emptyText='數據加載中'
          if (page == 1) {
            this.total = 0
          }
          let ajaxData = {};
          ajaxData = this.$utils.copy(this.search);
          ajaxData.page = {
            pageNum: page ? page : 1,
            pageSize: 10
          }
          this.searchparam = ajaxData
          this.$http.post('/claimmatch/getListMatchDataDspList', ajaxData).then(res => {
            // console.warn('res11111',res)
            console.log(res)
            if (res.success) {
              if (res.data.code == 200) {
                  this.tableData = res.data.data.list;
                  this.total = parseInt(res.data.data.total)
                  this.currentPage = page ? page : 1;
              }else{
                  this.tableData=[]
                  this.total=1
                  this.currentPage = 1;
                  this.$toast({ tips: res.data.message });
              }
              if(! this.tableData || this.tableData.length == 0){
                  this.emptyText = '暫無數據';
              }
            }

          })
        }
      })

    },
    searchFnfirst(page = 1) {
      // this.$refs.form.validate(validate => {
      // if (validate) {
      if (page == 1) {
        this.total = 0
      }
      let ajaxData = {};
      ajaxData = this.$utils.copy(this.search);
      ajaxData.page = {
        pageNum: page ? page : 1,
        pageSize: 10
      }
      this.searchparam = ajaxData
      this.$http.post('/claimmatch/getListMatchDataDspList', ajaxData).then(res => {
        console.warn('res11111', res)
        if (res.success) {
          // console.warn('res2222222',res)
          this.tableData = res.data.data ? res.data.data.list : [];
          this.total = res.data.data ? res.data.data.total : 0;
          this.currentPage = page ? page : 1;
        }

      })
      // }
      // })

    },
    audit(item, params) {
      this.$router.push({ name: 'claimListAudit', query: { id: item.id, type: item.matchType, useparams: params, usetabledata: this.tableData, thissearchparam: this.searchparam } })
    },
    changeSearchScore(score) {
      this.search.matchScoreLevel = score;
    },
    checkSelectable(row) {
      return !row.status
    },
    handleSelectionChange(list) {
      let array = [];
      list.forEach((item, index) => {
        array.push(item.dataUniqueKey)
      })
      this.selectList = array;
    },
    batchAudit(type) {
      if (!this.selectList.length) {
        this.$toast({ tips: '請至少選擇一項' })
        return;
      }
      this.$http.post('/claimmatch/batchUpdateListMatchDataDspStatusByParams', {
        uniqueKeyMd5List: this.selectList,
        status: type
      }).then(res => {
        if (res.success) {
          if (res.data.code == 200) {
            this.$toast({ tips: '批量審核' + (type == 1 ? '通過' : '拒絕') + '成功' });
            this.searchFn();
          } else {
            this.$toast({ tips: res.data.message })
          }

        }
      })

    },
    uploadChange(file) {
      console.log(file)
      // this.$http.post('/claimmatch/importAuditResults', {claimSetId:this.claimSetInfoId, year:this.year, flie:file.raw}).then(res=>{
      //   console.log(res);
      // })
      this.$utils.uploadFile({claimSetId:this.claimSetInfoId, year:this.year, flie:file.raw}, '/claimmatch/importAuditResults', this)
      this.dialogVisible =false
    },
    autoprod() {
      this.$msgbox.confirm(`確定生成custom_id操作?`, '提示', {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$refs.form.validate(validate => {
          if (validate) {
            let ajaxData = {};
            ajaxData.listMatchDataDsp = this.$utils.copy(this.search);
            if (ajaxData.listMatchDataDsp.matchScore == 99) {
              delete ajaxData.listMatchDataDsp.matchScore;
            }

            this.$http.post('/claimmatch/autoGenerateCustomId', ajaxData, { responseType: 'blob' }).then(res => {
              console.log(res)
              let data = res.data
              this.$utils.downloadByBlob(data)
            })
          }
        })
      })
    },
    exportFnid() {

      let ajaxData = {};
      ajaxData = this.$utils.copy(this.search);
      ajaxData.page = {
        pageNum: this.currentPage,
        pageSize: 10
      }
      this.$http.post('/claimmatch/autoGenerateCustomId', ajaxData).then(res => {
        console.log('resresrsa', res)
        // if (res.success) {
        //   this.tableData = res.data.data.list;
        //   this.total = res.data.data.total;
        //   this.currentPage = page ? page : 1;
        // }
      })

    },
    exportInFn(){
      this.dialogVisible = true
    },
    exportFn() {
      this.$msgbox.confirm(`確定進行[導出]操作?`, '提示', {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$refs.form.validate(validate => {
          if (validate) {

            let ajaxData = {};
            ajaxData = this.$utils.copy(this.search);
            if (ajaxData.matchScore == 99) {
              delete ajaxData.matchScore;
            }
            // 判断导出的数量-------------------
            this.$http.post('/claimmatch/exportCount', ajaxData).then(res => {
              console.log('rescount', res.data.data);
              let exporturl = res.data.data < 10000 ? '/claimmatch/export' : 'claimmatch/exportAsync'
              if (exporturl == '/claimmatch/export') {
                this.$http.post(exporturl, ajaxData, { responseType: 'blob' }).then(res => {
                  console.log('exportres', res)

                  let data = res.data
                  this.$utils.downloadByBlob(data, res.headers["content-disposition"])
                })
              } else {
                this.$http.post(exporturl, ajaxData).then(res => {
                  console.log('exportres222', res)
                  if (res.success && res.data.code == '200') {
                    this.$alert(res.data.data, '提示');
                  } else {
                    this.$toast({ tips: res.data.message })
                  }
                  // let data = res.data
                  // this.$utils.downloadByBlob(data, res.headers["content-disposition"])
                })




              }
            })
          }
        })
      })
    }
  },
  // beforeRouteEnter(to, from, next) {
  //   next(vm => {
  //     // console.log('*****out',vm,to,from,next)
  //     // vm.formData.distNo = to.params.distNo
  //     vm.searchFnfirst(vm.currentPage)
  //   })
  // }
}
</script>

<style  scoped>
.el-form-item {
  margin-left: 0;
  margin-right: 0;
}
.el-button {
  margin-left: 0;
  margin-right: 0;
}
.oneLine {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
<style scoped lang="scss">
    /deep/ .el-form-item__label {
        margin-left: 0 !important;
    }

</style>
