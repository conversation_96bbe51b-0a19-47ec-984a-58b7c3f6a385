<template>
    <!-- Name列表 -->
    <div>
        <el-form :inline="true" :model="searchForm" class="demo-form-inline" @keyup.enter.native="queryList(1)">
            <el-form-item label="Pa Name No:">
                <el-input v-model="searchForm.paNameNo" placeholder="雙擊查詢" @dblclick.native="getPaName2()" @change="changePa2" style="width: 150px;"></el-input>
            </el-form-item>
            <el-form-item label="Name:">
                <el-input v-model="searchForm.name" placeholder></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="queryList(1)" v-if="isAuth('payment:paymentlist:find')">搜 索</el-button>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="add" v-if="isAuth('payment:paymentlist:add')">新 增</el-button>
            </el-form-item>
            <el-form-item>
                <span class="clear-search" @click="clearSearch()" v-if="isAuth('payment:paymentlist:find')">清除搜索</span>
            </el-form-item>
        </el-form>
        <el-table :empty-text="tableresult" :data="tableData" border style="width: 100%">
            <el-table-column prop="paNameNo" label="Pa Name No"></el-table-column>
            <el-table-column prop="name" label="Name"></el-table-column>
            <el-table-column prop="splitName" label="Transfer Name"></el-table-column>
            <el-table-column prop="remark" label="备注" ></el-table-column>
            <el-table-column label="operation" width="250">
                <template slot-scope="scope">
                    <span class="a-blue" @click="edit(scope.row)" v-if="isAuth('payment:nameTransferList:change')">編輯</span>
                    <span class="a-blue" @click="del(scope.row)" v-if="isAuth('payment:nameTransferList:del') ">删除</span>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination background layout="prev, pager, next" :total="total" @current-change="queryList" :current-page="currentPage"></el-pagination>
        <el-dialog :title="dialogTitle" :visible.sync="paylistTableVisible" width="600px" :close-on-click-modal="false" :before-close='cancelEdit'>
            <el-form
                :inline="true"
                ref="editForm"
                :model="editForm"
                label-width="150px"
                label-position="right"
                class="demo-form-inline"
                :rules="rules"
            >
                <div>
                    <el-form-item label="Pa Name No" prop="paNameNo">
                        <el-input v-model="editForm.paNameNo" v-if="!isEdit" placeholder="雙擊查詢" @dblclick.native="getPaName()" @change="changePa" readonly></el-input>
                        <el-input v-model="editForm.paNameNo" v-if="isEdit" readonly></el-input>
                    </el-form-item>
                    <el-form-item label="name" prop="name">
                        <el-input v-model="editForm.name" placeholder="name" readonly></el-input>
                    </el-form-item>
                    <el-form-item label="Transfer Name" prop="splitName">
                        <el-input v-model="editForm.splitName" placeholder="Transfer Name"></el-input>
                    </el-form-item>
                    <el-form-item label="備註" prop="remark">
                        <el-input v-model="editForm.remark" placeholder="remark"></el-input>
                    </el-form-item>
                </div>
            </el-form>
            <span slot="footer" class="dialog-footer">
        <el-button @click="cancelEdit">取 消</el-button>
        <el-button type="primary" @click="editConfirm">確 定</el-button>
      </span>
        </el-dialog>
        <pa-name ref="paName" :paNameNo='paNameNo' @checkPaName='checkPaName' @flag='flag=true'></pa-name>
        <pa-name ref="paName2" :paNameNo='paNameNo2' @checkPaName='checkPaName2' @flag='flag2=true'></pa-name>
    </div>
</template>

<script>
    import paName from '../export/components/paName';
    export default {
        components: { paName },
        name: 'paymentlist',
        data: () => {
            return {
                searchForm: {
                    paNameNo:'',
                    name: ''
                },
                tableData: [],tableresult:' ',
                editForm: {
                    paNameNo: '',
                    name: '',
                    splitName: '',
                    remark: ''
                },
                isEdit: false,
                paylistTableVisible: false,
                distNoDetails: [],
                total: 1,
                currentPage:1,
                dialogTitle:'',
                loading: null,
                dialogVisible: false,
                tableData1: [],
                flag:true,
                flag2:true,
                paNameNo: '',
                paNameNo2: '',
                rules:{
                    paNameNo:[
                        { required: true, message: '請選擇', trigger: 'change' },
                    ],
                    splitName:[
                        { required: true, message: '请输入', trigger: 'blur' },
                    ]
                }
            }
        },
        methods: {
            
            checkPaName(data){
                this.flag=true
                this.editForm.paNameNo =data.ip_name_no
                this.editForm.name =data.name
            },
            checkPaName2(data){
                this.flag2=true
                this.$set(this.searchForm,'paNameNo',data.ip_name_no)
            },
            clearSearch () {
                this.searchForm = {
                    paNameNo: '',
                    name: ''
                }
                this.queryList()
            },
            queryList (num) {
                let ajaxData = {
                    page_num: num ? num : 1,
                    page_size: 10
                }
                ajaxData = Object.assign(ajaxData, this.searchForm)
                const loading = this.$loading()
                this.tableresult = '數據加載中...'
                this.$http
                  .get('/name/split/transfer/getListWithPage', {params: ajaxData})
                  .then(res => {
                      if (res.success) {
                          this.tableData = res.data.data.list || []
                          this.total = res.data.data.total
                          this.currentPage = num
                          // todo-guan  分页
                          loading.close()
                      }
                          this.tableresult = this.tableData.length == 0 ? '暫無數據' : ' '
                  })
            },
            edit (item) {
                this.isEdit = true
                this.dialogTitle='编辑Name Transfer'
                // this.$router.push({name: 'paymentEdit'})
                this.paylistTableVisible = true
                this.editForm = this.$utils.copy(item)

                // this.editForm.paNameNo = item.paNameNo
                // this.editForm.name = item.name
                // this.editForm.splitName = item.splitName
                // this.editForm.remark = item.remark
            },
            add () {
                this.dialogTitle='新增Name Transfer'
                this.paylistTableVisible = true
                this.isEdit = false

                this.$refs.editForm.clearValidate()
            },
            editConfirm () {
                this.$refs.editForm.validate(validate => {
                    if(validate){
                        let params = this.$utils.copy(this.editForm)
                        const loading = this.$loading()
                        this.$http
                          .post('/name/split/transfer/saveDistNameSplitTransfer', params)
                          .then(res => {
                              loading.close()
                              if (res.success) {
                                  if (res.status === 200) {
                                    let message = res.data ? res.data : '操作成功'
                                      this.$toast({tips: message})
                                      this.paylistTableVisible = false
                                      this.queryList()
                                  } else {
                                      this.$toast({tips: res.data.message})
                                  }

                                  this.$refs.editForm.resetFields()
                              }
                          })
                    }
                })

            },
            cancelEdit () {
                this.paylistTableVisible = false
                 this.editForm = {
                    paNameNo: '',
                    name: '',
                    splitName: '',
                    remark: ''
                }
            },
            del(row){
                console.log(row)
                this.$msgbox.confirm(`確定進行[删除]操作?`, '提示', {
                    confirmButtonText: '確定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    const loading = this.$loading()
                    this.$http.delete('/name/split/transfer/deleteDistNameSplitTransfer/' + row.id).then(res => {
                        loading.close()
                        if (res.success) {
                            this.$toast({ tips: '删除成功' })
                            this.queryList(this.currentPage)
                        }
                        
                    })
                }).catch(() => {
                
                })
            },
            getPaName(){
                if(this.flag){
                    this.paNameNo = ''
                    this.flag=false
                    this.$refs.paName.paNameDataD()
                }
            },
            getPaName2(){
                if(this.flag2){
                    this.paNameNo2 = ''
                    this.flag2=false
                    this.$refs.paName2.paNameDataD()
                }
            },
            changePa(data){
                if(data){
                    if(this.flag){
                    this.paNameNo = data
                    this.flag=false
                    this.$nextTick(()=>{
                        this.$refs.paName.paNameDataC()
                    })
                    }
                }else{
                    this.editForm.searchForm = ''
                    this.editForm.name = ''
                }
            },
            changePa2(data){
                if(data){
                    if(this.flag2){
                    this.paNameNo2 = data
                    this.flag2=false
                    this.$nextTick(()=>{
                        this.$refs.paName2.paNameDataC()
                    })
                    }
                }else{
                    this.$set(this.searchForm,'paNameNo','')
                }
            }
        },
        mounted () {
            this.queryList()
        }
    }
</script>

<style scoped>
    .cell-detail {
        display: block;
        cursor: pointer;
    }
</style>
