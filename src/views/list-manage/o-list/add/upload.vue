<template>
    <div class="upload">
        <top-bar :noShow="false" @save="fileUploadSubmit"></top-bar>
        <el-tabs v-model="isFile"  type="border-card" >
            <el-tab-pane label="文件導入" name="Y" v-if= "showFile">
                <el-form :model="formType" label-position="left">
                    <div style="padding-left: 16px;">
                        <el-form-item>
                            <el-upload
                                ref="fileUpload"
                                class="upload-demo"
                                drag
                                action=""
                                multiple
                                name="files"
                                :on-change="getFile"
                                :fileList="fileList"
                                :auto-upload="false"
                            >
                                <i class="el-icon-upload"></i>
                                <div class="el-upload__text">
                                    將文件拖到此處，或
                                    <em>點擊選擇</em>
                                </div>
                                <div class="el-upload__tip3" slot="tip">水單ID：{{receiptId}}</div>
                                <div class="el-upload__tip" slot="tip">支持擴展名：.E4, .F2, .CRD, .FIE</div>
                            </el-upload>
                        </el-form-item>
                    </div>
                </el-form>
            </el-tab-pane>
            <el-tab-pane label="目錄導入" name="N">
                <el-form :inline="true" ref="form1" :model="formType1" :rules="rule1" class="demo-form-inline" label-position="left">
                    <div>
                        <el-form-item label="輸入目錄" prop="filePath">
                            <el-input v-model="formType1.filePath" placeholder="請輸入目錄地址"></el-input>
                        </el-form-item>
                    </div>
                    <div class="el-upload__tip2">⽂件⽬錄下(\\**************\...)放置海外協會⽂件，⽂件名按照以下命名，程式會⾃動對應⽔單，如果未能獲取到對應⽔單，⽂件列表會要求⼈⼯指定⽔單</div>
                    <div class="el-upload__tip2">上傳⽂件命名規則:【海外協會編號】-【⽔單ID】-MUST-國外分配代號【⾃定義區分序號】.【⽂件後綴】</div>
                </el-form>
            </el-tab-pane>
        </el-tabs>
        <el-dialog title="水單列表" :visible.sync="showOList" width="50%" :close="closeOList">
            <div class="search">
                <el-form :model="searchForm" :inline="true"   @keyup.enter.native='getList()'>
                    <el-form-item label="分配代號">
                        <el-input v-model="searchForm.distNo"></el-input>
                    </el-form-item>
                    <el-form-item label="協會名稱">
                        <el-input v-model.trim="searchForm.sourceSocName"></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="getList()">搜索</el-button>
                    </el-form-item>
                </el-form>
            </div>
            <el-table :empty-text="tableresult"   :data="tableData" v-loading="loading" border height="500" style="width: 100%">
                <el-table-column prop="distNo" label="分配代號"></el-table-column>
                <el-table-column prop="id" label="水單ID"></el-table-column>
                <el-table-column prop="sourceSocName" label="協會名稱"></el-table-column>
                <el-table-column label="operation">
                    <template slot-scope="scope">
                        <i class="el-icon-check pointer" @click="checkList(scope.row.id)"></i>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                background
                layout="prev, pager, next"
                :total="total"
                @current-change="handleChange"
            ></el-pagination>
        </el-dialog>
    </div>
</template>

<script>
    export default {
        data() {
            return {
                isFile:'N',
                formType: {},
                formType1:{},
                rule1:{
                    filePath:[
                        { required: true, message: '請輸入', trigger: 'blur' },
                    ],
                },
                uploadData: {},
                fileList:[],
                showOList:false,
                total:0,
                tableData:[],
                tableresult:' ',
                loading:true,
                searchForm:{
                    distNo:'',
                    sourceSocName:'',
                    page:{
                        pageSize:10,
                        pageNum:1
                    }
                },
                receiptId:'',
                showFile: false
            };
        },
        watch: {
            '$route'(to,from){
                if(to.name === 'oListStep2'){
                    if(to.query.id){
                        this.showFile = true
                        this.isFile = 'Y'
                        this.receiptId = to.query.id
                    } else {
                        this.showFile = false
                        this.receiptId = ''
                        this.isFile = 'N'
                    }
                }
            }
        },
        mounted(){
            if(this.$route.query.id){
                this.receiptId = this.$route.query.id
                this.showFile = true
                this.isFile = 'Y'
                console.log(this.receiptId)
            } else {
                this.showFile = false
                this.isFile = 'N'
            }
            document.addEventListener('drop',function (e) {
                e.preventDefault()
            },false)
            document.addEventListener('dragover',function (e) {
                e.preventDefault()
            },false)
        },
        methods: {
            getFile(file,fileList){
                console.log('getFile')
                // 校驗文件格式
                let type = file.name.split('.')[1].toLowerCase()
                console.log(type)
                let patten = /^[0-9]*$/
                if(type == 'e4' || type == 'f2' || patten.test(type)||type=='crd'||type=='fie'){
                    this.fileList = []
                    fileList.map(item => {
                        if(item.raw){
                            this.fileList.push(item.raw)
                        }else{
                            this.fileList.push(item)
                        }
                    })
                    console.log(fileList)
                    console.log(this.fileList)
                }else{
                    this.$toast({tips:'請上傳E4,F2,CRD,FIE格式的文件'})
                    this.$refs.fileUpload.clearFiles()
                }

            },
            fileUploadSubmit(){
                if(this.isFile == 'Y'){
                    this.upload()
                }else{
                    this.uploadDirectory()
                }
            },
            upload(){
                let formData = new FormData()
                formData.append('receiptId',this.receiptId)
                this.fileList.map(file => {
                    formData.append('files',file)
                })
                this.$http.post('/listOverseasFileBase/uploadFileForDetailsNew',formData).then(res => {
                    if(res.success){
                        this.$refs.fileUpload.clearFiles();
                        if(res.data.code == 200){
                            this.$message({
                                type:'success',
                                message:'上傳成功',
                                duration:1500,
                                onClose:() => {
                                    this.$bus.$emit('closeCurrentTab', () => {
                                        if(this.receiptId){
                                            this.$router.push({name: 'oListStep3',query:{id:this.receiptId,sourceSoc:this.$route.query.soc,title:this.receiptId,nameId:this.receiptId}});
                                        }else{
                                            this.$router.push({name: 'billList'});
                                        }
                                    })
                                }
                            })
                        }else{
                            this.$message.error(res.data)
                        }
                    }
                })
            },
            uploadDirectory(){
                let formData = new FormData()
                // formData.append('receiptId',this.receiptId)
                formData.append('filePath',this.formType1.filePath)
                this.$http.post('/listOverseasFileBase/uploadFilePathForDetailsNew',formData).then(res => {
                    if(res.success){
                        if(res.data.code == 200){
                            this.$message({
                                type:'success',
                                message:'上傳成功',
                                duration:1500,
                                onClose:() => {
                                    this.$bus.$emit('closeCurrentTab', () => {
                                        this.$router.push({name: 'billList'});
                                    })
                                }
                            })
                        }else{
                            this.$message.error(res.data)
                        }
                    }
                })
            },
            handleChange(page){
                this.getList(page)
            },
            showListDialog(){
                this.showOList = true
                this.getList(1)
            },
            closeOList(){
                this.searchForm = {
                    distNo:'',
                    sourceSocName:'',
                    page:{
                        pageSize:1,
                        pageNum:1
                    }
                }
                this.showOList = false
            },
            getList(page = 1){
                this.searchForm.page.pageNum = page
                this.loading = true;
                                  this.tableresult = '數據加載中...'
                this.$http.post('/listOverseasReceipt/getListOverseasReceiptList', this.searchForm).then(res => {
                    console.log(res)
                    if (res.success) {
                        this.tableData = res.data.list
                        this.total = res.data.total
                    }
                        this.tableresult = this.tableData.length == 0 ? '暫無數據' : ' '
                    this.loading = false;
                })
            },
            checkList(id){

            }
        }
    };
</script>
<style lang="scss" scoped>
    .upload{
        padding-top: 40px;
    }
    .el-upload__tip{
        font-size: 14px;
        height: 20px;
        line-height: 20px;
        text-align: left;
        margin-top: 0;
    }
    .el-upload__tip2{
        font-size: 14px;
        height: 20px;
        line-height: 20px;
        text-align: left;
        color: red;
        margin-top: 0;
    }
    .el-upload__tip3{
        font-size: 20px;
        height: 20px;
        line-height: 20px;
        text-align: left;
        color: red;
        margin-top: 0;
    }
    /deep/ .el-upload-list__item-name{
        text-align: left;
    }
    /deep/ .el-tabs__content{
        padding-top: 40px;
    }
    /deep/ .el-form-item__label{
        margin-left: 0;
    }
</style>
