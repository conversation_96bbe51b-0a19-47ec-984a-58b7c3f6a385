<template>
    <div>
        <el-dialog title="導出會員信息" :visible.sync="show" :close-on-click-modal="false">
            <!-- <el-row class="select-type">
                <el-col :span="3" style="width: 110px;" class="t-r">導出類型：</el-col>
                <el-col :span="8">
                    <el-select v-model="exportType">
                        <el-option value="mcl" label="MEMBER CONTACT LIST"></el-option>
                    </el-select>
                </el-col>
            </el-row> -->
            <el-form class="form" :inline="true" label-width="110px" style="margin-bottom: 0;">
                <el-form-item label="導出類型">
                    <el-select v-model="exportType" style="width: 340px;">
                        <el-option value="mcl" label="MEMBER CONTACT LIST"></el-option>
                        <!-- <el-option value="lmml" label="LOCAL MEMBER MAILING LABEL"></el-option> -->
                    </el-select>
                </el-form-item>
            </el-form>
            <el-form class="form" :inline="true" label-width="110px" style="margin-top: 0;">
                <div>
                    <el-form-item label="Membership">
                        <el-select v-model="mclData.membership">
                            <el-option value="FW" label="Full Writer"></el-option>
                            <el-option value="FP" label="Full Publisher"></el-option>
                            <el-option value="AW" label="Associate Writer"></el-option>
                            <el-option value="AP" label="Associate Publisher"></el-option>
                            <el-option value="RE" label="Reject"></el-option>
                            <el-option value="WA" label="Waiting"></el-option>
                            <el-option value="AWM" label="All Writer Member"></el-option>
                            <el-option value="APM" label="All Publisher Member"></el-option>
                            <el-option value="" label="All Member"></el-option>
                        </el-select>
                    </el-form-item>
                </div>
                <el-form-item label="Joint From">
                    <date-picker v-model="mclData.jointFrom" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd" style="width: 207px !important;"></date-picker>
                </el-form-item>
                <el-form-item label="To">
                    <date-picker v-model="mclData.jointTo" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd" style="width: 207px !important;"></date-picker>
                </el-form-item>
                <el-form-item label="Phone">
                    <el-input type="text" v-model="mclData.phone"></el-input>
                </el-form-item>
                <el-form-item label="Fax">
                    <el-input type="text" v-model="mclData.fax"></el-input>
                </el-form-item>
                <el-form-item label="Email">
                    <el-input type="text" v-model="mclData.email"></el-input>
                </el-form-item>
                <el-form-item label="Address">
                    <el-radio v-model="mclData.type" label="">Corresponding</el-radio>
                    <el-radio v-model="mclData.type" label="DO">Domicile</el-radio>
                </el-form-item>
            </el-form>
            <el-row>
                <el-col :offset="8" :span="8" class="t-c">
                    <el-button type="primary" @click="exportFn">導 出</el-button>
                    <span class="clear-search" @click="clearSearch()">清除搜索</span>
                </el-col>
            </el-row>
        </el-dialog>
    </div>
</template>
<script>
export default {
    data(){
        return {
            show: false,
            exportType: 'mcl',
            mclData: {
                membership: '',
                jointFrom: '',
                jointTo: '',
                phone: '',
                fax: '',
                email: '',
                type: ''
            }
        }
    },
    methods: {
        init(){
            this.show = true;
        },
        exportFn(){
            let ajaxData = this.mclData;
            // ajaxData.ipBaseNo = 'I0001946138'
            this.$utils.download('/member/report', ajaxData);
            // this.$http.post('/member/report', ajaxData).then( res => {

            // })
        },
        clearSearch(){
            this.mclData = {
                    membership: '',
                    jointFrom: '',
                    jointTo: '',
                    phone: '',
                    fax: '',
                    email: '',
                    type: ''
                }
            },
    }
}
</script>
<style scoped lang="scss">
/deep/ .el-dialog{
    min-width: 750px;
}
.form{
    margin: 20px 0;
}
    .select-type{
        line-height: 32px;
        .el-select{
            width: 100%;
        }
    }
    
</style>