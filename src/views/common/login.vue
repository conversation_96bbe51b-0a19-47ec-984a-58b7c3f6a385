
<template>
    <div class="login-wrap">
        <div class="ms-title">MoneySystem</div>
        <!-- <div class="ms-title">詞曲版權分配管理系統</div> -->
        <div class="ms-login">
            <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="0px" class="demo-ruleForm">
                <el-form-item prop="username">
                    <el-input v-model="dataForm.userName" placeholder="username"></el-input>
                </el-form-item>
                <el-form-item prop="password">
                    <el-input type="password" placeholder="password" v-model="dataForm.password"></el-input>
                </el-form-item>
                <div class="login-btn">
                    <el-button type="primary" @click="dataFormSubmit()">登錄</el-button>
                </div>
                <!-- <p style="font-size:12px;line-height:30px;color:#999;">Tips : 用户名和密碼随便填。</p> -->
            </el-form>
        </div>
    </div>
</template>

<script>
    // import axios from '../../utils/httpRequest'
    // import { getUUID } from '@/utils'
    export default {
        data () {
            return {
                dataForm: {
                    userName: '',
                    password: '',
                    uuid: '',
                    captcha: ''
                },
                dataRule: {
                    userName: [
                        { required: true, message: '賬號不能為空', trigger: 'blur' }
                    ],
                    password: [
                        { required: true, message: '密碼不能為空', trigger: 'blur' }
                    ]
                    // captcha: [
                    //     { required: true, message: '验证碼不能為空', trigger: 'blur' }
                    // ]
                },
                captchaPath: ''
            }
        },
        created () {
            // this.getCaptcha()
            window.addEventListener('keydown',this.keyDown);
        },
        methods: {
            keyDown(e){
                if(e.keyCode == 13){
                  this.dataFormSubmit();
                }
            },
            // 提交表單
            dataFormSubmit () {
                this.$refs['dataForm'].validate((valid) => {
                    // let data = {
                    //     'username': this.dataForm.userName,
                    //     'password': this.dataForm.password
                    // }
                    if (valid) {
                        this.$http.post('/login' + '?password=' + this.dataForm.password + '&username=' + this.dataForm.userName).then(res => {
                            if (res.status === 200) {
                                if(res.data && res.data.code){
                                    this.$toast({tips: '用户名或密码错误'});
                                }else{
                                    this.$router.replace({ name: 'home' });
                                }
                            }
                        })
                    }
                })
            },
            // 獲取验证碼
            getCaptcha () {
                // this.dataForm.uuid = getUUID()
                // this.captchaPath = this.$http.adornUrl(`/captcha.jpg?uuid=${this.dataForm.uuid}`)
            }
        },
        destroyed(){
            window.removeEventListener('keydown',this.keyDown,false);
        }
    }
</script>
<style>

</style>

<style lang="scss" scoped>
    .login-wrap{
        position: relative;
        width:100%;
        height:100%;
        background: #324157
    }
    .ms-title{
        position: absolute;
        top:50%;
        width:100%;
        margin-top: -230px;
        text-align: center;
        font-size:30px;
        color: #fff;

    }
    .ms-login{
        position: absolute;
        left: 50%;
        top: 50%;
        width: 380px;
        height: 240px;
        margin: -150px 0 0 -190px;
        padding: 40px;
        border-radius: 5px;
        background: #fff;
    }
    .login-btn{
        text-align: center;
    }
    .login-btn button{
        width:100%;
        height:36px;
    }
</style>
