.ll-toast {
    position: fixed;
    top: 50%;
    left: 50%;
    max-width: 500px;
    min-width: 100px;
    line-height: 20px;
    padding: 10px 14px;
    transform: translate(-50%,-50%);
    text-align: center;
    font-size: 14px;
    color: #fff;
    border-radius: 5px;
    background: rgba(0, 0, 0, 0.7);
    animation: show-toast .5s;
    overflow: hidden;
    text-overflow: ellipsis;
    /* white-space: nowrap; */
    z-index:999999;
}
.faile-toast {




    position: fixed;
    top: 50%;
    left: 50%;
    max-width: 500px;
    min-width: 100px;
    line-height: 20px;
    padding: 10px 14px;
    transform: translate(-50%,-50%);
    text-align: center;
    font-size: 14px;font-weight: 900;
    color: #ff0000;
    border-radius: 5px;
    background: rgba(0, 0, 0, 0.7);
    animation: show-toast .5s;
    overflow: hidden;
    text-overflow: ellipsis;
    
    
    
    
    /* position: fixed;
    top: 50%;
    left: 50%;
    max-width: 500px;
    min-width: 100px;
    line-height: 20px;
    padding: 10px 14px;
    transform: translate(-50%,-50%);
    text-align: center;
    font-size: 14px;
    border: 1px solid red;
    color: rgb(248, 6, 6);
    border-radius: 5px;
    background: #fff;
    animation: show-toast .5s;
    overflow: hidden;
    text-overflow: ellipsis; */
    /* white-space: nowrap; */
    z-index:999998;
}
.ll-toast i{
    display: inline-block;
    font-size: 30px;
    color: #fff;
    margin-bottom: 10px;
}
.ll-toast p{
    margin: 0;
}
.ll-toast-mark {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index:66;
}
.faile-toast i{
    display: inline-block;
    font-size: 30px;
    color: #fff;
    margin-bottom: 10px;
}
.faile-toast p{
    margin: 0;
}
.faile-toast-mark {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index:66;
}
@keyframes show-toast {
    from {
        opacity: 0;
        /*transform: translate(-50%, -10px);
        -webkit-transform: translate(-50%, -10px);*/
    }
    to {
        opacity: 1;
        /*transform: translate(-50%, 0);
        -webkit-transform: translate(-50%, 0);*/
    }
}



