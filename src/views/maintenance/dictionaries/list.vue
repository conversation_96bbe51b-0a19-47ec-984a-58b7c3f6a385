<template>
    <div>
        <el-form :inline="true" class="demo-form-inline" @keyup.enter.native="searchFn(1)">
            <el-form-item>
                <el-input
                  type="text"
                  v-model="searchData.dictName"
                  placeholder="字典名稱"
                  style="width: 160px"
                ></el-input>
            </el-form-item>
            <el-form-item prop="">
                <el-input
                  v-model="searchData.dictCode"
                  placeholder="字典編號"
                  style="width: 160px"
                ></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="searchFn(1)">查詢</el-button>
            </el-form-item>
            <el-form-item>
                <span class="clear-search" @click="clearSearch()">清除搜索</span>
            </el-form-item>
        </el-form>
        <div style="padding-bottom:20px;">
            <el-button type="primary" @click="addDict()">添加</el-button>
            <el-button type="primary" @click="refresh()">刷新缓存</el-button>
        </div>
        <el-table
            :data="tableData"
            border
            stripe
            style="width: 100%"
            v-loading="loading"
            :empty-text="emptyText"
        >
            <el-table-column prop="id" label="#" width="80px"> </el-table-column>
            <el-table-column prop="dictName" label="字典名稱"></el-table-column>
            <el-table-column prop="dictCode" label="字典編號"></el-table-column>
            <el-table-column prop="description" label="描述"></el-table-column>
            <el-table-column
                label="operation"
                width="200"
            >
                <template slot-scope="scope">
                    <div class="option">
                        <el-button @click="handleEdit(scope.row)" type="text" size="small">編輯</el-button>
                        <el-button @click="handleConfig(scope.row)" type="text" size="small">字典配置</el-button>
                        <el-button @click="handleDelete(scope.row, scope.$index)" type="text" size="small">刪除</el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
          background
          layout="prev, pager, next"
          :total="total"
          @current-change="searchFn"
          :current-page="currentPage"
        >
        </el-pagination>
        <el-dialog :title="isEdit?'編輯':'新增'" :visible="addshow" width="550px" :show-close="false" :close-on-click-modal="false">
            <el-form label-position="right" label-width="100px" ref="editForm" :model="dictEdit" class="demo-form-inline" :rules="rules" @keyup.enter.native="addFormItem">
                <el-form-item label="字典名稱" prop="dictName">
                    <el-input style="width:300px" v-model="dictEdit.dictName" placeholder="字典名稱"></el-input>
                </el-form-item>
                <el-form-item label="字典編號" prop="dictCode">
                    <el-input style="width:300px" v-model="dictEdit.dictCode" placeholder="字典編號"></el-input>
                </el-form-item>
                <el-form-item label="描述">
                    <el-input style="width:300px" v-model="dictEdit.description" placeholder="描述"></el-input>
                </el-form-item>
            </el-form>
            <div style="text-align: right;margin-top: 50px;">
                <el-button @click="cancelDialog">关闭</el-button>
                <el-button type="primary" @click="addFormItem">確定</el-button>
            </div>
        </el-dialog>
        <dict-detail ref="dictItem"></dict-detail>
    </div>
</template>
<script>
import dictDetail from './components/dictDetail'
export default {
    name:'dictionaries',
    data(){
        return{
            searchData:{
                dictName:'',
                dictCode:''
            },
            tableData:[],
            currentPage:1,
            total:1,
            loading:false,
            addshow:false,
            isEdit:false,
            rules:{
                dictName:[{ required: true, message: '請輸入字典名稱', trigger: 'blur' }],
                dictCode:[{ required: true, message: '請輸入字典編號', trigger: 'blur' }],
            },
            dictEdit:{},
            emptyText: '數據加載中',
        }
    },
    components:{
        dictDetail
    },
    created() {
        this.searchFn();
    },
    methods:{
        searchFn(num){
            this.loading=true
            let params = this.searchData
            params.pageNum = num?num:1
            params.pageSize = 10
            this.emptyText = '數據加載中';
            this.$http
            .get("/sysDict/getSysDictList", { params })
            .then((res) => {
                this.loading=false
                if (res.success) {
                    console.log(res.data.data);
                    this.tableData = res.data.data.list;
                    if(! this.tableData || this.tableData.length == 0){
                        this.emptyText = '暫無數據';
                    }
                    this.total=res.data.data.total
                    this.currentPage = num ? num : 1;
                    if(!this.tableData.length && num!=1){
                        this.searchFn(num-1)
                    }
                }
            });
        },
        addDict(){
            this.dictEdit={}
            this.isEdit = false
            this.addshow=true
        },
        refresh(){
            this.loading=true
            this.$http
            .delete(`/sysDict/deleteDictCache`)
            .then((res) => {
                this.loading=false
                if (res.success) {
                    this.searchFn(this.currentPage)
                }
            });
        },
        handleEdit(data){
            console.log(data)
            this.dictEdit = this.$utils.copy(data);
            this.isEdit = true
            this.addshow=true
        },
        handleConfig(data){
            this.$refs.dictItem.itemShow(data.id)
        },
        handleDelete(data){
            this.$msgbox.confirm(`確定進行[刪除]操作?`, '提示', {
                confirmButtonText: '確定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.deleteFn(data.id)
            }).catch( () => {
            })
        },
        deleteFn(id){
            this.loading=true
            this.$http
            .delete(`/sysDict/deleteDict/${id}`)
            .then((res) => {
                this.loading=false
                if (res.success) {
                    this.searchFn(this.currentPage)
                }
            });
        },
        clearSearch(){
            this.searchData={
                dictName:'',
                dictCode:''
            }
            this.searchFn(1)
        },
        addFormItem(){
            this.$refs.editForm.validate(validate => {
                if(validate){
                    this.$http.post('/sysDict/saveOrUpdateDict',this.dictEdit).then(res => {
                        if(res.success){
                            if(res.data.code === 200){
                                this.cancelDialog()
                                this.searchFn(1)
                                // this.searchFn(this.currentPage)
                            }else{
                                this.$toast({tips: res.data.message})
                            }
                        }
                    })
                }
            })
        },
        cancelDialog(){
            this.$refs.editForm.resetFields()
            this.addshow=false
        },
    }
}
</script>
<style scoped>

</style>