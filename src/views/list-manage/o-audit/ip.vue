<template>
  <div>
    <div class="main">
      <div class="box1 box-search">
        <el-form :inline="true" :model="search" ref="form" class="demo-form-inline"
                 @keyup.enter.native='searchFn(1)'>
          <el-form-item label="">
            <el-input v-model="search.batch" placeholder="batch" style="width: 80px;"></el-input>
          </el-form-item>
          <el-form-item label="">
            <el-input v-model="search.matchWorkId" placeholder="Match Work No" style="width: 155px;"></el-input>
          </el-form-item>
          <el-form-item label="">
            <el-input v-model="search.matchWorkSoc" placeholder="Match Work Soc" style="width: 160px;"></el-input>
          </el-form-item>
          <el-form-item label="">
            <el-input v-model="search.matchWorkTitle" placeholder="Match Work Title" style="width: 160px;"></el-input>
          </el-form-item>
          <el-form-item label="">
            <el-input v-model="search.distributedIpName" placeholder="Match Distributed IP Name" style="width: 240px;"></el-input>
          </el-form-item>
          <el-form-item label="">
            <el-input v-model="search.distributedIp" placeholder="Match Distributed IP Name No" style="width: 270px;"></el-input>
          </el-form-item>
          <el-form-item label="">
            <el-input v-model="search.matchIpSoc" placeholder="Match IP Soc" style="width: 135px;"></el-input>
          </el-form-item>
          <el-form-item label="">
            <el-input v-model="search.distNo" placeholder="distNo" style="width: 90px;"></el-input>
          </el-form-item>
          <el-form-item label="">
            <el-input v-model="search.fId" placeholder="FID" style="width: 75px;"></el-input>
          </el-form-item>
          <el-form-item label="">
            <el-input v-model="search.remitSoc" placeholder="RemitSoc" @keydown.native="$inputNumber" style="width: 165px;"></el-input>
          </el-form-item>
          <el-form-item label="">
            <el-input v-model="search.remitWorkTitle" placeholder="Remit Work Title" style="width: 165px;"></el-input>
          </el-form-item>
          <el-form-item label="">
            <el-input v-model="search.remitIpName" placeholder="RemitIpName" style="width: 165px;"></el-input>
          </el-form-item>
          <el-form-item label="">
            <el-input v-model="search.dataUniqueKey" placeholder="DataUniqueKey" style="width: 165px;"></el-input>
          </el-form-item>
          <el-form-item label="">
            <el-select v-model="search.rejectCode" placeholder="FIE" style="width: 100px;">
              <el-option label="Y" value="1"></el-option>
              <el-option label="N" value="0"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="">
            <el-select v-model="search.status" placeholder="status" style="width: 100px;">
              <el-option label="Y" value="1"></el-option>
              <el-option label="N" value="0"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="searchFn(1)">搜索</el-button>
          </el-form-item>
          <el-form-item>
        <el-upload class="upload-demo" ref="upload" action="" accept=".xls,.xlsx" :auto-upload="false" :on-change="uploadChange" :show-file-list="false">
          <el-button size="small" type="primary" style="background-color: #b240ff;border-color: #b240ff;" v-if="isAuth('list-manage:o-audit:auditList:exportin')">審核結果導入</el-button>
        </el-upload>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="exportFn" v-if="isAuth('list-manage:o-audit:auditList:exportout')">審核結果導出</el-button>
      </el-form-item>
          <el-form-item>
            <span class="clear-search" @click="clearSearch()">清除搜索</span>
          </el-form-item>
        </el-form>
      </div>
      <div class="box1 box-match-work-info">
        <div class="box2" style="width: 48%; margin-right: 2%">
          <h4>Match Work</h4>
          <div v-loading="matchWorkLoading" element-loading-text="數據加載中" element-loading-background="rgba(255, 255, 255, 0.8)">
            <el-table :empty-text="tableresult" stripe :data="matchWorkTable" border :row-class-name="handleRowClass" @row-dblclick="getCurrentWorkInfo" height="345">
            <el-table-column prop="batch" label="batch" min-width="100">
            </el-table-column>
            <el-table-column prop="matchWorkId" label="WorkNo" min-width="120">
            </el-table-column>
            <el-table-column prop="matchWorkSocietyCode" label="workSoc" min-width="90">
            </el-table-column>
            <el-table-column prop="matchWorkType" label="work type" min-width="90">

            </el-table-column>
            <el-table-column prop="matchWorkTitle" label="work title" min-width="120">
              <template slot-scope="scope"><span class="over-line">{{scope.row.matchWorkTitle}}</span></template>
            </el-table-column>
          </el-table>
          <el-pagination background layout="prev, pager, next" :total="total" @current-change="handleWorkChange">
          </el-pagination>
          </div>
        </div>
        <div class="box2" style="width: 49%;">
          <h4>IP Share</h4>
          <div v-loading="ipShareLoading" element-loading-text="數據加載中" element-loading-background="rgba(255, 255, 255, 0.8)">
            <el-table :empty-text="tableresult" stripe :data="ipShareTable" border height="345" @row-dblclick="gotoMemberInfo1">
            <el-table-column prop="name" label="Name" min-width="200">
            </el-table-column>
            <el-table-column prop="workIpRole" label="IP Role" min-width="60">
            </el-table-column>
            <el-table-column prop="ipNameNo" label="IP Name No" min-width="80">
            </el-table-column>
            <el-table-column prop="ipSocietyCode" label="IP Soc" min-width="60">
            </el-table-column>
            <el-table-column prop="ipShare" label="Share" min-width="80">
            </el-table-column>
          </el-table>
          </div>
        </div>
      </div>
      <div class="box1 box-remit-work-info">
        <div class="box2" style="width: 50%; margin-bottom: 28px;">
          <h4>Remitted Work</h4>
          <div v-loading="remittedWorkLoading" element-loading-text="數據加載中" element-loading-background="rgba(255, 255, 255, 0.8)">
            <el-table :empty-text="tableresult" stripe :data="remittedWorkTable" border height="280" :row-class-name="handleRowClass" @row-dblclick="getCurrentIPInfo">
            <el-table-column prop="dataUniqueKey" label="Distinct" min-width="100">
            </el-table-column>
            <el-table-column prop="remitSociety" label="Remit Soc" min-width="100">
            </el-table-column>
            <el-table-column prop="fileType" label="File Type" min-width="90">
              <!-- <template slot-scope="scope">
              <span v-if="scope.row.fileType == 0">CRD</span>
              <span v-if="scope.row.fileType == 1">E4</span>
              <span v-if="scope.row.fileType == 2">F2</span>
            </template> -->
            </el-table-column>
            <!-- <el-table-column prop="matchWorkTitle" label="Title" min-width="110">
            </el-table-column> -->
            <el-table-column prop="fileBaseId" label="FID" min-width="80">
            </el-table-column>
          </el-table>
          </div>
        </div>
        <div class="box2" style="width: 50%;">
          <h4>Remitted Work IP</h4>
          <div v-loading="remittedIPLoading" element-loading-text="數據加載中" element-loading-background="rgba(255, 255, 255, 0.8)">
            <el-table :empty-text="tableresult"   stripe :data="remittedIPTable" border height="280" >
            <el-table-column prop="fileMappingId" label="Seq No" min-width="70">
            </el-table-column>
            <el-table-column prop="ipName" label="Name" min-width="150">
            </el-table-column>
            <el-table-column prop="workIpRole" label="IP Role" min-width="60">
            </el-table-column>
            <el-table-column prop="ipNameNo" label="Ip Name No" min-width="90">
            </el-table-column>
            <el-table-column prop="ipSocietyCode" label="IP Soc" min-width="60">
            </el-table-column>
            <el-table-column prop="shareRatio" label="Share" min-width="60">
            </el-table-column>
            <el-table-column prop="amount" label="Amount" min-width="70">
            </el-table-column>
          </el-table>
          </div>
        </div>
      </div>
      <div class="box1 box-distibute-work-ip">
        <div class="box2" style="width: 99%;">
          <div class="flex">
            <h4>Distribute Work IP</h4>
            <el-button type="primary" @click="submitFn">save</el-button>
          </div>
          <div v-loading="distributeWorkIPLoading" element-loading-text="數據加載中" element-loading-background="rgba(255, 255, 255, 0.8)">
            <el-table :empty-text="tableresult"   stripe :data="remittedIPTable"
                      border height="280" :row-class-name="handleRowClass" @row-dblclick="gotoMemberInfo2">
            <el-table-column prop="status" label="審核狀態">
              <template slot-scope="scope">
                <span style="color: red" v-if="scope.row.status == 0">待審核</span>
                <span v-if="scope.row.status == 1">已審核</span>
              </template>
            </el-table-column>
            <el-table-column label="IsManual"><template slot-scope="scope">
                <!-- <span class="over-line" :title="scope.row.rejectMessage">{{scope.row.rejectMessage}}</span> -->
                <span v-if="scope.row.manual==true&&scope.row.status!=0">Manual</span>
                <span v-if="scope.row.manual==false&&scope.row.status!=0">Auto</span>
                <span v-if="scope.row.status==0"></span>

              </template>
            </el-table-column>
            <el-table-column prop="fileMappingId" label="Seq No">
            </el-table-column>
            <el-table-column prop="matchIpNameNo" label="Match IP Name No">
            </el-table-column>
            <el-table-column prop="matchIpName" label="IP Name">
            </el-table-column>
            <el-table-column prop="matchIpRole" label="IP Role">
            </el-table-column>
            <!-- 删除重复的IP Name No字段，因为我们已经有了Match IP Name No字段 -->
            <el-table-column prop="matchIpSoc" label="IP Soc">
            </el-table-column>
            <el-table-column prop="matchIpShare" label="Share">
            </el-table-column>
            <el-table-column prop="amount" label="Amount">
            </el-table-column>
            <el-table-column prop="rejectCode" label="FIE Code">
            </el-table-column>
            <el-table-column prop="rejectMessage" label="FIE Message">
              <template slot-scope="scope"><span class="over-line" :title="scope.row.rejectMessage">{{scope.row.rejectMessage}}</span></template>
            </el-table-column>
            <el-table-column label="operation">
              <template slot-scope="scope">
                <el-button type="text" @click="showDialog(scope.row)" class="lastclick">審核</el-button>
              </template>
            </el-table-column>
          </el-table>
          </div>
        </div>
      </div>
    </div>
    <el-dialog title="匹配指定IP" :visible.sync="dialogVisible" width="60%">
        <el-button type="primary" @click="showLpiDialog">选择IP</el-button>
        <el-tabs v-model="dialogActiveName">
        <el-tab-pane label="審核" name="first">
          <el-table :empty-text="tableresult"   stripe :data="ipShareTable" border>
            <el-table-column label="Name" min-width="90">
              <template slot-scope="scope">
                <span>{{ scope.row.chineseName || scope.row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="workIpRole" label="IP Role">
            </el-table-column>
            <el-table-column prop="ipNameNo" label="IP Name No">
            </el-table-column>
            <el-table-column prop="ipSocietyCode" label="IP Soc">
            </el-table-column>
            <el-table-column prop="ipShare" label="Share">
            </el-table-column>
            <el-table-column label="operation">
              <template slot-scope="scope">
                <el-button type="text" @click="matchIp(scope.row)">選擇</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="不匹配" name="second">
          <!-- <el-select v-model="selectValue" @change="msgChange" placeholder="请选择">
            <el-option v-for="item in errorList" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
          <div class="description">
            <el-button type="primary" @click="reject">確定</el-button>
          </div> -->
          <el-table :empty-text="tableresult"   stripe :data="errorList" border>
            <el-table-column prop="value" label="code" min-width="90">
            </el-table-column>
            <el-table-column prop="label" label="Description">
            </el-table-column>
            <el-table-column label="operation">
              <template slot-scope="scope">
                <el-button type="text" @click="reject(scope.row)">選擇</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
      <select-ip v-if="IpTableVisible" ref="selectIpCom" :search="ipSearch" @checkIp="checkIp"></select-ip>
  </div>
</template>

<script>
import qs from 'qs'
import axios from '../../../utils/httpRequest'
import selectIp from '@/components/select-ip'  // 新增导入语句

export default {
  name: "ip",
    components: {  // 新增组件注册
        selectIp
    },
  data() {
    return {
        IpTableVisible: false,// 控制select-ip弹窗显示
        ipSearch: {},           // select-ip搜索参数
      search: {
        page_size: 10,
        batch: '',
        matchWorkId: '',
        matchWorkSoc: '',
        matchWorkTitle: '',
        dataUniqueKey: '',
        distributedIp: '',
        distributedIpName: '',
        matchIpSoc: '',
        remitSoc: '',
        remitIpName: '',
        remitWorkTitle: '',
        disNo: '',
        fId: '',
        rejectCode: '',
        status: ''
      },
      total: 0,
      matchInfo: {},
      matchWorkTable: [],
      ipShareTable: [],
      remittedWorkTable: [],
      matchIPInfo: {},
      remittedIPTable: [],
      dialogActiveName: 'first',
      dialogVisible: false,
      errorList: [
        {
          label: 'NON MUST WORK',
          value: 'F05'
        },
        {
          label: 'UNIDENTIFIED WORK',
          value: 'F01'
        },
        {
          label: 'NON MUST MEMBER',
          value: 'F02'
        },
        {
          label: 'NON MUST SHARE',
          value: 'F03'
        },
        {
          label: 'MUST PUBLISHER MEMBER HAS NOT INTEREST IN YOUR TERITON',
          value: 'F04'
        },
        {
          label: 'MISSING SHARES',
          value: 'MS'
        },
        {
          label: 'ENQUIRY',
          value: 'ENQ'
        },
        {
          label: 'FICHE INTERNATIONALES',
          value: 'FI'
        },
        {
          label: 'ROYAL TIES RETURNED BY SOCIETY',
          value: 'R'
        }
      ],
      selectValue: '',
      selectLabel: "",
      currentIpRow: null,
      tabShow: false,
      useitem: '',tableresult:' ',
      showTable: true,
      matchWorkLoading: false,
      ipShareLoading: false,
      remittedWorkLoading: false,
      remittedIPLoading: false,
      distributeWorkIPLoading: false,
      ipShareLoadingTimer: null
    }
  },
  mounted() {
    this.searchFn(1)
  },
  methods: {
    handleRowClass(row, index) {

      if (row.row == this.matchInfo || row.row == this.matchIPInfo || row.row == this.currentIpRow) {
        return 'current'
      } else {
        return 'no';
      }
    },
      showLpiDialog() {
          this.IpTableVisible = true;
          this.ipSearch = {
              name_no: '',
              name: '',
              soc: ''
          }
          this.$nextTick(() => {
              this.$refs.selectIpCom.init();
          })
      },
      checkIp(info) {
          // 处理选择的IP信息
          console.log('Selected IP:', info);
          // 将选择的IP信息赋值给当前行
          if (this.currentIpRow) {
              this.currentIpRow.matchIpNameNo = info.ip_name_no;
              this.currentIpRow.matchIpSoc = info.society_code;
              this.currentIpRow.matchIpName = info.chinese_name || info.name;
              this.currentIpRow.matchIpType = info.ip_type;
              this.currentIpRow.matchIpShare = '';
              this.currentIpRow.matchIpRole = info.role_code;
              this.currentIpRow.manual = true;
              this.currentIpRow.rejectCode = '';
              this.currentIpRow.rejectMessage = '';
              this.currentIpRow.status = 1;
              this.currentIpRow.matchFlag = true;
          }
          this.IpTableVisible = false;
          this.dialogVisible = false;
      },
    clearSearch() {
      this.search = {
        batch: '',
        matchWorkId: '',
        matchWorkSoc: '',
        matchWorkTitle: '',
        dataUniqueKey: '',
        distributedIp: '',
        distributedIpName: '',
        matchIpSoc: '',
        remitSoc: '',
        remitIpName: '',
        remitWorkTitle: '',
        disNo: '',
        fId: '',
        rejectCode: '',
        page_size: 10
      }
      this.searchFn(1);
    },
    searchFn(page = 1) {
      if (page == 1) {
        this.total = 0
      }
      let ajaxData = this.$utils.copy(this.search);
      ajaxData.page = {
        pageNum:page,
        pageSize:this.search.page_size
      }
      this.tableresult = ' '
      this.matchWorkLoading = true

      // 打印搜索参数，帮助调试
      console.log('搜索参数:', ajaxData);

      this.$http.post('/listmatchoverseas/listListMatchDataOverseasUniq', ajaxData).then(res => {
        if (res.success && res.data && res.data.data) {
          this.matchWorkTable = res.data.data.list || []
          this.total = res.data.data.total || 0

          // 清空相关表格
          this.ipShareTable = []
          this.remittedWorkTable = []
          this.remittedIPTable = []

          // 根据Match Work表格的数据设置表格结果提示
          this.tableresult = this.matchWorkTable.length === 0 ? '暫無數據' : ' '
        } else {
          // 处理API响应成功但数据结构不符合预期的情况
          this.matchWorkTable = []
          this.total = 0
          this.ipShareTable = []
          this.remittedWorkTable = []
          this.remittedIPTable = []
          this.tableresult = '暫無數據'
          console.warn('API返回数据结构不符合预期:', res);
        }
        this.matchWorkLoading = false
      }).catch(error => {
        // 处理API请求失败的情况
        this.matchWorkTable = []
        this.total = 0
        this.ipShareTable = []
        this.remittedWorkTable = []
        this.remittedIPTable = []
        this.tableresult = '獲取數據失敗'
        this.matchWorkLoading = false
        console.error('API请求失败:', error);
      })
    },
    handleWorkChange(page) {
      this.searchFn(page)
    },
    getCurrentWorkInfo(row) {
      this.matchInfo = row
      this.remittedIPTable = []
      // 使用 $forceUpdate 强制更新组件，重新计算行样式，但不会重新创建DOM
      this.$forceUpdate()
      this.getRemittedWork()
      this.getMatchWorkIpShare()
    },
    getRemittedWork() {
      this.remittedWorkLoading = true
      this.$http.get('/ListMatchDataOverseasMatchWorkCheck/listRemittedWork', { params: { dataUniqueKey: this.matchInfo.dataUniqueKey } }).then(res => {
        console.log(res)
        if (res.success) {
          this.remittedWorkTable = res.data.data
          if (this.remittedWorkTable.length > 0) {
            let row = this.remittedWorkTable[0]
            this.handleRowClass(row)
            this.getCurrentIPInfo(row)
          }
        }
        this.remittedWorkLoading = false
      }).catch(error => {
        this.remittedWorkLoading = false
        console.error('获取Remitted Work数据失败:', error);
      })
    },
    getCurrentIPInfo(row) {
      this.matchIPInfo = row
      this.getRemittedIP()
    },
    getRemittedIP() {
      this.remittedIPLoading = true
      this.distributeWorkIPLoading = true
      this.$http.get('/ListMatchDataOverseasMatchWorkCheck/listRemittedIp', { params: { dataUniqueKey: this.matchIPInfo.dataUniqueKey, fId: this.matchIPInfo.fileBaseId } }).then(res => {
        console.log(res)
        if (res.success) {
          this.remittedIPTable = res.data.data
            this.matchInfo.workUniqueKey = this.remittedIPTable[0].matchWorkUniqueKey
        }
        this.remittedIPLoading = false
        this.distributeWorkIPLoading = false
      }).catch(error => {
        this.remittedIPLoading = false
        this.distributeWorkIPLoading = false
        console.error('获取Remitted IP数据失败:', error);
      })
    },
    getMatchWorkIpShare() {
      let params = {
        id: this.matchInfo.id,
        workId: this.matchInfo.matchWorkId,
        workSocietyCode: this.matchInfo.matchWorkSocietyCode
      }
      this.tableresult = ' '

      // 清除之前的计时器（如果存在）
      if (this.ipShareLoadingTimer) {
        clearTimeout(this.ipShareLoadingTimer);
      }

      // 设置一个计时器，如果加载时间超过250毫秒，则显示加载遮罩
      this.ipShareLoadingTimer = setTimeout(() => {
        this.ipShareLoading = true;
      }, 250);
      this.$http.get('/wrk/getWrkWorkIpShareById', { params }).then(res => {
        if (res.success) {
          this.ipShareTable =res.data.wwisList[0].hasOwnProperty('PER')? res.data.wwisList[0]['PER']:[]
        }
        this.tableresult = this.ipShareTable.length == 0 ? '暫無數據' : ' '

        // 清除计时器并隐藏加载遮罩
        clearTimeout(this.ipShareLoadingTimer);
        this.ipShareLoading = false;
      })
    },
    showDialog(row) {
      this.currentIpRow = row
      this.dialogVisible = true
      this.selectValue = row.rejectCode
    },
    msgChange(val) {
      this.errorList.map(item => {
        if (item.value == val) {
          this.selectLabel = item.label
        }
      })
      // console.log('=========',this.selectLabel)
    },
    reject(row) {
      // console.warn('this.sleelct',this.selectLabel,this.selectValue)
      // if (!this.selectValue) {
      //   this.$toast({tips:'請選擇不匹配原因'})
      //   return
      // }
      this.currentIpRow.rejectCode = row.value
      this.currentIpRow.rejectMessage = row.label
      this.currentIpRow.status = 1
      this.currentIpRow.manual = true
      // this.currentIpRow.matchIpNameNo = ''
      // this.currentIpRow.matchIpSoc = ''
      // this.currentIpRow.matchIpName = ''
      // this.currentIpRow.matchIpType = ''
      this.currentIpRow.matchFlag = true
      this.dialogVisible = false

    },
    matchIp(row) {
      this.currentIpRow.matchIpNameNo = row.ipNameNo
      this.currentIpRow.matchIpSoc = row.ipSocietyCode
      this.currentIpRow.matchIpName = row.chineseName || row.name
      this.currentIpRow.matchIpType = row.ipType
      this.currentIpRow.matchIpShare = row.ipShare
      this.currentIpRow.matchIpRole = row.workIpRole
      this.currentIpRow.manual = true
      this.currentIpRow.rejectCode = ''
      this.currentIpRow.rejectMessage = ''
      this.currentIpRow.status = 1
      this.currentIpRow.matchFlag = true
      this.dialogVisible = false
    },
    submitFn() {
      let matchData = this.remittedIPTable.filter(item => {
        if (item.matchFlag) {
          return item
        }
      })
      if(matchData.length == 0){
          this.$toast({ tips: '請選擇匹配' })
          return
      }
      let data = {
        listMatchDataOverseasMappingList: matchData,
        workUniqueKey: this.matchInfo.workUniqueKey
      }
      this.$http.post('/ListMatchDataOverseasMatchWorkCheck/checkIp', data).then(res => {
        console.log(res)
        if (res.data.code == 200) {
          this.$toast({ tips: '保存成功' })
        } else {
          this.$toast({ tips: res.data.message || '保存失败！' })
        }
      })
    },
    gotoMemberInfo1(row){
      if(!row.ipNameNo){
        this.$toast({ tips: '未审核或无效的IP数据，无法查看详情！' });
        return;
      }

      this.gotoMemberInfo(row.ipNameNo)

    },

    gotoMemberInfo2(row){
       // 检查行数据是否有效且已审核
       if (!row.matchIpNameNo || row.status === 0 || !row.matchIpName) {
        this.$toast({ tips: '未审核或无效的IP数据，无法查看详情！' });
        return;
      }

      this.currentIpRow = row

      this.gotoMemberInfo(row.matchIpNameNo)

    },
    gotoMemberInfo(ipNameNo) {

      // this.$router.push({name:'member-info',query:{nameId:row.ipBaseNo,title:row.name,tabShow:false,ipBaseNo:row.ipBaseNo}})
      // console.table("%%%%%%%%%%",row)
      let params = {
        name_no: ipNameNo, // 使用matchIpNameNo而不是ipNameNo
        name: '',
        status: '',
        ip_no: '',
        ip_type: '',
        role_code: '',
        soc: '',
        page_num: 1,
        page_size: 10
      }

      // 记录日志，帮助调试
      console.log('查询IP信息参数:', params);

      let formData = qs.stringify(params);
      let config = { headers: { 'content-type': 'application/x-www-form-urlencoded' } }
      axios.post('/ip/name/es', formData, config).then(res => {
        // console.log('@@@@@@@@',res)
        this.useitem = res.data.list.length > 0 ? res.data.list[0] : res.data.list
        console.warn("-----", res, 'useitem', this.useitem)
        if (res.data.list.length == 0) {
          this.$toast({ tips: '没有当前会员信息！' });
          return
        }
        if (res.data.list.length > 0) {
          if (this.useitem.society_code == 161) {
            this.tabShow = false
          } else {
            this.tabShow = true
          }
          this.$router.push({
            path: '/member-info', name: 'member-info',
            query: { id: this.useitem.id, nameId: this.useitem.ip_base_no + this.useitem.ip_name_no, title: this.useitem.chinese_name ? this.useitem.chinese_name : this.useitem.name, tabShow: this.tabShow, ipBaseNo: this.useitem.ip_base_no, ipId: this.useitem.id }
          })
        }
      }).catch(error => {
        console.error('查询IP信息失败:', error);
        this.$toast({ tips: '查询IP信息失败！' });
      })
    },
    uploadChange(file) {
      console.log(file)
      this.$utils.uploadFile({flie:file.raw}, '/listmatchoverseasmapping/importAuditResults', this)
    },
    exportFn() {
      this.$msgbox.confirm(`確定進行[導出]操作?`, '提示', {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let ajaxData = {};
        ajaxData = this.$utils.copy(this.search);
        this.$http.post('/listmatchoverseasmapping/export', ajaxData, { responseType: 'blob' }).then(res => {
          console.log(res)
          let data = res.data
          this.$utils.downloadByBlob(data, res.headers["content-disposition"])
        })
      })
    },
  }
}
</script>

<style scoped lang="scss">
.main {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  .box1 {
    display: flex;

  }
}
.description {
  text-align: center;
  padding-top: 20px;
}
.flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
/deep/ tr.current > td {
  background-color: #17b3a3 !important;

}
/deep/ tr.current > td:nth-last-child(1) .el-button--text span {
  // background-color: #17b3a3 !important;
  color:#fff !important;

}
</style>
