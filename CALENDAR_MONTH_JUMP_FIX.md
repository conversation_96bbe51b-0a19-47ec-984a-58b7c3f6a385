# 日历月份跳转自动选中1号问题修复

## 问题描述

在自定义日期编辑区域跳转到其他月份时，会自动将1号选中。

## 问题原因

这个问题的根本原因是 `vue-calendar-component` 组件的设计行为：

1. **月份选择器值格式**: 月份选项的值格式为 `"YYYY-MM-01"`，固定使用1号作为日期部分
2. **ChoseMonth方法行为**: 当传入完整日期（如 `"2024-01-01"`）时，`vue-calendar-component` 不仅会跳转到对应月份，还会自动选中该具体日期（1号）
3. **组件设计**: 这是 `vue-calendar-component` 的预期行为，而不是一个bug

## 解决方案

采用**方案1：修改传入 ChoseMonth 的参数格式**

### 修改内容

在所有调用 `ChoseMonth` 方法的地方，将传入的日期从 `"YYYY-MM-01"` 改为 `"YYYY-MM-15"`：

```javascript
// 修改前
this.$refs.calendar.ChoseMonth(this.targetYearMonth); // "2024-01-01"

// 修改后  
const yearMonth = this.targetYearMonth.substring(0, 7) // "2024-01-01" -> "2024-01"
this.$refs.calendar.ChoseMonth(yearMonth + '-15'); // "2024-01-15"
```

### 修改的文件

1. `src/views/list-manage/normal-list/sample-date/edit.vue`
   - `chooseMonth()` 方法
   - `chooseCustomMonth()` 方法  
   - `copyFromPreview()` 方法中的日历同步

2. `src/views/list-manage/normal-list/sample-date/add.vue`
   - `chooseMonth()` 方法

3. `src/views/list-manage/sample-date/edit.vue`
   - `chooseMonth()` 方法

4. `src/views/list-manage/sample-date/add.vue`
   - `chooseMonth()` 方法

### 修改效果

- ✅ 跳转月份时不再自动选中1号
- ✅ 保持原有的月份跳转功能
- ✅ 不影响用户手动选择日期的功能
- ✅ 使用15号作为跳转日期，避免月末日期可能的边界问题

## 测试建议

1. 测试月份跳转功能是否正常工作
2. 验证跳转后不会自动选中任何日期
3. 确认用户仍可以正常手动选择/取消选择日期
4. 测试从预览复制日期到自定义编辑的功能

## 备选方案

如果当前方案有问题，可以考虑：

**方案2**: 在跳转后清除选中状态
**方案3**: 使用项目中的自定义日历组件 `src/components/custom-calendar/index.vue`
