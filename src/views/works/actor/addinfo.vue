<template>
    <div>
        <!-- <el-card class="box-card saveright"><el-button type="primary" @click="savewrk">保 存</el-button></el-card> -->
        <top-bar :noShow="false" @save="savewrk"></top-bar>
        <div class="contentbox">
            <el-form :inline="true" :model="formWorks" label-width="100px" label-position="left" class="demo-form-inline">
                <div>
                    <!-- <el-form-item label="id">
                        <el-input v-model="formWorks.id" placeholder="" disabled></el-input>
                    </el-form-item> -->
                    <el-form-item label="refArtistId">
                        <el-input v-model="formWorks.refArtistId" placeholder=""></el-input>
                    </el-form-item>
                    <el-form-item label="firstName">
                        <el-input v-model="formWorks.firstName" placeholder=""></el-input>
                    </el-form-item>
                    <el-form-item label="lastName">
                        <el-input v-model="formWorks.lastName" placeholder=""></el-input>
                    </el-form-item>
                </div>
                <div>
                    <el-form-item label="ChName">
                        <el-input v-model="formWorks.chineseName" placeholder=""></el-input>
                    </el-form-item>
                    <el-form-item label="romanName">
                        <el-input v-model="formWorks.romanName" placeholder=""></el-input>
                    </el-form-item>
                </div>
                <div>
                    <el-form-item label="ipNameNo" >
                        <el-input v-model="formWorks.ipNameNo" placeholder=""></el-input>
                    </el-form-item>
                    <el-form-item label="bandName">
                        <el-input v-model="formWorks.bandName" placeholder=""></el-input>
                    </el-form-item>
                <el-form-item label="country" >
                    <el-input v-model="formWorks.country" placeholder="雙擊查詢" @dblclick.native="getCountry(1,'country')" readonly></el-input>
                </el-form-item>
                </div>
                <div>
                    <el-form-item label="ipBaseNo" >
                        <el-input v-model="formWorks.ipBaseNo" placeholder=""></el-input>
                    </el-form-item>
                    <el-form-item label="tisN" >
                        <el-input v-model="formWorks.tisN" placeholder="雙擊查詢" @dblclick.native="getCountry(1,'tisN')" readonly></el-input>
                    </el-form-item>
                    <el-form-item label="is_band" >
                        <el-checkbox v-model="checked"></el-checkbox>
                    </el-form-item>
                </div>
            </el-form>
        </div>
        <el-dialog :visible.sync="countryDialogVisible" :close-on-click-modal="false" :title="countryDialogTitle">
            <div style="width: 600px;margin: auto;margin-bottom: 20px">
                <el-input style="width:150px" @keyup.enter.native="getCountry()" placeholder="countryCode" v-model="searchData.countryCode" class="input-with-select"></el-input>
                <el-input style="width:150px" @keyup.enter.native="getCountry()" placeholder="name" v-model="searchData.name" class="input-with-select"></el-input>
                <el-input style="width:150px" @keyup.enter.native="getCountry()" placeholder="tisN" v-model="searchData.tisN" class="input-with-select"></el-input>
                <el-button slot="append" icon="el-icon-search" @click="getCountry()"></el-button>
                <span class="clear-search" @click="clearSearch()">清除搜索</span>
            </div>
            <el-table :empty-text="tableresult"   stripe :data="countryData">
                <el-table-column property="countryCode" label="countryCode"></el-table-column>
                <el-table-column property="name" label="name"></el-table-column>
                <el-table-column property="tisN" label="tisN"></el-table-column>
                <el-table-column
                    label="operation"
                    width="100">
                    <template slot-scope="scope">
                        <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                            <i class="el-icon-check" @click="checkedTisN(scope.$index,scope.row)"></i>
                        </span>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                background
                layout="prev, pager, next"
                :total="countryTotal" @current-change="getCountry" :current-page="currentPage">
            </el-pagination>
        </el-dialog>
    </div>
</template>

<script>
    // import axios from '../../utils/httpRequest'
    export default {
        name: 'addinfo',
        data () {
            return {
                countryDialogTitle:'',
                checked: false,
                countryDialogVisible: false,
                formWorks: {
                    // id: '',
                    refArtistId: '',
                    firstName: '',
                    lastName: '',
                    chineseName: '',
                    // chinese: '',
                    romanName: '',
                    bandName: '',
                    tisN: '',
                    country: '',
                    ipBaseNo: '',
                    isBand: '',
                    ipNameNo: '',
                    inputSoc: 161
                },
                pageinfo: {
                    page: {
                        pageNum: 1,
                        pageSize: 10
                    }
                },
                countryData: [],tableresult:' ',
                countryTotal: 0,
                currentPage:0,
                searchTisN: '',
                searchData:{
                    tisN:'',
                    countryCode:'',
                    name:'',
                    page: {
                        pageNum: 1,
                        pageSize: 10
                    }
                }

            }
        },
        methods: {
            clearSearch(){
                this.searchData={
                    tisN:'',
                    countryCode:'',
                    name:'',
                    page: {
                        pageNum: 1,
                        pageSize: 10
                    }
                }
                this.getCountry()
            },
            savewrk () {
                if(!this.formWorks.firstName && !this.formWorks.lastName){
                    this.$toast({tips: 'firstName與lastName必填一個！'})
                    return;
                }
                if (this.checked) {
                    this.formWorks.isBand = 'Y'
                } else {
                    this.formWorks.isBand = 'N'
                }
                let params = this.formWorks;
                this.$http.post('/wrk/artist/saveOrUpdateWrkArtist', params).then(res => {
                    if (res.status === 200) {
                        this.$toast({tips: this.id ? '編輯成功': '添加成功'});
                        this.$bus.$emit('closeCurrentTab');
                        this.$router.push({name: 'works-actor'})
                    }
                })
            },
            getCountry (num,name) {
                // let params = this.pageinfo
                let params = this.searchData
                console.log(params)
                this.countryDialogTitle = name || this.countryDialogTitle
                params.page.pageNum = num ? num : 1;
                this.countryDialogVisible = true
                // params.tisN = this.searchTisN ? this.searchTisN : null;
                      this.tableresult = '數據加載中...'
                this.$http.post('/ref/getRefCountry', params).then(res => {
                    this.countryData = res.data.list;
                    this.countryTotal = res.data.total;
                    this.currentPage = params.page.pageNum
                      this.tableresult = this.countryData.length == 0 ? '暫無數據' : ' '
                })
            },
            getTisN () {
                // let params = this.pageinfo
                let params = this.searchData
                this.countryDialogTitle = 'TisN'
                this.countryDialogVisible = true
                      this.tableresult = '數據加載中...'
    //   this.tableresult = this.tableData.length == 0 ? '暫無數據' : ' '
                this.$http.post('/ref/getRefCountry', params).then(res => {
                    this.countryData = res.data.list;
                    this.countryTotal = res.data.total;
                this.tableresult = this.countryData.length == 0 ? '暫無數據' : ' '
                })
            },
            checkedTisN (index, item) {
                this.countryDialogVisible = false;
                this.formWorks.tisN = item.tisN;
                this.formWorks.country = item.countryCode;
                // this.searchTisN = '';
                this.searchData.tisN=''
                this.searchData.countryCode=''
                this.searchData.name=''

            },
            getInfo(){
                let params = this.id;
                this.$http.get('/wrk/artist/queryWrkWorkById/?id=' + params).then(res => {
                    if (res.status === 200) {
                        this.formWorks = res.data;
                        if(this.formWorks.isBand == 'Y'){
                            this.checked = true;
                        }
                    }
                })
            }
        },
        mounted () {
            this.id = this.$route.query.id;
            if(this.id){
                this.getInfo();
            }
        },
    }
</script>

<style>
    .right{
        float: right;
    }
    .el-checkbox{
        width: 185px;
    }
    .el-form-item__label{
        margin-left: 40px;
    }
</style>
