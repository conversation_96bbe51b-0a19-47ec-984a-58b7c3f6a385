<template>
  <div style="padding-top: 40px;">
    <el-form ref="form" :model="baseInfo" :inline="true" :rules="rules" class="p-t-20" label-width="146px">
      <el-form-item label="記錄序號">
        <el-input type="text" v-model="baseInfo.id" :disabled="true" style="text-align:right"></el-input>
      </el-form-item>
      <el-form-item label="匯款日期" prop="receiptDate">
        <el-date-picker v-model="baseInfo.receiptDate" type="date" placeholder="輸入日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd"></el-date-picker>
      </el-form-item>
      <el-form-item label="協會代碼" required style="width: 373px">
        <el-col style="width: 130px;">
          <el-form-item prop="sourceSocName">
            <el-input type="text" style="width: 130px;" v-model.trim="baseInfo.sourceSocName" placeholder="soc name" @dblclick.native="getSocietyList()" @blur="blurwrite(baseInfo.sourceSocName,'sourceSocName')"></el-input>
            <!-- onfocus=this.blur() -->
            <!-- @blur="getSocietyByName" -->
          </el-form-item>
        </el-col>
        <el-col style="width: 72px;margin-right: 5px">
          <el-form-item prop="sourceSoc">
            <el-input type="text" style="width: 72px;" @keydown.native="$inputNumber" placeholder="code" v-model.trim="baseInfo.sourceSoc" @blur="blurwrite(baseInfo.sourceSoc,'sourceSoc')" @dblclick.native="getSocietyList()"></el-input>
            <!-- @blur="getSocietyByCode" -->
          </el-form-item>
        </el-col>

      </el-form-item>
      <el-form-item label="國家名稱" class="is-required" style="width: 373px">
        <el-col style="width: 130px;">
          <el-form-item prop="sourceCountryName">
            <el-input type="text" style="width: 130px;" placeholder="country name" @dblclick.native="getCountryList()" v-model.trim="baseInfo.sourceCountryName" @blur="blurwrite(baseInfo.sourceCountryName,'sourceCountryName')"></el-input>
          </el-form-item>
        </el-col>
        <el-col style="width: 72px;margin-right: 5px">
          <el-form-item prop="sourceCountryCode">
            <el-input type="text" style="width: 72px;" placeholder="code" v-model.trim="baseInfo.sourceCountryCode" @dblclick.native="getCountryList()"></el-input>
            <!-- @blur="getCountryByCode" -->
          </el-form-item>
        </el-col>

      </el-form-item>
      <el-form-item label="貨幣代號" prop="currencyCode">
        <el-input type="text" v-model="baseInfo.currencyCode" placeholder="雙擊選擇" @dblclick.native="getCurrencyList()" ></el-input>
      </el-form-item>
      <el-form-item label="匯率" prop="exchangeRate">
        <el-input type="text" @keydown.native="$inputNumber" v-model="baseInfo.exchangeRate"></el-input>
      </el-form-item>
      <el-form-item label="外幣金額" prop="receiptAmount">
        <!-- <el-input type="text" @keydown.native="$inputNumber" v-model="baseInfo.receiptAmount"></el-input> -->
        <thousand-input class="input-text-right" @keydown.native="$inputNumber" v-model.trim="baseInfo.receiptAmount"  />
      </el-form-item>
      <el-form-item label="台幣金額">
        <!-- <el-input type="text" @keydown.native="$inputNumber" v-model="localAmount" :readonly="true"></el-input> -->
        <thousand-input class="input-text-right" @keydown.native="$inputNumber" v-model.trim="localAmount" :readonly="true"  />
      </el-form-item>
      <el-form-item label="手續費" prop="chargeAmount">
        <!-- <el-input type="text" @keydown.native="$inputNumber" v-model="baseInfo.chargeAmount"></el-input> -->
        <thousand-input class="input-text-right" @keydown.native="$inputNumber" v-model.trim="baseInfo.chargeAmount" />
      </el-form-item>
      <el-form-item label="入帳金額">
        <!-- <el-input type="text" @keydown.native="$inputNumber" v-model="distAmount" :readonly="true"></el-input> -->
        <thousand-input class="input-text-right" @keydown.native="$inputNumber" v-model.trim="distAmount" :readonly="true" />
      </el-form-item>
      <el-form-item label="通知人員">
        <el-input type="text" v-model="baseInfo.noticeUserName" ></el-input>
      </el-form-item>
      <el-form-item label="通知日期">
        <el-date-picker v-model="baseInfo.noticeTime" type="date" placeholder="輸入日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd"></el-date-picker>
      </el-form-item>
      <!--<el-form-item label="清單序號">
                <el-input type="text" v-model="baseInfo.countryCode" :readonly="true"></el-input>
            </el-form-item>-->
      <!--<el-form-item label="分配代號">
                <el-input type="text" v-model="baseInfo.distNo"></el-input>
            </el-form-item>-->
      <!--<el-form-item label="处理状态">
                <el-switch
                    v-model="baseInfo.checked"
                    :active-value="1"
                    :inactive-value="0"
                    active-color="#13ce66"
                    inactive-color="#e9e9e9"
                ></el-switch>
            </el-form-item>-->
      <div class="remark">
        <el-form-item label="備註">
          <el-input type="textarea" autosize v-model="baseInfo.remark"></el-input>
        </el-form-item>
      </div>
      <div>
        <el-form-item label="上傳文件">
          <el-upload class="upload-demo" style="display: inline-block;margin-right: 20px" action :on-change="getFile" :auto-upload="false">
            <el-button size="small" type="primary">{{baseInfo.attachmentPath?'重新上傳':'點擊上傳'}}</el-button>

          </el-upload>

        </el-form-item>
      </div>
      <div v-if="baseInfo.attachmentPath && $route.query.title != 'add'">
        <el-form-item label="下載文件">
          <p class="download-link" @click="download">{{attachmentPath}}</p>
        </el-form-item>
      </div>
      <!-- <el-form-item label="下載文件" v-if="baseInfo.attachmentPath && $route.query.title != 'add'">
                <el-button size="small" type="primary" @click="download">下載文件</el-button>
                <span>{{attachmentPath}}</span>
            </el-form-item>-->
      <div style="text-align: center;">
        <el-form-item>
          <el-button type="primary" @click="uploadFile" :loading="btnLoading">{{btnText}}</el-button>
        </el-form-item>
      </div>
    </el-form>

    <!--        获取协会编码-->
    <el-dialog title="協會列表" :visible.sync="dialog1Visible" width="60%" :close-on-click-modal="false">
      <div class="search">
        <el-form :model="societyFormData.data" :inline="true" @keyup.enter.native='getSocietyList()'>
          <el-form-item>
            <el-input placeholder="请输入SocietyCode" v-model.trim="societyFormData.data.societyCode" @keydown.native="$inputNumber" class="input-with-select">
            </el-input>
          </el-form-item>
          <el-form-item>
            <el-input placeholder="请输入societyName" v-model.trim="societyFormData.data.societyName"></el-input>
          </el-form-item>
          <!-- <el-form-item>
            <el-input placeholder="请输入sourceCountryName" v-model.trim="societyFormData.data.sourceCountryName"></el-input>
          </el-form-item> -->
          <el-form-item>
            <el-button type="primary" @click="getSocietyList()">搜索</el-button>
          </el-form-item>
          <el-form-item>
            <span class="clear-search" @click="clearSearch()">清除搜索</span>
          </el-form-item>
        </el-form>

      </div>
      <el-table :empty-text="emptyText"   :data="societyList" v-loading="Loading1" style="width: 100%">
        <el-table-column prop="societyCode" label="SocietyCode" width="180"></el-table-column>
        <el-table-column prop="societyName" label="SocietyName"></el-table-column>
        <el-table-column prop="countryCode" label="countryCode"></el-table-column>
        <el-table-column prop="countryName" label="countryName"></el-table-column>
        <el-table-column label="operation">
          <template slot-scope="scope">
            <i class="el-icon-check pointer" @click="checkSoc(scope.row)"></i>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background layout="prev, pager, next" :total="total1" @current-change="handleSocChange"></el-pagination>
    </el-dialog>
    <!--        获取国家名称-->
    <el-dialog title="國家列表" :visible.sync="dialog2Visible" width="50%" :close-on-click-modal="false">
      <el-form :model="countryFormData" :inline="true" @submit.native.prevent>
        <el-form-item>
          <el-input placeholder="请输入countryCode" v-model="countryFormData.countryCode" @keyup.enter.native='getCountryList()'>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getCountryList()">搜索</el-button>
        </el-form-item>
      </el-form>
      <el-table :empty-text="emptyText"   :data="countryList" v-loading="Loading2" style="width: 100%">
        <el-table-column prop="countryCode" label="CountryCode" width="180"></el-table-column>
        <el-table-column prop="name" label="Name"></el-table-column>
        <el-table-column label="operation" width="100">
          <template slot-scope="scope">
            <i class="el-icon-check pointer" @click="checkCountry(scope.row)"></i>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background layout="prev, pager, next" :total="total2" @current-change="handleCountryChange"></el-pagination>
    </el-dialog>
    <!--        获取货币-->
    <el-dialog title="货币列表" :visible.sync="dialog3Visible" width="50%" :close-on-click-modal="false">
      <div class="search">
        <el-input @keyup.enter.native='getCurrencyList()' placeholder="请输入currencyCode" v-model="currencyFormData.currencyCode" class="input-with-select">
          <el-button slot="append" @click="getCurrencyList()" icon="el-icon-search"></el-button>
        </el-input>
      </div>
      <el-table :empty-text="emptyText"   :data="currencyList" v-loading="Loading3" style="width: 100%">
        <el-table-column prop="currencyCode" label="CurrencyCode" width="120"></el-table-column>
        <el-table-column prop="currencyName" label="Name"></el-table-column>
        <el-table-column prop="currencyChineseName" label="ChineseName"></el-table-column>
        <el-table-column label="operation" width="100">
          <template slot-scope="scope">
            <i class="el-icon-check pointer" @click="checkCurrency(scope.row)"></i>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background layout="prev, pager, next" :total="total3" @current-change="handleCurrencyChange"></el-pagination>
    </el-dialog>
  </div>
</template>

<script>
import ThousandInput from '@/components/thousand-input';
export default {
  data() {
    return {
      rules: {
        receiptDate: [{ required: true, message: '请输入匯款日期', trigger: 'blur' }],
        receiptAmount: [{ required: true, message: '请输入外幣金額', trigger: 'blur' }],
        chargeAmount: [{ required: true, message: '请输入手續費', trigger: 'blur' }],
        sourceSoc: [{ required: true, message: '请选择', trigger: 'change' }],
        sourceSocName: [{ required: true, message: '请选择', trigger: 'change' }],
        sourceCountryCode: [{ required: true, message: '请选择', trigger: 'change' }],
        sourceCountryName: [{ required: true, message: '请选择', trigger: 'change' }],
        currencyCode: [{ required: true, message: '请选择', trigger: 'change' }],
        exchangeRate: [{ required: true, message: '请输入匯率', trigger: 'blur' }],

      },
      baseInfo: {
        attachmentPath: ''
      },
      file: '',
      dialog1Visible: false,
      Loading1: false,
      btnLoading: false,
      btnText: '確定',
      total1: 0,
      dialog2Visible: false,
      Loading2: false,
      total2: 0,
      dialog3Visible: false,
      Loading3: false,
      total3: 0,
      societyFormData: {
        page: {
          pageNum: 1,
          pageSize: 10
        },
        data: {
          societyCode: '',
          societyName: ''
        }
      },
      countryFormData: {
        page: {
          pageNum: 1,
          pageSize: 10
        },
        countryCode: ''

      },
      currencyFormData: {
        currencyCode: '',
        page_num: 1,
        page_size: 10
      },
      societyList: [],
      emptyText:'暫無數據',
      countryList: [],
      currencyList: [],
      canSave: true
    }
  },
  components: {
    ThousandInput
  },
  computed: {
    localAmount() {
      let reciept = this.baseInfo.receiptAmount || 0
      let rate = this.baseInfo.exchangeRate || 0
      reciept = new BigNumber(reciept)
      rate = new BigNumber(rate)
      return reciept.multipliedBy(rate).toFixed(0)
    },
    distAmount() {
      let localAmount = this.localAmount || 0
      let charge = this.baseInfo.chargeAmount || 0
      localAmount = new BigNumber(localAmount)
      charge = new BigNumber(charge)
      return localAmount.minus(charge).toFixed(0)
    },
    attachmentPath() {
      let attachmentPath = this.baseInfo.attachmentPath
      if (attachmentPath.lastIndexOf('/') != -1) {
        return attachmentPath.slice(attachmentPath.lastIndexOf('/') + 1, attachmentPath.length)
      } else if (attachmentPath.lastIndexOf('\\') != -1) {
        return attachmentPath.slice(attachmentPath.lastIndexOf('\\') + 1, attachmentPath.length)
      }

    }
  },
  activated() {
    if (this.$route.query.row) {
      this.baseInfo = JSON.parse(this.$route.query.row)
      console.log(this.baseInfo)
    }
  },
  mounted() {
    document.onkeydown = (e) => {
      console.log(e)
      if (e.keyCode === 13) {
        if (this.dialog2Visible) {
          this.getCountryList()
        } else if (this.dialog3Visible) {
          this.getCurrencyList()
        }
      }
    }
  },

  methods: {
    getFile(file) {
      this.file = file.raw
    },
    uploadFile() {
      if (this.file) {
        let formData = new FormData()
        formData.append('files', this.file)
        this.$http
          .post('/listOverseasFileBase/uploadFile', formData)
          .then(res => {
            if (res.success) {
              let type = 'warning'
              let msg = res.data
              if (res.data.indexOf('overseas-') !== -1) {
                this.baseInfo.attachmentPath = res.data.substring(9)
                this.save()
              } else {
                this.$message({
                  message: msg,
                  type: type
                })
              }

            }
          })
      } else {
        this.save()
      }

    },
    download() {
      this.$http.get('/listOverseasFileBase/download', { params: { absoluteFileName: this.baseInfo.attachmentPath }, responseType: "arraybuffer" }).then((res) => {
        console.log(res)
        if (res.data) {
          let type = 'pdf'
          let blob = new Blob([res.data], {
            type: res.headers['content-type']      //将会被放入到blob中的数组内容的MIME类型
          });
          let objectUrl = URL.createObjectURL(blob);  //生成一个url
          // window.location.href = objectUrl;   //浏览器打开这个url
          let a = document.createElement('a')
          a.href = objectUrl
          a.download = this.attachmentPath
          a.click()
        }
      })
    },
    save() {
      this.$refs.form.validate(validate => {
        if (validate) {
          this.baseInfo.localAmount = this.localAmount
          this.baseInfo.distAmount = this.distAmount
          this.btnLoading = true
          this.btnText = '保存中'
          this.$http
            .post(
              '/listOverseasReceipt/saveOrUpdateListOverseasReceipt',
              this.baseInfo
            )
            .then(res => {

              if (res.success) {
                if (res.data.indexOf('成功') != -1) {
                  this.$message({
                    message: res.data,
                    type: 'success',
                    duration: 1000,
                    onClose: () => {
                      this.$bus.$emit('closeCurrentTab', () => {
                        this.$router.push({ name: 'oList', query: { update: true } })
                      })
                    }
                  })
                } else {
                  this.$toast({ tips: res.data })
                  this.btnLoading = false
                  this.btnText = '確定'
                }
              }
            })
        }
      })
    },
    blurwrite(sourcename, type) {
      // debugger
      if (this.dialog1Visible||!sourcename) {
        return
      }


      // this.loading1 = true
      this.societyFormData.page.pageNum = 1
      if (type == 'sourceSocName') {
        this.societyFormData.data.societyName = sourcename
      }
      if (type == 'sourceSoc') {
        this.societyFormData.data.societyCode = sourcename
      }
      //   if (type=='sourceCountryName') {
      //       this.societyFormData.data.sourceCountryName = sourcename
      //   }

      this.$http.post('/ref/society/getSocietyList', this.societyFormData).then(res => {
        console.log('blur---', res.data.list[0])
        if (res.success) {
          let row = res.data.list[0]
          this.total1 = res.data.total
          this.$set(this.baseInfo, 'sourceSoc', row.societyCode)
          this.$set(this.baseInfo, 'sourceSocName', row.societyName)
          this.$set(this.baseInfo, 'sourceCountryCode', row.countryCode)
          this.$set(this.baseInfo, 'sourceCountryName', row.countryName)
        }
        this.loading1 = false
      })
    },
    clearSearch(){

        this.societyFormData.data.societyName=''
        this.societyFormData.data.societyCode=''
         this.societyFormData.page.pageNum = 1
         this.getSocietyList()

    },
    getSocietyList(page = 1) {
      this.dialog1Visible = true
      this.loading1 = true
      this.societyFormData.page.pageNum = page
      if (page == 1) {
        this.total1 = 0
      }
      this.emptyText = '數據加載中'
      this.$http.post('/ref/society/getSocietyList', this.societyFormData).then(res => {
          this.emptyText = '暫無數據'
          if (res.success) {
            this.societyList = res.data.list
            this.total1 = res.data.total
          }
          this.loading1 = false
        })
    },
    clear() {
      this.$set(this.baseInfo, 'sourceSoc', '')
      this.$set(this.baseInfo, 'sourceCountryCode', '')
      this.$set(this.baseInfo, 'sourceCountryName', '')
    },
    getSocietyByName() {
      if (!this.baseInfo.sourceSocName) {
        this.clear()
        return;
      }
      this.loading1 = true
      let data = {
        page: {
          pageNum: 1,
          pageSize: 50
        },
        data: {
          societyName: this.baseInfo.sourceSocName
        }
      }
      this.$http
        .post('/ref/society/getSocietyList', data)
        .then(res => {
          if (res.success) {
            let list = res.data.list
            if (!list.length) {
              this.clear()
              return this.$toast({ tips: '查詢不到想對應的協會數據，請確認查詢協會名称的準確性！' })
            } else if (list.length == 1) {
              this.$set(this.baseInfo, 'sourceSoc', list[0].societyCode)
              this.$set(this.baseInfo, 'sourceSocName', list[0].societyName)
              this.$set(this.baseInfo, 'sourceCountryCode', list[0].countryCode)
              this.$set(this.baseInfo, 'sourceCountryName', list[0].countryName)
            } else {
              let next = true
              list.forEach(item => {
                console.log(item.societyName)
                if (item.societyName.toUpperCase() == this.baseInfo.sourceSocName.toUpperCase()) {
                  this.$set(this.baseInfo, 'sourceSoc', item.societyCode)
                  this.$set(this.baseInfo, 'sourceSocName', item.societyName)
                  this.$set(this.baseInfo, 'sourceCountryCode', item.countryCode)
                  this.$set(this.baseInfo, 'sourceCountryName', item.countryName)
                  next = false
                }
              })
              if (next) {
                this.clear()
                return this.$toast({ tips: '查詢不到想對應的協會數據，請確認查詢協會名称的完整！' })
              }
            }
            //   this.societyList = res.data.list
            //   this.total1 = res.data.total
          }
          this.loading1 = false
        })
    },
    getSocietyByCode() {

      this.baseInfo.sourceSoc && this.$http
        .get('/ref/society/getSocietyBySocietyCode', { params: { societyCode: this.baseInfo.sourceSoc } })
        .then(res => {
          if (res.data.code == 200) {
            if (res.data.data.refSociety && res.data.data.refSociety.societyName) {
              this.$set(this.baseInfo, 'sourceSocName', res.data.data.refSociety.societyName)
              this.$set(this.baseInfo, 'sourceCountryCode', res.data.data.refSociety.countryCode)
              this.$set(this.baseInfo, 'sourceCountryName', res.data.data.refSociety.countryName)
            } else {
              this.$set(this.baseInfo, 'sourceSocName', '')
            }
          } else {
            this.$toast({ tips: res.data.message })
          }
        })
    },
    handleSocChange(page) {
      this.getSocietyList(page)
    },
    checkSoc(row) {
      // this.getSocietyByName()
      this.dialog1Visible = false
      this.$set(this.baseInfo, 'sourceSoc', row.societyCode)
      this.$set(this.baseInfo, 'sourceSocName', row.societyName)
      this.$set(this.baseInfo, 'sourceCountryCode', row.countryCode)
      this.$set(this.baseInfo, 'sourceCountryName', row.countryName)
    },
    getCountryList(page = 1) {
      this.dialog2Visible = true
      this.loading2 = true
      this.countryFormData.page.pageNum = page
      if (page == 1) {
        this.total2 = 0
      }
      this.emptyText = '數據加載中'
      this.$http.post('/ref/getRefCountry', this.countryFormData).then(res => {
        console.log(res)
        this.emptyText = '暫無數據'
        if (res.success) {
          this.countryList = res.data.list
          this.total2 = res.data.total
        }
        this.loading2 = false
      })
    },
    getCountryByCode() {
      let params = {
        countryCode: this.baseInfo.sourceCountryCode,
        page: {
          pageNum: 0,
          pageSize: 1
        },
        tisN: ''
      }
      this.baseInfo.sourceCountryCode && this.$http
        .post('/ref/getRefCountry', params)
        .then(res => {
          if (res.success) {
            let list = res.data.list
            if (list.length) {
              this.$set(this.baseInfo, 'sourceCountryName', list[0].name)
            }
          }
        })
    },
    checkCountry(row) {
      this.dialog2Visible = false
      this.$set(this.baseInfo, 'sourceCountryCode', row.countryCode)
      this.$set(this.baseInfo, 'sourceCountryName', row.name)
    },
    handleCountryChange(page) {
      this.getCountryList(page)
    },
    getCurrencyList(page = 1) {
      this.dialog3Visible = true
      this.loading3 = true
      this.currencyFormData.page_num = page
      if (page == 1) {
        this.total3 = 0
      }
      this.emptyText = '數據加載中'
      this.$http.get('/ref/getRefCurrency', { params: this.currencyFormData }).then(res => {
          this.emptyText = '暫無數據'
          if (res.success) {
            this.currencyList = res.data.data.list
            this.total3 = res.data.data.total
          }
          this.loading3 = false
        })
    },
    checkCurrency(row) {
      this.dialog3Visible = false
      this.$set(this.baseInfo, 'currencyCode', row.currencyCode)
    },
    handleCurrencyChange(page) {
      this.getCurrencyList(page)
    }
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      if (vm.$route.params.row) {
        vm.baseInfo = JSON.parse(JSON.stringify(vm.$route.params.row))
      }
    })
  }
}
</script>
<style lang="scss" scoped>
/deep/ .el-dialog__title {
  font-weight: bold;
}
/deep/ .el-dialog__body {
  text-align: center;
  padding-top: 0;
}
/deep/ .el-dialog__body td {
  padding: 0;
}
/deep/ .el-select,
.el-switch {
  width: 207px;
}

/deep/ .el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 207px;
}
.download-link {
  margin: 0;
  cursor: pointer;
  &:hover {
    text-decoration: underline;
  }
}
.remark {
  /deep/ .el-form-item {
    width: 100%;
    /deep/ .el-form-item__content {
      width: 52%;
    }
  }
}
</style>
