var inputLimitNumber = function (e) {
    let num = e.target.value || "";
    console.log(e.target.value)
    let code = e.which || e.keyCode;
    let str =
        e.key && e.key != "Unidentified" ? e.key : num.substr(num.length - 1);
    //无论任何情况，皆可执行
    let codeList = [8,9,17,67,86]
    if (codeList.includes(code)) {
        return true;
    }
    //没有满足任何一种情况，中断执行
    if (!(/[\d.]/.test(str) || code == "190")) {
        e.returnValue = false;
        return false;
    }
    if (
        (num.indexOf(".") >= 0 && code == "190") ||
        (num.length == 0 && code == "190")
    ) {
        e.returnValue = false;
        return false;
    }
    return true;
}

export default inputLimitNumber;
