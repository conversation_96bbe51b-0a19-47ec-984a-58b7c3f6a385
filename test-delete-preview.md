# 删除预览功能测试说明

## 功能概述
在文件队列删除功能中增加了删除预览机制，用户在删除前可以查看受影响的数据。

## 修改内容

### 1. 单个删除功能 (deleteFile方法)
- 点击删除按钮时，先调用 `/deleteListFileQueue/{id}/preview` 接口
- 展示删除影响预览弹窗
- 用户确认后执行实际删除

### 2. 批量删除功能 (deleteByList方法)
- 对选中的多个文件ID分别获取预览数据
- 汇总所有影响数据并去重
- 展示批量删除影响预览弹窗
- 用户确认后执行批量删除

### 3. 新增辅助方法

#### getDeletePreview(id)
- 调用预览接口获取单个文件删除影响数据
- 返回Promise，包含影响的ID列表和描述

#### getBatchDeletePreview(ids)
- 为多个文件ID获取预览数据
- 汇总并去重所有影响数据
- 生成批量删除的影响描述

#### showDeleteConfirmDialog(previewData, confirmCallback)
- 展示删除确认弹窗
- 显示影响数据的详细信息，包括：
  - 数据条目数量统计
  - 具体的ID列表（最多显示10个，超出显示省略号和总数）
  - 支持滚动查看长列表
- 用户确认后执行回调函数

## 接口数据格式
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "combinedTotalIds": [1, 2, 3],
    "sampleRuleBaseIds": [10, 11],
    "basicFileBaseIds": [100, 101, 102],
    "impactDescription": "删除此文件队列将会影响： 3 条基础文件记录（软删除）， 3 条合并总表记录（软删除）， 2 条抽样规则记录（软删除）"
  }
}
```

## 测试步骤

### 单个删除测试
1. 在文件列表中点击某个文件的"删除"按钮
2. 应该弹出删除预览弹窗，显示影响数据统计
3. 点击"确定删除"执行实际删除
4. 点击"取消"取消删除操作

### 批量删除测试
1. 选中多个文件
2. 点击"批量删除"按钮
3. 应该弹出批量删除预览弹窗，显示汇总的影响数据
4. 点击"确定删除"执行批量删除
5. 点击"取消"取消删除操作

## 错误处理
- 如果预览接口调用失败，会显示错误提示
- 不会执行实际删除操作，保证数据安全

## 弹窗显示内容

删除确认弹窗现在会显示：
- **删除影响描述**：总体影响说明文本
- **基础文件记录**：
  - 数量统计
  - 具体ID列表（如：1, 2, 3, 4, 5...）
- **合并总表记录**：
  - 数量统计
  - 具体ID列表
- **抽样规则记录**：
  - 数量统计
  - 具体ID列表

### ID显示规则
- 默认最多显示10个ID
- 超过10个时显示省略号和总数，如：`1, 2, 3, 4, 5, 6, 7, 8, 9, 10... (共25条)`
- 支持滚动查看完整内容
- ID之间用逗号和空格分隔

### 弹窗显示示例
```
删除影响预览：

删除此文件队列将会影响： 3 条基础文件记录（软删除）， 3 条合并总表记录（软删除）， 2 条抽样规则记录（软删除）

• 基础文件记录：3 条
  ID: 100, 101, 102

• 合并总表记录：3 条
  ID: 1, 2, 3

• 抽样规则记录：2 条
  ID: 10, 11

確定要刪除嗎？
```

## 用户体验改进
- 用户可以在删除前了解操作的影响范围
- 提供详细的影响数据统计和具体ID
- 支持取消操作，避免误删
- 弹窗支持滚动，适应大量数据显示
