<template>
  <!-- <el-dialog title="指定作品" :visible.sync="selectWork.selectWorkShow" :close-on-click-modal="false"> -->
  <div class="dialog-select-work-2">
    <div style="width: 100%; margin: auto; margin-bottom: 20px">
      <el-input  @keyup.enter.native='onSubmit()' style="width: 140px" placeholder="Title" v-model="selectWork.title"></el-input>
      <el-input  @keyup.enter.native='onSubmit()' style="width: 140px" placeholder="Work No" v-model="selectWork.workId"></el-input>
      <el-input  @keyup.enter.native='onSubmit()' style="width: 60px" placeholder="Soc" v-model="selectWork.soc"></el-input>
      <!-- <el-input  @keyup.enter.native='onSubmit()' style="width: 140px" placeholder="Author" v-model="selectWork.author"></el-input> -->
      <el-input  @keyup.enter.native='onSubmit()' style="width: 200px" placeholder="Composer or Author" v-model="selectWork.authorName"></el-input>
      <el-input  @keyup.enter.native='onSubmit()' style="width: 140px" placeholder="ArtistName" v-model="selectWork.artistName"></el-input>
      <el-button slot="append" icon="el-icon-search" @click="onSubmit()"></el-button>
      <span class="clear-search" @click="clearSearch()">清除搜索</span>
    </div>
    <el-table :empty-text="emptyText3" :data="tableData"
              @row-dblclick="getCurrentWorkInfo"
              @current-change=changeRowClassName
              :row-class-name="rowClassName"
              highlight-current-row>
      <el-table-column property="title" label="title">
        <template slot-scope="scope">
          <span class="over-line" :title="scope.row.title || scope.row.title_en">{{ scope.row.title || scope.row.title_en }}</span>
        </template>
      </el-table-column>
      <el-table-column property="work_id" label="workId"></el-table-column>
      <el-table-column property="work_society_code" label="workSoc"></el-table-column>
      <el-table-column property="genre_code" label="Genre1"></el-table-column>
      <el-table-column property="author" label="author"></el-table-column>
      <el-table-column property="genre_code" label="Composer">
        <template slot-scope="scope">
          <span class="over-line" :title="scope.row.composer && scope.row.composer.join('、')">{{ scope.row.composer && scope.row.composer.join("、") }}</span>
        </template>
      </el-table-column>
      <el-table-column label="operation">
        <template slot-scope="scope">
          <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer;" title="指定作品">
            <i class="el-icon-check" @click="checkedWork(scope.$index, scope.row)"></i>
          </span>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination class="pager" background layout="prev, pager, next" :total="total" @current-change="onSubmit">
    </el-pagination>
  </div>
  <!-- </el-dialog> -->
</template>
<script>
import router1 from '@/router'

export default {
  // 2024-12-06 created
  name: 'select-work-2',
  data() {
    return {
      currentRow:null,
      tableData: [],
      total: null,
      tableresult:' ',
      emptyText3: '暫無數據',
      selectWork: {
        workId: "",
        soc: "",
        title: "",
        author: "",
        composer: "",
        performer: "",
        selectWorkShow: false,
        list: [],
      },
    }
  },
  created() {
    setTimeout(() => {
      // 关闭显示示例代码
      // this.close();
      // 或者可以使用props更新API来关闭弹窗，props更新API支持传递el-dialog官方文档上所有的props参数。
      // this.updateDialog({ visible: false });
    }, 2000)
  },
  methods: {
    clearSearch() {
      for (let item in this.selectWork) {
        this.selectWork[item] = ''
      }
      this.onSubmit()
    },
    onSubmit(page) {
      let params = this.selectWork;
      params.page = {
        pageNum: page ? page : 1,
        pageSize: 10,
      };
      this.currentpage = page
      this.emptyText3 = '數據加載中';
      this.$http.post("/wrk/queryWrkWorkListEs", params).then((res) => {
        if (res.success) {
          this.tableData = res.data.list;
          this.total = res.data.total;
        }
        if(! this.tableData || this.tableData.length == 0){
            this.emptyText3 = '暫無數據';
        }
      });
    },
    getCurrentWorkInfo(row) {
      console.log("%%%%%", row)
      let routeName = '';
      if (row.work_type === 'ARR') {
        routeName = 'works-baseinfoarr';
      } else if (row.work_type === 'ORG') {
        routeName = 'works-baseinfo';
      } else if (row.work_type === 'ADP') {
        routeName = 'works-baseinfoadp';
      } else if (row.work_type === 'AV' && row.genre_code != 'TVS') {
        routeName = 'works-baseinfoav';
      } else if ((row.work_type === 'AV' && row.genre_code === 'TVS') || row.work_type == 'TV') {
        routeName = 'works-baseinfotv';
      } else if (row.work_type == 'ME') {
        routeName = 'works-medleyinfo'
      }
      // r.push({name: routeName, query: {id: row.work_id, socId: row.work_society_code, title: row.title ? row.title : row.title_en, nameId: row.id+row.work_id}, params: {id: row.work_id}})
      //                         let {href} = this.$router.resolve({name:'login'})

//        let { href } = this.$router.resolve({ name: routeName, query: { id: row.work_id, socId: row.work_society_code, title: row.title ? row.title : row.title_en, nameId: row.id + row.work_id }, params: { id: row.work_id } })
        let { href } = router1.resolve({ name: routeName, query: { id: row.work_id, socId: row.work_society_code, title: row.title ? row.title : row.title_en, nameId: row.id + row.work_id }, params: { id: row.work_id } })
      window.open(href, '_blank')
    },
      changeRowClassName(row, rowIndex){
        this.currentRow = row;
      },
      rowClassName({ row }) {
          return row === this.currentRow ? 'highlight-row' : '';
      },
    checkedWork(index, row) {
      this.checkWork(row);
      this.close();
    },
  }
}
</script>
<style lang="scss">
.dialog-select-work-2 {
  height: 665px;
  .pager {
    margin-top: 12px;
  }
}

/* 从根部元素开始调用，确保样式被正确应用 */
.dialog-select-work-2 .el-table__row.highlight-row td {
    background-color: #17b3a3 !important;
    color: #fff !important;
}

</style>
