<template>
    <aside class="site-sidebar" :class="'site-sidebar--' + sidebarLayoutSkin">
        <div class="site-sidebar__inner">
            <!-- <span>{{lang}}</span> -->
            <el-menu
                :default-active="menuActiveName || 'home'"
                :collapse="sidebarFold"
                :collapseTransition="false"
                class="site-sidebar__menu">
                <!-- <el-menu-item index="home" @click="$router.push({ name: 'home' })">
                    <icon-svg name="shouye" class="site-sidebar__menu-icon"></icon-svg> -->
                                <!--<div class="homeimgbox"><img src="../assets/img/home/<USER>"></div>-->
                    <!-- <span class="homespan" slot="title">{{lang.home}}</span>
                </el-menu-item> -->
                <template >
                    <el-submenu  v-for="(item, index) in menuList1" :key="index" :index="item.name"  >
                        <template slot="title">
                            <icon-svg :name="item.icon||'geren'" class="site-sidebar__menu-icon"></icon-svg>
                            <span>{{item.name}}</span>
                        </template>
                        
                        <el-menu-item v-for="(item2, index2) in item.childs" :key="index2" :index="item2.url" @click="openMenu(item2)">
                            <template v-if="item2.type">
                                <icon-svg :name="item2.icon||'geren'" class="site-sidebar__menu-icon"></icon-svg>
                                <span slot="title">{{item2.name}}</span>
                            </template>
                            <template v-if="!item2.type">
                                <template slot="title">
                                    <icon-svg :name="item2.icon||'geren'" class="site-sidebar__menu-icon"></icon-svg>
                                    <span>{{item2.name}}</span>
                                </template>
                                <!-- <el-submenu v-for="(item3, index3) in item2.childs" :key="index3" :index="item3.url" @click="openMenu(item3.path)">
                                    <icon-svg :name="item2.icon||'geren'" class="site-sidebar__menu-icon"></icon-svg>
                                    <span>{{item3.name}}</span>
                                </el-submenu>    -->
                            </template> 
                        </el-menu-item>
                    </el-submenu>
                </template>
                
                
                <!-- <el-submenu index="member">
                    <template slot="title">
                        <icon-svg name="geren" class="site-sidebar__menu-icon"></icon-svg>
                        <span>{{lang.member}}</span>
                    </template>
                    <el-menu-item index="addMember" @click="$router.push({ name: 'member-dummyinfo' })">
                        <icon-svg name="geren" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.addMember}}</span>
                    </el-menu-item>
                    <el-menu-item index="memberList" @click="$router.push({ name: 'member-ipi' })">
                        <icon-svg name="geren" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.memberList}}</span>
                    </el-menu-item>
                    <el-menu-item index="ipTransfer" @click="$router.push({ name: 'member-transfer' })">
                        <icon-svg name="geren" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.ipTransfer}}</span>
                    </el-menu-item>
                </el-submenu>
                <el-submenu index="work">
                    <template slot="title">
                        <icon-svg name="log" class="site-sidebar__menu-icon"></icon-svg>
                        <span>{{lang.work}}</span>
                    </template>
                    <el-menu-item index="work-list" @click="$router.push({ name: 'works-list' })">
                        <icon-svg name="log" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.workList}}</span>
                    </el-menu-item>
                    <el-menu-item index="work-transfer" @click="$router.push({ name: 'works-transfer' })">
                        <icon-svg name="log" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.workTransfer}}</span>
                    </el-menu-item>
                </el-submenu>
                <el-submenu index="removal">
                    <template slot="title">
                        <icon-svg name="log" class="site-sidebar__menu-icon"></icon-svg>
                        <span>{{lang.duplicate}}</span>
                    </template>
                    <el-menu-item index="removalList" @click="$router.push({ name: 'removalListCWR', query: {type: 'cwr'} })">
                        <icon-svg name="log" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.cwrUpload}}</span>
                    </el-menu-item>
                    <el-menu-item index="avrUpload" @click="$router.push({ name: 'removalListAVR', query: {type: 'avr'} })">
                        <icon-svg name="log" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.avrUpload}}</span>
                    </el-menu-item>
                    <el-menu-item index="avrAudit" @click="$router.push({ name: 'removalAuditlist' })">
                        <icon-svg name="log" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.duplicateAuditList}}</span>
                    </el-menu-item>
                </el-submenu>
                <el-submenu index="agreement">
                    <template slot="title">
                        <icon-svg name="editor" class="site-sidebar__menu-icon"></icon-svg>
                        <span>{{lang.agreement}}</span>
                    </template>
                    <el-menu-item index="demo-contract" @click="$router.push({ name: 'contractList'})">
                        <icon-svg name="editor" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.agreementList}}</span>
                    </el-menu-item>
                </el-submenu> -->

                <!-- <el-submenu index="listMange">
                    <template slot="title">
                        <icon-svg name="log" class="site-sidebar__menu-icon"></icon-svg>
                        <span>{{lang.usageList}}</span>
                    </template>
                    <el-submenu index="template-config">
                        <template slot="title">
                            <icon-svg name="log" class="site-sidebar__menu-icon"></icon-svg>
                            <span slot="title">{{lang.templateConfig}}</span>
                        </template>
                        <el-menu-item index="template-config-index" @click="$router.push({ name: 'templateConfig'})">
                            <icon-svg name="log" class="site-sidebar__menu-icon"></icon-svg>
                            <span slot="title">{{lang.templateConfigList}}</span>
                        </el-menu-item> -->
                                    <!--<el-menu-item index="map-config-index" @click="$router.push({ name: 'mapConfig'})">
                                        <icon-svg name="log" class="site-sidebar__menu-icon"></icon-svg>
                                        <span slot="title">{{lang.mapConfigList}}</span>
                                    </el-menu-item>-->
                        <!-- <el-menu-item index="classification-process-index" @click="$router.push({ name: 'classification'})">
                            <icon-svg name="log" class="site-sidebar__menu-icon"></icon-svg>
                            <span slot="title">{{lang.classification}}</span>
                        </el-menu-item>
                    </el-submenu> -->
                    <!-- <el-submenu index="merge">
                        <template slot="title">
                            <icon-svg name="log" class="site-sidebar__menu-icon"></icon-svg>
                            <span slot="title">{{lang.mergeTitle}}</span>
                        </template>
                        <el-menu-item index="template-config-index" @click="$router.push({ name: 'merge'})">
                            <icon-svg name="log" class="site-sidebar__menu-icon"></icon-svg>
                            <span slot="title">{{lang.mergeList}}</span>
                        </el-menu-item>
                    </el-submenu>
                    <el-menu-item index="list-number" @click="$router.push({ name: 'listNumber'})">
                        <icon-svg name="log" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.listNumber}}</span>
                    </el-menu-item>
                    <el-menu-item index="sample-date" @click="$router.push({ name: 'sampleDateList'})">
                        <icon-svg name="log" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.sampleDate}}</span>
                    </el-menu-item>
                    <el-menu-item index="list" @click="$router.push({ name: 'uploadList'})">
                        <icon-svg name="log" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.usageUploadList}}</span>
                    </el-menu-item>
                    <el-menu-item index="auditList" @click="$router.push({ name: 'auditList'})">
                        <icon-svg name="log" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.usageListMatch}}</span>
                    </el-menu-item>
                    <el-menu-item index="singList" @click="$router.push({ name: 'singList'})">
                        <icon-svg name="log" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.singListMatch}}</span>
                    </el-menu-item>



                </el-submenu> -->
                <!-- <el-submenu index="oListMange">
                    <template slot="title">
                        <icon-svg name="log" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.oUsage}}</span>
                    </template>
                    <el-menu-item index="oListMange-index" @click="$router.push({ name: 'oList'})">
                        <icon-svg name="log" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.oUsageList}}</span>
                    </el-menu-item>
                    <el-menu-item index="oListMange-fileList" @click="$router.push({ name: 'bill'})">
                        <icon-svg name="log" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.oUsageFileList}}</span>
                    </el-menu-item>
                    <el-menu-item index="oAuditList" @click="$router.push({ name: 'oAuditList'})">
                        <icon-svg name="log" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.oUsageMatch}}</span>
                    </el-menu-item>
                    <el-menu-item index="oAuditIp" @click="$router.push({ name: 'oAuditIp'})">
                        <icon-svg name="log" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.oAuditIp}}</span>
                    </el-menu-item>
                    <el-menu-item index="oAddData" @click="$router.push({ name: 'oAddData'})">
                        <icon-svg name="log" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.oAddData}}</span>
                    </el-menu-item>
                </el-submenu>
                <el-submenu index="claim">
                    <template slot="title">
                        <icon-svg name="mudedi" class="site-sidebar__menu-icon"></icon-svg>
                        <span>{{lang.claim}}</span>
                    </template>
                    <el-menu-item index="claimlist" @click="$router.push({ name: 'distribute-claimlist' })">
                        <icon-svg name="mudedi" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.claimParameter}}</span>
                    </el-menu-item>
                    <el-menu-item index="report" @click="$router.push({ name: 'distribute-report' })">
                        <icon-svg name="mudedi" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.claimReport}}</span>
                    </el-menu-item>
                    <el-menu-item index="ClaimFilterWork" @click="$router.push({ name: 'ClaimFilterWork' })">
                        <icon-svg name="mudedi" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.ClaimFilterWork}}</span>
                    </el-menu-item>
                    <el-submenu index="claimListMange">
                        <template slot="title">
                            <icon-svg name="log" class="site-sidebar__menu-icon"></icon-svg>
                            <span slot="title">{{lang.claimUsage}}</span>
                        </template>
                        <el-menu-item index="claimUpload" @click="$router.push({ name: 'claimListUpload'})">
                            <icon-svg name="log" class="site-sidebar__menu-icon"></icon-svg>
                            <span slot="title">{{lang.claimUpload}}</span>
                        </el-menu-item>
                        <el-menu-item index="oListMange-index" @click="$router.push({ name: 'claimUploadList'})">
                            <icon-svg name="log" class="site-sidebar__menu-icon"></icon-svg>
                            <span slot="title">{{lang.claimList}}</span>
                        </el-menu-item>
                        <el-menu-item index="claimAuditList" @click="$router.push({ name: 'claimAuditList'})">
                            <icon-svg name="log" class="site-sidebar__menu-icon"></icon-svg>
                            <span slot="title">{{lang.claimMatchList}}</span>
                        </el-menu-item>
                        <el-menu-item index="ClaimHistory" @click="$router.push({ name: 'ClaimHistory'})">
                            <icon-svg name="log" class="site-sidebar__menu-icon"></icon-svg>
                            <span slot="title">{{lang.ClaimHistory}}</span>
                        </el-menu-item>
                        <el-menu-item index="ClaimHistoryLog" @click="$router.push({ name: 'ClaimHistoryLog'})">
                            <icon-svg name="log" class="site-sidebar__menu-icon"></icon-svg>
                            <span slot="title">{{lang.ClaimHistoryLog}}</span>
                        </el-menu-item>
                    </el-submenu>
                </el-submenu>
                <el-submenu index="demo3">
                    <template slot="title">
                        <icon-svg name="mudedi" class="site-sidebar__menu-icon"></icon-svg>
                        <span>{{lang.distribution}}</span>
                    </template>
                    <el-menu-item index="distributionList" @click="$router.push({ name: 'distribute-list' })">
                        <icon-svg name="mudedi" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.distributionList}}</span>
                    </el-menu-item>
                    <el-menu-item index="createDistribution" @click="$router.push({ name: 'distribute-createdistribution' })">
                        <icon-svg name="mudedi" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.createDistribution}}</span>
                    </el-menu-item>
                    <el-submenu index="royalties">
                        <template slot="title">
                            <icon-svg name="editor" class="site-sidebar__menu-icon"></icon-svg>
                            <span>{{lang.royalties}}</span>
                        </template>
                        <el-menu-item index="royaltiesMember" @click="$router.push({ name: 'royalties-member'})">
                            <icon-svg name="editor" class="site-sidebar__menu-icon"></icon-svg>
                            <span slot="title">{{lang.royaltiesMember}}</span>
                        </el-menu-item>
                        <el-menu-item index="royaltiesSociety" @click="$router.push({ name: 'royalties-society'})">
                            <icon-svg name="editor" class="site-sidebar__menu-icon"></icon-svg>
                            <span slot="title">{{lang.royaltiesSociety}}</span>
                        </el-menu-item>
                    </el-submenu>
                    <el-menu-item index="specialCondition" @click="$router.push({ name: 'distribute-condition' })">
                        <icon-svg name="mudedi" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.specialCondition}}</span>
                    </el-menu-item>
                </el-submenu>
                <el-submenu index="adjustment">
                    <template slot="title">
                        <icon-svg name="editor" class="site-sidebar__menu-icon"></icon-svg>
                        <span>{{lang.adjustment}}</span>
                    </template>
                    <el-menu-item index="sdAdjustment" @click="$router.push({ name: 'distribute-sd'})">
                        <icon-svg name="editor" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.sdAdjustment}}</span>
                    </el-menu-item>
                    <el-menu-item index="srAdjustment" @click="$router.push({ name: 'distribute-sr'})">
                        <icon-svg name="editor" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.srAdjustment}}</span>
                    </el-menu-item>
                </el-submenu>
                <el-submenu index="newMedia">
                    <template slot="title">
                        <icon-svg name="mudedi" class="site-sidebar__menu-icon"></icon-svg>
                        <span>{{lang.newMedia}}</span>
                    </template>
                    <el-menu-item index="mediaVersion" @click="$router.push({ name: 'mediaVersion' })">
                        <icon-svg name="mudedi" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.mediaVersion}}</span>
                    </el-menu-item>
                    <el-menu-item index="ipSet" @click="$router.push({ name: 'ipSet' })">
                        <icon-svg name="mudedi" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.publishers}}</span>
                    </el-menu-item>
                    <el-menu-item index="numberSet" @click="$router.push({ name: 'numberSet' })">
                        <icon-svg name="mudedi" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.newMediaDistNo}}</span>
                    </el-menu-item>
                </el-submenu>
                <el-submenu index="Mechanical Right">
                    <template slot="title">
                        <icon-svg name="mudedi" class="site-sidebar__menu-icon"></icon-svg>
                        <span>{{lang.mechanicalRight}}</span>
                    </template>
                    <el-menu-item index="productCategoryList" @click="$router.push({ name: 'productCategoryList' })">
                        <icon-svg name="mudedi" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.producerType}}</span>
                    </el-menu-item>
                    <el-menu-item index="mechanicalList" @click="$router.push({ name: 'mechanicalList' })">
                        <icon-svg name="mudedi" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.producer}}</span>
                    </el-menu-item>
                    <el-menu-item index="resetList" @click="$router.push({ name: 'resetList' })">
                        <icon-svg name="mudedi" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.mecSalesMaintenance}}</span>
                    </el-menu-item>
                    <el-menu-item index="japan" @click="$router.push({ name: 'salesList' })">
                        <icon-svg name="mudedi" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">作品授權</span>
                    </el-menu-item>
                    <el-menu-item index="label" @click="$router.push({ name: 'label' })">
                        <icon-svg name="mudedi" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">Producer Label</span>
                    </el-menu-item>
                </el-submenu>
                <el-submenu index="society">
                    <template slot="title">
                        <icon-svg name="tubiao" class="site-sidebar__menu-icon"></icon-svg>
                        <span>{{lang.society}}</span>
                    </template>
                    <el-menu-item index="societyList" @click="$router.push({ name: 'societyList'})">
                        <icon-svg name="tubiao" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.societyList}}</span>
                    </el-menu-item>
                </el-submenu>

                <el-submenu index="payment">
                    <template slot="title">
                        <icon-svg name="mudedi" class="site-sidebar__menu-icon"></icon-svg>
                        <span>{{lang.payment}}</span>
                    </template>
                    <el-menu-item index="paymentList" @click="$router.push({ name: 'payment-paymentlist'})">
                        <icon-svg name="mudedi" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.paymentList}}</span>
                    </el-menu-item>
                    <el-menu-item index="incomeMember" @click="$router.push({ name: 'incomeMember'})">
                        <icon-svg name="mudedi" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">income Member</span>
                    </el-menu-item>
                    <el-menu-item index="incomeSociety" @click="$router.push({ name: 'incomeSociety'})">
                        <icon-svg name="mudedi" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">income Society</span>
                    </el-menu-item> -->
                                        <!-- <el-menu-item index="income-detail" @click="$router.push({ name: 'incomeDetail'})">
                                            <icon-svg name="mudedi" class="site-sidebar__menu-icon"></icon-svg>
                                            <span slot="title">{{lang.incomeDetail}}</span>
                                        </el-menu-item> -->
                    <!-- <el-menu-item index="salesTaxList" @click="$router.push({ name: 'salesTaxList'})">
                        <icon-svg name="mudedi" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">sales tax manager</span>
                    </el-menu-item>
                </el-submenu> -->
                                    <!-- <el-submenu index="demo6">
                                        <template slot="title">
                                            <icon-svg name="tubiao" class="site-sidebar__menu-icon"></icon-svg>
                                            <span>報表管理</span>
                                        </template>
                                        <el-menu-item index="demo-works" @click="$router.push({ name: 'works-actor'})">
                                            <icon-svg name="tubiao" class="site-sidebar__menu-icon"></icon-svg>
                                            <span slot="title">報表管理</span>
                                        </el-menu-item>
                                    </el-submenu> -->
                                    <!-- <el-submenu index="demo7">
                                        <template slot="title">
                                            <icon-svg name="job" class="site-sidebar__menu-icon"></icon-svg>
                                            <span>定時器任務管理</span>
                                        </template>
                                    </el-submenu> -->
                <!-- <el-submenu index="system">
                    <template slot="title">
                        <icon-svg name="mudedi" class="site-sidebar__menu-icon"></icon-svg>
                        <span>{{lang.system}}</span>
                    </template>
                    <el-menu-item index="role" @click="$router.push({ name: 'role'})">
                        <icon-svg name="mudedi" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.role}}</span>
                    </el-menu-item>
                    <el-menu-item index="user" @click="$router.push({name: 'user'})">
                        <icon-svg name="mudedi" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.user}}</span>
                    </el-menu-item>
                    <el-menu-item index="menu" @click="$router.push({name: 'menu'})">
                        <icon-svg name="mudedi" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.menu}}</span>
                    </el-menu-item>
                </el-submenu> -->
                <!-- <el-submenu index="config">
                    <template slot="title">
                        <icon-svg name="mudedi" class="site-sidebar__menu-icon"></icon-svg>
                        <span>{{lang.maintenance}}</span>
                    </template>
                    <el-menu-item index="category" @click="$router.push({ name: 'parameterCategory'})">
                        <icon-svg name="mudedi" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.category}}</span>
                    </el-menu-item>
                    <el-menu-item index="source" @click="$router.push({name: 'parameterSource'})">
                        <icon-svg name="mudedi" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.source}}</span>
                    </el-menu-item>
                    <el-menu-item index="works-actor" @click="$router.push({ name: 'works-actor'})">
                        <icon-svg name="log" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.performer}}</span>
                    </el-menu-item>
                    <el-menu-item index="convert-pinyin" @click="$router.push({ name: 'convertPinyin'})">
                        <icon-svg name="log" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.convert}}</span>
                    </el-menu-item> -->
                    <!-- 銀行管理 -->
                    <!-- <el-menu-item index="bank" @click="$router.push({ name: 'bankList'})">
                        <icon-svg name="log" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.bank}}</span>
                    </el-menu-item> -->
                                    <!-- <el-menu-item index="branch" @click="$router.push({ name: 'branchList'})">
                                        <icon-svg name="log" class="site-sidebar__menu-icon"></icon-svg>
                                        <span slot="title">{{lang.branch}}</span>
                                    </el-menu-item> -->
                    <!-- 税率管理 -->
                    <!-- <el-menu-item index="rateSoc" @click="$router.push({ name: 'rateSoc'})">
                        <icon-svg name="log" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">特殊稅率配置</span>
                    </el-menu-item> -->
                                    <!-- <el-menu-item index="rateIpi" @click="$router.push({ name: 'rateIpi'})">
                                        <icon-svg name="log" class="site-sidebar__menu-icon"></icon-svg>
                                        <span slot="title">特殊匯率配置（會員）</span>
                                    </el-menu-item> -->
                    <!-- iswc -->
                    <!-- <el-menu-item index="iswc" @click="$router.push({ name: 'iswc'})">
                        <icon-svg name="log" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.iswc}}</span>
                    </el-menu-item>
                    <el-menu-item index="數據字典" @click="$router.push({ name: 'dictionaries'})">
                        <icon-svg name="log" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">數據字典</span>
                    </el-menu-item>
                </el-submenu>
                <el-submenu index="sample DNA">
                    <template slot="title">
                        <icon-svg name="mudedi" class="site-sidebar__menu-icon"></icon-svg>
                        <span>{{lang.sampleDNA}}</span>
                    </template>
                    <el-menu-item index="sampleList" @click="$router.push({ name: 'sampleList'})">
                        <icon-svg name="mudedi" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.sampleList}}</span>
                    </el-menu-item>
                    <el-menu-item index="DNASearch" @click="$router.push({name: 'comparison'})">
                        <icon-svg name="mudedi" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.DNASearch}}</span>
                    </el-menu-item>
                </el-submenu>
                <el-submenu index="export">
                    <template slot="title">
                        <icon-svg name="mudedi" class="site-sidebar__menu-icon"></icon-svg>
                        <span>{{lang.export}}</span>
                    </template>
                    <el-menu-item index="exportDiy" @click="$router.push({ name: 'exportDiy'})">
                        <icon-svg name="mudedi" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.exportDiy}}</span>
                    </el-menu-item>
                    <el-menu-item index="export" @click="$router.push({ name: 'exportReport'})">
                        <icon-svg name="mudedi" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.exportReport}}</span>
                    </el-menu-item>
                </el-submenu>
                 <el-submenu index="management">
                    <template slot="title">
                        <icon-svg name="mudedi" class="site-sidebar__menu-icon"></icon-svg>
                        <span>{{lang.management}}</span>
                    </template>
                    <el-menu-item index="exportDiy" @click="$router.push({ name: 'allTasks'})">
                        <icon-svg name="mudedi" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.allTasks}}</span>
                    </el-menu-item>
                    <el-menu-item index="export" @click="$router.push({ name: 'myTasks'})">
                        <icon-svg name="mudedi" class="site-sidebar__menu-icon"></icon-svg>
                        <span slot="title">{{lang.myTasks}}</span>
                    </el-menu-item>
                </el-submenu>
                <el-menu-item index="home" @click="$router.push({ name: 'setTimeOut' })">
                    <icon-svg name="shouye" class="site-sidebar__menu-icon"></icon-svg>
                    <span class="homespan" slot="title">定時任務</span>
                </el-menu-item> -->
            </el-menu>
        </div>
    </aside>

</template>
<script>
    import en from '@/i18n/lang/en'
    import cn from '@/i18n/lang/cn'
    import SubMenu from './main-sidebar-sub-menu'
    import {isURL} from '@/utils/validate'

    export default {
        data () {
            return {
                // lang: '',
                home: cn.routeNmae.home,
                worksDialogVisible: true,
                dynamicMenuRoutes: [],
                menuList1:[]
            }
        },
        components: {
            SubMenu
        },
        computed: {
            lang: {
                get () {
                    return this.$store.state.lang.lang.menuName;
                }
            },
            sidebarLayoutSkin: {
                get () {
                    return this.$store.state.common.sidebarLayoutSkin
                }
            },
            sidebarFold: {
                get () {
                    return this.$store.state.common.sidebarFold
                }
            },
            menuList: {
                get () {
                    return this.$store.state.common.menuList
                },
                set (val) {
                    this.$store.commit('common/updateMenuList', val)
                }
            },
            menuActiveName: {
                get () {
                    return this.$store.state.common.menuActiveName
                },
                set (val) {
                    this.$store.commit('common/updateMenuActiveName', val)
                }
            },
            mainTabs: {
                get () {
                    return this.$store.state.common.mainTabs
                },
                set (val) {
                    this.$store.commit('common/updateMainTabs', val)
                }
            },
            mainTabsActiveName: {
                get () {
                    return this.$store.state.common.mainTabsActiveName
                },
                set (val) {
                    this.$store.commit('common/updateMainTabsActiveName', val)
                }
            },
            selectLanguage: {
                get () {
                    if (this.$store.state.lang.language === 'cn') {
                        this.home = cn.routeNmae.home
                    }
                    if (this.$store.state.lang.language === 'en') {
                        this.home = en.routeNmae.home
                    }
                    return this.$store.state.lang.language
                }
            }
        },
        watch: {
            $route: 'routeHandle'
        },
        created () {
            // this.menuList = JSON.parse(sessionStorage.getItem('menuList') || '[]')
            this.dynamicMenuRoutes = JSON.parse(sessionStorage.getItem('dynamicMenuRoutes') || '[]')
            this.routeHandle(this.$route)
            this.$http.get('/sys/menus/nav', ).then(res => {
                console.log(res)
                if (res.status == 200) {
                    this.menuList1=res.data.data.menuList
                }
            })
        },
        methods: {
            openMenu(item){
                // console.log('==',url)
                // let path = url.slice(url.indexOf('/'))
                // console.log(path)
                this.$router.push({path:item.path,query:{id:item.id}})
            },
            // 路由操作
            routeHandle (route) {
                if(route.name == 'removalListAVR'){
                    route.query.type = 'AVR'
                }if(route.name == 'removalListCWR'){
                    route.query.type = 'cwr'
                }
                if (route.meta.isTab) {
                    let title = route.meta.title
                    let name = route.name
                    let extraName = route.name
                    this.mainTabsActiveName = route.name

                    // 編輯作品
                    // if 里的routename 為特殊的，需要在title里显示某些特殊内容
                    /** guan
                     * title 為显示的title
                     * name 為路由的名字
                     * extraName 為el-tabs 识別的唯一id，所以保证唯一
                     * mainTabsActiveName 設置当前的tab頁，用extraName 识別
                     *
                     * 参數传递：
                     * 跳转路由时： nameId 需要在query里传以生成extraName
                     *             title  需要在query里传以在title里展示編輯的对象名稱
                     * 其他字段正常传就行了
                     */
                    let routeArr = ['distribute-claim','distribute-editcopy','distribute-editwork','distribute-editip','distribute-editsoc','member-info','member-info2','societyEdit','editCategory','resetEdit','resetEditList','orderEdit','distribute', 'contractInfo', 'distribute-info', 'removalAudit','oListStep1',
                        'singListaudit','templateConfigDetail','fileList','fileDetail', 'editDiy','editSqlDiy','oListStep3','exportDetail', 'salesEdit','editActor','rel-list','transferActor','royalties-member','royalties-society','contractInfo2']
                    if (
                            (route.name.indexOf('works-base') != -1)
                            || routeArr.includes(route.name)
                        ) {
                        title = route.meta.title + (route.query.title ? ':' : '') + (route.query.title ? route.query.title : '')
                        name = route.name
                        extraName = route.name + route.query.nameId
                        this.mainTabsActiveName = extraName
                    }

                    // tab选中, 不存在先添加
                    var tab = this.mainTabs.filter(item => item.name === name && item.extraName === extraName)[0]
                    if (!tab || !tab.name) {
                        if (route.meta.isDynamic) {
                            route = this.dynamicMenuRoutes.filter(item => item.name === route.name)[0]
                            if (!route) {
                                return console.error('未能找到可用标签頁!')
                            }
                        }

                        tab = {
                            menuId: route.meta.menuId || route.name,
                            name: name,
                            title: title,
                            type: isURL(route.meta.iframeUrl) ? 'iframe' : 'module',
                            iframeUrl: route.meta.iframeUrl || '',
                            params: route.params,
                            query: route.query,
                            extraName: extraName,
                        }
                        this.mainTabs = this.mainTabs.concat(tab)
                    }
                    this.menuActiveName = tab.menuId + ''
                }
            },
            addworks () {
                this.$emit('sendFlag', true)
            },
            selectUpload () {
                this.$emit('sendUploadFlag', true)
            }
        }
    }
</script>
<style>
    .selectbox {
        width: 200px;
        position: absolute;
        top: 200px;
        left: 300px;
        background-color: #FFFFFF;
    }

    /* .el-menu-item {
       text-align: center;
       height: 100px;
       position: relative;
     }*/
    /*.el-submenu{
      text-align: center;
      height: 100px;
      position: relative;
    }*/

    /* .el-tooltip{
       width: 50px;
     }*/
    /* .homeimgbox{
       width: 100%;
       height: 56px!important;
       margin: auto;
     }
     .homeimgbox img{
       height: 50px;
       margin-left: -15px;
     }
     .homespan{
       position: absolute;
       top:50px;
       left: 95px;

     }*/
    .el-submenu__title {
        color: #BFCBD9;
    }

    .site-sidebar__menu, .el-menu--inline {
        background: #324157;
    }

    .el-menu-item {
        color: #BFCBD9;
    }

    .el-submenu__title:hover > span, .el-submenu__title:hover > svg {
        color: #324157 !important;
    }

    .el-menu-item:hover > span, .el-menu-item:hover > svg {
        color: #324157 !important;
    }

    /* .el-submenu:hover span{
        color:#fff !important;
    } */
    /* li:hover span{
        color:#fff !important;

    } */

</style>






























