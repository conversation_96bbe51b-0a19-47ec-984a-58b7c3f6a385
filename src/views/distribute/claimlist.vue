<template>
  <div>
    <el-form :inline="true" :model="searchForm" class="demo-form-inline" @keyup.enter.native="getClaimList(1)">
      <el-form-item label="Company">
        <el-input v-model.trim="searchForm.company" placeholder=""></el-input>
      </el-form-item>
      <el-form-item label="status">
        <el-select v-model="searchForm.status" placeholder="status" style="width:120px">
          <el-option label="全部" value=""></el-option>
          <el-option label="啟用" value="1"></el-option>
          <el-option label="禁用" value="2"></el-option>
        </el-select>
      </el-form-item>
      <!--<el-form-item label="type">
                <el-select v-model="searchForm.type" placeholder="type" style="width:120px">
                    <el-option label="全部" value=""></el-option>
                    <el-option label="公傳" value="gc"></el-option>
                    <el-option label="重製" value="cz"></el-option>
                </el-select>
            </el-form-item>-->
      <el-form-item>
        <el-button type="primary" @click="getClaimList(1)" v-if="isAuth('distribute:claimlist:find')">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="success" @click="addClaim" v-if="isAuth('distribute:claimlist:add')">新 增</el-button>
      </el-form-item>
      <el-form-item>
        <span class="clear-search" @click="clearSearch()" v-if="isAuth('distribute:claimlist:find')">清除搜索</span>
      </el-form-item>
    </el-form>
    <el-table :empty-text="emptyText" :data="tableData" border stripe style="width: 100%" v-loading="loading">
      <el-table-column prop="id" label="Id"></el-table-column>
      <el-table-column prop="company" label="company"></el-table-column>
      <el-table-column prop="date" label="重製 Authorization time" min-width="110">
        <template slot-scope="scope">
          <span>{{scope.row.mechanicalStartTime.replace(/-/g,'/')}}</span>
          <span v-if="scope.row.mechanicalStartTime">~</span>
          <span>{{scope.row.mechanicalEndTime.replace(/-/g,'/')}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="date" label="公傳授權時間" min-width="110">
        <template slot-scope="scope">
          <span>{{scope.row.publicTransmissionStartTime.replace(/-/g,'/')}}</span>
          <span v-if="scope.row.publicTransmissionStartTime">~</span>
          <span>{{scope.row.publicTransmissionEndTime.replace(/-/g,'/')}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="desc">
      </el-table-column>
      <el-table-column prop="status" label="status">
        <template slot-scope="scope">
          <span v-if="scope.row.status === 1">啟用</span>
          <span v-else>禁用</span>
        </template>
      </el-table-column>
      <el-table-column label="operation">
        <template slot-scope="scope">
          <el-button @click="handleClickEdit(scope.row,'edit')" type="text" size="small"  v-if="isAuth('distribute:claimlist:change')">編 輯</el-button>
          <el-button @click="showReportDialog(scope.row)" type="text" size="small" v-if="isAuth('distribute:claimlist:report')">生成報告</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination background layout="prev, pager, next" :total='claimTotal' :current-page="searchForm.page_num" @current-change="handleCurrentdetails">
    </el-pagination>

    <!--生成報告編輯弹窗-->
    <el-dialog title="生成報告" :visible.sync="showReport" width="50%" :close-on-click-modal="false">
      <el-form label-position="right" label-width="140px" :model="createReportData" class="demo-form-inline">
        <el-form-item label="Company">
          <el-input v-model="createReportData.company" placeholder="輸入聯想查詢"></el-input>
        </el-form-item>
        <el-form-item label="claim date">
          <el-date-picker v-model="dateRange" type="daterange" range-separator="-" start-placeholder="start" value-format="yyyy-MM-dd" end-placeholder="end">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="type">
          <el-radio-group v-model="createReportData.ccidNo">
            <el-radio label="ccid v13">ccid v13</el-radio>
            <el-radio label="ccid v14">ccid v14</el-radio>
            <el-radio label="ccr">ccr</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="product">
          <el-select v-model="products" multiple placeholder="請選擇">
            <el-option v-for="item in productList" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
          <el-checkbox v-model="createReportData.isPart">根據product拆分輸出</el-checkbox>
        </el-form-item>
        <el-form-item label="output path">
          <el-input v-model="createReportData.filePath" placeholder="輸入聯想查詢"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="createReport">生成</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import axios from '../../utils/httpRequest'

export default {
  name: 'claimlist',
  data() {
    return {
      emptyText: '暫無數據',
      valueDate: '',
      claimTotal: 10,
      searchForm: {
        company: '',
        endTime: '',
        startTime: '',
        page_num: 1,
        page_size: '',
        type: '',
        status: ''
      },
      tableData: [],
      createReportData: {},
      showReport: false,
      productList: [],
      products: [],
      dateRange: [],
      loading: false,
    }
  },
  computed: {

    startTime() {
      if (this.valueDate) {
        let date = this.valueDate[0]
        let year = date.getFullYear()
        let month = date.getMonth() + 1
        let day = date.getDate()
        let startValue = `${year}-${month}-${day} 00:00:00`
        return startValue
      } else {
        return '1990-01-01 00:00:00'
      }

    },
    endTime() {
      if (this.valueDate) {
        let date = this.valueDate[1]
        let year = date.getFullYear()
        let month = date.getMonth() + 1
        let day = date.getDate()
        let startValue = `${year}-${month}-${day} 00:00:00`
        return startValue
      } else {
        return '2100-12-31 23:59:59'
      }
    }
  },
  methods: {
    clearSearch() {
      this.searchForm = {
        company: '',
        endTime: '',
        startTime: '',
        page_num: 1,
        page_size: '',
        type: '',
        status: ''
      }
      this.getClaimList()
    },
    getProducts() {
      let params = {
        setId: this.createReportData.claimSetId
      }
      this.$http.get('/claim/minima/product', { params }).then(res => {
        console.log(res)
        if (res.status === 200) {
          res.data.map(item => {
            this.productList.push({
              label: item,
              value: item
            })
          })
        }
      })
    },
    showReportDialog(row) {
      this.createReportData = { ccidNo: 'ccid v13', isPart: 0 }
      this.createReportData.company = row.company
      this.createReportData.claimSetId = row.id
      // this.getProducts()
      this.$router.push({ name: 'distribute-createreport', query: { data: JSON.stringify(this.createReportData) } })
    },
    createReport() {
      let products = this.products.join(',')
      this.createReportData.products = products
      this.createReportData.startDate = this.dateRange[0]
      this.createReportData.endDate = this.dateRange[1]
      console.log(this.createReportData)
      this.$http.post('/claim/ccid/header/if', this.createReportData).then(res => {
        console.log(res)
        if (!res.data) {
          return this.$http.post('/claim/ccid/header', this.createReportData)
        } else {
          this.$alert('報告已經生成，是否重新生成', '溫馨提示', {
            confirmButtonText: '確定',
            showCancelButton: true,
            callback: action => {
              console.log(action)
              if (action === 'confirm') {
                return this.$http.post('/claim/ccid/header', this.createReportData)
              }
            }
          })
        }
      }).then((res) => {
        console.log(res)
        if (res.status === 200) {
          this.showReport = false
          let data = {
            company: this.createReportData.company,
            product: this.createReportData.products
          }
          this.$router.push({ name: 'distribute-report', params: { data } })
        }
      })

    },
    handleClickEdit(item, type) {
      this.$router.push({ name: 'distribute-claim', query: { item: JSON.stringify(item), nameId: item.id, title: item.company } })
    },
    addClaim() {
      let nowDate = new Date()
      let year = nowDate.getFullYear()
      let month = nowDate.getMonth() + 1
      let day = nowDate.getDate()
      let item = {
        company: '',
        mechanicalStartTime: '',
        mechanicalEndTime: '',
        publicTransmissionStartTime: '',
        publicTransmissionEndTime: '',
        status: 1
      }
      this.handleClickEdit(item, 'add')
    },
    savelist(item) {
      axios.post('/claim/set', item).then(res => {
        if (res.status === 200) {
        }
      })
    },
    handleCurrentdetails(val) {
      this.searchForm.page_num = val
      this.getClaimList()
    },
    getClaimList(page) {
        if (page==1||this.searchForm.company||this.searchForm.status) {
          this.searchForm.page_num=1
        }
      let params = this.searchForm
      params.startTime = this.startTime
      params.endTime = this.endTime
      this.loading = true
      this.emptyText = '數據加載中';
      axios.get('/claim', { params }).then(res => {
        this.loading = false
        if (res.status === 200) {
          res.data.list.map(item => {
            item.mechanicalStartTime && (item.mechanicalStartTime = item.mechanicalStartTime.replace('00:00:00',''))
            item.mechanicalEndTime && (item.mechanicalEndTime = item.mechanicalEndTime.replace('00:00:00', ''))
            item.publicTransmissionStartTime && (item.publicTransmissionStartTime = item.publicTransmissionStartTime.replace('00:00:00', ''))
            item.publicTransmissionEndTime && (item.publicTransmissionEndTime = item.publicTransmissionEndTime.replace('00:00:00', ''))
          })
          this.tableData = res.data.list
          if(! this.tableData || this.tableData.length == 0){
            this.emptyText = '暫無數據';
          }
          this.claimTotal = res.data.total
        }
      })
    }
  },
  activated() {
    this.getClaimList()
    // this.getButtonMenu()
  }
}
</script>

<style scoped>
/deep/ .el-form-item__label {
  margin-left: 0 !important;
}
</style>
