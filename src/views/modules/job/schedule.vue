<template>
    <div class="mod-schedule">
        <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
            <el-form-item>
                <el-input v-model="dataForm.name" placeholder="name" clearable></el-input>
            </el-form-item>
            <el-form-item>
                <el-input v-model="dataForm.nameEn" placeholder="nameEn" clearable></el-input>
            </el-form-item>
            <el-form-item>
                <el-input v-model="dataForm.beanName" placeholder="bean名稱" clearable></el-input>
            </el-form-item>
            <el-form-item label="status">
                <!--<el-switch
                    v-model="dataForm.status"
                    :active-value="1"
                    :inactive-value="0"
                    active-color="#13ce66"
                    inactive-color="#e9e9e9">
                </el-switch>-->
                <el-select v-model="dataForm.status" placeholder="請選擇">
                    <el-option label="全部" value=""></el-option>
                    <el-option label="正常" value="1"></el-option>
                    <el-option label="暫停" value="0"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="getDataList(1)">搜索</el-button>
                <el-button type="success" @click="addOrUpdateHandle()">新增</el-button>
                <el-button type="danger" @click="deleteHandleAll" :disabled="dataListSelections.length <= 0">刪除
                </el-button>
                <el-button type="danger" @click="pauseHandleAll" :disabled="dataListSelections.length <= 0">暫停
                </el-button>
                <el-button type="danger" @click="resumeHandleAll" :disabled="dataListSelections.length <= 0">恢復
                </el-button>
                <el-button type="danger" @click="runHandleAll" :disabled="dataListSelections.length <= 0">立即執行
                </el-button>
                <el-button type="success" @click="logHandle">日誌列表</el-button>
            </el-form-item>
        </el-form>
        <el-table
            :data="dataList"
            border
            v-loading="dataListLoading"
            @selection-change="selectionChangeHandle"
            style="width: 100%;">
            <el-table-column
                type="selection"
                header-align="center"
                align="center"
                width="50">
            </el-table-column>
            <el-table-column
                prop="id"
                header-align="center"
                align="center"
                label="ID">
            </el-table-column>
            <el-table-column
                prop="name"
                header-align="center"
                align="center"
                label="name">
            </el-table-column>
            <el-table-column
                prop="nameEn"
                header-align="center"
                align="center"
                label="nameEn">
            </el-table-column>
            <el-table-column
                prop="beanName"
                header-align="center"
                align="center"
                label="bean名稱">
            </el-table-column>
            <el-table-column
                prop="cronExpression"
                header-align="center"
                align="center"
                label="cron表达式">
                <template slot-scope="scope">
                    <span :title="scope.row.cronExpression">{{scope.row.cronExpression}}</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="amendTime"
                header-align="center"
                align="center"
                label="amend time">
            </el-table-column>
            <el-table-column
                prop="status"
                header-align="center"
                align="center"
                label="狀態">
                <template slot-scope="scope">
                    <el-tag v-if="scope.row.status === 1" size="small">正常</el-tag>
                    <el-tag v-else size="small" type="danger">暫停</el-tag>
                </template>
            </el-table-column>
            <el-table-column
                fixed="right"
                header-align="center"
                align="center"
                width="150"
                label="操作">
                <template slot-scope="scope">
                    <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row)">修改</el-button>
                    <el-button type="text" size="small" @click="deleteHandle(scope.row)">刪除</el-button>
                    <el-button type="text" v-if="scope.row.status == 1" size="small" @click="pauseHandle(scope.row)">暫停</el-button>
                    <el-button type="text" v-if="scope.row.status == 0" size="small" @click="resumeHandle(scope.row)">恢復</el-button>
                    <el-button type="text" v-if="scope.row.status == 0" size="small" @click="runHandle(scope.row)">立即執行</el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            @size-change="sizeChangeHandle"
            @current-change="currentChangeHandle"
            :current-page="pageIndex"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pageSize"
            :total="totalPage"
            layout="total, sizes, prev, pager, next, jumper">
        </el-pagination>
        <!-- 弹窗, 新增 / 修改 -->
        <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList" @hideLog="hideAddOrUpdate"></add-or-update>
        <!-- 弹窗, 日志列表 -->
        <log v-if="logVisible" ref="log"></log>
    </div>
</template>

<script>
    import AddOrUpdate from './schedule-add-or-update'
    import Log from './schedule-log'

    export default {
        data () {
            return {
                dataForm: {
                    beanName: '',
                    name:'',
                    nameEn:'',
                    status:'',
                    page_num:1,
                    page_size:10
                },
                dataList: [],
                pageIndex: 1,
                pageSize: 10,
                totalPage: 0,
                dataListLoading: false,
                dataListSelections: [],
                addOrUpdateVisible: false,
                logVisible: false
            }
        },
        components: {
            AddOrUpdate,
            Log
        },
        mounted () {
            this.getDataList(1)
        },
        methods: {
            // 获取数据列表
            getDataList (page) {
                this.pageIndex = this.dataForm.page_num = page
                this.dataListLoading = true
                this.$http.get('/sys/job/list', {params:this.dataForm}).then(res => {
                    console.log(res)
                    if (res.success) {
                        this.dataListLoading = false
                        this.dataList = res.data.data.list
                        this.totalPage = res.data.data.total
                    }
                })
            },
            // 每页数
            sizeChangeHandle (val) {
                this.dataForm.page_size = val
                this.dataForm.page_num = 1
                this.getDataList()
            },
            // 当前页
            currentChangeHandle (val) {
                this.getDataList(val)
            },
            // 多选
            selectionChangeHandle (val) {
                console.log(val)
                this.dataListSelections = val
            },
            // 新增 / 修改
            addOrUpdateHandle (row) {
                this.addOrUpdateVisible = true
                this.$nextTick(() => {
                    this.$refs.addOrUpdate.init(row)
                })
            },
            //隐藏新增
            hideAddOrUpdate(){
                this.addOrUpdateVisible = false
            },
            // 删除
            deleteHandle (row) {
                this.$msgbox.confirm(`確定对[name=${row.name}]进行[${row.name ? '删除' : '批量删除'}]操作?`, '提示', {
                    confirmButtonText: '確定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$http.delete('/sys/job/delete/' + row.id).then(({data}) => {
                        console.log(data)
                        if (data && data.code === 200) {
                            this.$message({
                                message: '操作成功',
                                type: 'success',
                                duration: 1500,
                                onClose: () => {
                                    this.getDataList()
                                }
                            })
                        } else {
                            this.$message.error(data.message)
                        }
                    })
                }).catch(() => {
                })
            },
            getIds(){
                let ids = []
                this.dataListSelections.map(item => {
                    ids.push(item.id)
                })
                return ids
            },
            getNames(){
                let names = []
                this.dataListSelections.map(item => {
                    names.push(item.name)
                })
                return names
            },
            deleteHandleAll(){
                let ids = this.getIds()
                let names = this.getNames()
                this.$msgbox.confirm(`確定对[name=${names}]进行['批量删除'}]操作?`, '提示', {
                    confirmButtonText: '確定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$http.delete('/sys/job/deletes',{data:ids}).then(({data}) => {
                        if (data && data.code === 200) {
                            this.$message({
                                message: '操作成功',
                                type: 'success',
                                duration: 1500,
                                onClose: () => {
                                    this.getDataList()
                                }
                            })
                        } else {
                            this.$message.error(data.message)
                        }
                    })
                }).catch(() => {
                })

            },
            pauseHandleAll(){
                let ids = this.getIds()
                let names = this.getNames()
                this.$msgbox.confirm(`確定对[name=${names}]进行['批量暂停'}]操作?`, '提示', {
                    confirmButtonText: '確定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$http.post('/sys/job/pauses',ids).then(({data}) => {
                        if (data && data.code === 200) {
                            this.$message({
                                message: '操作成功',
                                type: 'success',
                                duration: 1500,
                                onClose: () => {
                                    this.getDataList()
                                }
                            })
                        } else {
                            this.$message.error(data.message)
                        }
                    })
                }).catch(() => {
                })
            },
            resumeHandleAll(){
                let ids = this.getIds()
                let names = this.getNames()
                this.$msgbox.confirm(`確定对[name=${names}]进行['批量恢复'}]操作?`, '提示', {
                    confirmButtonText: '確定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$http.post('/sys/job/starts',ids).then(({data}) => {
                        if (data && data.code === 200) {
                            this.$message({
                                message: '操作成功',
                                type: 'success',
                                duration: 1500,
                                onClose: () => {
                                    this.getDataList()
                                }
                            })
                        } else {
                            this.$message.error(data.message)
                        }
                    })
                }).catch(() => {
                })

            },
            runHandleAll(){
                let ids = this.getIds()
                let names = this.getNames()
                this.$msgbox.confirm(`確定对[name=${names}]进行['批量立即执行'}]操作?`, '提示', {
                    confirmButtonText: '確定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$http.post('/sys/job/executes',ids).then(({data}) => {
                        if (data && data.code === 200) {
                            this.$message({
                                message: '操作成功',
                                type: 'success',
                                duration: 1500,
                                onClose: () => {
                                    this.getDataList(this.dataForm.page_num)
                                }
                            })
                        } else {
                            this.$message.error(data.message)
                        }
                    })
                }).catch(() => {
                })
            },
            // 暂停
            pauseHandle (row) {
                this.$msgbox.confirm(`確定对[name=${row.name}]进行[${row.name ? '暂停' : '批量暂停'}]操作?`, '提示', {
                    confirmButtonText: '確定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$http.post('/sys/job/pause/' + row.id).then(({data}) => {
                        console.log(data)
                        if (data && data.code === 200) {
                            this.$message({
                                message: '操作成功',
                                type: 'success',
                                duration: 1500,
                                onClose: () => {
                                    this.getDataList(this.dataForm.page_num)
                                }
                            })
                        } else {
                            this.$message.error(data.message)
                        }
                    })
                }).catch(() => {
                })
            },
            // 恢复
            resumeHandle (row) {
                this.$msgbox.confirm(`確定对[name=${row.name}]进行[${row.name ? '恢复' : '批量恢复'}]操作?`, '提示', {
                    confirmButtonText: '確定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$http.post('/sys/job/start/' + row.id).then(({data}) => {
                        if (data && data.code === 200) {
                            this.$message({
                                message: '操作成功',
                                type: 'success',
                                duration: 1500,
                                onClose: () => {
                                    this.getDataList(this.dataForm.page_num)
                                }
                            })
                        } else {
                            this.$message.error(data.message)
                        }
                    })
                }).catch(() => {
                })
            },
            // 立即执行
            runHandle (row) {
                this.$msgbox.confirm(`確定对[name=${row.name}]进行[${row.name ? '立即执行' : '批量立即执行'}]操作?`, '提示', {
                    confirmButtonText: '確定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$http.post('/sys/job/execute/' + row.id).then(({data}) => {
                        console.log(data)
                        if (data && data.code === 200) {
                            this.$message({
                                message: '操作成功',
                                type: 'success',
                                duration: 1500,
                                onClose: () => {
                                    this.getDataList(this.dataForm.page_num)
                                }
                            })
                        } else {
                            this.$message.error(data.message)
                        }
                    })
                }).catch(() => {
                })
            },
            // 日志列表
            logHandle () {
                this.logVisible = true
                this.$nextTick(() => {
                    this.$refs.log.init()
                })
            }
        }
    }
</script>

<style scoped lang="scss">
    /deep/ .el-form-item__label {
        margin-left: 0 !important;
    }
    /deep/ .el-button+.el-button{
        margin-left: 0;
    }
    /deep/ .el-button--danger.is-disabled{
        background-color: #c0c4cc;
        border-color: #c0c4cc;
    }
</style>
