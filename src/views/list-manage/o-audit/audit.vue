<template>
  <div v-loading='loading'>
    <h3 class="title">Match Work List</h3>
    <el-table
      :empty-text="emptyText"
      :data="matchTable"
      border
      stripe
      @row-click="handleRowClick"
      @row-dblclick="workDrawer"
      highlight-current-row
      ref="matchTableRef"
      class="highlight-row-table match-table">
      <!-- 添加新的列来显示五角星 -->
    <el-table-column label="" width="33">
      <template slot-scope="scope">
        <i v-if="scope.row.workUniqueKey === fileInfo.matchWorkUniqueKey" class="el-icon-star-on" style="color: red;"></i>
      </template>
    </el-table-column>
      <el-table-column prop="matchWorkId" label="WorkNo"> </el-table-column>
      <el-table-column prop="matchWorkSocietyCode" label="WorkSoc">
      </el-table-column>
      <el-table-column prop="matchTitle" label="WorkTitle"> </el-table-column>
      <el-table-column prop="matchWorkGenre" label="genre"> </el-table-column>
      <el-table-column prop="matchScore" label="score"> </el-table-column>
    </el-table>
    <el-collapse v-model="activeNames" class="clear" style="border-bottom: 0">
      <el-collapse-item class="step-jump" title="File Work Info" name="1" :disabled="true" style="width: 40%; float: left">
        <div class="boxline p-t-10 part1">
          <el-form label-position="left" label-width="90px">
            <div>
              <el-form-item label="WorkNum">
                <el-input v-model="fileInfo.sourceWorkCode" readonly></el-input>
              </el-form-item>
              <el-form-item label="WorkSoc">
                <el-input v-model="fileInfo.remitSociety" readonly></el-input>
              </el-form-item>
              <el-form-item label="WorkTitle" v-if="fileInfo.originalTitle">
                <el-input v-model="fileInfo.originalTitle" readonly></el-input>
              </el-form-item>
              <el-form-item label="WorkTitle" v-if="!fileInfo.originalTitle">
                <el-input v-model="fileInfo.matchWorkTitle" readonly></el-input>
              </el-form-item>
              <el-form-item label="Composer">
                <el-input v-model="fileInfo.authorComposer" readonly></el-input>
              </el-form-item>
              <el-form-item label="ISWC">
                <el-input v-model="fileInfo.iswc" readonly></el-input>
              </el-form-item>
              <el-form-item label="ISRC">
                <el-input v-model="fileInfo.isrc" readonly></el-input>
              </el-form-item>
              <el-form-item label="">
                <el-button type="primary" @click="viewSampleDetail(fileInfo)">查看合併清單明細</el-button>
              </el-form-item>
            </div>
          </el-form>
        </div>
      </el-collapse-item>
      <el-collapse-item class="step-jump table2" title="Match Work Info" name="2" :disabled="true" style="width: 56%; float: right">
        <div class="boxline" v-loading="matchWorkInfoLoading" element-loading-text="數據加載中" element-loading-background="rgba(255, 255, 255, 0.8)">
          <el-form class="match-info" :inline="true" label-position="left" label-width="90px">
            <div v-if="listType != 'pg'">
              <!-- <el-form-item label="Composer">
                <el-input v-model="matchInfo.matchComposers" style="width: 320px" readonly></el-input>
              </el-form-item> -->
              <!-- <el-form-item label="Author">
                <el-input v-model="matchInfo.matchAuthors" style="width: 320px" readonly></el-input>
              </el-form-item> -->
              <el-form-item label="ISWC">
                <el-input v-model.number="matchInfo.matchIswc" style="width: 320px" readonly></el-input>
              </el-form-item>
            </div>
            <div v-else>
              <el-form-item label="Eposide No" label-width="110px">
                <el-input v-model="matchInfo.episode_no" style="width: 350px" readonly></el-input>
              </el-form-item>
            </div>
            <div v-if="listType != 'pg'" class="clear">
              <div class="f-l match-work-info-scroll" style="
                  width: 40%;
                  margin-right: 4%;
                  padding-left: 20px;
                  box-sizing: border-box;
                ">
                <div>Performer</div>
                <el-form-item>
                  <ul class="list" style="width: 190px">
                    <li class="el-sl" v-for="(item, index) in matchInfo.matchArtists" :key="index">
                      {{ item }}
                    </li>
                    <li v-if="
                        !matchInfo.matchArtists ||
                        matchInfo.matchArtists.length == 0
                      ">
                      暂無數據
                    </li>
                  </ul>
                </el-form-item>
              </div>
              <div class="f-l match-work-info-scroll" style="width: 51%">
                <div>ISRC</div>
                <el-form-item>
                  <ul class="list" style="width: 190px">
                    <li class="el-sl" v-for="(item, index) in matchInfo.matchIsrc" :key="index">
                      {{ item }}
                    </li>
                    <li v-if="
                        !matchInfo.matchIsrc || matchInfo.matchIsrc.length == 0
                      ">
                      暂無數據
                    </li>
                  </ul>
                </el-form-item>
              </div>
            </div>
            <div class="component" v-else>
              <el-form-item label="Component">
                <el-table :empty-text="emptyText1" stripe :data="matchInfo.componentList" border>
                  <!-- todo-guan 等周兵接口添加字段後，確認字段 -->
                  <el-table-column prop="title" label="Title">
                  </el-table-column>
                  <el-table-column prop="componentWorkId" label="Work No" width="120px">
                  </el-table-column>
                  <el-table-column prop="comWorkSociety" label="WorkSoc" width="90px">
                  </el-table-column>
                  <!-- todo-guan 等周兵接口添加字段後，確認字段 -->
                  <el-table-column prop="genre" label="Genre" width="70px">
                  </el-table-column>
                  <el-table-column prop="usageType" label="Usage" width="70px">
                  </el-table-column>
                </el-table>
              </el-form-item>
            </div>
          </el-form>
          <h3 class="title" style="margin-bottom: 0">Match Work Ip Share</h3>
          <div v-loading="ipShareLoading">
            <el-table :empty-text="emptyText2" stripe :data="matchWorkIpShareInfo" border height="141px">
            <el-table-column prop="ipSocietyCode" label="ip_Soc" width="79px">
            </el-table-column>
            <el-table-column prop="ipNameNo" label="Ip_name_no" width="140px">
            </el-table-column>
            <el-table-column prop="name" label="Ip_name" width="402px">
            </el-table-column>
            <el-table-column prop="workIpRole" label="Ip_Role" width="100px">
            </el-table-column>
            <el-table-column prop="ipShare" label="Ip_share" width="100px"> </el-table-column>
            <el-table-column prop="rightType" label="Ip_right" width="100px">
            </el-table-column>
          </el-table>
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>

    <div class="t-c p-t-40" style="width: 750px">
      <el-button type="primary" @click="submitFn(1)">確認</el-button>
<!--        <el-button type="primary" @click="refuse(0)">拒絕</el-button>-->
        <el-button type="primary" @click="selectWorkFn()">指定作品</el-button>
    </div>

    <!-- 指定作品 查找作品 弹框 artistName，author，composer -->
    <!-- 輸入查詢条件，查詢結果在match work info 列表里展示  artistName，author，composer  ，composer，artist Name -->
    <el-dialog title="指定作品" :visible.sync="selectWork.selectWorkShow" :close-on-click-modal="false">
      <div style="width: 100%; margin: auto; margin-bottom: 20px">
        <el-input  @keyup.enter.native='onSubmit()' style="width: 140px" placeholder="Title" v-model="selectWork.title"></el-input>
        <el-input  @keyup.enter.native='onSubmit()' style="width: 140px" placeholder="Work No" v-model="selectWork.workId"></el-input>
        <el-input  @keyup.enter.native='onSubmit()' style="width: 60px" placeholder="Soc" v-model="selectWork.soc"></el-input>
        <el-input  @keyup.enter.native='onSubmit()' style="width: 200px" placeholder="Author or Composer" v-model="selectWork.authorName"></el-input>
        <!-- <el-input  @keyup.enter.native='onSubmit()' style="width: 140px" placeholder="Composer" v-model="selectWork.composer"></el-input> -->
        <el-input  @keyup.enter.native='onSubmit()' style="width: 140px" placeholder="ArtistName" v-model="selectWork.artistName"></el-input>
        <el-button slot="append" icon="el-icon-search" @click="onSubmit()"></el-button>
        <span class="clear-search" @click="clearSearch()">清除搜索</span>
      </div>
      <el-table :empty-text="emptyText3" :data="tableData" @row-dblclick="getCurrentWorkInfo" @row-click="highlightDialogRow" highlight-current-row ref="dialogTableRef">
        <el-table-column property="title" label="title">
          <template slot-scope="scope">
            <span class="over-line" :title="scope.row.title || scope.row.title_en">{{ scope.row.title || scope.row.title_en }}</span>
          </template>
        </el-table-column>
        <el-table-column property="work_id" label="workId"></el-table-column>
        <el-table-column property="work_society_code" label="workSoc"></el-table-column>
        <el-table-column property="genre_code" label="Genre"></el-table-column>
        <el-table-column property="author" label="author"></el-table-column>
        <el-table-column property="genre_code" label="Composer">
          <template slot-scope="scope">
            <span class="over-line" :title="scope.row.composer && scope.row.composer.join('、')">{{ scope.row.composer && scope.row.composer.join("、") }}</span>
          </template>
        </el-table-column>
        <el-table-column label="operation">
          <template slot-scope="scope">
            <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer;" title="指定作品">
              <i class="el-icon-check" @click="checkedWork(scope.$index, scope.row)"></i>
            </span>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background layout="prev, pager, next" :total="total" @current-change="onSubmit">
      </el-pagination>
    </el-dialog>
    <work-list ref="workList" :workId='selectWorkNo.toString()' :workSocietyCode='selectWorkSoc.toString()'></work-list>
  </div>
</template>
<script>
import workList from '../../demo/workList.vue'
export default {
  data() {
    return {
      listType: "",
      currentpage: 1,
      queryparam: "",
      queryindex: "",
      uploadType: "",
      // file work info
      fileInfo: {},
      // 选中的work 的信息
      matchInfo: {},
      workIpShareInfo: [],
      matchWorkIpShareInfo: [],
      activeNames: ["1", "2"],
      matchTable: [],
      isrcList: [],
      performerList: [],
      selectWorkTitleId: "",
      selectWork: {
        workId: "",
        soc: "",
        title: "",
        authorName: "",
        composer: "",
        performer: "",
        selectWorkShow: false,
        list: [],
      },
      fileMappingId: "",
      dataUniqueKey: "",
      searchInfo: {
        workId: "",
        title: "",
        soc: "",
      },
      tableData: [],
      loading: false,
      matchWorkInfoLoading: false,
      ipShareLoading: false,
      total: 0,
      ignore: true,
      emptyText: '暫無數據',
      emptyText1: '暫無數據',
      emptyText2: '暫無數據',
      emptyText3: '暫無數據',
      selectWorkNo:'',
      selectWorkSoc:'',
      ipShareLoadingTimer: null
    };
  },
  components:{ workList },
  mounted() {
    this.listType = this.$route.query.listType;
    this.queryindex = this.$route.query.useparams;
    console.log('----------', this.$route.query);
    // alert("))0000000")
    // this.uploadType = this.$route.query.uploadType;
    if (localStorage.getItem('tempindex')) {
      // this.queryparam = JSON.parse(localStorage.getItem('initquery'))
      // localStorage.getItem("tempindex", this.queryindex);
      // this.queryInfo(localStorage.getItem('tempindex'),2)

    } else {

      this.queryparam = JSON.parse(this.$route.query.usetabledata);
      var useid = this.queryparam[this.queryindex].id
      let nextparams = JSON.parse(this.$route.query.thissearchparam);
      nextparams.ListMatchDataOverseas.status = '0'
      this.$http.post('/listmatchoverseas/getListMatchDataOverseasList', nextparams).then(res => {
        if (res.success) {
          this.queryparam = res.data.data.list || [];
          this.queryparam.forEach((item, i, arr) => {
            if (arr[i].id == useid) {
              this.queryindex = i
            }
          })
        }
      })
      this.queryInfo();
    }
  },
  destroyed() {
    localStorage.removeItem("tempindex");
    localStorage.removeItem("initquery");
    localStorage.removeItem("pageindex");
  },

  methods: {
    workDrawer(row){
      console.log(row)
      // 设置当前高亮行
      this.$refs.matchTableRef.setCurrentRow(row);
      this.selectWorkNo = row.matchWorkId;
      this.selectWorkSoc = row.matchWorkSocietyCode;
      this.$nextTick(()=>{
          this.$refs.workList.init()
      })
    },
    // 高亮弹窗中的表格行
    highlightDialogRow(row) {
      // 设置当前高亮行
      this.$refs.dialogTableRef.setCurrentRow(row);
    },

    getCurrentWorkInfo(row) {
      // 设置当前高亮行
      this.$refs.dialogTableRef.setCurrentRow(row);

      let routeName = '';
      if (row.work_type === 'ARR') {
        routeName = 'works-baseinfoarr';
      } else if (row.work_type === 'ORG') {
        routeName = 'works-baseinfo';
      } else if (row.work_type === 'ADP') {
        routeName = 'works-baseinfoadp';
      } else if (row.work_type === 'AV' && row.genre_code != 'TVS') {
        routeName = 'works-baseinfoav';
      } else if ((row.work_type === 'AV' && row.genre_code === 'TVS') || row.work_type == 'TV') {
        routeName = 'works-baseinfotv';
      } else if (row.work_type == 'ME') {
        routeName = 'works-medleyinfo'
      }
      // r.push({name: routeName, query: {id: row.work_id, socId: row.work_society_code, title: row.title ? row.title : row.title_en, nameId: row.id+row.work_id}, params: {id: row.work_id}})
      //                         let {href} = this.$router.resolve({name:'login'})
      let { href } = this.$router.resolve({ name: routeName, query: { id: row.work_id, socId: row.work_society_code, title: row.title ? row.title : row.title_en, nameId: row.id + row.work_id }, params: { id: row.work_id } })
      window.open(href, '_blank')
    },
    queryInfo(index, nextone) {
      //獲取file work info
      //   alert(index)
      if (nextone == 2) {
        // console.warn('*****',this.$route.query.useparams+1,'^^^^^',this.$route.query.usetabledata,"$$$$$$",this.$route.query.usetabledata[this.$route.query.useparams+1])

        // if (index >= this.queryparam.length) {
        //   this.$toast({ tips: '這已經是最後一條數據了!' })
        //   return;
        // } else {
          // console.log('index-----',index)
          this.$http.get("/listmatchoverseas/getListMatchDataOverseasById", {
              params: { id:index },
              // params: { id: this.queryparam[index].id },
            }).then((res) => {
                //  alert(index)
              localStorage.setItem("tempindex", index);
                //  console.warn('localstorage',res)
              if (res.success) {
                this.fileInfo = res.data;
                // this.fileMappingId = res.data.fileMappingId;
                this.dataUniqueKey = res.data.dataUniqueKey;
                this.queryMatchWorks(this.dataUniqueKey);
                this.getFileWorkIpShare();
              }
            });
        // }
      } else {
        this.$http.get("/listmatchoverseas/getListMatchDataOverseasById", { params: { id: this.$route.query.id }, }).then((res) => {

          if (res.success) {
            this.fileInfo = res.data;
            // this.fileMappingId = res.data.fileMappingId;
            this.dataUniqueKey = res.data.dataUniqueKey;
            this.queryMatchWorks(this.dataUniqueKey);
            this.getFileWorkIpShare();
          }
        });


      }
    },

    queryMatchWorks(dataUniqueKey) {
      this.loading = true
      this.emptyText = ' ';
      this.$http
        .get("/listmatchoverseas/getListMatchDataOverseasMatchWorkList", {
          params: { dataUniqueKey },
        })
        .then((res) => {
          if (res.success) {
            this.matchTable = res.data;
            this.loading = false

            if(! this.matchTable || this.matchTable.length == 0){
              this.emptyText = '暫無數據';
            } else {
               // //默认选中第一行
              this.$refs.matchTableRef.setCurrentRow(this.matchTable[0]);
              this.changeWork(this.matchTable[0]);
            }
          }else{
            this.emptyText = '暫無數據';
          }
        });
    },
    // 处理行点击事件，设置当前高亮行
    handleRowClick(row, column, event) {
      // 设置当前高亮行
      this.$refs.matchTableRef.setCurrentRow(row);
      // 调用原有的changeWork逻辑
      this.changeWork(row, column, event);
    },
    changeWork(row, column, event) {
      this.matchWorkInfoLoading = true;
      this.matchInfo = {};
      this.selectWorkTitleId = row.matchTitleId;
      this.matchInfo = row;
      console.log(this.matchInfo.matchIsrc)
      console.log(this.matchInfo.matchArtists)
      // 代码有故障，值为 [] 时会报告".split" is not a function; 2025-01-03
      // this.matchInfo.matchIsrc =
      //   this.matchInfo.matchIsrc != ""
      //     ? this.matchInfo.matchIsrc.split(";")
      //     : [];
      // this.matchInfo.matchArtists =
      //   this.matchInfo.matchArtists != ""
      //     ? this.matchInfo.matchArtists.split(";")
      //     : [];
      // 以下代码为 修复代码 2025-01-03
      if (!this.matchInfo.matchIsrc) {
        this.matchInfo.matchIsrc = [];
      }
      if (this.matchInfo.matchIsrc.constructor === String) {
        this.matchInfo.matchIsrc = this.matchInfo.matchIsrc.split(";");
      }

      if (!this.matchInfo.matchArtists) {
        this.matchInfo.matchArtists = [];
      }
      if (this.matchInfo.matchArtists.constructor === String) {
        this.matchInfo.matchArtists = this.matchInfo.matchArtists.split(";");
      }

      this.getMatchWorkIpShare();
    },
    // file work ip share
    getFileWorkIpShare() {
      let params = {
        fileMappingId: this.fileMappingId,
      };
      this.$http
        .get("/listmatchoverseas/getListMatchDataOverseasMappingList", {
          params,
        })
        .then((res) => {
          if (res.success) {
            this.workIpShareInfo = res.data;
          }
        });
    },
    // match work ip share
    getMatchWorkIpShare() {
      let params = {
        id: this.matchInfo.id,
        workId: this.matchInfo.matchWorkId,
        workSocietyCode: this.matchInfo.matchWorkSocietyCode,
      };
      this.emptyText1 = ' ';
      this.emptyText2 = ' ';

      // 清除之前的计时器（如果存在）
      if (this.ipShareLoadingTimer) {
        clearTimeout(this.ipShareLoadingTimer);
      }

      // 设置一个计时器，如果加载时间超过300毫秒，则显示加载遮罩
      this.ipShareLoadingTimer = setTimeout(() => {
        this.ipShareLoading = true;
      }, 300);
      this.$http.get("/wrk/getWrkWorkById", { params }).then((res) => {
        if (res.success) {
          this.$set(this.matchInfo, "componentList", res.data.wwcList);
          if(! res.data.wwcList || res.data.wwcList.length == 0){
            this.emptyText1 = '暫無數據';
          }
          // Match Work Info 加载完成
          this.matchWorkInfoLoading = false;
          let type = "PER";
          this.matchWorkIpShareInfo = res.data.wwisList[0][type];
          let ipNameList = [];
          if (this.matchWorkIpShareInfo) {
            this.matchWorkIpShareInfo.forEach((item, i, arr) => {
              arr[i].name = arr[i].name ? arr[i].name : arr[i].dummyNameRoman
              if (item.ipNameNo && item.name) {
                ipNameList.push({
                  value: item.ipNameNo,
                  label: item.name,
                });
              }
            });
          }
          if(! this.matchWorkIpShareInfo || this.matchWorkIpShareInfo.length == 0){
            this.emptyText2 = '暫無數據';
          }
          this.workIpShareInfo.map((item) => {
            this.$set(item, "ipNameList", ipNameList);
            this.$set(item, "ipNameListValue", "");
          });
        }else{
          this.emptyText1 = '暫無數據';
          this.emptyText2 = '暫無數據';
          // 请求失败时也要关闭 Match Work Info 的加载状态
          this.matchWorkInfoLoading = false;
        }
        // 清除计时器并隐藏加载遮罩
        clearTimeout(this.ipShareLoadingTimer);
        this.ipShareLoading = false;
      });
    },
    selectWorkFn() {
      this.selectWork.selectWorkShow = true;
      this.selectWork.workId = "";
      this.selectWork.soc = "";
      this.selectWork.title = "";
      this.selectWork.list = [];
      this.emptyText3='暫無數據'
    },
    querySelectWorkList() {
      let ajaxData = {
        page: {
          pageNum: 1,
          pageSize: 999,
        },
        title: this.selectWork.title,
        workId: this.selectWork.workId,
        soc: this.selectWork.soc,
      };
      this.$http.post("/wrk/queryWrkWorkListEs", ajaxData).then((res) => {
        if (res.success) {
          this.matchTable = [];
          res.data.list.forEach((item, index) => {
            let temp = {};
            if (this.listType != "pg") {
              temp = {
                type: "m", //m 即為用户指定作品查找的作品
                matchTitleId: item.id,
                matchWorkId: item.work_id,
                matchWorkSocietyCode: item.work_society_code,
                matchTitle: item.title || item.title_en,
                matchComposers: item.composer && item.composer.join("、"),
                matchAuthors: <AUTHORS>
                matchIswc: item.iswc,
                isrcList: item.isrc,
                performerList: item.artist,
              };
            } else {
              temp = {
                type: "m",
                matchTitleId: item.id,
                matchWorkId: item.work_id,
                matchWorkSocietyCode: item.work_society_code,
                matchTitle: item.title || item.title_en,
                episode_no: item.episode_no,
              };
            }

            this.matchTable.push(temp);
          });
          //默认选中第一行
          this.$refs.matchTableRef.setCurrentRow(this.matchTable[0]);
          this.changeWork(this.matchTable[0]);
          this.selectWork.selectWorkShow = false;
        }
      });
    },
    clearSearch() {
      for (let item in this.selectWork) {
        this.selectWork[item] = ''
      }
      this.onSubmit()
    },
    onSubmit(page) {
      let params = this.selectWork;
      params.page = {
        pageNum: page ? page : 1,
        pageSize: 10,
      };
      this.currentpage = page
      this.emptyText3 = ' ';
      this.$http.post("/wrk/queryWrkWorkListEs", params).then((res) => {
        if (res.success) {
          this.tableData = res.data.list;
          this.total = res.data.total;
        }
        if(! this.tableData || this.tableData.length == 0){
            this.emptyText3 = '暫無數據';
        }
      });
    },
    checkedWork(index, row) {
      console.warn('----------------', row, index);
      // 设置当前高亮行
      this.$refs.dialogTableRef.setCurrentRow(row);

      this.$msgbox
        .confirm("確定指定此作品?", "提示", {
          confirmButtonText: "確定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(() => {
          this.ignore = false;
          // this.selectWorkTitleId = row.id;

          this.selectWork.selectWorkShow = false;
          let matchComposers = [];
          let matchAuthors = [];
          row.composer &&
            row.composer.map((item) => {
              item && matchComposers.push(item);
            });
          row.author &&
            row.author.map((item) => {
              item && matchAuthors.push(item);
            });
          let data = {
            matchWorkId: row.work_id,
            matchWorkSocietyCode: row.work_society_code,
            matchTitle: row.title_en,
            matchTitleId: row.id,
            matchAuthors: <AUTHORS>
            matchComposers: matchComposers.join(";"),
            matchIswc: row.iswc,
          };
          this.matchTable = [data];
          this.changeWork(data);
        })
        .catch(() => { });
    },
    queryWorkInfo() {
      this.selectWork.list = [];
      // 查詢此work no 的ipshare
      let params = {
        workId: this.selectWork.workId,
        workSocietyCode: this.selectWork.soc,
      };
      this.$http.get("/wrk/getWrkWorkById", { params }).then((res) => {
        if (res.success) {
          this.selectWork.list.push(res.data.wrkWork);
          this.selectWork.workInfo = res.data.wrkWork;
        }
      });
    },
    submitFn(status) {
      if (this.matchTable.length > 1 && !this.selectWorkTitleId) {
        this.$toast({ tips: "請選擇一個匹配作品" });
        return;
      }
      //  0 默认  1通過  2拒絕
      let listMatchDataOverseasMappingDto = [];
      let canSave = true;
      /*this.workIpShareInfo.map(item => {
                    /!*if(this.ignore){
                        if(!item.ipNameListValue){
                            this.$toast({tips:'請選擇match ip_name'})
                            canSave =false
                        }
                    }*!/
                    listMatchDataOverseasMappingDto.push({
                        id:item.id,
                        ipNameNo:item.ipNameListValue,
                        status:status
                    })
                })*/
      let ajaxData = {
        status: status,
        dataUniqueKey: this.fileInfo.dataUniqueKey,
        workTitleId: this.selectWorkTitleId,
      };
      canSave && this.$http.post("/listmatchoverseas/checkListMatchDataOverseasUniqWithMtchWork", ajaxData).then((res) => {
        this.ignore = true;
        console.warn("resres", res);
        if (
          Object.prototype.toString.call(res.data) === "[object Object]"
        ) {
          this.$toast({ tips: res.data.message });
        } else {
          // 操作成功，询问是否继续审核下一条
          this.$toast({ tips: '操作成功，繼續審核下一條' });
          this.findNextPendingItem();
        }
      });

      // 以下是旧的代码，已被新的findNextPendingItem方法替代
      /*
      if (this.queryindex >= this.queryparam.length - 1) {
        this.$confirm('操作成功，继续审核下一条？', '确认信息', {
          distinguishCancelAndClose: true, closeOnClickModal: false,
          confirmButtonText: '是',
          cancelButtonText: '否'
        }).then(() => {
          var tempindex
          if (this.queryparam.length < 10) {
            tempindex = 1
          } else {
            if (localStorage.getItem('pageindex')) {
              tempindex = this.$route.query.thissearchparam.page.pageNum = localStorage.getItem('pageindex')
            } else {
              tempindex = this.$route.query.thissearchparam.page.pageNum
            }
            localStorage.setItem('pageindex', tempindex)
          }
          let nextparams = this.$route.query.thissearchparam
          nextparams.page.pageNum = tempindex
          nextparams.ListMatchDataOverseas.status = '0'
          this.$http.post('/listmatchoverseas/getListMatchDataOverseasList', nextparams).then(res => {
            if (res.success) {
              this.queryparam = res.data.data.list || [];
              localStorage.setItem('initquery', JSON.stringify(this.queryparam))
              this.queryindex = 0
              localStorage.setItem("tempindex", this.queryindex);
              this.queryInfo(this.queryindex, 2);
            }
          })
        }).catch(action => {
        });
        return;
      } else {
        this.$confirm('操作成功，继续审核下一条？', '确认信息', {
          distinguishCancelAndClose: true, closeOnClickModal: false,
          confirmButtonText: '是',
          cancelButtonText: '否'
        }).then(() => {
          this.queryindex += 1;
          console.warn("indexquery", this.queryindex, 'legnrth', this.queryparam.length);
          localStorage.setItem("tempindex", this.queryindex);
          this.queryInfo(this.queryindex, 2);
        }).catch(action => {
          if (action === 'cancel') {
            this.$bus.$emit("closeCurrentTab", () => {
              this.$router.push({
                name: "oAuditList",
                query: { update: true },
              });
            });
          }
        });
      }
      */
    },
    viewSampleDetail(info) {
      console.log('查看合併清單鳴謝', info);
      this.$router.push({
        name: "oAuditSample",
        query: { work: JSON.stringify(info) },
      });
    },

    // 查找下一条待审核的数据
    findNextPendingItem() {
      // 先从当前页面传递的数据中查找待审核的数据
      this.findPendingItemsFromCurrentList();
    },

    // 从当前页面传递的数据中查找待审核的数据
    findPendingItemsFromCurrentList() {
      // 尝试解析传递的数据列表
      try {
        if (this.$route.query.usetabledata) {
          const tableData = JSON.parse(this.$route.query.usetabledata);
          const currentIndex = parseInt(this.$route.query.useparams, 10);

          // 从当前项开始向后查找状态为待审核的数据
          const pendingItems = tableData.filter((item, index) =>
            index > currentIndex && item.status === 0
          );

          if (pendingItems.length > 0) {
            // 找到了下一条待审核的数据，跳转到该数据的审核页面
            const nextItem = pendingItems[0];
            const nextIndex = tableData.findIndex(item => item.id === nextItem.id);

            this.$router.push({
              name: 'oAuditDetail',
              params: {
                title: `O清單審核明細:${nextItem.originalTitle}`,
              },
              query: {
                id: nextItem.id,
                listType: nextItem.uploadType,
                useparams: nextIndex,
                usetabledata: this.$route.query.usetabledata,
                thissearchparam: this.$route.query.thissearchparam
              }
            });
            return;
          }
        }

        // 如果当前列表中没有找到待审核的数据，则从服务器获取新的待审核数据
        this.fetchPendingItemsFromServer();
      } catch (error) {
        console.error('解析数据失败:', error);
        // 出错时从服务器获取新的待审核数据
        this.fetchPendingItemsFromServer();
      }
    },

    // 从服务器获取待审核的数据
    fetchPendingItemsFromServer() {
      // 构造查询参数，只查询状态为待审核的数据
      const searchParams = {
        ListMatchDataOverseas: {
          matchScore: '',
          fileBaseId: '',
          sourceWorkCode: '',
          status: '0',  // 状态为待审核
          matchWorkTitle: ''
        },
        page: {
          pageNum: 1,
          pageSize: 10
        }
      };

      this.$http.post('/listmatchoverseas/getListMatchDataOverseasList', searchParams).then(res => {
        if (res.success && res.data.data.list && res.data.data.list.length > 0) {
          // 找到了待审核的数据，跳转到第一条数据的审核页面
          const nextItem = res.data.data.list[0];

          this.$router.push({
            name: 'oAuditDetail',
            params: {
              title: `O清單審核明細:${nextItem.originalTitle}`,
            },
            query: {
              id: nextItem.id,
              listType: nextItem.uploadType,
              useparams: 0,
              usetabledata: JSON.stringify(res.data.data.list),
              thissearchparam: JSON.stringify(searchParams)
            }
          });
        } else {
          // 没有找到待审核的数据，提示用户并返回列表页面
          this.$toast({tips: '沒有未審核的數據了'});
          this.$bus.$emit("closeCurrentTab", () => {
            this.$router.push({
              name: "oAuditList",
              query: { update: true },
            });
          });
        }
      }).catch(error => {
        console.error('获取待审核数据失败:', error);
        this.$toast({tips: '获取待审核数据失败'});
        // 出错时返回列表页面
        this.$bus.$emit("closeCurrentTab", () => {
          this.$router.push({
            name: "oAuditList",
            query: { update: true },
          });
        });
      });
    },
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      console.log("*****", vm);
      // vm.formData.distNo = to.params.distNo
      if (localStorage.getItem("tempindex")) {
        vm.queryInfo(localStorage.getItem("tempindex"), 2);
      }
    });
  },
};
</script>
<style lang="scss" scoped>
@import "../../../assets/scss/works.scss";
.p-t-10 {
  padding-top: 0;
}
.el-collapse-item {
  width: 480px;
  float: left;
}
/deep/ .table2 .el-collapse-item__wrap {
  height: 502px;
}
.match-table {
  width: 100%;
}
/deep/ .match-table thead .el-checkbox {
  display: none;
}
/deep/ .match-info {
  margin-top: 10px;
}
/deep/ .match-info .el-table tr {
  background: #fff;
}
/deep/ .el-collapse-item__arrow {
  display: none;
}
/deep/ .component .el-form-item__content {
  width: 100%;
}
/deep/ .el-input.is-disabled .el-input__inner {
  background: #fff;
  color: #333;
}
/* 表格行高亮效果样式 - 与billlist.vue和list.vue保持一致 */
/deep/ .el-table__body tr.current-row > td {
  background-color: #17b3a3 !important;
  color: white !important;
}

/deep/ .el-table__body tr.current-row .el-button--text span {
  color: #eae1e1 !important;
}

/* 确保选中行的图标也变白 */
/deep/ .el-table__body tr.current-row .el-icon-star-on {
  color: white !important;
}

/* 确保选中行的所有元素文字都是白色 */
/deep/ .el-table__body tr.current-row td * {
  color: white !important;
}

/* 弹窗中的表格也应用相同的行高亮效果 */
/deep/ .el-dialog .el-table__body tr.current-row > td {
  background-color: #17b3a3 !important;
  color: white !important;
}

/deep/ .el-dialog .el-table__body tr.current-row .el-button--text span {
  color: #eae1e1 !important;
}

/deep/ .el-dialog .el-table__body tr.current-row td * {
  color: white !important;
}
/deep/ .el-form-item.f12 label {
  font-size: 13px;
}

ul.list {
  background: #fff;
  padding: 0;
  border: 1px solid #ddd;
  margin-top: 10px;
  border-radius: 4px;
  li {
    list-style: none;
    border-bottom: 1px solid #ddd;
    line-height: 26px;
    padding: 0 4px;
  }
  &:last-child {
    border-bottom: 0;
  }
}

/deep/ .el-input {
  width: 180px;
}
.part1 {
  padding: 10px 0 0;
  /deep/ .el-input {
    width: 400px;
  }
}
.match-work-info-scroll {
  height: 140px;
  overflow-y: auto;
}
.title {
  font-size: 18px;
}
</style>
