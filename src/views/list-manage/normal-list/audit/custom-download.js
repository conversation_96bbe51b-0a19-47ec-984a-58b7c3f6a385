/**
 * 自定义下载函数，用于处理文件下载
 * @param {Blob} data - 文件数据
 * @param {string} contentDisposition - Content-Disposition 头
 * @param {string} contentType - Content-Type 头
 */
export function customDownload(data, contentDisposition, contentType) {
  // 创建 Blob 对象
  const blob = new Blob([data], { type: contentType });
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;

  // 从 Content-Disposition 头中提取文件名
  let filename = '';
  if (contentDisposition) {
    // 使用正则表达式提取文件名，处理引号和其他特殊字符
    const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/i;
    const matches = contentDisposition.match(filenameRegex);
    if (matches && matches[1]) {
      // 移除引号和其他特殊字符
      filename = matches[1].replace(/['"]*$/g, '').replace(/^['"]*/, '');
    }
  }

  // 如果没有提取到文件名，根据 Content-Type 设置默认文件名
  if (!filename) {
    // 根据 Content-Type 判断文件类型
    if (contentType === 'application/zip') {
      filename = 'export.zip';
    } else if (contentType === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
      filename = 'export.xlsx';
    } else {
      // 默认文件名
      filename = 'export.xlsx';
    }
  }

  // 显示下载文件类型的提示信息
  if (contentType === 'application/zip') {
    // 如果是ZIP文件，显示提示信息
    const message = document.createElement('div');
    message.style.position = 'fixed';
    message.style.top = '20px';
    message.style.left = '50%';
    message.style.transform = 'translateX(-50%)';
    message.style.padding = '10px 20px';
    message.style.backgroundColor = '#67C23A';
    message.style.color = 'white';
    message.style.borderRadius = '4px';
    message.style.zIndex = '9999';
    message.style.boxShadow = '0 2px 12px 0 rgba(0, 0, 0, 0.1)';
    message.textContent = '已下载ZIP文件，请解压后查看内容';
    document.body.appendChild(message);

    // 3秒后自动移除提示
    setTimeout(() => {
      document.body.removeChild(message);
    }, 3000);
  } else if (contentType === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
    // 如果是Excel文件，显示提示信息
    const message = document.createElement('div');
    message.style.position = 'fixed';
    message.style.top = '20px';
    message.style.left = '50%';
    message.style.transform = 'translateX(-50%)';
    message.style.padding = '10px 20px';
    message.style.backgroundColor = '#409EFF';
    message.style.color = 'white';
    message.style.borderRadius = '4px';
    message.style.zIndex = '9999';
    message.style.boxShadow = '0 2px 12px 0 rgba(0, 0, 0, 0.1)';
    message.textContent = '已下载Excel文件';
    document.body.appendChild(message);

    // 3秒后自动移除提示
    setTimeout(() => {
      document.body.removeChild(message);
    }, 3000);
  }

  // 设置下载文件名
  link.setAttribute('download', decodeURIComponent(filename));
  document.body.appendChild(link);
  link.click();
  window.URL.revokeObjectURL(url);
  document.body.removeChild(link);
}
