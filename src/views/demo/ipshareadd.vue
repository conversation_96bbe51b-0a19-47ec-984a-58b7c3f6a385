<template>
  <div>
    <el-form :inline="true" :model="formShare" class="demo-form-inline">
      <el-form-item label="Type">
        <el-select v-model="formShare.rightType" placeholder="Type">
          <el-option label="PERFORMING RIGHT" value="PER"></el-option>
          <el-option label="MECHANICAL RIGHT" value="MEC"></el-option>
          <el-option label="SYNCHRONIZATION RIGHT" value="ZYN"></el-option>
          <el-option label="OD RIGHT" value="NOD"></el-option>
          <el-option label="DB RIGHT" value="NDB"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-checkbox v-model="dischecked" :disabled="!disCanCheck">Distributable</el-checkbox>
      </el-form-item>
      <el-form-item label="Share Type">
        <el-select v-model="formShare.autoCalc" placeholder="Share Type">
          <el-option label="Manual" value="manual"></el-option>
          <el-option label="Auto" value="auto"></el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="Sort by">
             <el-select v-model="formShare.region" placeholder="Share Type">
             <el-option label="Group" value="md"></el-option>
             <el-option label="english" value="en"></el-option>
             <el-option label="both" value="both"></el-option>
             </el-select>
             </el-form-item>-->
      <el-form-item v-if="updateRsd">
        <el-checkbox v-model="rschecked" @change="changeRsd">Right SD</el-checkbox>
      </el-form-item>
      <div class="right add-new">
        <el-button type="primary" @click="copyRight()">Copy IP</el-button>
        <el-button type="primary" @click="pasteRight()">Paster IP</el-button>
        <el-button type="primary" @click="adddata2()">新 增</el-button>
      </div>
    </el-form>
    <div class="boxline" v-if="update">
      <el-table :data="rightData[formShare.rightType]" border stripe show-summary :summary-method="getSummaries" style="width: 100%">
        <el-table-column prop="groupIndicator" label="Gp">
          <template slot-scope="scope">
            <el-input v-model=scope.row.groupIndicator placeholder="Gp"></el-input>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="Name">
          <template slot-scope="scope">
            <el-input v-model=scope.row.name placeholder="Name" @dblclick.native="getIpShareData(scope.$index, scope.row)"></el-input>
          </template>
        </el-table-column>
        <el-table-column prop="chineseName" label="Chinese Name">
          <template slot-scope="scope">
            <el-input v-model=scope.row.chineseName placeholder=""></el-input>
          </template>
        </el-table-column>
        <el-table-column prop="ipNameNo" label="Ip Name No">
          <template slot-scope="scope">
            <el-input v-model=scope.row.ipNameNo placeholder="Ip Name No" @dblclick.native="getDataByNo(scope.$index, scope.row)"></el-input>
          </template>
        </el-table-column>
        <el-table-column prop="workIpRole" label="Role" width="100">
          <template slot-scope="scope">
            <el-input v-model=scope.row.workIpRole placeholder="Role"></el-input>
          </template>
        </el-table-column>
        <el-table-column prop="ipSocietyCode" label="Soc" width="100">
          <template slot-scope="scope">
            <el-input v-model=scope.row.ipSocietyCode placeholder="Soc"></el-input>
          </template>
        </el-table-column>
        <el-table-column prop="ipShare" label="Ip Share" width="100">
          <template slot-scope="scope">
            <el-input v-model=scope.row.ipShare placeholder="Ip Share"></el-input>
          </template>
        </el-table-column>
        <el-table-column prop="sdcheck" label="Sd" width="80px">
          <template slot-scope="scope">
            <el-checkbox v-model=scope.row.sdcheck @change="changeSd(scope.row)"></el-checkbox>
          </template>
        </el-table-column>
        <el-table-column label="operation" width="120">
          <template slot-scope="scope">
            <span style="width: 100%;display: inline-block; text-align: center;">
              <i class="el-icon-delete" v-if="isAuth('works:base:change')" @click="deletedata2(scope.$index,rightData[formShare.rightType])"></i>
            </span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-dialog :visible.sync="ipShareShow" :close-on-click-modal="false">
      <div style="width: 500px;margin: auto;margin-bottom: 20px">
        <el-input placeholder="請輸入name" @keyup.enter.native="ipShareSearch" v-model="ipShareInput" class="input-with-select">
          <el-button slot="append" icon="el-icon-search" @click="ipShareSearch"></el-button>
        </el-input>
      </div>
      <el-table :empty-text="tableresult" stripe :data="ipShareData">
        <el-table-column property="name" label="Name"></el-table-column>
        <el-table-column property="ip_name_no" label="ipNameNo"></el-table-column>
        <el-table-column property="society_code" label="Soc"></el-table-column>
        <el-table-column property="role_code" label="roleCode"></el-table-column>
        <el-table-column label="operation" width="100">
          <template slot-scope="scope">
            <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
              <i class="el-icon-check" v-if="isAuth('works:base:change')" @click="checkedIpShare(scope.$index,scope.row)"></i>
            </span>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background layout="prev, pager, next" :total=ipShareTotal @current-change="ipShareHandleCurrentChange">
      </el-pagination>
    </el-dialog>
  </div>
</template>

<script>
import axios from '../../utils/httpRequest'
import qs from 'qs'
export default {
  name: 'ipshareadd',
  data() {
    return {
         tableresult:' ',

      formShare: {
        rightType: 'PER',
        autoCalc: 'manual'
      },
      update: true,
      dischecked: true,
      updateRsd: true,
      ipShareInput: '',
      ipShareData: [],
      rschecked: false,
      ipShareShow: false,
      ipShareTotal: 10,
      disCanCheck: false,
      rightData: {}
    }
  },
  props: {
    tableRight: {
      type: Object,
      default: () => { }
    }
  },
  methods: {
    // 供父级组件調用，獲取当前ipshareData
    getIpShare() {
      //  為当前的列表數據  并不是所有類型的ipshare的數據
      return this.rightData;
      // return this.rightData[this.formShare.rightType];
    },
    checkDisCanCheck() {
      let flag = true;
      // 每次change都检查distributable是否可以點擊
      // 当前type
      let count = 0;
      this.rightData[this.formShare.rightType] && this.rightData[this.formShare.rightType].forEach((item, index) => {
        count += parseFloat(item.ipShare);
        if (!item.groupIndicator && value.groupIndicator != 0) {
          flag = false;
        }
      })
      if (count != 100) {
        flag = false;
      }
      let types = ['PER', 'MEC', 'ZYN', 'NOD', 'NDB'];
      types.forEach((item, index) => {
        if (item == this.formShare.rightType) {
          return;
        }
        count = 0;
        this.rightData[item] && this.rightData[item].forEach((value) => {
          count += parseFloat(value.ipShare);
          if (!value.groupIndicator && value.groupIndicator != 0) {
            flag = false;
          }
        })
        if (count != 100) {
          flag = false;
        }
      })
      this.disCanCheck = flag;
    },
    changeRsd(val) {
      this.rschecked = val
      this.update = false
      this.$nextTick(() => {
        this.update = true
      })

      if (val) {
        this.rightData[this.formShare.rightType].forEach((item, index) => {
          item.sdcheck = val
        })
      }
      if (!val) {
        this.rightData[this.formShare.rightType].forEach((item, index) => {
          item.sdcheck = val
        })
      }
    },
    changeSd(item) {
      this.updateRsd = false
      this.$nextTick(() => {
        this.updateRsd = true
      })
      if (JSON.stringify(this.rightData[this.formShare.rightType]).includes('false')) {
        this.rschecked = false
      } else {
        this.rschecked = true
      }
    },
    deletedata2(index, rows) {
      this.$msgbox.confirm(`確定進行[删除]操作?`, '提示', {
        confirmButtonText: '確定', closeOnClickModal: false,
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.rightData[this.formShare.rightType].splice(index, 1)
      })
    },
    getSummaries(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '總計'
        } else if (index === 5) {
          const values = data.map(item => Number(item[column.property]))
          if (!values.every(value => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr)
              if (!isNaN(value)) {
                return prev + curr
              } else {
                return prev
              }
            }, 0)
          } else {
            sums[index] = 'N/A'
          }
        } else {
          sums[index] = '--'
        }
      })
      return sums
    },
    // 复制功能
    copyRight() {
      // if (this.formShare.rightType === 'PER') {
      //     localStorage.setItem('rightData', JSON.stringify(this.tableData2))
      //     localStorage.setItem('PER', JSON.stringify(this.tableData2))
      // }
      // if (this.formShare.rightType === 'MEC') {
      //     localStorage.setItem('rightData', JSON.stringify(this.tableData2))
      //     localStorage.setItem('MEC', JSON.stringify(this.tableData2))
      // }
      // if (this.formShare.rightType === 'ZYN') {
      //     localStorage.setItem('rightData', JSON.stringify(this.tableData2))
      //     localStorage.setItem('ZYN', JSON.stringify(this.tableData2))
      // }
      // if (this.formShare.rightType === 'NOD') {
      //     localStorage.setItem('rightData', JSON.stringify(this.tableData2))
      //     localStorage.setItem('NOD', JSON.stringify(this.tableData2))
      // }
      // if (this.formShare.rightType === 'NDB') {
      //     localStorage.setItem('rightData', JSON.stringify(this.tableData2))
      //     localStorage.setItem('NDB', JSON.stringify(this.tableData2))
      // }
      // copyRight
    },
    pasteRight() {
      // this.tableData2 = JSON.parse(localStorage.getItem('rightData'))
      // if (this.formShare.rightType === 'PER') {
      //     this.PER = this.tableData2
      //     localStorage.setItem('PER', JSON.stringify(this.tableData2))
      // }
      // if (this.formShare.rightType === 'MEC') {
      //     this.MEC = this.tableData2
      //     localStorage.setItem('MEC', JSON.stringify(this.tableData2))
      // }
      // if (this.formShare.rightType === 'ZYN') {
      //     this.ZYN = this.tableData2
      //     localStorage.setItem('ZYN', JSON.stringify(this.tableData2))
      // }
      // if (this.formShare.rightType === 'NOD') {
      //     this.NOD = this.tableData2
      //     localStorage.setItem('NOD', JSON.stringify(this.tableData2))
      // }
      // if (this.formShare.rightType === 'NDB') {
      //     this.NDB = this.tableData2
      //     localStorage.setItem('NDB', JSON.stringify(this.tableData2))
      // }
    },
    adddata2() {
      this.rschecked = false
      let myArray = [{ groupIndicator: '', name: '', ipNameNo: '', workSocietyCode: '', ipShare: '', sdcheck: '', ipType: '' }]
      if (!this.rightData[this.formShare.rightType]) {
        this.$set(this.rightData, this.formShare.rightType, [])
      }
      this.rightData[this.formShare.rightType].push(myArray);
    },
    getIpShareData(index, item) {
      this.ipShareIndex = index
      this.ipShareShow = true
      let data = {
        name: '',
        page_num: 1,
        page_size: '10'
      }
      this.tableresult = '數據加載中...'
      let formData = qs.stringify(data)
      let config = { headers: { 'content-type': 'application/x-www-form-urlencoded' } }
      axios.post('/ip/name/es', formData, config).then(res => {
        if (res.status === 200) {
          this.ipShareData = res.data.list
          this.tableresult = this.ipShareData.length == 0 ? '暫無數據' : ' '
          this.ipShareTotal = res.data.total
        }
      })
    },
    // 根據會員編號精確查詢
    getDataByNo(index, item) {
      if (item.ipNameNo === '') {
        this.$message({
          type: 'warning',
          message: '請輸入精確的ipNameNo'
        })
      }
      if (item.ipNameNo !== '') {
        let data = {
          name_no: item.ipNameNo,
          page_num: 1,
          page_size: '10'
        }
        this.tableresult = '數據加載中...'

        let formData = qs.stringify(data)
        let config = { headers: { 'content-type': 'application/x-www-form-urlencoded' } }
        axios.post('/ip/name/es', formData, config).then(res => {
          if (res.status === 200) {
            this.ipShareData =res.data? res.data.list:[];
            this.artistTotal =res.data? res.data.total:0;

            this.rightData[this.formShare.rightType][index].name = this.ipShareData[0].name;
            this.rightData[this.formShare.rightType][index].ipNameNo = this.ipShareData[0].ip_name_no;
            this.rightData[this.formShare.rightType][index].workIpRole = this.ipShareData[0].role_code;
            this.rightData[this.formShare.rightType][index].ipSocietyCode = this.ipShareData[0].society_code;
          }
            this.tableresult = this.ipShareData.length == 0 ? '暫無數據' : ' '
        })
      }
    },
    ipShareSearch() {
      let data = {
        name: this.ipShareInput,
        page_num: 1,
        page_size: '10'
      }
      let formData = qs.stringify(data)
      let config = { headers: { 'content-type': 'application/x-www-form-urlencoded' } }
      this.tableresult = '數據加載中...'

      axios.post('/ip/name/es', formData, config).then(res => {
        if (res.status === 200) {
          this.ipShareData = res.data.list
          this.tableresult = this.ipShareData.length == 0 ? '暫無數據' : ' '

          this.ipShareTotal = res.data.total
        }
      })
    },
    checkedIpShare(index, item) {
      this.ipShareShow = false;
      this.rightData[this.formShare.rightType][this.ipShareIndex].name = item.name;
      this.rightData[this.formShare.rightType][this.ipShareIndex].ipNameNo = item.ip_name_no;
      this.rightData[this.formShare.rightType][this.ipShareIndex].workIpRole = item.role_code;
      this.rightData[this.formShare.rightType][this.ipShareIndex].ipSocietyCode = item.society_code;
    },
    ipShareHandleCurrentChange(val) {
      let data = {
        name: this.ipShareInput,
        page_num: val,
        page_size: '10'
      }
      let formData = qs.stringify(data)
      let config = { headers: { 'content-type': 'application/x-www-form-urlencoded' } }
          this.tableresult='數據加載中...'
      axios.post('/ip/name/es', formData, config).then(res => {
        if (res.status === 200) {
          this.ipShareData = res.data.list
              this.tableresult=this.ipShareData.length==0?'暫無數據':' '
          this.ipShareTotal = res.data.total
        }
      })
    }
  },
  computed: {
    id: {
      get() {
        return this.$store.state.lang.id
      }
    },
    workId: {
      get() {
        return this.$route.query.id
      }
    },
    socId: {
      get() {
        return this.$route.query.socId
      }
    }
  },
  mounted() {
  },
  watch: {
    rightData: {
      handler(newVal, oldVal) {
        this.checkDisCanCheck();
      },
      deep: true
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../../assets/scss/works.scss";
</style>
