import Vue from 'vue'
import router from '@/router'
import store from '@/store'
import axios from "axios";
import { Loading } from 'element-ui'
import { MessageBox } from 'element-ui';
import { toast } from '../components/toast';
const loading = null;

/**
 * 获取uuid
 */
export function getUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {
        return (c === 'x' ? (Math.random() * 16 | 0) : ('r&0x3' | '0x8')).toString(16)
    })
}

/**
 * 是否有权限
 * @param {*} key
 */
export function isAuth(key) {
    return JSON.parse(sessionStorage.getItem('permissions') || '[]').indexOf(key) !== -1 || false
}

/**
 * 树形数据转换
 * @param {*} data
 * @param {*} id
 * @param {*} pid
 */
export function treeDataTranslate(data, id = 'id', pid = 'parentId') {
    var res = []
    var temp = {}
    for (var i = 0; i < data.length; i++) {
        temp[data[i][id]] = data[i]
    }
    for (var k = 0; k < data.length; k++) {
        if (temp[data[k][pid]] && data[k][id] !== data[k][pid]) {
            if (!temp[data[k][pid]]['children']) {
                temp[data[k][pid]]['children'] = []
            }
            if (!temp[data[k][pid]]['_level']) {
                temp[data[k][pid]]['_level'] = 1
            }
            data[k]['_level'] = temp[data[k][pid]]._level + 1
            temp[data[k][pid]]['children'].push(data[k])
        } else {
            res.push(data[k])
        }
    }
    return res
}

/**
 * 清除登录信息
 */
export function clearLoginInfo() {
    Vue.cookie.delete('token')
    store.commit('resetStore')
    router.options.isAddDynamicMenuRoutes = false
}

/**
 * 千分位格式化，支持小数精度格式化
 * @param {String|Number} value
 * @param {Number} litNumberFixLength
*/
/**
 * 千分位格式化，支持小数精度格式化
 * @param {String|Number} value
 * @param {Number} litNumberFixLength
 */
export function formatThousand(value = '', litNumberFixLength = 0) {
    let isNegative = false;
    let strValue = String(value);
    // 检查是否为负数
    if (strValue.startsWith('-')) {
        isNegative = true;
        strValue = strValue.slice(1);
    }
    // 去除非数字和非小数点字符
    const purifyRegex = /[^0-9\.]/gi;
    const matchRegex = /\d{1,3}(\..*)*$/gi;
    let analyseStr = strValue.replace(purifyRegex, '');
    let matchResults = [];
    let loopLimit = 100;
    while (loopLimit > 0 && analyseStr.length > 0) {
        loopLimit = loopLimit - 1;
        const matchStr = (analyseStr.match(matchRegex) || [''])[0];
        analyseStr = analyseStr.replace(new RegExp(`${matchStr}$`, 'gi'), '');
        matchResults.push(matchStr);
    }
    if (litNumberFixLength) {
        const litPart = matchResults[0].split('.')[1];
        if (!litPart) {
            const litNumberArr = (new Array(litNumberFixLength)).fill('0');
            matchResults[0] = matchResults[0] + '.' + litNumberArr.join('');
        } else {
            const litFixLength = litNumberFixLength - litPart.length;
            if (litFixLength > 0) {
                const litNumberArr = (new Array(litFixLength)).fill('0');
                matchResults[0] = matchResults[0] + litNumberArr.join('');
            }
        }
    }
    let formatted = matchResults.reverse().join(',');
    // 如果是负数，添加负号
    if (isNegative) {
        formatted = '-' + formatted;
    }
    return formatted;
}
// export function formatThousand(value = '', litNumberFixLength = 0) {
//     const purifyRegex = /[^0-9\.]/gi;
//     const matchRegex = /\d{1,3}(\..*)*$/gi;
//     let analyseStr = String(value).replace(purifyRegex, '');
//     let matchResults = [];
//     let loopLimit = 100;
//     while (loopLimit > 0 && analyseStr.length > 0) {
//         loopLimit = loopLimit - 1;
//         const matchStr = (analyseStr.match(matchRegex) || [''])[0];
//         analyseStr = analyseStr.replace(new RegExp(`${matchStr}$`, 'gi'), '');
//         matchResults.push(matchStr);
//     }
//     if (litNumberFixLength) {
//         const litPart = matchResults[0].split('.')[1];
//         if (!litPart) {
//             const litNumberArr = (new Array(litNumberFixLength)).fill('0');
//             matchResults[0] = matchResults[0] + '.' + litNumberArr.join('');
//         } else {
//             const litFixLength = litNumberFixLength - litPart.length;
//             if (litFixLength > 0) {
//                 const litNumberArr = (new Array(litFixLength)).fill('0');
//                 matchResults[0] = matchResults[0] + litNumberArr.join('');
//             }
//         }
//     }

//     return matchResults.reverse().join(',');
// }

export const utils = {
    /**
     *JSON 2 get
     */
    parseParams(data) {
        try {
            var tempArr = []
            for (var i in data) {
                var key = encodeURIComponent(i)
                var value = encodeURIComponent(data[i])
                tempArr.push(key + '=' + value)
            }
            var urlParamsStr = tempArr.join('&')
            return urlParamsStr
        } catch (err) {
            return ''
        }
    },
    isAuth(key) {
        return JSON.parse(sessionStorage.getItem('permissions') || '[]').indexOf(key) !== -1 || false
    },
    getUUID() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {
            return (c === 'x' ? (Math.random() * 16 | 0) : ('r&0x3' | '0x8')).toString(16)
        })
    },
    copy(obj) {
        return JSON.parse(JSON.stringify(obj||{}));
    },
    clone(obj) {
        if (!obj || (typeof obj !== 'object')) {
            return;
        }
        var newObj = Object.prototype.toString.call(obj) == '[object Array]' ? [] : {};
        for (let key in obj) {
            if (typeof obj[key] == 'object') {
                newObj[key] = Clone(obj[key])
            } else {
                newObj[key] = obj[key];
            }
        }
        return newObj;
    },
    transdatetype(dateparam) {
        let newVal =dateparam.toString();
        let year = parseInt(newVal.substr(0, 4));

        let month = parseInt(newVal.substr(4, 2))>9?parseInt(newVal.substr(4, 2)):'0'+parseInt(newVal.substr(4, 2));
        let day = parseInt(newVal.substr(6, 2))>9?parseInt(newVal.substr(6, 2)):'0'+parseInt(newVal.substr(6, 2));
        newVal = year + '-' + month + '-' + day;
        return newVal
    },
    DATE(date, fmt) {
        if (!date) {
            return ''
        }
        var date = new Date(date)
        if (/(y+)/.test(fmt)) {
            fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length))
        }
        let o = {
            'M+': date.getMonth() + 1,
            'd+': date.getDate(),
            'h+': date.getHours(),  //小 h 为 12小时制，，需改
            'H+': date.getHours(),
            'm+': date.getMinutes(),
            's+': date.getSeconds()
        }
        for (let k in o) {
            if (new RegExp(`(${k})`).test(fmt)) {
                let str = o[k] + ''
                fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? str : ('00' + str).substr(str.length))
            }
        }
        return fmt
    },
    /**
     * 去除對象屬性前後的空格
     */
    trim(ajaxData) {
        for (let key in ajaxData) {
            if (key.indexOf('page') == -1 && typeof ajaxData[key] == 'string') {
                ajaxData[key] = (ajaxData[key] + '') ? (ajaxData[key] + '').trim() : ''
            }
        }
    },
    download(url, params, type = 'vnd.ms-excel') {
        // toast({tips: '數據準備中'});
        axios.post(url, params, {
            responseType: 'blob',     //告诉服务器我们需要的响应格式
            hide: true
        })
            .then(res => {

                let blob = new Blob([res.data], {
                    type: res.headers['content-type']      //将会被放入到blob中的数组内容的MIME类型
                });
                if (res.headers['content-type'].indexOf('json') != -1) {
                    var reader = new FileReader();
                    let content = '';
                    reader.onload = function (event) {
                        content = reader.result;//内容就在这里
                        MessageBox.confirm(JSON.parse(content).message, '提示', { showCancelButton: false });
                    };
                    reader.readAsText(blob);
                    return;
                }
                let objectUrl = URL.createObjectURL(blob);  //生成一个url
                let link = document.createElement('a');
                link.href = objectUrl;
                link.setAttribute("download", res.headers["content-disposition"].split('=')[1])
                document.body.appendChild(link);

                setTimeout(function () {
                    link.click();
                    document.body.removeChild(link);
                }, 66);
            })
            .catch(err => {
                console.log(err);
            })
    },
    downloadGet(url, params, type = 'vnd.ms-excel') {
        // toast({tips: '數據準備中'});
        loading = Loading.service({
            lock: true,
            text: 'Loading',
            background: 'rgba(0,0,0,0.3)'
        })
        axios.get(url, {
            params: params,
            responseType: 'blob',     //告诉服务器我们需要的响应格式
            hide: true
        })
            .then(res => {
                loading.close();
                console.log('res: ', res);
                let blob = new Blob([res.data], {
                    type: res.headers['content-type']      //将会被放入到blob中的数组内容的MIME类型
                });

                if (res.headers['content-type'].indexOf('json') != -1) {
                    var reader = new FileReader();
                    let content = '';
                    reader.onload = function (event) {
                        content = reader.result;//内容就在这里
                        MessageBox.confirm(JSON.parse(content).message, '提示', { showCancelButton: false });
                    };
                    reader.readAsText(blob);
                    return;
                }


                let objectUrl = URL.createObjectURL(blob);  //生成一个url
                console.log('url: ', blob);
                let link = document.createElement('a');
                link.href = objectUrl;

                // link.download = title+format
                link.setAttribute("download", decodeURI(decodeURI(res.headers["content-disposition"] ? res.headers["content-disposition"].split('=')[1] : '作品列表')))
                document.body.appendChild(link);

                setTimeout(function () {
                    link.click();
                    document.body.removeChild(link);
                    toast({ tips: '操作成功' })
                }, 66);
            })
            .catch(err => {
                loading.close();
                console.log(err);
            })
    },
    formatIswc(iswc) {
        if (!iswc) {
            return '';
        }
        let len = iswc.length
        let temp = '';
        console.log('len')
        if (len == 1) {
            temp = iswc[0]
            return temp;
        } else if (len < 5) {
            temp = iswc[0] + '-' + iswc.substring(1)
            return temp;
        } else if (len < 8) {
            temp = iswc[0] + '-' + iswc.substring(1, 4) + '.' + iswc.substring(4)
            return temp;
        } else if (len < 11) {
            temp = iswc[0] + '-' + iswc.substring(1, 4) + '.' + iswc.substring(4, 7) + '.' + iswc.substring(7)
            return temp;
        } else if (len) {
            temp = iswc[0] + '-' + iswc.substring(1, 4) + '.' + iswc.substring(4, 7) + '.' + iswc.substring(7, 10) + '-' + iswc[10];
            return temp;
        }
    },
    getIds(list, prop = 'id') {
        let arr = []
        if (list.length > 0) {
            list.map(item => {
                arr.push(item[prop])
            })
        }
        return arr
    },
    downloadByBlobPDF(blob, titlename, format = 'application/pdf') {
        console.log(titlename);
        let file = new Blob([blob], { type: format })
        let url = URL.createObjectURL(file)
        let aLink = document.createElement('a')
        let title = decodeURI(decodeURI(titlename ? titlename.split('=')[1] : '作品列表'))
        aLink.href = url
        aLink.download = title
        aLink.click()
        aLink.remove()
        URL.revokeObjectURL(url)
        setTimeout(function () {
            toast({ tips: '操作成功' })
        }, 66);
    },
    downloadByBlob(blob, titlename, format = '.xlsx') {
        // 'application/vnd.ms-excel'
        let file = new Blob([blob], { type: 'application/vnd.ms-excel' })
        let url = URL.createObjectURL(file)
        let aLink = document.createElement('a')
        let title = decodeURI(decodeURI(titlename ? titlename.split('=')[1] : '作品列表'))
        aLink.href = url
        aLink.download = title
        aLink.click()
        aLink.remove()
        URL.revokeObjectURL(url)
    },
    uploadFile(file, url, that) {
        let formData = new FormData()
        formData.append('file', file.flie||'')
        formData.append('claimSetId', file.claimSetId||'')
        formData.append('year', file.year||'')
        formData.append('fid', file.fid||'')
        console.log(formData)
        axios.post(url, formData).then(res => {
            console.log('^^^^^^^^^^^^^^^', res)

            if (res.data.code == 200) {
                // alert('hahahah')
                // this.$toast({tips:'上傳成功'})
                // this.$refs.upload.clearFiles()
                // setTimeout(function() {
                // console.warn('=============',!isNaN(res.data.data),Number(res.data.data))
                if (Number(res.data.data)) {
                    toast({ tips: '上传成功' + res.data.data + '条数据' })
                    // that.getClaimList()
                } else {
                    let MessageBox = res.data.data || res.data.message || res.message
                    toast({ tips: MessageBox })
                }
                if (that.currentPage) {
                    that.searchFn(that.currentPage)
                } else {
                    that.getClaimList()
                }
                // this.$refs.upload.clearFiles()
                // }, 0);
            } else {
                let MessageBox = res.data.data || res.data.message || res.message
                // setTimeout(function() {
                toast({ tips: MessageBox })
                // }, 0);
                // this.$toast({tips:res.data.message})
            }
        })
    },
}

export default utils
