<template>
<div>
    <el-dialog :visible.sync="show" width="1200px" title="Note" :close-on-click-modal="false">
        <!-- title: ipbaseno  + name -->
        <div class="clear" style="width: 1070px;margin: auto;margin-bottom: 20px">
            <div style="float: left;width: 620px;text-align: left;">
                <el-input v-model="searchInfo.ipBaseNo" placeholder="IP Base No" style="width: 160px;" @keyup.enter.native="onSubmit()"></el-input>
                <el-input v-model ="searchInfo.subject" placeholder="Subject" style="width: 130px;" @keyup.enter.native="onSubmit()"></el-input>
                <el-select v-model="searchInfo.status"  style="width: 140px;">
                    <el-option :label="'All Status'" :value="''"></el-option>
                    <el-option :label="'Incomplete'" :value="false"></el-option>
                    <el-option :label="'Completed'" :value="true"></el-option>
                </el-select>
                <el-select v-model="searchInfo.category" style="width: 160px;">
                    <el-option :label="'All Category'" :value="''"></el-option>
                    <template v-for="(item,index) in categoryData">
                        <el-option :key="index" :label="item.detailCode" :value="item.detailCode"></el-option>
                    </template>
                    <!-- <el-option :label="'Bank Update'" :value="'Bank Update'"></el-option>
                    <el-option :label="'Contact Update'" :value="'Contact Update'"></el-option>
                    <el-option :label="'Member Task'" :value="'Member Task'"></el-option>
                    <el-option :label="'Others'" :value="'Others'"></el-option> -->
                </el-select>
                <div style="margin-top: 20px;">
                    <el-input v-model="searchInfo.createUserName" placeholder="Created By" style="width: 120px;" @keyup.enter.native="onSubmit()"></el-input>
                    <label style="margin-right: 10px;">Order by: </label>
                    <el-select v-model="searchInfo.orderColumn" style="width: 160px;">
                        <el-option :label="'Created Date'" :value="'C'"></el-option>
                        <el-option :label="'Due Date'" :value="'D'"></el-option>
                    </el-select>
                    <label style="margin-left: 20px;">Desc: </label>
                    <el-switch v-model="searchInfo.isDesc" style="margin-right: 20px;"></el-switch>
                </div>
            </div>
            <div style="float: left;padding: 22px 0 0 70px;">
                <el-button type="primary" @click="onSubmit()">搜索</el-button>
                <el-button type="success" @click="addNote">新增</el-button>
                <el-button type="success" @click="exportFn">導出</el-button>
                <span class="clear-search" @click="clearSearch()">清除搜索</span>
            </div>
        </div>
        <el-table :empty-text="emptyText" :data="tableData">
            <el-table-column property="category" label="Category" min-width="150px"></el-table-column>
            <el-table-column property="subject" label="Subject" min-width="300px">
                <template slot-scope="scope">
                    <span :title="scope.row.subject" class="el-sl">{{scope.row.subject}}</span>
                </template>
            </el-table-column>
            <el-table-column property="createUserName" label="Create By" width="110px"></el-table-column>
            <el-table-column property="createTime" label="Date" width="120px">
                <template slot-scope="scope">
                    {{$utils.DATE(scope.row.createTime ,'yyyy-MM-dd')}}
                </template>
            </el-table-column>
            <el-table-column property="dueDate" label="Due Date" width="120px">
                <template slot-scope="scope">
                    {{$utils.DATE(scope.row.dueDate ,'yyyy-MM-dd')}}
                </template>
            </el-table-column>
            <el-table-column label="Status" width="150px">
                <template slot-scope="scope">
                    {{scope.row.status ? 'Completed' : 'Incomplete'}}
                </template>
            </el-table-column>
            <el-table-column
                label="operation"
                width="100px">
                <template slot-scope="scope">
                    <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                        <i class="el-icon-edit" title="編輯" @click="editNote(scope.$index,scope.row)"></i>
                        <i class="el-icon-delete" style="margin-left: 10px;" title="刪除" @click="deleteNote(scope.$index,scope)"></i>
                    </span>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :total="total" @current-change="onSubmit" :current-page="currentPage">
        </el-pagination>
    </el-dialog>
    <el-dialog :visible.sync="showAdd" v-if="showAdd" width="1100px" :title="addInfo.id ? '編輯NOTE' : '添加NOTE'" :close-on-click-modal="false">
        <el-form ref="addForm" :model="addInfo" :rules="rules" :inline="true" label-width="84px" style="text-align: left;">
            <div>
                <el-form-item label="Category" label-width="120px" required prop="category">
                    <el-select v-model="addInfo.category"  style="width: 160px;">
                        <template v-for="(item,index) in categoryData">
                            <el-option :key="index" :label="item.detailCode" :value="item.detailCode"></el-option>
                        </template>
                        <!-- <el-option :label="'Bank Update'" :value="'Bank Update'"></el-option>
                        <el-option :label="'Contact Update'" :value="'Contact Update'"></el-option>
                        <el-option :label="'Member Task'" :value="'Member Task'"></el-option>
                        <el-option :label="'Others'" :value="'Others'"></el-option> -->
                    </el-select>
                </el-form-item>
                <el-form-item label="Subject" required prop="subject">
                    <el-input v-model="addInfo.subject"  placeholder="Subject" style="width: 300px;"></el-input>
                </el-form-item>
                <el-form-item label="Due Data" label-width="120px">
                    <date-picker  v-model="addInfo.dueDate"  type="date" value-format="yyyy-MM-dd" format="yyyyMMdd"></date-picker>
                </el-form-item>
            </div>
            <el-form-item label="Task" label-width="120px">
                <el-input v-model="addInfo.task"  type="textarea" rows="6" style="width:700px;"></el-input>
            </el-form-item>
            <div>
                <el-form-item label="Created By" label-width="120px">
                    <el-input v-model="addInfo.createUserName"  type="text" readonly></el-input>
                </el-form-item>
                <el-form-item label="Date">
                    <date-picker  v-model="addInfo.createTime" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd" readonly></date-picker>
                </el-form-item>
            </div>
            <div>
                <el-form-item label="Completed By" label-width="120px">
                    <el-input v-model="addInfo.updateUserName"  type="text" readonly></el-input>
                </el-form-item>
                <el-form-item label="Date">
                    <date-picker v-model="addInfo.amendTime"  type="date" value-format="yyyy-MM-dd" format="yyyyMMdd" readonly></date-picker>
                </el-form-item>
            </div>
            
            <el-form-item label="Status" label-width="120px">
                <!-- <el-input type="text" v-model="statusStr"  readonly></el-input> -->
                <el-select v-model="addInfo.status"  style="width: 160px;">
                    <el-option :label="'Incomplete'" :value="false"></el-option>
                    <el-option :label="'Complete'" :value="true"></el-option>
                </el-select>
            </el-form-item>
            <div style="text-align: center;">
                <el-form-item>
                    <el-button type="primary" @click="addNotePre">Save</el-button>
                </el-form-item>
            </div>
            
        </el-form>
    </el-dialog>
</div>
</template>
<script>
import qs from 'qs'

export default {
    data(){
        return{
            show:false,
            searchInfo: {
                subject: '',
                status: '',
                category: '',
                createUserName: '',
                orderColumn: 'C',
                isDesc: false,

                ipBaseNo: ''
            },
            categoryData:[],
            tableData: [],
            total: 0,
            currentPage: 1 ,

            showAdd: false,
            addInfo: {
                category: '',
                subject: '',
                dueDate: '',
                ipBaseNo: '',
                task: '',
                status: false,

                createUserName: '',
                createTime: '',
                updateUserName: '',
                amendTime: '',
            },
            rules: {
                category: [
                    { required: true, message: 'category不能為空', trigger: 'blur' }
                ],
                subject: [
                    { required: true, message: 'subject不能為空', trigger: 'blur' }
                ],
            },
            emptyText:'暫無數據'

        }
    },
    computed: {
        statusStr: function(){
            return this.addInfo.status ? 'Completed' : 'Incomplete';
        }
    },
    props: ['noteInfo'],
    mounted(){
        this.getCategory()
    },
    methods: {
        /**
         * dialog 组件调用方式： 
         * 1、父级用v-if在html里加载组件,
         * 2、父级调用子级的init方法，将数据初始化，prop传给data等
         * 
         */
        init(){
            // this.searchInfo.ipBaseNo = this.noteInfo.ip_base_no;
            // this.show = true;
            // this.onSubmit();


            this.clearSearch();
            this.show = true;
        },
        clearSearch(){
            this.searchInfo = {
                subject: '',
                status: '',
                category: '',
                createUserName: '',
                orderColumn: 'C',
                isDesc: false,
                ipBaseNo: this.noteInfo.ip_base_no
            };
            this.onSubmit();
        },
        getCategory(){
            let code={
                code: 'IPI_NOTE'
            }
            // 
            this.$http.get('/sys/data/detailList', {params: code}).then(res => {
                if (res.success){
                    console.log('getCategory')
                    this.categoryData = res.data.data
                    console.log(this.categoryData)
                }
            })
        },
        onSubmit(page){
            let params = this.searchInfo;
            params.page_num = page ? page : 1;
            params.page_size = 10;
            this.emptyText = '數據加載中';
            this.$http.get('/ip/listNote', {params: this.searchInfo}).then(res => {
                if (res.success){
                    this.tableData = res.data.data.list;
                    this.total = res.data.data.total;
                    this.currentPage = page ? page : 1;
                    if(! this.tableData || this.tableData.length == 0){
                        this.emptyText = '暫無數據';
                    }
                }else{
                    this.emptyText = '暫無數據';
                }
            })
        },
        editNote(index, row){
            
        },
        addNote(){
            this.showAdd = true;
            this.addInfo = {
                category: '',
                subject: '',
                dueDate: '',
                ipBaseNo: '',
                task: '',
                status: false,

                createUserName: '',
                createTime: '',
                updateUserName: '',
                amendTime: '',
            }
        },
        addNotePre(){
            this.$refs.addForm.validate( valid => {
                if(valid){
                    this.addNoteFn();
                }
            })
        },
        addNoteFn(){
            this.addInfo.ipBaseNo = this.noteInfo.ip_base_no;
            this.$http.post('/ip/editNote', this.addInfo).then( res => {
                if(res.success){
                    if(this.addInfo.id){
                        this.$toast({tips: '編輯成功'})
                        this.onSubmit(this.currentPage);
                    }else{
                        this.$toast({tips: '添加成功'})
                        this.onSubmit();
                    }
                    this.showAdd = false;
                }
            })
        },

        editNote(index, row){
            this.addInfo = row;
            this.showAdd = true;
        },

        exportFn(){
            let ajaxData = this.$utils.copy(this.searchInfo);
            delete ajaxData.page_num;
            delete ajaxData.page_size;

            this.$utils.downloadGet('/ip/reportNoteExcel', ajaxData);
        },
        deleteNote (index, rows) {
            this.$msgbox.confirm(`確定進行[删除]操作?`, '提示', {
                confirmButtonText: '確定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.deleteNoteFn(index);
                
            })
        },
        deleteNoteFn(index){
            this.$http.delete('/ip/delNote/' + this.tableData[index].id).then( res => {
                if(res.success){
                    this.$toast({tips: '刪除成功'})
                    // this.tableData.splice(index, 1);
                    this.onSubmit(this.currentPage);
                }
                // this.tableresult = '數據加載中...'
            })
        },
    }
    
}
</script>
<style lang="scss" scoped>
    /deep/ .el-dialog__title{
        font-weight: bold;
    }
    /deep/ .el-dialog__body{
        text-align: center;
        padding-top: 0;
    }
</style>