<template>
  <div class="export">
    <el-form :inline="true" class="search-form" @keyup.enter.native="queryList(1)">
      <el-form-item>
        <el-input type="text" v-model="searchData.sqlName" placeholder="模板名稱" style="width: 160px"></el-input>
      </el-form-item>
      <el-form-item prop="createTime">
        <date-picker v-model="searchData.startTime" type="date" format="yyyyMMdd" placeholder="startTime" style="width: 160px">
        </date-picker>
        -
        <date-picker v-model="searchData.endTime" type="date" format="yyyyMMdd" placeholder="endTime" style="width: 160px">
        </date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="queryList(1)" v-if="isAuth('export:export-diy:list:find')">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="show=true" v-if="isAuth('export:export-diy:list:configuration')">配置導出</el-button>
        <!-- <el-button type="primary" @click="addFn">添加導出模板</el-button> -->
      </el-form-item>
      <el-form-item>
        <span class="clear-search" @click="clearSearch()" v-if="isAuth('export:export-diy:list:find')"> 清除搜索</span>
      </el-form-item>
    </el-form>
    <el-table :data="tableData" border stripe style="width: 100%" class="table-fixed" v-loading="loading" :empty-text="emptyText">
      <el-table-column prop="sqlName" label="導出模板名稱" width="320">
      </el-table-column>
      <el-table-column prop="remark" label="模板备注" width="120">
        <template slot-scope="scope">
          <span class="el-sl" :title="scope.row.remark">{{scope.row.remark}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="createUserName" label="創建人" width="100">
      </el-table-column>
      <el-table-column prop="type" label="导出类别" width="120">
        <template slot-scope="scope">
          <span class="el-sl">根据{{scope.row.type?'sql':'模板'}}生成</span>
        </template>
      </el-table-column>
      <!-- <el-table-column
                prop="createTime"
                label="創建時間"
                width="200">
            </el-table-column> -->
      <el-table-column prop="amendTime" label="更新時間" width="200">
      </el-table-column>
      <el-table-column prop="status" label="导出状态" width="100">
        <template slot-scope="scope">
          <span class="el-sl">{{scope.row.status==0?'待生成':scope.row.status==1?'生成中':scope.row.status==2?'生成完成':scope.row.status==3?'生成失败':''}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="errorMsg" label="失敗原因" width="120">
      </el-table-column>
      <el-table-column prop="exportParam" label="數據導出參數" width="160">
      </el-table-column>
      <el-table-column prop="currentParam" label="目前參數" width="160">
      </el-table-column>
      <el-table-column label="operation" width="250">
        <template slot-scope="scope">
          <span style="width: 100%;display: inline-block;text-align: left;cursor: pointer">
            <span class="a-blue" v-if="isAuth('export:export-diy:list:create')&&scope.row.status==0" @click="generate(scope.row)" >生成</span>
            <span class="a-blue" v-if="isAuth('export:export-diy:list:change')&&scope.row.status!=1" @click="editFn(scope.row)">編輯</span>
            <!-- <span class="a-blue" v-if="scope.row.status==0 || scope.row.status==2" @click="editFn(scope.row)">編輯</span> -->
            <span class="a-blue" v-if="isAuth('export:export-diy:list:download')&&scope.row.status==2" @click="download(scope.row)">下載數據</span>
            <span class="a-blue" v-if="isAuth('export:export-diy:list:againCreate')&&(scope.row.status==2 || scope.row.status==3)" @click="generate(scope.row)">重新生成</span>
            <span class="a-red" v-if="isAuth('export:export-diy:list:del')&&scope.row.status!=1" @click="deleteHandle(scope.row)">刪除</span>
          </span>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination background layout="prev, pager, next" :total='total' @current-change="queryList" :current-page="currentPage">
    </el-pagination>
    <el-dialog :visible.sync="show" width="400px" title="選擇生成模板" :close-on-click-modal='false'>
      <div class="selectButton">
        <el-button type="primary" @click="addFn(1)">根據sql生成</el-button>
      </div>
      <div class="selectButton">
        <el-button type="primary" @click="addFn(0)">根據模板生成</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { Loading } from 'element-ui'
export default {
  name: 'export',
  data() {
    return {
      show: false,
      total: 0,
      currentPage: 1,
      tableData: [],tableresult:" ",
      loading: false,
      loadingB: null,
      searchData: {
        sqlName: '',
        startTime: '',
        endTime: ''
      },
      emptyText: '數據加載中',
    }
  },
  activated() {
    // if(this.$route.query.update){
    //     this.init();
    // }
    this.$nextTick(() => {
      if (this.$route.query.update) {
        let query = this.$route.query;
        delete query.update;
        this.queryList();
      }
    })
  },
  created() {
    this.queryList();
  },
  methods: {
    generate(row) {
      console.log(row)

      this.$msgbox.confirm('確定重新生成?', '提示', {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
          this.$http.get('/sys/export/generate/' + row.id).then(res => {
            if (res.success) {
              if (res.data.code == 200) {
                console.log(res)
                row.status = 1
              } else {
                this.$toast({ tips: res.data.message })
              }
            }
          })
        }).catch(() => {
      });
    },
    clearSearch() {
      this.searchData = {
        sqlName: '',
        startTime: '',
        endTime: ''
      }
      this.queryList()
    },
    queryList(page = 1) {
      let param = this.$utils.copy(this.searchData)
      param.startTime = param.startTime ? (param.startTime.replace(/-/g, '')) : '';
      param.endTime = param.endTime ? (param.endTime.replace(/-/g, '')) : '';
      param.pageNum = page
      param.pageSize = 10
      this.emptyText = '數據加載中';
      this.$http.get('/sys/export/listSysExportSql', { params: param }).then(res => {
        if (res.success) {
          if (res.data.code == 200) {
            this.tableData = res.data.data.list;
            if(! this.tableData || this.tableData.length == 0){
              this.emptyText = '暫無數據';
            }
            this.total = res.data.data.total;
            this.currentPage = page;
          } else {
            this.$toast({ tips: res.data.message })
          }
        }
              this.tableresult = this.tableData.length == 0 ? '暫無數據' : ' '
      })
    },
    addFn(data) {
      this.show = false
      if (data) {
        this.$router.push({ name: 'addSqlDiy' })
      } else {
        this.$router.push({ name: 'addDiy' })
      }
    },
    editFn(row) {
      if (row.type) {
        this.$router.push({ name: 'editSqlDiy', query: { type: 'edit', info: JSON.stringify(row), title: row.sqlName, nameId: row.id } })
      } else {
        this.$router.push({ name: 'editDiy', query: { type: 'edit', info: JSON.stringify(row), title: row.sqlName, nameId: row.id } })
      }
      return
    },
    download(row) {
      let ajaxData = {
        sql: row.sqlInfo,
        templateName: row.sqlName
      }
      // this.$http.get('/sys/export/download/'+row.id).then( res => {
      //     if(res.success){
      //         if(res.data.code == 200){
      //             console.log(res)
      //         }else{
      //             this.$toast({tips: res.data.message})
      //         }
      //     }
      // })
      console.log(row)
      let filePath = row.filePath.slice(row.filePath.lastIndexOf('.') + 1)
      if (filePath == 'zip') {
        this.loadingB = Loading.service({
          lock: true,
          text: 'Loading',
          background: 'rgba(0,0,0,0.3)'
        })
        this.$http.get('/sys/export/download/' + row.id, { responseType: 'blob' }).then(res => {
          this.loadingB.close();
          let blob = new Blob([res.data], { type: 'application/zip' })
          let url = window.URL.createObjectURL(blob)
          const link = document.createElement('a') // 创建a标签
          let title = decodeURI(decodeURI(res.headers["content-disposition"] ? res.headers["content-disposition"].split('=')[1] : '报表下载'))
          link.href = url
          link.download = title
          link.click()
          URL.revokeObjectURL(url) // 释放内存
        }).catch(err => {
          console.log(err)
        })
      } else {
        this.$utils.downloadGet('/sys/export/download/' + row.id);
      }

      // this.$utils.downloadGet('/sys/export/excel', ajaxData);
    },
    // 删除
    deleteHandle(row) {
      this.$msgbox.confirm('確定刪除?', '提示', {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteFn(row);
      }).catch(() => {
      });
    },
    deleteFn(row) {
      this.$http.delete('/sys/export/deleteTemplate/' + row.id).then(res => {
        if (res.success) {
          if (res.data.code == 200) {
            this.queryList(this.currentPage)
            this.$toast({ tips: '刪除成功' })
          } else {
            this.$toast({ tips: res.data.message })
          }

        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.selectButton {
  text-align: center;
  button {
    width: 200px;
    height: 50px;
  }
}
.selectButton:nth-of-type(2) {
  margin-top: 20px;
}
</style>


