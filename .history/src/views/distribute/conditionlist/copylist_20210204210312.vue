<template>
    <div>
        <el-button type="primary" class="btn" @click="add">新增</el-button>
        <el-table
            :data="tableData"
            border
            stripe
            style="width: 100%">
            <el-table-column
                prop="distNo"
                label="dist no">
            </el-table-column>
            <el-table-column
                prop="fromDistNo"
                label="from dist no">
            </el-table-column>
            <el-table-column 
                prop="fromListSourceCode"
                label="list source">
            </el-table-column>
            <el-table-column
                prop="fromListSourceDescription"
                label="list source desc"
                min-width="100">
            </el-table-column>
            <el-table-column
                prop="fromListCategoryCode"
                label="list category">
            </el-table-column>
            <el-table-column
                prop="categoryDescription"
                label="list category desc"
                min-width="110">
            </el-table-column>
            <el-table-column
                prop="toListSourceCode"
                label="to list source">
            </el-table-column>
            <el-table-column
                prop="toListCategoryCode"
                label="to list category"
                min-width="100">
            </el-table-column>
            <el-table-column
                prop="toCategoryDescription"
                label="to category desc"
                min-width="100">
            </el-table-column>
            <el-table-column
                prop="startPerfDate"
                label="begin perf date"
                min-width="100">
                <template slot-scope="scope">
                    {{formatDate(scope.row.startPerfDate)}}
                </template>
            </el-table-column>
            <el-table-column
                prop="endPerfDate"
                label="end perf date">
                <template slot-scope="scope">
                    {{formatDate(scope.row.endPerfDate)}}
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :total="total" @current-change="handleCurrentChange">
        </el-pagination>


    </div>
</template>

<script>
    export default {
        name: 'copylist',
        props: ['distNo'],
        data () {
            return {
                tableData:[],
                page_num:1,
                total:1
            }
        },
        mounted(){
            this.getList()
        },
        methods:{
            formatDate(time){
                return time.split(' ')[0]
            },
            getList(){
                console.log('copy')
                let params = {
                    distNo:this.distNo,
                    page_num:this.page_num
                }
                this.$http.get('/distributionSpecialCondition/listDistListCopyFilterWithPage',{params}).then(res => {
                    console.log(res)
                    if(res.success){
                        this.tableData = res.data.data.list
                        this.total = res.data.data.total
                    }
                })
            },
            handleCurrentChange(val){
                this.page_num = val
                this.getList()
            },
            add(){
                this.$router.push({name:'distribute-addcopy',params: {distNo:this.distNo}})
            }
        }
    }
</script>

<style scoped>
    .btn{
        margin-bottom: 10px;
    }

</style>
