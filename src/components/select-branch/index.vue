<template>
<div>
    <el-dialog :visible.sync="show" width="700px" title="Branch" :close-on-click-modal="false">
        <div style="width: 520px;margin: auto;margin-bottom: 20px">
            <el-input v-model.trim="searchInfo.branchNo" @keyup.enter.native="onSubmit()" placeholder="branchNo" style="width: 140px;"></el-input>
            <!-- <el-input v-model.trim="searchInfo.bankNo" placeholder="bankNo" style="width: 140px;" readonly></el-input> -->
            <el-input v-model="searchInfo.branchName" @keyup.enter.native="onSubmit()" placeholder="branchName" style="width: 160px;"></el-input>
            <el-button type="primary" @click="onSubmit()">查詢</el-button>
            <span class="clear-search" @click="clearSearch()">清除搜索</span>
        </div>
        <el-table :empty-text="emptyText"   :data="tableData">
            <el-table-column property="branchNo" label="branchNo" width="230px"></el-table-column>
            <el-table-column property="branchName" label="branchName" width="300px"></el-table-column>
            <el-table-column
                label="operation">
                <template slot-scope="scope">
                    <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                        <i class="el-icon-check" @click="checkBranch(scope.$index,scope.row)"></i>
                    </span>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :total="total" @current-change="onSubmit" :current-page="currentPage">
        </el-pagination>
    </el-dialog>
</div>
</template>
<script>

export default {
    data(){
        return{
            emptyText:'暫無數據',
            show:false,
            searchInfo: {},
            tableData: [],
            total: 0,
            currentPage: 1 
        }
    },
    props: {
        search: {
            type: Object,
            default: () => {
                return {
                    bankNo: '',
                    branchName: '',
                    branchNo: '',
                }

            }
        },
    },

    methods: {
        /**
         * dialog 组件调用方式： 
         * 1、父级用v-if在html里加载组件,
         * 2、父级调用子级的init方法，将数据初始化，prop传给data等
         * 
         */
        init(){
            this.show = true;
            this.searchInfo = this.$utils.copy(this.search);
            this.onSubmit();
        },
        clearSearch(){
            this.searchInfo = {
                bankNo: this.searchInfo.bankNo,
                branchName: '',
                branchNo: '',
            };

            this.onSubmit();
        },
        onSubmit(page){
            let params = this.searchInfo;
            params.page_num = page ? page : 1;
            params.page_size = 10;
            this.emptyText = '數據加載中';
            this.$http.get('/ref/bank/branch/page', {params: params}).then(res => {
                if (res.success && res.data.code == '200') {
                    this.tableData = res.data.data.list;
                    if(! this.tableData || this.tableData.length == 0){
                        this.emptyText = '暫無數據';
                    }
                    this.tableData.forEach( (item,index) => {
                        let no = '';
                        for(let i =0; i<7-(item.id + '').length;i++){
                            no += '0';
                        }
                        no += item.id;
                        this.tableData[index].id = no;
                    })
                    this.total = res.data.data.total;
                    this.currentPage = page ? page : 1;
                }else{
                    this.$toast({tips: res.data.message})
                }
            })
        },
        checkBranch(index, row){
            this.$emit('checkBranch', row);
            this.show = false;
        }
    }
    
}
</script>
<style lang="scss" scoped>
    /deep/ .el-dialog__title{
        font-weight: bold;
    }
    /deep/ .el-dialog__body{
        text-align: center;
        padding-top: 0;
    }
</style>