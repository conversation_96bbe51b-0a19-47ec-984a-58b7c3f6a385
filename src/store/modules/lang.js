import en from '@/i18n/lang/en'
import cn from '@/i18n/lang/cn'
export default {
  namespaced: true,
  state: {
    language: '',
    biaoyanformWorks: {},
    // mytableData6: [],
    /**
     * tabShow 会员是否是161 的会员
     * 
     */
    tabShow: false,
    formDummy: {},
    baseNO: '',
    index: '',
    id: '',
    socId: '',
    //語言包
    lang: cn
  },
  mutations: {
    updateLanguage (state, language) {
      state.language = language;
      if(language == 'cn'){
        state.lang = cn;
      }else{
        state.lang = en;
      }
      
    },
    updateFormWorks (state, biaoyanformWorks) {
      state.biaoyanformWorks = biaoyanformWorks
    },
    updateTabShow (state, tabShow) {
      state.tabShow = tabShow
    },
    updateFormDummy  (state, formDummy) {
      state.formDummy = formDummy
    },
    updateBaseNO  (state, baseNO) {
      state.baseNO = baseNO
    },
    updateIndex (state, index) {
      state.index = index
    },
    updateId (state, id) {
      state.id = id
    },
    updateSocId (state, socId) {
      state.socId = socId
    }
    // updateTableData6 (state, mytableData6) {
    //   state.mytableData6 = mytableData6
    // }
  }
}
