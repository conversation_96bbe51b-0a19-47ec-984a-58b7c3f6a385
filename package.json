{"name": "renren-fast-vue", "version": "1.2.2", "description": "", "author": "", "private": true, "scripts": {"dev": "webpack-dev-server --inline --progress --config build/webpack.dev.conf.js", "start": "npm run dev", "unit": "jest --config test/unit/jest.conf.js --coverage", "e2e": "node test/e2e/runner.js", "test": "npm run unit && npm run e2e", "lint": "eslint --ext .js,.vue src test/unit/specs test/e2e/specs", "build": "gulp"}, "dependencies": {"axios": "0.17.1", "babel-plugin-component": "0.10.1", "babel-polyfill": "6.26.0", "element-ui": "2.13.0", "gulp-concat": "2.6.1", "gulp-load-plugins": "1.5.0", "gulp-replace": "0.6.1", "gulp-shell": "0.6.5", "lodash": "4.17.5", "node-sass": "^4.12.0", "npm": "^6.9.0", "sass-loader": "6.0.6", "svg-sprite-loader": "3.7.3", "vue": "2.5.2", "vue-calendar-component": "^2.8.2", "vue-cookie": "1.1.4", "vue-i18n": "^8.4.0", "vue-router": "3.0.1", "vuex": "3.0.1"}, "devDependencies": {"autoprefixer": "7.1.2", "babel-core": "6.22.1", "babel-eslint": "7.1.1", "babel-jest": "21.0.2", "babel-loader": "7.1.1", "babel-plugin-dynamic-import-node": "1.2.0", "babel-plugin-transform-es2015-modules-commonjs": "6.26.0", "babel-plugin-transform-runtime": "6.22.0", "babel-preset-env": "1.3.2", "babel-preset-stage-2": "6.22.0", "babel-register": "6.22.0", "chalk": "2.3.0", "chromedriver": "2.27.2", "copy-webpack-plugin": "4.0.1", "cross-spawn": "5.0.1", "css-loader": "0.28.0", "eventsource-polyfill": "0.9.6", "extract-text-webpack-plugin": "3.0.0", "file-loader": "1.1.4", "friendly-errors-webpack-plugin": "1.6.1", "gulp": "^4.0.2", "html-webpack-plugin": "2.30.1", "jest": "21.2.0", "jest-serializer-vue": "0.3.0", "nightwatch": "0.9.12", "node-notifier": "5.1.2", "optimize-css-assets-webpack-plugin": "3.2.0", "ora": "1.2.0", "portfinder": "1.0.13", "postcss-import": "11.0.0", "postcss-loader": "2.0.8", "rimraf": "2.6.0", "selenium-server": "3.0.1", "semver": "5.3.0", "shelljs": "0.7.6", "uglifyjs-webpack-plugin": "1.1.1", "url-loader": "0.5.8", "vue-jest": "1.0.2", "vue-loader": "14.2.4", "vue-style-loader": "3.0.1", "vue-template-compiler": "2.5.2", "webpack": "3.6.0", "webpack-bundle-analyzer": "2.9.0", "webpack-dev-server": "2.9.1", "webpack-merge": "4.1.0"}, "engines": {"node": ">= 8.11.1", "npm": ">= 5.6.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}