<template>
    <div class="originalbox">
        <top-bar :steps="steps" :showTransfer="(workId && !from) ? true: false" :showConvert="(workId && !from) ? showConvert : false"  :showBatchCreate="workId ? true: false" :showDistribution=" (workId && from != 'convert')  ? true : false"  
                @save="saveWrk" @transfer="transfer" @convert="convert" v-if="top_barShow">
            <slot v-if="workId">
                <div class="work-header">
                    <!-- <label><span>Work No: </span>{{formOriginal.workId}}</label> -->
                    <label><span>Soc/Work No:</span> {{formOriginal.workSocietyCode}}/{{formOriginal.workId}}</label>
                    <!-- <label><span>Soc:</span> {{formOriginal.workSocietyCode}}</label> -->
                    <label class="title" :title="headerInfo.title"><span>Title:</span> {{headerInfo.title}}</label>
                    <label :title="headerInfo.performersAll"><span>Perf:</span> {{headerInfo.performers}}</label>
                    <label :title="headerInfo.authorComposerAll"><span>Ar&C:</span> {{headerInfo.authorComposer}}</label>
                </div>
            </slot>
        </top-bar>
            <slot v-if="!top_barShow&&workId">
                <div class="work-header">
                    <!-- <label><span>Work No: </span>{{formOriginal.workId}}</label> -->
                    <label><span>Soc/Work No:</span> {{formOriginal.workSocietyCode}}/{{formOriginal.workId}}</label>
                    <!-- <label><span>Soc:</span> {{formOriginal.workSocietyCode}}</label> -->
                    <label class="title" :title="headerInfo.title"><span>Title:</span> {{headerInfo.title}}</label>
                    <label :title="headerInfo.performersAll"><span>Perf:</span> {{headerInfo.performers}}</label>
                    <label :title="headerInfo.authorComposerAll"><span>Ar&C:</span> {{headerInfo.authorComposer}}</label>
                </div>
            </slot>
        <el-collapse v-model="activeNames" class="contentbox">
            <el-collapse-item class="step-jump works" title="works" name="1">
                <div class="boxline p-t-10">
                    <el-form :inline="true" :model="formOriginal" :rules="rulesOriginal" ref="formOriginal" label-position="left" class="demo-form-inline">
                        <!-- <div class="f-l"> -->
                        <el-form-item label="Title" prop="title">
                            <el-input v-model="formOriginal.title" placeholder=""></el-input>
                        </el-form-item>
                        <el-form-item label="OT Work No" prop="refWorkId">
                            <el-input ref="refWorkId" v-model="formOriginal.refWorkId" placeholder="雙擊查詢" @dblclick.native="getWork()" style="width: 130px;" readonly></el-input>
                        </el-form-item>
                        <el-form-item label="OT Soc" prop="refWorkSociety">
                            <el-input v-model="formOriginal.refWorkSociety" placeholder="雙擊查詢" style="width: 80px;" @dblclick.native="getWork()"  readonly></el-input>
                        </el-form-item>
                        <el-form-item label="OT Title" >
                            <el-input v-model="formOriginal.toTitle" placeholder="雙擊查詢" @dblclick.native="getWork()" readonly></el-input>
                        </el-form-item>
                        <el-form-item label="Local" v-if="workId">
                            <el-checkbox v-model="localchecked" disabled></el-checkbox>
                        </el-form-item>
                        <el-form-item label="Genre">
                            <el-input v-model="formOriginal.genre" placeholder="雙擊查詢" readonly @dblclick.native="genreInit" style="width: 80px;"></el-input>
                        </el-form-item>
                        <!-- </div> -->
                        <div class="f-l double">
                            <el-form-item label="Duration" prop=""> <!-- prop="durationM" -->
                                <el-input style="width: 60px" v-model.number="formOriginal.durationM" placeholder="" @change="changeDuration('durationM')"></el-input>
                            </el-form-item>
                            :
                            <el-form-item style="margin-left: 5px; margin-right: 15px;" prop=""><!-- prop="durationS" -->
                                <el-input style="width: 60px" v-model.number="formOriginal.durationS" placeholder="" @change="changeDuration('durationS')"></el-input>
                            </el-form-item>
                        </div>
                        <el-form-item label="PerLanguage">
                            <el-input v-model="formOriginal.performLanguage" readonly placeholder="雙擊查詢" @dblclick.native="languageInit('pre')" style="width: 80px;"></el-input>
                        </el-form-item>
                        <el-form-item label="Title Language">
                            <el-input v-model="formOriginal.titleLanguage" readonly placeholder="雙擊查詢" @dblclick.native="languageInit('title')" style="width: 80px"></el-input>
                        </el-form-item>
                        <el-form-item label="PublishDate">
                            <!-- <el-input v-model="formOriginal.publishAirDate" v-dateFmt></el-input> -->
                            <date-picker v-model="formOriginal.publishAirDate" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd"></date-picker>
                        </el-form-item>
                        <el-form-item label="ISWC">
                            <!-- <el-input v-model="formOriginal.iSWC" placeholder="" style="width: 140px;"></el-input> -->
                                <el-select v-model="currentIswc" @change="changeIswc">
                                     <el-option
                                        v-for="item in iswcList"
                                        :key="item.id"
                                        :label="item.iswc"
                                        :value="item.id">
                                    </el-option>
                                </el-select>
                        </el-form-item>
                    </el-form>
                </div>
            </el-collapse-item>
            <el-collapse-item class="step-jump" title="Performer " name="2">
                <performer ref="performer" :tableData="tableData1"></performer>
            </el-collapse-item>
            <!--IP Share-->
            <el-collapse-item class="step-jump component-class" title="IP Share" name="3">
                <ipshare ref="ipshare" :tableRight="tableRight" :baseInfo="formOriginal" :wrkWorkRightMap="wrkWorkRightMap" :isADP="isADP" :refWrkWorkRightMap="refWrkWorkRightMap"></ipshare>
            </el-collapse-item>
            <!--Source-->
            <el-collapse-item class="step-jump" title="Source" name="4">
                <work-source ref="getSource" :tableData="tableData3"></work-source>
            </el-collapse-item>
            <!--Other Titles-->
            <el-collapse-item class="step-jump" title="Other Titles" name="5">
                <other-title ref="otherTitle" :tableData="tableData4"></other-title>
            </el-collapse-item>
            <!--Remark-->
            <el-collapse-item class="step-jump" title="Remark" name="6">
                <div class="boxline">
                    <el-table
                        :data="tableData5"
                        border
                        stripe
                        style="width: 100%">
                        <el-table-column
                            prop="inputSoc"
                            label="Soc">
                            <template slot-scope="scope">
                                <el-input v-model=scope.row.inputSoc placeholder="" readonly></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="remark"
                            label="Remark">
                            <template slot-scope="scope">
                                <el-input type="textarea" v-model=scope.row.remark placeholder=""></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column
                            label="operation"
                            width="100">
                            <template slot-scope="scope">
                                <span class="spanright">
                                    <i class="el-icon-delete" @click="deletedata5(scope.$index,tableData5)"></i>
                                </span>
                            </template>
                        </el-table-column>
                    </el-table>
                    <div class="add-new">
                        <el-button type="primary" @click="adddata5()" v-if="isAuth('works:base:change')">新 增</el-button>
                    </div>
                </div>
            </el-collapse-item>
            <!--ISRC -->
            <el-collapse-item class="isrcpo" title="ISRC" name="7">
                <div class="boxline">
                    <div id='scrollBox' style="max-height: 320px; overflow: auto;">
                        <el-table
                            :data="tableData6"
                            border
                            stripe
                            style="width: 100%"
                            id='scrollTable'>
                            <el-table-column
                                prop="isrc"
                                label="ISRC">
                                <template slot-scope="scope">
                                    <el-input v-model=scope.row.isrc placeholder=""></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="inputSoc"
                                label="SOC">
                                <template slot-scope="scope">
                                    <el-input v-model=scope.row.inputSoc placeholder=""></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="operation"
                                width="100">
                                <template slot-scope="scope">
                                    <span class="spanright">
                                        <i class="el-icon-delete" @click="deletedata6(scope.$index,tableData6)"></i>
                                    </span>
                                </template>
                            </el-table-column>
                        </el-table>
                        <div class="t-c" style="height: 40px; line-height: 40px;" v-if="wiLength > 1">
                            <span :class="wiLength != scrollPage?'pointer':''" @click='lookMore()'>{{more}} </span>
                        </div>
                    </div>
                    <div class="add-new">
                        <el-button type="primary" @click="adddata6()" v-if="isAuth('works:base:change')">新 增</el-button>
                    </div>
                </div>
            </el-collapse-item>
            <!--关联作品-->
            <!-- <el-collapse-item class="step-jump component-class" title="Ref Work" name="8" v-show="workId"> -->
            <el-collapse-item class="step-jump component-class" title="Ref Work" name="8">
                <ref-work ref="refWork" :tableData="wrkWorkOtherSocCodeList" :baseInfo="formOriginal"></ref-work>
            </el-collapse-item>
            <!--Album-->
            <el-collapse-item class="step-jump component-class" title="Album" name="9">
                <album ref="album" :tableData="wrkWorkAlbumList" :baseInfo="formOriginal"></album>
            </el-collapse-item>
            <el-collapse-item class="step-jump" title="Sound Carrier" name="10">
                <div class="boxline p-t-10 clear carrier">
                    <p v-for="(item,index) in soundCarrierSet" :key="index">{{item}}</p>
                </div>
            </el-collapse-item>
                        <!--ISWC-->
            <el-collapse-item class="isrcpo" title="ISWC" name="11">
                <el-form :inline="true" :model="iswcdata" label-position="right" label-width="150px" class="demo-form-inline">
                    <div class="boxline">
                        <!-- <div>
                            <el-form-item label="iswc">
                                <el-select v-model="currentIswc" @change="changeIswc">
                                     <el-option
                                        v-for="item in iswcList"
                                        :key="item.id"
                                        :label="item.iswc"
                                        :value="item.id">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </div> -->
                        <div>
                            <el-form-item label="iswc">
                                <el-input v-model="iswcdata.iswc" placeholder="" @change="updateIswc"></el-input>
                            </el-form-item>
                            <el-form-item label="source">
                                <el-input v-model="iswcdata.source" placeholder=""></el-input>
                            </el-form-item>
                            <!-- <el-form-item label="Country">
                                <el-input v-model="iswcdata.country" placeholder=""></el-input>
                            </el-form-item> -->
                            <el-form-item label="Duplicate Status">
                                <el-input v-model="iswcdata.duplicateStatus" ></el-input>
                            </el-form-item>
                            <el-form-item label="Music Arr Code">
                                <el-input v-model="iswcdata.musicArrCode" ></el-input>
                            </el-form-item>
                            <el-form-item label="Lyric Adapt Code">
                                <el-input v-model="iswcdata.lyricAdaptCode" ></el-input>
                            </el-form-item>
                            <el-form-item label="Version Type">
                                <el-input v-model="iswcdata.versionType" placeholder="" ></el-input>
                            </el-form-item>
                            <el-form-item label="Version Iswc">
                                <el-input v-model="iswcdata.versionIswc" placeholder=""></el-input>
                            </el-form-item>
                            <el-form-item label="Version Title">
                                <el-input v-model="iswcdata.versionTitle" placeholder=""></el-input>
                            </el-form-item>
                            <el-form-item label="Version Ip">
                                <el-input v-model="iswcdata.versionIp" placeholder=""></el-input>
                            </el-form-item>
                            <el-form-item label="Version Lang">
                                <el-input v-model="iswcdata.versionLangCode" placeholder=""></el-input>
                            </el-form-item>
                            <el-form-item label="Excerpt Type">
                                <el-input v-model="iswcdata.excerptType" placeholder=""></el-input>
                            </el-form-item>
                            <el-form-item label="Excerpt Iswc">
                                <el-input v-model="iswcdata.excerptIswc" placeholder=""></el-input>
                            </el-form-item>
                            <el-form-item label="Excerpt Title">
                                <el-input v-model="iswcdata.excerptTitle" placeholder=""></el-input>
                            </el-form-item>
                            <el-form-item label="Excerpt Ip">
                                <el-input v-model="iswcdata.excerptIp" placeholder=""></el-input>
                            </el-form-item>
                            <el-form-item label="Excerpt Lang">
                                <el-input v-model="iswcdata.excerptLangCode" placeholder="" style="width: 250px"></el-input>
                            </el-form-item>
                            <el-form-item label="Composite Type">
                                <el-input v-model="iswcdata.compositeType" placeholder=""></el-input>
                            </el-form-item>
                            <el-form-item label="Composite Title">
                                <el-input v-model="iswcdata.compositeTitle" placeholder="" style="width: 250px"></el-input>
                            </el-form-item>

                            <el-form-item label="Composite Iswc">
                                <el-input v-model="iswcdata.compositeIswc" placeholder=""></el-input>
                            </el-form-item>
                            <el-form-item label="Composite Ip">
                                <el-input v-model="iswcdata.compositeIp" placeholder="" style="width: 250px"></el-input>
                            </el-form-item>

                            <el-form-item label="Composite Dur">
                                <el-input v-model="iswcdata.compositeDur" placeholder=""></el-input>
                            </el-form-item>
                            <el-form-item label="Preferred">
                                <el-input v-model="iswcdata.preferred" placeholder="" style="width: 250px"></el-input>
                            </el-form-item>
                        </div>
                    </div>
                </el-form>
            </el-collapse-item>
        </el-collapse>
        <el-dialog :visible.sync="genreShow" :before-close='genreClose' :close-on-click-modal="false" width="750px">
            <div style="width: 500px;margin: auto;margin-bottom: 20px">
                <el-input placeholder="請輸入曲風" @keyup.enter.native="workSearch" v-model="workInput" class="input-with-select">
                    <el-button slot="append" icon="el-icon-search" @click="workSearch"></el-button>
                </el-input>
            </div>
            <el-table :empty-text="emptyText" stripe :data="genreData">
                <el-table-column property="genreDetail" label="曲風詳情" width="120"></el-table-column>
                <el-table-column property="workType" label="作品類型" width="120"></el-table-column>
                <el-table-column property="usageFlag" label="使用標識" width="120"></el-table-column>
                <el-table-column property="description" label="描述"></el-table-column>
                <el-table-column
                    label="operation"
                    width="100">
                    <template slot-scope="scope">
                        <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                            <i class="el-icon-check" @click="checkedGenre(scope.$index,scope.row)"></i>
                        </span>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                background
                layout="prev, pager, next"
                :total=genreTotal @current-change="genreHandleCurrentChange">
            </el-pagination>
        </el-dialog>

        <el-dialog :visible.sync="languageShow" :before-close='languageClose' :close-on-click-modal="false">
            <div style="width: 500px;margin: auto;margin-bottom: 20px">
                <el-input placeholder="請輸入語言编碼" @keyup.enter.native="languageSearch" v-model="languageCodeInput" class="input-with-select">
                    <el-button slot="append" icon="el-icon-search" @click="languageSearch"></el-button>
                </el-input>
            </div>
            <el-table :empty-text="emptyText1" stripe :data="languageData">
                <el-table-column property="languageCode" label="語言编碼"></el-table-column>
                <el-table-column property="description" label="描述" ></el-table-column>
                <el-table-column
                    label="operation"
                    width="100">
                    <template slot-scope="scope">
                        <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                            <i class="el-icon-check" @click="checkedLanguage(scope.$index,scope.row)"></i>
                        </span>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                background
                layout="prev, pager, next"
                :total=languageTotal @current-change="languageHandleCurrentChange">
            </el-pagination>
        </el-dialog>

        <!-- <el-dialog :visible.sync="toWorkShow">
            <div style="width: 600px;margin: auto;margin-bottom: 20px">
                <el-input style="width: 240px" placeholder="請輸入workNo" v-model='selectWork.workId'></el-input>
                <el-input style="width: 240px" placeholder="請輸入title" v-model="selectWork.title" class="input-with-select">
                    <el-button slot="append" icon="el-icon-search" @click="worknoSearch"></el-button>
                </el-input>
            </div>
            <el-table :empty-text="tableresult"   stripe :data="worknoTableData">
                <el-table-column prop="title_en" label="title">
                    <template slot-scope="scope">
                        {{scope.row.title||scope.row.titleEn}}
                    </template>
                </el-table-column>
                <el-table-column property="workSocietyCode" label="Soc"></el-table-column>
                <el-table-column property="workId" label="workNo"></el-table-column>
                <el-table-column
                    label="operation"
                    width="100">
                    <template slot-scope="scope">
                        <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                            <i class="el-icon-check" @click="checkedTableWorkno(scope.row)"></i>
                        </span>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                background
                layout="prev, pager, next"
                :total="toWorkShowTotal" @current-change="genreHandleCurrentChange">
            </el-pagination>
        </el-dialog> -->

        <transfer-dialog :transfer-info="transferInfo" :show-transfer="showTransfer" @change="changeValue" v-if="showTransfer"></transfer-dialog>
        <select-work v-if="workTableVisible" ref="selectWorkCom" :search="workSearchData" @checkWork="checkWork"></select-work>
    </div>
</template>

<script>
    import axios from '../../../utils/httpRequest'
    import qs from 'qs'
    import transferDialog from '../dialog/transfer'
    import otherTitle from '../../demo/othertitle'
    import ipshare from '../../demo/ipshare'
    import performer from '../../demo/performer'
    import workSource from '../../demo/worksource'
    import selectWork from '../../../components/select-work';
    import refWork from '../../demo/ref-work'
    import album from '../../demo/album'

    export default {
        name: 'adaptedinfo',
        data () {
            var checkDurationM = (rule, value, callback) => {
                if (value < 0 || value > 1000) {
                    callback(new Error('不能大於1000min或者小於0min！'))
                } else {
                    callback()
                }
            }
            var checkDurationS = (rule, value, callback) => {
                if (value < 0 || value > 59) {
                    callback(new Error('不能大於59s或者小於0s！'))
                } else {
                    callback()
                }
            }
            var validateNameInput = (rule, value, callback) => {
                if (!value) {
                    return callback(new Error('此項必填！'))
                } else {
                    callback()
                }
            }
            var validateTypeInput = (rule, value, callback) => {
                if (!value) {
                    return callback(new Error('此項必填！'))
                } else {
                    callback()
                }
            }
            var validateIdInput = (rule, value, callback) => {
                if (!value) {
                    return callback(new Error('此項必填！'))
                } else {
                    callback()
                }
            }
            var validateDateInput = (rule, value, callback) => {
                if (!value) {
                    return callback(new Error('此項必填！'))
                } else {
                    callback()
                }
            }
            var validateSocInput = (rule, value, callback) => {
                if (!value) {
                    return callback(new Error('此項必填！'))
                } else {
                    callback()
                }
            }
            return {
                top_barShow:true,
                tableData3: [
                    // { sourceType: '', notifySouceId: '', source: '', name: '', notifyDate: '', inputSoc: '' }
                ],
                tableRules: {
                    sourceType: [{ validator: validateTypeInput, trigger: 'blur' }],
                    notifySouceId: [{ validator: validateIdInput, trigger: 'blur' }],
                    name: [{ validator: validateNameInput, trigger: 'blur' }],
                    notifyDate: [{ validator: validateDateInput, trigger: 'blur' }],
                    inputSoc: [{ validator: validateSocInput, trigger: 'blur' }]
                },
                titleInput: '',
                titleData: [],
                titleTotal: 10,
                activeStep: 1,
                rschecked: '',
                // workId: '',
                title: '',
                steps: [
                    {
                        name: 'works'
                    }, {
                        name: 'Performer'
                    }, {
                        name: 'IP Share'
                    }, {
                        name: 'Source'
                    }, {
                        name: 'Other Titles'
                    },
                    {
                        name: 'Remark'
                    },
                    {
                        name: 'ISRC'
                    },
                    {
                        name: 'Ref Work'
                    },
                    {
                        name: 'Album'
                    },
                    {
                        name: 'Sound Carrier'
                   },
                    {
                        name: 'ISWC'
                    }
                ],
                stepshow: true,
                dischecked: true,
                toWorkShow: false,
                typeData: [],
                worknoTableData: [],
                toWorkShowTotal: 0,
                rules: {
                    sourceType: [{ validator: validateTypeInput, trigger: 'blur' }],
                    notifySouceId: [{ validator: validateIdInput, trigger: 'blur' }],
                    name: [{ validator: validateNameInput, trigger: 'blur' }],
                    notifyDate: [{ validator: validateDateInput, trigger: 'blur' }],
                    inputSoc: [{ validator: validateSocInput, trigger: 'blur' }]
                },
                typeTotal: 10,
                typeInput: '',
                genreShow: false,
                genretableInput: '',
                langIndex: '',
                genreIndex: '',
                langCodeInput: '',
                langData: [],
                genreTableData: [],
                languageShow: false,
                titleLanguageShow: true,
                workInput: '',
                romanNameInput: '',
                languageCodeInput: '',
                languageTotal: 1,
                genreTotal: 1,
                pageInfo: {
                    page: {
                        pageNum: 0,
                        pageSize: 10
                    }
                },
                genreData: [],
                languageData: [],
                checked: true,
                checkedperf: true,
                localchecked: true,
                checkedpub: true,
                activeNames: ['1', '2', '3', '4', '5', '6', '7','8','9','10'],
                formOriginal: {
                    id: '',
                    local: '',
                    title: '',
                    refWorkId: '',
                    genre: '',
                    durationM: '',
                    durationS: '',
                    performLanguage: '',
                    publishAirDate: '',
                    iSWC: '',
                    workType: 'ADP',
                    toTitle: ''
                },
                rulesOriginal: {
                    title: [
                        { required: true, message: '請輸入作品名稱', trigger: 'blur' }
                    ],
                    refWorkId: [
                        { required: true, message: '請輸入修改的編號', trigger: 'blur' }
                    ],
                    durationM: [
                        // { required: true, message: '請輸入分钟', trigger: 'blur' },
                        { validator: checkDurationM, trigger: 'blur' }
                    ],
                    durationS: [
                        // { required: true, message: '請輸入秒', trigger: 'blur' },
                        { validator: checkDurationS, trigger: 'blur' }
                    ]
                },
                tableData1: [
                    // { id: '', ipNameNo: '', romanName: 'male', chineseName: '', country: '', soc: '', perf: '', pub: '' }
                ],
                tableData2: [
                    // { dummyName: '', ipNameNo: '', id: '', ipShare: '', sdcheck: '', ipType: '' }
                    // { gp: '', name: '', cn: '', status: '', inn: '', soc: '', ips: '', sd: '' }
                ],
                tableData4: [
                    // { group: '', title: '', languageCode: '', genreCode: '', durationM: '', durationS: '', inputSoc: '' }
                ],
                tableData5: [
                    // { inputSoc: '', remark: '' }
                ],
                tableData6: [
                    // { isrc: '', inputSoc: '' }
                ],
                tableData7: [
                    // { soc: '', remark: '' }
                ],
                tableData8: [
                    // {date: '', userid: 'ZHOUCHUANXIONG', type: 'male', remark: 'L120881672'}
                ],
                wrkWorkAlbumList:[],
                soundCarrierSet:[],
                showTransfer: false,
                transferInfo: {},
                disCanCheck: false,
                tableRight: {},
                tempRight: {},
                // 頭部
                headerInfo:{
                    performers: '',
                    performersAll: '',
                    authorComposer: '',
                    authorComposerAll: '',
                    title: ''
                },
                selectWork: {
                    workId: '',
                    title: ''
                },

                // 查詢作品
                editIndex: 0,
                workTableVisible: false,
                workSearchData: {},

                showConvert: false,

                wrkWorkRightMap: {},
                wrkWorkOtherSocCodeList: [],
                scrollPage:1,
                wiList:[],
                wiLength:1,
                more:'查看更多',
                emptyText: '暫無數據',
                emptyText1: '暫無數據',
                isADP: true,
                refWrkWorkRightMap: {},
                iswcdata: {
                    iswc: '',
                    source: '',
                    duplicateStatus: '',
                    musicArrCode: '',
                    lyricAdaptCode: '',
                    versionType: '',
                    versionIswc: '',
                    versionTitle: '',
                    versionIp: '',
                    versionLangCode: '',
                    excerptType: '',
                    excerptIswc: '',
                    excerptTitle: '',
                    excerptIp: '',
                    excerptLangCode: '',
                    compositeType: '',
                    compositeTitle: '',
                    compositeIswc: '',
                    compositeIp: '',
                    compositeDur: '',
                    preferred:''
                },
                iswcList: [],
                currentIswc: ''
            }
        },
        components: {
            transferDialog,
            otherTitle,
            workSource,
            performer,
            ipshare,
            selectWork,
            refWork,
            album
        },
        methods: {
            changeDuration(data){
                let isNum = /^[0-9]*$/;
                let val = this.formOriginal[data]
                if (isNum.test(val)) {
                    if(data=='durationM' && (val < 0 || val > 1000)){
                        this.$toast({tips: '不能大於1000min或者小於0min！'});
                        this.$set(this.formOriginal, data, '')
                    }else if(data=='durationS' && (val < 0 || val > 59)){
                        this.$toast({tips: '不能大於59s或者小於0s！'});
                        this.$set(this.formOriginal, data, '')
                    }
                }else{
                    this.$toast({tips: 'Duration为纯数字'});
                    this.$set(this.formOriginal, data, '')
                }
            },
            genreClose(){
                this.workInput=''
                this.pageInfo.genreDetail = this.workInput
                this.genreShow=false
            },
            languageClose(){
                this.languageCodeInput=''
                this.pageInfo.languageCode = this.languageCodeInput
                this.languageShow=false
            },
            lookMore(){
                if(this.wiLength != this.scrollPage){
                    let box = document.querySelector("#scrollBox");
                    let table = document.querySelector("#scrollTable");
                    let b = table.offsetHeight;
                    this.scrollPage ++;
                    this.tableData6 = this.wiList.slice(1,(this.scrollPage*100))
                    this.$nextTick(()=>{
                        box.scrollTop=(b/this.scrollPage)*(this.scrollPage)
                    })
                    return
                }else{
                    this.more='没有更多数据了'
                }
            },
             /**
             * 选取作品
             */
            getWork() {
                this.workTableVisible= true;
                this.workSearchData = {
                    workId: this.formOriginal.refWorkId,
                    soc: this.formOriginal.refWorkSociety,
                    title: this.formOriginal.toTitle
                }
                this.$nextTick( () => {
                    this.$refs.selectWorkCom.init();
                })
            },
            checkWork(info){
                this.$set(this.formOriginal, 'toTitle', info.title ? info.title : info.title_en);
                this.$set(this.formOriginal, 'refWorkId', info.work_id);
                this.$set(this.formOriginal, 'refWorkSociety', info.work_society_code);
                this.$nextTick( () => {
                    this.$refs.refWorkId.focus();
                    this.$refs.refWorkId.blur();
                })
                /**
                 * 如果是新增，選取作品後，查詢作品詳情，把ipshare帶入
                 */
                if(!this.$route.query.id){
                    this.getIpShareInfo(info.work_id, info.work_society_code);
                }
            },
            getIpShareInfo(workId, workSocietyCode){
                console.log('query ipshare?');
                 let params = {
                    workId: workId,
                    workSocietyCode: workSocietyCode
                }
                axios.get('/wrk/getWrkWorkById', {params}).then(res => {
                    if (res.status === 200) {
                        // this.formOriginal.distFlag = res.data.wrkWork.distFlag;

                        this.tableRight = res.data.wwisList[0] ? res.data.wwisList[0] : {};

                        for(let key in this.tableRight){
                            let temp = [];
                            this.tableRight[key].forEach( item => {
                                if(item.workIpRole == 'CA' || item.workIpRole == 'C' || item.workIpRole == 'A'){
                                    temp.push(this.$utils.copy(item));
                                }
                            })
                            this.tableRight[key] = temp;
                        }
                        for( let key in this.tableRight){
                            this.tableRight[key].forEach( (item) => {
                                if (item.sd === 'Y') {
                                    item.sdcheck = true
                                }
                                if (item.sd === 'N') {
                                    item.sdcheck = false
                                }
                                if(!item.ipNameNo){
                                    item.name = item.dummyNameRoman;
                                    item.chineseName = item.dummyName;
                                }
                            })
                        }
                    }
                })
            },
            adddata5 () {
                let myArray = [{ inputSoc: '161', remark: '' }]
                this.tableData5 = [...this.tableData5, ...myArray]
            },
            deletedata5 (index, rows) {
                this.$msgbox.confirm(`確定進行[删除]操作?`, '提示', {
                    confirmButtonText: '確定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    rows.splice(index, 1)
                })
            },
            adddata6 () {
                let myArray = [{ isrc: '', inputSoc: '' }]
                this.tableData6 = [...this.tableData6, ...myArray]
            },
            deletedata6 (index, rows) {
                this.$msgbox.confirm(`確定進行[删除]操作?`, '提示', {
                    confirmButtonText: '確定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    rows.splice(index, 1)
                })
            },
            saveWrk () {
                let that = this
                let wrkWorkRightMap = this.$refs.ipshare.getRightMap();
                let wrkWorkOtherSocCodeList = this.$refs.refWork.getData();
                let wrkWorkAlbumList = this.$refs.album.getData();
                console.log('/: ', wrkWorkOtherSocCodeList);
                if(!wrkWorkOtherSocCodeList || !wrkWorkAlbumList){
                    return;
                }
                var tempRight = this.$refs.ipshare.getIpShare();
                that.PER = tempRight.PER;
                that.ZYN = tempRight.ZYN;
                that.MEC = tempRight.MEC;
                that.NOD = tempRight.NOD;
                that.NDB = tempRight.NDB;
                var p1 = new Promise(function (resolve, reject) {
                    that.$refs['formOriginal'].validate((valid) => {
                        if (valid) {
                            resolve()
                        }
                    })
                })
                // var p2 = new Promise(function (resolve, reject) {
                //     that.$refs['tableForm'].validate((valid) => {
                //         if (valid) {
                //             resolve()
                //         }
                //     })
                // })
                this.tableData1 = this.$refs.performer.getData();
                Promise.all([p1]).then(function () {
                    // if (that.localchecked) {
                    //     that.formOriginal.local = 'L'
                    // }
                    // if (!that.localchecked) {
                    //     that.formOriginal.local = 'F'
                    // }
                    // if (that.dischecked) {
                    //     that.formOriginal.distFlag = 1
                    // }
                    // if (!that.dischecked) {
                    //     that.formOriginal.distFlag = 0
                    // }
                    that.tableData1.forEach(item => {
                        if (item.pub) {
                            item.oriAritist = 'Y'
                        }
                        if (!item.pub) {
                            item.oriAritist = 'N'
                        }
                        if (item.perf) {
                            item.firstPub = 'Y'
                        }
                        if (!item.perf) {
                            item.firstPub = 'N'
                        }
                    })
                    if (that.PER && that.PER.length > 0) {
                        that.PER.forEach(item => {
                            if (item.sdcheck) {
                                item.sd = 'Y'
                            }
                            if (!item.sdcheck) {
                                item.sd = 'N'
                            }
                        })
                    }
                    if (that.ZYN && that.ZYN.length > 0) {
                        that.ZYN.forEach(item => {
                            if (item.sdcheck) {
                                item.sd = 'Y'
                            }
                            if (!item.sdcheck) {
                                item.sd = 'N'
                            }
                        })
                    }
                    if (that.MEC && that.MEC.length > 0) {
                        that.MEC.forEach(item => {
                            if (item.sdcheck) {
                                item.sd = 'Y'
                            }
                            if (!item.sdcheck) {
                                item.sd = 'N'
                            }
                        })
                    }
                    if (that.NOD && that.NOD.length > 0) {
                        that.NOD.forEach(item => {
                            if (item.sdcheck) {
                                item.sd = 'Y'
                            }
                            if (!item.sdcheck) {
                                item.sd = 'N'
                            }
                        })
                    }
                    if (that.NDB && that.NDB.length > 0) {
                        that.NDB.forEach(item => {
                            if (item.sdcheck) {
                                item.sd = 'Y'
                            }
                            if (!item.sdcheck) {
                                item.sd = 'N'
                            }
                        })
                    }
                    that.tableDataSource = that.$refs.getSource.getData();
                    if(that.tableDataSource.length == 0){
                        console.log('stop');
                        return;
                    }
                    that.tableData4 = that.$refs.otherTitle.getData();
                    that.formOriginal.workType = "ADP";
                    let tableData6 = []
                    if(that.wiLength != that.scrollPage){
                        tableData6 = [...that.tableData6,...that.wiList.slice((that.scrollPage*100)+1)]
                    }else{
                        tableData6 = that.tableData6
                    }
                    let params = {
                        'soundCarrierSet': that.soundCarrierSet,
                        'waList': that.tableData1,     // 表演者Performer
                        // 'wamList': [],
                        'wiList': tableData6,
                        'wrkWorkAlbumList': wrkWorkAlbumList,
                        'wrkWork': Object.assign(that.formOriginal, that.formShare),     // works头部基本信息
                        // 'wwamList': [],
                        // 'wwcList': [],
                        wrkWorkRightMap: wrkWorkRightMap,
                        wrkWorkOtherSocCodeList: wrkWorkOtherSocCodeList,
                        'wwisList': {
                            'PER': that.PER,
                            'ZYN': that.ZYN,
                            'MEC': that.MEC,
                            'NOD': that.NOD,
                            'NDB': that.NDB
                        },        // ip share
                        'wwrList': that.tableData5,         // remark
                        'wwsList': that.tableDataSource,
                        'wwtList': that.tableData4        // Other Titles
                        // 'wwtfList': []

                    }
                    let iSWC = params.wrkWork.iSWC.split('')
                    let num = 0
                    iSWC.forEach(item=>{
                        if(item=='.'||item=='-'){
                            num+=1
                        }
                    })
                    if(num == iSWC.length && num){
                        that.$toast({tips: 'ISWC不可全為.-'})
                        return
                    }
                    params.wrkWork.iSWC = params.wrkWork.iSWC.replace(/-/g, '').replace(/\./g, '')

                    params.iswcList = that.iswcList || []
                    // 判断 ipshare 里 ipnameno是否為空

                    let flag = true;
                    for(let key in params.wwisList){
                        if(params.wwisList[key]){
                            for(let i =0;i<params.wwisList[key].length ;i++){
                                if(!params.wwisList[key][i].ipNameNo){
                                    flag = false;
                                }
                            }
                        }
                    }
                    if(!flag){
                        that.$toast({tips: 'IP Share中的Ip Name No必須填寫'})
                        return;
                    }
                    const loading = that.$loading();
                    axios.post('/wrk/saveOrUpdateWrkWork', params).then(res => {
                        loading.close();
                        if (res.status === 200) {
                            // that.$message.success(res.data)
                            that.$toast({tips: res.data})
                            that.$bus.$emit('closeCurrentTab', () => {
                                that.$router.push({name: 'works-list', query: {update: true}});
                            })
                        }
                    }).catch( () => {
                        loading.close();
                    })
                })
            },
            // getTitledata () {
            //     this.toWorkShow = true
            //     let data = {
            //         title: this.selectWork.title,
            //         workId: this.selectWork.workId
            //     }
            //     let formData = qs.stringify(data)
            //     let config = {headers: { 'content-type': 'application/x-www-form-urlencoded' }}
            //     // let params = this.formOriginal.refWorkId
            //     axios.post('/agreement/getWorkByTitle', formData, config).then(res => {
            //         if (res.status === 200) {
            //             this.worknoTableData = res.data;
            //             this.toWorkShowTotal = res.data.total;
            //         }
            //     })
            // },
            // worknoSearch () {
            //     let data = {
            //         title: this.selectWork.title,
            //         workId: this.selectWork.workId
            //     }
            //     let formData = qs.stringify(data)
            //     let config = {headers: { 'content-type': 'application/x-www-form-urlencoded' }}
            //     // let params = this.formOriginal.refWorkId
            //     axios.post('/agreement/getWorkByTitle', formData, config).then(res => {
            //         if (res.status === 200) {
            //             this.worknoTableData = res.data;
            //             this.toWorkShowTotal = res.data.total;
            //         }
            //     })
            // },
            // checkedTableWorkno (item) {
            //     this.toWorkShow = false
            //     if (item.title === '') {
            //         this.formOriginal.totitle = item.titleEn
            //     }
            //     if (item.title !== '') {
            //         this.formOriginal.totitle = item.title
            //     }
            //     this.$set(this.formOriginal, 'refWorkId', item.workId);
            //     this.$set(this.formOriginal, 'refWorkSociety', item.workSocietyCode);
            //     // this.formOriginal.refWorkId = item.workId
            //     // this.formOriginal.soc = item.workSocietyCode
            //     this.$refs['refWorkId'].focus();
            //     this.$refs['refWorkId'].blur();

            //     // 查詢此work no 的ipshare
            //     let params = {
            //         workId: item.workId,
            //         workSocietyCode: item.workSocietyCode
            //     }
            //     axios.get('/wrk/getWrkWorkById', {params}).then(res => {
            //         if (res.status === 200) {
            //             this.formShare.rightType = 'PER';
            //             this.tableData2 = res.data.wwisList[0].PER
            //        }
            //     })

            // },
            genreInit () {
                this.genreShow = true
                this.emptyText = '數據加載中'
                axios.post('/ref/getRefGenreDtlByDetail', this.pageInfo).then(res => {
                    if (res.status === 200) {
                        this.genreData = res.data.list
                        this.genreTotal = res.data.total
                    }
                    if(! this.genreData || this.genreData.length == 0){
                        this.emptyText = '暫無數據';
                    }
                })
            },
            checkedGenre (index, item) {
                this.formOriginal.genre = item.genreDetail
                this.genreShow = false
            },
            workSearch () {
                this.pageInfo.genreDetail = this.workInput
                this.emptyText = '數據加載中'
                axios.post('/ref/getRefGenreDtlByDetail', this.pageInfo).then(res => {
                    if (res.status === 200) {
                        this.genreData = res.data.list
                        this.genreTotal = res.data.total
                    }
                    if(! this.genreData || this.genreData.length == 0){
                        this.emptyText = '暫無數據';
                    }
                })
            },
            languageInit (str) {
                this.languageShow = true
                if (str === 'title') {
                    this.titleLanguageShow = true
                } else {
                    this.titleLanguageShow = false
                }
                this.emptyText1 = '數據加載中'
                axios.post('/ref/languageByCode', this.pageInfo).then(res => {
                    if (res.status === 200) {
                        this.languageData = res.data.list
                        this.languageTotal = res.data.total
                    }
                    if(! this.languageData || this.languageData.length == 0){
                        this.emptyText1 = '暫無數據';
                    }
                })
            },
            languageSearch () {
                this.pageInfo.languageCode = this.languageCodeInput
                this.emptyText1 = '數據加載中'
                axios.post('/ref/languageByCode', this.pageInfo).then(res => {
                    if (res.status === 200) {
                        this.languageData = res.data.list
                        this.languageTotal = res.data.total
                    }
                    if(! this.languageData || this.languageData.length == 0){
                        this.emptyText1 = '暫無數據';
                    }
                })
            },
            checkedLanguage (index, item) {
                if (this.titleLanguageShow) {
                    this.formOriginal.titleLanguage = item.languageCode
                } else {
                    this.formOriginal.performLanguage = item.languageCode
                }
                this.languageShow = false
            },

            genreHandleCurrentChange (val) {
                this.pageInfo.page.pageNum = val
                this.pageInfo.genreDetail = this.workInput
                this.emptyText = '數據加載中'
                axios.post('/ref/getRefGenreDtlByDetail', this.pageInfo).then(res => {
                    if (res.status === 200) {
                        this.genreData = res.data.list
                        this.genreTotal = res.data.total
                    }
                    if(! this.genreData || this.genreData.length == 0){
                        this.emptyText = '暫無數據';
                    }
                })
            },
            languageHandleCurrentChange (val) {
                this.pageInfo.page.pageNum = val
                this.pageInfo.languageCode = this.languageCodeInput
                this.emptyText1 = '數據加載中'
                axios.post('/ref/languageByCode', this.pageInfo).then(res => {
                    if (res.status === 200) {
                        this.languageData = res.data.list
                        this.languageTotal = res.data.total
                    }
                    if(! this.languageData || this.languageData.length == 0){
                        this.emptyText1 = '暫無數據';
                    }
                })
            },
            getworklistinfo () {
                let params = {
                    workId: this.workId,
                    workSocietyCode: this.socId
                }
                const loading = this.$loading();
                axios.get('/wrk/getWrkWorkById', {params}).then(res => {
                    loading.close();
                    if (res.status === 200) {
                        //判断，如果已经transfer,就弹框提示跳转到新的work
                        if(this.$route.query.from != 'transfer' && res.data.wrkWork.transferStatus){
                            this.$msgbox.confirm('This work will be transferred to ' + res.data.wrkWork.destWorkNum + '(' + res.data.wrkWork.destWorkNumSoc + ')' + '?', '提示', {
                                confirmButtonText: 'OK',
                                type: 'warning'
                            }).then(() => {
                                this.$bus.$emit('closeCurrentTab', (callback) => {
                                    this.$router.push({name: 'works-baseinfoadp', query: {id: res.data.wrkWork.destWorkNum, socId: res.data.wrkWork.destWorkNumSoc, title: res.data.wrkWork.destWorkNum, nameId: res.data.wrkWork.destWorkNum + res.data.wrkWork.destWorkNumSoc, from: 'transfer'}, params: {id: res.data.wrkWork.destWorkNum}})
                                })
                            }).catch( () => {
                                this.$bus.$emit('closeCurrentTab', () => { this.$router.push({name: 'works-list'}) });
                            })
                            return;
                        }
                        // let count = 0;
                        let temp = [];
                        this.tableData1 = [];
                        res.data.waList.forEach((item, index) => {
                            let obj = {}
                            obj = item
                            if (item.oriAritist === 'Y') {
                                obj.pub = true
                            }
                            if (item.oriAritist === 'N') {
                                obj.pub = false
                            }
                            if (item.firstPub === 'N') {
                                obj.perf = false
                            }
                            if (item.firstPub === 'Y') {
                                obj.perf = true
                            }
                            this.tableData1.push(obj);
                                // if(count < 3){
                                temp.push(obj.chineseName ? obj.chineseName : obj.artistName);
                                // count++;
                            // }
                        })
                        this.headerInfo.performersAll = temp.join(',');
                        if(temp.length > 3){
                            temp = temp.slice(0, 3);
                        }
                        this.headerInfo.performers = temp.length > 0 ? temp.join(',') : '';
                        this.tableData3 = res.data.wwsList;
                        this.tableData4 = res.data.wwtList;
                        // this.tableData6 = res.data.wiList;
                        this.wiList = res.data.wiList;
                        this.wiLength = Math.ceil(this.wiList.length/100)
                        this.tableData6 = this.wiList.slice(0,100)
                        this.wrkWorkAlbumList = res.data.wrkWorkAlbumList;
                        this.soundCarrierSet = res.data.soundCarrierSet;
                        this.wrkWorkOtherSocCodeList = res.data.wrkWorkOtherSocCodeList || [];
                        // this.tableData2 = res.data.wwisList[0].PER;
                        this.transferInfo.ipInfo = res.data.wwisList[0];
                        this.tableRight = res.data.wwisList[0] ? res.data.wwisList[0] : {};
                        if(res.data.iswcList && res.data.iswcList.length > 0){
                            this.iswcList = res.data.iswcList;
                            this.iswcdata = res.data.iswcList[0]
                            this.currentIswc = res.data.iswcList[0].iswc
                        }
                         let ac = [];
                        // count = 0;
                        for(let key in this.tableRight){
                            // if(count < 3){
                                this.tableRight[key].forEach( item => {
                                    if(item.workIpRole == 'C' || item.workIpRole == 'A' || item.workIpRole == 'CA'){
                                        if( ac.indexOf(item.chineseName ? item.chineseName : item.name) === -1){
                                            ac.push(item.chineseName ? item.chineseName : item.name);
                                            // count++;
                                        }

                                    }
                                })
                            // }
                        }
                        for( let key in this.tableRight){
                            this.tableRight[key].forEach( (item) => {
                                if (item.sd === 'Y') {
                                    item.sdcheck = true
                                }
                                if (item.sd === 'N') {
                                    item.sdcheck = false
                                }
                            })
                        }
                        this.headerInfo.authorComposerAll = ac.join(',');
                        if(ac.length > 3){
                            ac = ac.slice(0, 3);
                        }
                        this.headerInfo.authorComposer = ac.length > 0 ? ac.join(',') : '';
                        
                        this.formOriginal = res.data.wrkWork;
                        //处理iswc
                        this.formOriginal.iSWC = this.$utils.formatIswc(this.formOriginal.iSWC);
                        if(this.socId == 161){
                            this.showConvert = true;
                        }
                        this.formOriginal.refWorkId = (this.formOriginal.refWorkId && this.formOriginal.refWorkId != '0') ? this.formOriginal.refWorkId : '';
                        this.formOriginal.refWorkSociety = (this.formOriginal.refWorkSociety && this.formOriginal.refWorkSociety != '0') ? this.formOriginal.refWorkSociety : '';
                        this.wrkWorkRightMap = res.data.wrkWorkRightMap;
                        this.headerInfo.title = this.formOriginal.title;
                        if(this.formOriginal.local == 'L'){
                            this.localchecked = true;
                        }else{
                            this.localchecked = false;
                        }
                        // res.data.wwisList.forEach((item, index) => {
                        //     if (item.sd === 'Y') {
                        //         this.tableData2 = res.data.wwisList
                        //         this.tableData2[index].sdcheck = true
                        //     }
                        //     if (item.sd === 'N') {
                        //         this.tableData2 = res.data.wwisList
                        //         this.tableData2[index].sdcheck = false
                        //     }
                        // })

                        this.tableData5 = res.data.wwrList
                        console.log(res.data.wwtList)
                        res.data.wwtList.forEach((item, index) => {
                            if (item.subTitleId === 0) {
                                this.tableData4[index - 1] = item
                            }
                            // if (item.subTitleId === 1) {
                            //     this.formOriginal.title = item.title
                            // }
                        }),
                        this.refWrkWorkRightMap = ref.data.owwisMap || {}
                    }
                }).catch(()=>{
                    loading.close();
                })
            },
            transfer () {
                console.log('showTransfer');
                // 弹框显示此ip信息 點擊保存时，保存將次ip transfer
                this.transferInfo. baseInfo = this.formOriginal
                this.showTransfer = true;
            },
            // 主要用来修改show
            changeValue(obj){
                console.log(obj.name,'--', obj.value);
                this[obj.name] = obj.value;
            },
            convert(){
                this.$router.push({name: 'works-baseinfoadp', query: {id: this.workId, socId: this.socId, title: '', from: 'convert'}})
            },
            changeIswc(val){
                let _this = this
                _this.iswcList.forEach(item => {
                    if(item.id == val){
                        _this.currentIswc = item.iswc
                        _this.iswcdata = item
                        return
                    }
                })
            },
            updateIswc(val){
                this.currentIswc = val
            }
        },
        mounted () {
            this.top_barShow = this.$route.query.type ? false :true
            if(this.$route.query.id){
                this.getworklistinfo()
            }

        },
        computed: {
            workId: {
                get () {
                    return this.$route.query.id
                }
            },
            from: {
                get(){
                    return this.$route.query.from
                }
            },

            socId: {
                get () {
                    return this.$route.query.socId
                }
            }
        }
    }
</script>
<style lang="scss" scoped>
@import "../../../assets/scss/works.scss";
</style>

<style>
.work-header label:first-child{
    max-width: 225px;
}
    .position{
        position: fixed;
        top:150px;
        right: 20px;
        width:170px;
        text-align: left;
        background-color: #FFFFFF;
        z-index: 199999999;
    }
    .el-step {
        cursor: pointer;
    }
    .el-card{
        margin-bottom: 20px;
    }
    .memberdetail{
        width: 100%;
    }
    .memberdetail .el-form-item__content{
        width: calc(100% - 150px )!important;
    }
    .el-icon-circle-plus-outline{
        cursor: pointer;
    }
    .el-icon-delete{
        cursor: pointer;
    }
    .save{
        position: fixed;
        top:130px;
        right: 100px;
        z-index: 1999;
    }

    
   .works .el-form-item__label{
        width: 130px;
        white-space:nowrap;
        overflow:hidden;
        text-overflow:ellipsis;
    }
    .works .el-form-item__content{
        width: 200px;
    }
    .works .el-form-item .el-input input{
        width: 190px;
    }
    .works .double .el-form-item__content,.double .el-form-item .el-input input{
        width: 85px;
    }
    .carrier{
        padding-bottom: 10px;
    }
    .carrier>p{
        float: left;
        margin: 0 0 0 10px;
        background: #cde69c;
        color: #638421;
        border: 1px solid #a5d24a;
        padding: 0 10px;
    }
    .isrcpo{
        position: relative;
    }
</style>

