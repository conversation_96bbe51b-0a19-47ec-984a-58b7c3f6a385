<template>
  <!-- 分配金額配置  -->
  <div class="wrap-omoney-config">
    <el-form :inline="true" :model="search" label-position="left" ref="form" class="demo-form-inline"  @keyup.enter.native='searchFn()'>
      <el-form-item label="協會代號">
        <el-input v-model="search.sourceSocietyCode" placeholder="協會代號" style="width: 100px;"></el-input>
      </el-form-item>
      <el-form-item label="協會名稱">
        <el-input v-model="search.sourceSocietyName" placeholder="協會名稱" style="width: 100px;"></el-input>
      </el-form-item>
      <el-form-item label="FIE">
        <el-select v-model="search.recordType" placeholder="FIE">
          <el-option label="所有" value=""></el-option>
          <el-option label="Y" value="1"></el-option>
          <el-option label="N" value="0"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="RETAIN">
        <el-select v-model="search.recorddata" placeholder="RETAIN">
          <el-option label="所有" value=""></el-option>
          <el-option label="Y" value="1"></el-option>
          <el-option label="N" value="0"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="searchFn()" v-if="isAuth('list-manage:o-list:list:find')">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="addTableItem" :disabled="status == 7">水單明細中選擇</el-button>
        <el-button type="primary" @click="mergeList" :disabled="selectMergeFileList.length <= 1">合併</el-button>
        <el-button type="primary" @click="saveList" :disabled="status==1||status==11||status==3||status==31||status==41||status==7||status==2||status==4">save</el-button>
        <!--                <el-button type="primary" @click="getListByDetail" :disabled="!!status">水單明細中拉取</el-button>-->
        <el-button type="primary" @click="deleteBatch" :disabled="selectMergeFileList.length <= 0" >批量刪除</el-button>
      </el-form-item>
      <el-form-item>
        <span class="clear-search" @click="clearSearch()" v-if="isAuth('list-manage:o-list:list:find')">清除搜索</span>
      </el-form-item>
    </el-form>
    <el-table
      ref="table"
      class="flexable-table table-o-money"
      :empty-text="emptyText"
      :row-class-name="rowClassName"
      :data="tableData" border
      @select="highlightSelectionRow"
      @select-all="highlightSelectionRow"
      @selection-change="handleMergeSelectChange"
      @row-click="handleRowClick"
      style="width: 100%"
      :max-height="tableMaxHeight"
    >
      <el-table-column type="selection" fixed="left"></el-table-column>
      <el-table-column prop="lastest" label="有更新" >
        <template slot-scope="scope" >
          <span style="color:red;" v-if="scope.row.isLatest === 1"><i class="el-icon-warning"></i></span>
        </template>
      </el-table-column>
      <el-table-column prop="receiptDate" label="Receive Date" width="130px">
        <template slot-scope="scope">{{scope.row.receiptDate | splitDate}}</template>
      </el-table-column>
      <el-table-column prop="sourceSocietyCode" label="Soc code" width="100px">
        <!--<template slot-scope="scope">
                    <el-input type="number" v-model.number="scope.row.sourceSocietyCode"
                              @blur="getSocName(scope)"></el-input>
                </template>-->
      </el-table-column>
      <el-table-column width="110px" prop="sourceSocietyName" label="Soc name">
      </el-table-column>
      <el-table-column prop="receiptId" label="⽔單ID" width="100px">
      </el-table-column>
      <el-table-column prop="royNo" label="Roy No" width="90px">
      </el-table-column>
      <el-table-column prop="rightType" width="120px" label="Right Type">
        <!-- <template slot-scope="scope">
          <el-input v-model="scope.row.rightType" disabled @dblclick.native="getRightType(scope.$index)" placeholder="雙擊選擇"></el-input>
        </template> -->
      </el-table-column>
      <el-table-column prop="categoryCode" width="140px" label="Category code">
        <template slot-scope="scope">
          <el-input v-model="scope.row.categoryCode" @dblclick.native="addCategory(scope.$index)" placeholder="雙擊選擇"></el-input>
        </template>
      </el-table-column>
      <el-table-column prop="retainRate" width="150px" label="Retain rate(%)" @input="royChange(scope.row)">
        <template slot-scope="scope">
          <el-input v-model="scope.row.retainRate"></el-input>
        </template>
      </el-table-column>
      <el-table-column prop="exchangeRate" label="Exchange Rate(%)" width="170px">
      </el-table-column>
      <el-table-column prop="sourceDistNo" label="Soc Dist No" width="140px">
      </el-table-column>
      <el-table-column prop="sourceDistYear" label="Period" width="100px">
      </el-table-column>
      <el-table-column prop="receiptCurrencyCode" label="Curr" width="100px">
      </el-table-column>
      <el-table-column prop="sourceDataAmount" label="Data Roy" width="120px">
        <template slot-scope="scope">
          <el-input v-model="scope.row.sourceDataAmount" @input="royChange(scope.row)"></el-input>
        </template>
      </el-table-column>
      <el-table-column prop="sourceNoDataAmount" label="No Data Roy" width="130px">
        <template slot-scope="scope">
          <el-input v-model="scope.row.sourceNoDataAmount" @input="retainRoy()"></el-input>
        </template>
      </el-table-column>
      <el-table-column prop="sourceTotalAmount" label="Gross Soc Roy" width="140px">
      </el-table-column>
      <el-table-column prop="sourceDeductionAmount" label="Less Deduction" width="150px">
      </el-table-column>
      <el-table-column prop="sourceTax" label="Less Tax roy" width="140px">
      </el-table-column>
      <el-table-column prop="sourceBankCharge" label="Less bank charge" width="160px">
      </el-table-column>
      <el-table-column prop="netRoy" label="Net Roy" width="120px">
      </el-table-column>
      <el-table-column prop="draftCurr" label="Draft Curr" width="120px">
      </el-table-column>
      <el-table-column prop="draftGrossRoy" label="Draft Gross Roy" width="150px">
      </el-table-column>
      <el-table-column prop="lessDraftCharge" label="Less Draft Charge" width="170px">
      </el-table-column>
      <el-table-column prop="netDraftRoy" label="Net Draft Roy" width="150px">
      </el-table-column>
      <el-table-column prop="localNetAmount" label="Gross Roy" width="130px">
      </el-table-column>
      <el-table-column prop="localBankCharge" label="Less Bank Charge" width="170px">
      </el-table-column>
      <el-table-column prop="remark" label="Remark(明細)" width="130px">
      </el-table-column>
      <el-table-column prop="localNetRoy" label="台幣金額(Net Roy)" width="180px">
      </el-table-column>
      <el-table-column label="option">
        <template slot-scope="scope">
          <el-button
              type="text"
              class="delete-button"
              :disabled="status==1||status==11||status==3||status==31||status==41||status==7||status==2||status==4"
              @click="deleteAmount(scope.row,scope.$index)"
          >刪除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--        <div>-->
    <!--            <el-input v-model="totalMoney" style="width: 100px"></el-input>-->
    <!--            <el-input v-model="lessFie" style="width: 100px"></el-input>-->
    <!--            <el-input v-model="netDistRoy" style="width: 100px"></el-input>-->
    <!--        </div>-->
    <div class="total-count">
      <!-- <p>
        <span class="label">total:</span>{{formatThousand(totalMoney)}}
        <span class="label">Less FIE:</span>{{formatThousand(lessFie)}}
        <span class="label">Retain Roy:</span>{{formatThousand(retainRoy)}}
        <span class="label">Net Dist Roy:</span>{{formatThousand(netDistRoy)}}
      </p> -->
      <p>
        <span class="label">total:</span>{{formatThousand(totalMoney)}}
      </p>
      <p>
        <span class="label">Less FIE:</span>{{formatThousand(lessFie)}}
      </p>
      <p>
        <span class="label">Retain Roy:</span>{{formatThousand(retainRoy)}}
      </p>
      <p>
        <span class="label">Net Dist Roy:</span>{{formatThousand(netDistRoy)}}
      </p>
    </div>
    <el-dialog :visible.sync="moneyTableShow" :close-on-click-modal="false">
      <div style="width: 300px;margin: auto;">
        <el-form :inline="true" :model="addSearch">
          <el-form-item label="">
            <el-input placeholder="請輸入Source Name" v-model="addSearch.sourceName" class="input-with-select">
              <el-button slot="append" icon="el-icon-search" @click="getCategoryList()"></el-button>
            </el-input>
          </el-form-item>
        </el-form>
      </div>
      <el-table :empty-text="emptyText" stripe :data="moneyTableData">
        <el-table-column property="id" label="ID"></el-table-column>
        <el-table-column property="categoryCode" label="Category Code"></el-table-column>
        <el-table-column property="categoryDesc" label="Category Desc"></el-table-column>
        <el-table-column property="sourceName" label="Source Name"></el-table-column>
        <el-table-column label="Operation" width="120px">
          <template slot-scope="scope">
            <i class="el-icon-check pointer" @click="chooseCategory(scope.row)"></i>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background layout="prev, pager, next" :total=moneyTotal @current-change="getCategoryList">
      </el-pagination>
    </el-dialog>

    <!--        新增一行選擇弹框-->
    <el-dialog :visible.sync="addTableShow" width="80%" :close-on-click-modal="false">
      <el-table :empty-text="emptyText" stripe :data="addTable" max-height="250" @selection-change="handleSelectChange">
        <el-table-column type="selection" width="55">
        </el-table-column>
        <el-table-column prop="receiptId" label="水單序號">
        </el-table-column>
        <el-table-column prop="id" label="清單序號">
        </el-table-column>
        <el-table-column prop="distOrderNumber" label="分配序號">
        </el-table-column>
        <el-table-column prop="sourceSocietyCode" label="協會代碼">
        </el-table-column>
        <el-table-column prop="distNo" label="分配代號">
        </el-table-column>
        <el-table-column prop="sourceDistNo" label="國外分配代號">
        </el-table-column>
        <el-table-column prop="sourceDistYear" label="分配年度">
        </el-table-column>
        <el-table-column prop="summayTotalAmount" label="分配金額">
        </el-table-column>
        <el-table-column prop="summaryCommissionAmount" label="管理費">
        </el-table-column>
        <el-table-column prop="summaryTaxAmount" label="稅額">
        </el-table-column>
        <el-table-column prop="summaryChargeAmount" label="手續費">
        </el-table-column>
        <el-table-column prop="summaryReceiptAmount" label="匯款金額">
        </el-table-column>
        <el-table-column prop="localAmount" label="台幣金額">
          <template slot-scope="scope">
            <span class="over-line">{{scope.row.localAmount}}</span>
          </template>
        </el-table-column>
        <!--<el-table-column label="operation" width="120px">
                    <template slot-scope="scope">
                        <i class="el-icon-check pointer" @click="chooseItem(scope.row)"></i>
                    </template>
                </el-table-column>-->
      </el-table>
      <h3>statement</h3>
      <el-table :empty-text="emptyText" stripe :data="statementData" max-height="250">
        <el-table-column prop="currencyCode" label="幣別">
        </el-table-column>
        <el-table-column prop="receiptAmount" label="分配金額">
        </el-table-column>
        <el-table-column prop="localAmount" label="台幣金額">
        </el-table-column>
        <el-table-column prop="uploadAmount" label="Upload总金額">
        </el-table-column>
        <el-table-column prop="manualUploadAmount" label="Manual-upload总金額">
        </el-table-column>
        <el-table-column prop="statementAmount" label="Statement总金額">
        </el-table-column>
        <el-table-column prop="noStatementAmount" label="No Statement总金額">
        </el-table-column>
        <el-table-column prop="percentage" label="Percentage(%)">
        </el-table-column>
        <el-table-column prop="deduction" label="扣除額">
        </el-table-column>
      </el-table>
      <h3>FIE</h3>
      <el-table :empty-text="emptyText" stripe :data="fieData" max-height="250">
        <el-table-column prop="currencyCode" label="幣別">
        </el-table-column>
        <el-table-column prop="receiptAmount" label="分配金額">
        </el-table-column>
        <el-table-column prop="localAmount" label="台幣金額">
        </el-table-column>
        <el-table-column prop="uploadAmount" label="Upload总金額">
        </el-table-column>
        <el-table-column prop="manualUploadAmount" label="Manual-upload总金額">
        </el-table-column>
        <el-table-column prop="statementAmount" label="Statement总金額">
        </el-table-column>
        <el-table-column prop="noStatementAmount" label="No Statement总金額">
        </el-table-column>
        <el-table-column prop="fieDistYear" label="分配代號或年度">
        </el-table-column>
      </el-table>
      <div style="text-align: center;padding-top: 10px">
        <el-button type="primary" @click="pullList">拉取</el-button>
      </div>
    </el-dialog>

    <!--        rightType彈窗-->
    <el-dialog :visible.sync="rightTypeVisible" :close-on-click-modal="false">
      <el-table :empty-text="emptyText" stripe :data="rightTypeTable">
        <el-table-column prop="rightType" label="RightType">
        </el-table-column>
        <el-table-column prop="description" label="Description">
        </el-table-column>
        <el-table-column label="operation" width="120px">
          <template slot-scope="scope">
            <i class="el-icon-check pointer" @click="chooseRightType(scope.row)"></i>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import { formatThousand } from '@/utils';
export default {
  data() {
    return {
      distNo: '',
      totalIncome: '',
      emptyText: '暫無數據',
      tableData: [],
      selectMergeFileList: [],
      moneyTableShow: false,
      addTableShow: false,
      addTable: [],
      selectionList: [],
      statementData: [],
      fieData: [],
      addSearch: {

      },
      moneyTableData: [],
      moneyTotal: 0,
      currentRowIndex: 0,
      rightTypeVisible: false,
      rightTypeTable: [],
      status: '',
      delData:[],
      search: {
        sourceSocietyCode: '',
        sourceSocietyName: '',
        recordType: '',
        recorddata: ''
      },
      selectedRow: [],
      tableMaxHeight: 500,
      currentRow: null,
      lastAction: '', // 'select' 或 'click'

    }
  },
  computed: {
    totalMoney: function () {
      let total = 0;
      // if(!this.tableData){
      //     return total
      // }
      this.tableData.forEach(item => {
        let netDistIncome = new BigNumber((item.localNetAmount && item.localNetAmount != 'NaN') ? item.localNetAmount : 0)
        total = netDistIncome.plus(new BigNumber(total)).toFixed(0)
        console.log(total)
      })
      return total;
    },
    formatThousand: {
      get: () => formatThousand
    },
    lessFie() {
      let lessTotal = 0;
      // if(!this.tableData){
      //     return lessTotal
      // }
      this.tableData.forEach(item => {
        if (item.recordType === 1) {
          let netDistIncome = new BigNumber((item.localNetAmount && item.localNetAmount != 'NaN') ? item.localNetAmount : 0)
          lessTotal = netDistIncome.plus(new BigNumber(lessTotal)).toFixed(0)
        }
      })
      return lessTotal;
    },
    netDistRoy() {
      let royTotal = 0;
      let total = new BigNumber(this.totalMoney);
      let lessFie = new BigNumber(this.lessFie);
      royTotal = total.minus(lessFie).toFixed(0);
      return royTotal;
    },
    retainRoy(){
      let totalRetainRoy = new BigNumber(0);
      // if(!this.tableData){
      //     return lessTotal
      // }
      this.tableData.forEach(item => {
        let netRoy = new BigNumber((item.localNetRoy && item.localNetRoy != 'NaN') ? item.localNetRoy : 0)
        let retainRate = new BigNumber((item.retainRate && item.retainRate != 'NaN') ? item.retainRate : 0)
        if(retainRate > 0){
          let retainRoy = netRoy.multipliedBy(retainRate).multipliedBy(new BigNumber(0.01))
          totalRetainRoy = totalRetainRoy.plus(retainRoy)
        }
      })
      return totalRetainRoy.toFixed(0);
    }
  },
  created() {
    this.distNo = this.$route.query.distNo
    this.status = this.$route.query.status;
    this.getList();
    this.setTableHeight();

  },
  activated() {
    if (this.distNo != this.$route.query.distNo) {
      this.distNo = this.$route.query.distNo
      this.status = this.$route.query.status;
      this.getList()
      this.setTableHeight();
    }
  },
  methods: {
    clearSearch() {
      this.search = {
        sourceSocietyCode: '',
        sourceSocietyName: '',
        recordType: '',
        recorddata: ''
      }
    },
    getSocName(scope) {
      this.$http.get('/ref/society/getSocietyBySocietyCode', { params: { societyCode: scope.row.sourceSocietyCode } }).then(res => {
        console.log(res)
        if (res.success && res.data.code == 200) {
          this.tableData[scope.$index].sourceSocietyName = res.data.data.refSociety.societyName
        }
      })
    },
    searchFn(){
      let ajaxData = this.$utils.copy(this.search);
      ajaxData.distNo = this.distNo
      this.loading = true;
      this.emptyText = '數據加載中...'
      this.$http.get('/dist/param/overseas/listDistParamOverseas', { params: ajaxData }).then(res => {
        this.loading = false;
        if (res.success) {
          // this.tableData = res.data.data.distParamOverseasList || [];
          // this.total = res.data.total;
          // this.currentPage = ajaxData.page_num;
          // this.emptyText = this.tableData.length == 0 ? '暫無數據' : ' '
          let temparr = res.data.data.distParamOverseasList
          // exchangeRate   retainRate
          temparr.forEach((item, i, usearr) => {
            usearr[i].exchangeRate = usearr[i].exchangeRate > 100 ? "100" : ((+usearr[i].exchangeRate).toFixed(8))

            usearr[i].retainRate = usearr[i].retainRate > 100 ? "100" : ((+usearr[i].retainRate).toFixed(2))
            if (usearr[i].retainRate == 'NaN') {
              usearr[i].retainRate = 0.00
            }
          })
          this.tableData = temparr || [];
          this.emptyText = this.tableData.length == 0 ? '暫無數據' : ' '
        }
      })
    },
    getList() {
      this.emptyText = '數據加載中'
      this.$http.get('/dist/param/overseas/listDistParamOverseas', { params: { distNo: this.distNo } }).then(res => {
        console.log(res)
        if (res.success && res.data.code === 200) {
          let temparr = res.data.data.distParamOverseasList
          // exchangeRate   retainRate
          temparr.forEach((item, i, usearr) => {
            usearr[i].exchangeRate = usearr[i].exchangeRate > 100 ? "100" : ((+usearr[i].exchangeRate).toFixed(8))

            usearr[i].retainRate = usearr[i].retainRate > 100 ? "100" : ((+usearr[i].retainRate).toFixed(2))
            if (usearr[i].retainRate == 'NaN') {
              usearr[i].retainRate = 0.00
            }
          })
          this.tableData = temparr || [];
        }
        this.emptyText = '暫無數據'
      })
    },
    getListByDetail() {
      this.$alert('拉取水單信息會清空已有的數據，確定要拉取嗎', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        duration: 1500,
        showCancelButton: true,
        callback: action => {
          if (action === 'confirm') {
            this.$http.get('/dist/param/overseas/pullDistParamOverseas', { params: { distNo: this.distNo } }).then(res => {
              console.log(res)
              if (res.success && res.data.code === 200) {
                let list = []
                res.data.data && res.data.data.map(itemArr => {
                  itemArr.map(item => {
                    list.push(item)
                  })
                })
                console.log(list)
                this.tableData = list || []
              }
            })
          }

        }
      })

    },
    addTableItem() {
      this.emptyText = '數據加載中'
      this.$http.get('/dist/param/overseas/listListOverseasReceiptDetails', { params: { distNo: this.distNo } }).then(res => {
        // console.log(res)
        if (res.success && res.data.code === 200) {
          this.addTable = res.data.data || []
          this.addTableShow = true
        }
        this.emptyText = '暫無數據'
      })
    },
    chooseItem(row) {
      let result = true
      this.tableData && this.tableData.map(item => {
        console.log(item)
        if (item.receiptDetailsId === row.id) {
          result = false
        }
      })
      console.log(row)
      if (result) {
        row.distParamOverseasList.map(item => {
          this.tableData.push(item)
        })

        this.addTableShow = false
      }
    },
    handleSelectChange(selection) {
      console.log("473")
      console.log(selection)
      this.selectionList = selection
      this.statementData = []
      this.fieData = []
      selection.length && selection.map(row => {
        let subList = row.listOverseasReceiptStatementList
        if (subList) {
          subList.map(item => {
            if (item.type === 0) {
              if (!this.statementData.includes(item)) {
                this.statementData.push(item)
              }
            } else {
              if (!this.fieData.includes(item)) {
                this.fieData.push(item)
              }
            }
          })
        }
      })
    },
    pullList() {
      if (this.selectionList.length) {
        let receiptIds = []
        this.selectionList.forEach(e => {
            receiptIds.push(e.receiptId)
        })
        this.emptyText = '數據加載中'
        this.$http.post('/dist/param/overseas/transformDistParamOverseas', this.selectionList).then(res => {
          // console.log(res)
          if (res.data.code == 200) {
            let result = []
            this.tableData.forEach(t => {
              if(!receiptIds.includes(t.receiptId)){
                result.push(t)
              }
            })
            this.tableData = result.concat(res.data.data)
            this.tableData.forEach((item, index, arr) => {
              arr[index].exchangeRate = (+arr[index].exchangeRate).toFixed(8)
            })
            this.addTableShow = false
          } else {
            this.$toast({ tips: res.data.message })
          }
          this.emptyText = '暫無數據'
        })
      }
    },
    saveList() {
      if(this.delData.length){
        this.delData.forEach((item,index)=>{
          let params = {
            distNo: item.distNo,
            receiptDetailsId: item.receiptDetailsId
          }
          this.$http.delete('/dist/param/overseas/deleteDistParamOverseas', { params }).then(res => {
            if (res.success && res.data.code === 200) {
              if(index+1 == this.delData.length){
                this.saveFn()
              }
            }
          })
        })
      }else{
        this.saveFn()
      }
    },
    saveFn(){
      this.$http.post('/dist/param/overseas/saveDistParamOverseas', this.tableData).then(res => {
        console.log(res)
        if (res.data.code && res.data.code != 200 && res.data.code != -1) {
          this.$message({
            message: res.data.message || '網絡錯誤',
            type: 'error'
          })
        } else {
          this.$message({
            message: '保存成功',
            type: 'success'
          })
          this.tableData = res.data?res.data.data.distParamOverseasList : [];
        }
      })
    },
    deleteAmount(row,index) {
      console.log(row)
      this.$msgbox.confirm(`確定進行[删除]操作?`, '提示', {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if(row.id){
          if(this.delData.length){
            this.delData.forEach(item=>{
              if(item.distNo != row.distNo || item.receiptDetailsId != row.receiptDetailsId){
                this.delData.push(row)
              }
            })
          }else{
            this.delData.push(row)
          }
        }
        this.tableData.splice(index, 1);
      })
    },
    deleteBatch(){
      if(this.selectMergeFileList.length){
          this.$msgbox.confirm(`確定進行[删除]操作?`, '提示', {
            confirmButtonText: '確定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            // this.delData = []
            // this.selectMergeFileList.forEach(item=>{
            //   this.delData.push(item)
            // })
            // console.log("delData:",this.delData)
            this.$http.post('/dist/param/overseas/deleteBatch',this.selectMergeFileList).then(res => {
            if (res.success && res.data.code === 200) {
              this.searchFn()
              this.$message({
                message: '刪除成功',
                type: 'success'
              })
            } else {
              this.$message({
                message: res.data.message || '刪除失敗',
                type: 'error'
              })
            }
          })
          })
      } else {
        this.$message({
          message: '請選擇要刪除的項目',
          type: 'warning'
        })
      }
    },
    addCategory(index) {
      this.currentRowIndex = index
      this.moneyTableShow = true;
      this.getCategoryList(1);
    },
    getCategoryList(page) {
      let data = this.addSearch
      data.page_size = 10
      data.page_num = page ? page : 1
      this.emptyText = '數據加載中'
      this.$http.get('/list/categorys', { params: data }).then(res => {
        if (res.success) {
          this.moneyTableData = res.data.data.list || []
          this.moneyTotal = res.data.data.total
        }
        this.emptyText = '暫無數據'
      })
    },
    chooseCategory(row) {
      this.tableData[this.currentRowIndex].categoryCode = row.categoryCode;
      this.moneyTableShow = false
    },
    getRightType(index) {
      this.$http.post('/ref/wrkRight').then(res => {
        console.log(res)
        if (res.success) {
          this.rightTypeVisible = true
          this.rightTypeTable = res.data
          this.currentRowIndex = index
        }
      })
    },
    chooseRightType(row) {
      this.rightTypeVisible = false
      this.tableData[this.currentRowIndex].rightType = row.rightType
    },
    royChange(row) {
      let dataRoy = new BigNumber(row.sourceDataAmount)
      let noDataRoy = new BigNumber(row.sourceNoDataAmount)
      let grossRoy = new BigNumber(row.localNetAmount)
      let lessDeduction = new BigNumber(row.sourceDeductionAmount)
      let lessBankCharge = new BigNumber(row.sourceBankCharge)
      let sourceTotalAmount = dataRoy.plus(noDataRoy)  //gross soc roy
      let exchangeRate = grossRoy.dividedBy(sourceTotalAmount)  //Exchange Rate
      let netRoy = sourceTotalAmount.minus(lessDeduction.plus(lessBankCharge))
      row.sourceTotalAmount = sourceTotalAmount.toFixed(6)
      row.exchangeRate = exchangeRate.toFixed(8)
      row.netRoy = netRoy.toFixed(6)
    },
    handleMergeSelectChange(file) {
      this.selectMergeFileList = file
    },
    mergeList() {
      this.emptyText = '數據加載中'
      this.$http.post('/dist/param/overseas/mergeParamOverseasList', this.selectMergeFileList).then(res => {
        if (res.data.code == 200) {
          this.$toast({ tips: '合併成功' })
          this.searchFn()
          // this.tableData = [].concat(res.data.data)
          // this.tableData.forEach((item, index, arr) => {
          //   return arr[index].exchangeRate = (+arr[index].exchangeRate).toFixed(8)
          // })
          this.addTableShow = false
        } else {
          this.$toast({ tips: res.data.message })
        }
        this.emptyText = '暫無數據'
      })
    },
    highlightSelectionRow(selection, row){
      this.selectedRow = selection || []
      this.lastAction = 'select';
      // console.log('613',selection)
      // console.log('614',row)

      // if(selection.includes( row ))  {
      //   this.rowStyle.background = 'blue';
      // } else {
      //   this.rowStyle.background = 'red';
      // }
    },
      rowClassName({ row }) {
        const isSelected = this.selectedRow.includes(row);
        const isCurrent = this.currentRow === row;

        if (isSelected && isCurrent) {
          return this.lastAction === 'select' ? 'selected-row' : 'current-row';
        }

        if (isSelected) {
          return 'selected-row';
        }

        if (isCurrent) {
          return 'current-row';
        }

        return '';
      },
      handleRowClick(row) {
        this.currentRow = row;
        this.lastAction = 'click';
      },
    tableRowClassName({row,rowIndex}){
      if(this.selectedRow.includes(row)){
         return 'selected-row'
      } else {
         return 'unselected-row'
      }
    },
      setTableHeight() {
          // u8ba1u7b97u8868u683cu9ad8u5ea6
          this.$nextTick(() => {
              this.tableMaxHeight = window.innerHeight - 300; // u6839u636eu5b9eu9645u60c5u51b5u8c03u6574
          });
      },
  }
}
</script>
<style lang="scss">

.wrap-omoney-config {
  .table-o-money {
    .el-table__body-wrapper {
      //max-height: calc(100vh - 505px);
        overflow-y: auto;
    }
  }
}

.el-table__body tr.current-row > td {
    background-color: #409eff !important;
    color: white;
}

.el-table .current-row > td {
  background-color: #409eff !important; // 绿色
  color: white;
}

.el-table .selected-row > td{
    background: #409eff7a;
  }

  //.el-table .unselected-row {
  //  background: white;
  //}
.el-table__fixed {
    position: absolute !important;
}

.el-table__row.current-row > td .el-button--text span {
    color: #fbfbfb;
    &:hover {
      color: red;
    }
}
//.delete-button {
//    color: red;
//}
</style>

<style scoped lang="scss">
/deep/ .el-form-item__label {
  margin-left: 0;
}
.total-count {
  p {
    text-align: right;
    color: red;
    .label {
      width: 100px;
      margin-right: 10px;
      color: #333;
    }
  }
}

.selected-row .delete-button,
.current-row .delete-button {
    color: white !important;
}
</style>
