<template>
  <div>
    <el-form   @keyup.enter.native='getList()' :inline="true" :model="searchForm" class="demo-form-inline is-required">
      <el-form-item label="字段">
        <el-autocomplete v-model="keyWord" :fetch-suggestions="getWordList" placeholder="请選擇内容" @select="handleSelect1" style="width:300px">
          <template slot-scope="scope">
            <p class="auto-p">字段：{{scope.item.key}}</p>
            <p class="auto-p">描述：{{scope.item.description}}</p>
          </template>
        </el-autocomplete>
      </el-form-item>
      <el-form-item label="表頭" prop="header">
        <el-input v-model.trim="searchForm.header" placeholder=""></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList()">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="success" @click="add({},'新增')">新增</el-button>
      </el-form-item>
      <el-form-item>
        <span class="clear-search" @click="clearSearch()">清除搜索</span>
      </el-form-item>
    </el-form>
    <el-table :empty-text="tableresult"   :data="tableData" border stripe style="width: 100%" v-loading="loading">
      <el-table-column prop="id" label="id" width="80px">
      </el-table-column>
      <el-table-column prop="listBasicFileConfigKey" width="280" label="字段">
      </el-table-column>
      <el-table-column prop="listBasicFileConfigDescription" label="字段說明">
      </el-table-column>
      <el-table-column prop="header" label="表頭">
      </el-table-column>
      <el-table-column prop="description" label="表頭說明">
      </el-table-column>
      <el-table-column prop="obtainMethod" label="表頭獲取方式">
        <template slot-scope="scope">
          {{scope.row.obtainMethod == 0?'簡單表頭':'表達式表頭'}}
        </template>
      </el-table-column>
      <el-table-column prop="createTime" width="200px" label="創建時間">
      </el-table-column>
      <el-table-column label="operation" align="center">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="add(scope.row,'編輯')">編輯</el-button>
          <el-button type="text" size="small" @click="deleteRow(scope.row.id)">刪除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination background layout="prev, pager, next" :total="total" @current-change="handleCurrentChange">
    </el-pagination>

    <el-dialog :title="dialogTitle" :visible.sync="dialogFormVisible" width="40%" @close="closeDialog" :close-on-click-modal="false">
      <el-form :model="addForm" :rules="rules" ref="addDialog" label-position="right" label-width="120px">
        <el-form-item label="字段" class="is-required">
          <el-autocomplete v-model="addFormKey" :fetch-suggestions="getWordList" placeholder="请输入内容" @select="handleSelect2" style="width: 300px">
            <template slot-scope="scope">
              <p class="auto-p">字段：{{scope.item.key}}<span v-if="scope.item.behoove" class="tips">（必須）</span></p>
              <p class="auto-p">描述：{{scope.item.description}}</p>
            </template>
          </el-autocomplete>
        </el-form-item>
        <el-form-item label="表頭獲取方式" prop="obtainMethod" class="is-required">
          <el-select v-model="addForm.obtainMethod" placeholder="请选择">
            <el-option label="簡單表頭" :value="0"></el-option>
            <el-option label="表達式表頭" :value="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="表頭" prop="header" class="is-required">
          <el-input v-model="addForm.header" placeholder=""></el-input>
        </el-form-item>
        <el-form-item label="表頭說明">
          <el-input v-model="addForm.description" placeholder=""></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="addConfirm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'map-config',
  data() {
    return {
      loading: false,
      total: 0,
      keyWord: '',
      searchForm: {
        listBasicFileConfigId: '',
        header: '',
        page: {
          pageNum: 1,
          pageSize: 10
        },
        templateId: this.$route.query.nameId
      },
      tableData: [],tableresult:' ',
      dialogFormVisible: false,
      dialogTitle: '新增',
      addForm: {},
      addFormKey: '',
      rules: {
        name: [{ required: true, trigger: 'blur' }]
      }
    }
  },
  activated() {

  },
  mounted() {
    this.getList()
  },
  methods: {
    clearSearch() {
      this.searchForm = {
        listBasicFileConfigId: '',
        header: '',
        page: {
          pageNum: 1,
          pageSize: 10
        },
        templateId: this.$route.query.nameId
      }
      this.getList()
    },
    getList(page = 1) {
      if (page == 1) {
        this.total = 0
      }
      if (!this.searchForm.listBasicFileConfigId) {
        this.keyWord = ''
      }
      this.searchForm.page.pageNum = page
                        this.tableresult = '數據加載中...'
      this.$http.post('/list2BasicFileTemplateConfig/searchTemplateConfig', this.searchForm).then(res => {
        if (res.success) {
          this.tableData = res.data.data.list
          this.total = res.data.data.total
        }
          this.tableresult = this.tableData.length == 0 ? '暫無數據' : ' '
      })
    },
    add(row, type) {
      this.$refs['addDialog'] && this.$refs['addDialog'].clearValidate()
      this.dialogTitle = type
      if (row.listBasicFileConfigKey) {
        this.addForm = this.$utils.copy(row)
      } else {
        this.addForm = {
          description: '',
          header: '',
          listBasicFileConfigId: '',
          obtainMethod: ''
        }
      }

      this.addFormKey = row.listBasicFileConfigKey || ''
      this.addForm.templateId = this.$route.query.nameId
      this.dialogFormVisible = true
    },
    addConfirm() {
      if (!this.addForm.listBasicFileConfigId) {
        this.addFormKey = ''
        this.$toast({ tips: '請從下拉列表中選擇指定數據' })
        return
      }
      let url = ''
      if (this.dialogTitle == '編輯') {
        url = '/list2BasicFileTemplateConfig/update'
      } else {
        url = '/list2BasicFileTemplateConfig/add'
      }
      this.$refs['addDialog'].validate(valid => {
        if (valid) {
          this.$http.post(url, this.addForm).then(res => {
            console.log(res)
            if (res.data.code == 200) {
              this.$toast({ tips: this.dialogTitle + '成功' })
              this.dialogFormVisible = false
              this.getList()
            } else {
              this.$toast({ tips: res.data.message })
            }
          })
        }
      })

    },
    closeDialog() {
      this.$refs['addDialog'] && this.$refs['addDialog'].clearValidate()
    },
    getWordList(query, cb) {
      this.searchForm.listBasicFileConfigId = ''
      let ajaxData = {
        data: {
          key: query
        },
        page: {
          pageNum: 1,
          pageSize: 9999
        }
      }
      this.$http.post('/list2BasicFileConfig/getListBasicFileConfigList', ajaxData).then(res => {
        if (res.success) {
          let list = res.data.list
          cb(list)
        }
      })
    },
    handleSelect1(item) {
      this.keyWord = item.key
      this.searchForm.listBasicFileConfigId = item.id
    },
    handleSelect2(item) {
      this.addFormKey = this.addForm.key = item.key
      this.addForm.listBasicFileConfigId = item.id
    },
    deleteRow(id) {
      this.$alert('確定要刪除嗎？', '刪除', {
        confirmButtonText: '确定',
        showCancelButton: true,
        callback: action => {
          if (action == 'confirm') {
            this.$http.delete('/list2BasicFileTemplateConfig/delete/' + id).then(res => {
              if (res.success) {
                this.$toast({ tips: '刪除成功' })
                this.getList()
              }
            })
          }
        }
      });
    },
    handleCurrentChange(page) {

      this.getList(page)
    }
  }
}
</script>

<style scoped lang="scss">
/deep/ .el-form-item__label {
  margin-left: 0 !important;
}
.auto-p {
  margin: 10px 0;
  line-height: 1.2;
}
.tips {
  color: red;
  font-size: 12px;
}
</style>
