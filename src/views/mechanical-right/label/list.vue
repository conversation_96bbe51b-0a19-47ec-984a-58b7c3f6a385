<template>
  <div>
    <el-form :inline="true" class="demo-form-inline" @keyup.enter.native="searchFn(1)">
      <el-form-item>
        <el-input type="text" v-model="formInline.labelName" placeholder="Label Name" style="width: 260px;"></el-input>
      </el-form-item>
      <el-form-item prop="">
        <el-input v-model="formInline.producerCode" placeholder="Producer Code" style="width: 200px;"></el-input>
      </el-form-item>
      <el-form-item>
        <el-input type="text" v-model="formInline.producerName" placeholder="Producer Name" style="width: 260px;"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="searchFn(1)" v-if="isAuth('mechanical-right:label:list:find')">查询</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="addFn()" v-if="isAuth('mechanical-right:label:list:add')">添加</el-button>
      </el-form-item>
      <el-form-item>
        <span class="clear-search" @click="clearSearch()" v-if="isAuth('mechanical-right:label:list:find')">清除搜索</span>
      </el-form-item>
    </el-form>
    <el-table :empty-text="tableresult"  :data="tableData" border stripe style="width: 100%" v-loading="loading" >
      <el-table-column prop="id" label="Label ID" width="100px">
        <template slot-scope="scope">
          <span :title="scope.row.id">{{scope.row.id}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="labelName" label="Label Name">
        <template slot-scope="scope">
          <span :title="scope.row.labelName">{{scope.row.labelName}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="producerCode" label="Producer Code">
        <template slot-scope="scope">
          <span :title="scope.row.producerCode">{{scope.row.producerCode}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="producerName" label="Producer Name">
        <template slot-scope="scope">
          <span :title="scope.row.producerName">{{scope.row.producerName}}</span>
        </template>
      </el-table-column>
      <el-table-column label="OP" width="140px">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="editFn(scope.row)" v-if="isAuth('mechanical-right:label:list:change')">編輯</el-button>
          <el-button type="text" size="small" @click="deleteFn(scope.row, scope.$index)" v-if="isAuth('mechanical-right:label:list:del')">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination background layout="prev, pager, next" :total="total" @current-change="searchFn" :current-page="currentPage">
    </el-pagination>
    <!-- 編輯、添加 -->
    <el-dialog :title="(opType == 'add' ? '添加' : '編輯')" :visible.sync="show" v-if="show" :close-on-click-modal="false">
      <el-form ref="editForm" :inline="true" :model="edit" label-width="160px" @keyup.enter.native="confirmEdit">
        <!-- <el-form-item label="產品编碼">
                    <el-input type="text" v-model="edit.versionName"></el-input>
                </el-form-item> -->
        <el-form-item label="Producer Name" required>
          <!-- <el-select
              remote
              reserve-keyword
              ">
          </el-select> -->
          <el-select v-model="edit.producerName" :placeholder="edit.producerName || 'Producer Name'" filterable :remote-method="remoteMethod" @change="nameChange" style="width:220px;">
            <el-option v-for="item in options" :key="item.producerCode" :label="item.producerName" :value="item.producerCode">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="Producer Code" required>
          <el-input style="width:220px;" type="text" v-model="edit.producerCode" @blur="producerCodeChange"></el-input>
        </el-form-item>
        <el-form-item label="Label Name">
          <el-input style="width:220px;" type="text" v-model="edit.labelName" maxlength="50"></el-input>
        </el-form-item>
        <div class="t-c">
          <el-form-item>
            <el-button type="primary" @click="confirmEdit">確定</el-button>
          </el-form-item>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'list',
  data() {
    return {
      tableData: [],
      tableresult:" ",
      total: 0,
      currentPage: 1,
      formInline: {
        labelName: '',
        producerCode: '',
        producerName: ''
      },

      // dialog
      show: false,
      opType: 'add',
      edit: {
        producerName: '',
        producerCode: '',
        labelName: ''
      },
      loading: false,

      options: [],
      producerId: ''
    }
  },
  mounted() {
    this.searchFn();
  },
  created() {
    // this.getOptions()
  },

  methods: {
    getOptions() {
      let ajaxData = {
      };
      ajaxData.page_num = 1;
      ajaxData.page_size = 999;
      // this.loading = true;
      this.$http.get('/dist/mrd/listDistDistMrdProducerWithPage', { params: ajaxData }).then(res => {
        if (res.success) {
          // this.loading = false;
          console.log('1111111')
          this.options = res.data.data.list;
        }
      })
    },
    clearSearch() {
      this.formInline = {
        labelName: '',
        producerCode: '',
        producerName: ''
      }
      this.searchFn(1);
    },
    searchFn(page) {
      let ajaxData = this.$utils.copy(this.formInline);
      ajaxData.page_num = page ? page : 1;
      ajaxData.page_size = 10;
      this.loading = true;
      this.tableresult = '數據加載中...'
      this.$http.get('/distMrdScLabel', { params: ajaxData }).then(res => {
        this.loading = false;
        if (res.success) {
          this.tableData = res.data.data.list;
          this.total = res.data.data.total;
          this.currentPage = ajaxData.page_num;
        }
          this.tableresult = this.tableData.length == 0 ? '暫無數據' : ' '
      })
    },
    addFn() {
      this.getOptions()
      this.opType = 'add';
      this.show = true;
      this.edit = {
        producerName: '',
        producerCode: '',
        labelName: ''
      };
      this.producerId = '';
      this.options = [];
    },
    editFn(row) {
      console.log(row)
      this.getOptions()
      this.opType = 'edit';
      this.edit = this.$utils.copy(row);
      this.show = true;
    },
    confirmEdit() {
      let ajaxData = this.edit;

      if (!ajaxData.producerName) {
        this.$toast({ tips: '請輸入Producer Name' })
        return;
      }
      if (!ajaxData.producerCode) {
        this.$toast({ tips: '請輸入Producer Code' })
        return;
      }

      this.$http.post('/distMrdScLabel', ajaxData).then(res => {
        if (res.success) {
          if (res.data.code && res.data.code != 200) {
            this.$toast({ tips: res.data.message });
          } else {
            this.$toast({ tips: (this.opType == 'add' ? '添加' : '編輯') + '成功' });
            this.show = false;
            this.searchFn(1);
          }
        }
      })

    },
    deleteFn(row, index) {
      this.$msgbox.confirm(`確定進行[删除]操作?`, '提示', {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.delete('/distMrdScLabel/' + row.id).then(res => {
          if (res.success && res.data.code == 200) {
            this.$toast({ tips: '删除成功' })
            this.tableData.splice(index, 1);
          } else {
            this.$toast({ tips: res.data.message })
          }
                            this.tableresult = '數據加載中...'
      this.tableresult = this.tableData.length == 0 ? '暫無數據' : ' '
        })
      }).catch(() => {

      })
    },
    remoteMethod(query) {
      console.log(query)
      if (!query) {
        this.options = [];
        return;
      }
      let ajaxData = {
        producerName: query
      };
      ajaxData.page_num = 1;
      ajaxData.page_size = 999;
      this.$http.get('/dist/mrd/listDistDistMrdProducerWithPage', { params: ajaxData }).then(res => {
        if (res.success) {
          this.options = res.data.data.list;
        }
      })
    },
    nameChange(row) {
      console.log('row: ', row);
      this.edit.producerCode = row;
      this.options.forEach(item => {
        if (item.producerCode == row) {
          this.edit.producerName = item.producerName;
        }
      })
    },
    producerCodeChange() {
      if (!this.edit.producerCode) {
        return;
      }
      let ajaxData = {
        producerCode: this.edit.producerCode
      };
      ajaxData.page_num = 1;
      ajaxData.page_size = 999;
      // this.loading = true;
      this.$http.get('/dist/mrd/listDistDistMrdProducerWithPage', { params: ajaxData }).then(res => {
        // this.loading = false;
        if (res.success) {
          let temp = res.data.data.list[0] || {};
          this.edit.producerName = temp.producerName;
          this.producerId = temp.producerCode;
        }
      })
    }
  },



}
</script>
<style lang="scss" scoped>
/deep/ .el-dialog {
  width: 500px;
}
/deep/ .el-dialog__body {
  padding-top: 14px;
}
</style>

