<template>
  <div>
    <div style="width:80%">
      <el-form label-position="right" ref="addform" label-width="180px" :model="formData" class="demo-form-inline">
        <el-form-item label="dist no" prop="distNo" :rules="[{ required: true, message: '請輸入distNo', trigger: 'blur' }]">
          <el-input v-model="formData.distNo" placeholder="請輸入"></el-input>
        </el-form-item>
        <el-form-item label="is include">
          <el-switch v-model="formData.isinclude" active-color="#13ce66" inactive-color="#e9e9e9" active-value="Y" inactive-value="N">
          </el-switch>
        </el-form-item>
        <el-form-item label="list category" prop="listCategoryCode" >
        <!-- <el-form-item label="list category" prop="listCategoryCode" :rules="[{ required: true, message: '請輸入list category', trigger: 'blur' }]"> -->
          <!-- <el-autocomplete 
          v-model="formData.listCategoryCode" 
          :fetch-suggestions="getCategoryList" 
          placeholder="請輸入内容"
          @select="handleSelect">
            <template slot-scope="scope">
              <p style="margin:0;">category：{{scope.item.categoryCode}}</p>
              <p style="margin:0;">desc：{{scope.item.categoryDesc}}</p>
            </template>
          </el-autocomplete> -->
          <el-select 
              v-model="formData.listCategoryCode" 
              placeholder="請輸入内容" 
              filterable 
              remote 
              :remote-method="getCategoryList" 
              @change="handleSelect" 
              style="width:220px;">
            <el-option v-for="item in CategoryOptions" :key="item.categoryDesc" :value="item.categoryCode" style="height:68px">
                  <p style="margin:0;">category：{{item.categoryCode}}</p>
                  <p style="margin:0;">desc：{{item.categoryDesc}}</p>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="list category desc">
          <el-input v-model="formData.categoryDescription" placeholder="選擇list category後自動獲取" :readonly="true"></el-input>
        </el-form-item>
        <!-- <el-form-item label="society code" prop="societyCode" :rules="[{ required: true, message: '請輸入society code', trigger: 'change' }]"> -->
        <el-form-item label="society code" prop="societyCode">
          <!-- <el-input v-model="formData.societyCode" placeholder="請輸入" ref="socInput" :readonly="true" placeholder="請雙擊" @dblclick.native="showSocList" @blur="getSocietyInfo"></el-input> -->
          <el-input v-model="formData.societyCode" ref="socInput" :readonly="true" placeholder="請雙擊" @dblclick.native="showSocList"></el-input>
        </el-form-item>
        <el-form-item label="society name">
          <el-input v-model="formData.societyName" placeholder="由society code帶出" :readonly="true"></el-input>
        </el-form-item>
        <el-form-item label="full society name">
          <el-input v-model="formData.societyFullName" placeholder="由society code帶出" :readonly="true"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="save">確定</el-button>
        </el-form-item>
      </el-form>
      <!--list category弹窗-->
      <el-dialog title="協會列表" :visible.sync="showSocDialog" width="50%" @close="closeSoc" :close-on-click-modal="false">
        <el-input placeholder="请输入societyCode" v-model="socCode" class="input-soc" @keyup.enter.native="getSocList(1)">
          <el-button slot="append" icon="el-icon-search" @click="getSocList(1)"></el-button>
        </el-input>
        <el-table :empty-text="emptyText" :data="socList" stripe style="width: 100%">
          <el-table-column prop="societyCode" label="societyCode">
          </el-table-column>
          <el-table-column prop="societyName" label="name" width="400px">
          </el-table-column>
          <el-table-column label="operation" align="center">
            <template slot-scope="scope">
              <i class="el-icon-check check" @click="chooseSoc(scope.row)"></i>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination background layout="prev, pager, next" :total="socTotal" @current-change="handleSocChange" :current-page="socCurrentPage">
        </el-pagination>
      </el-dialog>

    </div>
  </div>
</template>

<script>
export default {
  name: 'addSoc',
  data() {
    return {
      emptyText: ' ',
      formData: {
        distNo: '',
        listCategoryCode: '',
        categoryDescription: '',
        societyCode: '',
        societyFullName: '',
        societyName: '',
        isinclude: 'N',
      },
      socCode: '',
      showDialog: false,
      showSocDialog: false,
      total: 0,
      socTotal: 0,
      socCurrentPage:1,
      list: [],
      socList: [],
      pageNum: 1,
      pageSize: 10,
      canSave: true,
      isEdit:false,
      CategoryOptions:[],
    }
  },
  mounted(){
    this.isEdit = this.$route.name == 'distribute-editsoc'
    if(this.isEdit){
      this.formData = JSON.parse(this.$route.query.row)
        this.getCategoryList(this.formData.listCategoryCode,data=>{
            if(data.length){
                this.$set(this.formData,'categoryDescription',data[0].categoryDesc)
            }
        })
    }
  },
  methods: {
    //    chooseType(row) {
    //   this.$set(this.formData, 'tranType', row.tranType);
    //   this.$set(this.formData, 'tranDescpt', row.tranDescpt);
    //   this.showTypeDialog = false 
    //   this.$refs.typeInput.focus()
    //   this.$refs.typeInput.blur()
    // },
    showSocList() {
      this.showSocDialog = true
      this.socCode = this.socCode || this.formData.societyCode
      this.getSocList()
    },
    getSocList(page = 1) {
      let ajaxData = {
        data: {
          societyCode: this.socCode
        },
        page: {
          pageNum: page,
          pageSize: 10
        }
      }
      this.emptyText = '數據加載中';
      this.$http.post('/ref/society/getSocietyList', ajaxData).then(res => {
        console.log(res)
        if (res.success) {
          this.socList = res.data.list
          this.socTotal = res.data.total
          this.socCurrentPage = page
          if(! this.socList || this.socList.length == 0){
              this.emptyText = '暫無數據';
          }
        }
      })

    },
    chooseSoc(row) {
      this.showSocDialog = false
      //   this.$set(this.formData, 'societyCode', row.societyCode);
      //   this.$set(this.formData, 'societyName', row.societyName);
      this.formData.societyCode = row.societyCode
      this.formData.societyName = row.societyName
      this.formData.societyFullName = row.societyFullName
      //   this.$refs.socInput.focus()
      //   this.$refs.socInput.blur()
      //   if (row.societyName) {
      //     this.$refs.socNameInput.focus()
      //     this.$refs.socNameInput.blur()
      //   }
    },
    handleSocChange(val) {
      this.getSocList(val)
    },
    closeSoc() {
      this.socCode = ''
    },
    // getSocietyInfo() {
    //   if (!this.formData.societyCode) {
    //     this.formData.societyName = '';
    //     this.formData.societyFullName = '';
    //     return
    //   }
    //   this.$http.get('/transaction/statement/ref/society/' + this.formData.societyCode).then(res => {
    //     console.log(res)
    //     if (res.success && res.data) {
    //       this.formData.societyName = res.data.societyName
    //       this.formData.societyFullName = res.data.societyFullName
    //       this.canSave = true
    //     } else {
    //       this.formData.societyName = ''
    //       this.formData.societyFullName = ''
    //       this.$message({
    //         message: 'society code無效',
    //         type: 'error'
    //       })
    //       this.canSave = false
    //     }
    //   })
    // },
    // 獲取list category列表
    getCategoryList(query, cb) {
      if (!query) {
        return false
      }
      this.$http.get('/distributionSpecialCondition/listCategoryList', { params: { categoryCode: query } }).then(res => {
        console.log(res)
        if (res.success) {
          if (res.data.code == 200) {
            this.CategoryOptions = res.data.data
            cb(res.data.data)
          } else {
            this.CategoryOptions = []
            // this.$message.error(res.data.message)
            // this.formData.categoryDescription = ''
          }
        }
      })
    },
    handleSelect(val) {
      this.CategoryOptions.forEach(item=>{
          if(item.categoryCode == val){
              this.formData.listCategoryCode = item.categoryCode
              this.$set(this.formData,'categoryDescription',item.categoryDesc)
          }
      })
      // this.formData.listCategoryCode = val.categoryCode
      // this.formData.categoryDescription = val.categoryDesc
      // this.$set(this.formData,'categoryDescription',val.categoryDesc)
    },
    save() {
      this.$refs.addform.validate(valid => {
        if (valid) {
          if (!this.canSave) {
            return
          }
          this.$http.post('/distributionSpecialCondition/saveDistListFilterSoc', this.formData).then(res => {
            if (res.success) {
              if (res.data.code === 200) {
                this.$message({
                  message: this.isEdit?'修改成功':'新增成功',
                  type: 'success',
                  duration: 2000,
                  onClose: () => {
                    this.$bus.$emit('closeCurrentTab', () => { this.$router.push({ name: 'distribute-condition' }) });
                  }
                })
              } else {
                this.$message({
                  message: res.data.message,
                  type: 'warning'
                })
              }
            }
          })
        }
      })
    }
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      if (to.params.distNo) {
        vm.formData.distNo = to.params.distNo
      }

    })
  }
}
</script>

<style scoped>
/deep/ .el-form-item__label {
  margin-left: 0 !important;
}
/deep/ .el-input.is-disabled .el-input__inner {
  color: #000;
}
</style>
