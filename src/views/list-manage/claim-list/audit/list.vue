<template>
<!-- 清單明細 + 類別等信息表  不要了-->
<!-- 清單明細 + 類別等信息  =》 匹配列表 =》 審核 -->
    <div class="ipibpox">
        <el-form :inline="true" :model="search" ref="form" class="demo-form-inline"  @keyup.enter.native='searchFn()'>
            <el-form-item prop="File Name">
                <el-input v-model="search.fileName" placeholder="File Name" style="width: 180px;"></el-input>
            </el-form-item>
            <el-form-item prop="File Type">
                <el-select placeholder="File Type">
                    <el-option>1</el-option>
                    <el-option>2</el-option>
                </el-select>
            </el-form-item>
            <el-form-item prop="Category">
                <el-input v-model="search.categoryCode" placeholder="Category" style="width: 140px;"></el-input>
            </el-form-item>
            <el-form-item>
                <el-select v-model="search.status" placeholder="所有狀態" style="width: 120px;">
                    <el-option v-for=" (value, key) in config.status" :key="key" :value="key" :label="value"></el-option>
                </el-select>
            </el-form-item>
            <!-- <el-form-item prop="List Type">
                <el-select placeholder="List Type">
                    <el-option>1</el-option>
                    <el-option>2</el-option>
                </el-select>
            </el-form-item> -->
            <el-form-item>
                <el-button type="primary" @click="searchFn()">搜索</el-button>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="addFn">上传</el-button>
            </el-form-item>
            <el-form-item>
                <span class="clear-search" @click="clearSearch()">清除搜索</span>
            </el-form-item>
        </el-form>
        <el-table :empty-text="tableresult"   stripe :data="tableData" border style="width: 100%">
            <el-table-column
                prop="id"
                label="ID">
            </el-table-column>
            <el-table-column
                prop="fileName"
                label="File Name">
            </el-table-column>
            <el-table-column
                prop="Log Type"
                label="Upload User">
            </el-table-column>
            <el-table-column
                prop="抽樣名稱"
                label="Upload Time">
            </el-table-column>
            <el-table-column
                prop="ip_type"
                label="File Type">
            </el-table-column>
            <el-table-column
                prop="name_type"
                label="Sequence">
            </el-table-column>
            <el-table-column
                prop="categoryCode"
                label="Category">
            </el-table-column>
            <el-table-column
                prop="name_type"
                label="List Type">
            </el-table-column>
            <el-table-column
                prop="status"
                label="Status">
            </el-table-column>
            <el-table-column
                fixed="right"
                label="operation"
                width="100">
                <template slot-scope="scope">
                    <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                        <span class="a-red" @click="handleClick(scope.row)">删除</span>
                    </span>
                    <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                        <span class="a-blue" @click="viewFile(scope.row)">查看文件</span>
                    </span>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :total="total" @current-change="handleCurrentChange" :current-page="currentPage">
        </el-pagination>
    </div>
</template>

<script>
    export default {
        name: 'sampleDateList',
        data () {
            return {
                tableData: [],tableresult:' ',
                total: 0,
                currentPage: 1,
                search: {},
                config: {
                    status: {
                        0: '等待處理',
                        1: '處理中',
                        2: '處理完成',
                        3: '處理失敗'
                    }
                }
            }
        },
        filters: {
            Status: function(status){
                let config = {
                    0: '等待處理',
                    1: '處理中',
                    2: '處理完成',
                    3: '處理失敗'
                }
                return config[status]
            }
        },
        created(){
            this.init();
        },
        methods: {
            clearSearch(){
                // todo-guan
                this.search= {
                    fileName: '',
                    status: '',

                }
                this.searchFn(1);
            },
            init(){
                this.searchFn(1);
                // this.tableData = [{}]
            },
            querySearch(queryString, callback) {
                // todo-guan 調用查詢接口,  传入查詢条件 需後台接口配合
                this.$http.get('/list/category', {parsm: {page_num:1, page_size: 999}}).then( res => {
                    if(res.success){
                        let temp = res.data.list;
                        temp.forEach( item => {
                            item.value = item.listName;
                        })
                        callback(temp)
                    }
                })
            },
            handleSelect(item) {
                console.log(item);
            },
            querySearchLogType(queryString, callback) {
                // todo-guan 調用查詢接口,  传入查詢条件 需後台接口配合
                this.$http.get('/list/category', {parsm: {page_num:1, page_size: 999}}).then( res => {
                    if(res.success){
                        let temp = res.data.list;
                        temp.forEach( item => {
                            item.value = item.listName;
                        })
                        callback(temp)
                    }
                })
            },
            handleSelectLogType(item) {
                console.log(item);
            },
            searchFn (page) {
                let ajaxData = this.$utils.copy(this.search);
                ajaxData.page_num = page ? page : 1;
                ajaxData.page_size = 10;
                ajaxData.typeId = 1;
                this.$http.get('/list', {params: ajaxData}).then(res => {
                    if (res.success) {

                    }
                })
            },
            handleCurrentChange (val) {
                this.searchFn(val);
            },
            handleClick (item) {
                this.$router.push({name: '', query: {}})
            },
            addFn(){
                this.$router.push({name: 'listUpload'})
            },
            // 查看清單處理後的文件列表
            viewFile() {
                this.$router.push({name: 'fileList'})
            }
        }
    }
</script>

<style>
</style>
