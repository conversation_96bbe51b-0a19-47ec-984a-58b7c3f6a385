<template>
  <!-- <div class="date-picker-wrapper"> -->
  <el-input v-model="inputValue" ref="dateRef" type="text" @change="inputValueChange" @blur="inputValueChange" :placeholder="placeholder" :disabled="disabled" :maxlength="8"></el-input>
  <!-- </div> -->
</template>
<script>
export default {
  props: {
    value: {
      type: String
    },
    valueFormat: {
      type: String,
      default: 'yyyy-MM-dd'
    },
    placeholder: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      inputValue: ''
    }
  },
  created() {
    this.valueChange();
  },
  watch: {
    value: function () {
      this.valueChange();
    }
  },
  methods: {
    inputValueChange() {
      /**
       * 验证是不是 yyyyMMdd,
       * 不是的话，就提示
       * 是的话，就赋值给父级变量
       * 
       * 规则： 8位数字， 月份1-12，日期判断1-31，并判断大月小月，闰年2月平年2月
       * 
       */
      let newVal = this.inputValue.toString();
      if (newVal.length == 0) {
        this.$emit('input', '');
        this.$emit('change', '');
        this.inputValue=''
        return;
      } else if (newVal.length != 8) {
        this.$emit('input', '');
        this.$emit('change', '');
        this.inputValue=''
        this.$alert('日期必須為8位數字，請重新輸入', '提示');
        return;
      }
      let year = parseInt(newVal.substr(0, 4));

      let month = parseInt(newVal.substr(4, 2));
      if (month > 12 || month <= 0) {
        this.$emit('input', '');
        this.$emit('change', '');
        this.inputValue=''
        this.$alert('月份必須大於0小於12，請重新輸入', '提示');
        return;
      }

      let day = parseInt(newVal.substr(6, 2));

      if (month == 2) {
        if (year % 100 == 0 && year % 400 || year % 100 && year % 4 == 0) {
          if (day > 29) {
            this.$emit('input', '');
            this.$emit('change', '');
            this.inputValue=''
            this.$alert('當前年份為閏年，二月沒有30日，請重新輸入', '提示');
            return;
          }
        } else {
          if (day > 28) {
            this.$emit('input', '');
            this.$emit('change', '');
            this.inputValue=''
            this.$alert('當前年份為平年，二月沒有29日，請重新輸入', '提示');
            return;
          }
        }
      } else if ((month == 4 || month == 6 || month == 9 || month == 11) && day > 30) {
        this.$emit('input', '');
        this.$emit('change', '');
        this.inputValue=''
        this.$alert('此月份沒有31日，請重新輸入', '提示');
        return;
      } else {
        if (day <= 0 || day > 31) {
          this.$emit('input', '');
          this.$emit('change', '');
          this.inputValue=''
          this.$alert('日期須大於0小於31，請重新輸入', '提示');
          return;
        }
      }
      newVal = year + '-' + month + '-' + day;
      this.$emit('input', this.$utils.DATE(newVal, this.valueFormat));
      this.$emit('change', this.$utils.DATE(newVal, this.valueFormat));
    //   let newVal = this.inputValue.toString();
    //   let year = parseInt(newVal.substr(0, 4));

    //   let month = parseInt(newVal.substr(4, 2));
    //   let day = parseInt(newVal.substr(6, 2));
    //   newVal = year + '-' + month + '-' + day;
    },
    valueChange() {
      /**
       * 转化为yyyyMMdd
       */
      let temp = this.value;
      if (temp == '0000-00-00') {
        temp = '1970-01-01';
      }
      this.inputValue = this.$utils.DATE(temp, 'yyyyMMdd');
    }

  }

}
</script>
<style lang="scss" scoped>
.date-picker-wrapper {
  display: inline-block;
}
</style>


