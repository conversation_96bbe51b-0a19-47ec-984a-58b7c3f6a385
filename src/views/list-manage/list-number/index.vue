<template>
    <div class="ipibpox">
        <el-form :inline="true" :model="search" ref="form" class="demo-form-inline"  @keyup.enter.native='searchFn()'>
            <el-form-item prop="name">
                <el-input v-model="search.folder" placeholder="資料夾"></el-input>
            </el-form-item>
            <el-form-item prop="categoryCode">
                <el-input v-model="search.poolCode" placeholder="pool code"></el-input>
            </el-form-item>
            <el-form-item prop="generalYear">
                <el-input v-model="search.generalYear" placeholder="通用年度"></el-input>
            </el-form-item>
            <el-form-item prop="listNumber">
                <el-input v-model="search.listNumber" placeholder="清單編號"></el-input>
            </el-form-item>
            <el-form-item prop="agrNo">
                <el-input v-model="search.agrNo" placeholder="合約編號"></el-input>
            </el-form-item>
            <el-form-item prop="shoudan">
                <el-input v-model="search.shoudan" placeholder="收單狀況"></el-input>
            </el-form-item>
            <el-form-item prop="remark">
                <el-input v-model="search.remark" placeholder="備註"></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="searchFn()" v-if="isAuth('list-manage:list-number:index:find')">搜索</el-button>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="addFn" v-if="isAuth('list-manage:list-number:index:add')">新增</el-button>
            </el-form-item>
            <el-form-item>
                <span class="clear-search" @click="init()" v-if="isAuth('list-manage:list-number:index:find')">清除搜索</span>
            </el-form-item>
        </el-form>
        <el-table :empty-text="tableresult"   stripe :data="tableData" border style="width: 100%" v-loading="loading">
            <el-table-column
                prop="folder"
                label="資料夾">
            </el-table-column>
            <el-table-column
                prop="generalYear"
                label="通用年度"
                width="200"
                min-width="100px">
            </el-table-column>
            <el-table-column
                prop="poolCode"
                width="120"
                label="pool code">
            </el-table-column>
            <el-table-column
                prop="listNumber"
                width="300"
                label="清單編號">
            </el-table-column>
            <el-table-column
                prop="agrNo"
                width="200"
                label="合約編號">
            </el-table-column>
            <el-table-column
                prop="shoudan"
                width="200"
                label="收單狀況">
            </el-table-column>
            <el-table-column
                prop="remark"
                width="300"
                label="備註">
            </el-table-column>
            <el-table-column
                fixed="right"
                label="operation"
                width="110">
                <template slot-scope="scope">
                    <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer" >
                        <el-button @click="changeFn(scope.row)" type="text" size="small" >编辑</el-button>
                        <el-button @click="deleteFn(scope.row.id)" type="text" size="small" v-if="isAuth('list-manage:list-number:index:del')">刪除</el-button>
                    </span>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :total="total" @current-change="handleCurrentChange">
        </el-pagination>
        <el-dialog
            title="新增清單編號"
            :visible.sync="dialogVisible"
            width="40%"
        >
            <el-form :model="addForm" ref="form" :rules="rules" label-width="120px">
                <el-form-item label="資料夾" prop="folder">
                    <template slot-scope="scope">
                        <el-input v-model="addForm.folder"></el-input>
                    </template>
                </el-form-item>
                <el-form-item label="通用年度" prop="generalYear">
                    <template slot-scope="scope">
                        <el-input v-model="addForm.generalYear"></el-input>
                    </template>
                </el-form-item>
                <el-form-item label="pool code" prop="poolCode">
                    <template slot-scope="scope">
                        <el-input v-model="addForm.poolCode" maxlength="2"></el-input>
                    </template>
                </el-form-item>

                <el-form-item label="合約編號" prop="agrNo">
                    <template slot-scope="scope">
                        <el-input v-model="addForm.agrNo" ></el-input>
                    </template>
                </el-form-item>
                <el-form-item label="收單狀況" prop="shoudan">
                    <template slot-scope="scope">
                        <el-input v-model="addForm.shoudan"></el-input>
                    </template>
                </el-form-item>
                <el-form-item label="備註" prop="remark">
                    <template slot-scope="scope">
                        <el-input type="textarea"  :rows="2"  placeholder="请输入内容" v-model="addForm.remark" ></el-input>
                    </template>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="addDataFn">确 定</el-button>
            </span>
        </el-dialog>
        <el-dialog
            title="修改清單編號"
            :visible.sync="dialogVisible2"
            :close-on-click-modal="false"
            width="40%"
        >
            <el-form :model="changeForm"  ref="form2" :rules="rules2" label-width="120px">
                <el-form-item label="清單編號" prop="listNumber">
                    <el-input v-model="changeForm.listNumber" @blur="changeForm.listNumber=$event.target.value.trim()"></el-input>
                </el-form-item>
                <el-form-item label="合約編號" prop="agrNo">
                    <el-input v-model="changeForm.agrNo" @blur="changeForm.agrNo=$event.target.value.trim()"></el-input>
                </el-form-item>
                <el-form-item label="收單狀況" prop="shoudan">
                    <el-input v-model="changeForm.shoudan" @blur="changeForm.shoudan=$event.target.value.trim()"></el-input>
                </el-form-item>
                <el-form-item label="備註" prop="remark">
                    <el-input type="textarea"  :rows="2"  placeholder="请输入内容" v-model="changeForm.remark" ></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible2 = false">取 消</el-button>
                <el-button type="primary" @click="changeDataFn">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
    export default {
        name: 'index',
        data () {
            return {
                tableData: [],tableresult:' ',
                total: 0,
                search: {
                    folder: '',
                    poolCode: '',
                    agrNo: '',
                    shoudan: '',
                    remark: '',
                    listNumber: '',
                    generalYear: '',
                    page_num:1,
                    page_size:10
                },
                loading:false,
                dialogVisible:false,
                dialogVisible2:false,
                addForm:{},
                changeForm:{},
                rules:{
                    folder: [
                        { required: true, message: '请填寫資料夾', trigger: 'blur' }
                    ],
                    generalYear: [
                        { required: true, message: '请填寫通用年份', trigger: 'blur' }
                    ],
                    poolCode: [
                        { required: true, message: '请填寫pool code', trigger: 'blur' }
                    ],
                },
                rules2:{
                    listNumber: [
                        { required: true, message: '请填寫清單編號', trigger: 'blur' }
                    ],
                },
            }
        },
        mounted(){
            this.searchFn();
        },
        activated(){
            // if(this.$route.query.update){
            //     this.init();
            //     this.$router.push({name: 'sampleDateList', query: {update: false}});
            // }
            this.$nextTick( () => {
                if(this.$route.query.update){
                    let query = this.$route.query;
                    delete query.update;
                    this.init();
                }
            })
        },

        methods: {
            init(){
                this.search ={
                    folder: '',
                    poolCode: '',
                    agrNo: '',
                    shoudan: '',
                    remark: '',
                    listNumber: '',
                    generalYear: '',
                    page_num:1,
                    page_size:10
                }
                this.total = 0
                this.searchFn();
            },
            splitTime(time){
                return time.split(' ')[0]
            },
            searchFn (page = 1) {
                if(page == 1){
                    this.total = 0
                }
                this.search.page_num = page
                this.loading = true;
                      this.tableresult = '數據加載中...'
                this.$http.get('/listNumGenerate',{params:this.search}).then(res => {
                    console.log(res)
                    if (res.success) {
                        this.tableData = res.data.data.list
                        this.total = res.data.data.total
                    }
                          this.tableresult = this.tableData.length == 0 ? '暫無數據' : ' '
                    this.loading = false;
                })
            },
            handleCurrentChange (val) {
                this.searchFn(val);
            },
            changeFn(row){
                this.dialogVisible2 = true
                this.changeForm= this.$utils.copy(row)
            },
            changeDataFn(){
                this.$refs.form2.validate(validate => {
                    if(validate){
                        this.$http.post('/listNumGenerate/editListNumber',this.changeForm).then(res => {
                            console.log(res)
                            if(res.data.code == 200){
                                this.dialogVisible2 = false
                                this.$toast({tips:'修改成功'})
                                this.searchFn()
                            }else{
                                this.$toast({tips:res.data.message})
                            }
                        })
                    }
                })
            },
            deleteFn(id){
                this.$alert('確定要刪除嗎？', '刪除', {
                    confirmButtonText: '確定',
                    showCancelButton: true,
                    cancelButtonText: '取消',
                    callback: action => {
                        if(action == 'confirm'){
                            this.$http.delete('/listNumGenerate/'+id).then(res => {
                                console.log(res)

                                if(res.success){
                                    this.searchFn()
                                    this.$toast({tips:'刪除成功'})
                                }else{
                                    this.$toast({tips:res.data.message})
                                }
                            })
                        }
                    }
                });

            },
            addFn(){
                this.dialogVisible = true
                this.addForm = {}
                this.$refs.form && this.$refs.form.clearValidate()
            },
            addDataFn(){
                this.$refs.form.validate(validate => {
                    if(validate){
                        this.$http.post('/listNumGenerate',this.addForm).then(res => {
                            console.log(res)
                            if(res.data.code == 200){
                                this.dialogVisible = false
                                this.$toast({tips:'新增成功'})
                                this.searchFn()
                            }else{
                                this.$toast({tips:res.data.message})
                            }
                        })
                    }
                })
            }
        }
    }
</script>

<style lang="scss" scoped>
    /deep/ .el-form-item__label{
        margin-left: 0!important;
    }
</style>
