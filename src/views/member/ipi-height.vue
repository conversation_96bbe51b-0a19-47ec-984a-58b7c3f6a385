<template>
    <div class="ipibpox">
        <el-form :inline="true" :model="formInline" ref="form" class="search-form" :class="showHigh ? 'high' : ''" @keyup.enter.native="onSubmit">
            <div>
                <el-form-item prop="name">
                    <el-input v-model="highSearch.bankName" placeholder="Bank A/C Name" style="width: 160px"></el-input>
                </el-form-item>
                <el-form-item prop="name">
                    <el-input v-model="highSearch.name" placeholder="Successor" style="width: 160px"></el-input>
                </el-form-item>
                <el-form-item prop="name">
                    <el-input v-model="highSearch.person" placeholder="Contact Person" style="width: 160px"></el-input>
                </el-form-item>
                <el-form-item prop="name">
                    <el-input v-model="highSearch.mail_name" placeholder="Mail Name" style="width: 160px"></el-input>
                </el-form-item>
                <el-form-item prop="name">
                    <el-input v-model="highSearch.idCardNo" placeholder="ID Card" style="width: 160px"></el-input>
                </el-form-item>
                <el-form-item prop="name">
                    <el-input v-model="highSearch.memberNo" placeholder="Member No" style="width: 160px"></el-input>
                </el-form-item>
                <el-form-item prop="name">
                    <el-input v-model="highSearch.ciNo" placeholder="CI No" style="width: 160px"></el-input>
                </el-form-item>                
                <el-form-item label="Terminated" class="ter">
                    <el-select v-model="highSearch.terminate" style="width: 60px;">
                        <el-option :value="false" label="NO"></el-option>
                        <el-option :value="true" label="YES"></el-option>
                    </el-select>
                    <!-- <el-radio v-model="formInline.name" label="1" style="margin-right: 0;">No</el-radio>
                    <el-radio v-model="formInline.name" label="2" style="margin-left: 5px;">Yes</el-radio> -->
                </el-form-item>
                <el-form-item prop="name">
                    <el-input v-model="highSearch.recipientBrNoOrHkId" placeholder="Recipient ID/BR" style="width: 160px"></el-input>
                </el-form-item>
                <el-form-item prop="name">
                    <el-input v-model="highSearch.telephoneOrMobileOrFaxNo" placeholder="Telephone/Fax" style="width: 160px"></el-input>
                </el-form-item>
                <el-form-item prop="name">
                    <!-- <el-input v-model="highSearch.terminateReason" placeholder="Reason" style="width: 160px"></el-input> -->
                    <el-select @change="ReasonChange" v-model="highSearch.terminateReason" placeholder="Reason" style="width: 160px;">
                        <el-option label="" value=""></el-option>
                        <el-option value="D" label="Dismission"></el-option>
                        <el-option value="W" label="Withdrawal"></el-option>
                    </el-select>
                </el-form-item>
                
                <el-form-item prop="name">
                    <el-input v-model="highSearch.email" placeholder="Email" style="width: 160px"></el-input>
                </el-form-item>
                <el-form-item prop="name">
                    <el-input v-model="highSearch.districtStreetSectionLaneAlleyNo" placeholder="Address" style="width: 160px"></el-input>
                </el-form-item>
                <el-form-item prop="name">
                    <date-picker v-model="highSearch.valid_from" placeholder="Perm From" value-format="yyyy-MM-dd" type="date" format="yyyyMMdd" style="width: 160px !important;"></date-picker>
                    -
                    <date-picker v-model="highSearch.valid_to" placeholder="Perm To" value-format="yyyy-MM-dd" type="date" format="yyyyMMdd" style="width: 160px !important;"></date-picker>
<!-- 
                    <el-input v-model="highSearch.valid_from" placeholder="Perm From" style="width: 160px"></el-input>
                    <el-input v-model="highSearch.valid_to" placeholder="Perm To" style="width: 160px"></el-input> -->
                </el-form-item>
                
            <!-- </div>
            
            <div style="text-align: center; position: absolute;bottom: 0;width: 100%;background: #fff;"> -->
                <el-form-item>
                    <el-button type="primary" @click="onSubmit" v-if="isAuth('member:ipi:find')">搜索</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="success" @click="dummyAdd" v-if="isAuth('member:ipi:add')">新增</el-button>
                </el-form-item>
                <el-form-item style="">
                    <el-button type="success" @click="exportList"  v-if="isAuth('member:ipi:exportout')">導出</el-button>
                </el-form-item>
                <el-form-item>
                    <span class="clear-search" @click="clearSearch()" v-if="isAuth('member:ipi:find')">清除搜索</span>
                </el-form-item>
            </div>
        </el-form>
        <el-table
            :data="tableData"
            border
            stripe
            style="width: 100%" 
            class="table-fixed"
            v-loading="loading"
            :empty-text="emptyText">
            <el-table-column
                prop="name"
                label="Name"
                min-width="180">
            </el-table-column>
            <el-table-column
                prop="chinese_name"
                label="Chinese Name"
                width="180">
            </el-table-column>
            <el-table-column
                prop="ip_base_no"
                label="IP Base no"
                width="160">
            </el-table-column>
            <el-table-column
                prop="ip_name_no"
                label="IP Name no"
                width="130">
            </el-table-column>
            <el-table-column
                prop="ip_type"
                label="IP Type"
                width="90px">
            </el-table-column>
            <el-table-column
                prop="name_type"
                label="NameType"
                width="110px">
            </el-table-column>
            <!-- <el-table-column
                prop="status"
                label="status"
                width="80">
                <template slot-scope="scope">
                    {{!(scope.row.name_type === 'HR' || scope.row.name_type === 'PG') ? scope.row.status : ''}}
                </template>
            </el-table-column>
            <el-table-column
                prop="society_code"
                label="soc"
                width="60px"
                >
                <template slot-scope="scope">
                    <span v-if="scope.row.society_code.toString().length == 3">
                        {{scope.row.society_code}}
                    </span>
                    <span v-if="scope.row.society_code.toString().length == 2">
                        {{'0'+scope.row.society_code}}
                    </span>
                    <span v-if="scope.row.society_code.toString().length == 1">
                        {{'00'+scope.row.society_code}}
                    </span>
                </template>
            </el-table-column> -->
            <el-table-column
                label="operation"
                width="100"
                fixed="right">
                <template slot-scope="scope">
                    <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                        <span class="a-blue" @click="handleClick(scope.row,'look')" v-if="!isAuth('member:ipi:change')">查看</span>
                        <span class="a-blue" @click="handleClick(scope.row)" v-if="isAuth('member:ipi:change')">編輯</span>
                        <span class="a-blue" @click="note(scope.row)" v-if="isAuth('member:ipi:note')&&(!(scope.row.name_type === 'HR' || scope.row.name_type === 'PG'))">Note</span>
                    </span>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :total=total 
            @current-change="handleCurrentChange"
            :current-page="currentPage">
        </el-pagination>

        <el-dialog
            :visible.sync="lookDialogVisible"
            width="1100px"
            center>
            <div>
                <el-form :inline="true" :model="formDialog" ref="formInline" class="demo-form-inline">
                    <el-form-item prop="name" label="name">
                        <el-input v-model="formDialog.name" readonly></el-input>
                    </el-form-item>
                    <el-form-item prop="ip_base_no" label="IP Base No">
                        <el-input v-model="formDialog.ip_base_no" readonly></el-input>
                    </el-form-item>
                    <el-form-item prop="ip_name_no" label="IP Name No">
                        <el-input v-model="formDialog.ip_name_no" readonly></el-input>
                    </el-form-item>
                </el-form>
                <el-table
                    :data="tableDialogData"
                    border
                    stripe
                    style="width: 100%" 
                    v-loading="loading"
                    :empty-text="emptyText1">
                    <el-table-column
                        prop="name"
                        label="Name"
                        width="180">
                    </el-table-column>
                    <el-table-column
                        prop="chineseName"
                        label="Chinese Name"
                        width="180">
                    </el-table-column>
                    <el-table-column
                        prop="nameType"
                        label="nameType"
                        width="180">
                    </el-table-column>
                    <el-table-column
                        prop="ipBaseNo"
                        label="IP Base no">
                    </el-table-column>
                    <el-table-column
                        prop="nameNo"
                        label="IP Name no">
                    </el-table-column>
                    <el-table-column
                        fixed="right"
                        label="operation"
                        width="100">
                        <template slot-scope="scope">
                            <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                                <el-button @click="confirmDialog(scope.row)" type="text" size="small">確認</el-button>
                            </span>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="lookDialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="lookDialogVisible = false">確 定</el-button>
            </span>
        </el-dialog>
        <export-dlg ref="exportCom" v-if="exportShow"></export-dlg>
        <note v-if="noteShow" ref="noteCom" :noteInfo="noteInfo"></note>
    </div>
</template>

<script>
    import exportDlg from './dialog/export'
    import axios from '../../utils/httpRequest'
    import qs from 'qs'
    import note from './dialog/note';
    const cityOptions = ['P', 'M', 'CP', 'MA']
    export default {
        name: 'ipi',
        data () {
            return {
                status: [],
                options: [
                    {
                        value: '1',
                        label: '1'
                    }, {
                        value: '2',
                        label: '2'
                    }, {
                        value: '3',
                        label: '3'
                    }, {
                        value: '4',
                        label: '4'
                    }],
                lookDialogVisible: false,
                dummyDialogVisible: false,
                checkedCities1: ['P', 'M'],
                cities: cityOptions,
                myindex: 0,
                tabShow: false,
                loading: false,
                checked: false,
                total: 1,
                currentPage: 1,
                formInline: {
                    name: '',
                    status: '',
                    ip_no: '',
                    name_no: '',
                    ip_type: '',
                    role_code: '',
                    soc: '',
                    page_num: 1,
                    page_size: '10'
                },
                highSearch: {
                    bankName: '',
                    name: '',
                    person: '',
                    mail_name: '',
                    idCardNo: '',
                    memberNo: '',
                    ciNo: '',
                    terminate: false,
                    recipientBrNoOrHkId: '',
                    telephoneOrMobileOrFaxNo: '',
                    terminateReason: '',
                    email: '',
                    districtStreetSectionLaneAlleyNo: '',
                    pageNum: 1,
                    pageSize: '10'

                },
                formDialog: {
                    name: '',
                    ip_no: '',
                    name_type: ''
                },
                formDummy: {
                    user: '',
                    region: '',
                    type: ''
                },
                obj: {},
                tableDialogData: [],
                tableData: [
                    // {name: '1', chinese: '', ipBaseNo: '', nameNo: '', ipType: '', societyCode: ''},
                    // {name: '2', chinese: '', ipBaseNo: '', nameNo: '', ipType: '', societyCode: ''}
                ],
                roleCodeList: [],

                noteShow:false,
                noteInfo: {},

                exportShow: false,

                showHigh: true,
                emptyText: '數據加載中',
                emptyText1: '數據加載中',
            }
        },
        components: {
            exportDlg,
            note
        },
        methods: {
            ReasonChange(val){
                if(val){
                    this.highSearch.terminate=true
                }
            },
            clearSearch(){
                // this.$refs.form.resetFields();
                this.highSearch= {
                    bankName: '',
                    name: '',
                    person: '',
                    mail_name: '',
                    idCardNo: '',
                    memberNo: '',
                    ciNo: '',
                    terminate: false,
                    recipientBrNoOrHkId: '',
                    telephoneOrMobileOrFaxNo: '',
                    terminateReason: '',
                    email: '',
                    districtStreetSectionLaneAlleyNo: '',
                    pageNum: 1,
                    pageSize: '10'
                }
                this.onSubmit();
            },
            onSubmit(){
                if(this.showHigh){
                    this.highSearchFn();
                }else{
                    this.onSubmitFn();
                }
            },
            onSubmitFn() {
                // this.loading = true
                // this.formInline.status = this.status.toString()
                this.formInline.page_num = 1;
                this.$utils.trim(this.formInline);
                let formData = qs.stringify(this.formInline);
                let config = {headers: { 'content-type': 'application/x-www-form-urlencoded' }}
                this.emptyText = '數據加載中';
                axios.post('/ip/name/es', formData, config).then(res => {
                    if (res.status === 200) {
                        this.tableData = res.data.list
                        if(! this.tableData || this.tableData.length == 0){
                            this.emptyText = '暫無數據';
                        }
                        this.total = parseInt(res.data.total)
                        this.currentPage = 1;
                    }
                })
            },
            highSearchFn(page){
                this.highSearch.pageNum = page ? page : 1;
                this.$utils.trim(this.highSearch);
                this.emptyText = '數據加載中';
                axios.post('/member/highSearch', this.highSearch).then(res => {
                    if (res.status === 200) {
                        this.tableData = res.data.list
                        if(! this.tableData || this.tableData.length == 0){
                            this.emptyText = '暫無數據';
                        }
                        this.total = parseInt(res.data.total)
                        this.currentPage = this.highSearch.pageNum;
                    }
                })
            },
            dummyAdd () {
                this.myindex += 1
                this.$router.push({ name: 'member-dummyinfo', query: {index: this.myindex} })
            },
            handleClick (item,type) {
                this.obj = item
                let ipnameno = item.ip_name_no
                console.log(item.name_type);
                if (item.name_type === 'HR' || item.name_type === 'PG') {
                    this.lookDialogVisible = true
                    this.formDialog = item
                    this.emptyText1 = '數據加載中';
                    axios.get('/ip/name/' + ipnameno + '/members').then(res => {
                        this.tableDialogData = res.data
                        if(! this.tableDialogData || this.tableDialogData.length == 0){
                            this.emptyText1 = '暫無數據';
                        }
                    })
                }
                if (item.name_type !== 'HR' && item.name_type !== 'PG') {
                    if (item.society_code.toString().indexOf('161') === -1) {
                        this.tabShow = false
                    } else {
                        this.tabShow = true
                    }
                    this.$router.push({ path: '/member-info2', name: 'member-info2',
                        query: {id: item.id, nameId:item.ip_base_no + item.ip_name_no, title: item.chinese_name ? item.chinese_name : item.name, tabShow: this.tabShow, ipBaseNo: item.ip_base_no, ipId: item.id,type:type}
                    })
                }
            },
            confirmDialog (item) {
                this.lookDialogVisible = false
                if (item.society_code && item.society_code.toString().indexOf('161') === -1) {
                    this.tabShow = false
                } else {
                    this.tabShow = true
                }

                console.log(':', item);
                this.$router.push({ path: '/member-info', name: 'member-info',
                    query: {id: item.id, nameId:item.ipBaseNo + item.nameNo, title: item.chineseName ? item.chineseName : item.name, tabShow: this.tabShow, ipBaseNo: item.ipBaseNo, ipId: item.id},
                    // params: {tabShow: this.tabShow, ipBaseNo: item.ip_base_no, ipId: item.id}
                 })
            },
            handleCurrentChange (val) {
                if(this.showHigh){
                    this.highSearchFn(val);
                }else{
                    this.formInline.page_num = val
                    this.$utils.trim(this.formInline);
                    let formData = qs.stringify(this.formInline)
                    let config = {headers: { 'content-type': 'application/x-www-form-urlencoded' }}
                    this.emptyText = '數據加載中';
                    axios.post('/ip/name/es', formData, config).then(res => {
                        if (res.status === 200) {
                            this.tableData = res.data.list
                            if(! this.tableData || this.tableData.length == 0){
                                this.emptyText = '暫無數據';
                            }
                            this.total = parseInt(res.data.total)
                            this.currentPage = val;
                        }
                    })
                }
            },
            note(row){
                this.noteInfo = row;
                this.noteShow = true;
                this.$nextTick( () => {
                    this.$refs.noteCom.init();
                })
            },
            exportList(){
                this.exportShow = true;
                this.$nextTick( () => {
                    this.$refs.exportCom.init();
                })
            },
            openHigh(){
                this.showHigh = true;
            },
            closeHigh(){
                this.showHigh = false;
            }
        },
        mounted () {
            this.$http.get('/ref/role').then( res => {
                if(res.success){
                    this.roleCodeList = res.data;
                }
            })
            this.$utils.trim(this.formInline);
            let formData = qs.stringify(this.formInline);

            let config = {headers: { 'content-type': 'application/x-www-form-urlencoded' }}
            // this.loading = true

            this.emptyText = '數據加載中';
            axios.post('/ip/name/es', formData, config).then(res => {
                // this.loading=false;
                if (res.status === 200) {
                    this.tableData = res.data.list
                    if(! this.tableData || this.tableData.length == 0){
                        this.emptyText = '暫無數據';
                    }
                    this.total = parseInt(res.data.total)
                    this.currentPage = 1;
                    // this.loading = false
                }
            })
        },
        activated () {
            console.log(new Date().getTime());
            setTimeout( () => {
            console.log(new Date().getTime());

                if(this.$route.query.update){
                    this.$utils.trim(this.formInline);
                    let formData = qs.stringify(this.formInline);
                    let config = {headers: { 'content-type': 'application/x-www-form-urlencoded' }}
                    this.emptyText = '數據加載中';
                    axios.post('/ip/name/es', formData, config).then(res => {
                        if (res.status === 200) {
                            this.tableData = res.data.list;
                            if(! this.tableData || this.tableData.length == 0){
                                this.emptyText = '暫無數據';
                            }
                            this.total = parseInt(res.data.total);
                            // this.loading = false;
                        }
                    })
                }
            }, 500)
        }
    }
</script>
<style lang="scss" scoped>
    .search-form{
        position: relative;
        /deep/ input{
            padding: 0 6px;
        }

    }
    .el-form--inline .el-form-item{
        margin-right: 2px;
    }
    /deep/ .el-dialog__header{
        text-align: left;
        font-weight: bold;
    }
    .search-form{
        // max-width: 1300px;
        margin: 0 auto;
        text-align: left;
        // &>div{
        //     display: inline-block;
        // }
    }
    .ter{
       /deep/ .el-form-item__label{
            margin-left: 0;
        }
    }
</style>

<style>
    .el-card{
        margin-bottom: 20px;
    }
    .memberdetail{
        width: 100%;
    }
    .memberdetail .el-form-item__content{
        width: calc(100% - 150px )!important;
    }
</style>
