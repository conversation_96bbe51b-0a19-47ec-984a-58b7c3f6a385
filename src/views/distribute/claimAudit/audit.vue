<template>
  <div>
    <el-collapse v-model="activeNames" class="clear" style="border-bottom: 0;">
      <el-collapse-item class="step-jump" title="File Work Info" name="1" :disabled="true">
        <div class="boxline p-t-10">
          <el-form :inline="true" label-position="left" label-width="90px">
            <div>
              <el-form-item label="WorkTitle">
                <el-input v-model="fileInfo.workTitles" readonly></el-input>
              </el-form-item>
              <el-form-item label="Composer">
                <el-input v-model="fileInfo.composerName" readonly></el-input>
              </el-form-item>
              <el-form-item label="Author">
                <el-input v-model="fileInfo.authorNames" readonly></el-input>
              </el-form-item>
              <el-form-item label="ISWC">
                <el-input v-model="fileInfo.iswc" readonly></el-input>
              </el-form-item>
              <el-form-item label="Performer">
                <el-input v-model="fileInfo.artistNames" readonly></el-input>
              </el-form-item>
              <el-form-item label="ISRC">
                <el-input v-model="fileInfo.isrc" readonly></el-input>
              </el-form-item>
            </div>
          </el-form>
        </div>
      </el-collapse-item>
      <el-collapse-item class="step-jump" title="Match Work Info" name="2" :disabled="true">
        <div class="boxline">
          <el-table :empty-text="tableresult"   :data="matchTable" v-loading='loading' border stripe height="220px" @row-click="changeWork" highlight-current-row ref="matchTableRef" class="match-table">
            <el-table-column prop="matchWorkId" label="WorkNo">
              <template slot-scope="scope">
                {{scope.row.matchWorkId}}
              </template>
            </el-table-column>
            <el-table-column prop="matchWorkSoc" label="WorkSoc">
              <template slot-scope="scope">
                {{scope.row.matchWorkSoc}}
              </template>
            </el-table-column>
            <el-table-column prop="matchWorkTitle" label="WorkTitle">
              <template slot-scope="scope">
                {{scope.row.matchWorkTitle}}
              </template>
            </el-table-column>
            <el-table-column prop="matchSocre" label="Score">
              <template slot-scope="scope">
                {{scope.row.matchSocre}}
              </template>
            </el-table-column>
          </el-table>
          <el-form class="match-info" :inline="true" label-position="left" label-width="90px">
            <div>
              <el-form-item label="Composer">
                <el-input v-model="matchInfo.matchWorkComposerName" readonly></el-input>
              </el-form-item>
              <el-form-item label="Author">
                <el-input v-model="matchInfo.matchWorkAuthor" readonly></el-input>
              </el-form-item>
              <el-form-item label="ISWC">
                <el-input v-model.number="matchInfo.matchIswc" readonly></el-input>
              </el-form-item>
            </div>
            <div>
              <div class="f-l" style="width: 49%;padding-left: 20px;box-sizing: border-box;">
                <div>Performer</div>
                <el-form-item>
                  <ul class="list" style="width: 190px;max-height: 300px;overflow-y: auto;">
                    <li class="el-sl" v-for="(item, index) in matchInfo.performerList" :key="index">{{item}}</li>
                    <li v-if="!matchInfo.performerList || matchInfo.performerList.length == 0">暂無數據</li>
                  </ul>
                </el-form-item>
              </div>
              <div class="f-l" style="width: 51%;">
                <div>ISRC</div>
                <el-form-item>
                  <ul class="list" style="width: 190px;max-height: 300px;overflow-y: auto;">
                    <li class="el-sl" v-for="(item, index) in matchInfo.isrcList" :key="index">{{item}}</li>
                    <li v-if="!matchInfo.isrcList || matchInfo.isrcList.length == 0">暂無數據</li>
                  </ul>
                </el-form-item>
              </div>
            </div>
          </el-form>
        </div>
      </el-collapse-item>
    </el-collapse>
    <div class="t-c p-t-40" style="width: 1200px;">
      <el-button type="primary" @click="submitFn('merge')">合併</el-button>
      <el-button type="primary" @click="submitFn('add')">添加</el-button>
      <el-button type="primary" @click="selectWorkFn()">指定作品</el-button>
    </div>

    <el-dialog :visible.sync="show" width="1000px" :close-on-click-modal="false">
      <div style="width: 500px;margin: auto;margin-bottom: 20px">
        <el-input @keyup.enter.native="onSubmit()" v-model="searchInfo.title" placeholder="Title" style="width: 220px;"></el-input>
        <el-input @keyup.enter.native="onSubmit()" v-model="searchInfo.workId" placeholder="Work No" style="width: 128px;"></el-input>
        <el-input @keyup.enter.native="onSubmit()" v-model="searchInfo.soc" placeholder="Soc" style="width: 60px;"></el-input>
        <el-button type="primary" @click="onSubmit()">搜索</el-button>
      </div>
      <el-table :empty-text="tableresult"   :data="tableData">
        <el-table-column property="title" label="title">
          <template slot-scope="scope">
            {{scope.row.title||scope.row.title_en}}
          </template>
        </el-table-column>
        <el-table-column property="work_id" label="workId" width="120"></el-table-column>
        <el-table-column property="work_society_code" label="workSoc" width="90"></el-table-column>
        <el-table-column property="genre_code" label="Genre" width="80"></el-table-column>
        <el-table-column property="genre_code" label="Composer" width="150">
          <template slot-scope="scope">
            {{scope.row.composer && scope.row.composer.join('、')}}
          </template>
        </el-table-column>
        <el-table-column property="genre_code" label="Author" width="150"><template slot-scope="scope">
            {{scope.row.author && scope.row.author.join('、')}}
          </template></el-table-column>
        <el-table-column label="operation" width="100">
          <template slot-scope="scope">
            <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer" title="指定作品">
              <span class="a-blue" @click="checkedWork(scope.$index,scope.row,)">合併</span>
            </span>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background layout="prev, pager, next" :total="total" @current-change="onSubmit">
      </el-pagination>
    </el-dialog>
  </div>
</template>
<script>
export default {
  data() {
    return {
      // file work info
      fileInfo: {},
      // 选中的work 的信息
      matchInfo: {},
      activeNames: ['1', '2'],
      matchTable: [],
      isrcList: [],
      performerList: [],
      tableresult: ' ',
      selectWorkTitleId: '',
      selectWork: {
        workId: '',
        soc: '',
        title: '',
        selectWorkShow: false,
        list: []
      },

      show: false,
      loading: false,
      searchInfo: {
        workId: '',
        title: '',
        soc: ''
      },
      tableData: [],
      total: 0
    }
  },
  created() {
    this.dataType = this.$route.query.type;
    this.queryInfo();
  },
  methods: {
    queryInfo() {
      this.$http.get('/work/check/getMatchsWorkDetail', { params: { id: this.$route.query.id } }).then(res => {
        if (res.success) {
          this.fileInfo = res.data;
          this.queryMatchWorks(res.data.dataId);

        }
      })
    },
    queryMatchWorks(dataId) {
      this.loading = true
            this.tableresult = '數據加載中...'
      this.$http.get('/work/check/getMatchsWorkList', { params: { dataId: dataId } }).then(res => {
        if (res.success) {
          this.matchTable = res.data;
          this.loading = false
          if (this.matchTable.length > 0) {
            //默认选中第一行
            this.$refs.matchTableRef.setCurrentRow(this.matchTable[0]);
            this.changeWork(this.matchTable[0]);
          }
            this.tableresult = this.matchTable.length == 0 ? '暫無數據' : ' '

        }
      })
    },
    changeWork(row, column, event) {
      console.log('row: ', row);
      this.matchInfo = {};
      this.selectWorkTitleId = row.matchWorkId;
      this.selectWorkNo = row.matchWorkSoc;
      this.matchInfo = row;
      if (this.matchInfo.type != 'm') { //如果不是用户指定作品查找的，就转化數據
        this.matchInfo.performerList = this.matchInfo.matchWorkArtist.split(';');
        this.matchInfo.isrcList = this.matchInfo.matchIsrc.split(';');
      }

    },
    selectWorkFn() {
      this.searchInfo = {
        workId: '',
        title: '',
        soc: ''
      };
      this.tableData = [];
      this.total = 0;
      this.show = true;
    },
    onSubmit(page) {
      let params = this.searchInfo;
      params.page = {
        pageNum: page ? page : 1,
        pageSize: 10
      }
      this.$http.post('/wrk/queryWrkWorkListEs', params).then(res => {
        if (res.success) {
          this.tableData = res.data.list;
          this.total = res.data.total;
        }
      })
    },
    checkedWork(index, row) {

      this.$msgbox.confirm('確定指定此作品?', '提示', {
        confirmButtonText: '確定',closeOnClickModal:false,
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.selectWorkNo = row.work_id;
        this.selectWorkSoc = row.work_society_code;
        this.submitFn('merge');
      }).catch(() => {
      });
    },
    queryWorkInfo() {
      this.selectWork.list = [];
      // 查詢此work no 的ipshare
      let params = {
        workId: this.selectWork.workId,
        workSocietyCode: this.selectWork.soc
      }
      this.$http.get('/wrk/getWrkWorkById', { params }).then(res => {
        if (res.success) {
          this.selectWork.list.push(res.data.wrkWork);
          this.selectWork.workInfo = res.data.wrkWork
        }
      })
    },
    submitFn(opType) {
      let url = '';

      let ajaxData = {}
      if (opType == 'add') {

        if (this.dataType == 'AVR') {
          url = '/work/check/avrWorkNew';
        } else {
          url = '/work/check/cwrWorkNew';
        }
        ajaxData = {
          dataId: this.fileInfo.dataId
        }
      } else {
        if (this.dataType == 'AVR') {
          url = '/work/check/avrWorkUpdate';
        } else {
          url = '/work/check/cwrWorkUpdate';
        }
        ajaxData = {
          dataId: this.fileInfo.dataId,
          workId: this.selectWorkTitleId,
          workSoc: this.selectWorkSoc
        }
      }
      this.loading = this.$loading();
      this.$http.post(url, ajaxData).then(res => {
        this.loading.close();
        if (res.success) {
          this.$toast({ tips: '操作成功' });
          setTimeout(() => {
            this.$bus.$emit('closeCurrentTab', () => {
              // this.$router.push({name: 'removalAuditlist', query: {update: true}})
            });
          }, 500)

        }
      })
    }
  }

}
</script>
<style lang="scss" scoped>
@import "../../../assets/scss/works.scss";
.el-collapse-item {
  width: 480px;
  margin-right: 40px;
  float: left;
}
.match-table {
  width: 100%;
}
/deep/ .match-table thead .el-checkbox {
  display: none;
}
/deep/ .match-info {
  margin-top: 10px;
}
/deep/ .match-info .el-table tr {
  background: #fff;
}
/deep/ .el-collapse-item__arrow {
  display: none;
}
/deep/ .component .el-form-item__content {
  width: 100%;
}
/deep/ .el-input.is-disabled .el-input__inner {
  background: #fff;
  color: #333;
}
/deep/ .el-table__body tr.current-row > td {
  background-color: #17b3a3;
}
/deep/ .el-form-item.f12 label {
  font-size: 13px;
}

ul.list {
  background: #fff;
  padding: 0;
  border: 1px solid #ddd;
  margin-top: 10px;
  border-radius: 4px;
  li {
    list-style: none;
    border-bottom: 1px solid #ddd;
    line-height: 26px;
    padding: 0 4px;
  }
  &:last-child {
    border-bottom: 0;
  }
}
/deep/ .el-input {
  width: 350px;
}
</style>
