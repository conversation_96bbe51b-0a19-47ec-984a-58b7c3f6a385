<template>
  <el-dialog title="地址標簽導出" :visible.sync="showExport" width="650px" :show-close="true" :close-on-click-modal="false">
    <el-form :inline="true" class="demo-form-inline">
      <el-form-item label="Payment No" label-width="110px">
          <el-input v-model="exportData.paymentNo" placeholder="" style="width:150px"></el-input>
      </el-form-item>
      <el-form-item label="Membership" label-width="110px">
        <el-select v-model="exportData.memberShip" style="width: 150px;">
          <el-option label="全部" value=""></el-option>
          <el-option label="full wirter" value="FW"></el-option>
          <el-option label="full publisher" value="FP"></el-option>
          <el-option label="associate writer" value="AW"></el-option>
          <el-option label="associate publiser" value="AP"></el-option>
          <el-option label="non member" value="NM"></el-option>
          <el-option label="reject" value="RE"></el-option>
          <el-option label="waiting" value="WA"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="排序條件" label-width="110px">
        <el-radio v-model="orderby" label="1">order of Input</el-radio>
        <el-radio v-model="orderby" label="2">name</el-radio>
        <el-radio v-model="orderby" label="3">member no</el-radio>
        <!-- <el-select multiple v-model="exportData.inputOrder" placeholder="order of Input" style="width: 205px;">
          <el-option label="address" value="FW"></el-option>
          <el-option label="attn" value="RE"></el-option>
        </el-select>
        <el-select v-model="exportData.nameOrder" placeholder="name" style="width: 90px;">
          <el-option label="Y" :value="1"></el-option>
          <el-option label="N" :value="0"></el-option>
        </el-select>
        <el-select v-model="exportData.memberNoOrder" placeholder="member no" style="width: 135px;">
          <el-option label="Y" :value="1"></el-option>
          <el-option label="N" :value="0"></el-option>
        </el-select> -->
      </el-form-item>
    </el-form>
    <el-tabs v-model="exportType" type="card">
      <el-tab-pane label="會員編號" name="code">
        <el-button size="mini" type="primary" @click="exportAddFn()">添加</el-button>
        <el-table :empty-text="tableresult"   :data="exportForm.memberNo" border stripe style="width: 100%">
          <el-table-column label="Member No">
            <template slot-scope="scope">
              <el-input v-model="scope.row.code" placeholder=""></el-input>
            </template>
          </el-table-column>
          <el-table-column label="Operation" width="220px">
            <template slot-scope="scope">
              <el-button size="mini" type="danger" @click="exportDeleteFn(scope.$index, scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="會員區間" name="range">
        <el-form :inline="false" label-width="180px">
          <el-form-item label="From Member No">
            <el-input v-model="exportForm.memberNoA" style="width: 180px"></el-input>
          </el-form-item>
          <el-form-item label="To Member No">
            <el-input v-model="exportForm.memberNoB" style="width: 180px"></el-input>
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>
    <div slot="footer">
      <el-button type="primary" @click="requData('preview')">预览</el-button>
      <el-button type="primary" @click="requData('export')">導出</el-button>
    </div>
  </el-dialog>
</template>
<script>
export default {
  name: "",
  data() {
    return {
      tableresult:' ',
      showExport: false,
      exportType: "code",
      exportData:{
        paymentNo:'',
        memberShip:'',
        inputOrder:[],
        memberNo:[],
        memberNoOrder:null,
        nameOrder:null,
        type:0
      },
      exportForm: {
        memberNo: [],
        memberNoA: "",
        memberNoB: "",
      },
      orderby:'',
    };
  },
  methods: {
    exportList() {
      this.exportData={
        paymentNo:'',
        memberShip:'',
        inputOrder:[],
        memberNo:[],
        memberNoOrder:null,
        nameOrder:null,
        type:0
      }
      this.exportForm = {
        memberNo: [],
        memberNoA: "",
        memberNoB: "",
      };
      this.showExport = true;
    },
    exportAddFn() {
      this.exportForm.memberNo.push({ code: ""});
    },
    exportDeleteFn(index) {
      this.exportForm.memberNo.splice(index, 1);
    },
    requData(data){
      let ajaxData = this.$utils.copy(this.exportData);
      if (this.exportType == "code") {
        // ajaxData.memberNo = this.exportForm.memberNo;
        if(!this.exportForm.memberNo.length && !ajaxData.paymentNo){
          this.$toast({ tips: "Payment No和Member No不能都為空" });
          return
        }
        let flag = true;
        this.exportForm.memberNo.forEach((item) => {
          if (!item.code) {
            flag = false;
          }else{
            console.log(ajaxData)
            console.log(item.code)
            ajaxData.memberNo.push(item.code)
          }
        });
        if (!flag) {
          this.$toast({ tips: "請輸入Member No" });
          return;
        }
      } else if (this.exportType == "range") {
        ajaxData.memberNoA = this.exportForm.memberNoA;
        ajaxData.memberNoB = this.exportForm.memberNoB;
        if (!ajaxData.memberNoA || !ajaxData.memberNoB) {
          this.$toast({
            tips: "請輸入From Member No 和 To Member No",
          });
          return;
        }
      }
      console.log(this.orderby)
      if(this.orderby=='1'){
        ajaxData.inputOrder=['address']
        ajaxData.nameOrder=0
        ajaxData.memberNoOrder=0
      }else if(this.orderby=='2'){
        ajaxData.nameOrder=1
        ajaxData.inputOrder=[]
        ajaxData.memberNoOrder=0
      }else if(this.orderby=='3'){
        ajaxData.memberNoOrder=1
        ajaxData.inputOrder=[]
        ajaxData.nameOrder=0
      }
      if(data=='preview'){
        ajaxData.type=0
        this.previewFn(ajaxData) 
      }else{
        ajaxData.type=1
        this.exportFn(ajaxData)
      }
    },
    previewFn(ajaxData){
      this.$http.post('/export/pdf/autoPayMemberInfo', ajaxData,{responseType:'blob'}).then( res => {
          if(res.data.type!='application/pdf'){
              this.$toast({tips: '預覽失敗'})
          }else{
            let file = new Blob([res.data],{type:'application/pdf'})
            let url = URL.createObjectURL(file)
            let aLink = document.createElement('a')
            let title=decodeURI(decodeURI(res.headers["content-disposition"] ? res.headers["content-disposition"].split(';')[1] : '地址標簽導出'))
            aLink.href = url
            aLink.target='_blank'
            // aLink.download = title
            aLink.click()
            // this.$utils.downloadByBlobPDF(res.data,res.headers["content-disposition"] ? res.headers["content-disposition"].split(';')[1] : '=地址標簽導出')
          }
      })
    },
    exportFn(ajaxData) {
      this.$http.post('/export/pdf/autoPayMemberInfo', ajaxData,{responseType:'blob'}).then( res => {
          let tempBlob = new Blob([res.data], { type: 'application/json' })
          let reader = new FileReader()
          reader.onload = e => {
            let res1 = e.target.result
            // 此处对fileReader读出的结果进行JSON解析
            // 可能会出现错误，需要进行捕获
            try {
              let json = JSON.parse(res1)
              this.$toast({ tips: json.message })
              // console.log('========',json)
              //正常json数据格式
            } catch (err) {
              // 该异常为无法将字符串转为json
              // 说明返回的数据是一个流文件
              // 不需要处理该异常，只需要捕获即刻
              this.showExport = false;
              this.$utils.downloadByBlobPDF(
              res.data,
              res.headers["content-disposition"]
                ? res.headers["content-disposition"].split(";")[1]
                : "=地址標簽導出"
              );
            }
          }
          // 将blob对象以文本的方式读出，读出完成后将会执行 onload 方法
          reader.readAsText(tempBlob)
          // if(res.data.type!='application/pdf'){
          //     this.$toast({tips: '導出失敗'})
          // }else{
          //     this.$utils.downloadByBlobPDF(res.data,res.headers["content-disposition"] ? res.headers["content-disposition"].split(';')[1] : '=地址標簽導出')
          // }
      })
    

    },
  },
};
</script>
<style scoped>
/deep/ .el-dialog__body{
  padding-top: 0;
}
/deep/ .el-radio+.el-radio{
  margin-left: 0;
}
</style>