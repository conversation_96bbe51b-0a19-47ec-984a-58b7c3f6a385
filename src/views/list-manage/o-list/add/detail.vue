<template>
  <div>
    <el-form :inline="true" class="p-t-20" ref="beseInfozhou" :model="baseInfo" :rules="rules" label-width="146px">
      <!-- <el-form :inline="true" class="p-t-20"  label-width="146px"> -->
      <el-form-item label="水單ID">
        <el-input type="text" v-model="baseInfo.receiptId" :readonly="true"></el-input>
      </el-form-item>
      <el-form-item label="协会代碼">
        <el-input type="text" v-model="baseInfo.sourceSocietyCode" :readonly="baseInfo.sourceSocietyCode?true:false"></el-input>
      </el-form-item>
      <el-form-item label="协会名稱">
        <el-input type="text" v-model="baseInfo.sourceSocietyName" :readonly="baseInfo.sourceSocietyName?true:false"></el-input>
      </el-form-item>
      <!--        distOrderNumber是同一個分配編號，同一個協會，第幾筆-->
      <el-form-item label="協會分配序號">
        <el-input type="text" v-model="baseInfo.distOrderNumber" readonly></el-input>
      </el-form-item>

      <el-form-item label="分配代號">
        <el-input type="text" v-model="baseInfo.distNo"></el-input>
      </el-form-item>
      <!--        表裡有字段，參數裡面沒有寫-->
      <el-form-item label="通知/寄送日期" prop="messageDate">
        <!-- <date-picker v-model="baseInfo.receiptDate" type="date" @blur="checkdate()" placeholder="輸入日期" format="yyyyMMdd" value-format="yyyy-MM-dd"></date-picker> -->
        <!-- <el-input   v-model="baseInfo.receiptDate" @blur="checkdate()" value-format="yyyy-MM-dd" format="yyyy-MM-dd"></el-input> -->
        <!-- <el-input v-model="baseInfo.receiptDate" value-format="yyyy-MM-dd" format="yyyy-MM-dd" maxlength="10"></el-input> -->
        <el-date-picker v-model="baseInfo.messageDate" type="date" placeholder="輸入日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd"></el-date-picker>
        <!-- <span id="eldate">

            </span> -->
      </el-form-item>
      <el-form-item label="國外分配代號">
        <el-input type="text" v-model="baseInfo.sourceDistNo"></el-input>
      </el-form-item>
      <el-form-item label="國外分配年度">
        <el-input type="text" v-model="baseInfo.sourceDistYear" placeholder="填寫年份"></el-input>
      </el-form-item>
      <el-form-item>
        <el-checkbox v-model="baseInfo.checkStatus" true-label="Y" false-label="N">检核</el-checkbox>
      </el-form-item>
      <el-form-item label="匯款幣別金額">
        <el-input type="text" style="width: 120px;" @keydown.native="$inputNumber" v-model.number="baseInfo.receiptCurrencyCode" @dblclick.native="showCurrencyLog('receiptCurrencyCode','baseInfo')" placeholder="輸入或雙擊"></el-input>
        <!-- <el-input type="text" style="width: 140px;" @keydown.native="$inputNumber" v-model="baseInfo.receiptCurrencyAmount" @input="setReceiptCurrencyAmount"></el-input> -->
        <thousand-input type="text" class="input-text-right" style="width: 140px" @keydown.native="$inputNumber" v-model.trim="baseInfo.receiptCurrencyAmount" @input="setReceiptCurrencyAmount" />
      </el-form-item>
      <el-form-item label="匯率">
        <el-input type="text" v-model="baseInfo.exchangeRate" readonly></el-input>
      </el-form-item>
      <el-form-item label="水單匯款金額">
        <!-- <el-input type="text" @keydown.native="$inputNumber" v-model="baseInfo.receiptAmount" readonly></el-input> -->
        <el-input type="text" style="width: 120px;" v-model.number="baseInfo.receiptCurrencyCode2" readonly></el-input>
        <thousand-input type="text" class="input-text-right" style="width: 140px;" v-model.trim="baseInfo.receiptAmount" readonly/>
      </el-form-item>
      <el-form-item label="銀行手續費">
        <el-input type="text" style="width: 120px;" v-model="baseInfo.receiptChargeCurrencyCode" @dblclick.native="showCurrencyLog('receiptChargeCurrencyCode','baseInfo')" placeholder="輸入或雙擊"></el-input>
        <!-- <el-input type="text" style="width: 140px;" v-model="baseInfo.receiptChargeAmount" readonly></el-input> -->
        <thousand-input type="text" style="width: 140px" v-model.trim="baseInfo.receiptChargeAmount" readonly/>
      </el-form-item>
      <el-form-item label="水單台幣入賬">
        <!-- <el-input type="text" @keydown.native="$inputNumber" v-model="baseInfo.localAmount" readonly></el-input> -->
        <thousand-input type="text" class="input-text-right" v-model.trim="baseInfo.localAmount" readonly/>
      </el-form-item>
      <!-- <el-form-item label="本次台幣沖賬金額">
        <el-input type="text" v-model="baseInfo.currentLocalAmount" hidden></el-input>
      </el-form-item> -->
      <el-form-item label="合計已沖台幣金額">
        <!-- <el-input type="text" @keydown.native="$inputNumber" v-model="baseInfo.localStrikeAmount" readonly></el-input> -->
        <thousand-input type="text" class="input-text-right" v-model.trim="baseInfo.localStrikeAmount" readonly/>
      </el-form-item>
      <div>
        <el-form-item label="審核備註">
          <el-input type="textarea" v-model="baseInfo.checkRemark" style="width: 500px;"></el-input>
        </el-form-item>
      </div>
      <el-input type="text" v-model="baseInfo.currentLocalAmount" hidden></el-input>
      <el-button type="primary" @click="toUpload(baseInfo.receiptId,baseInfo.sourceSocietyCode)">上傳文件</el-button>
    </el-form>
    <!-- summary -->
    <div class="title-box">
      <h3>summary</h3>
    </div>
    <el-table :empty-text="tableresult" stripe :data="summaryData" border class="table">
      <el-table-column label="分配幣別">
        <template slot-scope="scope">
          <el-input type="text" style="width: 120px;" v-model="scope.row.summaryCurrencyCode" @dblclick.native="showCurrencyLog('summaryCurrencyCode','summaryData',scope.row)" placeholder="輸入或雙擊"></el-input>
        </template>
      </el-table-column>
      <el-table-column label="分配金額">
        <template slot-scope="scope">
          <!-- <el-input type="text" @keydown.native="$inputNumber" @input="setReceiptAmount" v-model="scope.row.summayTotalAmount"></el-input> -->
          <thousand-input type="text" class="input-text-right" @keydown.native="$inputNumber" @input="setReceiptAmount" v-model="scope.row.summayTotalAmount" />
        </template>
      </el-table-column>
      <el-table-column label="管理費">
        <template slot-scope="scope">
          <!-- <el-input type="text" @keydown.native="$inputNumber" @input="setReceiptAmount" v-model="scope.row.summaryCommissionAmount"></el-input> -->
          <thousand-input type="text" class="input-text-right" @keydown.native="$inputNumber" @input="setReceiptAmount" v-model="scope.row.summaryCommissionAmount" />
        </template>
      </el-table-column>
      <el-table-column label="稅款">
        <template slot-scope="scope">
          <!-- <el-input type="text" @keydown.native="$inputNumber" @input="setReceiptAmount" v-model="scope.row.summaryTaxAmount"></el-input> -->
          <thousand-input type="text" class="input-text-right" @keydown.native="$inputNumber" @input="setReceiptAmount" v-model="scope.row.summaryTaxAmount" />
        </template>
      </el-table-column>
      <el-table-column label="手續費">
        <template slot-scope="scope">
          <!-- <el-input type="text" @keydown.native="$inputNumber" @input="setReceiptAmount" v-model="scope.row.summaryChargeAmount"></el-input> -->
          <thousand-input type="text" class="input-text-right" @keydown.native="$inputNumber" @input="setReceiptAmount" v-model="scope.row.summaryChargeAmount" />
        </template>
      </el-table-column>
      <el-table-column label="匯款金額">
        <template slot-scope="scope">
          <!-- <el-input type="text" :disabled="true" v-model="scope.row.summaryReceiptAmount"></el-input> -->
          <thousand-input type="text" class="input-text-right" :disabled="true" v-model="scope.row.summaryReceiptAmount" readonly/>
        </template>
      </el-table-column>
    </el-table>
    <!-- statement -->
    <div class="title-box">
      <div>
        <h3>statement</h3>
        <p>文件匯總金額：<span class="red">{{this.baseInfo.totalStatementAmount || '0'}}</span></p>
        <p>文件讀取總金額：<span class="red">{{this.baseInfo.fileStatementAmount || '0'}}</span></p>
      </div>
      <el-button type="primary" @click="addTableItem('statementData')">新增</el-button>
    </div>
    <el-table :empty-text="tableresult" stripe :data="statementData" border class="table">
      <el-table-column label="分配幣別">
        <template slot-scope="scope">
          <el-input type="text" v-if="scope.$index < statementData.length - 1" v-model="scope.row.currencyCode" @dblclick.native="showCurrencyLog('currencyCode','statementData',scope.row)" placeholder="輸入或雙擊"></el-input>
          <p style="text-align: center;margin: 0" v-else>總計</p>
        </template>
      </el-table-column>
      <el-table-column label="分配金額" align="right">
        <template slot-scope="scope">
          <!-- <el-input v-if="scope.$index < statementData.length - 1" type="text" @keydown.native="$inputNumber" v-model="scope.row.receiptAmount" @input="setStatement(scope.row)"></el-input> -->
          <thousand-input class="input-text-right" v-if="scope.$index < statementData.length - 1" type="text" @keydown.native="$inputNumber" v-model="scope.row.receiptAmount" @input="setStatement(scope.row)"/>
          <span v-else :title="scope.row.receiptAmount">{{formatThousand(scope.row.receiptAmount)}}</span>
        </template>
      </el-table-column>
      <el-table-column label="台幣金額" align="right">
        <template slot-scope="scope">
          <!-- <el-input v-if="scope.$index < statementData.length - 1" type="text" @keydown.native="$inputNumber" v-model="scope.row.localAmount" @input="setLocalStrikeAmount(scope.row)"></el-input> -->
          <thousand-input class="input-text-right" v-if="scope.$index < statementData.length - 1" type="text" @keydown.native="$inputNumber" v-model="scope.row.localAmount" @input="setLocalStrikeAmount(scope.row)"/>
          <span v-else :title="scope.row.localAmount">{{formatThousand(scope.row.localAmount)}}</span>
        </template>
      </el-table-column>
      <el-table-column label="upload總金額" align="right">
        <template slot-scope="scope">
          <!-- <el-input v-if="scope.$index < statementData.length - 1" type="text" @keydown.native="$inputNumber" v-model="scope.row.uploadAmount" @input="setStatementAmount(scope.row)"></el-input> -->
          <thousand-input class="input-text-right" v-if="scope.$index < statementData.length - 1" type="text" @keydown.native="$inputNumber" v-model="scope.row.uploadAmount" @input="setStatementAmount(scope.row)"/>
          <span v-else :title="scope.row.uploadAmount">{{formatThousand(scope.row.uploadAmount)}}</span>
        </template>
      </el-table-column>
      <el-table-column label="manual-upload總金額" align="right">
        <template slot-scope="scope">
          <!-- <el-input v-if="scope.$index < statementData.length - 1" type="text" @keydown.native="$inputNumber" v-model="scope.row.manualUploadAmount" @input="setStatementAmount(scope.row)"></el-input> -->
          <thousand-input class="input-text-right" v-if="scope.$index < statementData.length - 1" type="text" @keydown.native="$inputNumber" v-model="scope.row.manualUploadAmount" @input="setStatementAmount(scope.row)"/>
          <span v-else :title="scope.row.manualUploadAmount">{{formatThousand(scope.row.manualUploadAmount)}}</span>
        </template>
      </el-table-column>
      <el-table-column label="statement總金額" align="right">
        <template slot-scope="scope">
          <thousand-input class="input-text-right" v-if="scope.$index < statementData.length - 1" type="text" @keydown.native="$inputNumber" :disabled="true" v-model="scope.row.statementAmount"/>
          <span v-else :title="scope.row.statementAmount">{{formatThousand(scope.row.statementAmount)}}</span>
        </template>
      </el-table-column>
      <el-table-column label="no statement總金額" align="right">
        <template slot-scope="scope">
          <thousand-input class="input-text-right" v-if="scope.$index < statementData.length - 1" type="text" :disabled="true" v-model="scope.row.noStatementAmount"/>
          <span v-else :title="scope.row.noStatementAmount">{{formatThousand(scope.row.noStatementAmount )}}</span>
        </template>
      </el-table-column>
      <el-table-column label="percentage(%)">
        <template slot-scope="scope">
          <el-input v-if="scope.$index < statementData.length - 1" type="text" v-model="scope.row.percentage" @keydown.native="$inputNumber" @input="setStatement(scope.row)"></el-input>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="deduction扣除額" align="right">
        <template slot-scope="scope">
          <thousand-input class="input-text-right" v-if="scope.$index < statementData.length - 1" type="text" v-model="scope.row.deduction" :disabled="true"/>
          <span v-else :title="scope.row.deduction">{{formatThousand(scope.row.deduction || 0)}}</span>
        </template>
      </el-table-column>

      <el-table-column fixed="right" label="operation" width="100">
        <template slot-scope="scope">
          <el-button v-if="scope.$index < statementData.length - 1" @click="deleteRow(scope,'statementData')" type="text" size="small">刪除</el-button>

        </template>
      </el-table-column>
    </el-table>
    <div class="title-box">
      <div>
        <h3>FIE</h3>
        <p>文件內解析金額：<span class="red">{{this.baseInfo.fieTotalAmount || '0'}}</span></p>
      </div>
      <el-button type="primary" @click="addTableItem('fieData')">新增</el-button>
    </div>
    <el-table :empty-text="tableresult" stripe :data="fieData" border class="table">
      <el-table-column label="分配幣別">
        <template slot-scope="scope">
          <el-input v-if="scope.$index < fieData.length - 1" type="text" v-model="scope.row.currencyCode" @dblclick.native="showCurrencyLog('currencyCode','fieData',scope.row)" placeholder="輸入或雙擊"></el-input>
          <p style="text-align: center;margin: 0" v-else>總計</p>
        </template>
      </el-table-column>
      <el-table-column label="分配金額" align="right">
        <template slot-scope="scope">
          <thousand-input class="input-text-right" v-if="scope.$index < fieData.length - 1" type="text" @keydown.native="$inputNumber" v-model="scope.row.receiptAmount" @input="setStatement(scope.row)"/>
          <span v-else :title="scope.row.receiptAmount">{{formatThousand(scope.row.receiptAmount || 0)}}</span>
        </template>
      </el-table-column>
      <el-table-column label="台幣金額" align="right">
        <template slot-scope="scope">
          <thousand-input class="input-text-right" v-if="scope.$index < fieData.length - 1" type="text" @keydown.native="$inputNumber" v-model="scope.row.localAmount" @input="setLocalStrikeAmount(scope.row)"/>
          <span v-else :title="scope.row.localAmount">{{formatThousand(scope.row.localAmount || 0)}}</span>
        </template>
      </el-table-column>
      <el-table-column label="upload總金額" align="right">
        <template slot-scope="scope">
          <thousand-input class="input-text-right" v-if="scope.$index < fieData.length - 1" type="text" @keydown.native="$inputNumber" v-model="scope.row.uploadAmount" @input="setStatementAmount(scope.row)"/>
          <span v-else :title="scope.row.uploadAmount">{{formatThousand(scope.row.uploadAmount || 0)}}</span>
        </template>
      </el-table-column>
      <el-table-column label="manual-upload總金額" align="right">
        <template slot-scope="scope">
          <thousand-input class="input-text-right" v-if="scope.$index < fieData.length - 1" type="text" @keydown.native="$inputNumber" v-model="scope.row.manualUploadAmount" @input="setStatementAmount(scope.row)"/>
          <span v-else :title="scope.row.manualUploadAmount">{{formatThousand(scope.row.manualUploadAmount || 0)}}</span>
        </template>
      </el-table-column>
      <el-table-column label="statement總金額" align="right">
        <template slot-scope="scope">
          <thousand-input class="input-text-right" v-if="scope.$index < fieData.length - 1" type="text" :disabled="true" v-model="scope.row.statementAmount"/>
          <span v-else :title="scope.row.statementAmount">{{formatThousand(scope.row.statementAmount || 0)}}</span>
        </template>
      </el-table-column>
      <el-table-column label="no statement總金額" align="right">
        <template slot-scope="scope">
          <thousand-input class="input-text-right" v-if="scope.$index < fieData.length - 1" type="text" :disabled="true" v-model="scope.row.noStatementAmount"/>
          <span v-else :title="scope.row.noStatementAmount">{{formatThousand(scope.row.noStatementAmount || 0)}}</span>
        </template>
      </el-table-column>
      <el-table-column label="分配代號或年度">
        <template slot-scope="scope">
          <el-input v-if="scope.$index < fieData.length - 1" type="text" v-model="scope.row.fieDistYear"></el-input>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="operation" width="100">
        <template slot-scope="scope">
          <el-button v-if="scope.$index < fieData.length - 1" @click="deleteRow(scope,'fieData')" type="text" size="small">刪除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div >
      <!-- <el-button type="primary" style="margin-left:30px" @click="toUpload()">上傳文件</el-button> -->
        <el-upload class="upload-fie-files" style="display: inline-block;margin-left: 30px" action :on-change="handleFileChanged "
        :on-remove="handleRemove" :auto-upload="false" :file-list="fieFileList">
            <el-button size="small" type="primary">{{'點擊上傳'}}</el-button>

          </el-upload>
    </div>
    <!-- <div v-if="fieFileList.length > 0">
        <p v-for="(item, index) in fieFileList" :key="index" class="download-link" @click="download(item)">{{item.name}}</p>
    </div> -->
     <div class="btns">
      <el-button type="primary" @click="save">確認</el-button>
    </div>

    <!--    幣別列表彈窗-->
    <el-dialog title="幣別列表" :visible.sync="dialogVisible" width="60%" :close-on-click-modal="false">
      <div class="search">
        <el-input placeholder="请输入currencyCode" @keyup.enter.native='getCurrencyList()' v-model="currencyCode" class="input-with-select">
          <el-button slot="append" @click="getCurrencyList()" icon="el-icon-search"></el-button>
        </el-input>
      </div>
      <el-table :empty-text="tableresult" :data="currencyList" v-loading="currencyLoading" style="width: 100%">
        <el-table-column prop="id" label="id" width="80"></el-table-column>
        <el-table-column prop="currencyCode" label="currencyCode" width="120"></el-table-column>
        <el-table-column prop="currencyName" label="Name"></el-table-column>
        <el-table-column prop="currencyChineseName" label="ChineseName"></el-table-column>
        <el-table-column label="operation" width="100">
          <template slot-scope="scope">
            <i class="el-icon-check pointer" @click="chooseCurrency(scope.row)"></i>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background layout="prev, pager, next" :current-page="currentpage" :total="total" @current-change="handleCurrentChange">
      </el-pagination>
    </el-dialog>
  </div>
</template>

<script>
//项目目录结构如上图
// import { isEmail, isMobile, isIdentityId } from '@/utils/validate'
import { formatThousand } from '@/utils';
import ThousandInput from '@/components/thousand-input';

export default {
  inject: ['refresh'],
  data() {
    // var checkIdentitytionId = (rule, value, callback) => {
      // console.warn('=====', rule, value)
      // var errorMsg = isIdentityId(value);
      // if (errorMsg) {
      //   callback(new Error(errorMsg));
      // } else {
      //   callback()
      // }

      //   if (isNaN(value)) {
      //     // value=''
      //     callback(new Error('日期必須為8位數字，請重新輸入', '提示'));
      //     return
      //   }


      //   if (value.substring(0, 2) == 'Na') {
      //     value = ''
      //     callback(new Error('日期必須為8位數字，請重新輸入', '提示'));
      //     return;
      //   }
      //   let newVal = value.toString();
      //   if (newVal.length == 0) {
      //     // _this.$emit('input', '');
      //     // _this.$emit('change', '');
      //     return;
      //   } else if (newVal.length != 8) {
      //     // _this.$emit('input', '');
      //     // _this.$emit('change', '');
      //     callback(new Error('日期必須為8位數字，請重新輸入', '提示'));
      //     return;
      //   }
      //   let year = parseInt(newVal.substr(0, 4));

      //   let month = parseInt(newVal.substr(4, 2));
      //   if (month > 12 || month <= 0) {
      //     callback(new Error('月份必須大於0小於12，請重新輸入', '提示'));
      //     return;
      //   }

      //   let day = parseInt(newVal.substr(6, 2));

      //   if (month == 2) {
      //     if (year % 100 == 0 && year % 400 || year % 100 && year % 4 == 0) {
      //       if (day > 29) {
      //         callback(new Error('當前年份為閏年，二月沒有30日，請重新輸入', '提示'));
      //         return;
      //       }
      //     } else {
      //       if (day > 28) {
      //         callback(new Error('當前年份為平年，二月沒有29日，請重新輸入', '提示'));
      //         return;
      //       }
      //     }
      //   } else if ((month == 4 || month == 6 || month == 9 || month == 11) && day > 30) {
      //     callback(new Error('此月份沒有31日，請重新輸入', '提示'));
      //     return;
      //   } else {
      //     if (day <= 0 || day > 31) {
      //       callback(new Error('日期須大於0小於31，請重新輸入', '提示'));
      //       return;
      //     }
      //   }



    // }
    return {
      //校验规则
      rules: {
        // receiptDate: [
        //   { validator: checkIdentitytionId, trigger: "blur" }
        // ]
      },
      placeholder: '輸入日期',
      listRow: {},
      currentpage: 1,
      tableresult: '數據加載中...',
      baseInfo: {
        checkStatus: 'N',
        sourceSocietyCode: '',
        distNo: '',
        receiptDate: '',
        sourceDistNo: '',
        receiptCurrencyCode: '',
        receiptCurrencyCode2: '',
        receiptCurrencyName: '',
        exchangeRate: '',
        receiptAmount: '',
        receiptChargeCurrencyCode: '',
        receiptChargeAmount: '',
        localAmount: '',
        localStrikeAmount: '',
        checkRemark: '',
        totalStatementAmount: '',
        fileStatementAmount: ''
      },
      summaryData: [],
      statementData: [],
      fieData: [],
      currencyList: [],
      dialogVisible: false,
      currencyCode: '',
      currencyLoading: false,
      currentProp: '', //當前要修改的幣別
      currentObj: '',
      fathermessage: '',
      currentRow: {},
      total: 0,
      fieFileList: [],
      receiptDetailId: ''
    }
  },
  components: {
    ThousandInput
  },
  computed: {
    formatThousand: {
      get: () => formatThousand
    },
    localAmount() {
      let reciept = this.baseInfo.receiptAmount || 0
      let rate = this.baseInfo.exchangeRate || 0
      reciept = new BigNumber(reciept)
      rate = new BigNumber(rate)
      return reciept.multipliedBy(rate).toFixed(0)
    },
    distAmount() {
      let localAmount = this.localAmount || 0
      let charge = this.baseInfo.chargeAmount || 0
      localAmount = new BigNumber(localAmount)
      charge = new BigNumber(charge)
      return localAmount.minus(charge).toFixed(0)
    },
    statementDataTotal() {
      let res = {
        receiptAmount: 0,
        localAmount: 0,
        uploadAmount: 0,
        manualUploadAmount: 0,
        noStatementAmount: 0,
        statementAmount: 0,
        deduction: 0,
      }
      console.log(this.statementData)
      if (this.statementData.length) {
        this.statementData.map(item => {
          Object.keys(res).map(key => {
            res[key] = res[key] + Number(item[key])
          })
        })
      }
      let statementData = this.$utils.copy(this.statementData)
      statementData.push(res)
      return statementData
    },
    fieDataTotal() {

    }

  },
  updated(e) {
    if (this.fieFileList.length) {
      this.bindEventToUploadItem();
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      let id = this.$route.query.id
      this.receiptDetailId = id
      let item = {
        receiptAmount: 0,
        localAmount: 0,
        uploadAmount: 0,
        manualUploadAmount: 0,
        noStatementAmount: 0,
        statementAmount: 0,
        deduction: 0,
      }
      this.summaryData = []
      this.statementData = []
      this.fieData = []
      this.tableresult = '數據加載中...'
      this.$http.get(
          '/listOverseasReceiptDetails/getListOverseasReceiptDetailsByReceiptId',
          { params: { receiptId: id } }).then(res => {
          if (res.success && res.data.code === 200) {
            let data = res.data.data
            this.baseInfo = data || { checkStatus: 'Y' }
            this.baseInfo.receiptId = id
            this.baseInfo.receiptDate = data.receiptDate
            this.baseInfo.receiptCurrencyCode2 = this.baseInfo.receiptCurrencyCode
            this.baseInfo.sourceSocietyCode = this.$route.query.sourceSoc
            if (!this.baseInfo.summaryCurrencyCode) {
              this.baseInfo.summaryCurrencyCode = this.baseInfo.receiptCurrencyCode
            }
            this.fieFileList = res.data.data.listOverseasReceiptFieFileList || []
            this.summaryData.push(data || {})
            if (data) {
              this.statementData = data.listOverseasReceiptStatementList
              this.fieData = data.listOverseasReceiptStatementFieList
              this.statementData.push(item)
              this.fieData.push(item)
              this.calcTotal(this.statementData)
              this.calcTotal(this.fieData)
            }
          }
          this.tableresult = this.summaryData.length == 0 ? '暫無數據' : ' '
        })
    },
    bindEventToUploadItem() {
      const parentEl = (document.getElementsByClassName('upload-fie-files') || [])[0];
      if (parentEl) {
        const fileItemsEls = parentEl.getElementsByClassName('el-upload-list__item');
        for (let i = 0; i < fileItemsEls.length; i++) {
          const fileItemEl = fileItemsEls[i];
          if (!fileItemEl.hasEventClick) {
            fileItemEl.hasEventClick = 1;
            fileItemEl.addEventListener('click', () => {this.handleFieFileListItemOnClick(i)});
          }
        }
      }
    },
    handleFieFileListItemOnClick(index) {
      console.log('handleFieFileListItemOnClick:', index);
      const fileListItem = this.fieFileList[index];
      console.log('handleFieFileListItemOnClick:fileListItem', fileListItem);
      if (fileListItem.filePath) {
        this.download(fileListItem);
      } else {
        console.warn(`handleFieFileListItemOnClick:fileList[${index}].filePath is empty!`);
      }
    },
    setLocalAmount() { // 這個方法暫時未使用
      let localStrikeAmount = new BigNumber(this.baseInfo.localStrikeAmount || 0)
      let receiptChargeAmount = new BigNumber(this.baseInfo.receiptChargeAmount || 0)

      this.baseInfo.localAmount = localStrikeAmount.minus(receiptChargeAmount)
    },
    setReceiptCurrencyAmount() {
      let receiptCurrencyAmount = new BigNumber(this.baseInfo.receiptCurrencyAmount || 0)
      let exchangeRate = new BigNumber(this.baseInfo.exchangeRate || 0)
      let receiptAmount = new BigNumber(this.baseInfo.receiptAmount || 0)
      let val = receiptCurrencyAmount.multipliedBy(exchangeRate)
      // this.baseInfo.localStrikeAmount = val.toFixed(0)
      this.baseInfo.receiptChargeAmount = receiptCurrencyAmount.minus(receiptAmount).toFixed(6)
      console.log(receiptCurrencyAmount)
    },
    setLocalStrikeAmount(){
      let _localStrikeAmount = 0
      this.statementData.forEach(s => {
        let  _localAmount = new BigNumber(s.localAmount || 0)
        _localStrikeAmount = _localStrikeAmount.sum(_localAmount)
      })

      this.fieData.forEach(f => {
        let  _localAmount = new BigNumber(s.localAmount || 0)
        _localStrikeAmount = _localStrikeAmount.sum(_localAmount)
      })
      this.baseInfo.localStrikeAmount = _localStrikeAmount
    },
    save() {
      // 确认发送ajax请求-----------------------000000000000000000000
      // this.placeholder='請輸入正確的日期'

      //         this.fathermessage=true

      this.$refs.beseInfozhou.validate(validate => {
        if (validate) {
          // console.log('[[[[[[[[[', this.baseInfo.receiptDate)
          for (let key in this.summaryData[0]) {
            this.baseInfo[key] = this.summaryData[0][key]
          }

          let ajaxData = {}
          ajaxData.lord = this.baseInfo
          ajaxData.lorsList = this.statementData.slice(0, this.statementData.length - 1).concat(this.fieData.slice(0, this.fieData.length - 1))
          // let receiptAmount = ajaxData.lord.receiptAmount || 0
          // let summaryReceiptAmount = ajaxData.lord.summaryReceiptAmount || 0

          if(this.baseInfo.localAmount != this.baseInfo.localStrikeAmount){
            this.$message({
                message: '水單台幣入帳金額與STATEMENT的台幣累計⾦額+FIE的台幣累計⾦額不相等！請確認後再保存！',
                type: 'warning'
              });

              return
          }

          this.$http
          .post(
            '/listOverseasReceiptDetails/saveOrUpdateListOverseasReceiptDetails',
            ajaxData
          )
          .then(res => {
            console.log(res)
            if (res.data.code == 200) {
              this.$message({
                message: '保存成功',
                type: 'success'
              });
              this.baseInfo.distOrderNumber = res.data.data.distOrderNumber
              this.baseInfo.id = res.data.data.id
              this.init()
            } else {
              this.$toast({ tips: res.data.message })
            }
          })
        }
      })
    },
    showCurrencyLog(prop, obj, row = {}) {
      this.currentProp = prop
      this.currentObj = obj
      this.currentRow = row
      this.dialogVisible = true
      this.getCurrencyList()
    },
    getCurrencyList(page = 1) {
      this.currencyLoading = true
      if (page == 1) {
        this.total = 0
      }
      let params = {
        currencyCode: this.currencyCode,
        page_num: page,
        page_size: 10
      }
      this.$http
        .get('/ref/getRefCurrency', { params })
        .then(res => {
          console.log(res)
          if (res.success && res.data.code === 200) {
            this.currencyList = res.data.data.list
            this.total = res.data.data.total
          }
          this.currencyLoading = false
        })
    },
    chooseCurrency(row) {
      if (this.currentObj === 'baseInfo') {
        this.baseInfo[this.currentProp] = row.currencyCode
      } else {
        // this.currentRow[this.currentProp] = row.currencyCode
        this.$set(this.currentRow, this.currentProp, row.currencyCode)
      }

      this.dialogVisible = false
    },
    handleCurrentChange(page) {
      this.currentpage = page
      this.getCurrencyList(page)
    },
    addTableItem(tableName) {
      let item = {
        currencyCode: this.baseInfo.receiptCurrencyCode,
        receiptAmount: 0,
        localAmount: 0,
        uploadAmount: 0,
        manualUploadAmount: 0,
        statementAmount: 0,
        noStatementAmount: 0,
        percentage: 0,
        deduction: 0,
        type: 0
      }
      if (tableName === 'fieData') {
        item = {
          currencyCode: this.baseInfo.receiptCurrencyCode,
          receiptAmount: 0,
          localAmount: 0,
          uploadAmount: 0,
          manualUploadAmount: 0,
          statementAmount: 0,
          noStatementAmount: 0,
          fieDistYear: 0,
          type: 1
        }
      }
      this[tableName].unshift(item)
    },
    deleteRow(scope, table) {

      let index = scope.$index
      let row = scope.row
      this.$alert('確定要刪除嗎？', '刪除', {
        confirmButtonText: '确定',
        showCancelButton: true,
        cancelButtonText: '取消',
        callback: action => {
          if (action == 'confirm') {
            if (row.id) {
              this.$http.delete('/listOverseasReceiptDetails/deleteListOverseasReceiptStatement/' + row.id).then(res => {
                console.log(res)
                if (res.success && res.data.code == 200) {
                  this.$toast({ tips: '刪除成功' })
                  this[table].splice(index, 1)

                }
              })
            } else {
              this[table].splice(index, 1)
            }
          }
          this.calcTotal(this[table])
        }
      })

    },
    checkdate() {

      alert('000000000000000')
    },
    setReceiptAmount(val) {
      let data = this.summaryData[0]
      let summayTotalAmount = new BigNumber(data.summayTotalAmount || 0)
      let summaryCommissionAmount = new BigNumber(data.summaryCommissionAmount || 0)
      let summaryTaxAmount = new BigNumber(data.summaryTaxAmount || 0)
      let summaryChargeAmount = new BigNumber(data.summaryChargeAmount || 0)
      let res = summayTotalAmount.minus(summaryCommissionAmount).minus(summaryTaxAmount).minus(summaryChargeAmount)
      data.summaryReceiptAmount = res.toFixed(6)
    },
    setStatement(data) {
      console.log(data)
      let receiptAmount = new BigNumber(data.receiptAmount || 0)
      let rate = new BigNumber(this.baseInfo.exchangeRate || 0)
      let statementAmount = new BigNumber(data.statementAmount || 0)
      let summayTotalAmount = new BigNumber(this.summaryData[0].summayTotalAmount || 0)
      let currentLocalAmount = new BigNumber(this.baseInfo.currentLocalAmount || 0)
      if (Number(summayTotalAmount.toFixed(0))) {
        data.localAmount = receiptAmount.dividedBy(summayTotalAmount).multipliedBy(currentLocalAmount).toFixed(0)
      } else {
        data.localAmount = 0
      }
      let res = receiptAmount.minus(statementAmount)
      data.noStatementAmount = res.toFixed(6)
      let percentage = new BigNumber(data.percentage / 100 || 0)
      let localAmount = new BigNumber(data.localAmount || 0)
      data.deduction = localAmount.multipliedBy(percentage).toFixed(0)
      // let _localStrikeAmount = 0
      this.setLocalStrikeAmount(data)
    },
    setStatementAmount(data) {
      debugger
      console.log(data)
      let uploadAmount = new BigNumber(data.uploadAmount || 0)
      let manualUploadAmount = new BigNumber(data.manualUploadAmount || 0)
      let receiptAmount = new BigNumber(data.receiptAmount || 0)
      let statementAmount = uploadAmount.plus(manualUploadAmount)
      data.statementAmount = statementAmount.toFixed(6)
      data.noStatementAmount = receiptAmount.minus(statementAmount).toFixed(6)
      if (data.type == 0) {
        this.calcTotal(this.statementData)
        console.log('this.statementData', this.statementData)
      } else {
        this.calcTotal(this.fieData)
      }
    },
    setLocalStrikeAmount(data){
      if (data.type == 0) {
        this.calcTotal(this.statementData)
        let _statementLocalAmount = new BigNumber(this.statementData[this.statementData.length - 1].localAmount || 0)
        let _fieLocalAmount = new BigNumber(this.fieData[this.fieData.length - 1].localAmount || 0)
        this.baseInfo.localStrikeAmount = _statementLocalAmount.plus(_fieLocalAmount)
      } else {
        this.calcTotal(this.fieData)
        let _statementLocalAmount = new BigNumber(this.statementData[this.statementData.length - 1].localAmount || 0)
        let _fieLocalAmount = new BigNumber(this.fieData[this.fieData.length - 1].localAmount || 0)
        this.baseInfo.localStrikeAmount = _statementLocalAmount.plus(_fieLocalAmount)
      }
    },
    calcTotal(arr) {
      // 传入的statementData和fieData因为有一栏总计，所以要先去掉
      let data = this.$utils.copy(arr)
      data = data.slice(0, data.length - 1)
      console.log(data)
      let total = {
        receiptAmount: 0,
        localAmount: 0,
        uploadAmount: 0,
        manualUploadAmount: 0,
        noStatementAmount: 0,
        statementAmount: 0,
        deduction: 0,
      }
      if (data.length) {
        data.map(item => {
          Object.keys(total).map(key => {
            if(key == 'localAmount'){
              total[key] = new BigNumber(total[key]).plus(new BigNumber(Number(item[key]))).toFixed(0)
            } else {
              total[key] = new BigNumber(total[key]).plus(new BigNumber(Number(item[key]))).toFixed(6)
            }

          })
        })

      }
      arr.splice(-1, 1, total)
    },
    toUpload(id, soc) {
      this.$router.push({ name: 'oListStep2', query: { id, soc } })
    },
    handleFileChanged (file,fileList) {
      // this.fieFileList.push(file.raw)
      console.log(fileList)
      console.log("745")
       // 检查是否有重复文件，有的话删除新选择的文件
      if(fileList.findIndex(f=>f.name===file.name)!=fileList.findLastIndex(f=>f.name===file.name))
      {
        this.$message({
          message: file.name +' 文件已存在',
          type: 'warning'
        });
        fileList.pop()
      } else {
        // this.fieFileList.push(file.raw)
        this.uploadFile(file)
      }
    },
    handleRemove(file, fileList){
    //  this.fieFileList.findIndex(f => f.name === file.name)
      console.log(fileList)
      console.log(file)
      this.$http.delete('/listOverseasReceiptDetails/deleteFile/' + file.id).then(res => {
        console.log(res)
        if (res.status == 200) {
          this.$toast({ tips: res.data })
          // file.id =
        } else {
          this.$toast({ tips: '刪除失敗' })
        }
      })
    },
    uploadFile(file) {
      let formData = new FormData()
      formData.append('files', file.raw)
      formData.append('receiptDetailsId',this.receiptDetailId)
      this.$http
        .post('/listOverseasReceiptDetails/uploadFile', formData)
        .then(res => {
          if (res.success) {
            file.id = res.data
            // let type = 'warning'
            // let msg = res.data
            // if (res.data.indexOf('overseas-') !== -1) {
            //   this.$message({
            //     message: '上傳成功',
            //     type: 'success'
            //   })
            // } else {
            //   this.$message({
            //     message: msg,
            //     type: type
            //   })
            // }

          }
        })
    },
    download(item) {
      this.$http.get('/listOverseasFileBase/download', { params: { absoluteFileName: item.filePath }, responseType: "arraybuffer" }).then((res) => {
        console.log(res)
        if (res.data) {
          let type = 'pdf'
          let blob = new Blob([res.data], {
            type: res.headers['content-type']      //将会被放入到blob中的数组内容的MIME类型
          });
          let objectUrl = URL.createObjectURL(blob);  //生成一个url
          // window.location.href = objectUrl;   //浏览器打开这个url
          let a = document.createElement('a')
          a.href = objectUrl
          a.download = item.name
          console.log(a)
          a.click()
        }
      })
    },
  }

}
</script>
<style lang="scss" scoped>
/deep/ .el-dialog__title {
  font-weight: bold;
}
/deep/ .el-dialog__body {
  text-align: center;
  padding-top: 0;
}
/deep/ .el-dialog__body td {
  padding: 0;
}
.table {
  width: 98%;
  margin: auto;
  margin-bottom: 20px;
}

.btns {
  text-align: center;

  & > button {
    margin-right: 20px;
  }
}

/deep/ .el-select,
.el-switch {
  width: 207px;
}

/deep/ .el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 207px;
}

.title-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 11px;
  > div {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    p {
      margin-left: 20px;
    }
  }
}

.search {
  padding-bottom: 20px;
}
/deep/ .el-form-item__error {
  width: 300px;
}
.download-link {
  margin: 10;
  cursor: pointer;
  &:hover {
    text-decoration: underline;
  }
}
</style>
