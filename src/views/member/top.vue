<template>
  <div class="container">

    <el-steps
      direction="vertical"
      finish-status="wait"
      :active="activeStep">
      <el-step
        v-for="(item, index) in steps"
        :key="index"
        icon=" "
        :title="item.name"
        @click.native="jump(index)">
        <dl-icon slot="icon" :type="index === 0 ?  'yinzhang' : 'shixinyuan'"
                 :color="index === 0 ?  '#383636' : '#BEC0C2'" size="0.5"></dl-icon>
      </el-step>
    </el-steps>

    <div class="step-jump">
      <p>代課教師規劃設計考了多少黑科技</p>
      <p>代課教師規劃設計考了多少黑科技</p>
      <p>代課教師規劃設計考了多少黑科技</p>
      <p>代課教師規劃設計考了多少黑科技</p>
      <p>代課教師規劃設計考了多少黑科技</p>
      <p>代課教師規劃設計考了多少黑科技</p>
      <p>代課教師規劃設計考了多少黑科技</p>
      <p>代課教師規劃設計考了多少黑科技</p>
      <p>代課教師規劃設計考了多少黑科技</p>
      <p>代課教師規劃設計考了多少黑科技</p>
    </div>

    <div class="step-jump">
      <p>代課教師規劃設計考了多少黑科技</p>
      <p>代課教師規劃設計考了多少黑科技</p>
      <p>代課教師規劃設計考了多少黑科技</p>
      <p>代課教師規劃設計考了多少黑科技</p>
      <p>代課教師規劃設計考了多少黑科技</p>
      <p>代課教師規劃設計考了多少黑科技</p>
      <p>代課教師規劃設計考了多少黑科技</p>
      <p>代課教師規劃設計考了多少黑科技</p>
      <p>代課教師規劃設計考了多少黑科技</p>
      <p>代課教師規劃設計考了多少黑科技</p>
    </div>

    <div class="step-jump">
      <p>代課教師規劃設計考了多少黑科技</p>
      <p>代課教師規劃設計考了多少黑科技</p>
      <p>代課教師規劃設計考了多少黑科技</p>
      <p>代課教師規劃設計考了多少黑科技</p>
      <p>代課教師規劃設計考了多少黑科技</p>
      <p>代課教師規劃設計考了多少黑科技</p>
      <p>代課教師規劃設計考了多少黑科技</p>
      <p>代課教師規劃設計考了多少黑科技</p>
      <p>代課教師規劃設計考了多少黑科技</p>
      <p>代課教師規劃設計考了多少黑科技</p>
    </div>

    <div class="step-jump">
      <p>代課教師規劃設計考了多少黑科技</p>
      <p>代課教師規劃設計考了多少黑科技</p>
      <p>代課教師規劃設計考了多少黑科技</p>
      <p>代課教師規劃設計考了多少黑科技</p>
      <p>代課教師規劃設計考了多少黑科技</p>
      <p>代課教師規劃設計考了多少黑科技</p>
      <p>代課教師規劃設計考了多少黑科技</p>
      <p>代課教師規劃設計考了多少黑科技</p>
      <p>代課教師規劃設計考了多少黑科技</p>
      <p>代課教師規劃設計考了多少黑科技</p>
    </div>

    <div class="step-jump">
      <p>代課教師規劃設計考了多少黑科技</p>
      <p>代課教師規劃設計考了多少黑科技</p>
      <p>代課教師規劃設計考了多少黑科技</p>
      <p>代課教師規劃設計考了多少黑科技</p>
      <p>代課教師規劃設計考了多少黑科技</p>
      <p>代課教師規劃設計考了多少黑科技</p>
      <p>代課教師規劃設計考了多少黑科技</p>
      <p>代課教師規劃設計考了多少黑科技</p>
      <p>代課教師規劃設計考了多少黑科技</p>
      <p>代課教師規劃設計考了多少黑科技</p>
    </div>




  </div>
</template>
<script>
  export default {
    data () {
      return {
        scroll: '',
        steps: [
          {
            name: '第一條',
            backgroundcolor: '#90B2A3'
          }, {
            name: '第二条',
            backgroundcolor: '#A593B2'
          }, {
            name: '第三条',
            backgroundcolor: '#A7B293'
          }, {
            name: '第四条',
            backgroundcolor: '#0F2798'
          }, {
            name: '第五条',
            backgroundcolor: '#0A464D'
          }],
        navList: [1, 2, 3, 4, 5]
      }
    },
    methods: {
      jump (index) {
        // 用 class="d_jump" 添加锚点
        let jump = document.querySelectorAll('.step-jump')
        let total = jump[index].offsetTop
        let distance = document.documentElement.scrollTop || document.body.scrollTop
        // 平滑滚動，时长500ms，每10ms一跳，共50跳
        let step = total / 50
        if (total > distance) {
          smoothDown()
        } else {
          let newTotal = distance - total
          step = newTotal / 50
          smoothUp()
        }
        function smoothDown () {
          if (distance < total) {
            distance += step
            document.body.scrollTop = distance
            document.documentElement.scrollTop = distance
            setTimeout(smoothDown, 10)
          } else {
            document.body.scrollTop = total
            document.documentElement.scrollTop = total
          }
        }
        function smoothUp () {
          if (distance > total) {
            distance -= step
            document.body.scrollTop = distance
            document.documentElement.scrollTop = distance
            setTimeout(smoothUp, 10)
          } else {
            document.body.scrollTop = total
            document.documentElement.scrollTop = total
          }
        }
      },
      onScroll () {
        let scrolled = document.documentElement.scrollTop || document.body.scrollTop
        // 586、1063分別為第二個和第三個锚点对应的距离
        if (scrolled >= 1063) {
          this.steps.active = 2
        } else if (scrolled < 1063 && scrolled >= 586) {
          this.steps.active = 1
        } else {
          this.steps.active = 0
        }
      }

    },
    watch: {
      scrollTop (val) {
        this.onScroll()
        this.pageTop = val
      }
    },
    mounted () {
      this.$nextTick(function () {
        window.addEventListener('scroll', this.onScroll)
      })
    }
  }
</script>

<style>
  * {
    padding: 0;
    margin: 0;
  }
  .nav1 {
    display: block;
    width: 40px;
    height: 40px;
    text-align: center;
    line-height: 40px;
    background: #eee;
    margin: 10px 0;
  }
  .current {
    color: #fff;
    background: red;
  }
</style>
