<template>
  <!-- 拆分後的文件 用于審核用的 -->
  <!-- @close="clearExport" -->
  <div class="ipibpox">
    <el-form :inline="true" :model="search" ref="form" class="demo-form-inline" :rules="rules"  @keyup.enter.native='searchFn()'>
      <el-form-item>
        <!-- <span class="red">*</span> -->
        <el-input v-model.trim="search.fileBaseId" placeholder="FID" style="width: 80px"></el-input>
      </el-form-item>
      <el-form-item label="" prop="startDate">
        <date-picker v-model="search.perDate" placeholder="Perf. Date" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd" style="width: 120px"></date-picker>
      </el-form-item>
      <el-form-item>
        <el-input v-model="search.categoryCode" placeholder="Category Code" style="width: 120px"></el-input>
      </el-form-item>
      <el-form-item>
        <el-checkbox v-model="ischecked" :disabled="true">dist</el-checkbox>
      </el-form-item>
      <el-form-item>
        <el-select v-model="search.type" placeholder="Type" style="width: 120px">
          <el-option v-for="(item, index) in config.type" :key="index" :value="item.value" :label="item.label"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="search.matchScore" placeholder="分數選擇" style="width: 116px;">
          <el-option v-for=" (item,index) in config.score" :key="index" :value="item.value" :label="item.label"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="search.status" placeholder="所有狀態" style="width: 120px">
          <el-option v-for="(item, index) in config.status" :key="index" :value="item.value" :label="item.label"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="searchFn()" v-if="isAuth('list-manage:general-list:sing:singList:find')">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="success" @click="auditBatch(1)" v-if="isAuth('list-manage:general-list:sing:singList:pass')">批量通過</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="danger" @click="auditBatch(2)" v-if="isAuth('list-manage:general-list:sing:singList:refuse')">批量拒絕</el-button>
      </el-form-item>
      <el-form-item>
        <el-upload class="upload-demo" :disabled='tableData.length==0||search.fileBaseId==""' ref="upload" action="" accept=".xls,.xlsx" :auto-upload="false" :on-change="uploadChange" :show-file-list="false" :file-list="fileList">
          <el-button size="small" type="primary" @click="beforeupload()" style="background-color: #b240ff; border-color: #b240ff" v-if="isAuth('list-manage:general-list:sing:singList:exportin')">審核結果導入</el-button>
        </el-upload>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="exportFn" v-if="isAuth('list-manage:general-list:sing:singList:exportout')">審核結果導出</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="checkfid()" v-if="isAuth('list-manage:general-list:sing:singList:add')">新增</el-button>
      </el-form-item>
      <el-form-item>
        <span class="clear-search" @click="clearSearch()" v-if="isAuth('list-manage:general-list:sing:singList:find')">清除搜索</span>
      </el-form-item>
      <div>
        <el-form-item v-if="titilehost">
          <!-- <span>Title:{{ titlename }}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span> -->
          <span @click="edittitleshow()" style="color:#409EFF">Title:
            {{titilehost}}
            <!-- {{titilehost}} &nbsp;&nbsp;{{titledata}}
            主辦{{titlename}}，共{{titilenumber}}場，共{{titilemusicnu}}首，共{{titilenumberc}}次 -->
            <!-- 雙擊concert title彈窗顯示項：perf Date、concert Title -->

          </span>
          <!-- <span>作品数:{{ worknum }}</span> -->
        </el-form-item>
      </div>
    </el-form>
    <el-table :empty-text="emptyText" stripe :data="tableData" border style="width: 100%" @selection-change="selectionChangeHandle">
      <el-table-column type="selection"> </el-table-column>
      <el-table-column prop="fileBaseId" label="FID" width="80px">
      </el-table-column>
      <el-table-column prop="title" label="Title"> </el-table-column>
      <el-table-column prop="authors" label="Author" min-width="100">
        <template slot-scope="scope">
          {{ scope.row.authors | addSpace }}
        </template>
      </el-table-column>
      <el-table-column prop="composers" label="Composer" min-width="120">
        <template slot-scope="scope">
          {{ scope.row.composers | addSpace }}
        </template>
      </el-table-column>
      <el-table-column label="Duration" width="120px">
        <template slot-scope="scope">
          <span v-if="scope.row.durationM || scope.row.durationS">{{
            scope.row.durationM + ":" + scope.row.durationS
          }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="clickNumber" label="ClickNum"> </el-table-column>
      <el-table-column prop="matchWorkTitle" label="Match Work Title" min-width="170px">
      </el-table-column>
      <el-table-column label="Soc-WorkId" min-width="150">
        <template slot-scope="scope">
          {{
            (scope.row.matchWorkSocietyCode
              ? scope.row.matchWorkSocietyCode
              : "") + (scope.row.matchWorkId ? "-" + scope.row.matchWorkId : "")
          }}
        </template>
      </el-table-column>
      <!-- <el-table-column prop="title" label="MatchAuthor"> </el-table-column>
      <el-table-column prop="title" label="MatchComposer"> </el-table-column> -->
      <el-table-column prop="createTime" label="CreateTime">
        <!-- <template slot-scope="scope">
          {{ scope.row.createTime | splitDate }}
        </template> -->
      </el-table-column>
      <el-table-column prop="amendTime" label="UpdateTime">
        <!-- <template slot-scope="scope">
          {{ scope.row.amendTime | splitDate }}
        </template> -->
      </el-table-column>
      <el-table-column prop="matchScore" label="Score" width="70px">
      </el-table-column>
      <el-table-column prop="extJson" label="Remark" width="70px">
        <template slot-scope="scope">
          <span v-if="scope.row.extJson=='{}'"></span>
          <span v-else>{{ scope.row.extJson }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="matchScore" label="Status" width="80px">
        <template slot-scope="scope">
          {{ scope.row.status | Status }}
        </template>
      </el-table-column>

      <el-table-column fixed="right" label="operation" width="200">
        <template slot-scope="scope">
          <el-button @click="switchDist(scope.row,scope.$index)" type="text" size="small" v-if="isAuth('list-manage:general-list:sing:singList:audit')">審核</el-button>
          <el-button @click="auditdetail(scope.row)" type="text" size="small" v-if="isAuth('list-manage:general-list:sing:singList:change')">編輯</el-button>
          <el-button @click="deleteitem(scope.row)" type="text" size="small" v-if="isAuth('list-manage:general-list:sing:singList:del')">刪除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination background layout=" prev, pager, next" :total="total" :current-page.sync="currentPage" @current-change="handleCurrentChange">
    </el-pagination>
    <el-dialog title="選擇" :visible.sync="exportVisible2" width="1045px" :close-on-click-modal="false">
      <el-table :empty-text="emptyText1" stripe @row-click="singleElection" :data="tableDatacheck" height="490" border style="width: 100%">
        <el-table-column prop="categoryCode" label="categoryCode"></el-table-column>
        <el-table-column prop="title" label="title"></el-table-column>
        <el-table-column prop="id" label="FID"></el-table-column>
        <el-table-column prop="listFileStartTime" label="Perform Date">
          <template slot-scope="scope">{{scope.row.listFileStartTime | splitDate}}</template>
        </el-table-column>
        <el-table-column prop="sourceCompany" label="create user"></el-table-column>
        <!-- <el-table-column type="selection" label="选择" width="55"> </el-table-column> -->
        <el-table-column label="" width="65">
          <template slot-scope="scope">
            <el-radio class="radio" v-model="templateSelection" :label="scope.$index">&nbsp;</el-radio>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="uploadType" label="选择"></el-table-column> -->
      </el-table>
      <el-pagination background layout="prev, pager, next" v-if="pageshow" :total="totalcheck" :current-page.sync="currentPagechange" @current-change="handleCurrentChangecheck">
      </el-pagination>
      <div style="width: 520px; display: flex; flex-direction: row-reverse">
        <el-button type="primary" @click="onSubmit()">確定</el-button>
        <el-button style="margin-right: 10px" @click="cancelselect()">取消</el-button>
      </div>
    </el-dialog>
    <!-- :rules="exportRules"  -->
    <el-dialog :title="isadd?'新增':'編輯'" :visible.sync="adddialogshow" width="500px" @close="clearExport" :close-on-click-modal="false">
      <el-form :model="addformData" ref="addformData" :inline="false" label-width="152px" label-position="right" :rules="newaddrules">
        <el-form-item label="Work title" prop="title">
          <el-input type="text" v-model="addformData.title" style="width: 222px"></el-input>
        </el-form-item>
        <el-form-item label="author" prop="authors">
          <el-input type="text" v-model="addformData.authors" placeholder="author" style="width: 222px"></el-input>
        </el-form-item>
        <el-form-item label="composers" prop="composers">
          <el-input type="text" v-model="addformData.composers" placeholder="" style="width: 222px"></el-input>
        </el-form-item>
        <el-form-item v-if="isadd" label="performer" prop="artists">
          <el-input type="text" v-model="addformData.artists" placeholder="" style="width: 222px"></el-input>
        </el-form-item>
        <div style="width: 250px;display:flex">
          <div class='durationcss' style="width:50%;">
            
            <el-form-item label="duration" prop="durationM">
              <el-input v-model="addformData.durationM" style="width: 97px" placeholder="M"></el-input>
            </el-form-item>
          </div>
          <div class='durationcss' style="width:50%;">
            <el-form-item label=":" prop="durationS">
              <el-input v-model="addformData.durationS" style="width: 97px" placeholder="S"></el-input>
            </el-form-item>
          </div>
        </div>
        <el-form-item v-if="isadd" label="clickNumber">
          <el-input v-model="addformData.clickNumber" style="width: 222px"></el-input>
        </el-form-item>
        <el-form-item v-if="isadd" label="match work id" prop="">
          <el-input type="text" v-model="addformData.matchWorkId" placeholder="雙擊查詢" @dblclick.native="getWork" readonly style="width: 222px"></el-input>
        </el-form-item>
        <el-form-item v-if="isadd" label="match work soc" prop="">
          <el-input type="text" v-model="addformData.matchWorkSocietyCode" placeholder="雙擊查詢" @dblclick.native="getWork" style="width: 222px" readonly></el-input>
        </el-form-item>
        <el-form-item v-if="isadd" label="match title" prop="matchtitle">
          <el-input type="text" v-model="addformData.matchWorkTitle" ref="category" style="width: 222px" placeholder="雙擊查詢" readonly @dblclick.native="getWork"></el-input>
        </el-form-item>
        <el-form-item label="remark" prop="remark">
          <el-input type="text" v-model="addformData.extJson" ref="category" style="width: 222px" placeholder=""></el-input>
        </el-form-item>
        <el-form-item v-if="!isadd" label="Usage" prop="clickNumber">
          <el-input type="text" v-model="addformData.clickNumber" ref="category" style="width: 222px" placeholder=""></el-input>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="adddialogshow = false">取 消</el-button>
        <el-button type="primary" @click="saveadd">Save</el-button>
      </div>
    </el-dialog>
    <el-dialog close="editInput" title="修改標題" :visible.sync="showtitle" width="700px" :close-on-click-modal="false">
      <el-form :inline="true" :model="updateinfo" label-width="209px" label-position="right" class="demo-form-inline titleupdata" ref="addformDatatext" :rules="addrules">
        <!-- 双击弹窗需要修改的字段有 title（活动名称）、listFileStartTime（）、sourceCompany（）、workName（授权曲目）、session（场次）、numberOfLines（歌曲数量）、clickNumber （播放次数），concertTitle（标题模板）；  -->
        <el-form-item class='singerH' :label="'活動名稱\ntitle'" prop="title" style="width: 90%">
          <el-input type="text" v-model="updateinfo.title"></el-input>
        </el-form-item>
        <el-form-item class='singerH' :label="'活動日期\nlistFileStartTime'" prop="listFileStartTime" style="width: 90%">
          <!-- <el-col :span="11">
            <el-date-picker type="date" placeholder="选择日期" value-format="yyyy-MM-dd" format="yyyy-MM-dd" v-model="updateinfo.listFileStartTime" style="width: 100%;"></el-date-picker>
          </el-col> -->
          <div id="eldate">
            <div id="cover" style="width:155px;height:35px;position: absolute;z-index:100"></div>
            <el-input type="date" v-model="updateinfo.listFileStartTime" value-format="yyyy-MM-dd" format="yyyy-MM-dd"></el-input>
          </div>
          <!-- <template slot-scope="scope">{{scope.row.listFileStartTime | splitDate}}</template> -->
        </el-form-item>
        <el-form-item class='singerH' :label="'主辦方\nsourceCompany'" prop="sourceCompany" style="width: 90%">
          <el-input type="text" v-model="updateinfo.sourceCompany"></el-input>
        </el-form-item>
        <el-form-item class='singerH' :label="'授權曲目\nworkName'" prop="workName" style="width: 90%">
          <el-input type="text" v-model="updateinfo.workName"></el-input>
        </el-form-item>
        <el-form-item class='singerH' :label="'場次\nsession'" prop="session" style="width: 90%">
          <el-input type="text" v-model="updateinfo.session"></el-input>
        </el-form-item>
        <el-form-item class='singerH' :label="'歌曲數目\nnumberOfLines'" prop="numberOfLines" style="width: 90%">
          <el-input type="text" v-model="updateinfo.numberOfLines" oninput="value=value.replace(/\D/^0/g,'')" onblur="value=value .replace(/\D/^0/g,'')"></el-input>
        </el-form-item>

        <el-form-item class='singerH' :label="'播放次數\nclickNumber'" prop="clickNumber" style="width: 90%">
          <el-input type="text" v-model="updateinfo.clickNumber" oninput="value=value.replace(/\D/^0/g,'')" onblur="value=value .replace(/\D/^0/g,'')"></el-input>
        </el-form-item>
        <el-form-item class='singerH' :label="'標題模板\nTitleTemplate'" prop="concertTitleTemplate" style="width: 90%">
          <el-input type="text" v-model="updateinfo.concertTitleTemplate"></el-input>
        </el-form-item>
        <el-form-item class='singerH' :label="'是否打印表尾\nisPrintTail'" prop="isPrintTail" style="width: 90%">
          <el-select v-model="updateinfo.isPrintTail" placeholder="请选择">
            <el-option label="打印" value="1"></el-option>
            <el-option label="不打印" value="0"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closetitledialog()">取 消</el-button>
        <el-button type="primary" @click="edittitlesave">Save</el-button>
      </div>
    </el-dialog>
    <select-work v-if="workTableVisible" ref="selectWorkCom" :search="workSearch" @checkWork="checkWork"></select-work>
  </div>
</template>

<script>
//  import axios from '../../utils/httpRequest'
//   import qs from 'qs'
import selectWork from "@/components/select-work";
export default {
  name: "sampleDateList",
  data() {

    function IsDate(rule, str, callback) {
      var reg = /^(\d{4})-(\d{2})-(\d{2})$/;
      var arr = reg.exec(str);
      //  if (str=="") return true;
      if (!reg.test(str) || RegExp.$2 > 12 || RegExp.$3 > 31) {
        //  alert("请保证输入的日期格式为2019-01-01类型的正确日期!"); 
        //  callback(new Error('不能大於1000min或者小於0min！'))
        //  return false;
        callback(new Error('请保证输入的日期格式为2019-01-01类型的正确日期!'))

      } else {

        callback()
      }
    }
    return {
      pageshow:true,
      searchparam: {},
      type: [],
      tableData: [],
      ischecked: false,
      loading: false,
      isadd: false,
      workSearch: {},
      updateinfo: {},
      workTableVisible: false,
      total: 0,
      rules: {
        fileBaseId: [{ required: true, message: "請輸入FID", trigger: "blur" }],
      },
      newaddrules: {
        title: [{ required: true, message: "請輸入Title", trigger: "blur" }],
        durationM: [{ message: '请输入数字值', pattern: /^(([1-9]\d?)|(0))*$/ }],
        durationS: [{ message: '请输入数字值', pattern: /^(([1-9]\d?)|(0))*$/ }],
        clickNumber: [{ message: '请输入数字值', pattern: /^[0-9]*[1-9][0-9]*$/ }],
        matchWorkId: [{ required: true, message: "請輸入matchworkid", trigger: "blur" }],
        matchWorkSocietyCode: [{ required: true, message: "請輸入 matchworksoc", trigger: "blur" }],
      },
      addrules: {
        title: [{ required: true, message: "請輸入Title", trigger: "blur" }],
        numberOfLines: [{ required: true, message: "請輸入numberOfLines", trigger: "blur" }, { message: '请输入数字值', pattern: /^[0-9]*[1-9][0-9]*$/ }],
        clickNumber: [{ required: true, message: "請輸入clickNumber", trigger: "blur" }, { message: '请输入数字值', pattern: /^[0-9]*[1-9][0-9]*$/ }],
        listFileStartTime: [{ required: true, message: "請輸入listFileStartTime", trigger: "blur" }, { validator: IsDate, trigger: ['blur', 'change'] }],
        sourceCompany: [{ required: true, message: "請輸入sourceCompany", trigger: "blur" }],
      },
      addformData: {
        albumTitle: "",
        amendTime: "",
        artists: "",
        authors: "",
        autoMatch: "",
        categoryCode: "",
        channelName: "",
        clickNumber: "",
        composers: "",
        createTime: "",
        durationM: "",
        durationS: "",
        episodeNo: "",
        extJson: "",
        fileBaseId: "",
        fileMappingId: "",
        id: "",
        isrc: "",
        iswc: "",
        matchScore: "",
        matchWorkId: "",
        matchWorkSocietyCode: "",
        matchWorkTitle: "",
        matchWorkTitleId: "",
        matchWorkType: "",
        matchWorkUniqueKey: "",
        performTime: "",
        poolCode: "",
        poolRight: "",
        publisher: "",
        sourceCode: "",
        status: "",
        title: "",
        tvName: "",
        uniqueKeyMd5: "",
        uploadTime: "",
        uploadType: "",
        uploadUserId: "",
        uploadUserName: "",
      },
      adddialogshow: false,
      showtitle: false,
      hasfid: true,
      titlename: "",
      titledata: "",
      titilehost: "",
      titlemusic: "",
      titilenumber: "",
      titilemusicnu: "",
      titilenumberc: "",
      worknum: "",
      templateSelection: "",
      exportVisible2: false,
      tableDatacheck: "", 
      totalcheck: "",
      edittitleform: {
        matchWorkTitle: "",
        authors: "",
      },
      search: {
        fileBaseId: "",
        id: "",
        type: "0",
        matchScore: '',
        categoryCode: "",
        uploadTime: "",
        perDate: "",
        sourceCode: "",
        status: "",
        uploadType: "",
      },
      currentPage: 1,
      currentPagechange: 1,
      config: {
        status: [
          {
            value: "",
            label: "全部狀態",
          },
          {
            value: "0",
            label: "待審核",
          },
          {
            value: "1",
            label: "已匹配",
          },
          {
            value: "2",
            label: "不匹配",
          },
          {
            value: "3",
            label: "認作者",
          },
        ],
        score: [{
          value: '',
          label: '全部評分'

        },
        {
          value: -1,
          label: '10分以下'
        },
        {
          value: 0,
          label: '10-20分'
        },
        {
          value: 1,
          label: '20分以上'
        },
        ],
        type: [
          {
            value: "0",
            label: "單場次",
          },
          {
            value: "1",
            label: "蒐證",
          },
        ],
        uploadType: ["PG", "FW", "MS", "CJ"],
      },
      dataListSelections: [],
      //导入报告列表
      fileList: [],
      emptyText:'暫無數據',
      emptyText1:'暫無數據',
    };
  },
  components: {
    // inputSelect,
    // viewContact,
    // selectIp,
    selectWork,
    // selectTerritory
  },
  watch: {
    showtitle(newName, oldName) {
      if (!newName) {
        this.searchFn(this.currentPage)
      }
      else {
        if (this.$refs['addformDatatext']) {

          this.$refs["addformDatatext"].resetFields();
        }
      }
    },
    exportVisible2(newname, oldname) {
      if (!newname) {
        this.templateSelection = "";
      }
    }
  },

  filters: {
    Status: function (status) {
      let config = {
        0: "待審核",
        1: "已匹配",
        2: "不匹配",
        3: "認作者",
      };
      return config[status];
    },
  },
  activated() {
    this.$nextTick(() => {
      if (this.$route.query.update) {
        let query = this.$route.query;
        delete query.update;
        this.searchFn(this.currentPage);
      }
    });
  },

  methods: {
    changeDuration(data){
      let isNum = /^[0-9]*$/;
      let val = this.addformData[data]
      if (isNum.test(val)) {
        if(data=='durationM' && (val < 0 || val > 1000)){
            this.$toast({tips: '不能大於1000min或者小於0min！'});
            this.$set(this.addformData, data, '')
        }else if(data=='durationS' && (val < 0 || val > 59)){
            this.$toast({tips: '不能大於59s或者小於0s！'});
            this.$set(this.addformData, data, '')
        }
      } else {
          this.$toast({tips: 'Duration为纯数字'});
          this.$set(this.addformData, data, '')
      }
    },
    edittitleshow() {
      // console.log(';;77777777777777', this.edittitleform)
      // function copyFn(obj) {
      //   if (obj == null) { return null }
      //   var result = Array.isArray(obj) ? [] : {};
      //   for (let key in obj) {
      //     if (obj.hasOwnProperty(key)) {
      //       if (typeof obj[key] === 'object') {
      //         result[key] = copyFn(obj[key]); // 如果是对象，再次调用该方法自身 
      //       } else {
      //         result[key] = obj[key];
      //       }
      //     }
      //   }
      //   return result;
      // }
      function deepClone(obj) {
        let objClone = Array.isArray(obj) ? [] : {};
        if (obj && typeof obj === 'object') {
          for (let key in obj) {
            if (obj[key] && typeof obj[key] === 'object') {
              objClone[key] = deepClone(obj[key]);
            } else if(key === "isPrintTail"){
              objClone[key] = obj[key] + ''
            } else {
              objClone[key] = obj[key]
            }
          }
        }
        return objClone;
      }

      this.updateinfo = deepClone(this.edittitleform)
      this.updateinfo.listFileStartTime = deepClone(this.edittitleform).listFileStartTime
      console.warn('======', this.updateinfo)

      this.showtitle = true


    },
    closetitledialog() {
      this.showtitle = false
      // this.searchFn(this.currentPage)
    },
    deleteitem(params) {
      this.$alert('確定要刪除嗎？', '刪除', {
        confirmButtonText: '确定',
        showCancelButton: true,
        cancelButtonText: '取消',
        callback: action => {
          if (action == 'confirm') {
            // this.$http.delete('/list/deleteListFileQueue/' + row.id).then(res => {
            //   console.log(res)
            //   if (res.success && res.data.code == 200) {
            //     this.$toast({ tips: '刪除成功' })
            //     this.querySearch()
            //   } else {
            //     this.$toast({ tips: '刪除失敗' })
            //   }
            // })
            this.$http.post('listmatch/deleteSingle?id=' + params.id).then((res) => {
              if (res.data.code == 200) {
                let Message = res.data.message ? res.data.message : '刪除成功!'
                console.warn('resrs', res.data, Message)
                this.$toast({ tips: Message })
              }
              if ((this.total - 1) % 10 > 0) {

                this.searchFn(this.currentPage)
              } else {
                let temppage = this.currentPage - 1 > 0 ? (this.currentPage - 1) : 1
                this.searchFn(temppage)
              }

            })
          }
        }
      });
      // this.$http.post('listmatch/deleteSingle?id='+params.id).then((res)=>{
      //   if (res.data.code==200) {
      //     let  Message=res.data.message?res.data.message:'刪除成功!'
      //     console.warn('resrs',res.data,Message)
      //     this.$toast({tips:Message})
      //   }
      //   this.searchFn(this.currentPage)
      // })

    },
    checkfid() {
      let ajaxData = {};
      ajaxData.page = {
        pageNum: this.currentPage,
        pageSize: 10,
      };
      if (this.currentPage == 1) {
        this.total = 0;
      }
      ajaxData.listMatchDataBasic = this.$utils.copy(this.search);
      console.log("serachparamas&&", ajaxData, "****", this.search);
      this.$http.post("/listmatch/getSingleListMatchList", ajaxData).then((res) => {
        // console.info('=======', res)   
        if (res.data.code == 200) {
          this.adddialogshow = res.data.data === null || res.data.data.list.length == 0 ? false : true;

          this.addformData = {}
          // console.warn('this.addformdata',this.addformData)  
          this.isadd = true

          if (res.data.data === null) {
            this.$toast({ tips: '請輸入正確的FID!' })
            this.search.fileBaseId = ''
            return
          }
        } else {
          console.log('0000000000', res.data.message);
          let mes = res.data.message == '' ? "請輸入正確的FID!" : res.data.message
          this.$toast({ tips: mes })
        }
      })
        .catch((res) => { });
    },
    checkWork(info) {
      console.log("======", info);
      this.$set(this.addformData, "matchWorkTitle", info.title ? info.title : info.title_en);
      this.$set(this.addformData, "matchWorkId", info.work_id);
      this.$set(this.addformData, "matchWorkSocietyCode", info.work_society_code);
      this.$set(this.addformData, "matchWorkTitleId", info.id);
      this.$set(this.addformData, "matchWorkType", info.work_title_type);
      this.$set(this.addformData, "matchWorkUniqueKey", info.work_unique_key);
      this.addformData.matchWorkTitle = info.title ? info.title : info.title_en
      this.addformData.matchworkid = info.work_id
      this.addformData.matchWorkSocietyCode = info.work_society_code
      this.addformData.matchWorkTitleId = info.id
      this.addformData.matchWorkType = info.work_title_type
      this.addformData.matchWorkUniqueKey = info.work_unique_key
      // this.$nextTick(() => {
      this.$refs.addformData.validate((validate) => {
        if (validate) { }
      })
      // })
    },
    getWork(index, row) {
      // this.editIndex = index;    matchWorkId matchWorkSocietyCode
      this.workTableVisible = true;
      // this.workSearch = {
      //     workId: this.formContract.worksnum,
      //     soc: this.formContract.inputSoc,
      //     title: this.formContract.title
      // }
      this.$nextTick(() => {
        this.$refs.selectWorkCom.init();
      })
    },

    edittitlesave() {
      this.$refs.addformDatatext.validate((validate) => {
        if (validate) {
          if (this.updateinfo.listFileStartTime == '') {
            this.updateinfo.listFileEndTime = ''
          }


          this.$http.post("/listBasicFileBase/updateConcertTitle", this.updateinfo).then((res) => {
            // console.log("saveaddmessage===========", res, '))))0000000', this.edittitleform);
            if (res.data.code == 200) {
              // this.adddialogshow = false;
              // let message = this.isadd ? '新增成功!' : "保存成功！"
              this.$toast({ tips: res.data.message });
              this.searchFn(this.currentPage);
              this.showtitle = false
            } else {
              this.$toast({ tips: res.data.message });
            }
          });

        }
      })
    },
    saveadd() {
      this.$refs.addformData.validate((validate) => {
        if (validate) {
          if(this.addformData.durationM && (this.addformData.durationM < 0 || this.addformData.durationM > 1000)){
            this.$toast({tips: 'duration M 不能大於1000min或者小於0min！'});
            return
          }else if(this.addformData.durationS && (this.addformData.durationS < 0 || this.addformData.durationS > 59)){
            this.$toast({tips: 'duration S 不能大於59s或者小於0s！'});
            return
          }
          if (this.addformData.combinedStartDate) {
            this.addformData.combinedStartDate += " 00:00:00";
          }
          if (this.addformData.combinedEndDate) {
            this.addformData.combinedEndDate += " 23:59:59";
          }
          this.addformData.fileBaseId = this.search.fileBaseId;
          // console.log(')))))))))', this.addformData);
          let params = {}
          if (!this.isadd) {
            params.authors = this.addformData.authors
            params.clickNumber = this.addformData.clickNumber ? this.addformData.clickNumber : 1
            params.composers = this.addformData.composers
            params.durationS = this.addformData.durationS
            params.durationM = this.addformData.durationM
            params.extJson = this.addformData.extJson
            params.title = this.addformData.title
            params.id = this.addformData.id
          }
          if (this.isadd) {
            params = this.addformData
            params.clickNumber = this.addformData.clickNumber ? this.addformData.clickNumber : 1
          }
          this.$http.post("/listmatch/saveOrUpateSingle", params).then((res) => {
            console.log("saveaddmessage", res);
            if (res.data.code == 200) {
              this.adddialogshow = false;
              let message = this.isadd ? '新增成功!' : "保存成功！"
              this.$toast({ tips: message });
              this.searchFn(this.currentPage);
            } else {

              this.$toast({ tips: res.data.message });
            }
          }).catch(err => {
            console.log(err);
          })
        }
      });
    },
    addFn() {
    },
    //  changeRsd (val) {
    //         this.search.isDist=val==true?'Y':'N'
    //       },
    clearExport() { },
    auditdetail(row) {
      this.addformData = {}
      // let fileType = "S";
      // this.$refs['uploadform'].validate()
      this.$refs.addformData && this.$refs.addformData.clearValidate()
      this.adddialogshow = true
      this.isadd = false
      this.addformData = this.$utils.copy(row)
      //  function Clone(obj){
      //     if (!obj || (typeof obj !== 'object')) {
      //       return;
      //     }
      //     var newObj = Object.prototype.toString.call(obj) == '[object Array]' ? [] : {};
      //     for (let key in obj) {
      //       if (typeof obj[key] == 'object') {
      //         newObj[key] = Clone(obj[key])
      //       } else {
      //         newObj[key] = obj[key];
      //       }
      //     }
      //     return newObj;
      //   }
      //   let rowuse=Clone(row)
      console.log("mingxi@@@", row)
      // this.addformData.matchWorkTitle = rowuse.matchWorkTitle
      // this.addformData.authors = rowuse.authors
      // this.addformData.performTime = rowuse.performTime
      this.addformData.extJson = this.$utils.copy(row).extJson == "{}" ? '' : this.$utils.copy(row).extJson
      // this.addformData.durationS = rowuse.durationS
      // this.addformData.durationM = rowuse.durationM
      // this.addformData.clickNumber = rowuse.clickNumber
      // this.addformData.id = rowuse.id
      // this.addformData.matchWorkTitle=row.matchWorkTitle
      //  author composer performTime durationM  durationS clickNumber matchwork
      // let {editdata.matchWorkTitle,editdata.author,editdata.performTime,editdata.durationM,editdata.durationS}=row
      // this.$router.push({
      //   name: "fileDetail",
      //   query: {
      //     id: row.fileBaseId,
      //     nameId: row.fileBaseId,
      //     title: row.fileBaseId,
      //     type: fileType,
      //     socName: row.title,
      //     company: row.sourceCompany,
      //   },
      // });
      // if(this.fileType == 'S'){
      // }else{
      //     this.$router.push({name: 'fileDetail',query:{id:row.id,nameId:row.id,title:row.id,type:this.fileType}})
      // }
    },

    switchDist(item, params) {
      console.warn(item, '+++++', params)
      this.$router.push({ name: 'singListaudit', query: { row: item, id: item.id, listType: item.uploadType, useparams: params, usetabledata: this.tableData, thissearchparam: this.searchparam,title:" ",nameId: item.fileMappingId+item.id} })
    },
    onSubmit(onefid) {
      console.warn('tttttttt', this.templateSelection)
      if (onefid != 0 && this.templateSelection === "") {
        this.$toast({ tips: "請先選擇一條數據" });
        return;
      } else {
        let tempobj = onefid == 0 ? this.tableDatacheck[0] : this.tableDatacheck[this.templateSelection];
        this.search.fileBaseId = tempobj.id;
        this.search.categoryCode = tempobj.categoryCode;
        //  this.search.title=tempobj.title
        //  this.search.performTime=tempobj.listFileStartTime.split(' ')[0]
        this.worknum = tempobj.numberOfLines;
        // this.titlename = tempobj.title;
        // this.titledata = tempobj.listFileStartTime ? tempobj.listFileStartTime.split(' ')[0] : ''
        this.exportVisible2 = false;
        this.templateSelection = "";
        //  console.log("{",tempobj,this.search)
        this.searchFn();
      }
    },
    cancelselect() {
      this.exportVisible2 = false;
      this.templateSelection = "";
    },
    clearSearch() {
      this.search = {
        id: "",
        fileBaseId: "",
        matchScore: "",
        categoryCode: "",
        uploadTime: "",
        sourceCode: "",
        status: "",
        isDist: "",
        uploadType: "",
      };
      this.currentPagechange = 1
      this.titlename = "";
      this.titilehost = "";
      this.worknum = "";
      this.total = 0
      this.tableData = [];
      // this.searchFn(1)
    },
    singleElection(row) {
      this.templateSelection = this.tableDatacheck.indexOf(row);
      // console.log('-------row',row);
      // console.log("^^^", this.templateSelection, row);
    },
    init() {
      this.searchFn(1);
    },
    handleSelect(item) {
      console.log(item);
    },
    searchFn(page = 1) {
      // console.log("^^^^^", this.search.fileBaseId);
      this.emptyText = '數據加載中';
      this.emptyText1 = '數據加載中';
      this.titilehost = ''
      this.tableData = []
      if (!this.search.fileBaseId) {
        let ajaxData = {};
        ajaxData.page = {
          pageNum: page,
          pageSize: 10,
        };
        this.currentPage = page;
        if (page == 1) {
          this.total = 0;
        }
        // console.warn("this.search", this.$utils.copy(this.search));
        ajaxData.categoryCode = this.$utils.copy(this.search).categoryCode;
        ajaxData.perDate = this.$utils.copy(this.search).perDate;
        ajaxData.type = this.$utils.copy(this.search).type;

        // console.log("serachparamas&&", ajaxData, "****", this.search);
        this.searchparam = ajaxData
        // this.searchparam.fileBaseId = ajaxData.listMatchDataBasic.fileBaseId
        this.$http.post("/listBasicFileBase/getSingleListBasicFileBaseList", ajaxData).then((res) => {
          if (res.data.code == 200) {
            this.tableDatacheck = res.data.data.list;
            
            // this.currentPage=
            if (this.tableDatacheck.length == 1) {
              this.onSubmit(0);
            } else if (this.tableDatacheck.length == 0) {
              this.exportVisible2 = false
            } else {
              // console.warn("((((((", res, "&&&", this.tableDatacheck);
              this.totalcheck = res.data.data.total;
              this.pageshow = false
              this.exportVisible2 = true;
              this.currentPagechange = 1;
              this.$nextTick(() => {
                  this.pageshow = true
              })
            }
          }
          if(! this.tableDatacheck || this.tableDatacheck.length == 0){
            this.emptyText1 = '暫無數據';
          }
        })
          .catch((res) => { });
      } else {
        this.$refs.form.validate((validate) => {
          if (validate) {
            let ajaxData = {};
            ajaxData.page = {
              pageNum: page,
              pageSize: 10,
            };
            this.currentPage = page;
            if (page == 1) {
              this.total = 0;
            }
            ajaxData.listMatchDataBasic = this.$utils.copy(this.search);
            if (ajaxData.listMatchDataBasic.fileBaseId) {
              // ajaxData.fileBaseId = ajaxData.listMatchDataBasic.fileBaseId
              delete ajaxData.listMatchDataBasic.type;
            }
            // console.log("serachparamas&&", ajaxData, "****", this.search);
            this.searchparam = ajaxData
            this.$http.post("/listmatch/getSingleListMatchList", ajaxData).then((res) => {
              if (res.data.data == null) {
                // console.log('0000000000', res.data.message);
                let mes = res.data.message == '' ? "請輸入正確的FID!" : res.data.message
                this.$toast({ tips: mes })
                // this.$toast({tips:'請輸入正確的fid'})
                // return
              }
              if (res.data.code == 200) {
                // console.warn('res===edittitileform====', res)
                // this.currentPage=res.data.data.list.pageNum
                // if (res.data.data.list.pageNum>res.data.data.list.pages) {
                //   this.searchFn(res.data.data.list.pages)
                //   return
                // }
                this.tableData = res.data && res.data.data.list.list;
                this.total = res.data && res.data.data.list.total
                // console.warn("((((((", this.tableData);
                // 双击弹窗需要修改的字段有 title（活动名称）、listFileStartTime（活动日期）、sourceCompany（主办方）、workName（授权曲目）、session（场次）、numberOfLines（歌曲数量）、clickNumber （播放次数），concertTitle（标题模板）； 
                // this.titilehost = res.data.data.base.concertTitle.replace(/null/g,"  ")
                this.titilehost = res.data && res.data.data.base.concertTitle.replace(/null/g, "0")
                // Object.assign(this.edittitleform, res.data.data.base)
                this.edittitleform = res.data && res.data.data.base
                this.edittitleform.listFileStartTime = res.data && res.data.data.base.listFileStartTime.split(' ')[0]
                // console.warn('edittiform', this.edittitleform)
                this.ischecked = res.data.data.base.dist == "Y" ? true : false;
              }
            })
              .catch((res) => { });
          }
        });
      }
      if(! this.tableData || this.tableData.length == 0){
        this.emptyText = '暫無數據';
      }
    },
    handleCurrentChange(val) {
      console.log('-------', val)
      this.currentPage = val
      this.searchFn(val);
      //  this.querySearch(val)
    },
    handleCurrentChangecheck(val) {
      // this.searchFn(val);
      this.templateSelection = ''
      this.currentPagechange = val
      let ajaxData = {};
      ajaxData.page = {
        pageNum: val,
        pageSize: 10,
      };
      ajaxData.listMatchDataBasic = this.$utils.copy(this.search);
      if (ajaxData.listMatchDataBasic.matchScore == 99) {
        delete ajaxData.listMatchDataBasic.matchScore;
      }
      console.log("serachparamas&&", ajaxData, "****", this.search);
      this.emptyText1 = '數據加載中';
      this.$http
        .post("/listBasicFileBase/getSingleListBasicFileBaseList", ajaxData)
        .then((res) => {
          if (res.data.code == 200) {
            this.tableDatacheck = res.data.data.list;
            // console.warn("((((((", res, "&&&", this.tableDatacheck);
            this.totalcheck = res.data.data.total;
            // this.exportVisible2 = true;
          }
          if(! this.tableDatacheck || this.tableDatacheck.length == 0){
            this.emptyText1 = '暫無數據';
          }
        })
        .catch((res) => { });
    },
    audit(item) {
      this.$router.push({
        name: "listAudit",
        query: { id: item.id, listType: item.uploadType },
      });
    },
    beforeupload() {
      if (!this.search.fileBaseId) {
        this.$toast({ tips: '請先輸入FID' })
        return
      }
      if (this.tableData.length == 0) {
        this.$toast({ tips: '請先搜索數據!' })
        return
      }
      // this.checkfid()

    },
    uploadChange(file) {
      if (!this.search.fileBaseId) {
        this.$toast({ tips: '請先輸入FID' })
        return
      }
      this.$msgbox
        .confirm(`確定進行[導出]操作?`, "提示", {
          confirmButtonText: "確定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(() => {
          this.$utils.uploadFile({flie:file.raw}, "/listmatch/importAuditResults", this);
        })
    },
    exportFn() {
      if (!this.search.fileBaseId) {
        this.$toast({ tips: '請先輸入FID' })
        return
      }
      if (this.tableData.length == 0) {
        this.$toast({ tips: '請先搜索數據!' })
        return
      }
      this.$msgbox
        .confirm(`確定進行[導出]操作?`, "提示", {
          confirmButtonText: "確定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(() => {
          this.$refs.form.validate((validate) => {
            if (validate) {
              let ajaxData = {};
              ajaxData.listMatchDataBasic = this.$utils.copy(this.search);
              if (ajaxData.listMatchDataBasic.matchScore == 99) {
                delete ajaxData.listMatchDataBasic.matchScore;
              }
              this.$http
                .post("/listmatch/export", ajaxData, { responseType: "blob" })
                .then((res) => {
                  console.warn(
                    "[resresres",
                    res.headers["content-disposition"]
                  );
                  let data = res.data;
                  this.$utils.downloadByBlob(
                    data,
                    res.headers["content-disposition"]
                  );
                });
            }
          });
        });
    },
    changeSearchScore(score) {
      this.search.matchScore = score;
    },
    selectionChangeHandle(val) {
      this.dataListSelections = val;
    },
    auditBatch(status) {
      let url = "/listmatch/checkListMatchDataBasicAll";

      let uniqueKeyMd5List = [];
      this.dataListSelections.forEach((item) => {
        uniqueKeyMd5List.push(item.uniqueKeyMd5);
      });
      if (uniqueKeyMd5List.length == 0) {
        this.$toast({ tips: "請至少選擇一條" });
        return;
      }
      let ajaxData = {
        status,
        uniqueKeyMd5List,
      };
      this.$http.post(url, ajaxData).then((res) => {
        console.log('resrespiliang-------', res.data);
        if (res.data.code && res.data.code != 200) {
          this.$toast({ tips: res.data.message });
        } else {
          this.$toast({ tips: res.data.data });
          this.searchFn();
        }
      });
    },
    importFn() {
      // todo 後端功能待開發 01-14
      console.log();
    },
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {

      if ((vm.search.categoryCode != '' || vm.search.perDate != '' || vm.search.fileBaseId != '') && vm.tableData.length > 0) {

        vm.searchFn(vm.currentPage)
      }

    });
  },
};
</script>

<style scoped>
/* .el-input {
  width: 104%;
} */
.titleupdata
  > .el-form-item--medium
  > .el-form-item__content
  > .el-input
  /deep/
  input {
  overflow: hidden;

  text-overflow: ellipsis;

  white-space: nowrap;
}
.titleupdata > .el-form-item--medium /deep/ .el-form-item__content {
  width: 70%;
}
.titleupdata .el-date-editor {
  width: 560px !important;
}
/* white-space: pre-wrap; */
.singerH /deep/ .el-form-item__label:first-line {
  text-align: right;
  font-size: 16px;
  line-height: 16px;
}
/* el-form-item__label */
.singerH /deep/ .el-form-item__label {
  white-space: pre-wrap;
  text-align: right;
  /* color: red !important; */
  width: 20% !important;
  font-size: 10px;
  /* background: red; */
  /* height: 47px; */
  line-height: 16px;
}
.durationcss /deep/ .el-form-item__error {
  width: 100px;
}
.editInput /deep/.el-table .cell{
    white-space:pre-line;
}
</style>


