<template>
  <div>
    <el-form :inline="true" :model="search" class="demo-form-inline" @keyup.enter.native="searchFn()">
      <el-form-item label="分配編號">
        <el-input v-model.trim="search.distNo" placeholder=""></el-input>
      </el-form-item>
      <el-form-item label="分配類別">
        <el-select v-model="search.distType" placeholder="Type">
          <el-option label="所有" value=""></el-option>
          <el-option label="P" value="P"></el-option>
          <el-option label="M" value="M"></el-option>
          <el-option label="I" value="I"></el-option>
          <el-option label="O" value="O"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="searchFn()" v-if="isAuth('distribute:list:find')">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="allot()" v-if="isAuth('distribute:list:add')">創建分配</el-button>
      </el-form-item>
    </el-form>
    <el-table :empty-text="tableresult" :data="tableData" border stripe style="width: 100%" v-loading="loading">
      <el-table-column prop="distNo" label="分配編號" width="180px">
      </el-table-column>
      <el-table-column label="分配清單時間">
        <template slot-scope="scope">{{scope.row.distListStartTime.split(' ')[0] + '~' + scope.row.distListEndTime.split(' ')[0]}}</template>
      </el-table-column>
      <el-table-column prop="createTime" label="創建時間">
      </el-table-column>
      <el-table-column prop="updateUserName" label="操作用户">
      </el-table-column>
      <el-table-column prop="num" label="分配进度">
        <!-- <template slot-scope="scope" v-if="scope.row.distStatus==12||scope.row.distStatus==42||scope.row.distStatus==32" style="color:red">
          <span @click="showremark(scope.row.remark)" style="color:blue">
            {{scope.row.distStatus | Status}}
          </span>
        </template> -->
        <template slot-scope="scope">

          <span style="color:#409EFF" v-if="scope.row.distStatus==12||scope.row.distStatus==42||scope.row.distStatus==32" @click="faildetail(scope.row.remark)">
            {{scope.row.distStatus | Status}}
            <!-- <el-popover placement="top-start" title="失敗原因" width="200" trigger="click" :content="scope.row.remark">
            <span slot="reference" style="color:#409EFF">{{scope.row.distStatus | Status}}</span>
          </el-popover> -->
          </span>
          <span v-else>
            {{scope.row.distStatus | Status}}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="lastRunStart" label="最近一次执行开始时间">
      </el-table-column>
      <el-table-column prop="lastRunEnd" label="最近一次执行结束时间">
      </el-table-column>
      <!-- <el-table-column prop="remark" label="備註">
        <template slot-scope="scope" v-if="scope.row.distStatus==12||scope.row.distStatus==42||scope.row.distStatus==32">
          <span class="breakword" :title="scope.row.remark">{{scope.row.remark}}</span>
        </template>
      </el-table-column> -->
      <el-table-column label="operation" width="260px">
        <template slot-scope="scope"> 
          <el-button type="text" size="small" @click="look(scope.row)" v-if="isAuth('distribute:list:look')">查看</el-button>
          <el-button type="text" size="small" @click="checkAgr(scope.row)" v-if="['P','M','I'].includes(scope.row.distNo.substr(0,1)) && checkAgrStatus.includes(scope.row.distStatus)">合約檢查</el-button>
          <el-button type="text" size="small" @click="distribution(scope.row)" v-if="scope.row.distStatus == 0">分配計算</el-button>
          <el-button type="text" size="small" @click="reDis(scope.row)" v-if="[5,12,42,32,82].includes(scope.row.distStatus)">重新計算</el-button>
          <el-button type="text" size="small" @click="downloadlist(scope.row)" v-if="scope.row.distStatus == 5">Cross Check</el-button>
          <el-button type="text" size="small" @click="check(scope.row)" >Distribution Check</el-button>
          <el-button type="text" size="small" @click="submitlock(scope.row, scope.$index)" v-if="scope.row.distStatus == 5">鎖定</el-button>
          <el-button type="text" size="small" @click="downloadlist(scope.row)" v-if="scope.row.distStatus == 7">下載報表</el-button>
          <!-- <el-button type="text" size="small" @click="corssCheck(scope.row)" v-if="scope.row.status == 6">Corss Check</el-button> -->
        </template>
      </el-table-column>
    </el-table>
    <el-pagination background layout="prev, pager, next" :total="total" @current-change="searchFn" :current-page="currentPage">
    </el-pagination>
  </div>
</template>

<script>
export default {
  name: 'list',
  data() {
    return {
      search: { num: '', agrType: 'P', distType: '' },
      tableData: [],
      total: 1,
      tableresult: ' ',
      currentPage: 1,
      loading: false,
      distNo: '',
      checkAgrStatus:[0,5,12,42,32,82]

    }
  },
  filters: {
    Status: function (status) {
      let config = {
        '0': '分配參數配置',
        '1': '待分配計算',
        '7': '分配完成',
        '11': '分配計算中',
        '12': '分配計算失败',
        '3': 'retain報表生成',
        '31': 'retain報表生成中',
        '32': 'retain報表生成失败',
        '4': '其他報表生成',
        '41': '其他生成中',
        '42': '其他生成失败',
        '5': '分配計算完成',
        // '6': '分配完成' 
        '61': '支付數據生成中',
        '6': '待生成支付數據',
        '8': '合約檢查',
        '81': '合約檢查中',
        '82': '合約檢查失敗'
      }
      return config[status]
    }
  },
  methods: {
    showCheckAgr(data){
        let start = data.distNo.substr(0,1)
        if(['P','M','I'].includes(start) && checkAgrStatus.includes(data.distStatus)){
            return true
        }
        return false
    },
    faildetail(value) {
      // this.$alert(`<strong>${value}</strong>`, '查看錯誤原因', {
      //   dangerouslyUseHTMLString: true
      // });
      this.$alert(value, '查看錯誤原因', {
        confirmButtonText: '确定',
      });
    },
    searchFn(page) {
      let ajaxData = this.$utils.copy(this.search);
      ajaxData.page_num = page ? page : 1;
      ajaxData.page_size = 10;
      this.loading = true;
      this.tableresult = '數據加載中...'
      this.$http.get('/dist/param/info', { params: ajaxData }).then(res => {
        this.loading = false;
        if (res.success) {
          this.tableData = res.data.list;
          this.total = res.data.total;
          this.currentPage = ajaxData.page_num;
          this.tableresult = this.tableData.length == 0 ? '暫無數據' : ' '
        }
      })
    },
    allot() {
      this.$router.push({ name: 'distribute-createdistribution' });
    },
    look(row) {
      this.$router.push({ name: 'distribute-info', query: { distNo: row.distNo, status: row.distStatus, nameId: row.distNo, title: row.distNo } })
    },
    downloadlist(row) {
      this.$http.get('dist/param/info/download?distNo=' + row.distNo).then(res => {
        let Message = res.data.data || ''
        this.$alert(Message, '下載報表路徑', {
          confirmButtonText: '确定',
          callback: action => {
            // this.$message({
            //   type: 'info',
            //   message: `action: ${action}`
            // });
          }
        });
        // let tempBlob = new Blob([res.data], { type: 'application/json' })
        // let reader = new FileReader()
        // reader.onload = e => {
        //   console.log('eeeeeeeeeee', e, res);
        //   let res1 = e.target.result
        //   try {
        //     let json = JSON.parse(res1).message || '下載失敗!'
        //     this.$toast({ tips: json })
        //   } catch (err) {

        //     this.$utils.downloadByBlob(res.data, res.headers["content-disposition"]);

        //   }
        // }
        // reader.readAsText(tempBlob)
      })



    },
    submitlock(row, index) {
      let ajaxData = {
        id: row.id,
        distStatus: 6
      }

      this.$http.post('/dist/changeDistStatus', ajaxData).then((res) => {
        if (res.data.code && res.data.code != 200) {
          this.$toast({ tips: res.data.message })
        } else {
          this.$toast({ tips: '操作成功' });
          this.tableData[index].distStatus = 6;
        }
      })
    },
    handleCurrentChange() { },
    check(row) {
      this.$router.push({ name: 'distribute-check',query:{ distNo: row.distNo } })
    },
    reDis(row) {
      this.distribution(row);
    },
    distribution(row) {
      this.$msgbox.confirm('是否確定分配計算？', '', {

      }).then(() => {
        let ajaxData = {
          id: row.id,
          distStatus: 1
        }
        this.$http.post('/dist/changeDistStatus', ajaxData).then((res) => {
          if (res.data.code && res.data.code != 200) {
            this.$toast({ tips: res.data.message })
          } else {
            this.$toast({ tips: '操作成功' });
            this.searchFn(1)
          }
        })
      })



    },

    checkAgr(row) {
      this.$msgbox.confirm('是否確定檢查合約？', '', {

      }).then(() => {
        let ajaxData = {
          id: row.id,
          distStatus: 8
        }
        this.$http.post('/dist/changeDistStatus', ajaxData).then((res) => {
          if (res.data.code && res.data.code != 200) {
            this.$toast({ tips: res.data.message })
          } else {
            this.$toast({ tips: '操作成功' });
            this.searchFn(1)
          }
        })
      })
    },
   
    corssCheck() {

    }
  },
  mounted() {
    this.searchFn();
  }
}
</script>

<style scoped>
</style>
