<template>
    <div>
        <el-form :inline="true" ref="form" :model="searchForm" :rules="rules" class="demo-form-inline">

            <el-form-item label="資料夾路徑" prop="filePath">
                <el-input v-model="searchForm.filePath" placeholder=""></el-input>
            </el-form-item>
            <el-form-item>
                <el-button @click="classify" type="primary">處理</el-button>
            </el-form-item>
        </el-form>
        <div>

        </div>
    </div>
</template>

<script>
    export default {
        name: 'classification-process',
        data(){
            return {
                searchForm:{
                    filePath:''
                },
                rules:{
                    filePath:[{required:true,message:'請填寫目錄地址'}]
                }
            }
        },
        methods: {
            classify(){
                this.$refs.form.validate(validate => {
                    if(validate){
                        let formData = new FormData()
                        formData.append('filePath',this.searchForm.filePath)
                        this.$http.post('/list2BasicFileTemplate/checkAndClassification',formData).then(res => {
                            this.$toast({tips:res.data.message})
                        })
                    }
                })
            },
        }
    }
</script>

<style scoped>

</style>
