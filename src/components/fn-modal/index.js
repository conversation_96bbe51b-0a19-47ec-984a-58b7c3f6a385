import Vue from 'vue';

function popupModal({data = {}, content, ...modalProp}) {
  // console.log('fn-modal', modalProp);
  const popupEl = document.getElementById('popup');
  const thisRenderNode = document.createElement('div');
  const thisDialogNode = document.createElement('div');
  const dialogId = `dialog-id-${(new Date()).getTime()}${String(Math.random()).replace('.', '')}`;
  thisRenderNode.classList.add('fn-dialog-node');
  thisRenderNode.setAttribute('id', dialogId);
  popupEl.appendChild(thisRenderNode)
  thisRenderNode.appendChild(thisDialogNode);

  let offset = {
    x: 0,
    y: 0
  }

  const handleDragEvent = (element) => {
    const target = element.getElementsByClassName('el-dialog')[0] || thisRenderNode;
    // console.warn('handleDragEvent:target', target);
    const dragBar = target.getElementsByClassName('el-dialog__header')[0];
    // console.warn('handleDragEvent:dragBar', dragBar);
    let isDragging = false
    let distance = {}
    const handleMove = (event) => {
      if (isDragging) {
        const centerdBottomLimitCoe = 0
        const offsetLeftLimit = (document.body.offsetWidth - target.offsetWidth) / 2 * -1
        const offsetRightLimit = (document.body.offsetWidth - target.offsetWidth) / 2
        const offsetTopLimit = 0 - centerdBottomLimitCoe
        const offsetBottomLimit = (document.body.offsetHeight - target.offsetHeight) // 现在允许塞屏幕下面 // 不允许塞到屏幕外 document.body.offsetHeight - target.offsetHeight - centerdBottomLimitCoe
        let targetLeft = event.pageX - distance.x
        let targetTop = event.pageY - distance.y - centerdBottomLimitCoe
        if (targetLeft > offsetRightLimit) {
          targetLeft = offsetRightLimit
        }
        if (targetLeft < offsetLeftLimit) {
          targetLeft = offsetLeftLimit
        }
        if (targetTop > offsetBottomLimit) {
          targetTop = offsetBottomLimit
        }
        if (targetTop < offsetTopLimit) {
          targetTop = offsetTopLimit
        }
        target.style.marginTop = '';
        target.style.left = targetLeft + 'px';
        target.style.top = targetTop + 'px';
      }
    }

    dragBar.addEventListener('mousedown', (event) => {
      const { target } = event;
      const { classList } = target;
      if (classList.contains('el-icon-close')) {
        return;
      }
      isDragging = true
      distance = {
        x: event.pageX - offset.x,
        y: event.pageY - offset.y
      }
      document.addEventListener('mousemove', handleMove)
    })

    document.addEventListener('mouseup', () => {
      if (isDragging) {
        isDragging = false
        document.removeEventListener('mousemove', handleMove)
        offset = {
          x: Number(target.style.left.replace('px', '')),
          y: target.offsetTop
        }
      }
    })
  }

  const bindDragEvent = (elem) => {
    if (elem && modalProp.draggable !== false) {
      handleDragEvent(elem)
    }
  }

  const setDialogCenterPosition = () => {
    const dialogEl = thisRenderNode.getElementsByClassName('el-dialog')[0];
    const dialogWidth = dialogEl.clientWidth;
    const dialogHeight = dialogEl.clientHeight;
    const leftOffset = (document.body.offsetWidth - dialogWidth) / 2;
    const topOffset = (document.body.offsetHeight - dialogHeight - 30) / 2;
    // const leftPosition = leftOffset > 0 ? leftOffset : 0;
    const leftPosition = leftOffset > 0 ? 0 : 0;
    const topPosition = topOffset > 5 ? topOffset : 5;
    offset.x = leftPosition;
    offset.y = topPosition;
    dialogEl.classList.add('reset-top');
    dialogEl.style.left = leftPosition + 'px';
    dialogEl.style.marginLeft = null;
    dialogEl.style.top = topPosition + 'px';
    dialogEl.style.marginTop = null;
    setTimeout(() => {
      dialogEl.classList.remove('reset-top');
    }, 280);
    console.log(`setDialogCenterPosition: dialogId=${dialogId}, dialogWidth=${dialogWidth}, dialogHeight=${dialogHeight}`);
  }

  const setDialogVerticalCenterPosition = () => {
    const dialogEl = thisRenderNode.getElementsByClassName('el-dialog')[0];
    const dialogWidth = dialogEl.clientWidth;
    const dialogHeight = dialogEl.clientHeight;
    const topOffset = (document.body.offsetHeight - dialogHeight - 30) / 2;
    const topPosition = topOffset > 5 ? topOffset : 5;
    offset.y = topPosition;
    dialogEl.classList.add('reset-top');
    dialogEl.style.marginTop = null;
    dialogEl.style.top = topPosition + 'px';
    setTimeout(() => {
      dialogEl.classList.remove('reset-top');
    }, 280);
    console.log(`setDialogVerticalCenterPosition: dialogId=${dialogId}, dialogWidth=${dialogWidth}, dialogHeight=${dialogHeight}`);
  }

  const setDialogHeadUpPosition = () => {
    const dialogEl = thisRenderNode.getElementsByClassName('el-dialog')[0];
    const dialogWidth = dialogEl.clientWidth;
    const dialogHeight = dialogEl.clientHeight;
    const topOffset = (document.body.offsetHeight - dialogHeight - 30) / 2 * 0.6;
    const topPosition = topOffset > 5 ? topOffset : 5;
    offset.y = topPosition;
    dialogEl.classList.add('reset-top');
    dialogEl.style.marginTop = null;
    dialogEl.style.top = topPosition + 'px';
    setTimeout(() => {
      dialogEl.classList.remove('reset-top');
    }, 280);
    console.log(`setDialogHeadUpPosition: dialogId=${dialogId}, dialogWidth=${dialogWidth}, dialogHeight=${dialogHeight}`);
  }

  new Vue({
    data: {
      visible: true,
      // okButtonProps: {
      //   props: {loading: false}
      // },
      ...modalProp
    },
    render(h) {
      const thisComponent = this;
      const modalProps = {
        getContainer: () => thisRenderNode,
        visible: thisComponent.visible,
        closeOnClickModal: false,
        top: '-1999px',
        customClass: ['fn-dialog', modalProp.wrapClassName].join(' '),
        // okButtonProps: this.okButtonProps,
        ...modalProp
      };
      Object.keys(modalProp).forEach((key) => {modalProps[key] = thisComponent[key]});
      const contentData = {
        updateDialog: this.updateModal.bind(this),
        setDialogToCenter: setDialogCenterPosition,
        setDialogToVerticalCenter: setDialogVerticalCenterPosition,
        setDialogToHeadUpCenter: setDialogHeadUpPosition,
        close: this.close,
        ...(content.data || (() => ({})))(), // 考虑 content.data 可能为空的情况
        ...data
      };
      // console.warn('content', content, contentData);
      // console.warn('modalProps', modalProps);
      return (
        h('el-dialog', {
          on: {
            close: thisComponent.close
          },
          props: {
            ...modalProps
          }
        }, [
          h({
            ...content,
            data() {return {...contentData}}
          })
        ])
      )
    },
    watch: {
      visible(nv, ov) {
        if (nv === false) {
          thisRenderNode.remove();
          console.warn('close:destroyed')
        }
      }
    },
    mounted() {
      setTimeout(() => {
        bindDragEvent(thisRenderNode);
        if (modalProp.center === undefined && modalProp.centered === undefined && modalProp.headupCenter === undefined) {
          setDialogHeadUpPosition();
          return;
        }
        if (modalProp.center === true || modalProp.centered === true) {
          setDialogCenterPosition();
          return;
        }
        if (modalProp.headupCenter || modalProp.center === 'headup' || modalProp.centered === 'headup') {
          setDialogHeadUpPosition();
          return;
        }
      }, 100)
    },
    methods: {
      updateModal(data) {
        Object.keys(data).forEach((key) => {
          this[key] = data[key];
        });
      },
      onCancel(cb) {
        this.cancel = cb;
      },
      onOk(cb) {
        this.ok = cb;
      },
      close() {
        // this.cancel && this.cancel();
        this.visible = false;
      },
      confirm() {
        // console.warn('fn-modal:confirm')
        this.ok && this.ok(this.updateModal.bind(this));
      }
    }
  }).$mount(thisDialogNode);
}

export default popupModal;
