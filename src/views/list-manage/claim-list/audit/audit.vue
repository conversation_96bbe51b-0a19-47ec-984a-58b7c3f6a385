<template>
  <div>
    <el-collapse v-model="activeNames" class="clear" style="border-bottom: 0;">
      <el-collapse-item class="step-jump" title="File Work Info" name="1" :disabled="true">
        <div class="boxline p-t-10">
          <el-form :inline="true" label-position="left" label-width="100px">
            <div v-if="listType != 'pg'">
              <el-form-item label="CompCustomId">
                <el-input v-model="fileInfo.compCustomId" readonly></el-input>
              </el-form-item>
              <el-form-item label="WorkTitle">
                <el-input v-model="fileInfo.title" readonly></el-input>
              </el-form-item>
              <el-form-item label="Composer">
                <el-input v-model="fileInfo.composer" readonly></el-input>
              </el-form-item>
              <el-form-item label="Author">
                <el-input v-model="fileInfo.author" readonly></el-input>
              </el-form-item>
              <el-form-item label="ISWC">
                <el-input v-model="fileInfo.iswc" readonly></el-input>
              </el-form-item>
              <el-form-item label="Performer">
                <el-input v-model="fileInfo.workArtist" readonly></el-input>
              </el-form-item>
              <el-form-item label="ISRC">
                <el-input v-model="fileInfo.isrc" readonly></el-input>
              </el-form-item>
              <el-form-item label="ClickNumber">
                <el-input v-model="fileInfo.clickNumber" readonly></el-input>
              </el-form-item>
              <el-form-item label="NetRevenue"> 
                <el-input v-model="fileInfo.workPrice" readonly></el-input>
              </el-form-item>
            </div>
            <div v-else>
              <el-form-item label="SrcWorkNo" label-width="110px">
                <el-input v-model="fileInfo.compCustomId" readonly></el-input>
              </el-form-item>
              <el-form-item label="WorkTitle" label-width="110px">
                <el-input v-model="fileInfo.title" readonly></el-input>
              </el-form-item>
              <el-form-item label="Eposide No" label-width="110px">
                <el-input v-model="fileInfo.episodeNo" readonly></el-input>
              </el-form-item>
              <el-form-item label="TV Name" label-width="110px">
                <el-input v-model="fileInfo.tvName" readonly></el-input>
              </el-form-item>
              <el-form-item class="f12" label="Channel Name" label-width="110px">
                <el-input v-model="fileInfo.channelName" readonly></el-input>
              </el-form-item>
              <el-form-item label="ClickNum">
                <el-input v-model="fileInfo.clickNumber" readonly></el-input>
              </el-form-item>
              <el-form-item label="NetRevenue">
                <el-input v-model="fileInfo.workPrice" readonly></el-input>
              </el-form-item>
            </div>
          </el-form>
        </div>
      </el-collapse-item>
      <el-collapse-item class="step-jump" title="Match Work Info" name="2" :disabled="true" :style="listType == 'pg' ? 'width: 600px' : ''">
        <div class="boxline">
          <el-table 
            :empty-text="emptyText" 
            :data="matchTable" 
            border 
            stripe 
            height="220px" 
            v-loading='loading' 
            @row-click="changeWork" 
            @row-dblclick="workDrawer"
            highlight-current-row 
            ref="matchTableRef" 
            class="match-table" 
            style="max-height: 240px;overflow-y: auto;">
            <el-table-column prop="matchWorkId" label="WorkNo" width="150">
              <template slot-scope="scope">
                <span style="color:red" v-if="scope.row.distributable!=1">*</span>
                {{scope.row.matchWorkId}}
              </template>
            </el-table-column>
            <el-table-column prop="matchWorkSocietyCode" label="WorkSoc">
              <template slot-scope="scope">
                {{scope.row.matchWorkSocietyCode}}
              </template>
            </el-table-column>
            <el-table-column prop="matchTitle" label="WorkTitle">
              <template slot-scope="scope">
                {{scope.row.matchTitle}}
              </template>
            </el-table-column>
            <el-table-column prop="matchScore" label="Score">
              <template slot-scope="scope">
                {{scope.row.matchScore}}
              </template>
            </el-table-column>
          </el-table>
          <el-form class="match-info" :inline="true" label-position="left" label-width="90px">
            <div v-if="listType != 'pg'">
              <el-form-item label="Composer">
                <el-input v-model="matchInfo.matchComposers" style="width: 320px;" readonly></el-input>
              </el-form-item>
              <el-form-item label="Author">
                <el-input v-model="matchInfo.matchAuthors" style="width: 320px;" readonly></el-input>
              </el-form-item>
              <el-form-item label="ISWC">
                <el-input v-model.number="matchInfo.matchIswc" style="width: 320px;" readonly></el-input>
              </el-form-item>
            </div>
            <div v-else>
              <el-form-item label="Eposide No" label-width="110px">
                <el-input v-model="matchInfo.episode_no" style="width: 350px;" readonly></el-input>
              </el-form-item>
            </div>
            <div v-if="listType != 'pg'">
              <div class="f-l" style="width: 49%;padding-left: 20px;box-sizing: border-box;">
                <div>Performer</div>
                <el-form-item>
                  <ul class="list" style="width: 190px;max-height: 300px;overflow-y: auto;">
                    <li class="el-sl" v-for="(item, index) in matchInfo.performerList" :key="index">
                      <span :title="item">{{item}}</span>
                    </li>
                    <li v-if="!matchInfo.performerList || matchInfo.performerList.length == 0">暂無數據</li>
                  </ul>
                </el-form-item>
              </div>
              <div class="f-l" style="width: 51%;">
                <div>ISRC</div>
                <el-form-item>
                  <ul class="list" style="width: 190px;max-height: 300px;overflow-y: auto;">
                    <li class="el-sl" v-for="(item, index) in matchInfo.isrcList" :key="index">
                      <span :title="item">{{item}}</span>
                    </li>
                    <li v-if="!matchInfo.isrcList || matchInfo.isrcList.length == 0">暂無數據</li>
                  </ul>
                </el-form-item>
              </div>
            </div>
            <div class="component" v-else>
              <el-form-item label="Component">
                <el-table :empty-text="emptyText1" stripe :data="matchInfo.componentList" border>
                  <!-- todo-guan 等周兵接口添加字段後，確認字段 -->
                  <el-table-column prop="title" label="Title">
                  </el-table-column>
                  <el-table-column prop="componentWorkId" label="Work No" width="120px">
                  </el-table-column>
                  <el-table-column prop="comWorkSociety" label="WorkSoc" width="90px">
                  </el-table-column>
                  <!-- todo-guan 等周兵接口添加字段後，確認字段 -->
                  <el-table-column prop="genre" label="Genre" width="70px">
                  </el-table-column>
                  <el-table-column prop="usageType" label="Usage" width="70px">
                  </el-table-column>
                </el-table>
              </el-form-item>
            </div>
          </el-form>
        </div>
      </el-collapse-item>
    </el-collapse>
    <div class="t-c p-t-40" style="width: 840px;">
      <el-button type="primary" @click="submitFn(1)">確認</el-button>
      <el-button type="primary" @click="submitFn(2)">拒絕</el-button>
      <el-button type="primary" @click="selectWorkFn()">指定作品</el-button>
    </div>

    <!-- 指定作品 查找作品 弹框 -->
    <!-- 輸入查詢条件，查詢結果在match work info 列表里展示 -->
    <!-- <el-dialog :visible.sync="selectWork.selectWorkShow">
        <div style="width: 600px;margin: auto;margin-bottom: 20px">
            <el-input style="width: 140px" placeholder="Work No" v-model='selectWork.workId'></el-input>
            <el-input style="width: 60px" placeholder="Soc" v-model='selectWork.soc'></el-input>
            <el-input style="width: 240px" placeholder="Title" v-model='selectWork.title'></el-input>
            <el-button slot="append" icon="el-icon-search" @click="querySelectWorkList()"></el-button>
        </div>
    </el-dialog> -->
    <el-dialog :visible.sync="show" width="1000px" title="指定作品" :close-on-click-modal="false">
      <div style="width: 800px;margin: auto;margin-bottom: 20px">
        <el-input   @keyup.enter.native='onSubmit()' v-model="searchInfo.title" placeholder="Title" style="width: 220px;"></el-input>
        <el-input   @keyup.enter.native='onSubmit()' v-model="searchInfo.workId" placeholder="Work No" style="width: 128px;"></el-input>
        <el-input   @keyup.enter.native='onSubmit()' v-model="searchInfo.soc" placeholder="Soc" style="width: 60px;"></el-input>
        <el-button type="primary" @click="onSubmit()">搜索</el-button>
        <el-button @click="cleansearch()">清除搜索</el-button>
      </div>
      <el-table :empty-text="emptyText2" :data="tableData">
        <el-table-column property="title" label="title">
          <template slot-scope="scope">
            {{scope.row.title||scope.row.title_en}}
          </template>
        </el-table-column>
        <el-table-column property="work_id" label="workId" width="120"></el-table-column>
        <el-table-column property="work_society_code" label="workSoc" width="90"></el-table-column>
        <el-table-column property="genre_code" label="Genre" width="80"></el-table-column>
        <el-table-column property="genre_code" label="Composer" width="150">
          <template slot-scope="scope">
            {{scope.row.composer && scope.row.composer.join('、')}}
          </template>
        </el-table-column>
        <el-table-column property="genre_code" label="Author" width="150"><template slot-scope="scope">
            {{scope.row.author && scope.row.author.join('、')}}
          </template></el-table-column>
        <el-table-column label="operation" width="100">
          <template slot-scope="scope">
            <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer" title="指定作品">
              <i class="el-icon-check" @click="checkedWork(scope.$index,scope.row)"></i>
            </span>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background layout="prev, pager, next" :total="total" @current-change="onSubmit">
      </el-pagination>
    </el-dialog>
    <work-list ref="workList" :workId='selectWorkNo.toString()' :workSocietyCode='selectWorkSoc.toString()'></work-list>
  </div>
</template>
<script>
import workList from '../../../demo/workList.vue'
export default {
  data() {
    return {
      listType: '',
      queryparam: "",
      queryindex: "",
      uploadType: "",
      uploadType: '',
      // file work info
      fileInfo: {},
      // 选中的work 的信息
      matchInfo: {},
      activeNames: ['1', '2'],
      matchTable: [],
      isrcList: [],
      performerList: [],
      selectWorkTitleId: '',
      selectWork: {
        workId: '',
        soc: '',
        title: '',
        selectWorkShow: false,
        list: []
      },
      loading:false,
      show: false,
      searchInfo: {
        workId: '',
        title: '',
        soc: ''
      },
      tableData: [],
      total: 0,
      emptyText:'暫無數據',
      emptyText1:'暫無數據',
      emptyText2:'暫無數據',
      selectWorkNo:'',
      selectWorkSoc:'',
      IP_ShareCheck:false,
    }
  },
  components:{ workList },
  activated() {
    this.listType = this.$route.query.listType;
    this.uploadType = this.$route.query.uploadType;
    this.queryparam = this.$route.query.usetabledata;
    this.queryindex = this.$route.query.useparams
    // this.nextsearchparam = this.$route.query.thissearchparam;
    this.queryInfo();
    this.queryWorkInfo();
  },
  destroyed() {
    localStorage.removeItem("tempindex");
    //    alert(localStorage.getItem('tempindex'))
  },
  methods: {
    workDrawer(row){
      console.log(row)
      this.selectWorkNo = row.matchWorkId;
      this.selectWorkSoc = row.matchWorkSocietyCode;
      this.$nextTick(()=>{
        this.$refs.workList.init()
      })
    },
    queryInfo(index, nextone) {
      if (nextone == 2) {
        // console.warn('*****',this.$route.query.useparams+1,'^^^^^',this.$route.query.usetabledata,"$$$$$$",this.$route.query.usetabledata[this.$route.query.useparams+1])
        console.info('*************', index, this.queryparam.length)
        if (index >= this.queryparam.length) {
          this.$toast({ tips: '這已經是最後一條數據了!' })
          return;
        } else {
          this.loading=true
          this.$http
            .get('/claimmatch/getListMatchDataDspWorkMappingById?listMatchDataDspId=' + this.queryparam[index].id)
            .then((res) => {
              //    alert(index)
              localStorage.setItem("tempindex", index);
              //    console.warn('localstorage',localStorage.getItem('tempindex'))
              if (res.success) {
                this.fileInfo = res.data;
                this.loading=false
                let temptable=res.data.listMatchDataDspWorkMappingList||[]
                this.queryMatchWorks(temptable);
                // this.fileMappingId = res.data.fileMappingId;
                // this.queryMatchWorks(this.fileMappingId);
                // this.getFileWorkIpShare();

              }
            });
        }
      } else {
        this.$http.get('/claimmatch/getListMatchDataDspWorkMappingById?listMatchDataDspId=' + this.$route.query.id).then(res => {
          if (res.success) {
            this.fileInfo = res.data;
            // this.queryMatchWorks(res.data.fileBaseId);
            let temptable=res.data.listMatchDataDspWorkMappingList||[]
            this.queryMatchWorks(temptable);

          }
        })
      }
    },
    queryMatchWorks(matchTable) {
      // if (matchTable.length==0) {
      //   alert('000000000')
      //   return 
      // }
      this.matchTable = matchTable;
      this.emptyText = this.matchTable.length?'':'暫無數據'
      //默认选中第一行
      this.$refs.matchTableRef.setCurrentRow(this.matchTable[0]);
      if (this.matchTable[0]) {
        this.changeWork(this.matchTable[0]);
      }
      //  console.log('---------matchinfo',this.matchInfo);
      // this.changeWork(this.matchTable[0]);
      // this.$http.get('/listmatch/getListMatchDataBasicMatchWorkByMatchBaseId', {params: {matchBaseId: matchBaseId}}).then( res => {
      //     if(res.success){
      //         this.matchTable = res.data;
      //         //默认选中第一行
      //         this.$refs.matchTableRef.setCurrentRow(this.matchTable[0]);
      //         this.changeWork(this.matchTable[0]);
      //     }
      // })
    },
    changeWork(row, column, event) {
      // return;
      this.matchInfo = {};
      this.selectWorkTitleId = row.id;

      //如果類型為!pg，  那么不需要查詢，直接把table里的值复制過去就行了
      //如果類型為pg， 则，把table值复制過去，且 查詢component
      if (this.listType != 'pg') {
        // alert('88888888888888')
        this.matchInfo = row;
        if (this.matchInfo.type != 'm') { //如果不是用户指定作品查找的，就转化數據
          this.matchInfo.performerList = this.matchInfo.matchArtists.split(';');
          this.matchInfo.isrcList = this.matchInfo.matchIsrc.split(';');
        }

      } else {
          // alert('3333333333333333')
        this.matchInfo = row;
        let params = {
          workId: row.matchWorkId,
          workSocietyCode: row.matchWorkSocietyCode
        }
        this.emptyText1 = '數據加載中';
        this.$http.get('/wrk/getWrkWorkById', { params }).then(res => {
          if (res.success) {
            this.matchInfo.componentList = res.data.wwcList;
            if(! res.data.wwcList || res.data.wwcList.length == 0){
              this.emptyText1 = '暫無數據';
            }
            this.$set(this.matchInfo, 'componentlist', res.data.wwcList);
          }
        })
      }
     
    },
    selectWorkFn() {
      this.searchInfo = {
        workId: '',
        title: '',
        soc: ''
      };
      this.tableData = [];
      this.total = 0;
      this.show = true;
    },
    cleansearch() {
      this.searchInfo = {
        workId: '',
        title: '',
        soc: ''
      };
      this.tableData = [];
      this.total = 0;
    },
    onSubmit(page) {
      let params = this.searchInfo;
      params.page = {
        pageNum: page ? page : 1,
        pageSize: 10
      }
      this.emptyText = '數據加載中';
      this.$http.post('/wrk/queryWrkWorkListEs', params).then(res => {
        if (res.success) {
          this.tableData = res.data.list;
          this.total = res.data.total;
        }
        if(! this.tableData || this.tableData.length == 0){
          this.emptyText = '暫無數據';
        }
      })
    },
    checkedWork(index, row) {
      this.$msgbox.confirm('確定指定此作品?', '提示', {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.selectWorkTitleId = row.id;
        this.submitFn(3);
        // this.show=false
      }).catch(() => {
      });
    },
    queryWorkInfo() {
      this.selectWork.list = [];
      // 查詢此work no 的ipshare
      let params = {
        workId: this.selectWork.workId,
        workSocietyCode: this.selectWork.soc
      }
      this.$http.get('/wrk/getWrkWorkById', { params }).then(res => {
        if (res.success) {
          this.selectWork.list.push(res.data.wrkWork);
          this.selectWork.workInfo = res.data.wrkWork
          this.IP_ShareCheck = this.selectWork.workInfo == 0?false:true
        }
      })
    },
    submitFn(status) {
    //   if (this.matchTable.length <= 0 || !this.selectWorkTitleId) {
      if (status!=2&&!this.selectWorkTitleId) {
        // =================-----------加上,iconClass:'failebox'   就是红色框框了!!!!!
        this.$toast({ tips: '請選擇一個匹配作品'})
        return;
      }
      //  0 默认  1通過  2拒絕 3指定作品  特殊   跳轉審核下一個！！！！
      let ajaxData = {
        status: status,
        uniqueKeyMd5: this.fileInfo.dataUniqueKey,
        matchDspMappingId: this.selectWorkTitleId
      }
      this.$http.post('/claimmatch/updateListMatchDataDspStatusByParams', ajaxData).then(res => {
        // console.log('--------', res)
        // console.warn('=========', this.queryindex, '---', this.queryparam.length - 1)
        if (res.data.code && res.data.code != 200) {
          this.$toast({ tips: res.data.message })
        } else {
          if (this.queryindex >= this.queryparam.length - 1) {
            this.$msgbox
              .confirm(`操作成功，继续审核下一条？`, "提示", {
                closeOnClickModal:false,
                confirmButtonText: "是",
                cancelButtonText: "否",
                type: "warning",
              })
              .then(() => {
                   if (this.queryparam.length<10) {
                      this.$route.query.thissearchparam.page.pageNum=1
                    }
                    // console.warn('000000',this.$route.query.thissearchparam)
                this.show = false
                // audit(scope.row,scope.$index)
                this.$http.post('/claimmatch/getListMatchDataDspList', this.$route.query.thissearchparam).then(res => {
                  if (res.success) {
                    this.queryparam = res.data.data.list||[];
                    this.queryindex = 0
                    this.queryInfo(this.queryindex, 2);
                  }
                })

                // this.queryparam = this.$route.query.usetabledata; //this.tabledata
                // this.$bus.$emit("closeCurrentTab", () => {
                //   this.$router.push({
                //     name: "claimAuditList",
                //     query: { update: true },
                //   });
                // });
              })
              .catch(() => {
                this.show = false
                 this.$bus.$emit('closeCurrentTab', () => {
                  this.$router.push({ name: 'claimAuditList', query: { update: true } });
                })
              });
            return;
          } else {
            this.$msgbox
              .confirm(`操作成功，是否繼續審核下一筆?`, "提示", {
                confirmButtonText: "是",closeOnClickModal:false,
                cancelButtonText: "否",
                type: "warning",
              })
              .then(() => {
                this.queryindex += 1;
                console.warn("indexquery", this.queryindex, 'legnrth', this.queryparam.length);
                this.show = false
                this.queryInfo(this.queryindex, 2);
              })
              .catch(() => {

                this.$bus.$emit('closeCurrentTab', () => {
                  this.$router.push({ name: 'claimAuditList', query: { update: true } });
                })
              });
            // this.$toast({tips: '操作成功'});
            // this.$bus.$emit('closeCurrentTab', () => {
            //     this.$router.push({name: 'claimAuditList', query: {update: true}});
            // })
          }
        }
      })
    }
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      console.log("*****", vm);
      // vm.formData.distNo = to.params.distNo
      if (localStorage.getItem("tempindex")) {
        vm.queryInfo(localStorage.getItem("tempindex"), 2);
      }
    });
  },

}
</script>
<style lang="scss" scoped>
@import "../../../../assets/scss/works.scss";
.el-collapse-item {
  width: 480px;
  margin-right: 40px;
  float: left;
}
.match-table {
  width: 100%;
}
/deep/ .match-table thead .el-checkbox {
  display: none;
}
/deep/ .match-info {
  margin-top: 10px;
}
/deep/ .match-info .el-table tr {
  background: #fff;
}
/deep/ .el-collapse-item__arrow {
  display: none;
}
/deep/ .component .el-form-item__content {
  width: 100%;
}
/deep/ .el-input.is-disabled .el-input__inner {
  background: #fff;
  color: #333;
}
/deep/ .el-table__body tr.current-row > td {
  background-color: #17b3a3;
}
/deep/ .el-form-item.f12 label {
  font-size: 13px;
}

ul.list {
  background: #fff;
  padding: 0;
  border: 1px solid #ddd;
  margin-top: 10px;
  border-radius: 4px;
  li {
    list-style: none;
    border-bottom: 1px solid #ddd;
    line-height: 26px;
    padding: 0 4px;
  }
  &:last-child {
    border-bottom: 0;
  }
}

/deep/ .el-input {
  width: 312px;
  margin-left: 31px;
}
</style>
