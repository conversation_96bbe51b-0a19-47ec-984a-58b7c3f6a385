/**
 * Created by guanjunjie
 * Date: 2020-02-13
 */
import zhLocale from 'element-ui/lib/locale/lang/zh-CN'
const cn = {
    menuName: {
        home: '主頁',
        // 会员
        member: '會員管理',
        addMember: '新增會員',
        memberList: '會員信息管理',
        ipTransfer: 'Ip Transfer List',
        // 作品
        work: '作品管理',
        workList: '作品基本信息管理',
        workTransfer: 'Work Transfer List',
        // 作品去重
        duplicate: '作品去重',
        cwrUpload: 'CWR上傳',
        avrUpload: 'AVR上傳',
        duplicateAuditList: '審核',
        // 合約
        agreement: '合約管理',
        agreementList: '合約管理',
        // 清單管理
        templateConfig: '清單模板配置',
        templateConfigList:'清單模板',
        mapConfigList:'清單解析映射關係配置',
        classification:'清單校驗',
        usageList: '清單管理',
        mergeTitle:'特殊抽樣',
        mergeList:'資料夾合併加總',
        listNumber: '清單編號生成',
        sampleDate: '抽樣管理',
        usageUploadList: '上傳列表',
        usageListMatch: '清單匹配',
        singListMatch: '單場次清單審核',
        oUsage: 'O清單管理',
        oUsageList: 'O水單列表',
        oUsageFileList:'O清單明細',
        oAuditIp:'IP審核',
        oUsageMatch: 'O清單作品審核',
        oAddData: '補充手動數據',
        // claim
        claim: 'Claim管理',
        claimParameter: 'Claim Parameter',
        ClaimFilterWork: 'Claim Filter Work',
        claimReport: 'Claim報表',
        claimUsage: 'Claim清單管理',
        claimUpload: 'Claim上傳',
        claimList: 'Claim列表',
        claimMatchList: 'Claim審核列表',
        ClaimHistory: 'Claim History',
        ClaimHistoryLog: 'Claim HistoryLog',
        // 分配管理
        distribution: '分配管理',
        distributionList: '分配列表',
        createDistribution: '創建分配',
        royalties: 'royalties transaction',
        royaltiesMember: 'member',
        royaltiesSociety: 'society',
        specialCondition: 'special condition',
        // adjustment
        adjustment: 'adjustment',
        sdAdjustment: 'SD Adjustment',
        srAdjustment: 'SR Adjustment',
        // newMedia
        newMedia: 'Digital Mechanical Right',
        publishers: 'publishers',
        newMediaDistNo: 'New Media Dist No',
        mediaVersion: 'Media Version',
        // mechanical right
        mechanicalRight: 'Mechanical Right',
        producerType: 'Producer Type',
        producer: 'producer',
        mecSalesMaintenance: 'Mec Sales Maintenance',
        // 协会
        society: '協會管理',
        societyList: '協會列表',
        // 支付
        payment: '支付管理',
        paymentList: '支付列表',
        incomeDetail: '支付明細',
        // 系统管理
        system: '系統管理',
        role: '角色管理',
        user: '用戶管理',
        menu: '菜單管理',
        // Maintenance
        maintenance: 'Maintenance',
        category: 'List Category',
        source: 'List Source',
        performer: '表演者管理',
        convert: '中文轉拼音',  //中文轉拼音
        // 指纹比对
        sampleDNA: '指紋比對管理',
        sampleList: 'Sample List',
        DNASearch: '指紋比對',
        export: '導出報表',
        exportDiy: '自主導出',
        exportReport: '特殊報告導出',
        bank: '銀行管理',
        branch: '分行管理',
        iswc:'iswc手動申請',
        management:'任務管理',
        allTasks:'所有任務',
        myTasks:'我的任務',
    },
    routeNmae: {
        home: '主頁',
        article: '文章管理',
        publishArticle: '发表文章',
        publishArticleEditor: '发表文章-富文本',
        icon: '图标',
        builtInIcon: '内置图标',
        shuttleBox: '穿梭框',
        demoShuttle: '穿梭框demo',
        permissions: '权限管理',
        pageControl: '页面权限',
        btnControl: '按钮权限',
        table: '表格',
        multiDataTable: '多选数据表格',
        filterTable: '筛选表格',
        dragSort: '拖拽排序',
        upload: '上傳',
        fileUpload: '文件上傳',
        editor: '编辑器',
        markdown: 'markdown',
        wangeditor: 'wangeditor',
        multiDirectory: '多级目录',
        'menu2-1': '二级-1',
        'menu2-2': '二级-2',
        'menu2-3': '二级-3',
        'menu3-1': '三级-1',
        'menu3-2': '三级-2',
        'menu3-3': '三级-3',
        'menu4-1': '四级-1',
        'menu4-2': '四级-2',
        'menu5-1': '五级-1',
        systemSettings: '系统设置',
        navMenu: '导航菜单'
    },
    rightMenu: {
        close: '关闭',
        closeOther: '关闭其他',
        closeAll: '全部关闭'
    },
    role: {
        superAdmin: '超级管理员',
        admin: '管理员',
        ordinary: '普通用户'
    },
    userDropdownMenu: {
        basicInfor: '基本资料',
        changePassword: '修改密码',
        logout: '退出'
    },

    ...zhLocale     //    合并element-ui内置翻译
}

export default cn
