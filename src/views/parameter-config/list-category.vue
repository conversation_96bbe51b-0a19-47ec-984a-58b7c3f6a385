<template>
    <div>
        <el-form :inline="true" :model="formInline" class="demo-form-inline" @keyup.enter.native="getList(1)">
            <el-form-item>
                <el-input v-model="formInline.categoryCode" placeholder="Code" style="width: 100px;"></el-input>
            </el-form-item>
            <el-form-item>
                <el-input v-model="formInline.categoryDesc" placeholder="Desc" style="width: 120px;"></el-input>
            </el-form-item>
            <el-form-item>
                <el-input v-model="formInline.sourceName" placeholder="source name" style="width: 120px;"></el-input>
            </el-form-item>
            <el-form-item>
                <el-select v-model="formInline.distType" placeholder="dist type" style="width: 120px;">
                    <el-option
                        v-for="item in distType"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
<!--                <el-input v-model="formInline.poolCode" placeholder="pool code" style="width: 100px;"></el-input>-->
                <el-select v-model="formInline.poolCode" placeholder="pool code" style="width: 130px;">
                    <el-option
                        v-for="item in poolCodeOption"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="is dist">
                <el-select v-model="formInline.isDist" placeholder="请選擇" style="width: 80px;">
                    <el-option label="All" value=""></el-option>
                    <el-option label="yes" value="1"></el-option>
                    <el-option label="no" value="0"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="getList()">搜索</el-button>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="configAdd">新增</el-button>
            </el-form-item>
            <!-- <el-form-item>
                <el-button type="success" @click="exportList">導出</el-button>
            </el-form-item> -->
            <el-form-item>
                <span class="clear-search" @click="clearSearch()">清除搜索</span>
            </el-form-item>
        </el-form>
        <el-table
            @cell-dblclick="poolCodeClick"
            :data="tableData"
            border
            stripe
            style="width: 100%" 
            v-loading="loading"
            :empty-text="emptyText">
            <el-table-column
                prop="id"
                label="Category Id"
                width="120">
            </el-table-column>
            <el-table-column
                prop="categoryCode"
                label="Categroy Code"
                width="150">
            </el-table-column>
            <el-table-column
                prop="categoryDesc"
                label="Category Desc"
                min-width="140">
            </el-table-column>
            <el-table-column
                class-name="poolCode"
                name="poolCode"
                prop="poolCode"
                label="Pool Code"
                width="110">
                <template slot-scope="scope">
                    {{mapPoolCode(scope.row)}}
                </template>
            </el-table-column>
            <el-table-column
                prop="poolRight"
                label="Pool Right"
                width="110">
            </el-table-column>
            <el-table-column
                prop="sourceName"
                label="Source Name"
                min-width="130">
            </el-table-column>
            <el-table-column
                prop="distType"
                label="Dist Type"
                width="100">
            </el-table-column>
            <el-table-column
                prop="isDist"
                label="isdist"
                width="70">
                <template slot-scope="scope">
                    <p v-if="scope.row.isDist === 1">Y</p>
                    <p v-else>N</p>
                </template>
            </el-table-column>
            <el-table-column
                fixed="right"
                label="operation"
                width="150">
                <template slot-scope="scope">
                    <div class="option">
                        <el-button @click="switchDist(scope.row)" type="text" size="small">切換dist</el-button>
                        <el-button @click="handleClick(scope.row)" type="text" size="small">編輯</el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :total=total @current-change="handleCurrentChange" :current-page='currentPage'>
        </el-pagination>
        <!--編輯弹框-->
        <el-dialog :title="isEdit?'編輯':'新增'" :visible="isShow" width="40%" :show-close="false" :close-on-click-modal="false">
            <el-form label-position="right" label-width="140px" ref="editForm" :model="tableItem" class="demo-form-inline" :rules="rules" @keyup.enter.native="addFormItem">
                <el-form-item label="category code:" prop="categoryCode">
                    <el-input v-model="tableItem.categoryCode" placeholder="category code" label="category code" :readonly="isEdit?true:false" maxlength=10></el-input>
                </el-form-item>
                <el-form-item label="category desc:" prop="categoryDesc">
                    <el-input v-model="tableItem.categoryDesc" placeholder="category desc"></el-input>
                </el-form-item>
                <el-form-item label="source name:" prop="sourceName">
                    <el-select 
                        v-model="tableItem.sourceName" 
                        placeholder="source name"
                        filterable 
                        @change="nameChange"
                    >
                        <el-option
                            v-for="item in sourceNameList"
                            :key="item.id"
                            :label="item.sourceName"
                            :value="item.sourceName">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="dist type:" prop="distType">
                    <el-select v-model="tableItem.distType" placeholder="dist type">
                        <el-option
                            v-for="item in distType"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="pool code:" prop="poolCode">
                    <el-select v-model="tableItem.poolCode" placeholder="code" @change="getPoolRight">
                        <el-option
                            v-for="(item,index) in poolCodeList"
                            :key="index"
                            :label="item.poolDesc"
                            :value="item.poolCode">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="pool right:" prop="poolRight">
                    <el-select v-model="tableItem.poolRight" placeholder="right">
                        <el-option
                            v-for="item in poolRightList"
                            :key="item.rightCode"
                            :label="item.rightCode"
                            :value="item.rightCode">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="is dist:">
                    <el-checkbox v-model="tableItem.checked">is dist</el-checkbox>
                </el-form-item>
            </el-form>
            <div style="text-align: center">
                <el-button @click="cancelDialog">取消</el-button>
                <el-button type="primary" @click="addFormItem" :disabled="isDisabled">確定</el-button>
            </div>
        </el-dialog>
        <!-- 导出弹框 -->
        <el-dialog title="單場次導出" :visible.sync="showExport" width="40%" :show-close="true" :close-on-click-modal="false">
            <el-tabs v-model="exportType" type="card">
                <el-tab-pane label="Category Code" name="code">
                    <el-button
                        size="mini"
                        type="primary"
                        @click="exportAddFn()">添加</el-button>
                    <el-table
                        :data="exportForm.categories"
                        border
                        stripe
                        style="width: 100%">
                        <el-table-column
                            label="Category Code">
                            <template slot-scope="scope">
                                <el-input v-model=scope.row.categoryCode placeholder=""></el-input>
                            </template>
                        </el-table-column>
                        <!-- <el-table-column
                            label="Perf Date">
                            <template slot-scope="scope">
                                <date-picker v-model="scope.row.performTime" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd" style="width: 180px;"></date-picker>
                            </template>
                        </el-table-column> -->
                        <el-table-column
                            label="Operation"
                            width="220px">
                            <template slot-scope="scope">
                                <el-button
                                    size="mini"
                                    type="danger"
                                    @click="exportDeleteFn(scope.$index, scope.row)">删除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-tab-pane>
                <el-tab-pane label="Category 段" name="range">
                    <el-form :inline="false" label-width="180px">
                        <el-form-item label="From Category Code">
                            <el-input v-model="exportForm.startCategroyCode" style="width: 180px;"></el-input>
                        </el-form-item>
                        <el-form-item label="To Category Code">
                            <el-input v-model="exportForm.endCategoryCode" style="width: 180px;"></el-input>
                        </el-form-item>
                    </el-form>
                </el-tab-pane>
                <el-tab-pane label="上傳時間" name="date">
                    <el-form :inline="false" label-width="120px">
                        <el-form-item label="Start Date">
                            <date-picker v-model="exportForm.startTime" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd" style="width: 180px;"></date-picker>
                        </el-form-item>
                        <el-form-item label="End Date">
                            <date-picker v-model="exportForm.endTime" type="date" value-format="yyyy-MM-dd" format="yyyyMMdd" style="width: 180px;"></date-picker>
                        </el-form-item>
                    </el-form>
                </el-tab-pane>
            </el-tabs>
            <div slot="footer">
                <el-button type="primary" @click="exportFn">導出</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
    export default {
        name: 'list-category',
        data () {
            return {
                loading: false,
                isDisabled: false,
                formInline: {
                    categoryCode: '',
                    categoryDesc: '',
                    sourceName: '',
                    poolCode: '',
                    listSourceId:'',
                    page_num:1,
                    isDist: '',
                    checked:false,
                    distType: ''
                },
                poolCodeOption: [
                    {
                        value: 'C',
                        label: 'CONCERT'
                    }, {
                        value: 'D',
                        label: 'download'
                    }, {
                        value: 'F',
                        label: 'Film'
                    }, {
                        value: 'G',
                        label: 'AIRLINE'
                    }, {
                        value: 'I',
                        label: 'PUBLIC TRANSMISSION'
                    }, {
                        value: 'K',
                        label: 'KARAOKE'
                    }, {
                        value: 'O',
                        label: 'OTHERS'
                    }, {
                        value: 'P',
                        label: 'Pre-claim'
                    }, {
                        value: 'R',
                        label: 'RADIO'
                    }, {
                        value: 'T',
                        label: 'TV'
                    }
                ],
                distType: [{
                    value: 'P',
                    label: 'P'
                }, {
                    value: 'M',
                    label: 'M'
                }, {
                    value: 'I',
                    label: 'I'
                }, {
                    value: 'O',
                    label: 'O'
                }],
                total: 0,
                currentPage:1,
                addTabelItem:{
                    id: '',
                    code: '',
                    desc: '',
                    poolCode: '',
                    poolRight: '',
                    name: '',
                    distType: '',
                    isDist: false
                },
                tableData: [],
                tableItem:{},
                checked:false,
                isShow:false, //显示新增弹框
                isEdit:false,
                sourceNameList:[],
                poolCodeList: [],
                poolRightList: [],
                poolRightListOrigin:[],
                rules:{
                    categoryCode:[{ required: true, message: '请输入', trigger: 'blur' }],
                    categoryDesc:[{ required: true, message: '请输入', trigger: 'blur' }],
                    sourceName:[{ required: true, message: '请输入', trigger: 'change' }],
                    distType:[{ required: true, message: '请输入', trigger: 'change' }],
                    poolCode:[{ required: true, message: '请输入', trigger: 'change' }],
                    poolRight:[{ required: true, message: '请输入', trigger: 'change' }],
                },
                showExport: false,
                exportType: 'code',
                exportForm: {
                    categories: [{categoryCode: '', performTime: ''}],
                    startCategroyCode: '',
                    endCategoryCode: '',
                    startTime: '',
                    endTime: '',
                },
                emptyText: '數據加載中',
            }
        },
        activated(){
            this.initSelect()
            this.getList()
        },

        methods: {
            exportList(){
                this.exportForm = {
                    categories: [{categoryCode: '', performTime: ''}],
                    startCategroyCode: '',
                    endCategoryCode: '',
                    startTime: '',
                    endTime: '',
                }
                this.showExport = true;
            },
            exportFn(){
                let ajaxData = {}
                if(this.exportType == 'code'){
                    ajaxData.type = 1;
                    ajaxData.categories = this.exportForm.categories;
                    let flag = true;
                    ajaxData.categories.forEach( item => {
                        if(!item.categoryCode){
                            flag = false;
                        }
                    })
                    if(!flag){
                        this.$toast({tips: '請輸入Category Code!'});
                        return;
                    }
                }else if(this.exportType == 'range'){
                    ajaxData.type = 2;
                    ajaxData.startCategroyCode = this.exportForm.startCategroyCode;
                    ajaxData.endCategoryCode = this.exportForm.endCategoryCode;
                    if(!ajaxData.startCategroyCode || !ajaxData.endCategoryCode){
                        this.$toast({tips: '請輸入From Category Code 和 To Category Code!'});
                        return;
                    }
                }else if(this.exportType == 'date'){
                    ajaxData.type = 3;
                    ajaxData.startTime = this.exportForm.startTime;
                    ajaxData.endTime = this.exportForm.endTime;
                    if(!ajaxData.startTime || !ajaxData.endTime){
                        this.$toast({tips: '請輸入Start Date 和 End Date!'});
                        return;
                    }
                }
                    console.log(ajaxData)
                this.$http.post('/export/pdf/singleSession', ajaxData,{responseType:'blob'}).then( res => {
                    if(res.data.type!='application/pdf'){
                        this.$toast({tips: '導出失敗'})
                    }else{
                        this.$utils.downloadByBlob(res.data,'單場次導出報表','.pdf')
                        // this.$toast({tips: '導出成功！'})
                        this.showExport = false;
                    }
                    // if(res.success && res.data.code == 200){
                    //     this.$utils.downloadByBlob(res.data,'單場次导出报表','.pdf')
                    //     // this.$toast({tips: '導出成功！'})
                    //     this.showExport = false;
                    // }else{
                    //     this.$toast({tips: res.data.message})
                    // }

                })

            },
            exportAddFn(){
                this.exportForm.categories.push({categoryCode: '', performTime: ''});
            },
            exportDeleteFn(index){
                this.exportForm.categories.splice(index, 1);
            },
            clearSearch(){
                this.formInline= {
                    categoryCode: '',
                    categoryDesc: '',
                    sourceName: '',
                    poolCode: '',
                    listSourceId:'',
                    page_num:1,
                    isDist: '',
                    checked:false,
                    distType: ''
                };
                this.getList();
            },
            nameChange(row){
                console.log('row: ', row);
                this.tableItem.sourceName = row;
                this.sourceNameList.forEach( item => {
                    if(item.sourceName == row){
                        this.tableItem.sourceName = item.sourceName;
                    }
                })
            },
            initSelect(){
                this.$http.get('/list/categorys/initData').then(res => {
                    if(res.success){
                        console.log(res)
                        this.poolCodeList = res.data.data.refPoolMstrList
                        this.sourceNameList = res.data.data.listSourceList
                        this.sourceNameList.reverse();
                        this.poolRightListOrigin =  res.data.data.refPoolRightList
                    }
                })
            },
            handleCurrentChange (val) {
                this.getList(val)
            },
            poolCodeClick(){
                let [row, column, cell, event] = [...arguments]
                let isPoolCode = (cell.getAttribute('class').indexOf('poolCode') != -1)
                if(isPoolCode){
                    this.handleClick(row)
                }
            },
            handleClick(row){
                this.isShow = true
                this.isEdit = true
                this.tableItem = this.$utils.copy(row)
                console.log(row)
                this.getPoolRight(row.poolCode)
                this.$set(this.tableItem,'checked',this.tableItem.isDist == 1?true:false)
            },
            getList(page){
                let params = this.$utils.copy(this.formInline);
                params.page_num = page ? page : 1;
                this.loading = true;
                this.emptyText = '數據加載中';
                this.$http.get('/list/categorys',{params}).then(res => {
                    this.loading = false;
                    if(res.success){
                        let list = res.data.data.list
                        this.tableData = list
                        if(! this.tableData || this.tableData.length == 0){
                            this.emptyText = '暫無數據';
                        }
                        this.total = res.data.data.total
                        this.currentPage = params.page_num
                    }
                })
            },
            configAdd(){
                this.isShow = true
                this.isEdit = false
                this.reset()
                if(this.$refs.editForm){
                    this.$refs.editForm.resetFields()
                }

            },
            cancelDialog(){
                this.isShow = false
                this.$refs.editForm.resetFields()
            },
            addFormItem(){
                this.isDisabled = true
                setTimeout(()=>{
                        this.isDisabled = false
                },3000)
                this.tableItem.isDist = this.tableItem.checked === false?0:1
                this.sourceNameList.map(item => {
                    if(item.sourceName === this.tableItem.sourceName){
                        this.tableItem.listSourceId = item.id||''

                    }
                })
                this.$refs.editForm.validate(validate => {
                    if(validate){
                        if(this.isEdit){
                            this.$http.post('/list/categorys',this.tableItem).then(res => {
                                if(res.success){
                                    this.$message({
                                        message:res.data.message,
                                        type:res.data.code === 200?'success':'warning'
                                    })
                                    if(res.data.code === 200){
                                        this.cancelDialog()
                                        this.getList()
                                    }
                                }
                            })
                        }else{

                            this.$http.put('/list/categorys',this.tableItem).then(res => {

                                if(res.success){
                                    if(res.data.code == 200){
                                        this.cancelDialog()
                                        this.getList()

                                    }else{
                                        this.$toast({tips:res.data.message})
                                    }
                                }
                            })
                        }
                    }
                })

            },
            switchDist(row){
                if(row.isDist === 1){
                    row.isDist = 0
                }else{
                    row.isDist = 1
                }
                this.$http.post('/list/categorys',row)
            },
            reset(){
                this.isEdit = false
                this.tableItem = {
                    categoryCode: '',
                    categoryDesc: '',
                    sourceName: '',
                    poolCode: '',
                    listSourceId:'',
                    page_num:this.formInline.page_num,
                    checked: false,
                    distTypeValue: ''
                }
            },
            getPoolRight (e) {
                let arr = []
                this.poolRightListOrigin.map(item => {
                    if(item.poolCode === e){
                        arr.push(item)
                    }
                })
                this.poolRightList = arr

            },
            mapPoolCode(row){
                let code = row.poolCode
                let name = ''
                if(code === 'C'){
                    name = 'CONCERT';
                }else if(code === 'D'){
                    name = 'download';
                }else if(code === 'F'){
                    name = 'Film';
                }else if(code === 'G'){
                    name = 'AIRLINE';
                }else if(code === 'I'){
                    name = 'PUBLIC TRANSMISSION';
                }else if(code === 'K'){
                    name = 'KARAOKE';
                }else if(code === 'O'){
                    name = 'OTHERS';
                }else if(code === 'P'){
                    name = 'Pre-claim';
                }else if(code === 'R'){
                    name = 'RADIO';
                }else if(code === 'T'){
                    name = 'TV';
                }
                return name;
            }
        }
    }
</script>

<style lang="scss" scoped>
    /deep/ .el-table .cell{
        text-align: center;
    }
    .option span{
        display: inline-block;
        padding: 0 5px;

    }
    .option span:nth-child(1){
        border-right: 1px solid #ccc;
    }
    /deep/ .el-form-item__label {
        margin-left: 0 !important;
    }
    /deep/ .el-table--enable-row-transition .el-table__body td .cell p{
        margin: 0;
    }
</style>
