
<template>
    <div class="transfer">
        <el-dialog :title="'Transfer'" :visible.sync="showt" :width="'1260px'" center top="10vh" :close-on-click-modal="false">
            <div class="clear">
                <div class="from">
                    <el-row style="height: 90px;">
                        <el-col :span="11">
                            <el-row> 
                                <label>IP Base No</label>
                                <el-input :value="ipiInfo.baseInfo && ipiInfo.baseInfo.ipBaseNo" readonly=""></el-input>
                            </el-row>
                        </el-col>                        
                    </el-row>
                    <el-row>
                        <el-table :empty-text="tableresult"   stripe :data="ipiInfo.ipiInfo" border :cell-class-name="cellClass">
                            <el-table-column prop="nameType" label="NT" width="38"></el-table-column>
                            <el-table-column prop="name" label="Transfer From Name" width="182"></el-table-column>
                            <el-table-column prop="nameNo" label="From Name No" width="140"></el-table-column>
                            <el-table-column label="NT" width="38">
                                <template slot-scope="scope">
                                    {{toResult.length && (scope.row.to || scope.row.to==0) ? toResult[scope.row.to].name_type : ''}}
                                </template>
                            </el-table-column>
                            <el-table-column label="Transfer To Name" width="160" class="select">
                                <template slot-scope="scope">
                                    <el-select v-model="scope.row.to">
                                        <el-option v-for="(item, index) in toResult" :key="index" :label="item.name" :value="index"></el-option>
                                    </el-select>
                                </template>
                            </el-table-column>
                            <el-table-column label="To Name No" width="118">
                                <template slot-scope="scope">
                                    {{toResult.length && (scope.row.to || scope.row.to==0) ? toResult[scope.row.to].ip_name_no : ''}}
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-row>
                </div>
                <div class="to" style="position:relative;right: -10px;padding-right: 10px;">
                    <el-form :inline="true" label-width="110px">
                        <el-form-item label="IP Base No">
                            <el-input v-model="search.ip_no" style="width: 140px" readonly placeholder="雙擊查詢" @dblclick.native="getIp()"></el-input>
                        </el-form-item>
                        <div>
                            <el-form-item label="IP Name No">
                                <el-input v-model="search.name_no" style="width: 140px" readonly placeholder="雙擊查詢" @dblclick.native="getIp()"></el-input>
                            </el-form-item>
                            <el-form-item label="Name" label-width="60px">
                                <el-input v-model="search.name" readonly placeholder="雙擊查詢" @dblclick.native="getIp()"></el-input>
                            </el-form-item>
                        </div>
                    </el-form>
                   
                    <el-row>
                        <el-table :empty-text="tableresult"   stripe :data="toResult" border>
                            <el-table-column prop="name_type" label="NT" width="50"></el-table-column>
                            <el-table-column prop="name" label="Transfer To Name" min-width="260">
                            </el-table-column>
                            <el-table-column prop="ip_name_no" label="To Name No" width="162"></el-table-column>
                        </el-table>
                    </el-row>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="showt = false">取 消</el-button>
                <el-button type="primary" @click="confirm()">確 定</el-button>
            </span>
        </el-dialog>
        <!-- <ip-result :show="showResult" :result-data="resultData" @selectIp="selectIp" @updateShow="updateShow" @searchNextPage="searchFn" :total="total"></ip-result> -->
        <select-ip v-if="ipShow" ref="selectIpCom" :search="ipSearch" :showAdd="false" @checkIp="checkIp"></select-ip>

    </div>
</template>
<script>
import qs from 'qs'
// import ipResult from '../../../components/ip-result'
    import selectIp from '@/components/select-ip';

export default {
    props: {
        ipInfo: {
            type: Object,
            default: {
                baseInfo: {}
            }
        },
        showTransfer: {
            type: Boolean,
            default: false
        }
    },
    data(){
        return {
            ipiInfo: JSON.parse(JSON.stringify(this.ipInfo)),
            showt: this.showTransfer,
            search: {
                ip_no: '',
                name_no: '',
                name: ''
            },
            tableData: [],
            resultData: [],
            total: 1,
            toResult: [],tableresult:' ',
            transferTo: [],
            showResult: false,

            ipShow: false,
            ipSearch: {}
        }
    },
    components: {
        selectIp
    },
    watch: {
        showt(){
            this.$emit('change', {'name': 'showTransfer', 'value': this.showt});
        },
        showTransfer(){
            this.showt = this.showTransfer
        }
    },
    methods: {
        cellClass({row, column, rowIndex, columnIndex}){
            if(columnIndex == 4){
                return 'select';
            }
        },
        updateShow(obj){
            this[obj.name] = obj.value
        },
        getIp(){
            this.ipShow = true;
            this.ipSearch = {
                name_no: this.search.name_no,
                ip_no: this.search.ip_no,
                name: this.search.name
            }
            this.$nextTick( () => {
                this.$refs.selectIpCom.init();
            })
        },
        checkIp(info){
            this.$set(this.search, 'name_no', info.ip_name_no);
            this.$set(this.search, 'ip_no', info.ip_base_no);
            this.$set(this.search, 'name', info.name);
            // this.toResult = [info];
            this.selectIp(info);
        },
        // searchFn(num){
        //     console.log('query query');
        //     //查詢數據
        //     this.loading = true;
        //     this.search.page_num = num ? num : 1;
        //     this.search.page_size = '10';
        //     let formData = qs.stringify(this.search)
        //     let config = {headers: { 'content-type': 'application/x-www-form-urlencoded' }}
        //     this.$http.post('/ip/name/es', formData, config).then(res => {
        //         if (res.status === 200) {
        //             this.showResult = true;
        //             this.resultData = res.data.list
        //             this.total = parseInt(res.data.total)
        //             this.loading = false
        //         }
        //     })
        // },
        selectIp(info){
            this.showResult = false
            // this.search.ip_no = info.ip_base_no;
            // 取info 中的ipbaseno 然後查此no的所有數據
             //查詢數據
            this.loading = true;
            let ajaxData = {
                ip_no: info.ip_base_no,
                page_num: 1,
                page_size: 10
            };
            let formData = qs.stringify(ajaxData)
            this.tableresult = '數據加載中...'
            let config = {headers: { 'content-type': 'application/x-www-form-urlencoded' }}
            this.$http.post('/ip/name/es', formData, config).then(res => {
                if (res.status === 200) {
                    this.toResult = res.data.list
                    this.loading = false
                }
                    this.tableresult = this.toResult.length == 0 ? '暫無數據' : ' '
            })
        },
        confirm(){
            let flag = true;
            this.ipiInfo.ipiInfo.forEach( (item) => {
                if(!item.to && item.to != 0){
                    flag = false;
                }
            })
            if(!flag){
                this.$toast({tips: "所有的Transfer To Name 都必須填寫!"})
                return;
            }
            let ajaxData = {
                orgIpBaseNo: this.ipiInfo.baseInfo.ipBaseNo,
                tarIpBaseNo: this.search.ip_no,
                nameNoMapping: {}
            }
            for(let i =0;i<this.ipiInfo.ipiInfo.length;i++){
                ajaxData.nameNoMapping[this.ipiInfo.ipiInfo[i].nameNo] = this.toResult[this.ipiInfo.ipiInfo[i].to] ? this.toResult[this.ipiInfo.ipiInfo[i].to].ip_name_no : '';
            }
            console.log('ajaxData:', ajaxData);
            this.$http.post('/ip/transfer', ajaxData).then(res => {
                if(res.status == 200){
                    // this.$message({
                    //     message: '提交成功',
                    //     type: 'success'
                    // })
                    this.$toast({tips: '提交成功'})
                    this.showt = false;
                }
            })
        }
    }
}
</script>
<style lang="scss" scoped>
    /deep/ .el-dialog{
        padding-bottom: 20px;
    }
    /deep/ .el-dialog__header{
        font-weight: bold;
    }
    /deep/ .el-dialog__body{
        padding: 25px 20px;
    }
    /deep/ .from{
        float: left;
        width: 678px;
        .el-input{
            width: 60%;
            margin-left: 8px;
        }
        label{
            display: inline-block;
            width: 86px;
            font-size: 14px;
        }
        .select{
            padding: 0 !important;
            .cell{
                height: 100%;
                padding: 0 !important;
                .el-input{
                    margin-left: 0;
                    width: 100% !important;
                    input{
                        border: 0;
                    }
                }
            }
        }
    }
    .to{
        float: left;
        width: 560px;
        /deep/.el-input{
            width: 54%;
            margin-left: 2px;
            input{
                padding-left: 6px;
            }
        }
        label{
            font-size: 14px;
        }
    }
    
    /deep/ .cell{
        padding: 0 6px !important;
    }
    .el-row{
        margin-bottom: 10px;
    }
</style>