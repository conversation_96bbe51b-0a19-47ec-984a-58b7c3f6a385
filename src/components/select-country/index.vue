<template>
<div>
    <el-dialog :visible.sync="show" width="800px" title="Country" :modal="modal" :close-on-click-modal="false">
        <div style="width: 520px;margin: auto;margin-bottom: 20px; display: inline-block;">
            <el-input v-model="searchInfo.countryCode" @keyup.enter.native="onSubmit()" placeholder="countryCode" style="width: 160px;"></el-input>
            <el-input v-model="searchInfo.tisN" @keyup.enter.native="onSubmit()" placeholder="tisN" style="width: 160px;"></el-input>
            <el-button type="primary" @click="onSubmit()">查詢</el-button>
            <span class="clear-search" @click="clearSearch()">清除搜索</span>
        </div>
        <el-table :empty-text="emptyText" :data="tableData">
            <el-table-column property="countryCode" label="countryCode"></el-table-column>
            <el-table-column property="name" label="name"></el-table-column>
            <el-table-column property="tisN" label="Tisn"></el-table-column>
            <el-table-column
                label="operation"
                width="120">
                <template slot-scope="scope">
                    <span style="width: 100%;display: inline-block;text-align: center;cursor: pointer">
                        <i class="el-icon-check" @click="checkedCountry(scope.$index,scope.row)"></i>
                    </span>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="prev, pager, next"
            :total="total" @current-change="onSubmit" :current-page="currentPage">
        </el-pagination>
    </el-dialog>
</div>
</template>
<script>
export default {
    data(){
        return{
            show:false,
            searchInfo: {
                countryCode: '',
                tisN: ''
            },
            tableData: [],
            total: 0,
            currentPage: 1,
            emptyText: '暫無數據',
        }
    },
    props: {
        search: {
            type: Object,
            default: () => {
                return {
                    countryCode: '',
                    tisN: ''
                }
            }
        },
        modal: {
            type: Boolean,
            default: true
        }
    },

    methods: {
        /**
         * dialog 组件调用方式： 
         * 1、父级用v-if在html里加载组件,
         * 2、父级调用子级的init方法，将数据初始化，prop传给data等
         * 
         */
        init(){
            this.show = true;
            this.searchInfo = this.$utils.copy(this.search);
            this.searchInfo.tisN = this.searchInfo.tisN ? this.searchInfo.tisN : '';
            this.searchInfo.countryCode = this.searchInfo.countryCode ? this.searchInfo.countryCode : '';
            // Object.assign(this.searchInfo, this.searchInfo, this.search);
            this.onSubmit();
        },
        clearSearch(){
            this.searchInfo = {
                tisN: '',
                countryCode: ''
            };
            this.onSubmit();
        },
        onSubmit(page){
            let params = this.searchInfo;
            params.countryCode = params.countryCode ? params.countryCode.trim() : params.currencyCode;
            if(!params.countryCode){
                delete params.countryCode;
            }
            params.tisN = params.tisN ? params.tisN.trim() : params.tisN;
            if(!params.tisN){
                delete params.tisN;
            }
            params.page = {
                pageNum: page ? page : 1, 
                pageSize: 10
            }
            this.emptyText = '數據加載中';
            this.$http.post('/ref/getRefCountry', params).then(res => {
                this.tableData = res.data.list;
                if(! this.tableData || this.tableData.length == 0){
                    this.emptyText = '暫無數據';
                }
                this.total = res.data.total;
                this.currentPage = page ? page : 1;
            })
        },
        checkedCountry(index, row){
            this.$emit('checkCountry', row);
            this.show = false;
        }
    }
    
}
</script>
<style lang="scss" scoped>
    /deep/ .el-dialog__title{
        font-weight: bold;
    }
    /deep/ .el-dialog__body{
        text-align: center;
        padding-top: 0;
    }
</style>